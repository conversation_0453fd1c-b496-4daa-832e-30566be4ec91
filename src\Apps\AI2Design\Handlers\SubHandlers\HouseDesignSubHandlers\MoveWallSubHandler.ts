import { AI2<PERSON><PERSON><PERSON>odeHand<PERSON> } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { EventName } from "@/Apps/EventSystem";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_Commands, LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { Vector3 } from "three";
import { HouseDesignSubHandler } from "./HouseDesignBaseHandler";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
export class MoveWallSubHandler extends HouseDesignSubHandler {

    /**
     *  启用吸附
     */
    _using_adsorption : boolean = true;
    _last_pos: Vector3;
    private _mouse_down_time: number = 0;
    private _mouse_down_pos: Vector3 = null;
    private readonly MINIMUM_MOVE_DISTANCE: number = 5; // 最小移动距离（像素）
    private readonly MAXIMUM_CLICK_DURATION: number = 60; // 最大点击持续时间（毫秒）

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.Transform_MovingWall;
        this._last_pos = null;
    }
    enter(state?: number): void {
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Moving);
    }
    leave(state?: number): void {
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        // 本状态下会把候选矩形设置为只有墙的矩形，退出时需要用父类中的方法更新候选矩形 加上其他的
        super.updateCandidateRects();
    }

    onmousedown(ev: I_MouseEvent): void {
        this._mouse_down_time = Date.now();
        this._mouse_down_pos = new Vector3(ev.posX, ev.posY, 0);

        if (!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect) {

            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        if (this.selected_target && this.selected_target.selected_rect) {
            // this.judgeIntersectWalls();
            this._cad_mode_handler._is_moving_element = true;
        }
        else {
            this._cad_mode_handler._is_moving_element = false;
        }

        if(this.selected_target.selected_rect)
        {
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) || null;
            this.EventSystem.emit_M(EventName.SelectingTarget, entity, ev._ev);
        }
        else{
            this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);

        }
        this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_rect);
        this.selected_target.selected_transform_element.startTransform(this.exsorb_rects);
        this._last_pos = new Vector3(ev.posX, ev.posY, 0);
        this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);

    }
    onmousemove(ev: I_MouseEvent): void {
        if (ev.buttons != 1) return;

        const time_diff = Date.now() - this._mouse_down_time;
        const pos = new Vector3(ev.posX, ev.posY, 0);
        const distance = this._mouse_down_pos ? pos.distanceTo(this._mouse_down_pos) : 0;

        // 加上判断鼠标按下弹起时间与鼠标位置移动的判断，防止鼠标按下弹起就因吸附做了移动操作
        if (time_diff < this.MAXIMUM_CLICK_DURATION || distance < this.MINIMUM_MOVE_DISTANCE) {
            return;
        }

        let transform_element = this.selected_target.selected_transform_element;
        if (!transform_element) return;

        this._cad_mode_handler._is_moving_element = true;

        let current_pos = new Vector3(ev.posX, ev.posY, 0);

        let movement = current_pos.clone().sub(this._last_pos);

        if(transform_element._target_rect)
        {
            let nor_val = movement.dot(transform_element._target_rect.nor);
            movement = transform_element._target_rect.nor.clone().multiplyScalar(nor_val);
        }
        transform_element.applyTransformByMovement(movement, null);

        this.updateTransformElements();

        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        const time_diff = Date.now() - this._mouse_down_time;
        const current_pos = new Vector3(ev.posX, ev.posY, 0);
        const distance = this._mouse_down_pos ? current_pos.distanceTo(this._mouse_down_pos) : 0;
        this.updateAttributes("init");
        // 加上判断鼠标按下弹起时间与鼠标位置移动的判断，防止鼠标按下弹起就因吸附做了移动操作
        if (time_diff < this.MAXIMUM_CLICK_DURATION || distance < this.MINIMUM_MOVE_DISTANCE) {
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
            this.EventSystem.emit_M(EventName.SelectingTarget, entity, ev._ev);
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            this.update();
            return;
        }

        let wall = null;  
        let origin_shape_rect = this.selected_target.selected_transform_element?._origin_shape_rect;
        let target_rect = this.selected_target.selected_transform_element?._target_rect;
        let movement = new Vector3(0,0,0);
        if(origin_shape_rect && target_rect)
        {
            movement = target_rect.rect_center.clone().sub(origin_shape_rect.rect_center);
        }

        if(this.selected_target.selected_rect)
        {

            wall = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TWall;
            if(wall)
            {
                wall.updateNewWall(this.container._wall_entities);
                wall.updateWinDoorLength();
            }

        }
        if (this.selected_target.selected_transform_element) {
            this.selected_target.selected_transform_element.doneTransform()
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info && movement.length() > 10) {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);
            // 对齐线吸附
            this.EventSystem.emit_M(EventName.SelectingTarget, this.selected_target.selected_rect, ev._ev);
        }
        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
        this.updateCandidateRects();
        if(wall?._delete_walls?.length > 0)
        {
            this.cleanSelection();
            wall._delete_walls = [];
        }
        this._cad_mode_handler._is_moving_element = false;

        this.update();

    }

    judgeIntersectWalls()
    {
        let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TWall;
        if(!entity){
            return;
        }
        entity.initNeighborsWalls(this.container._wall_entities);
        for(let wall of entity._vertical_neighbor_walls)
        {
            wall.more_intersection = false;
            for(let n_wall of entity._wall_neighbors)
            {
                if(wall.uidN == n_wall.wall.uidN) continue;

                let mid_line1 = wall._mid_line_edge._deep_clone();
                mid_line1.expandLength(this._thickness);
                mid_line1._nor.copy(wall.rect._nor);
                let mid_line2 = n_wall.wall._mid_line_edge._deep_clone();
                mid_line2.expandLength(this._thickness);
                mid_line2._nor.copy(n_wall.wall.rect._nor);
    
                if(Math.abs(wall.rect.nor.dot(n_wall.wall.rect.nor)) > 0 ) continue;
                let intersect_pos = mid_line1.checkIntersection(mid_line2);       
                if(intersect_pos)
                {
                    wall.more_intersection = true;
                    TWall.extendWall(entity, intersect_pos.point, this._thickness);
                    TWall.extendWall(n_wall.wall, intersect_pos.point, this._thickness);
                    return;
                }
            }
        }
    }

    updateCandidateRects() {
        this.candidate_rects.length = 0;
        this.candidate_rects.push(...this.container.getCandidateRects(["Wall"]));        
        this.updateExsorbRects();
    }
    updateExsorbRects(): void {
        this.exsorb_rects.length = 0;
    }
}