import { ENV } from "@svg/request";
import { IframeBusinessType } from "./RoyEngineProtocol";
import { IframeMessage } from "./RoyEngineProtocol";
import { RoyMsgCabinet } from "./RoyMsgCabinet";
import { RoyMsgScene } from "./RoyMsgScene";


/**
* @description 处理和实验室应用之间的消息
* 将消息派发到不同应用
* RoyEngine渲染
* AI搭柜
* <AUTHOR>
* @date 2025-04-11
* @lastEditTime 2025-04-11 14:15:59
* @lastEditors xuld
*/
export class RoyMsgEngine {
    private static _isInitEventListener: boolean = false;
    public static HOST_PRE = "https://pre-xr.3vjia.com";
    public static HOST = "https://xr.3vjia.com";
    public static PATH = "/appAILightDesign/";

    private static getHost() {
        if (ENV === "prod") {
            return RoyMsgEngine.HOST;
        } else {
            return RoyMsgEngine.HOST_PRE;
        }
    }

    public static getUrl(params: Record<string, string | number | boolean>): string {
        return RoyMsgEngine.buildUrl(RoyMsgEngine.getHost(), RoyMsgEngine.PATH, params);
    }

    public static buildUrl(host: string, path: string, params: Record<string, string | number | boolean>): string {
        let queryString = "";
        for (let key in params) {
            if (queryString) {
                queryString += "&";
            }
            queryString += `${key}=${params[key]}`;
        }
        // 标准 URL 形式 scheme://host:[port]/path?query#[fragment]
        // scheme: 协议，形如 http, https, ws, wss
        // host: 主机名，形如 www.example.com
        // port: 端口号，可选，形如 8080
        // path: 路径，形如 /path/to/resource
        // query: 查询参数，形如 ?key=value&key=value
        // fragment: 片段，可选，形如 #section
        return `${host}${path}?${queryString}`;
    }

    public static init() {
        if (this._isInitEventListener) {
            return;
        }
        this._isInitEventListener = true;
        window.addEventListener('message', (event) => {
            if (event.origin !== RoyMsgEngine.HOST) {
                return;
            }
            this.onMessage(event.data);
        });
    }

    public static onMessage(msg: IframeMessage) {
        if (msg.businessType === IframeBusinessType.SvgRoyScene) {
            RoyMsgScene.onMessage(msg);
        }
        else if (msg.businessType === IframeBusinessType.ParamAICabinet) {
            RoyMsgCabinet.onMessage(msg);
        } else {
            console.error("unknown business type", JSON.stringify(msg));
        }
    }

    public static postMessage(iframeRef: HTMLIFrameElement, msg: IframeMessage) {
        if (!this._isInitEventListener) {
            console.warn("engine not init event listener");
            return;
        }
        if (!iframeRef || !iframeRef.contentWindow) {
            console.error('Invalid iframe reference or contentWindow is not available');
            return;
        }
        iframeRef.contentWindow.postMessage(msg, '*');
    }
}

(globalThis as any).RoyMsgEngine = RoyMsgEngine;