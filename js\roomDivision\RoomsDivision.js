



// The RoomsDivisionModule object: Our interface to the outside world. We import

// and export values on it. There are various ways RoomsDivisionModule can be used:

// 1. Not defined. We create it here

// 2. A function parameter, function(RoomsDivisionModule) { ..generated code.. }

// 3. pre-run appended it, var RoomsDivisionModule = {}; ..generated code..

// 4. External script tag defines var RoomsDivisionModule.

// We need to check if RoomsDivisionModule already exists (e.g. case 3 above).

// Substitution will be replaced with actual code on later stage of the build,

// this way Closure Compiler will not mangle it (e.g. case 4. above).

// Note that if you want to run closure, and also to use RoomsDivisionModule

// after the generated code, you will need to define   var RoomsDivisionModule = {};

// before the code. Then that object will be used in the code, and you

// can continue to use RoomsDivisionModule afterwards as well.

var RoomsDivisionModule = typeof RoomsDivisionModule !== 'undefined' ? RoomsDivisionModule : {};



// --pre-jses are emitted after the RoomsDivisionModule integration code, so that they can

// refer to RoomsDivisionModule (if they choose; they can also define RoomsDivisionModule)

// {{PRE_JSES}}



// Sometimes an existing RoomsDivisionModule object exists with properties

// meant to overwrite the default module functionality. Here

// we collect those properties and reapply _after_ we configure

// the current environment's defaults to avoid having to be so

// defensive during initialization.

var moduleOverrides = {};

var key;

for (key in RoomsDivisionModule) {

  if (RoomsDivisionModule.hasOwnProperty(key)) {

    moduleOverrides[key] = RoomsDivisionModule[key];

  }

}



var arguments_ = [];

var thisProgram = './this.program';

var quit_ = function(status, toThrow) {

  throw toThrow;

};



// Determine the runtime environment we are in. You can customize this by

// setting the ENVIRONMENT setting at compile time (see settings.js).



// Attempt to auto-detect the environment

var ENVIRONMENT_IS_WEB = typeof window === 'object';

var ENVIRONMENT_IS_WORKER = typeof importScripts === 'function';

// N.b. Electron.js environment is simultaneously a NODE-environment, but

// also a web environment.

var ENVIRONMENT_IS_NODE = typeof process === 'object' && typeof process.versions === 'object' && typeof process.versions.node === 'string';

var ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER;



if (RoomsDivisionModule['ENVIRONMENT']) {

  throw new Error('RoomsDivisionModule.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)');

}



// `/` should be present at the end if `scriptDirectory` is not empty

var scriptDirectory = '';

function locateFile(path) {

  if (RoomsDivisionModule['locateFile']) {

    return RoomsDivisionModule['locateFile'](path, scriptDirectory);

  }

  return scriptDirectory + path;

}



// Hooks that are implemented differently in different runtime environments.

var read_,

    readAsync,

    readBinary,

    setWindowTitle;



// Normally we don't log exceptions but instead let them bubble out the top

// level where the embedding environment (e.g. the browser) can handle

// them.

// However under v8 and node we sometimes exit the process direcly in which case

// its up to use us to log the exception before exiting.

// If we fix https://github.com/emscripten-core/emscripten/issues/15080

// this may no longer be needed under node.

function logExceptionOnExit(e) {

  if (e instanceof ExitStatus) return;

  var toLog = e;

  if (e && typeof e === 'object' && e.stack) {

    toLog = [e, e.stack];

  }

  err('exiting due to exception: ' + toLog);

}



var nodeFS;

var nodePath;



if (ENVIRONMENT_IS_NODE) {

  if (!(typeof process === 'object' && typeof require === 'function')) throw new Error('not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)');

  if (ENVIRONMENT_IS_WORKER) {

    scriptDirectory = require('path').dirname(scriptDirectory) + '/';

  } else {

    scriptDirectory = __dirname + '/';

  }



// include: node_shell_read.js





read_ = function shell_read(filename, binary) {

  if (!nodeFS) nodeFS = require('fs');

  if (!nodePath) nodePath = require('path');

  filename = nodePath['normalize'](filename);

  return nodeFS['readFileSync'](filename, binary ? null : 'utf8');

};



readBinary = function readBinary(filename) {

  var ret = read_(filename, true);

  if (!ret.buffer) {

    ret = new Uint8Array(ret);

  }

  assert(ret.buffer);

  return ret;

};



readAsync = function readAsync(filename, onload, onerror) {

  if (!nodeFS) nodeFS = require('fs');

  if (!nodePath) nodePath = require('path');

  filename = nodePath['normalize'](filename);

  nodeFS['readFile'](filename, function(err, data) {

    if (err) onerror(err);

    else onload(data.buffer);

  });

};



// end include: node_shell_read.js

  if (process['argv'].length > 1) {

    thisProgram = process['argv'][1].replace(/\\/g, '/');

  }



  arguments_ = process['argv'].slice(2);



  if (typeof module !== 'undefined') {

    module['exports'] = RoomsDivisionModule;

  }



  process['on']('uncaughtException', function(ex) {

    // suppress ExitStatus exceptions from showing an error

    if (!(ex instanceof ExitStatus)) {

      throw ex;

    }

  });



  // Without this older versions of node (< v15) will log unhandled rejections

  // but return 0, which is not normally the desired behaviour.  This is

  // not be needed with node v15 and about because it is now the default

  // behaviour:

  // See https://nodejs.org/api/cli.html#cli_unhandled_rejections_mode

  process['on']('unhandledRejection', function(reason) { throw reason; });



  quit_ = function(status, toThrow) {

    if (keepRuntimeAlive()) {

      process['exitCode'] = status;

      throw toThrow;

    }

    logExceptionOnExit(toThrow);

    process['exit'](status);

  };



  RoomsDivisionModule['inspect'] = function () { return '[Emscripten RoomsDivisionModule object]'; };



} else

if (ENVIRONMENT_IS_SHELL) {



  if ((typeof process === 'object' && typeof require === 'function') || typeof window === 'object' || typeof importScripts === 'function') throw new Error('not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)');



  if (typeof read != 'undefined') {

    read_ = function shell_read(f) {

      return read(f);

    };

  }



  readBinary = function readBinary(f) {

    var data;

    if (typeof readbuffer === 'function') {

      return new Uint8Array(readbuffer(f));

    }

    data = read(f, 'binary');

    assert(typeof data === 'object');

    return data;

  };



  readAsync = function readAsync(f, onload, onerror) {

    setTimeout(function() { onload(readBinary(f)); }, 0);

  };



  if (typeof scriptArgs != 'undefined') {

    arguments_ = scriptArgs;

  } else if (typeof arguments != 'undefined') {

    arguments_ = arguments;

  }



  if (typeof quit === 'function') {

    quit_ = function(status, toThrow) {

      logExceptionOnExit(toThrow);

      quit(status);

    };

  }



  if (typeof print !== 'undefined') {

    // Prefer to use print/printErr where they exist, as they usually work better.

    if (typeof console === 'undefined') console = /** @type{!Console} */({});

    console.log = /** @type{!function(this:Console, ...*): undefined} */ (print);

    console.warn = console.error = /** @type{!function(this:Console, ...*): undefined} */ (typeof printErr !== 'undefined' ? printErr : print);

  }



} else



// Note that this includes Node.js workers when relevant (pthreads is enabled).

// Node.js workers are detected as a combination of ENVIRONMENT_IS_WORKER and

// ENVIRONMENT_IS_NODE.

if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {

  if (ENVIRONMENT_IS_WORKER) { // Check worker, not web, since window could be polyfilled

    scriptDirectory = self.location.href;

  } else if (typeof document !== 'undefined' && document.currentScript) { // web

    scriptDirectory = document.currentScript.src;

  }

  // blob urls look like blob:http://site.com/etc/etc and we cannot infer anything from them.

  // otherwise, slice off the final part of the url to find the script directory.

  // if scriptDirectory does not contain a slash, lastIndexOf will return -1,

  // and scriptDirectory will correctly be replaced with an empty string.

  // If scriptDirectory contains a query (starting with ?) or a fragment (starting with #),

  // they are removed because they could contain a slash.

  if (scriptDirectory.indexOf('blob:') !== 0) {

    scriptDirectory = scriptDirectory.substr(0, scriptDirectory.replace(/[?#].*/, "").lastIndexOf('/')+1);

  } else {

    scriptDirectory = '';

  }



  if (!(typeof window === 'object' || typeof importScripts === 'function')) throw new Error('not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)');



  // Differentiate the Web Worker from the Node Worker case, as reading must

  // be done differently.

  {



// include: web_or_worker_shell_read.js





  read_ = function(url) {

      var xhr = new XMLHttpRequest();

      xhr.open('GET', url, false);

      xhr.send(null);

      return xhr.responseText;

  };



  if (ENVIRONMENT_IS_WORKER) {

    readBinary = function(url) {

        var xhr = new XMLHttpRequest();

        xhr.open('GET', url, false);

        xhr.responseType = 'arraybuffer';

        xhr.send(null);

        return new Uint8Array(/** @type{!ArrayBuffer} */(xhr.response));

    };

  }



  readAsync = function(url, onload, onerror) {

    var xhr = new XMLHttpRequest();

    xhr.open('GET', url, true);

    xhr.responseType = 'arraybuffer';

    xhr.onload = function() {

      if (xhr.status == 200 || (xhr.status == 0 && xhr.response)) { // file URLs can return 0

        onload(xhr.response);

        return;

      }

      onerror();

    };

    xhr.onerror = onerror;

    xhr.send(null);

  };



// end include: web_or_worker_shell_read.js

  }



  setWindowTitle = function(title) { document.title = title };

} else

{

  throw new Error('environment detection error');

}



var out = RoomsDivisionModule['print'] || console.log.bind(console);

var err = RoomsDivisionModule['printErr'] || console.warn.bind(console);



// Merge back in the overrides

for (key in moduleOverrides) {

  if (moduleOverrides.hasOwnProperty(key)) {

    RoomsDivisionModule[key] = moduleOverrides[key];

  }

}

// Free the object hierarchy contained in the overrides, this lets the GC

// reclaim data used e.g. in memoryInitializerRequest, which is a large typed array.

moduleOverrides = null;



// Emit code to handle expected values on the RoomsDivisionModule object. This applies RoomsDivisionModule.x

// to the proper local x. This has two benefits: first, we only emit it if it is

// expected to arrive, and second, by using a local everywhere else that can be

// minified.



if (RoomsDivisionModule['arguments']) arguments_ = RoomsDivisionModule['arguments'];

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'arguments')) {

  Object.defineProperty(RoomsDivisionModule, 'arguments', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.arguments has been replaced with plain arguments_ (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



if (RoomsDivisionModule['thisProgram']) thisProgram = RoomsDivisionModule['thisProgram'];

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'thisProgram')) {

  Object.defineProperty(RoomsDivisionModule, 'thisProgram', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.thisProgram has been replaced with plain thisProgram (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



if (RoomsDivisionModule['quit']) quit_ = RoomsDivisionModule['quit'];

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'quit')) {

  Object.defineProperty(RoomsDivisionModule, 'quit', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.quit has been replaced with plain quit_ (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



// perform assertions in shell.js after we set up out() and err(), as otherwise if an assertion fails it cannot print the message

// Assertions on removed incoming RoomsDivisionModule JS APIs.

assert(typeof RoomsDivisionModule['memoryInitializerPrefixURL'] === 'undefined', 'RoomsDivisionModule.memoryInitializerPrefixURL option was removed, use RoomsDivisionModule.locateFile instead');

assert(typeof RoomsDivisionModule['pthreadMainPrefixURL'] === 'undefined', 'RoomsDivisionModule.pthreadMainPrefixURL option was removed, use RoomsDivisionModule.locateFile instead');

assert(typeof RoomsDivisionModule['cdInitializerPrefixURL'] === 'undefined', 'RoomsDivisionModule.cdInitializerPrefixURL option was removed, use RoomsDivisionModule.locateFile instead');

assert(typeof RoomsDivisionModule['filePackagePrefixURL'] === 'undefined', 'RoomsDivisionModule.filePackagePrefixURL option was removed, use RoomsDivisionModule.locateFile instead');

assert(typeof RoomsDivisionModule['read'] === 'undefined', 'RoomsDivisionModule.read option was removed (modify read_ in JS)');

assert(typeof RoomsDivisionModule['readAsync'] === 'undefined', 'RoomsDivisionModule.readAsync option was removed (modify readAsync in JS)');

assert(typeof RoomsDivisionModule['readBinary'] === 'undefined', 'RoomsDivisionModule.readBinary option was removed (modify readBinary in JS)');

assert(typeof RoomsDivisionModule['setWindowTitle'] === 'undefined', 'RoomsDivisionModule.setWindowTitle option was removed (modify setWindowTitle in JS)');

assert(typeof RoomsDivisionModule['TOTAL_MEMORY'] === 'undefined', 'RoomsDivisionModule.TOTAL_MEMORY has been renamed RoomsDivisionModule.INITIAL_MEMORY');



if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'read')) {

  Object.defineProperty(RoomsDivisionModule, 'read', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.read has been replaced with plain read_ (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'readAsync')) {

  Object.defineProperty(RoomsDivisionModule, 'readAsync', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.readAsync has been replaced with plain readAsync (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'readBinary')) {

  Object.defineProperty(RoomsDivisionModule, 'readBinary', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.readBinary has been replaced with plain readBinary (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'setWindowTitle')) {

  Object.defineProperty(RoomsDivisionModule, 'setWindowTitle', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}

var IDBFS = 'IDBFS is no longer included by default; build with -lidbfs.js';

var PROXYFS = 'PROXYFS is no longer included by default; build with -lproxyfs.js';

var WORKERFS = 'WORKERFS is no longer included by default; build with -lworkerfs.js';

var NODEFS = 'NODEFS is no longer included by default; build with -lnodefs.js';





assert(!ENVIRONMENT_IS_SHELL, "shell environment detected but not enabled at build time.  Add 'shell' to `-s ENVIRONMENT` to enable.");









var STACK_ALIGN = 16;

var POINTER_SIZE = 4;



function getNativeTypeSize(type) {

  switch (type) {

    case 'i1': case 'i8': return 1;

    case 'i16': return 2;

    case 'i32': return 4;

    case 'i64': return 8;

    case 'float': return 4;

    case 'double': return 8;

    default: {

      if (type[type.length-1] === '*') {

        return POINTER_SIZE;

      } else if (type[0] === 'i') {

        var bits = Number(type.substr(1));

        assert(bits % 8 === 0, 'getNativeTypeSize invalid bits ' + bits + ', type ' + type);

        return bits / 8;

      } else {

        return 0;

      }

    }

  }

}



function warnOnce(text) {

  if (!warnOnce.shown) warnOnce.shown = {};

  if (!warnOnce.shown[text]) {

    warnOnce.shown[text] = 1;

    err(text);

  }

}



// include: runtime_functions.js





// Wraps a JS function as a wasm function with a given signature.

function convertJsFunctionToWasm(func, sig) {



  // If the type reflection proposal is available, use the new

  // "WebAssembly.Function" constructor.

  // Otherwise, construct a minimal wasm module importing the JS function and

  // re-exporting it.

  if (typeof WebAssembly.Function === "function") {

    var typeNames = {

      'i': 'i32',

      'j': 'i64',

      'f': 'f32',

      'd': 'f64'

    };

    var type = {

      parameters: [],

      results: sig[0] == 'v' ? [] : [typeNames[sig[0]]]

    };

    for (var i = 1; i < sig.length; ++i) {

      type.parameters.push(typeNames[sig[i]]);

    }

    return new WebAssembly.Function(type, func);

  }



  // The module is static, with the exception of the type section, which is

  // generated based on the signature passed in.

  var typeSection = [

    0x01, // id: section,

    0x00, // length: 0 (placeholder)

    0x01, // count: 1

    0x60, // form: func

  ];

  var sigRet = sig.slice(0, 1);

  var sigParam = sig.slice(1);

  var typeCodes = {

    'i': 0x7f, // i32

    'j': 0x7e, // i64

    'f': 0x7d, // f32

    'd': 0x7c, // f64

  };



  // Parameters, length + signatures

  typeSection.push(sigParam.length);

  for (var i = 0; i < sigParam.length; ++i) {

    typeSection.push(typeCodes[sigParam[i]]);

  }



  // Return values, length + signatures

  // With no multi-return in MVP, either 0 (void) or 1 (anything else)

  if (sigRet == 'v') {

    typeSection.push(0x00);

  } else {

    typeSection = typeSection.concat([0x01, typeCodes[sigRet]]);

  }



  // Write the overall length of the type section back into the section header

  // (excepting the 2 bytes for the section id and length)

  typeSection[1] = typeSection.length - 2;



  // Rest of the module is static

  var bytes = new Uint8Array([

    0x00, 0x61, 0x73, 0x6d, // magic ("\0asm")

    0x01, 0x00, 0x00, 0x00, // version: 1

  ].concat(typeSection, [

    0x02, 0x07, // import section

      // (import "e" "f" (func 0 (type 0)))

      0x01, 0x01, 0x65, 0x01, 0x66, 0x00, 0x00,

    0x07, 0x05, // export section

      // (export "f" (func 0 (type 0)))

      0x01, 0x01, 0x66, 0x00, 0x00,

  ]));



   // We can compile this wasm module synchronously because it is very small.

  // This accepts an import (at "e.f"), that it reroutes to an export (at "f")

  var module = new WebAssembly.RoomsDivisionModule(bytes);

  var instance = new WebAssembly.Instance(module, {

    'e': {

      'f': func

    }

  });

  var wrappedFunc = instance.exports['f'];

  return wrappedFunc;

}



var freeTableIndexes = [];



// Weak map of functions in the table to their indexes, created on first use.

var functionsInTableMap;



function getEmptyTableSlot() {

  // Reuse a free index if there is one, otherwise grow.

  if (freeTableIndexes.length) {

    return freeTableIndexes.pop();

  }

  // Grow the table

  try {

    wasmTable.grow(1);

  } catch (err) {

    if (!(err instanceof RangeError)) {

      throw err;

    }

    throw 'Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.';

  }

  return wasmTable.length - 1;

}



function updateTableMap(offset, count) {

  for (var i = offset; i < offset + count; i++) {

    var item = getWasmTableEntry(i);

    // Ignore null values.

    if (item) {

      functionsInTableMap.set(item, i);

    }

  }

}



// Add a function to the table.

// 'sig' parameter is required if the function being added is a JS function.

function addFunction(func, sig) {

  assert(typeof func !== 'undefined');



  // Check if the function is already in the table, to ensure each function

  // gets a unique index. First, create the map if this is the first use.

  if (!functionsInTableMap) {

    functionsInTableMap = new WeakMap();

    updateTableMap(0, wasmTable.length);

  }

  if (functionsInTableMap.has(func)) {

    return functionsInTableMap.get(func);

  }



  // It's not in the table, add it now.



  var ret = getEmptyTableSlot();



  // Set the new value.

  try {

    // Attempting to call this with JS function will cause of table.set() to fail

    setWasmTableEntry(ret, func);

  } catch (err) {

    if (!(err instanceof TypeError)) {

      throw err;

    }

    assert(typeof sig !== 'undefined', 'Missing signature argument to addFunction: ' + func);

    var wrapped = convertJsFunctionToWasm(func, sig);

    setWasmTableEntry(ret, wrapped);

  }



  functionsInTableMap.set(func, ret);



  return ret;

}



function removeFunction(index) {

  functionsInTableMap.delete(getWasmTableEntry(index));

  freeTableIndexes.push(index);

}



// end include: runtime_functions.js

// include: runtime_debug.js





// end include: runtime_debug.js

var tempRet0 = 0;



var setTempRet0 = function(value) {

  tempRet0 = value;

};



var getTempRet0 = function() {

  return tempRet0;

};







// === Preamble library stuff ===



// Documentation for the public APIs defined in this file must be updated in:

//    site/source/docs/api_reference/preamble.js.rst

// A prebuilt local version of the documentation is available at:

//    site/build/text/docs/api_reference/preamble.js.txt

// You can also build docs locally as HTML or other formats in site/

// An online HTML version (which may be of a different version of Emscripten)

//    is up at http://kripken.github.io/emscripten-site/docs/api_reference/preamble.js.html



var wasmBinary;

if (RoomsDivisionModule['wasmBinary']) wasmBinary = RoomsDivisionModule['wasmBinary'];

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'wasmBinary')) {

  Object.defineProperty(RoomsDivisionModule, 'wasmBinary', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}

var noExitRuntime = RoomsDivisionModule['noExitRuntime'] || true;

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'noExitRuntime')) {

  Object.defineProperty(RoomsDivisionModule, 'noExitRuntime', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



if (typeof WebAssembly !== 'object') {

  abort('no native wasm support detected');

}



// include: runtime_safe_heap.js





// In MINIMAL_RUNTIME, setValue() and getValue() are only available when building with safe heap enabled, for heap safety checking.

// In traditional runtime, setValue() and getValue() are always available (although their use is highly discouraged due to perf penalties)



/** @param {number} ptr

    @param {number} value

    @param {string} type

    @param {number|boolean=} noSafe */

function setValue(ptr, value, type, noSafe) {

  type = type || 'i8';

  if (type.charAt(type.length-1) === '*') type = 'i32';

    switch (type) {

      case 'i1': HEAP8[((ptr)>>0)] = value; break;

      case 'i8': HEAP8[((ptr)>>0)] = value; break;

      case 'i16': HEAP16[((ptr)>>1)] = value; break;

      case 'i32': HEAP32[((ptr)>>2)] = value; break;

      case 'i64': (tempI64 = [value>>>0,(tempDouble=value,(+(Math.abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math.min((+(Math.floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math.ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[((ptr)>>2)] = tempI64[0],HEAP32[(((ptr)+(4))>>2)] = tempI64[1]); break;

      case 'float': HEAPF32[((ptr)>>2)] = value; break;

      case 'double': HEAPF64[((ptr)>>3)] = value; break;

      default: abort('invalid type for setValue: ' + type);

    }

}



/** @param {number} ptr

    @param {string} type

    @param {number|boolean=} noSafe */

function getValue(ptr, type, noSafe) {

  type = type || 'i8';

  if (type.charAt(type.length-1) === '*') type = 'i32';

    switch (type) {

      case 'i1': return HEAP8[((ptr)>>0)];

      case 'i8': return HEAP8[((ptr)>>0)];

      case 'i16': return HEAP16[((ptr)>>1)];

      case 'i32': return HEAP32[((ptr)>>2)];

      case 'i64': return HEAP32[((ptr)>>2)];

      case 'float': return HEAPF32[((ptr)>>2)];

      case 'double': return Number(HEAPF64[((ptr)>>3)]);

      default: abort('invalid type for getValue: ' + type);

    }

  return null;

}



// end include: runtime_safe_heap.js

// Wasm globals



var wasmMemory;



//========================================

// Runtime essentials

//========================================



// whether we are quitting the application. no code should run after this.

// set in exit() and abort()

var ABORT = false;



// set by exit() and abort().  Passed to 'onExit' handler.

// NOTE: This is also used as the process return code code in shell environments

// but only when noExitRuntime is false.

var EXITSTATUS;



/** @type {function(*, string=)} */

function assert(condition, text) {

  if (!condition) {

    abort('Assertion failed: ' + text);

  }

}



// Returns the C function with a specified identifier (for C++, you need to do manual name mangling)

function getCFunc(ident) {

  var func = RoomsDivisionModule['_' + ident]; // closure exported function

  assert(func, 'Cannot call unknown function ' + ident + ', make sure it is exported');

  return func;

}



// C calling interface.

/** @param {string|null=} returnType

    @param {Array=} argTypes

    @param {Arguments|Array=} args

    @param {Object=} opts */

function ccall(ident, returnType, argTypes, args, opts) {

  // For fast lookup of conversion functions

  var toC = {

    'string': function(str) {

      var ret = 0;

      if (str !== null && str !== undefined && str !== 0) { // null string

        // at most 4 bytes per UTF-8 code point, +1 for the trailing '\0'

        var len = (str.length << 2) + 1;

        ret = stackAlloc(len);

        stringToUTF8(str, ret, len);

      }

      return ret;

    },

    'array': function(arr) {

      var ret = stackAlloc(arr.length);

      writeArrayToMemory(arr, ret);

      return ret;

    }

  };



  function convertReturnValue(ret) {

    if (returnType === 'string') return UTF8ToString(ret);

    if (returnType === 'boolean') return Boolean(ret);

    return ret;

  }



  var func = getCFunc(ident);

  var cArgs = [];

  var stack = 0;

  assert(returnType !== 'array', 'Return type should not be "array".');

  if (args) {

    for (var i = 0; i < args.length; i++) {

      var converter = toC[argTypes[i]];

      if (converter) {

        if (stack === 0) stack = stackSave();

        cArgs[i] = converter(args[i]);

      } else {

        cArgs[i] = args[i];

      }

    }

  }

  var ret = func.apply(null, cArgs);

  function onDone(ret) {

    if (stack !== 0) stackRestore(stack);

    return convertReturnValue(ret);

  }



  ret = onDone(ret);

  return ret;

}



/** @param {string=} returnType

    @param {Array=} argTypes

    @param {Object=} opts */

function cwrap(ident, returnType, argTypes, opts) {

  return function() {

    return ccall(ident, returnType, argTypes, arguments, opts);

  }

}



// We used to include malloc/free by default in the past. Show a helpful error in

// builds with assertions.



var ALLOC_NORMAL = 0; // Tries to use _malloc()

var ALLOC_STACK = 1; // Lives for the duration of the current function call



// allocate(): This is for internal use. You can use it yourself as well, but the interface

//             is a little tricky (see docs right below). The reason is that it is optimized

//             for multiple syntaxes to save space in generated code. So you should

//             normally not use allocate(), and instead allocate memory using _malloc(),

//             initialize it with setValue(), and so forth.

// @slab: An array of data.

// @allocator: How to allocate memory, see ALLOC_*

/** @type {function((Uint8Array|Array<number>), number)} */

function allocate(slab, allocator) {

  var ret;

  assert(typeof allocator === 'number', 'allocate no longer takes a type argument')

  assert(typeof slab !== 'number', 'allocate no longer takes a number as arg0')



  if (allocator == ALLOC_STACK) {

    ret = stackAlloc(slab.length);

  } else {

    ret = _malloc(slab.length);

  }



  if (slab.subarray || slab.slice) {

    HEAPU8.set(/** @type {!Uint8Array} */(slab), ret);

  } else {

    HEAPU8.set(new Uint8Array(slab), ret);

  }

  return ret;

}



// include: runtime_strings.js





// runtime_strings.js: Strings related runtime functions that are part of both MINIMAL_RUNTIME and regular runtime.



// Given a pointer 'ptr' to a null-terminated UTF8-encoded string in the given array that contains uint8 values, returns

// a copy of that string as a Javascript String object.



var UTF8Decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder('utf8') : undefined;



/**

 * @param {number} idx

 * @param {number=} maxBytesToRead

 * @return {string}

 */

function UTF8ArrayToString(heap, idx, maxBytesToRead) {

  var endIdx = idx + maxBytesToRead;

  var endPtr = idx;

  // TextDecoder needs to know the byte length in advance, it doesn't stop on null terminator by itself.

  // Also, use the length info to avoid running tiny strings through TextDecoder, since .subarray() allocates garbage.

  // (As a tiny code save trick, compare endPtr against endIdx using a negation, so that undefined means Infinity)

  while (heap[endPtr] && !(endPtr >= endIdx)) ++endPtr;



  if (endPtr - idx > 16 && heap.subarray && UTF8Decoder) {

    return UTF8Decoder.decode(heap.subarray(idx, endPtr));

  } else {

    var str = '';

    // If building with TextDecoder, we have already computed the string length above, so test loop end condition against that

    while (idx < endPtr) {

      // For UTF8 byte structure, see:

      // http://en.wikipedia.org/wiki/UTF-8#Description

      // https://www.ietf.org/rfc/rfc2279.txt

      // https://tools.ietf.org/html/rfc3629

      var u0 = heap[idx++];

      if (!(u0 & 0x80)) { str += String.fromCharCode(u0); continue; }

      var u1 = heap[idx++] & 63;

      if ((u0 & 0xE0) == 0xC0) { str += String.fromCharCode(((u0 & 31) << 6) | u1); continue; }

      var u2 = heap[idx++] & 63;

      if ((u0 & 0xF0) == 0xE0) {

        u0 = ((u0 & 15) << 12) | (u1 << 6) | u2;

      } else {

        if ((u0 & 0xF8) != 0xF0) warnOnce('Invalid UTF-8 leading byte 0x' + u0.toString(16) + ' encountered when deserializing a UTF-8 string in wasm memory to a JS string!');

        u0 = ((u0 & 7) << 18) | (u1 << 12) | (u2 << 6) | (heap[idx++] & 63);

      }



      if (u0 < 0x10000) {

        str += String.fromCharCode(u0);

      } else {

        var ch = u0 - 0x10000;

        str += String.fromCharCode(0xD800 | (ch >> 10), 0xDC00 | (ch & 0x3FF));

      }

    }

  }

  return str;

}



// Given a pointer 'ptr' to a null-terminated UTF8-encoded string in the emscripten HEAP, returns a

// copy of that string as a Javascript String object.

// maxBytesToRead: an optional length that specifies the maximum number of bytes to read. You can omit

//                 this parameter to scan the string until the first \0 byte. If maxBytesToRead is

//                 passed, and the string at [ptr, ptr+maxBytesToReadr[ contains a null byte in the

//                 middle, then the string will cut short at that byte index (i.e. maxBytesToRead will

//                 not produce a string of exact length [ptr, ptr+maxBytesToRead[)

//                 N.B. mixing frequent uses of UTF8ToString() with and without maxBytesToRead may

//                 throw JS JIT optimizations off, so it is worth to consider consistently using one

//                 style or the other.

/**

 * @param {number} ptr

 * @param {number=} maxBytesToRead

 * @return {string}

 */

function UTF8ToString(ptr, maxBytesToRead) {

  ;

  return ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : '';

}



// Copies the given Javascript String object 'str' to the given byte array at address 'outIdx',

// encoded in UTF8 form and null-terminated. The copy will require at most str.length*4+1 bytes of space in the HEAP.

// Use the function lengthBytesUTF8 to compute the exact number of bytes (excluding null terminator) that this function will write.

// Parameters:

//   str: the Javascript string to copy.

//   heap: the array to copy to. Each index in this array is assumed to be one 8-byte element.

//   outIdx: The starting offset in the array to begin the copying.

//   maxBytesToWrite: The maximum number of bytes this function can write to the array.

//                    This count should include the null terminator,

//                    i.e. if maxBytesToWrite=1, only the null terminator will be written and nothing else.

//                    maxBytesToWrite=0 does not write any bytes to the output, not even the null terminator.

// Returns the number of bytes written, EXCLUDING the null terminator.



function stringToUTF8Array(str, heap, outIdx, maxBytesToWrite) {

  if (!(maxBytesToWrite > 0)) // Parameter maxBytesToWrite is not optional. Negative values, 0, null, undefined and false each don't write out any bytes.

    return 0;



  var startIdx = outIdx;

  var endIdx = outIdx + maxBytesToWrite - 1; // -1 for string null terminator.

  for (var i = 0; i < str.length; ++i) {

    // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code unit, not a Unicode code point of the character! So decode UTF16->UTF32->UTF8.

    // See http://unicode.org/faq/utf_bom.html#utf16-3

    // For UTF8 byte structure, see http://en.wikipedia.org/wiki/UTF-8#Description and https://www.ietf.org/rfc/rfc2279.txt and https://tools.ietf.org/html/rfc3629

    var u = str.charCodeAt(i); // possibly a lead surrogate

    if (u >= 0xD800 && u <= 0xDFFF) {

      var u1 = str.charCodeAt(++i);

      u = 0x10000 + ((u & 0x3FF) << 10) | (u1 & 0x3FF);

    }

    if (u <= 0x7F) {

      if (outIdx >= endIdx) break;

      heap[outIdx++] = u;

    } else if (u <= 0x7FF) {

      if (outIdx + 1 >= endIdx) break;

      heap[outIdx++] = 0xC0 | (u >> 6);

      heap[outIdx++] = 0x80 | (u & 63);

    } else if (u <= 0xFFFF) {

      if (outIdx + 2 >= endIdx) break;

      heap[outIdx++] = 0xE0 | (u >> 12);

      heap[outIdx++] = 0x80 | ((u >> 6) & 63);

      heap[outIdx++] = 0x80 | (u & 63);

    } else {

      if (outIdx + 3 >= endIdx) break;

      if (u > 0x10FFFF) warnOnce('Invalid Unicode code point 0x' + u.toString(16) + ' encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF).');

      heap[outIdx++] = 0xF0 | (u >> 18);

      heap[outIdx++] = 0x80 | ((u >> 12) & 63);

      heap[outIdx++] = 0x80 | ((u >> 6) & 63);

      heap[outIdx++] = 0x80 | (u & 63);

    }

  }

  // Null-terminate the pointer to the buffer.

  heap[outIdx] = 0;

  return outIdx - startIdx;

}



// Copies the given Javascript String object 'str' to the emscripten HEAP at address 'outPtr',

// null-terminated and encoded in UTF8 form. The copy will require at most str.length*4+1 bytes of space in the HEAP.

// Use the function lengthBytesUTF8 to compute the exact number of bytes (excluding null terminator) that this function will write.

// Returns the number of bytes written, EXCLUDING the null terminator.



function stringToUTF8(str, outPtr, maxBytesToWrite) {

  assert(typeof maxBytesToWrite == 'number', 'stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!');

  return stringToUTF8Array(str, HEAPU8,outPtr, maxBytesToWrite);

}



// Returns the number of bytes the given Javascript string takes if encoded as a UTF8 byte array, EXCLUDING the null terminator byte.

function lengthBytesUTF8(str) {

  var len = 0;

  for (var i = 0; i < str.length; ++i) {

    // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code unit, not a Unicode code point of the character! So decode UTF16->UTF32->UTF8.

    // See http://unicode.org/faq/utf_bom.html#utf16-3

    var u = str.charCodeAt(i); // possibly a lead surrogate

    if (u >= 0xD800 && u <= 0xDFFF) u = 0x10000 + ((u & 0x3FF) << 10) | (str.charCodeAt(++i) & 0x3FF);

    if (u <= 0x7F) ++len;

    else if (u <= 0x7FF) len += 2;

    else if (u <= 0xFFFF) len += 3;

    else len += 4;

  }

  return len;

}



// end include: runtime_strings.js

// include: runtime_strings_extra.js





// runtime_strings_extra.js: Strings related runtime functions that are available only in regular runtime.



// Given a pointer 'ptr' to a null-terminated ASCII-encoded string in the emscripten HEAP, returns

// a copy of that string as a Javascript String object.



function AsciiToString(ptr) {

  var str = '';

  while (1) {

    var ch = HEAPU8[((ptr++)>>0)];

    if (!ch) return str;

    str += String.fromCharCode(ch);

  }

}



// Copies the given Javascript String object 'str' to the emscripten HEAP at address 'outPtr',

// null-terminated and encoded in ASCII form. The copy will require at most str.length+1 bytes of space in the HEAP.



function stringToAscii(str, outPtr) {

  return writeAsciiToMemory(str, outPtr, false);

}



// Given a pointer 'ptr' to a null-terminated UTF16LE-encoded string in the emscripten HEAP, returns

// a copy of that string as a Javascript String object.



var UTF16Decoder = typeof TextDecoder !== 'undefined' ? new TextDecoder('utf-16le') : undefined;



function UTF16ToString(ptr, maxBytesToRead) {

  assert(ptr % 2 == 0, 'Pointer passed to UTF16ToString must be aligned to two bytes!');

  var endPtr = ptr;

  // TextDecoder needs to know the byte length in advance, it doesn't stop on null terminator by itself.

  // Also, use the length info to avoid running tiny strings through TextDecoder, since .subarray() allocates garbage.

  var idx = endPtr >> 1;

  var maxIdx = idx + maxBytesToRead / 2;

  // If maxBytesToRead is not passed explicitly, it will be undefined, and this

  // will always evaluate to true. This saves on code size.

  while (!(idx >= maxIdx) && HEAPU16[idx]) ++idx;

  endPtr = idx << 1;



  if (endPtr - ptr > 32 && UTF16Decoder) {

    return UTF16Decoder.decode(HEAPU8.subarray(ptr, endPtr));

  } else {

    var str = '';



    // If maxBytesToRead is not passed explicitly, it will be undefined, and the for-loop's condition

    // will always evaluate to true. The loop is then terminated on the first null char.

    for (var i = 0; !(i >= maxBytesToRead / 2); ++i) {

      var codeUnit = HEAP16[(((ptr)+(i*2))>>1)];

      if (codeUnit == 0) break;

      // fromCharCode constructs a character from a UTF-16 code unit, so we can pass the UTF16 string right through.

      str += String.fromCharCode(codeUnit);

    }



    return str;

  }

}



// Copies the given Javascript String object 'str' to the emscripten HEAP at address 'outPtr',

// null-terminated and encoded in UTF16 form. The copy will require at most str.length*4+2 bytes of space in the HEAP.

// Use the function lengthBytesUTF16() to compute the exact number of bytes (excluding null terminator) that this function will write.

// Parameters:

//   str: the Javascript string to copy.

//   outPtr: Byte address in Emscripten HEAP where to write the string to.

//   maxBytesToWrite: The maximum number of bytes this function can write to the array. This count should include the null

//                    terminator, i.e. if maxBytesToWrite=2, only the null terminator will be written and nothing else.

//                    maxBytesToWrite<2 does not write any bytes to the output, not even the null terminator.

// Returns the number of bytes written, EXCLUDING the null terminator.



function stringToUTF16(str, outPtr, maxBytesToWrite) {

  assert(outPtr % 2 == 0, 'Pointer passed to stringToUTF16 must be aligned to two bytes!');

  assert(typeof maxBytesToWrite == 'number', 'stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!');

  // Backwards compatibility: if max bytes is not specified, assume unsafe unbounded write is allowed.

  if (maxBytesToWrite === undefined) {

    maxBytesToWrite = 0x7FFFFFFF;

  }

  if (maxBytesToWrite < 2) return 0;

  maxBytesToWrite -= 2; // Null terminator.

  var startPtr = outPtr;

  var numCharsToWrite = (maxBytesToWrite < str.length*2) ? (maxBytesToWrite / 2) : str.length;

  for (var i = 0; i < numCharsToWrite; ++i) {

    // charCodeAt returns a UTF-16 encoded code unit, so it can be directly written to the HEAP.

    var codeUnit = str.charCodeAt(i); // possibly a lead surrogate

    HEAP16[((outPtr)>>1)] = codeUnit;

    outPtr += 2;

  }

  // Null-terminate the pointer to the HEAP.

  HEAP16[((outPtr)>>1)] = 0;

  return outPtr - startPtr;

}



// Returns the number of bytes the given Javascript string takes if encoded as a UTF16 byte array, EXCLUDING the null terminator byte.



function lengthBytesUTF16(str) {

  return str.length*2;

}



function UTF32ToString(ptr, maxBytesToRead) {

  assert(ptr % 4 == 0, 'Pointer passed to UTF32ToString must be aligned to four bytes!');

  var i = 0;



  var str = '';

  // If maxBytesToRead is not passed explicitly, it will be undefined, and this

  // will always evaluate to true. This saves on code size.

  while (!(i >= maxBytesToRead / 4)) {

    var utf32 = HEAP32[(((ptr)+(i*4))>>2)];

    if (utf32 == 0) break;

    ++i;

    // Gotcha: fromCharCode constructs a character from a UTF-16 encoded code (pair), not from a Unicode code point! So encode the code point to UTF-16 for constructing.

    // See http://unicode.org/faq/utf_bom.html#utf16-3

    if (utf32 >= 0x10000) {

      var ch = utf32 - 0x10000;

      str += String.fromCharCode(0xD800 | (ch >> 10), 0xDC00 | (ch & 0x3FF));

    } else {

      str += String.fromCharCode(utf32);

    }

  }

  return str;

}



// Copies the given Javascript String object 'str' to the emscripten HEAP at address 'outPtr',

// null-terminated and encoded in UTF32 form. The copy will require at most str.length*4+4 bytes of space in the HEAP.

// Use the function lengthBytesUTF32() to compute the exact number of bytes (excluding null terminator) that this function will write.

// Parameters:

//   str: the Javascript string to copy.

//   outPtr: Byte address in Emscripten HEAP where to write the string to.

//   maxBytesToWrite: The maximum number of bytes this function can write to the array. This count should include the null

//                    terminator, i.e. if maxBytesToWrite=4, only the null terminator will be written and nothing else.

//                    maxBytesToWrite<4 does not write any bytes to the output, not even the null terminator.

// Returns the number of bytes written, EXCLUDING the null terminator.



function stringToUTF32(str, outPtr, maxBytesToWrite) {

  assert(outPtr % 4 == 0, 'Pointer passed to stringToUTF32 must be aligned to four bytes!');

  assert(typeof maxBytesToWrite == 'number', 'stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!');

  // Backwards compatibility: if max bytes is not specified, assume unsafe unbounded write is allowed.

  if (maxBytesToWrite === undefined) {

    maxBytesToWrite = 0x7FFFFFFF;

  }

  if (maxBytesToWrite < 4) return 0;

  var startPtr = outPtr;

  var endPtr = startPtr + maxBytesToWrite - 4;

  for (var i = 0; i < str.length; ++i) {

    // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code unit, not a Unicode code point of the character! We must decode the string to UTF-32 to the heap.

    // See http://unicode.org/faq/utf_bom.html#utf16-3

    var codeUnit = str.charCodeAt(i); // possibly a lead surrogate

    if (codeUnit >= 0xD800 && codeUnit <= 0xDFFF) {

      var trailSurrogate = str.charCodeAt(++i);

      codeUnit = 0x10000 + ((codeUnit & 0x3FF) << 10) | (trailSurrogate & 0x3FF);

    }

    HEAP32[((outPtr)>>2)] = codeUnit;

    outPtr += 4;

    if (outPtr + 4 > endPtr) break;

  }

  // Null-terminate the pointer to the HEAP.

  HEAP32[((outPtr)>>2)] = 0;

  return outPtr - startPtr;

}



// Returns the number of bytes the given Javascript string takes if encoded as a UTF16 byte array, EXCLUDING the null terminator byte.



function lengthBytesUTF32(str) {

  var len = 0;

  for (var i = 0; i < str.length; ++i) {

    // Gotcha: charCodeAt returns a 16-bit word that is a UTF-16 encoded code unit, not a Unicode code point of the character! We must decode the string to UTF-32 to the heap.

    // See http://unicode.org/faq/utf_bom.html#utf16-3

    var codeUnit = str.charCodeAt(i);

    if (codeUnit >= 0xD800 && codeUnit <= 0xDFFF) ++i; // possibly a lead surrogate, so skip over the tail surrogate.

    len += 4;

  }



  return len;

}



// Allocate heap space for a JS string, and write it there.

// It is the responsibility of the caller to free() that memory.

function allocateUTF8(str) {

  var size = lengthBytesUTF8(str) + 1;

  var ret = _malloc(size);

  if (ret) stringToUTF8Array(str, HEAP8, ret, size);

  return ret;

}



// Allocate stack space for a JS string, and write it there.

function allocateUTF8OnStack(str) {

  var size = lengthBytesUTF8(str) + 1;

  var ret = stackAlloc(size);

  stringToUTF8Array(str, HEAP8, ret, size);

  return ret;

}



// Deprecated: This function should not be called because it is unsafe and does not provide

// a maximum length limit of how many bytes it is allowed to write. Prefer calling the

// function stringToUTF8Array() instead, which takes in a maximum length that can be used

// to be secure from out of bounds writes.

/** @deprecated

    @param {boolean=} dontAddNull */

function writeStringToMemory(string, buffer, dontAddNull) {

  warnOnce('writeStringToMemory is deprecated and should not be called! Use stringToUTF8() instead!');



  var /** @type {number} */ lastChar, /** @type {number} */ end;

  if (dontAddNull) {

    // stringToUTF8Array always appends null. If we don't want to do that, remember the

    // character that existed at the location where the null will be placed, and restore

    // that after the write (below).

    end = buffer + lengthBytesUTF8(string);

    lastChar = HEAP8[end];

  }

  stringToUTF8(string, buffer, Infinity);

  if (dontAddNull) HEAP8[end] = lastChar; // Restore the value under the null character.

}



function writeArrayToMemory(array, buffer) {

  assert(array.length >= 0, 'writeArrayToMemory array must have a length (should be an array or typed array)')

  HEAP8.set(array, buffer);

}



/** @param {boolean=} dontAddNull */

function writeAsciiToMemory(str, buffer, dontAddNull) {

  for (var i = 0; i < str.length; ++i) {

    assert(str.charCodeAt(i) === (str.charCodeAt(i) & 0xff));

    HEAP8[((buffer++)>>0)] = str.charCodeAt(i);

  }

  // Null-terminate the pointer to the HEAP.

  if (!dontAddNull) HEAP8[((buffer)>>0)] = 0;

}



// end include: runtime_strings_extra.js

// Memory management



function alignUp(x, multiple) {

  if (x % multiple > 0) {

    x += multiple - (x % multiple);

  }

  return x;

}



var HEAP,

/** @type {ArrayBuffer} */

  buffer,

/** @type {Int8Array} */

  HEAP8,

/** @type {Uint8Array} */

  HEAPU8,

/** @type {Int16Array} */

  HEAP16,

/** @type {Uint16Array} */

  HEAPU16,

/** @type {Int32Array} */

  HEAP32,

/** @type {Uint32Array} */

  HEAPU32,

/** @type {Float32Array} */

  HEAPF32,

/** @type {Float64Array} */

  HEAPF64;



function updateGlobalBufferAndViews(buf) {

  buffer = buf;

  RoomsDivisionModule['HEAP8'] = HEAP8 = new Int8Array(buf);

  RoomsDivisionModule['HEAP16'] = HEAP16 = new Int16Array(buf);

  RoomsDivisionModule['HEAP32'] = HEAP32 = new Int32Array(buf);

  RoomsDivisionModule['HEAPU8'] = HEAPU8 = new Uint8Array(buf);

  RoomsDivisionModule['HEAPU16'] = HEAPU16 = new Uint16Array(buf);

  RoomsDivisionModule['HEAPU32'] = HEAPU32 = new Uint32Array(buf);

  RoomsDivisionModule['HEAPF32'] = HEAPF32 = new Float32Array(buf);

  RoomsDivisionModule['HEAPF64'] = HEAPF64 = new Float64Array(buf);

}



var TOTAL_STACK = 5242880;

if (RoomsDivisionModule['TOTAL_STACK']) assert(TOTAL_STACK === RoomsDivisionModule['TOTAL_STACK'], 'the stack size can no longer be determined at runtime')



var INITIAL_MEMORY = RoomsDivisionModule['INITIAL_MEMORY'] || 16777216;

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, 'INITIAL_MEMORY')) {

  Object.defineProperty(RoomsDivisionModule, 'INITIAL_MEMORY', {

    configurable: true,

    get: function() {

      abort('RoomsDivisionModule.INITIAL_MEMORY has been replaced with plain INITIAL_MEMORY (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)')

    }

  });

}



assert(INITIAL_MEMORY >= TOTAL_STACK, 'INITIAL_MEMORY should be larger than TOTAL_STACK, was ' + INITIAL_MEMORY + '! (TOTAL_STACK=' + TOTAL_STACK + ')');



// check for full engine support (use string 'subarray' to avoid closure compiler confusion)

assert(typeof Int32Array !== 'undefined' && typeof Float64Array !== 'undefined' && Int32Array.prototype.subarray !== undefined && Int32Array.prototype.set !== undefined,

       'JS engine does not provide full typed array support');



// If memory is defined in wasm, the user can't provide it.

assert(!RoomsDivisionModule['wasmMemory'], 'Use of `wasmMemory` detected.  Use -s IMPORTED_MEMORY to define wasmMemory externally');

assert(INITIAL_MEMORY == 16777216, 'Detected runtime INITIAL_MEMORY setting.  Use -s IMPORTED_MEMORY to define wasmMemory dynamically');



// include: runtime_init_table.js

// In regular non-RELOCATABLE mode the table is exported

// from the wasm module and this will be assigned once

// the exports are available.

var wasmTable;



// end include: runtime_init_table.js

// include: runtime_stack_check.js





// Initializes the stack cookie. Called at the startup of main and at the startup of each thread in pthreads mode.

function writeStackCookie() {

  var max = _emscripten_stack_get_end();

  assert((max & 3) == 0);

  // The stack grows downwards

  HEAP32[((max + 4)>>2)] = 0x2135467;

  HEAP32[((max + 8)>>2)] = 0x89BACDFE;

  // Also test the global address 0 for integrity.

  HEAP32[0] = 0x63736d65; /* 'emsc' */

}



function checkStackCookie() {

  if (ABORT) return;

  var max = _emscripten_stack_get_end();

  var cookie1 = HEAPU32[((max + 4)>>2)];

  var cookie2 = HEAPU32[((max + 8)>>2)];

  if (cookie1 != 0x2135467 || cookie2 != 0x89BACDFE) {

    abort('Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x' + cookie2.toString(16) + ' 0x' + cookie1.toString(16));

  }

  // Also test the global address 0 for integrity.

  if (HEAP32[0] !== 0x63736d65 /* 'emsc' */) abort('Runtime error: The application has corrupted its heap memory area (address zero)!');

}



// end include: runtime_stack_check.js

// include: runtime_assertions.js





// Endianness check

(function() {

  var h16 = new Int16Array(1);

  var h8 = new Int8Array(h16.buffer);

  h16[0] = 0x6373;

  if (h8[0] !== 0x73 || h8[1] !== 0x63) throw 'Runtime error: expected the system to be little-endian! (Run with -s SUPPORT_BIG_ENDIAN=1 to bypass)';

})();



// end include: runtime_assertions.js

var __ATPRERUN__  = []; // functions called before the runtime is initialized

var __ATINIT__    = []; // functions called during startup

var __ATEXIT__    = []; // functions called during shutdown

var __ATPOSTRUN__ = []; // functions called after the main() is called



var runtimeInitialized = false;

var runtimeExited = false;

var runtimeKeepaliveCounter = 0;



function keepRuntimeAlive() {

  return noExitRuntime || runtimeKeepaliveCounter > 0;

}



function preRun() {



  if (RoomsDivisionModule['preRun']) {

    if (typeof RoomsDivisionModule['preRun'] == 'function') RoomsDivisionModule['preRun'] = [RoomsDivisionModule['preRun']];

    while (RoomsDivisionModule['preRun'].length) {

      addOnPreRun(RoomsDivisionModule['preRun'].shift());

    }

  }



  callRuntimeCallbacks(__ATPRERUN__);

}



function initRuntime() {

  checkStackCookie();

  assert(!runtimeInitialized);

  runtimeInitialized = true;



  

if (!RoomsDivisionModule["noFSInit"] && !FS.init.initialized)

  FS.init();

FS.ignorePermissions = false;



TTY.init();

  callRuntimeCallbacks(__ATINIT__);

}



function exitRuntime() {

  checkStackCookie();

  runtimeExited = true;

}



function postRun() {

  checkStackCookie();



  if (RoomsDivisionModule['postRun']) {

    if (typeof RoomsDivisionModule['postRun'] == 'function') RoomsDivisionModule['postRun'] = [RoomsDivisionModule['postRun']];

    while (RoomsDivisionModule['postRun'].length) {

      addOnPostRun(RoomsDivisionModule['postRun'].shift());

    }

  }



  callRuntimeCallbacks(__ATPOSTRUN__);

}



function addOnPreRun(cb) {

  __ATPRERUN__.unshift(cb);

}



function addOnInit(cb) {

  __ATINIT__.unshift(cb);

}



function addOnExit(cb) {

}



function addOnPostRun(cb) {

  __ATPOSTRUN__.unshift(cb);

}



// include: runtime_math.js





// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/imul



// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/fround



// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/clz32



// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc



assert(Math.imul, 'This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill');

assert(Math.fround, 'This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill');

assert(Math.clz32, 'This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill');

assert(Math.trunc, 'This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill');



// end include: runtime_math.js

// A counter of dependencies for calling run(). If we need to

// do asynchronous work before running, increment this and

// decrement it. Incrementing must happen in a place like

// RoomsDivisionModule.preRun (used by emcc to add file preloading).

// Note that you can add dependencies in preRun, even though

// it happens right before run - run will be postponed until

// the dependencies are met.

var runDependencies = 0;

var runDependencyWatcher = null;

var dependenciesFulfilled = null; // overridden to take different actions when all run dependencies are fulfilled

var runDependencyTracking = {};



function getUniqueRunDependency(id) {

  var orig = id;

  while (1) {

    if (!runDependencyTracking[id]) return id;

    id = orig + Math.random();

  }

}



function addRunDependency(id) {

  runDependencies++;



  if (RoomsDivisionModule['monitorRunDependencies']) {

    RoomsDivisionModule['monitorRunDependencies'](runDependencies);

  }



  if (id) {

    assert(!runDependencyTracking[id]);

    runDependencyTracking[id] = 1;

    if (runDependencyWatcher === null && typeof setInterval !== 'undefined') {

      // Check for missing dependencies every few seconds

      runDependencyWatcher = setInterval(function() {

        if (ABORT) {

          clearInterval(runDependencyWatcher);

          runDependencyWatcher = null;

          return;

        }

        var shown = false;

        for (var dep in runDependencyTracking) {

          if (!shown) {

            shown = true;

            err('still waiting on run dependencies:');

          }

          err('dependency: ' + dep);

        }

        if (shown) {

          err('(end of list)');

        }

      }, 10000);

    }

  } else {

    err('warning: run dependency added without ID');

  }

}



function removeRunDependency(id) {

  runDependencies--;



  if (RoomsDivisionModule['monitorRunDependencies']) {

    RoomsDivisionModule['monitorRunDependencies'](runDependencies);

  }



  if (id) {

    assert(runDependencyTracking[id]);

    delete runDependencyTracking[id];

  } else {

    err('warning: run dependency removed without ID');

  }

  if (runDependencies == 0) {

    if (runDependencyWatcher !== null) {

      clearInterval(runDependencyWatcher);

      runDependencyWatcher = null;

    }

    if (dependenciesFulfilled) {

      var callback = dependenciesFulfilled;

      dependenciesFulfilled = null;

      callback(); // can add another dependenciesFulfilled

    }

  }

}



RoomsDivisionModule["preloadedImages"] = {}; // maps url to image data

RoomsDivisionModule["preloadedAudios"] = {}; // maps url to audio data



/** @param {string|number=} what */

function abort(what) {

  {

    if (RoomsDivisionModule['onAbort']) {

      RoomsDivisionModule['onAbort'](what);

    }

  }



  what = 'Aborted(' + what + ')';

  // TODO(sbc): Should we remove printing and leave it up to whoever

  // catches the exception?

  err(what);



  ABORT = true;

  EXITSTATUS = 1;



  // Use a wasm runtime error, because a JS error might be seen as a foreign

  // exception, which means we'd run destructors on it. We need the error to

  // simply make the program stop.

  var e = new WebAssembly.RuntimeError(what);



  // Throw the error whether or not MODULARIZE is set because abort is used

  // in code paths apart from instantiation where an exception is expected

  // to be thrown when abort is called.

  throw e;

}



// {{MEM_INITIALIZER}}



// include: memoryprofiler.js





// end include: memoryprofiler.js

// include: URIUtils.js





// Prefix of data URIs emitted by SINGLE_FILE and related options.

var dataURIPrefix = 'data:application/octet-stream;base64,';



// Indicates whether filename is a base64 data URI.

function isDataURI(filename) {

  // Prefix of data URIs emitted by SINGLE_FILE and related options.

  return filename.startsWith(dataURIPrefix);

}



// Indicates whether filename is delivered via file protocol (as opposed to http/https)

function isFileURI(filename) {

  return filename.startsWith('file://');

}



// end include: URIUtils.js

function createExportWrapper(name, fixedasm) {

  return function() {

    var displayName = name;

    var asm = fixedasm;

    if (!fixedasm) {

      asm = RoomsDivisionModule['asm'];

    }

    assert(runtimeInitialized, 'native function `' + displayName + '` called before runtime initialization');

    assert(!runtimeExited, 'native function `' + displayName + '` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)');

    if (!asm[name]) {

      assert(asm[name], 'exported native function `' + displayName + '` not found');

    }

    return asm[name].apply(null, arguments);

  };

}



var wasmBinaryFile;

  wasmBinaryFile = 'RoomsDivision.wasm';

  if (!isDataURI(wasmBinaryFile)) {

    wasmBinaryFile = locateFile(wasmBinaryFile);

  }



function getBinary(file) {

  try {

    if (file == wasmBinaryFile && wasmBinary) {

      return new Uint8Array(wasmBinary);

    }

    if (readBinary) {

      return readBinary(file);

    } else {

      throw "both async and sync fetching of the wasm failed";

    }

  }

  catch (err) {

    abort(err);

  }

}



function getBinaryPromise() {

  // If we don't have the binary yet, try to to load it asynchronously.

  // Fetch has some additional restrictions over XHR, like it can't be used on a file:// url.

  // See https://github.com/github/fetch/pull/92#issuecomment-140665932

  // Cordova or Electron apps are typically loaded from a file:// url.

  // So use fetch if it is available and the url is not a file, otherwise fall back to XHR.

  if (!wasmBinary && (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER)) {

    if (typeof fetch === 'function'

      && !isFileURI(wasmBinaryFile)

    ) {

      return fetch(wasmBinaryFile, { credentials: 'same-origin' }).then(function(response) {

        if (!response['ok']) {

          throw "failed to load wasm binary file at '" + wasmBinaryFile + "'";

        }

        return response['arrayBuffer']();

      }).catch(function () {

          return getBinary(wasmBinaryFile);

      });

    }

    else {

      if (readAsync) {

        // fetch is not available or url is file => try XHR (readAsync uses XHR internally)

        return new Promise(function(resolve, reject) {

          readAsync(wasmBinaryFile, function(response) { resolve(new Uint8Array(/** @type{!ArrayBuffer} */(response))) }, reject)

        });

      }

    }

  }



  // Otherwise, getBinary should be able to get it synchronously

  return Promise.resolve().then(function() { return getBinary(wasmBinaryFile); });

}



// Create the wasm instance.

// Receives the wasm imports, returns the exports.

function createWasm() {

  // prepare imports

  var info = {

    'env': asmLibraryArg,

    'wasi_snapshot_preview1': asmLibraryArg,

  };

  // Load the wasm module and create an instance of using native support in the JS engine.

  // handle a generated wasm instance, receiving its exports and

  // performing other necessary setup

  /** @param {WebAssembly.RoomsDivisionModule=} module*/

  function receiveInstance(instance, module) {

    var exports = instance.exports;



    RoomsDivisionModule['asm'] = exports;



    wasmMemory = RoomsDivisionModule['asm']['memory'];

    assert(wasmMemory, "memory not found in wasm exports");

    // This assertion doesn't hold when emscripten is run in --post-link

    // mode.

    // TODO(sbc): Read INITIAL_MEMORY out of the wasm file in post-link mode.

    //assert(wasmMemory.buffer.byteLength === 16777216);

    updateGlobalBufferAndViews(wasmMemory.buffer);



    wasmTable = RoomsDivisionModule['asm']['__indirect_function_table'];

    assert(wasmTable, "table not found in wasm exports");



    addOnInit(RoomsDivisionModule['asm']['__wasm_call_ctors']);



    removeRunDependency('wasm-instantiate');

  }

  // we can't run yet (except in a pthread, where we have a custom sync instantiator)

  addRunDependency('wasm-instantiate');



  // Prefer streaming instantiation if available.

  // Async compilation can be confusing when an error on the page overwrites RoomsDivisionModule

  // (for example, if the order of elements is wrong, and the one defining RoomsDivisionModule is

  // later), so we save RoomsDivisionModule and check it later.

  var trueRoomsDivisionModule = RoomsDivisionModule;

  function receiveInstantiationResult(result) {

    // 'result' is a ResultObject object which has both the module and instance.

    // receiveInstance() will swap in the exports (to RoomsDivisionModule.asm) so they can be called

    assert(RoomsDivisionModule === trueRoomsDivisionModule, 'the RoomsDivisionModule object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?');

    trueRoomsDivisionModule = null;

    // TODO: Due to Closure regression https://github.com/google/closure-compiler/issues/3193, the above line no longer optimizes out down to the following line.

    // When the regression is fixed, can restore the above USE_PTHREADS-enabled path.

    receiveInstance(result['instance']);

  }



  function instantiateArrayBuffer(receiver) {

    return getBinaryPromise().then(function(binary) {

      return WebAssembly.instantiate(binary, info);

    }).then(function (instance) {

      return instance;

    }).then(receiver, function(reason) {

      err('failed to asynchronously prepare wasm: ' + reason);



      // Warn on some common problems.

      if (isFileURI(wasmBinaryFile)) {

        err('warning: Loading from a file URI (' + wasmBinaryFile + ') is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing');

      }

      abort(reason);

    });

  }



  function instantiateAsync() {

    if (!wasmBinary &&

        typeof WebAssembly.instantiateStreaming === 'function' &&

        !isDataURI(wasmBinaryFile) &&

        // Don't use streaming for file:// delivered objects in a webview, fetch them synchronously.

        !isFileURI(wasmBinaryFile) &&

        typeof fetch === 'function') {

      return fetch(wasmBinaryFile, { credentials: 'same-origin' }).then(function (response) {

        var result = WebAssembly.instantiateStreaming(response, info);



        return result.then(

          receiveInstantiationResult,

          function(reason) {

            // We expect the most common failure cause to be a bad MIME type for the binary,

            // in which case falling back to ArrayBuffer instantiation should work.

            err('wasm streaming compile failed: ' + reason);

            err('falling back to ArrayBuffer instantiation');

            return instantiateArrayBuffer(receiveInstantiationResult);

          });

      });

    } else {

      return instantiateArrayBuffer(receiveInstantiationResult);

    }

  }



  // User shell pages can write their own RoomsDivisionModule.instantiateWasm = function(imports, successCallback) callback

  // to manually instantiate the Wasm module themselves. This allows pages to run the instantiation parallel

  // to any other async startup actions they are performing.

  if (RoomsDivisionModule['instantiateWasm']) {

    try {

      var exports = RoomsDivisionModule['instantiateWasm'](info, receiveInstance);

      return exports;

    } catch(e) {

      err('RoomsDivisionModule.instantiateWasm callback failed with error: ' + e);

      return false;

    }

  }



  instantiateAsync();

  return {}; // no exports yet; we'll fill them in later

}



// Globals used by JS i64 conversions (see makeSetValue)

var tempDouble;

var tempI64;



// === Body ===



var ASM_CONSTS = {

  

};













  function callRuntimeCallbacks(callbacks) {

      while (callbacks.length > 0) {

        var callback = callbacks.shift();

        if (typeof callback == 'function') {

          callback(RoomsDivisionModule); // Pass the module as the first argument.

          continue;

        }

        var func = callback.func;

        if (typeof func === 'number') {

          if (callback.arg === undefined) {

            getWasmTableEntry(func)();

          } else {

            getWasmTableEntry(func)(callback.arg);

          }

        } else {

          func(callback.arg === undefined ? null : callback.arg);

        }

      }

    }



  function withStackSave(f) {

      var stack = stackSave();

      var ret = f();

      stackRestore(stack);

      return ret;

    }

  function demangle(func) {

      warnOnce('warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling');

      return func;

    }



  function demangleAll(text) {

      var regex =

        /\b_Z[\w\d_]+/g;

      return text.replace(regex,

        function(x) {

          var y = demangle(x);

          return x === y ? x : (y + ' [' + x + ']');

        });

    }



  var wasmTableMirror = [];

  function getWasmTableEntry(funcPtr) {

      var func = wasmTableMirror[funcPtr];

      if (!func) {

        if (funcPtr >= wasmTableMirror.length) wasmTableMirror.length = funcPtr + 1;

        wasmTableMirror[funcPtr] = func = wasmTable.get(funcPtr);

      }

      assert(wasmTable.get(funcPtr) == func, "JavaScript-side Wasm function table mirror is out of date!");

      return func;

    }



  function handleException(e) {

      // Certain exception types we do not treat as errors since they are used for

      // internal control flow.

      // 1. ExitStatus, which is thrown by exit()

      // 2. "unwind", which is thrown by emscripten_unwind_to_js_event_loop() and others

      //    that wish to return to JS event loop.

      if (e instanceof ExitStatus || e == 'unwind') {

        return EXITSTATUS;

      }

      quit_(1, e);

    }



  function jsStackTrace() {

      var error = new Error();

      if (!error.stack) {

        // IE10+ special cases: It does have callstack info, but it is only populated if an Error object is thrown,

        // so try that as a special-case.

        try {

          throw new Error();

        } catch(e) {

          error = e;

        }

        if (!error.stack) {

          return '(no stack trace available)';

        }

      }

      return error.stack.toString();

    }



  function setWasmTableEntry(idx, func) {

      wasmTable.set(idx, func);

      wasmTableMirror[idx] = func;

    }



  function stackTrace() {

      var js = jsStackTrace();

      if (RoomsDivisionModule['extraStackTrace']) js += '\n' + RoomsDivisionModule['extraStackTrace']();

      return demangleAll(js);

    }



  function ___cxa_allocate_exception(size) {

      // Thrown object is prepended by exception metadata block

      return _malloc(size + 16) + 16;

    }



  function _atexit(func, arg) {

    }

  function ___cxa_atexit(a0,a1

  ) {

  return _atexit(a0,a1);

  }



  function ExceptionInfo(excPtr) {

      this.excPtr = excPtr;

      this.ptr = excPtr - 16;

  

      this.set_type = function(type) {

        HEAP32[(((this.ptr)+(4))>>2)] = type;

      };

  

      this.get_type = function() {

        return HEAP32[(((this.ptr)+(4))>>2)];

      };

  

      this.set_destructor = function(destructor) {

        HEAP32[(((this.ptr)+(8))>>2)] = destructor;

      };

  

      this.get_destructor = function() {

        return HEAP32[(((this.ptr)+(8))>>2)];

      };

  

      this.set_refcount = function(refcount) {

        HEAP32[((this.ptr)>>2)] = refcount;

      };

  

      this.set_caught = function (caught) {

        caught = caught ? 1 : 0;

        HEAP8[(((this.ptr)+(12))>>0)] = caught;

      };

  

      this.get_caught = function () {

        return HEAP8[(((this.ptr)+(12))>>0)] != 0;

      };

  

      this.set_rethrown = function (rethrown) {

        rethrown = rethrown ? 1 : 0;

        HEAP8[(((this.ptr)+(13))>>0)] = rethrown;

      };

  

      this.get_rethrown = function () {

        return HEAP8[(((this.ptr)+(13))>>0)] != 0;

      };

  

      // Initialize native structure fields. Should be called once after allocated.

      this.init = function(type, destructor) {

        this.set_type(type);

        this.set_destructor(destructor);

        this.set_refcount(0);

        this.set_caught(false);

        this.set_rethrown(false);

      }

  

      this.add_ref = function() {

        var value = HEAP32[((this.ptr)>>2)];

        HEAP32[((this.ptr)>>2)] = value + 1;

      };

  

      // Returns true if last reference released.

      this.release_ref = function() {

        var prev = HEAP32[((this.ptr)>>2)];

        HEAP32[((this.ptr)>>2)] = prev - 1;

        assert(prev > 0);

        return prev === 1;

      };

    }

  

  var exceptionLast = 0;

  

  var uncaughtExceptionCount = 0;

  function ___cxa_throw(ptr, type, destructor) {

      var info = new ExceptionInfo(ptr);

      // Initialize ExceptionInfo content after it was allocated in __cxa_allocate_exception.

      info.init(type, destructor);

      exceptionLast = ptr;

      uncaughtExceptionCount++;

      throw ptr + " - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch.";

    }



  function __embind_register_bigint(primitiveType, name, size, minRange, maxRange) {}



  function getShiftFromSize(size) {

      

      switch (size) {

          case 1: return 0;

          case 2: return 1;

          case 4: return 2;

          case 8: return 3;

          default:

              throw new TypeError('Unknown type size: ' + size);

      }

    }

  

  function embind_init_charCodes() {

      var codes = new Array(256);

      for (var i = 0; i < 256; ++i) {

          codes[i] = String.fromCharCode(i);

      }

      embind_charCodes = codes;

    }

  var embind_charCodes = undefined;

  function readLatin1String(ptr) {

      var ret = "";

      var c = ptr;

      while (HEAPU8[c]) {

          ret += embind_charCodes[HEAPU8[c++]];

      }

      return ret;

    }

  

  var awaitingDependencies = {};

  

  var registeredTypes = {};

  

  var typeDependencies = {};

  

  var char_0 = 48;

  

  var char_9 = 57;

  function makeLegalFunctionName(name) {

      if (undefined === name) {

          return '_unknown';

      }

      name = name.replace(/[^a-zA-Z0-9_]/g, '$');

      var f = name.charCodeAt(0);

      if (f >= char_0 && f <= char_9) {

          return '_' + name;

      } else {

          return name;

      }

    }

  function createNamedFunction(name, body) {

      name = makeLegalFunctionName(name);

      /*jshint evil:true*/

      return new Function(

          "body",

          "return function " + name + "() {\n" +

          "    \"use strict\";" +

          "    return body.apply(this, arguments);\n" +

          "};\n"

      )(body);

    }

  function extendError(baseErrorType, errorName) {

      var errorClass = createNamedFunction(errorName, function(message) {

          this.name = errorName;

          this.message = message;

  

          var stack = (new Error(message)).stack;

          if (stack !== undefined) {

              this.stack = this.toString() + '\n' +

                  stack.replace(/^Error(:[^\n]*)?\n/, '');

          }

      });

      errorClass.prototype = Object.create(baseErrorType.prototype);

      errorClass.prototype.constructor = errorClass;

      errorClass.prototype.toString = function() {

          if (this.message === undefined) {

              return this.name;

          } else {

              return this.name + ': ' + this.message;

          }

      };

  

      return errorClass;

    }

  var BindingError = undefined;

  function throwBindingError(message) {

      throw new BindingError(message);

    }

  

  var InternalError = undefined;

  function throwInternalError(message) {

      throw new InternalError(message);

    }

  function whenDependentTypesAreResolved(myTypes, dependentTypes, getTypeConverters) {

      myTypes.forEach(function(type) {

          typeDependencies[type] = dependentTypes;

      });

  

      function onComplete(typeConverters) {

          var myTypeConverters = getTypeConverters(typeConverters);

          if (myTypeConverters.length !== myTypes.length) {

              throwInternalError('Mismatched type converter count');

          }

          for (var i = 0; i < myTypes.length; ++i) {

              registerType(myTypes[i], myTypeConverters[i]);

          }

      }

  

      var typeConverters = new Array(dependentTypes.length);

      var unregisteredTypes = [];

      var registered = 0;

      dependentTypes.forEach(function(dt, i) {

          if (registeredTypes.hasOwnProperty(dt)) {

              typeConverters[i] = registeredTypes[dt];

          } else {

              unregisteredTypes.push(dt);

              if (!awaitingDependencies.hasOwnProperty(dt)) {

                  awaitingDependencies[dt] = [];

              }

              awaitingDependencies[dt].push(function() {

                  typeConverters[i] = registeredTypes[dt];

                  ++registered;

                  if (registered === unregisteredTypes.length) {

                      onComplete(typeConverters);

                  }

              });

          }

      });

      if (0 === unregisteredTypes.length) {

          onComplete(typeConverters);

      }

    }

  /** @param {Object=} options */

  function registerType(rawType, registeredInstance, options) {

      options = options || {};

  

      if (!('argPackAdvance' in registeredInstance)) {

          throw new TypeError('registerType registeredInstance requires argPackAdvance');

      }

  

      var name = registeredInstance.name;

      if (!rawType) {

          throwBindingError('type "' + name + '" must have a positive integer typeid pointer');

      }

      if (registeredTypes.hasOwnProperty(rawType)) {

          if (options.ignoreDuplicateRegistrations) {

              return;

          } else {

              throwBindingError("Cannot register type '" + name + "' twice");

          }

      }

  

      registeredTypes[rawType] = registeredInstance;

      delete typeDependencies[rawType];

  

      if (awaitingDependencies.hasOwnProperty(rawType)) {

          var callbacks = awaitingDependencies[rawType];

          delete awaitingDependencies[rawType];

          callbacks.forEach(function(cb) {

              cb();

          });

      }

    }

  function __embind_register_bool(rawType, name, size, trueValue, falseValue) {

      var shift = getShiftFromSize(size);

  

      name = readLatin1String(name);

      registerType(rawType, {

          name: name,

          'fromWireType': function(wt) {

              // ambiguous emscripten ABI: sometimes return values are

              // true or false, and sometimes integers (0 or 1)

              return !!wt;

          },

          'toWireType': function(destructors, o) {

              return o ? trueValue : falseValue;

          },

          'argPackAdvance': 8,

          'readValueFromPointer': function(pointer) {

              // TODO: if heap is fixed (like in asm.js) this could be executed outside

              var heap;

              if (size === 1) {

                  heap = HEAP8;

              } else if (size === 2) {

                  heap = HEAP16;

              } else if (size === 4) {

                  heap = HEAP32;

              } else {

                  throw new TypeError("Unknown boolean type size: " + name);

              }

              return this['fromWireType'](heap[pointer >> shift]);

          },

          destructorFunction: null, // This type does not need a destructor

      });

    }



  var emval_free_list = [];

  

  var emval_handle_array = [{},{value:undefined},{value:null},{value:true},{value:false}];

  function __emval_decref(handle) {

      if (handle > 4 && 0 === --emval_handle_array[handle].refcount) {

          emval_handle_array[handle] = undefined;

          emval_free_list.push(handle);

      }

    }

  

  function count_emval_handles() {

      var count = 0;

      for (var i = 5; i < emval_handle_array.length; ++i) {

          if (emval_handle_array[i] !== undefined) {

              ++count;

          }

      }

      return count;

    }

  

  function get_first_emval() {

      for (var i = 5; i < emval_handle_array.length; ++i) {

          if (emval_handle_array[i] !== undefined) {

              return emval_handle_array[i];

          }

      }

      return null;

    }

  function init_emval() {

      RoomsDivisionModule['count_emval_handles'] = count_emval_handles;

      RoomsDivisionModule['get_first_emval'] = get_first_emval;

    }

  var Emval = {toValue:function(handle) {

        if (!handle) {

            throwBindingError('Cannot use deleted val. handle = ' + handle);

        }

        return emval_handle_array[handle].value;

      },toHandle:function(value) {

        switch (value) {

          case undefined :{ return 1; }

          case null :{ return 2; }

          case true :{ return 3; }

          case false :{ return 4; }

          default:{

            var handle = emval_free_list.length ?

                emval_free_list.pop() :

                emval_handle_array.length;

    

            emval_handle_array[handle] = {refcount: 1, value: value};

            return handle;

            }

          }

      }};

  

  function simpleReadValueFromPointer(pointer) {

      return this['fromWireType'](HEAPU32[pointer >> 2]);

    }

  function __embind_register_emval(rawType, name) {

      name = readLatin1String(name);

      registerType(rawType, {

          name: name,

          'fromWireType': function(handle) {

              var rv = Emval.toValue(handle);

              __emval_decref(handle);

              return rv;

          },

          'toWireType': function(destructors, value) {

              return Emval.toHandle(value);

          },

          'argPackAdvance': 8,

          'readValueFromPointer': simpleReadValueFromPointer,

          destructorFunction: null, // This type does not need a destructor

  

          // TODO: do we need a deleteObject here?  write a test where

          // emval is passed into JS via an interface

      });

    }



  function _embind_repr(v) {

      if (v === null) {

          return 'null';

      }

      var t = typeof v;

      if (t === 'object' || t === 'array' || t === 'function') {

          return v.toString();

      } else {

          return '' + v;

      }

    }

  

  function floatReadValueFromPointer(name, shift) {

      switch (shift) {

          case 2: return function(pointer) {

              return this['fromWireType'](HEAPF32[pointer >> 2]);

          };

          case 3: return function(pointer) {

              return this['fromWireType'](HEAPF64[pointer >> 3]);

          };

          default:

              throw new TypeError("Unknown float type: " + name);

      }

    }

  function __embind_register_float(rawType, name, size) {

      var shift = getShiftFromSize(size);

      name = readLatin1String(name);

      registerType(rawType, {

          name: name,

          'fromWireType': function(value) {

              return value;

          },

          'toWireType': function(destructors, value) {

              if (typeof value !== "number" && typeof value !== "boolean") {

                  throw new TypeError('Cannot convert "' + _embind_repr(value) + '" to ' + this.name);

              }

              // The VM will perform JS to Wasm value conversion, according to the spec:

              // https://www.w3.org/TR/wasm-js-api-1/#towebassemblyvalue

              return value;

          },

          'argPackAdvance': 8,

          'readValueFromPointer': floatReadValueFromPointer(name, shift),

          destructorFunction: null, // This type does not need a destructor

      });

    }



  function new_(constructor, argumentList) {

      if (!(constructor instanceof Function)) {

          throw new TypeError('new_ called with constructor type ' + typeof(constructor) + " which is not a function");

      }

  

      /*

       * Previously, the following line was just:

  

       function dummy() {};

  

       * Unfortunately, Chrome was preserving 'dummy' as the object's name, even though at creation, the 'dummy' has the

       * correct constructor name.  Thus, objects created with IMVU.new would show up in the debugger as 'dummy', which

       * isn't very helpful.  Using IMVU.createNamedFunction addresses the issue.  Doublely-unfortunately, there's no way

       * to write a test for this behavior.  -NRD 2013.02.22

       */

      var dummy = createNamedFunction(constructor.name || 'unknownFunctionName', function(){});

      dummy.prototype = constructor.prototype;

      var obj = new dummy;

  

      var r = constructor.apply(obj, argumentList);

      return (r instanceof Object) ? r : obj;

    }

  

  function runDestructors(destructors) {

      while (destructors.length) {

          var ptr = destructors.pop();

          var del = destructors.pop();

          del(ptr);

      }

    }

  function craftInvokerFunction(humanName, argTypes, classType, cppInvokerFunc, cppTargetFunc) {

      // humanName: a human-readable string name for the function to be generated.

      // argTypes: An array that contains the embind type objects for all types in the function signature.

      //    argTypes[0] is the type object for the function return value.

      //    argTypes[1] is the type object for function this object/class type, or null if not crafting an invoker for a class method.

      //    argTypes[2...] are the actual function parameters.

      // classType: The embind type object for the class to be bound, or null if this is not a method of a class.

      // cppInvokerFunc: JS Function object to the C++-side function that interops into C++ code.

      // cppTargetFunc: Function pointer (an integer to FUNCTION_TABLE) to the target C++ function the cppInvokerFunc will end up calling.

      var argCount = argTypes.length;

  

      if (argCount < 2) {

          throwBindingError("argTypes array size mismatch! Must at least get return value and 'this' types!");

      }

  

      var isClassMethodFunc = (argTypes[1] !== null && classType !== null);

  

      // Free functions with signature "void function()" do not need an invoker that marshalls between wire types.

  // TODO: This omits argument count check - enable only at -O3 or similar.

  //    if (ENABLE_UNSAFE_OPTS && argCount == 2 && argTypes[0].name == "void" && !isClassMethodFunc) {

  //       return FUNCTION_TABLE[fn];

  //    }

  

      // Determine if we need to use a dynamic stack to store the destructors for the function parameters.

      // TODO: Remove this completely once all function invokers are being dynamically generated.

      var needsDestructorStack = false;

  

      for (var i = 1; i < argTypes.length; ++i) { // Skip return value at index 0 - it's not deleted here.

          if (argTypes[i] !== null && argTypes[i].destructorFunction === undefined) { // The type does not define a destructor function - must use dynamic stack

              needsDestructorStack = true;

              break;

          }

      }

  

      var returns = (argTypes[0].name !== "void");

  

      var argsList = "";

      var argsListWired = "";

      for (var i = 0; i < argCount - 2; ++i) {

          argsList += (i!==0?", ":"")+"arg"+i;

          argsListWired += (i!==0?", ":"")+"arg"+i+"Wired";

      }

  

      var invokerFnBody =

          "return function "+makeLegalFunctionName(humanName)+"("+argsList+") {\n" +

          "if (arguments.length !== "+(argCount - 2)+") {\n" +

              "throwBindingError('function "+humanName+" called with ' + arguments.length + ' arguments, expected "+(argCount - 2)+" args!');\n" +

          "}\n";

  

      if (needsDestructorStack) {

          invokerFnBody +=

              "var destructors = [];\n";

      }

  

      var dtorStack = needsDestructorStack ? "destructors" : "null";

      var args1 = ["throwBindingError", "invoker", "fn", "runDestructors", "retType", "classParam"];

      var args2 = [throwBindingError, cppInvokerFunc, cppTargetFunc, runDestructors, argTypes[0], argTypes[1]];

  

      if (isClassMethodFunc) {

          invokerFnBody += "var thisWired = classParam.toWireType("+dtorStack+", this);\n";

      }

  

      for (var i = 0; i < argCount - 2; ++i) {

          invokerFnBody += "var arg"+i+"Wired = argType"+i+".toWireType("+dtorStack+", arg"+i+"); // "+argTypes[i+2].name+"\n";

          args1.push("argType"+i);

          args2.push(argTypes[i+2]);

      }

  

      if (isClassMethodFunc) {

          argsListWired = "thisWired" + (argsListWired.length > 0 ? ", " : "") + argsListWired;

      }

  

      invokerFnBody +=

          (returns?"var rv = ":"") + "invoker(fn"+(argsListWired.length>0?", ":"")+argsListWired+");\n";

  

      if (needsDestructorStack) {

          invokerFnBody += "runDestructors(destructors);\n";

      } else {

          for (var i = isClassMethodFunc?1:2; i < argTypes.length; ++i) { // Skip return value at index 0 - it's not deleted here. Also skip class type if not a method.

              var paramName = (i === 1 ? "thisWired" : ("arg"+(i - 2)+"Wired"));

              if (argTypes[i].destructorFunction !== null) {

                  invokerFnBody += paramName+"_dtor("+paramName+"); // "+argTypes[i].name+"\n";

                  args1.push(paramName+"_dtor");

                  args2.push(argTypes[i].destructorFunction);

              }

          }

      }

  

      if (returns) {

          invokerFnBody += "var ret = retType.fromWireType(rv);\n" +

                           "return ret;\n";

      } else {

      }

  

      invokerFnBody += "}\n";

  

      args1.push(invokerFnBody);

  

      var invokerFunction = new_(Function, args1).apply(null, args2);

      return invokerFunction;

    }

  

  function ensureOverloadTable(proto, methodName, humanName) {

      if (undefined === proto[methodName].overloadTable) {

          var prevFunc = proto[methodName];

          // Inject an overload resolver function that routes to the appropriate overload based on the number of arguments.

          proto[methodName] = function() {

              // TODO This check can be removed in -O3 level "unsafe" optimizations.

              if (!proto[methodName].overloadTable.hasOwnProperty(arguments.length)) {

                  throwBindingError("Function '" + humanName + "' called with an invalid number of arguments (" + arguments.length + ") - expects one of (" + proto[methodName].overloadTable + ")!");

              }

              return proto[methodName].overloadTable[arguments.length].apply(this, arguments);

          };

          // Move the previous function into the overload table.

          proto[methodName].overloadTable = [];

          proto[methodName].overloadTable[prevFunc.argCount] = prevFunc;

      }

    }

  /** @param {number=} numArguments */

  function exposePublicSymbol(name, value, numArguments) {

      if (RoomsDivisionModule.hasOwnProperty(name)) {

          if (undefined === numArguments || (undefined !== RoomsDivisionModule[name].overloadTable && undefined !== RoomsDivisionModule[name].overloadTable[numArguments])) {

              throwBindingError("Cannot register public name '" + name + "' twice");

          }

  

          // We are exposing a function with the same name as an existing function. Create an overload table and a function selector

          // that routes between the two.

          ensureOverloadTable(RoomsDivisionModule, name, name);

          if (RoomsDivisionModule.hasOwnProperty(numArguments)) {

              throwBindingError("Cannot register multiple overloads of a function with the same number of arguments (" + numArguments + ")!");

          }

          // Add the new function into the overload table.

          RoomsDivisionModule[name].overloadTable[numArguments] = value;

      }

      else {

          RoomsDivisionModule[name] = value;

          if (undefined !== numArguments) {

              RoomsDivisionModule[name].numArguments = numArguments;

          }

      }

    }

  

  function heap32VectorToArray(count, firstElement) {

      

      var array = [];

      for (var i = 0; i < count; i++) {

          array.push(HEAP32[(firstElement >> 2) + i]);

      }

      return array;

    }

  

  /** @param {number=} numArguments */

  function replacePublicSymbol(name, value, numArguments) {

      if (!RoomsDivisionModule.hasOwnProperty(name)) {

          throwInternalError('Replacing nonexistant public symbol');

      }

      // If there's an overload table for this symbol, replace the symbol in the overload table instead.

      if (undefined !== RoomsDivisionModule[name].overloadTable && undefined !== numArguments) {

          RoomsDivisionModule[name].overloadTable[numArguments] = value;

      }

      else {

          RoomsDivisionModule[name] = value;

          RoomsDivisionModule[name].argCount = numArguments;

      }

    }

  

  function dynCallLegacy(sig, ptr, args) {

      assert(('dynCall_' + sig) in RoomsDivisionModule, 'bad function pointer type - no table for sig \'' + sig + '\'');

      if (args && args.length) {

        // j (64-bit integer) must be passed in as two numbers [low 32, high 32].

        assert(args.length === sig.substring(1).replace(/j/g, '--').length);

      } else {

        assert(sig.length == 1);

      }

      var f = RoomsDivisionModule["dynCall_" + sig];

      return args && args.length ? f.apply(null, [ptr].concat(args)) : f.call(null, ptr);

    }

  function dynCall(sig, ptr, args) {

      // Without WASM_BIGINT support we cannot directly call function with i64 as

      // part of thier signature, so we rely the dynCall functions generated by

      // wasm-emscripten-finalize

      if (sig.includes('j')) {

        return dynCallLegacy(sig, ptr, args);

      }

      assert(getWasmTableEntry(ptr), 'missing table entry in dynCall: ' + ptr);

      return getWasmTableEntry(ptr).apply(null, args)

    }

  function getDynCaller(sig, ptr) {

      assert(sig.includes('j'), 'getDynCaller should only be called with i64 sigs')

      var argCache = [];

      return function() {

        argCache.length = arguments.length;

        for (var i = 0; i < arguments.length; i++) {

          argCache[i] = arguments[i];

        }

        return dynCall(sig, ptr, argCache);

      };

    }

  function embind__requireFunction(signature, rawFunction) {

      signature = readLatin1String(signature);

  

      function makeDynCaller() {

        if (signature.includes('j')) {

          return getDynCaller(signature, rawFunction);

        }

        return getWasmTableEntry(rawFunction);

      }

  

      var fp = makeDynCaller();

      if (typeof fp !== "function") {

          throwBindingError("unknown function pointer with signature " + signature + ": " + rawFunction);

      }

      return fp;

    }

  

  var UnboundTypeError = undefined;

  

  function getTypeName(type) {

      var ptr = ___getTypeName(type);

      var rv = readLatin1String(ptr);

      _free(ptr);

      return rv;

    }

  function throwUnboundTypeError(message, types) {

      var unboundTypes = [];

      var seen = {};

      function visit(type) {

          if (seen[type]) {

              return;

          }

          if (registeredTypes[type]) {

              return;

          }

          if (typeDependencies[type]) {

              typeDependencies[type].forEach(visit);

              return;

          }

          unboundTypes.push(type);

          seen[type] = true;

      }

      types.forEach(visit);

  

      throw new UnboundTypeError(message + ': ' + unboundTypes.map(getTypeName).join([', ']));

    }

  function __embind_register_function(name, argCount, rawArgTypesAddr, signature, rawInvoker, fn) {

      var argTypes = heap32VectorToArray(argCount, rawArgTypesAddr);

      name = readLatin1String(name);

  

      rawInvoker = embind__requireFunction(signature, rawInvoker);

  

      exposePublicSymbol(name, function() {

          throwUnboundTypeError('Cannot call ' + name + ' due to unbound types', argTypes);

      }, argCount - 1);

  

      whenDependentTypesAreResolved([], argTypes, function(argTypes) {

          var invokerArgsArray = [argTypes[0] /* return value */, null /* no class 'this'*/].concat(argTypes.slice(1) /* actual params */);

          replacePublicSymbol(name, craftInvokerFunction(name, invokerArgsArray, null /* no class 'this'*/, rawInvoker, fn), argCount - 1);

          return [];

      });

    }



  function integerReadValueFromPointer(name, shift, signed) {

      // integers are quite common, so generate very specialized functions

      switch (shift) {

          case 0: return signed ?

              function readS8FromPointer(pointer) { return HEAP8[pointer]; } :

              function readU8FromPointer(pointer) { return HEAPU8[pointer]; };

          case 1: return signed ?

              function readS16FromPointer(pointer) { return HEAP16[pointer >> 1]; } :

              function readU16FromPointer(pointer) { return HEAPU16[pointer >> 1]; };

          case 2: return signed ?

              function readS32FromPointer(pointer) { return HEAP32[pointer >> 2]; } :

              function readU32FromPointer(pointer) { return HEAPU32[pointer >> 2]; };

          default:

              throw new TypeError("Unknown integer type: " + name);

      }

    }

  function __embind_register_integer(primitiveType, name, size, minRange, maxRange) {

      name = readLatin1String(name);

      if (maxRange === -1) { // LLVM doesn't have signed and unsigned 32-bit types, so u32 literals come out as 'i32 -1'. Always treat those as max u32.

          maxRange = 4294967295;

      }

  

      var shift = getShiftFromSize(size);

  

      var fromWireType = function(value) {

          return value;

      };

  

      if (minRange === 0) {

          var bitshift = 32 - 8*size;

          fromWireType = function(value) {

              return (value << bitshift) >>> bitshift;

          };

      }

  

      var isUnsignedType = (name.includes('unsigned'));

  

      registerType(primitiveType, {

          name: name,

          'fromWireType': fromWireType,

          'toWireType': function(destructors, value) {

              // todo: Here we have an opportunity for -O3 level "unsafe" optimizations: we could

              // avoid the following two if()s and assume value is of proper type.

              if (typeof value !== "number" && typeof value !== "boolean") {

                  throw new TypeError('Cannot convert "' + _embind_repr(value) + '" to ' + this.name);

              }

              if (value < minRange || value > maxRange) {

                  throw new TypeError('Passing a number "' + _embind_repr(value) + '" from JS side to C/C++ side to an argument of type "' + name + '", which is outside the valid range [' + minRange + ', ' + maxRange + ']!');

              }

              return isUnsignedType ? (value >>> 0) : (value | 0);

          },

          'argPackAdvance': 8,

          'readValueFromPointer': integerReadValueFromPointer(name, shift, minRange !== 0),

          destructorFunction: null, // This type does not need a destructor

      });

    }



  function __embind_register_memory_view(rawType, dataTypeIndex, name) {

      var typeMapping = [

          Int8Array,

          Uint8Array,

          Int16Array,

          Uint16Array,

          Int32Array,

          Uint32Array,

          Float32Array,

          Float64Array,

      ];

  

      var TA = typeMapping[dataTypeIndex];

  

      function decodeMemoryView(handle) {

          handle = handle >> 2;

          var heap = HEAPU32;

          var size = heap[handle]; // in elements

          var data = heap[handle + 1]; // byte offset into emscripten heap

          return new TA(buffer, data, size);

      }

  

      name = readLatin1String(name);

      registerType(rawType, {

          name: name,

          'fromWireType': decodeMemoryView,

          'argPackAdvance': 8,

          'readValueFromPointer': decodeMemoryView,

      }, {

          ignoreDuplicateRegistrations: true,

      });

    }



  function __embind_register_std_string(rawType, name) {

      name = readLatin1String(name);

      var stdStringIsUTF8

      //process only std::string bindings with UTF8 support, in contrast to e.g. std::basic_string<unsigned char>

      = (name === "std::string");

  

      registerType(rawType, {

          name: name,

          'fromWireType': function(value) {

              var length = HEAPU32[value >> 2];

  

              var str;

              if (stdStringIsUTF8) {

                  var decodeStartPtr = value + 4;

                  // Looping here to support possible embedded '0' bytes

                  for (var i = 0; i <= length; ++i) {

                      var currentBytePtr = value + 4 + i;

                      if (i == length || HEAPU8[currentBytePtr] == 0) {

                          var maxRead = currentBytePtr - decodeStartPtr;

                          var stringSegment = UTF8ToString(decodeStartPtr, maxRead);

                          if (str === undefined) {

                              str = stringSegment;

                          } else {

                              str += String.fromCharCode(0);

                              str += stringSegment;

                          }

                          decodeStartPtr = currentBytePtr + 1;

                      }

                  }

              } else {

                  var a = new Array(length);

                  for (var i = 0; i < length; ++i) {

                      a[i] = String.fromCharCode(HEAPU8[value + 4 + i]);

                  }

                  str = a.join('');

              }

  

              _free(value);

  

              return str;

          },

          'toWireType': function(destructors, value) {

              if (value instanceof ArrayBuffer) {

                  value = new Uint8Array(value);

              }

  

              var getLength;

              var valueIsOfTypeString = (typeof value === 'string');

  

              if (!(valueIsOfTypeString || value instanceof Uint8Array || value instanceof Uint8ClampedArray || value instanceof Int8Array)) {

                  throwBindingError('Cannot pass non-string to std::string');

              }

              if (stdStringIsUTF8 && valueIsOfTypeString) {

                  getLength = function() {return lengthBytesUTF8(value);};

              } else {

                  getLength = function() {return value.length;};

              }

  

              // assumes 4-byte alignment

              var length = getLength();

              var ptr = _malloc(4 + length + 1);

              HEAPU32[ptr >> 2] = length;

              if (stdStringIsUTF8 && valueIsOfTypeString) {

                  stringToUTF8(value, ptr + 4, length + 1);

              } else {

                  if (valueIsOfTypeString) {

                      for (var i = 0; i < length; ++i) {

                          var charCode = value.charCodeAt(i);

                          if (charCode > 255) {

                              _free(ptr);

                              throwBindingError('String has UTF-16 code units that do not fit in 8 bits');

                          }

                          HEAPU8[ptr + 4 + i] = charCode;

                      }

                  } else {

                      for (var i = 0; i < length; ++i) {

                          HEAPU8[ptr + 4 + i] = value[i];

                      }

                  }

              }

  

              if (destructors !== null) {

                  destructors.push(_free, ptr);

              }

              return ptr;

          },

          'argPackAdvance': 8,

          'readValueFromPointer': simpleReadValueFromPointer,

          destructorFunction: function(ptr) { _free(ptr); },

      });

    }



  function __embind_register_std_wstring(rawType, charSize, name) {

      name = readLatin1String(name);

      var decodeString, encodeString, getHeap, lengthBytesUTF, shift;

      if (charSize === 2) {

          decodeString = UTF16ToString;

          encodeString = stringToUTF16;

          lengthBytesUTF = lengthBytesUTF16;

          getHeap = function() { return HEAPU16; };

          shift = 1;

      } else if (charSize === 4) {

          decodeString = UTF32ToString;

          encodeString = stringToUTF32;

          lengthBytesUTF = lengthBytesUTF32;

          getHeap = function() { return HEAPU32; };

          shift = 2;

      }

      registerType(rawType, {

          name: name,

          'fromWireType': function(value) {

              // Code mostly taken from _embind_register_std_string fromWireType

              var length = HEAPU32[value >> 2];

              var HEAP = getHeap();

              var str;

  

              var decodeStartPtr = value + 4;

              // Looping here to support possible embedded '0' bytes

              for (var i = 0; i <= length; ++i) {

                  var currentBytePtr = value + 4 + i * charSize;

                  if (i == length || HEAP[currentBytePtr >> shift] == 0) {

                      var maxReadBytes = currentBytePtr - decodeStartPtr;

                      var stringSegment = decodeString(decodeStartPtr, maxReadBytes);

                      if (str === undefined) {

                          str = stringSegment;

                      } else {

                          str += String.fromCharCode(0);

                          str += stringSegment;

                      }

                      decodeStartPtr = currentBytePtr + charSize;

                  }

              }

  

              _free(value);

  

              return str;

          },

          'toWireType': function(destructors, value) {

              if (!(typeof value === 'string')) {

                  throwBindingError('Cannot pass non-string to C++ string type ' + name);

              }

  

              // assumes 4-byte alignment

              var length = lengthBytesUTF(value);

              var ptr = _malloc(4 + length + charSize);

              HEAPU32[ptr >> 2] = length >> shift;

  

              encodeString(value, ptr + 4, length + charSize);

  

              if (destructors !== null) {

                  destructors.push(_free, ptr);

              }

              return ptr;

          },

          'argPackAdvance': 8,

          'readValueFromPointer': simpleReadValueFromPointer,

          destructorFunction: function(ptr) { _free(ptr); },

      });

    }



  function __embind_register_void(rawType, name) {

      name = readLatin1String(name);

      registerType(rawType, {

          isVoid: true, // void return values can be optimized out sometimes

          name: name,

          'argPackAdvance': 0,

          'fromWireType': function() {

              return undefined;

          },

          'toWireType': function(destructors, o) {

              // TODO: assert if anything else is given?

              return undefined;

          },

      });

    }



  function _abort() {

      abort('native code called abort()');

    }



  function _emscripten_memcpy_big(dest, src, num) {

      HEAPU8.copyWithin(dest, src, src + num);

    }



  function abortOnCannotGrowMemory(requestedSize) {

      abort('Cannot enlarge memory arrays to size ' + requestedSize + ' bytes (OOM). Either (1) compile with  -s INITIAL_MEMORY=X  with X higher than the current value ' + HEAP8.length + ', (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ');

    }

  function _emscripten_resize_heap(requestedSize) {

      var oldSize = HEAPU8.length;

      requestedSize = requestedSize >>> 0;

      abortOnCannotGrowMemory(requestedSize);

    }



  var ENV = {};

  

  function getExecutableName() {

      return thisProgram || './this.program';

    }

  function getEnvStrings() {

      if (!getEnvStrings.strings) {

        // Default values.

        // Browser language detection #8751

        var lang = ((typeof navigator === 'object' && navigator.languages && navigator.languages[0]) || 'C').replace('-', '_') + '.UTF-8';

        var env = {

          'USER': 'web_user',

          'LOGNAME': 'web_user',

          'PATH': '/',

          'PWD': '/',

          'HOME': '/home/<USER>',

          'LANG': lang,

          '_': getExecutableName()

        };

        // Apply the user-provided values, if any.

        for (var x in ENV) {

          // x is a key in ENV; if ENV[x] is undefined, that means it was

          // explicitly set to be so. We allow user code to do that to

          // force variables with default values to remain unset.

          if (ENV[x] === undefined) delete env[x];

          else env[x] = ENV[x];

        }

        var strings = [];

        for (var x in env) {

          strings.push(x + '=' + env[x]);

        }

        getEnvStrings.strings = strings;

      }

      return getEnvStrings.strings;

    }

  

  var PATH = {splitPath:function(filename) {

        var splitPathRe = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;

        return splitPathRe.exec(filename).slice(1);

      },normalizeArray:function(parts, allowAboveRoot) {

        // if the path tries to go above the root, `up` ends up > 0

        var up = 0;

        for (var i = parts.length - 1; i >= 0; i--) {

          var last = parts[i];

          if (last === '.') {

            parts.splice(i, 1);

          } else if (last === '..') {

            parts.splice(i, 1);

            up++;

          } else if (up) {

            parts.splice(i, 1);

            up--;

          }

        }

        // if the path is allowed to go above the root, restore leading ..s

        if (allowAboveRoot) {

          for (; up; up--) {

            parts.unshift('..');

          }

        }

        return parts;

      },normalize:function(path) {

        var isAbsolute = path.charAt(0) === '/',

            trailingSlash = path.substr(-1) === '/';

        // Normalize the path

        path = PATH.normalizeArray(path.split('/').filter(function(p) {

          return !!p;

        }), !isAbsolute).join('/');

        if (!path && !isAbsolute) {

          path = '.';

        }

        if (path && trailingSlash) {

          path += '/';

        }

        return (isAbsolute ? '/' : '') + path;

      },dirname:function(path) {

        var result = PATH.splitPath(path),

            root = result[0],

            dir = result[1];

        if (!root && !dir) {

          // No dirname whatsoever

          return '.';

        }

        if (dir) {

          // It has a dirname, strip trailing slash

          dir = dir.substr(0, dir.length - 1);

        }

        return root + dir;

      },basename:function(path) {

        // EMSCRIPTEN return '/'' for '/', not an empty string

        if (path === '/') return '/';

        path = PATH.normalize(path);

        path = path.replace(/\/$/, "");

        var lastSlash = path.lastIndexOf('/');

        if (lastSlash === -1) return path;

        return path.substr(lastSlash+1);

      },extname:function(path) {

        return PATH.splitPath(path)[3];

      },join:function() {

        var paths = Array.prototype.slice.call(arguments, 0);

        return PATH.normalize(paths.join('/'));

      },join2:function(l, r) {

        return PATH.normalize(l + '/' + r);

      }};

  

  function getRandomDevice() {

      if (typeof crypto === 'object' && typeof crypto['getRandomValues'] === 'function') {

        // for modern web browsers

        var randomBuffer = new Uint8Array(1);

        return function() { crypto.getRandomValues(randomBuffer); return randomBuffer[0]; };

      } else

      if (ENVIRONMENT_IS_NODE) {

        // for nodejs with or without crypto support included

        try {

          var crypto_module = require('crypto');

          // nodejs has crypto support

          return function() { return crypto_module['randomBytes'](1)[0]; };

        } catch (e) {

          // nodejs doesn't have crypto support

        }

      }

      // we couldn't find a proper implementation, as Math.random() is not suitable for /dev/random, see emscripten-core/emscripten/pull/7096

      return function() { abort("no cryptographic support found for randomDevice. consider polyfilling it if you want to use something insecure like Math.random(), e.g. put this in a --pre-js: var crypto = { getRandomValues: function(array) { for (var i = 0; i < array.length; i++) array[i] = (Math.random()*256)|0 } };"); };

    }

  

  var PATH_FS = {resolve:function() {

        var resolvedPath = '',

          resolvedAbsolute = false;

        for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {

          var path = (i >= 0) ? arguments[i] : FS.cwd();

          // Skip empty and invalid entries

          if (typeof path !== 'string') {

            throw new TypeError('Arguments to path.resolve must be strings');

          } else if (!path) {

            return ''; // an invalid portion invalidates the whole thing

          }

          resolvedPath = path + '/' + resolvedPath;

          resolvedAbsolute = path.charAt(0) === '/';

        }

        // At this point the path should be resolved to a full absolute path, but

        // handle relative paths to be safe (might happen when process.cwd() fails)

        resolvedPath = PATH.normalizeArray(resolvedPath.split('/').filter(function(p) {

          return !!p;

        }), !resolvedAbsolute).join('/');

        return ((resolvedAbsolute ? '/' : '') + resolvedPath) || '.';

      },relative:function(from, to) {

        from = PATH_FS.resolve(from).substr(1);

        to = PATH_FS.resolve(to).substr(1);

        function trim(arr) {

          var start = 0;

          for (; start < arr.length; start++) {

            if (arr[start] !== '') break;

          }

          var end = arr.length - 1;

          for (; end >= 0; end--) {

            if (arr[end] !== '') break;

          }

          if (start > end) return [];

          return arr.slice(start, end - start + 1);

        }

        var fromParts = trim(from.split('/'));

        var toParts = trim(to.split('/'));

        var length = Math.min(fromParts.length, toParts.length);

        var samePartsLength = length;

        for (var i = 0; i < length; i++) {

          if (fromParts[i] !== toParts[i]) {

            samePartsLength = i;

            break;

          }

        }

        var outputParts = [];

        for (var i = samePartsLength; i < fromParts.length; i++) {

          outputParts.push('..');

        }

        outputParts = outputParts.concat(toParts.slice(samePartsLength));

        return outputParts.join('/');

      }};

  

  var TTY = {ttys:[],init:function () {

        // https://github.com/emscripten-core/emscripten/pull/1555

        // if (ENVIRONMENT_IS_NODE) {

        //   // currently, FS.init does not distinguish if process.stdin is a file or TTY

        //   // device, it always assumes it's a TTY device. because of this, we're forcing

        //   // process.stdin to UTF8 encoding to at least make stdin reading compatible

        //   // with text files until FS.init can be refactored.

        //   process['stdin']['setEncoding']('utf8');

        // }

      },shutdown:function() {

        // https://github.com/emscripten-core/emscripten/pull/1555

        // if (ENVIRONMENT_IS_NODE) {

        //   // inolen: any idea as to why node -e 'process.stdin.read()' wouldn't exit immediately (with process.stdin being a tty)?

        //   // isaacs: because now it's reading from the stream, you've expressed interest in it, so that read() kicks off a _read() which creates a ReadReq operation

        //   // inolen: I thought read() in that case was a synchronous operation that just grabbed some amount of buffered data if it exists?

        //   // isaacs: it is. but it also triggers a _read() call, which calls readStart() on the handle

        //   // isaacs: do process.stdin.pause() and i'd think it'd probably close the pending call

        //   process['stdin']['pause']();

        // }

      },register:function(dev, ops) {

        TTY.ttys[dev] = { input: [], output: [], ops: ops };

        FS.registerDevice(dev, TTY.stream_ops);

      },stream_ops:{open:function(stream) {

          var tty = TTY.ttys[stream.node.rdev];

          if (!tty) {

            throw new FS.ErrnoError(43);

          }

          stream.tty = tty;

          stream.seekable = false;

        },close:function(stream) {

          // flush any pending line data

          stream.tty.ops.flush(stream.tty);

        },flush:function(stream) {

          stream.tty.ops.flush(stream.tty);

        },read:function(stream, buffer, offset, length, pos /* ignored */) {

          if (!stream.tty || !stream.tty.ops.get_char) {

            throw new FS.ErrnoError(60);

          }

          var bytesRead = 0;

          for (var i = 0; i < length; i++) {

            var result;

            try {

              result = stream.tty.ops.get_char(stream.tty);

            } catch (e) {

              throw new FS.ErrnoError(29);

            }

            if (result === undefined && bytesRead === 0) {

              throw new FS.ErrnoError(6);

            }

            if (result === null || result === undefined) break;

            bytesRead++;

            buffer[offset+i] = result;

          }

          if (bytesRead) {

            stream.node.timestamp = Date.now();

          }

          return bytesRead;

        },write:function(stream, buffer, offset, length, pos) {

          if (!stream.tty || !stream.tty.ops.put_char) {

            throw new FS.ErrnoError(60);

          }

          try {

            for (var i = 0; i < length; i++) {

              stream.tty.ops.put_char(stream.tty, buffer[offset+i]);

            }

          } catch (e) {

            throw new FS.ErrnoError(29);

          }

          if (length) {

            stream.node.timestamp = Date.now();

          }

          return i;

        }},default_tty_ops:{get_char:function(tty) {

          if (!tty.input.length) {

            var result = null;

            if (ENVIRONMENT_IS_NODE) {

              // we will read data by chunks of BUFSIZE

              var BUFSIZE = 256;

              var buf = Buffer.alloc(BUFSIZE);

              var bytesRead = 0;

  

              try {

                bytesRead = nodeFS.readSync(process.stdin.fd, buf, 0, BUFSIZE, null);

              } catch(e) {

                // Cross-platform differences: on Windows, reading EOF throws an exception, but on other OSes,

                // reading EOF returns 0. Uniformize behavior by treating the EOF exception to return 0.

                if (e.toString().includes('EOF')) bytesRead = 0;

                else throw e;

              }

  

              if (bytesRead > 0) {

                result = buf.slice(0, bytesRead).toString('utf-8');

              } else {

                result = null;

              }

            } else

            if (typeof window != 'undefined' &&

              typeof window.prompt == 'function') {

              // Browser.

              result = window.prompt('Input: ');  // returns null on cancel

              if (result !== null) {

                result += '\n';

              }

            } else if (typeof readline == 'function') {

              // Command line.

              result = readline();

              if (result !== null) {

                result += '\n';

              }

            }

            if (!result) {

              return null;

            }

            tty.input = intArrayFromString(result, true);

          }

          return tty.input.shift();

        },put_char:function(tty, val) {

          if (val === null || val === 10) {

            out(UTF8ArrayToString(tty.output, 0));

            tty.output = [];

          } else {

            if (val != 0) tty.output.push(val); // val == 0 would cut text output off in the middle.

          }

        },flush:function(tty) {

          if (tty.output && tty.output.length > 0) {

            out(UTF8ArrayToString(tty.output, 0));

            tty.output = [];

          }

        }},default_tty1_ops:{put_char:function(tty, val) {

          if (val === null || val === 10) {

            err(UTF8ArrayToString(tty.output, 0));

            tty.output = [];

          } else {

            if (val != 0) tty.output.push(val);

          }

        },flush:function(tty) {

          if (tty.output && tty.output.length > 0) {

            err(UTF8ArrayToString(tty.output, 0));

            tty.output = [];

          }

        }}};

  

  function zeroMemory(address, size) {

      HEAPU8.fill(0, address, address + size);

    }

  

  function alignMemory(size, alignment) {

      assert(alignment, "alignment argument is required");

      return Math.ceil(size / alignment) * alignment;

    }

  function mmapAlloc(size) {

      abort('internal error: mmapAlloc called but `memalign` native symbol not exported');

    }

  var MEMFS = {ops_table:null,mount:function(mount) {

        return MEMFS.createNode(null, '/', 16384 | 511 /* 0777 */, 0);

      },createNode:function(parent, name, mode, dev) {

        if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {

          // no supported

          throw new FS.ErrnoError(63);

        }

        if (!MEMFS.ops_table) {

          MEMFS.ops_table = {

            dir: {

              node: {

                getattr: MEMFS.node_ops.getattr,

                setattr: MEMFS.node_ops.setattr,

                lookup: MEMFS.node_ops.lookup,

                mknod: MEMFS.node_ops.mknod,

                rename: MEMFS.node_ops.rename,

                unlink: MEMFS.node_ops.unlink,

                rmdir: MEMFS.node_ops.rmdir,

                readdir: MEMFS.node_ops.readdir,

                symlink: MEMFS.node_ops.symlink

              },

              stream: {

                llseek: MEMFS.stream_ops.llseek

              }

            },

            file: {

              node: {

                getattr: MEMFS.node_ops.getattr,

                setattr: MEMFS.node_ops.setattr

              },

              stream: {

                llseek: MEMFS.stream_ops.llseek,

                read: MEMFS.stream_ops.read,

                write: MEMFS.stream_ops.write,

                allocate: MEMFS.stream_ops.allocate,

                mmap: MEMFS.stream_ops.mmap,

                msync: MEMFS.stream_ops.msync

              }

            },

            link: {

              node: {

                getattr: MEMFS.node_ops.getattr,

                setattr: MEMFS.node_ops.setattr,

                readlink: MEMFS.node_ops.readlink

              },

              stream: {}

            },

            chrdev: {

              node: {

                getattr: MEMFS.node_ops.getattr,

                setattr: MEMFS.node_ops.setattr

              },

              stream: FS.chrdev_stream_ops

            }

          };

        }

        var node = FS.createNode(parent, name, mode, dev);

        if (FS.isDir(node.mode)) {

          node.node_ops = MEMFS.ops_table.dir.node;

          node.stream_ops = MEMFS.ops_table.dir.stream;

          node.contents = {};

        } else if (FS.isFile(node.mode)) {

          node.node_ops = MEMFS.ops_table.file.node;

          node.stream_ops = MEMFS.ops_table.file.stream;

          node.usedBytes = 0; // The actual number of bytes used in the typed array, as opposed to contents.length which gives the whole capacity.

          // When the byte data of the file is populated, this will point to either a typed array, or a normal JS array. Typed arrays are preferred

          // for performance, and used by default. However, typed arrays are not resizable like normal JS arrays are, so there is a small disk size

          // penalty involved for appending file writes that continuously grow a file similar to std::vector capacity vs used -scheme.

          node.contents = null; 

        } else if (FS.isLink(node.mode)) {

          node.node_ops = MEMFS.ops_table.link.node;

          node.stream_ops = MEMFS.ops_table.link.stream;

        } else if (FS.isChrdev(node.mode)) {

          node.node_ops = MEMFS.ops_table.chrdev.node;

          node.stream_ops = MEMFS.ops_table.chrdev.stream;

        }

        node.timestamp = Date.now();

        // add the new node to the parent

        if (parent) {

          parent.contents[name] = node;

          parent.timestamp = node.timestamp;

        }

        return node;

      },getFileDataAsTypedArray:function(node) {

        if (!node.contents) return new Uint8Array(0);

        if (node.contents.subarray) return node.contents.subarray(0, node.usedBytes); // Make sure to not return excess unused bytes.

        return new Uint8Array(node.contents);

      },expandFileStorage:function(node, newCapacity) {

        var prevCapacity = node.contents ? node.contents.length : 0;

        if (prevCapacity >= newCapacity) return; // No need to expand, the storage was already large enough.

        // Don't expand strictly to the given requested limit if it's only a very small increase, but instead geometrically grow capacity.

        // For small filesizes (<1MB), perform size*2 geometric increase, but for large sizes, do a much more conservative size*1.125 increase to

        // avoid overshooting the allocation cap by a very large margin.

        var CAPACITY_DOUBLING_MAX = 1024 * 1024;

        newCapacity = Math.max(newCapacity, (prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2.0 : 1.125)) >>> 0);

        if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256); // At minimum allocate 256b for each file when expanding.

        var oldContents = node.contents;

        node.contents = new Uint8Array(newCapacity); // Allocate new storage.

        if (node.usedBytes > 0) node.contents.set(oldContents.subarray(0, node.usedBytes), 0); // Copy old data over to the new storage.

      },resizeFileStorage:function(node, newSize) {

        if (node.usedBytes == newSize) return;

        if (newSize == 0) {

          node.contents = null; // Fully decommit when requesting a resize to zero.

          node.usedBytes = 0;

        } else {

          var oldContents = node.contents;

          node.contents = new Uint8Array(newSize); // Allocate new storage.

          if (oldContents) {

            node.contents.set(oldContents.subarray(0, Math.min(newSize, node.usedBytes))); // Copy old data over to the new storage.

          }

          node.usedBytes = newSize;

        }

      },node_ops:{getattr:function(node) {

          var attr = {};

          // device numbers reuse inode numbers.

          attr.dev = FS.isChrdev(node.mode) ? node.id : 1;

          attr.ino = node.id;

          attr.mode = node.mode;

          attr.nlink = 1;

          attr.uid = 0;

          attr.gid = 0;

          attr.rdev = node.rdev;

          if (FS.isDir(node.mode)) {

            attr.size = 4096;

          } else if (FS.isFile(node.mode)) {

            attr.size = node.usedBytes;

          } else if (FS.isLink(node.mode)) {

            attr.size = node.link.length;

          } else {

            attr.size = 0;

          }

          attr.atime = new Date(node.timestamp);

          attr.mtime = new Date(node.timestamp);

          attr.ctime = new Date(node.timestamp);

          // NOTE: In our implementation, st_blocks = Math.ceil(st_size/st_blksize),

          //       but this is not required by the standard.

          attr.blksize = 4096;

          attr.blocks = Math.ceil(attr.size / attr.blksize);

          return attr;

        },setattr:function(node, attr) {

          if (attr.mode !== undefined) {

            node.mode = attr.mode;

          }

          if (attr.timestamp !== undefined) {

            node.timestamp = attr.timestamp;

          }

          if (attr.size !== undefined) {

            MEMFS.resizeFileStorage(node, attr.size);

          }

        },lookup:function(parent, name) {

          throw FS.genericErrors[44];

        },mknod:function(parent, name, mode, dev) {

          return MEMFS.createNode(parent, name, mode, dev);

        },rename:function(old_node, new_dir, new_name) {

          // if we're overwriting a directory at new_name, make sure it's empty.

          if (FS.isDir(old_node.mode)) {

            var new_node;

            try {

              new_node = FS.lookupNode(new_dir, new_name);

            } catch (e) {

            }

            if (new_node) {

              for (var i in new_node.contents) {

                throw new FS.ErrnoError(55);

              }

            }

          }

          // do the internal rewiring

          delete old_node.parent.contents[old_node.name];

          old_node.parent.timestamp = Date.now()

          old_node.name = new_name;

          new_dir.contents[new_name] = old_node;

          new_dir.timestamp = old_node.parent.timestamp;

          old_node.parent = new_dir;

        },unlink:function(parent, name) {

          delete parent.contents[name];

          parent.timestamp = Date.now();

        },rmdir:function(parent, name) {

          var node = FS.lookupNode(parent, name);

          for (var i in node.contents) {

            throw new FS.ErrnoError(55);

          }

          delete parent.contents[name];

          parent.timestamp = Date.now();

        },readdir:function(node) {

          var entries = ['.', '..'];

          for (var key in node.contents) {

            if (!node.contents.hasOwnProperty(key)) {

              continue;

            }

            entries.push(key);

          }

          return entries;

        },symlink:function(parent, newname, oldpath) {

          var node = MEMFS.createNode(parent, newname, 511 /* 0777 */ | 40960, 0);

          node.link = oldpath;

          return node;

        },readlink:function(node) {

          if (!FS.isLink(node.mode)) {

            throw new FS.ErrnoError(28);

          }

          return node.link;

        }},stream_ops:{read:function(stream, buffer, offset, length, position) {

          var contents = stream.node.contents;

          if (position >= stream.node.usedBytes) return 0;

          var size = Math.min(stream.node.usedBytes - position, length);

          assert(size >= 0);

          if (size > 8 && contents.subarray) { // non-trivial, and typed array

            buffer.set(contents.subarray(position, position + size), offset);

          } else {

            for (var i = 0; i < size; i++) buffer[offset + i] = contents[position + i];

          }

          return size;

        },write:function(stream, buffer, offset, length, position, canOwn) {

          // The data buffer should be a typed array view

          assert(!(buffer instanceof ArrayBuffer));

  

          if (!length) return 0;

          var node = stream.node;

          node.timestamp = Date.now();

  

          if (buffer.subarray && (!node.contents || node.contents.subarray)) { // This write is from a typed array to a typed array?

            if (canOwn) {

              assert(position === 0, 'canOwn must imply no weird position inside the file');

              node.contents = buffer.subarray(offset, offset + length);

              node.usedBytes = length;

              return length;

            } else if (node.usedBytes === 0 && position === 0) { // If this is a simple first write to an empty file, do a fast set since we don't need to care about old data.

              node.contents = buffer.slice(offset, offset + length);

              node.usedBytes = length;

              return length;

            } else if (position + length <= node.usedBytes) { // Writing to an already allocated and used subrange of the file?

              node.contents.set(buffer.subarray(offset, offset + length), position);

              return length;

            }

          }

  

          // Appending to an existing file and we need to reallocate, or source data did not come as a typed array.

          MEMFS.expandFileStorage(node, position+length);

          if (node.contents.subarray && buffer.subarray) {

            // Use typed array write which is available.

            node.contents.set(buffer.subarray(offset, offset + length), position);

          } else {

            for (var i = 0; i < length; i++) {

             node.contents[position + i] = buffer[offset + i]; // Or fall back to manual write if not.

            }

          }

          node.usedBytes = Math.max(node.usedBytes, position + length);

          return length;

        },llseek:function(stream, offset, whence) {

          var position = offset;

          if (whence === 1) {

            position += stream.position;

          } else if (whence === 2) {

            if (FS.isFile(stream.node.mode)) {

              position += stream.node.usedBytes;

            }

          }

          if (position < 0) {

            throw new FS.ErrnoError(28);

          }

          return position;

        },allocate:function(stream, offset, length) {

          MEMFS.expandFileStorage(stream.node, offset + length);

          stream.node.usedBytes = Math.max(stream.node.usedBytes, offset + length);

        },mmap:function(stream, address, length, position, prot, flags) {

          if (address !== 0) {

            // We don't currently support location hints for the address of the mapping

            throw new FS.ErrnoError(28);

          }

          if (!FS.isFile(stream.node.mode)) {

            throw new FS.ErrnoError(43);

          }

          var ptr;

          var allocated;

          var contents = stream.node.contents;

          // Only make a new copy when MAP_PRIVATE is specified.

          if (!(flags & 2) && contents.buffer === buffer) {

            // We can't emulate MAP_SHARED when the file is not backed by the buffer

            // we're mapping to (e.g. the HEAP buffer).

            allocated = false;

            ptr = contents.byteOffset;

          } else {

            // Try to avoid unnecessary slices.

            if (position > 0 || position + length < contents.length) {

              if (contents.subarray) {

                contents = contents.subarray(position, position + length);

              } else {

                contents = Array.prototype.slice.call(contents, position, position + length);

              }

            }

            allocated = true;

            ptr = mmapAlloc(length);

            if (!ptr) {

              throw new FS.ErrnoError(48);

            }

            HEAP8.set(contents, ptr);

          }

          return { ptr: ptr, allocated: allocated };

        },msync:function(stream, buffer, offset, length, mmapFlags) {

          if (!FS.isFile(stream.node.mode)) {

            throw new FS.ErrnoError(43);

          }

          if (mmapFlags & 2) {

            // MAP_PRIVATE calls need not to be synced back to underlying fs

            return 0;

          }

  

          var bytesWritten = MEMFS.stream_ops.write(stream, buffer, 0, length, offset, false);

          // should we check if bytesWritten and length are the same?

          return 0;

        }}};

  

  function asyncLoad(url, onload, onerror, noRunDep) {

      var dep = !noRunDep ? getUniqueRunDependency('al ' + url) : '';

      readAsync(url, function(arrayBuffer) {

        assert(arrayBuffer, 'Loading data file "' + url + '" failed (no arrayBuffer).');

        onload(new Uint8Array(arrayBuffer));

        if (dep) removeRunDependency(dep);

      }, function(event) {

        if (onerror) {

          onerror();

        } else {

          throw 'Loading data file "' + url + '" failed.';

        }

      });

      if (dep) addRunDependency(dep);

    }

  

  var ERRNO_MESSAGES = {0:"Success",1:"Arg list too long",2:"Permission denied",3:"Address already in use",4:"Address not available",5:"Address family not supported by protocol family",6:"No more processes",7:"Socket already connected",8:"Bad file number",9:"Trying to read unreadable message",10:"Mount device busy",11:"Operation canceled",12:"No children",13:"Connection aborted",14:"Connection refused",15:"Connection reset by peer",16:"File locking deadlock error",17:"Destination address required",18:"Math arg out of domain of func",19:"Quota exceeded",20:"File exists",21:"Bad address",22:"File too large",23:"Host is unreachable",24:"Identifier removed",25:"Illegal byte sequence",26:"Connection already in progress",27:"Interrupted system call",28:"Invalid argument",29:"I/O error",30:"Socket is already connected",31:"Is a directory",32:"Too many symbolic links",33:"Too many open files",34:"Too many links",35:"Message too long",36:"Multihop attempted",37:"File or path name too long",38:"Network interface is not configured",39:"Connection reset by network",40:"Network is unreachable",41:"Too many open files in system",42:"No buffer space available",43:"No such device",44:"No such file or directory",45:"Exec format error",46:"No record locks available",47:"The link has been severed",48:"Not enough core",49:"No message of desired type",50:"Protocol not available",51:"No space left on device",52:"Function not implemented",53:"Socket is not connected",54:"Not a directory",55:"Directory not empty",56:"State not recoverable",57:"Socket operation on non-socket",59:"Not a typewriter",60:"No such device or address",61:"Value too large for defined data type",62:"Previous owner died",63:"Not super-user",64:"Broken pipe",65:"Protocol error",66:"Unknown protocol",67:"Protocol wrong type for socket",68:"Math result not representable",69:"Read only file system",70:"Illegal seek",71:"No such process",72:"Stale file handle",73:"Connection timed out",74:"Text file busy",75:"Cross-device link",100:"Device not a stream",101:"Bad font file fmt",102:"Invalid slot",103:"Invalid request code",104:"No anode",105:"Block device required",106:"Channel number out of range",107:"Level 3 halted",108:"Level 3 reset",109:"Link number out of range",110:"Protocol driver not attached",111:"No CSI structure available",112:"Level 2 halted",113:"Invalid exchange",114:"Invalid request descriptor",115:"Exchange full",116:"No data (for no delay io)",117:"Timer expired",118:"Out of streams resources",119:"Machine is not on the network",120:"Package not installed",121:"The object is remote",122:"Advertise error",123:"Srmount error",124:"Communication error on send",125:"Cross mount point (not really error)",126:"Given log. name not unique",127:"f.d. invalid for this operation",128:"Remote address changed",129:"Can   access a needed shared lib",130:"Accessing a corrupted shared lib",131:".lib section in a.out corrupted",132:"Attempting to link in too many libs",133:"Attempting to exec a shared library",135:"Streams pipe error",136:"Too many users",137:"Socket type not supported",138:"Not supported",139:"Protocol family not supported",140:"Can't send after socket shutdown",141:"Too many references",142:"Host is down",148:"No medium (in tape drive)",156:"Level 2 not synchronized"};

  

  var ERRNO_CODES = {};

  var FS = {root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(path, opts) {

        path = PATH_FS.resolve(FS.cwd(), path);

        opts = opts || {};

  

        if (!path) return { path: '', node: null };

  

        var defaults = {

          follow_mount: true,

          recurse_count: 0

        };

        for (var key in defaults) {

          if (opts[key] === undefined) {

            opts[key] = defaults[key];

          }

        }

  

        if (opts.recurse_count > 8) {  // max recursive lookup of 8

          throw new FS.ErrnoError(32);

        }

  

        // split the path

        var parts = PATH.normalizeArray(path.split('/').filter(function(p) {

          return !!p;

        }), false);

  

        // start at the root

        var current = FS.root;

        var current_path = '/';

  

        for (var i = 0; i < parts.length; i++) {

          var islast = (i === parts.length-1);

          if (islast && opts.parent) {

            // stop resolving

            break;

          }

  

          current = FS.lookupNode(current, parts[i]);

          current_path = PATH.join2(current_path, parts[i]);

  

          // jump to the mount's root node if this is a mountpoint

          if (FS.isMountpoint(current)) {

            if (!islast || (islast && opts.follow_mount)) {

              current = current.mounted.root;

            }

          }

  

          // by default, lookupPath will not follow a symlink if it is the final path component.

          // setting opts.follow = true will override this behavior.

          if (!islast || opts.follow) {

            var count = 0;

            while (FS.isLink(current.mode)) {

              var link = FS.readlink(current_path);

              current_path = PATH_FS.resolve(PATH.dirname(current_path), link);

  

              var lookup = FS.lookupPath(current_path, { recurse_count: opts.recurse_count });

              current = lookup.node;

  

              if (count++ > 40) {  // limit max consecutive symlinks to 40 (SYMLOOP_MAX).

                throw new FS.ErrnoError(32);

              }

            }

          }

        }

  

        return { path: current_path, node: current };

      },getPath:function(node) {

        var path;

        while (true) {

          if (FS.isRoot(node)) {

            var mount = node.mount.mountpoint;

            if (!path) return mount;

            return mount[mount.length-1] !== '/' ? mount + '/' + path : mount + path;

          }

          path = path ? node.name + '/' + path : node.name;

          node = node.parent;

        }

      },hashName:function(parentid, name) {

        var hash = 0;

  

        for (var i = 0; i < name.length; i++) {

          hash = ((hash << 5) - hash + name.charCodeAt(i)) | 0;

        }

        return ((parentid + hash) >>> 0) % FS.nameTable.length;

      },hashAddNode:function(node) {

        var hash = FS.hashName(node.parent.id, node.name);

        node.name_next = FS.nameTable[hash];

        FS.nameTable[hash] = node;

      },hashRemoveNode:function(node) {

        var hash = FS.hashName(node.parent.id, node.name);

        if (FS.nameTable[hash] === node) {

          FS.nameTable[hash] = node.name_next;

        } else {

          var current = FS.nameTable[hash];

          while (current) {

            if (current.name_next === node) {

              current.name_next = node.name_next;

              break;

            }

            current = current.name_next;

          }

        }

      },lookupNode:function(parent, name) {

        var errCode = FS.mayLookup(parent);

        if (errCode) {

          throw new FS.ErrnoError(errCode, parent);

        }

        var hash = FS.hashName(parent.id, name);

        for (var node = FS.nameTable[hash]; node; node = node.name_next) {

          var nodeName = node.name;

          if (node.parent.id === parent.id && nodeName === name) {

            return node;

          }

        }

        // if we failed to find it in the cache, call into the VFS

        return FS.lookup(parent, name);

      },createNode:function(parent, name, mode, rdev) {

        assert(typeof parent === 'object')

        var node = new FS.FSNode(parent, name, mode, rdev);

  

        FS.hashAddNode(node);

  

        return node;

      },destroyNode:function(node) {

        FS.hashRemoveNode(node);

      },isRoot:function(node) {

        return node === node.parent;

      },isMountpoint:function(node) {

        return !!node.mounted;

      },isFile:function(mode) {

        return (mode & 61440) === 32768;

      },isDir:function(mode) {

        return (mode & 61440) === 16384;

      },isLink:function(mode) {

        return (mode & 61440) === 40960;

      },isChrdev:function(mode) {

        return (mode & 61440) === 8192;

      },isBlkdev:function(mode) {

        return (mode & 61440) === 24576;

      },isFIFO:function(mode) {

        return (mode & 61440) === 4096;

      },isSocket:function(mode) {

        return (mode & 49152) === 49152;

      },flagModes:{"r":0,"r+":2,"w":577,"w+":578,"a":1089,"a+":1090},modeStringToFlags:function(str) {

        var flags = FS.flagModes[str];

        if (typeof flags === 'undefined') {

          throw new Error('Unknown file open mode: ' + str);

        }

        return flags;

      },flagsToPermissionString:function(flag) {

        var perms = ['r', 'w', 'rw'][flag & 3];

        if ((flag & 512)) {

          perms += 'w';

        }

        return perms;

      },nodePermissions:function(node, perms) {

        if (FS.ignorePermissions) {

          return 0;

        }

        // return 0 if any user, group or owner bits are set.

        if (perms.includes('r') && !(node.mode & 292)) {

          return 2;

        } else if (perms.includes('w') && !(node.mode & 146)) {

          return 2;

        } else if (perms.includes('x') && !(node.mode & 73)) {

          return 2;

        }

        return 0;

      },mayLookup:function(dir) {

        var errCode = FS.nodePermissions(dir, 'x');

        if (errCode) return errCode;

        if (!dir.node_ops.lookup) return 2;

        return 0;

      },mayCreate:function(dir, name) {

        try {

          var node = FS.lookupNode(dir, name);

          return 20;

        } catch (e) {

        }

        return FS.nodePermissions(dir, 'wx');

      },mayDelete:function(dir, name, isdir) {

        var node;

        try {

          node = FS.lookupNode(dir, name);

        } catch (e) {

          return e.errno;

        }

        var errCode = FS.nodePermissions(dir, 'wx');

        if (errCode) {

          return errCode;

        }

        if (isdir) {

          if (!FS.isDir(node.mode)) {

            return 54;

          }

          if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {

            return 10;

          }

        } else {

          if (FS.isDir(node.mode)) {

            return 31;

          }

        }

        return 0;

      },mayOpen:function(node, flags) {

        if (!node) {

          return 44;

        }

        if (FS.isLink(node.mode)) {

          return 32;

        } else if (FS.isDir(node.mode)) {

          if (FS.flagsToPermissionString(flags) !== 'r' || // opening for write

              (flags & 512)) { // TODO: check for O_SEARCH? (== search for dir only)

            return 31;

          }

        }

        return FS.nodePermissions(node, FS.flagsToPermissionString(flags));

      },MAX_OPEN_FDS:4096,nextfd:function(fd_start, fd_end) {

        fd_start = fd_start || 0;

        fd_end = fd_end || FS.MAX_OPEN_FDS;

        for (var fd = fd_start; fd <= fd_end; fd++) {

          if (!FS.streams[fd]) {

            return fd;

          }

        }

        throw new FS.ErrnoError(33);

      },getStream:function(fd) {

        return FS.streams[fd];

      },createStream:function(stream, fd_start, fd_end) {

        if (!FS.FSStream) {

          FS.FSStream = /** @constructor */ function(){};

          FS.FSStream.prototype = {

            object: {

              get: function() { return this.node; },

              set: function(val) { this.node = val; }

            },

            isRead: {

              get: function() { return (this.flags & 2097155) !== 1; }

            },

            isWrite: {

              get: function() { return (this.flags & 2097155) !== 0; }

            },

            isAppend: {

              get: function() { return (this.flags & 1024); }

            }

          };

        }

        // clone it, so we can return an instance of FSStream

        var newStream = new FS.FSStream();

        for (var p in stream) {

          newStream[p] = stream[p];

        }

        stream = newStream;

        var fd = FS.nextfd(fd_start, fd_end);

        stream.fd = fd;

        FS.streams[fd] = stream;

        return stream;

      },closeStream:function(fd) {

        FS.streams[fd] = null;

      },chrdev_stream_ops:{open:function(stream) {

          var device = FS.getDevice(stream.node.rdev);

          // override node's stream ops with the device's

          stream.stream_ops = device.stream_ops;

          // forward the open call

          if (stream.stream_ops.open) {

            stream.stream_ops.open(stream);

          }

        },llseek:function() {

          throw new FS.ErrnoError(70);

        }},major:function(dev) {

        return ((dev) >> 8);

      },minor:function(dev) {

        return ((dev) & 0xff);

      },makedev:function(ma, mi) {

        return ((ma) << 8 | (mi));

      },registerDevice:function(dev, ops) {

        FS.devices[dev] = { stream_ops: ops };

      },getDevice:function(dev) {

        return FS.devices[dev];

      },getMounts:function(mount) {

        var mounts = [];

        var check = [mount];

  

        while (check.length) {

          var m = check.pop();

  

          mounts.push(m);

  

          check.push.apply(check, m.mounts);

        }

  

        return mounts;

      },syncfs:function(populate, callback) {

        if (typeof(populate) === 'function') {

          callback = populate;

          populate = false;

        }

  

        FS.syncFSRequests++;

  

        if (FS.syncFSRequests > 1) {

          err('warning: ' + FS.syncFSRequests + ' FS.syncfs operations in flight at once, probably just doing extra work');

        }

  

        var mounts = FS.getMounts(FS.root.mount);

        var completed = 0;

  

        function doCallback(errCode) {

          assert(FS.syncFSRequests > 0);

          FS.syncFSRequests--;

          return callback(errCode);

        }

  

        function done(errCode) {

          if (errCode) {

            if (!done.errored) {

              done.errored = true;

              return doCallback(errCode);

            }

            return;

          }

          if (++completed >= mounts.length) {

            doCallback(null);

          }

        };

  

        // sync all mounts

        mounts.forEach(function (mount) {

          if (!mount.type.syncfs) {

            return done(null);

          }

          mount.type.syncfs(mount, populate, done);

        });

      },mount:function(type, opts, mountpoint) {

        if (typeof type === 'string') {

          // The filesystem was not included, and instead we have an error

          // message stored in the variable.

          throw type;

        }

        var root = mountpoint === '/';

        var pseudo = !mountpoint;

        var node;

  

        if (root && FS.root) {

          throw new FS.ErrnoError(10);

        } else if (!root && !pseudo) {

          var lookup = FS.lookupPath(mountpoint, { follow_mount: false });

  

          mountpoint = lookup.path;  // use the absolute path

          node = lookup.node;

  

          if (FS.isMountpoint(node)) {

            throw new FS.ErrnoError(10);

          }

  

          if (!FS.isDir(node.mode)) {

            throw new FS.ErrnoError(54);

          }

        }

  

        var mount = {

          type: type,

          opts: opts,

          mountpoint: mountpoint,

          mounts: []

        };

  

        // create a root node for the fs

        var mountRoot = type.mount(mount);

        mountRoot.mount = mount;

        mount.root = mountRoot;

  

        if (root) {

          FS.root = mountRoot;

        } else if (node) {

          // set as a mountpoint

          node.mounted = mount;

  

          // add the new mount to the current mount's children

          if (node.mount) {

            node.mount.mounts.push(mount);

          }

        }

  

        return mountRoot;

      },unmount:function (mountpoint) {

        var lookup = FS.lookupPath(mountpoint, { follow_mount: false });

  

        if (!FS.isMountpoint(lookup.node)) {

          throw new FS.ErrnoError(28);

        }

  

        // destroy the nodes for this mount, and all its child mounts

        var node = lookup.node;

        var mount = node.mounted;

        var mounts = FS.getMounts(mount);

  

        Object.keys(FS.nameTable).forEach(function (hash) {

          var current = FS.nameTable[hash];

  

          while (current) {

            var next = current.name_next;

  

            if (mounts.includes(current.mount)) {

              FS.destroyNode(current);

            }

  

            current = next;

          }

        });

  

        // no longer a mountpoint

        node.mounted = null;

  

        // remove this mount from the child mounts

        var idx = node.mount.mounts.indexOf(mount);

        assert(idx !== -1);

        node.mount.mounts.splice(idx, 1);

      },lookup:function(parent, name) {

        return parent.node_ops.lookup(parent, name);

      },mknod:function(path, mode, dev) {

        var lookup = FS.lookupPath(path, { parent: true });

        var parent = lookup.node;

        var name = PATH.basename(path);

        if (!name || name === '.' || name === '..') {

          throw new FS.ErrnoError(28);

        }

        var errCode = FS.mayCreate(parent, name);

        if (errCode) {

          throw new FS.ErrnoError(errCode);

        }

        if (!parent.node_ops.mknod) {

          throw new FS.ErrnoError(63);

        }

        return parent.node_ops.mknod(parent, name, mode, dev);

      },create:function(path, mode) {

        mode = mode !== undefined ? mode : 438 /* 0666 */;

        mode &= 4095;

        mode |= 32768;

        return FS.mknod(path, mode, 0);

      },mkdir:function(path, mode) {

        mode = mode !== undefined ? mode : 511 /* 0777 */;

        mode &= 511 | 512;

        mode |= 16384;

        return FS.mknod(path, mode, 0);

      },mkdirTree:function(path, mode) {

        var dirs = path.split('/');

        var d = '';

        for (var i = 0; i < dirs.length; ++i) {

          if (!dirs[i]) continue;

          d += '/' + dirs[i];

          try {

            FS.mkdir(d, mode);

          } catch(e) {

            if (e.errno != 20) throw e;

          }

        }

      },mkdev:function(path, mode, dev) {

        if (typeof(dev) === 'undefined') {

          dev = mode;

          mode = 438 /* 0666 */;

        }

        mode |= 8192;

        return FS.mknod(path, mode, dev);

      },symlink:function(oldpath, newpath) {

        if (!PATH_FS.resolve(oldpath)) {

          throw new FS.ErrnoError(44);

        }

        var lookup = FS.lookupPath(newpath, { parent: true });

        var parent = lookup.node;

        if (!parent) {

          throw new FS.ErrnoError(44);

        }

        var newname = PATH.basename(newpath);

        var errCode = FS.mayCreate(parent, newname);

        if (errCode) {

          throw new FS.ErrnoError(errCode);

        }

        if (!parent.node_ops.symlink) {

          throw new FS.ErrnoError(63);

        }

        return parent.node_ops.symlink(parent, newname, oldpath);

      },rename:function(old_path, new_path) {

        var old_dirname = PATH.dirname(old_path);

        var new_dirname = PATH.dirname(new_path);

        var old_name = PATH.basename(old_path);

        var new_name = PATH.basename(new_path);

        // parents must exist

        var lookup, old_dir, new_dir;

  

        // let the errors from non existant directories percolate up

        lookup = FS.lookupPath(old_path, { parent: true });

        old_dir = lookup.node;

        lookup = FS.lookupPath(new_path, { parent: true });

        new_dir = lookup.node;

  

        if (!old_dir || !new_dir) throw new FS.ErrnoError(44);

        // need to be part of the same mount

        if (old_dir.mount !== new_dir.mount) {

          throw new FS.ErrnoError(75);

        }

        // source must exist

        var old_node = FS.lookupNode(old_dir, old_name);

        // old path should not be an ancestor of the new path

        var relative = PATH_FS.relative(old_path, new_dirname);

        if (relative.charAt(0) !== '.') {

          throw new FS.ErrnoError(28);

        }

        // new path should not be an ancestor of the old path

        relative = PATH_FS.relative(new_path, old_dirname);

        if (relative.charAt(0) !== '.') {

          throw new FS.ErrnoError(55);

        }

        // see if the new path already exists

        var new_node;

        try {

          new_node = FS.lookupNode(new_dir, new_name);

        } catch (e) {

          // not fatal

        }

        // early out if nothing needs to change

        if (old_node === new_node) {

          return;

        }

        // we'll need to delete the old entry

        var isdir = FS.isDir(old_node.mode);

        var errCode = FS.mayDelete(old_dir, old_name, isdir);

        if (errCode) {

          throw new FS.ErrnoError(errCode);

        }

        // need delete permissions if we'll be overwriting.

        // need create permissions if new doesn't already exist.

        errCode = new_node ?

          FS.mayDelete(new_dir, new_name, isdir) :

          FS.mayCreate(new_dir, new_name);

        if (errCode) {

          throw new FS.ErrnoError(errCode);

        }

        if (!old_dir.node_ops.rename) {

          throw new FS.ErrnoError(63);

        }

        if (FS.isMountpoint(old_node) || (new_node && FS.isMountpoint(new_node))) {

          throw new FS.ErrnoError(10);

        }

        // if we are going to change the parent, check write permissions

        if (new_dir !== old_dir) {

          errCode = FS.nodePermissions(old_dir, 'w');

          if (errCode) {

            throw new FS.ErrnoError(errCode);

          }

        }

        // remove the node from the lookup hash

        FS.hashRemoveNode(old_node);

        // do the underlying fs rename

        try {

          old_dir.node_ops.rename(old_node, new_dir, new_name);

        } catch (e) {

          throw e;

        } finally {

          // add the node back to the hash (in case node_ops.rename

          // changed its name)

          FS.hashAddNode(old_node);

        }

      },rmdir:function(path) {

        var lookup = FS.lookupPath(path, { parent: true });

        var parent = lookup.node;

        var name = PATH.basename(path);

        var node = FS.lookupNode(parent, name);

        var errCode = FS.mayDelete(parent, name, true);

        if (errCode) {

          throw new FS.ErrnoError(errCode);

        }

        if (!parent.node_ops.rmdir) {

          throw new FS.ErrnoError(63);

        }

        if (FS.isMountpoint(node)) {

          throw new FS.ErrnoError(10);

        }

        parent.node_ops.rmdir(parent, name);

        FS.destroyNode(node);

      },readdir:function(path) {

        var lookup = FS.lookupPath(path, { follow: true });

        var node = lookup.node;

        if (!node.node_ops.readdir) {

          throw new FS.ErrnoError(54);

        }

        return node.node_ops.readdir(node);

      },unlink:function(path) {

        var lookup = FS.lookupPath(path, { parent: true });

        var parent = lookup.node;

        var name = PATH.basename(path);

        var node = FS.lookupNode(parent, name);

        var errCode = FS.mayDelete(parent, name, false);

        if (errCode) {

          // According to POSIX, we should map EISDIR to EPERM, but

          // we instead do what Linux does (and we must, as we use

          // the musl linux libc).

          throw new FS.ErrnoError(errCode);

        }

        if (!parent.node_ops.unlink) {

          throw new FS.ErrnoError(63);

        }

        if (FS.isMountpoint(node)) {

          throw new FS.ErrnoError(10);

        }

        parent.node_ops.unlink(parent, name);

        FS.destroyNode(node);

      },readlink:function(path) {

        var lookup = FS.lookupPath(path);

        var link = lookup.node;

        if (!link) {

          throw new FS.ErrnoError(44);

        }

        if (!link.node_ops.readlink) {

          throw new FS.ErrnoError(28);

        }

        return PATH_FS.resolve(FS.getPath(link.parent), link.node_ops.readlink(link));

      },stat:function(path, dontFollow) {

        var lookup = FS.lookupPath(path, { follow: !dontFollow });

        var node = lookup.node;

        if (!node) {

          throw new FS.ErrnoError(44);

        }

        if (!node.node_ops.getattr) {

          throw new FS.ErrnoError(63);

        }

        return node.node_ops.getattr(node);

      },lstat:function(path) {

        return FS.stat(path, true);

      },chmod:function(path, mode, dontFollow) {

        var node;

        if (typeof path === 'string') {

          var lookup = FS.lookupPath(path, { follow: !dontFollow });

          node = lookup.node;

        } else {

          node = path;

        }

        if (!node.node_ops.setattr) {

          throw new FS.ErrnoError(63);

        }

        node.node_ops.setattr(node, {

          mode: (mode & 4095) | (node.mode & ~4095),

          timestamp: Date.now()

        });

      },lchmod:function(path, mode) {

        FS.chmod(path, mode, true);

      },fchmod:function(fd, mode) {

        var stream = FS.getStream(fd);

        if (!stream) {

          throw new FS.ErrnoError(8);

        }

        FS.chmod(stream.node, mode);

      },chown:function(path, uid, gid, dontFollow) {

        var node;

        if (typeof path === 'string') {

          var lookup = FS.lookupPath(path, { follow: !dontFollow });

          node = lookup.node;

        } else {

          node = path;

        }

        if (!node.node_ops.setattr) {

          throw new FS.ErrnoError(63);

        }

        node.node_ops.setattr(node, {

          timestamp: Date.now()

          // we ignore the uid / gid for now

        });

      },lchown:function(path, uid, gid) {

        FS.chown(path, uid, gid, true);

      },fchown:function(fd, uid, gid) {

        var stream = FS.getStream(fd);

        if (!stream) {

          throw new FS.ErrnoError(8);

        }

        FS.chown(stream.node, uid, gid);

      },truncate:function(path, len) {

        if (len < 0) {

          throw new FS.ErrnoError(28);

        }

        var node;

        if (typeof path === 'string') {

          var lookup = FS.lookupPath(path, { follow: true });

          node = lookup.node;

        } else {

          node = path;

        }

        if (!node.node_ops.setattr) {

          throw new FS.ErrnoError(63);

        }

        if (FS.isDir(node.mode)) {

          throw new FS.ErrnoError(31);

        }

        if (!FS.isFile(node.mode)) {

          throw new FS.ErrnoError(28);

        }

        var errCode = FS.nodePermissions(node, 'w');

        if (errCode) {

          throw new FS.ErrnoError(errCode);

        }

        node.node_ops.setattr(node, {

          size: len,

          timestamp: Date.now()

        });

      },ftruncate:function(fd, len) {

        var stream = FS.getStream(fd);

        if (!stream) {

          throw new FS.ErrnoError(8);

        }

        if ((stream.flags & 2097155) === 0) {

          throw new FS.ErrnoError(28);

        }

        FS.truncate(stream.node, len);

      },utime:function(path, atime, mtime) {

        var lookup = FS.lookupPath(path, { follow: true });

        var node = lookup.node;

        node.node_ops.setattr(node, {

          timestamp: Math.max(atime, mtime)

        });

      },open:function(path, flags, mode, fd_start, fd_end) {

        if (path === "") {

          throw new FS.ErrnoError(44);

        }

        flags = typeof flags === 'string' ? FS.modeStringToFlags(flags) : flags;

        mode = typeof mode === 'undefined' ? 438 /* 0666 */ : mode;

        if ((flags & 64)) {

          mode = (mode & 4095) | 32768;

        } else {

          mode = 0;

        }

        var node;

        if (typeof path === 'object') {

          node = path;

        } else {

          path = PATH.normalize(path);

          try {

            var lookup = FS.lookupPath(path, {

              follow: !(flags & 131072)

            });

            node = lookup.node;

          } catch (e) {

            // ignore

          }

        }

        // perhaps we need to create the node

        var created = false;

        if ((flags & 64)) {

          if (node) {

            // if O_CREAT and O_EXCL are set, error out if the node already exists

            if ((flags & 128)) {

              throw new FS.ErrnoError(20);

            }

          } else {

            // node doesn't exist, try to create it

            node = FS.mknod(path, mode, 0);

            created = true;

          }

        }

        if (!node) {

          throw new FS.ErrnoError(44);

        }

        // can't truncate a device

        if (FS.isChrdev(node.mode)) {

          flags &= ~512;

        }

        // if asked only for a directory, then this must be one

        if ((flags & 65536) && !FS.isDir(node.mode)) {

          throw new FS.ErrnoError(54);

        }

        // check permissions, if this is not a file we just created now (it is ok to

        // create and write to a file with read-only permissions; it is read-only

        // for later use)

        if (!created) {

          var errCode = FS.mayOpen(node, flags);

          if (errCode) {

            throw new FS.ErrnoError(errCode);

          }

        }

        // do truncation if necessary

        if ((flags & 512)) {

          FS.truncate(node, 0);

        }

        // we've already handled these, don't pass down to the underlying vfs

        flags &= ~(128 | 512 | 131072);

  

        // register the stream with the filesystem

        var stream = FS.createStream({

          node: node,

          path: FS.getPath(node),  // we want the absolute path to the node

          id: node.id,

          flags: flags,

          mode: node.mode,

          seekable: true,

          position: 0,

          stream_ops: node.stream_ops,

          node_ops: node.node_ops,

          // used by the file family libc calls (fopen, fwrite, ferror, etc.)

          ungotten: [],

          error: false

        }, fd_start, fd_end);

        // call the new stream's open function

        if (stream.stream_ops.open) {

          stream.stream_ops.open(stream);

        }

        if (RoomsDivisionModule['logReadFiles'] && !(flags & 1)) {

          if (!FS.readFiles) FS.readFiles = {};

          if (!(path in FS.readFiles)) {

            FS.readFiles[path] = 1;

          }

        }

        return stream;

      },close:function(stream) {

        if (FS.isClosed(stream)) {

          throw new FS.ErrnoError(8);

        }

        if (stream.getdents) stream.getdents = null; // free readdir state

        try {

          if (stream.stream_ops.close) {

            stream.stream_ops.close(stream);

          }

        } catch (e) {

          throw e;

        } finally {

          FS.closeStream(stream.fd);

        }

        stream.fd = null;

      },isClosed:function(stream) {

        return stream.fd === null;

      },llseek:function(stream, offset, whence) {

        if (FS.isClosed(stream)) {

          throw new FS.ErrnoError(8);

        }

        if (!stream.seekable || !stream.stream_ops.llseek) {

          throw new FS.ErrnoError(70);

        }

        if (whence != 0 && whence != 1 && whence != 2) {

          throw new FS.ErrnoError(28);

        }

        stream.position = stream.stream_ops.llseek(stream, offset, whence);

        stream.ungotten = [];

        return stream.position;

      },read:function(stream, buffer, offset, length, position) {

        if (length < 0 || position < 0) {

          throw new FS.ErrnoError(28);

        }

        if (FS.isClosed(stream)) {

          throw new FS.ErrnoError(8);

        }

        if ((stream.flags & 2097155) === 1) {

          throw new FS.ErrnoError(8);

        }

        if (FS.isDir(stream.node.mode)) {

          throw new FS.ErrnoError(31);

        }

        if (!stream.stream_ops.read) {

          throw new FS.ErrnoError(28);

        }

        var seeking = typeof position !== 'undefined';

        if (!seeking) {

          position = stream.position;

        } else if (!stream.seekable) {

          throw new FS.ErrnoError(70);

        }

        var bytesRead = stream.stream_ops.read(stream, buffer, offset, length, position);

        if (!seeking) stream.position += bytesRead;

        return bytesRead;

      },write:function(stream, buffer, offset, length, position, canOwn) {

        if (length < 0 || position < 0) {

          throw new FS.ErrnoError(28);

        }

        if (FS.isClosed(stream)) {

          throw new FS.ErrnoError(8);

        }

        if ((stream.flags & 2097155) === 0) {

          throw new FS.ErrnoError(8);

        }

        if (FS.isDir(stream.node.mode)) {

          throw new FS.ErrnoError(31);

        }

        if (!stream.stream_ops.write) {

          throw new FS.ErrnoError(28);

        }

        if (stream.seekable && stream.flags & 1024) {

          // seek to the end before writing in append mode

          FS.llseek(stream, 0, 2);

        }

        var seeking = typeof position !== 'undefined';

        if (!seeking) {

          position = stream.position;

        } else if (!stream.seekable) {

          throw new FS.ErrnoError(70);

        }

        var bytesWritten = stream.stream_ops.write(stream, buffer, offset, length, position, canOwn);

        if (!seeking) stream.position += bytesWritten;

        return bytesWritten;

      },allocate:function(stream, offset, length) {

        if (FS.isClosed(stream)) {

          throw new FS.ErrnoError(8);

        }

        if (offset < 0 || length <= 0) {

          throw new FS.ErrnoError(28);

        }

        if ((stream.flags & 2097155) === 0) {

          throw new FS.ErrnoError(8);

        }

        if (!FS.isFile(stream.node.mode) && !FS.isDir(stream.node.mode)) {

          throw new FS.ErrnoError(43);

        }

        if (!stream.stream_ops.allocate) {

          throw new FS.ErrnoError(138);

        }

        stream.stream_ops.allocate(stream, offset, length);

      },mmap:function(stream, address, length, position, prot, flags) {

        // User requests writing to file (prot & PROT_WRITE != 0).

        // Checking if we have permissions to write to the file unless

        // MAP_PRIVATE flag is set. According to POSIX spec it is possible

        // to write to file opened in read-only mode with MAP_PRIVATE flag,

        // as all modifications will be visible only in the memory of

        // the current process.

        if ((prot & 2) !== 0

            && (flags & 2) === 0

            && (stream.flags & 2097155) !== 2) {

          throw new FS.ErrnoError(2);

        }

        if ((stream.flags & 2097155) === 1) {

          throw new FS.ErrnoError(2);

        }

        if (!stream.stream_ops.mmap) {

          throw new FS.ErrnoError(43);

        }

        return stream.stream_ops.mmap(stream, address, length, position, prot, flags);

      },msync:function(stream, buffer, offset, length, mmapFlags) {

        if (!stream || !stream.stream_ops.msync) {

          return 0;

        }

        return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags);

      },munmap:function(stream) {

        return 0;

      },ioctl:function(stream, cmd, arg) {

        if (!stream.stream_ops.ioctl) {

          throw new FS.ErrnoError(59);

        }

        return stream.stream_ops.ioctl(stream, cmd, arg);

      },readFile:function(path, opts) {

        opts = opts || {};

        opts.flags = opts.flags || 0;

        opts.encoding = opts.encoding || 'binary';

        if (opts.encoding !== 'utf8' && opts.encoding !== 'binary') {

          throw new Error('Invalid encoding type "' + opts.encoding + '"');

        }

        var ret;

        var stream = FS.open(path, opts.flags);

        var stat = FS.stat(path);

        var length = stat.size;

        var buf = new Uint8Array(length);

        FS.read(stream, buf, 0, length, 0);

        if (opts.encoding === 'utf8') {

          ret = UTF8ArrayToString(buf, 0);

        } else if (opts.encoding === 'binary') {

          ret = buf;

        }

        FS.close(stream);

        return ret;

      },writeFile:function(path, data, opts) {

        opts = opts || {};

        opts.flags = opts.flags || 577;

        var stream = FS.open(path, opts.flags, opts.mode);

        if (typeof data === 'string') {

          var buf = new Uint8Array(lengthBytesUTF8(data)+1);

          var actualNumBytes = stringToUTF8Array(data, buf, 0, buf.length);

          FS.write(stream, buf, 0, actualNumBytes, undefined, opts.canOwn);

        } else if (ArrayBuffer.isView(data)) {

          FS.write(stream, data, 0, data.byteLength, undefined, opts.canOwn);

        } else {

          throw new Error('Unsupported data type');

        }

        FS.close(stream);

      },cwd:function() {

        return FS.currentPath;

      },chdir:function(path) {

        var lookup = FS.lookupPath(path, { follow: true });

        if (lookup.node === null) {

          throw new FS.ErrnoError(44);

        }

        if (!FS.isDir(lookup.node.mode)) {

          throw new FS.ErrnoError(54);

        }

        var errCode = FS.nodePermissions(lookup.node, 'x');

        if (errCode) {

          throw new FS.ErrnoError(errCode);

        }

        FS.currentPath = lookup.path;

      },createDefaultDirectories:function() {

        FS.mkdir('/tmp');

        FS.mkdir('/home');

        FS.mkdir('/home/<USER>');

      },createDefaultDevices:function() {

        // create /dev

        FS.mkdir('/dev');

        // setup /dev/null

        FS.registerDevice(FS.makedev(1, 3), {

          read: function() { return 0; },

          write: function(stream, buffer, offset, length, pos) { return length; }

        });

        FS.mkdev('/dev/null', FS.makedev(1, 3));

        // setup /dev/tty and /dev/tty1

        // stderr needs to print output using err() rather than out()

        // so we register a second tty just for it.

        TTY.register(FS.makedev(5, 0), TTY.default_tty_ops);

        TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops);

        FS.mkdev('/dev/tty', FS.makedev(5, 0));

        FS.mkdev('/dev/tty1', FS.makedev(6, 0));

        // setup /dev/[u]random

        var random_device = getRandomDevice();

        FS.createDevice('/dev', 'random', random_device);

        FS.createDevice('/dev', 'urandom', random_device);

        // we're not going to emulate the actual shm device,

        // just create the tmp dirs that reside in it commonly

        FS.mkdir('/dev/shm');

        FS.mkdir('/dev/shm/tmp');

      },createSpecialDirectories:function() {

        // create /proc/self/fd which allows /proc/self/fd/6 => readlink gives the

        // name of the stream for fd 6 (see test_unistd_ttyname)

        FS.mkdir('/proc');

        var proc_self = FS.mkdir('/proc/self');

        FS.mkdir('/proc/self/fd');

        FS.mount({

          mount: function() {

            var node = FS.createNode(proc_self, 'fd', 16384 | 511 /* 0777 */, 73);

            node.node_ops = {

              lookup: function(parent, name) {

                var fd = +name;

                var stream = FS.getStream(fd);

                if (!stream) throw new FS.ErrnoError(8);

                var ret = {

                  parent: null,

                  mount: { mountpoint: 'fake' },

                  node_ops: { readlink: function() { return stream.path } }

                };

                ret.parent = ret; // make it look like a simple root node

                return ret;

              }

            };

            return node;

          }

        }, {}, '/proc/self/fd');

      },createStandardStreams:function() {

        // TODO deprecate the old functionality of a single

        // input / output callback and that utilizes FS.createDevice

        // and instead require a unique set of stream ops

  

        // by default, we symlink the standard streams to the

        // default tty devices. however, if the standard streams

        // have been overwritten we create a unique device for

        // them instead.

        if (RoomsDivisionModule['stdin']) {

          FS.createDevice('/dev', 'stdin', RoomsDivisionModule['stdin']);

        } else {

          FS.symlink('/dev/tty', '/dev/stdin');

        }

        if (RoomsDivisionModule['stdout']) {

          FS.createDevice('/dev', 'stdout', null, RoomsDivisionModule['stdout']);

        } else {

          FS.symlink('/dev/tty', '/dev/stdout');

        }

        if (RoomsDivisionModule['stderr']) {

          FS.createDevice('/dev', 'stderr', null, RoomsDivisionModule['stderr']);

        } else {

          FS.symlink('/dev/tty1', '/dev/stderr');

        }

  

        // open default streams for the stdin, stdout and stderr devices

        var stdin = FS.open('/dev/stdin', 0);

        var stdout = FS.open('/dev/stdout', 1);

        var stderr = FS.open('/dev/stderr', 1);

        assert(stdin.fd === 0, 'invalid handle for stdin (' + stdin.fd + ')');

        assert(stdout.fd === 1, 'invalid handle for stdout (' + stdout.fd + ')');

        assert(stderr.fd === 2, 'invalid handle for stderr (' + stderr.fd + ')');

      },ensureErrnoError:function() {

        if (FS.ErrnoError) return;

        FS.ErrnoError = /** @this{Object} */ function ErrnoError(errno, node) {

          this.node = node;

          this.setErrno = /** @this{Object} */ function(errno) {

            this.errno = errno;

            for (var key in ERRNO_CODES) {

              if (ERRNO_CODES[key] === errno) {

                this.code = key;

                break;

              }

            }

          };

          this.setErrno(errno);

          this.message = ERRNO_MESSAGES[errno];

  

          // Try to get a maximally helpful stack trace. On Node.js, getting Error.stack

          // now ensures it shows what we want.

          if (this.stack) {

            // Define the stack property for Node.js 4, which otherwise errors on the next line.

            Object.defineProperty(this, "stack", { value: (new Error).stack, writable: true });

            this.stack = demangleAll(this.stack);

          }

        };

        FS.ErrnoError.prototype = new Error();

        FS.ErrnoError.prototype.constructor = FS.ErrnoError;

        // Some errors may happen quite a bit, to avoid overhead we reuse them (and suffer a lack of stack info)

        [44].forEach(function(code) {

          FS.genericErrors[code] = new FS.ErrnoError(code);

          FS.genericErrors[code].stack = '<generic error, no stack>';

        });

      },staticInit:function() {

        FS.ensureErrnoError();

  

        FS.nameTable = new Array(4096);

  

        FS.mount(MEMFS, {}, '/');

  

        FS.createDefaultDirectories();

        FS.createDefaultDevices();

        FS.createSpecialDirectories();

  

        FS.filesystems = {

          'MEMFS': MEMFS,

        };

      },init:function(input, output, error) {

        assert(!FS.init.initialized, 'FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)');

        FS.init.initialized = true;

  

        FS.ensureErrnoError();

  

        // Allow RoomsDivisionModule.stdin etc. to provide defaults, if none explicitly passed to us here

        RoomsDivisionModule['stdin'] = input || RoomsDivisionModule['stdin'];

        RoomsDivisionModule['stdout'] = output || RoomsDivisionModule['stdout'];

        RoomsDivisionModule['stderr'] = error || RoomsDivisionModule['stderr'];

  

        FS.createStandardStreams();

      },quit:function() {

        FS.init.initialized = false;

        // force-flush all streams, so we get musl std streams printed out

        var fflush = RoomsDivisionModule['_fflush'];

        if (fflush) fflush(0);

        // close all of our streams

        for (var i = 0; i < FS.streams.length; i++) {

          var stream = FS.streams[i];

          if (!stream) {

            continue;

          }

          FS.close(stream);

        }

      },getMode:function(canRead, canWrite) {

        var mode = 0;

        if (canRead) mode |= 292 | 73;

        if (canWrite) mode |= 146;

        return mode;

      },findObject:function(path, dontResolveLastLink) {

        var ret = FS.analyzePath(path, dontResolveLastLink);

        if (ret.exists) {

          return ret.object;

        } else {

          return null;

        }

      },analyzePath:function(path, dontResolveLastLink) {

        // operate from within the context of the symlink's target

        try {

          var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });

          path = lookup.path;

        } catch (e) {

        }

        var ret = {

          isRoot: false, exists: false, error: 0, name: null, path: null, object: null,

          parentExists: false, parentPath: null, parentObject: null

        };

        try {

          var lookup = FS.lookupPath(path, { parent: true });

          ret.parentExists = true;

          ret.parentPath = lookup.path;

          ret.parentObject = lookup.node;

          ret.name = PATH.basename(path);

          lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });

          ret.exists = true;

          ret.path = lookup.path;

          ret.object = lookup.node;

          ret.name = lookup.node.name;

          ret.isRoot = lookup.path === '/';

        } catch (e) {

          ret.error = e.errno;

        };

        return ret;

      },createPath:function(parent, path, canRead, canWrite) {

        parent = typeof parent === 'string' ? parent : FS.getPath(parent);

        var parts = path.split('/').reverse();

        while (parts.length) {

          var part = parts.pop();

          if (!part) continue;

          var current = PATH.join2(parent, part);

          try {

            FS.mkdir(current);

          } catch (e) {

            // ignore EEXIST

          }

          parent = current;

        }

        return current;

      },createFile:function(parent, name, properties, canRead, canWrite) {

        var path = PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name);

        var mode = FS.getMode(canRead, canWrite);

        return FS.create(path, mode);

      },createDataFile:function(parent, name, data, canRead, canWrite, canOwn) {

        var path = name ? PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name) : parent;

        var mode = FS.getMode(canRead, canWrite);

        var node = FS.create(path, mode);

        if (data) {

          if (typeof data === 'string') {

            var arr = new Array(data.length);

            for (var i = 0, len = data.length; i < len; ++i) arr[i] = data.charCodeAt(i);

            data = arr;

          }

          // make sure we can write to the file

          FS.chmod(node, mode | 146);

          var stream = FS.open(node, 577);

          FS.write(stream, data, 0, data.length, 0, canOwn);

          FS.close(stream);

          FS.chmod(node, mode);

        }

        return node;

      },createDevice:function(parent, name, input, output) {

        var path = PATH.join2(typeof parent === 'string' ? parent : FS.getPath(parent), name);

        var mode = FS.getMode(!!input, !!output);

        if (!FS.createDevice.major) FS.createDevice.major = 64;

        var dev = FS.makedev(FS.createDevice.major++, 0);

        // Create a fake device that a set of stream ops to emulate

        // the old behavior.

        FS.registerDevice(dev, {

          open: function(stream) {

            stream.seekable = false;

          },

          close: function(stream) {

            // flush any pending line data

            if (output && output.buffer && output.buffer.length) {

              output(10);

            }

          },

          read: function(stream, buffer, offset, length, pos /* ignored */) {

            var bytesRead = 0;

            for (var i = 0; i < length; i++) {

              var result;

              try {

                result = input();

              } catch (e) {

                throw new FS.ErrnoError(29);

              }

              if (result === undefined && bytesRead === 0) {

                throw new FS.ErrnoError(6);

              }

              if (result === null || result === undefined) break;

              bytesRead++;

              buffer[offset+i] = result;

            }

            if (bytesRead) {

              stream.node.timestamp = Date.now();

            }

            return bytesRead;

          },

          write: function(stream, buffer, offset, length, pos) {

            for (var i = 0; i < length; i++) {

              try {

                output(buffer[offset+i]);

              } catch (e) {

                throw new FS.ErrnoError(29);

              }

            }

            if (length) {

              stream.node.timestamp = Date.now();

            }

            return i;

          }

        });

        return FS.mkdev(path, mode, dev);

      },forceLoadFile:function(obj) {

        if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true;

        if (typeof XMLHttpRequest !== 'undefined') {

          throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");

        } else if (read_) {

          // Command-line.

          try {

            // WARNING: Can't read binary files in V8's d8 or tracemonkey's js, as

            //          read() will try to parse UTF8.

            obj.contents = intArrayFromString(read_(obj.url), true);

            obj.usedBytes = obj.contents.length;

          } catch (e) {

            throw new FS.ErrnoError(29);

          }

        } else {

          throw new Error('Cannot load without read() or XMLHttpRequest.');

        }

      },createLazyFile:function(parent, name, url, canRead, canWrite) {

        // Lazy chunked Uint8Array (implements get and length from Uint8Array). Actual getting is abstracted away for eventual reuse.

        /** @constructor */

        function LazyUint8Array() {

          this.lengthKnown = false;

          this.chunks = []; // Loaded chunks. Index is the chunk number

        }

        LazyUint8Array.prototype.get = /** @this{Object} */ function LazyUint8Array_get(idx) {

          if (idx > this.length-1 || idx < 0) {

            return undefined;

          }

          var chunkOffset = idx % this.chunkSize;

          var chunkNum = (idx / this.chunkSize)|0;

          return this.getter(chunkNum)[chunkOffset];

        };

        LazyUint8Array.prototype.setDataGetter = function LazyUint8Array_setDataGetter(getter) {

          this.getter = getter;

        };

        LazyUint8Array.prototype.cacheLength = function LazyUint8Array_cacheLength() {

          // Find length

          var xhr = new XMLHttpRequest();

          xhr.open('HEAD', url, false);

          xhr.send(null);

          if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);

          var datalength = Number(xhr.getResponseHeader("Content-length"));

          var header;

          var hasByteServing = (header = xhr.getResponseHeader("Accept-Ranges")) && header === "bytes";

          var usesGzip = (header = xhr.getResponseHeader("Content-Encoding")) && header === "gzip";

  

          var chunkSize = 1024*1024; // Chunk size in bytes

  

          if (!hasByteServing) chunkSize = datalength;

  

          // Function to get a range from the remote URL.

          var doXHR = (function(from, to) {

            if (from > to) throw new Error("invalid range (" + from + ", " + to + ") or no bytes requested!");

            if (to > datalength-1) throw new Error("only " + datalength + " bytes available! programmer error!");

  

            // TODO: Use mozResponseArrayBuffer, responseStream, etc. if available.

            var xhr = new XMLHttpRequest();

            xhr.open('GET', url, false);

            if (datalength !== chunkSize) xhr.setRequestHeader("Range", "bytes=" + from + "-" + to);

  

            // Some hints to the browser that we want binary data.

            if (typeof Uint8Array != 'undefined') xhr.responseType = 'arraybuffer';

            if (xhr.overrideMimeType) {

              xhr.overrideMimeType('text/plain; charset=x-user-defined');

            }

  

            xhr.send(null);

            if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);

            if (xhr.response !== undefined) {

              return new Uint8Array(/** @type{Array<number>} */(xhr.response || []));

            } else {

              return intArrayFromString(xhr.responseText || '', true);

            }

          });

          var lazyArray = this;

          lazyArray.setDataGetter(function(chunkNum) {

            var start = chunkNum * chunkSize;

            var end = (chunkNum+1) * chunkSize - 1; // including this byte

            end = Math.min(end, datalength-1); // if datalength-1 is selected, this is the last block

            if (typeof(lazyArray.chunks[chunkNum]) === "undefined") {

              lazyArray.chunks[chunkNum] = doXHR(start, end);

            }

            if (typeof(lazyArray.chunks[chunkNum]) === "undefined") throw new Error("doXHR failed!");

            return lazyArray.chunks[chunkNum];

          });

  

          if (usesGzip || !datalength) {

            // if the server uses gzip or doesn't supply the length, we have to download the whole file to get the (uncompressed) length

            chunkSize = datalength = 1; // this will force getter(0)/doXHR do download the whole file

            datalength = this.getter(0).length;

            chunkSize = datalength;

            out("LazyFiles on gzip forces download of the whole file when length is accessed");

          }

  

          this._length = datalength;

          this._chunkSize = chunkSize;

          this.lengthKnown = true;

        };

        if (typeof XMLHttpRequest !== 'undefined') {

          if (!ENVIRONMENT_IS_WORKER) throw 'Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc';

          var lazyArray = new LazyUint8Array();

          Object.defineProperties(lazyArray, {

            length: {

              get: /** @this{Object} */ function() {

                if (!this.lengthKnown) {

                  this.cacheLength();

                }

                return this._length;

              }

            },

            chunkSize: {

              get: /** @this{Object} */ function() {

                if (!this.lengthKnown) {

                  this.cacheLength();

                }

                return this._chunkSize;

              }

            }

          });

  

          var properties = { isDevice: false, contents: lazyArray };

        } else {

          var properties = { isDevice: false, url: url };

        }

  

        var node = FS.createFile(parent, name, properties, canRead, canWrite);

        // This is a total hack, but I want to get this lazy file code out of the

        // core of MEMFS. If we want to keep this lazy file concept I feel it should

        // be its own thin LAZYFS proxying calls to MEMFS.

        if (properties.contents) {

          node.contents = properties.contents;

        } else if (properties.url) {

          node.contents = null;

          node.url = properties.url;

        }

        // Add a function that defers querying the file size until it is asked the first time.

        Object.defineProperties(node, {

          usedBytes: {

            get: /** @this {FSNode} */ function() { return this.contents.length; }

          }

        });

        // override each stream op with one that tries to force load the lazy file first

        var stream_ops = {};

        var keys = Object.keys(node.stream_ops);

        keys.forEach(function(key) {

          var fn = node.stream_ops[key];

          stream_ops[key] = function forceLoadLazyFile() {

            FS.forceLoadFile(node);

            return fn.apply(null, arguments);

          };

        });

        // use a custom read function

        stream_ops.read = function stream_ops_read(stream, buffer, offset, length, position) {

          FS.forceLoadFile(node);

          var contents = stream.node.contents;

          if (position >= contents.length)

            return 0;

          var size = Math.min(contents.length - position, length);

          assert(size >= 0);

          if (contents.slice) { // normal array

            for (var i = 0; i < size; i++) {

              buffer[offset + i] = contents[position + i];

            }

          } else {

            for (var i = 0; i < size; i++) { // LazyUint8Array from sync binary XHR

              buffer[offset + i] = contents.get(position + i);

            }

          }

          return size;

        };

        node.stream_ops = stream_ops;

        return node;

      },createPreloadedFile:function(parent, name, url, canRead, canWrite, onload, onerror, dontCreateFile, canOwn, preFinish) {

        Browser.init(); // XXX perhaps this method should move onto Browser?

        // TODO we should allow people to just pass in a complete filename instead

        // of parent and name being that we just join them anyways

        var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent;

        var dep = getUniqueRunDependency('cp ' + fullname); // might have several active requests for the same fullname

        function processData(byteArray) {

          function finish(byteArray) {

            if (preFinish) preFinish();

            if (!dontCreateFile) {

              FS.createDataFile(parent, name, byteArray, canRead, canWrite, canOwn);

            }

            if (onload) onload();

            removeRunDependency(dep);

          }

          var handled = false;

          RoomsDivisionModule['preloadPlugins'].forEach(function(plugin) {

            if (handled) return;

            if (plugin['canHandle'](fullname)) {

              plugin['handle'](byteArray, fullname, finish, function() {

                if (onerror) onerror();

                removeRunDependency(dep);

              });

              handled = true;

            }

          });

          if (!handled) finish(byteArray);

        }

        addRunDependency(dep);

        if (typeof url == 'string') {

          asyncLoad(url, function(byteArray) {

            processData(byteArray);

          }, onerror);

        } else {

          processData(url);

        }

      },indexedDB:function() {

        return window.indexedDB || window.mozIndexedDB || window.webkitIndexedDB || window.msIndexedDB;

      },DB_NAME:function() {

        return 'EM_FS_' + window.location.pathname;

      },DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(paths, onload, onerror) {

        onload = onload || function(){};

        onerror = onerror || function(){};

        var indexedDB = FS.indexedDB();

        try {

          var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);

        } catch (e) {

          return onerror(e);

        }

        openRequest.onupgradeneeded = function openRequest_onupgradeneeded() {

          out('creating db');

          var db = openRequest.result;

          db.createObjectStore(FS.DB_STORE_NAME);

        };

        openRequest.onsuccess = function openRequest_onsuccess() {

          var db = openRequest.result;

          var transaction = db.transaction([FS.DB_STORE_NAME], 'readwrite');

          var files = transaction.objectStore(FS.DB_STORE_NAME);

          var ok = 0, fail = 0, total = paths.length;

          function finish() {

            if (fail == 0) onload(); else onerror();

          }

          paths.forEach(function(path) {

            var putRequest = files.put(FS.analyzePath(path).object.contents, path);

            putRequest.onsuccess = function putRequest_onsuccess() { ok++; if (ok + fail == total) finish() };

            putRequest.onerror = function putRequest_onerror() { fail++; if (ok + fail == total) finish() };

          });

          transaction.onerror = onerror;

        };

        openRequest.onerror = onerror;

      },loadFilesFromDB:function(paths, onload, onerror) {

        onload = onload || function(){};

        onerror = onerror || function(){};

        var indexedDB = FS.indexedDB();

        try {

          var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);

        } catch (e) {

          return onerror(e);

        }

        openRequest.onupgradeneeded = onerror; // no database to load from

        openRequest.onsuccess = function openRequest_onsuccess() {

          var db = openRequest.result;

          try {

            var transaction = db.transaction([FS.DB_STORE_NAME], 'readonly');

          } catch(e) {

            onerror(e);

            return;

          }

          var files = transaction.objectStore(FS.DB_STORE_NAME);

          var ok = 0, fail = 0, total = paths.length;

          function finish() {

            if (fail == 0) onload(); else onerror();

          }

          paths.forEach(function(path) {

            var getRequest = files.get(path);

            getRequest.onsuccess = function getRequest_onsuccess() {

              if (FS.analyzePath(path).exists) {

                FS.unlink(path);

              }

              FS.createDataFile(PATH.dirname(path), PATH.basename(path), getRequest.result, true, true, true);

              ok++;

              if (ok + fail == total) finish();

            };

            getRequest.onerror = function getRequest_onerror() { fail++; if (ok + fail == total) finish() };

          });

          transaction.onerror = onerror;

        };

        openRequest.onerror = onerror;

      },absolutePath:function() {

        abort('FS.absolutePath has been removed; use PATH_FS.resolve instead');

      },createFolder:function() {

        abort('FS.createFolder has been removed; use FS.mkdir instead');

      },createLink:function() {

        abort('FS.createLink has been removed; use FS.symlink instead');

      },joinPath:function() {

        abort('FS.joinPath has been removed; use PATH.join instead');

      },mmapAlloc:function() {

        abort('FS.mmapAlloc has been replaced by the top level function mmapAlloc');

      },standardizePath:function() {

        abort('FS.standardizePath has been removed; use PATH.normalize instead');

      }};

  var SYSCALLS = {mappings:{},DEFAULT_POLLMASK:5,calculateAt:function(dirfd, path, allowEmpty) {

        if (path[0] === '/') {

          return path;

        }

        // relative path

        var dir;

        if (dirfd === -100) {

          dir = FS.cwd();

        } else {

          var dirstream = FS.getStream(dirfd);

          if (!dirstream) throw new FS.ErrnoError(8);

          dir = dirstream.path;

        }

        if (path.length == 0) {

          if (!allowEmpty) {

            throw new FS.ErrnoError(44);;

          }

          return dir;

        }

        return PATH.join2(dir, path);

      },doStat:function(func, path, buf) {

        try {

          var stat = func(path);

        } catch (e) {

          if (e && e.node && PATH.normalize(path) !== PATH.normalize(FS.getPath(e.node))) {

            // an error occurred while trying to look up the path; we should just report ENOTDIR

            return -54;

          }

          throw e;

        }

        HEAP32[((buf)>>2)] = stat.dev;

        HEAP32[(((buf)+(4))>>2)] = 0;

        HEAP32[(((buf)+(8))>>2)] = stat.ino;

        HEAP32[(((buf)+(12))>>2)] = stat.mode;

        HEAP32[(((buf)+(16))>>2)] = stat.nlink;

        HEAP32[(((buf)+(20))>>2)] = stat.uid;

        HEAP32[(((buf)+(24))>>2)] = stat.gid;

        HEAP32[(((buf)+(28))>>2)] = stat.rdev;

        HEAP32[(((buf)+(32))>>2)] = 0;

        (tempI64 = [stat.size>>>0,(tempDouble=stat.size,(+(Math.abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math.min((+(Math.floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math.ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[(((buf)+(40))>>2)] = tempI64[0],HEAP32[(((buf)+(44))>>2)] = tempI64[1]);

        HEAP32[(((buf)+(48))>>2)] = 4096;

        HEAP32[(((buf)+(52))>>2)] = stat.blocks;

        HEAP32[(((buf)+(56))>>2)] = (stat.atime.getTime() / 1000)|0;

        HEAP32[(((buf)+(60))>>2)] = 0;

        HEAP32[(((buf)+(64))>>2)] = (stat.mtime.getTime() / 1000)|0;

        HEAP32[(((buf)+(68))>>2)] = 0;

        HEAP32[(((buf)+(72))>>2)] = (stat.ctime.getTime() / 1000)|0;

        HEAP32[(((buf)+(76))>>2)] = 0;

        (tempI64 = [stat.ino>>>0,(tempDouble=stat.ino,(+(Math.abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math.min((+(Math.floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math.ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[(((buf)+(80))>>2)] = tempI64[0],HEAP32[(((buf)+(84))>>2)] = tempI64[1]);

        return 0;

      },doMsync:function(addr, stream, len, flags, offset) {

        var buffer = HEAPU8.slice(addr, addr + len);

        FS.msync(stream, buffer, offset, len, flags);

      },doMkdir:function(path, mode) {

        // remove a trailing slash, if one - /a/b/ has basename of '', but

        // we want to create b in the context of this function

        path = PATH.normalize(path);

        if (path[path.length-1] === '/') path = path.substr(0, path.length-1);

        FS.mkdir(path, mode, 0);

        return 0;

      },doMknod:function(path, mode, dev) {

        // we don't want this in the JS API as it uses mknod to create all nodes.

        switch (mode & 61440) {

          case 32768:

          case 8192:

          case 24576:

          case 4096:

          case 49152:

            break;

          default: return -28;

        }

        FS.mknod(path, mode, dev);

        return 0;

      },doReadlink:function(path, buf, bufsize) {

        if (bufsize <= 0) return -28;

        var ret = FS.readlink(path);

  

        var len = Math.min(bufsize, lengthBytesUTF8(ret));

        var endChar = HEAP8[buf+len];

        stringToUTF8(ret, buf, bufsize+1);

        // readlink is one of the rare functions that write out a C string, but does never append a null to the output buffer(!)

        // stringToUTF8() always appends a null byte, so restore the character under the null byte after the write.

        HEAP8[buf+len] = endChar;

  

        return len;

      },doAccess:function(path, amode) {

        if (amode & ~7) {

          // need a valid mode

          return -28;

        }

        var lookup = FS.lookupPath(path, { follow: true });

        var node = lookup.node;

        if (!node) {

          return -44;

        }

        var perms = '';

        if (amode & 4) perms += 'r';

        if (amode & 2) perms += 'w';

        if (amode & 1) perms += 'x';

        if (perms /* otherwise, they've just passed F_OK */ && FS.nodePermissions(node, perms)) {

          return -2;

        }

        return 0;

      },doDup:function(path, flags, suggestFD) {

        var suggest = FS.getStream(suggestFD);

        if (suggest) FS.close(suggest);

        return FS.open(path, flags, 0, suggestFD, suggestFD).fd;

      },doReadv:function(stream, iov, iovcnt, offset) {

        var ret = 0;

        for (var i = 0; i < iovcnt; i++) {

          var ptr = HEAP32[(((iov)+(i*8))>>2)];

          var len = HEAP32[(((iov)+(i*8 + 4))>>2)];

          var curr = FS.read(stream, HEAP8,ptr, len, offset);

          if (curr < 0) return -1;

          ret += curr;

          if (curr < len) break; // nothing more to read

        }

        return ret;

      },doWritev:function(stream, iov, iovcnt, offset) {

        var ret = 0;

        for (var i = 0; i < iovcnt; i++) {

          var ptr = HEAP32[(((iov)+(i*8))>>2)];

          var len = HEAP32[(((iov)+(i*8 + 4))>>2)];

          var curr = FS.write(stream, HEAP8,ptr, len, offset);

          if (curr < 0) return -1;

          ret += curr;

        }

        return ret;

      },varargs:undefined,get:function() {

        assert(SYSCALLS.varargs != undefined);

        SYSCALLS.varargs += 4;

        var ret = HEAP32[(((SYSCALLS.varargs)-(4))>>2)];

        return ret;

      },getStr:function(ptr) {

        var ret = UTF8ToString(ptr);

        return ret;

      },getStreamFromFD:function(fd) {

        var stream = FS.getStream(fd);

        if (!stream) throw new FS.ErrnoError(8);

        return stream;

      },get64:function(low, high) {

        if (low >= 0) assert(high === 0);

        else assert(high === -1);

        return low;

      }};

  function _environ_get(__environ, environ_buf) {

      var bufSize = 0;

      getEnvStrings().forEach(function(string, i) {

        var ptr = environ_buf + bufSize;

        HEAP32[(((__environ)+(i * 4))>>2)] = ptr;

        writeAsciiToMemory(string, ptr);

        bufSize += string.length + 1;

      });

      return 0;

    }



  function _environ_sizes_get(penviron_count, penviron_buf_size) {

      var strings = getEnvStrings();

      HEAP32[((penviron_count)>>2)] = strings.length;

      var bufSize = 0;

      strings.forEach(function(string) {

        bufSize += string.length + 1;

      });

      HEAP32[((penviron_buf_size)>>2)] = bufSize;

      return 0;

    }



  function _fd_close(fd) {try {

  

      var stream = SYSCALLS.getStreamFromFD(fd);

      FS.close(stream);

      return 0;

    } catch (e) {

    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) throw e;

    return e.errno;

  }

  }



  function _fd_read(fd, iov, iovcnt, pnum) {try {

  

      var stream = SYSCALLS.getStreamFromFD(fd);

      var num = SYSCALLS.doReadv(stream, iov, iovcnt);

      HEAP32[((pnum)>>2)] = num;

      return 0;

    } catch (e) {

    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) throw e;

    return e.errno;

  }

  }



  function _fd_seek(fd, offset_low, offset_high, whence, newOffset) {try {

  

      

      var stream = SYSCALLS.getStreamFromFD(fd);

      var HIGH_OFFSET = 0x100000000; // 2^32

      // use an unsigned operator on low and shift high by 32-bits

      var offset = offset_high * HIGH_OFFSET + (offset_low >>> 0);

  

      var DOUBLE_LIMIT = 0x20000000000000; // 2^53

      // we also check for equality since DOUBLE_LIMIT + 1 == DOUBLE_LIMIT

      if (offset <= -DOUBLE_LIMIT || offset >= DOUBLE_LIMIT) {

        return -61;

      }

  

      FS.llseek(stream, offset, whence);

      (tempI64 = [stream.position>>>0,(tempDouble=stream.position,(+(Math.abs(tempDouble))) >= 1.0 ? (tempDouble > 0.0 ? ((Math.min((+(Math.floor((tempDouble)/4294967296.0))), 4294967295.0))|0)>>>0 : (~~((+(Math.ceil((tempDouble - +(((~~(tempDouble)))>>>0))/4294967296.0)))))>>>0) : 0)],HEAP32[((newOffset)>>2)] = tempI64[0],HEAP32[(((newOffset)+(4))>>2)] = tempI64[1]);

      if (stream.getdents && offset === 0 && whence === 0) stream.getdents = null; // reset readdir state

      return 0;

    } catch (e) {

    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) throw e;

    return e.errno;

  }

  }



  function _fd_write(fd, iov, iovcnt, pnum) {try {

  

      ;

      var stream = SYSCALLS.getStreamFromFD(fd);

      var num = SYSCALLS.doWritev(stream, iov, iovcnt);

      HEAP32[((pnum)>>2)] = num;

      return 0;

    } catch (e) {

    if (typeof FS === 'undefined' || !(e instanceof FS.ErrnoError)) throw e;

    return e.errno;

  }

  }



  function _getentropy(buffer, size) {

      if (!_getentropy.randomDevice) {

        _getentropy.randomDevice = getRandomDevice();

      }

      for (var i = 0; i < size; i++) {

        HEAP8[(((buffer)+(i))>>0)] = _getentropy.randomDevice();

      }

      return 0;

    }



  function _setTempRet0(val) {

      setTempRet0(val);

    }



  function __isLeapYear(year) {

        return year%4 === 0 && (year%100 !== 0 || year%400 === 0);

    }

  

  function __arraySum(array, index) {

      var sum = 0;

      for (var i = 0; i <= index; sum += array[i++]) {

        // no-op

      }

      return sum;

    }

  

  var __MONTH_DAYS_LEAP = [31,29,31,30,31,30,31,31,30,31,30,31];

  

  var __MONTH_DAYS_REGULAR = [31,28,31,30,31,30,31,31,30,31,30,31];

  function __addDays(date, days) {

      var newDate = new Date(date.getTime());

      while (days > 0) {

        var leap = __isLeapYear(newDate.getFullYear());

        var currentMonth = newDate.getMonth();

        var daysInCurrentMonth = (leap ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR)[currentMonth];

  

        if (days > daysInCurrentMonth-newDate.getDate()) {

          // we spill over to next month

          days -= (daysInCurrentMonth-newDate.getDate()+1);

          newDate.setDate(1);

          if (currentMonth < 11) {

            newDate.setMonth(currentMonth+1)

          } else {

            newDate.setMonth(0);

            newDate.setFullYear(newDate.getFullYear()+1);

          }

        } else {

          // we stay in current month

          newDate.setDate(newDate.getDate()+days);

          return newDate;

        }

      }

  

      return newDate;

    }

  function _strftime(s, maxsize, format, tm) {

      // size_t strftime(char *restrict s, size_t maxsize, const char *restrict format, const struct tm *restrict timeptr);

      // http://pubs.opengroup.org/onlinepubs/009695399/functions/strftime.html

  

      var tm_zone = HEAP32[(((tm)+(40))>>2)];

  

      var date = {

        tm_sec: HEAP32[((tm)>>2)],

        tm_min: HEAP32[(((tm)+(4))>>2)],

        tm_hour: HEAP32[(((tm)+(8))>>2)],

        tm_mday: HEAP32[(((tm)+(12))>>2)],

        tm_mon: HEAP32[(((tm)+(16))>>2)],

        tm_year: HEAP32[(((tm)+(20))>>2)],

        tm_wday: HEAP32[(((tm)+(24))>>2)],

        tm_yday: HEAP32[(((tm)+(28))>>2)],

        tm_isdst: HEAP32[(((tm)+(32))>>2)],

        tm_gmtoff: HEAP32[(((tm)+(36))>>2)],

        tm_zone: tm_zone ? UTF8ToString(tm_zone) : ''

      };

  

      var pattern = UTF8ToString(format);

  

      // expand format

      var EXPANSION_RULES_1 = {

        '%c': '%a %b %d %H:%M:%S %Y',     // Replaced by the locale's appropriate date and time representation - e.g., Mon Aug  3 14:02:01 2013

        '%D': '%m/%d/%y',                 // Equivalent to %m / %d / %y

        '%F': '%Y-%m-%d',                 // Equivalent to %Y - %m - %d

        '%h': '%b',                       // Equivalent to %b

        '%r': '%I:%M:%S %p',              // Replaced by the time in a.m. and p.m. notation

        '%R': '%H:%M',                    // Replaced by the time in 24-hour notation

        '%T': '%H:%M:%S',                 // Replaced by the time

        '%x': '%m/%d/%y',                 // Replaced by the locale's appropriate date representation

        '%X': '%H:%M:%S',                 // Replaced by the locale's appropriate time representation

        // Modified Conversion Specifiers

        '%Ec': '%c',                      // Replaced by the locale's alternative appropriate date and time representation.

        '%EC': '%C',                      // Replaced by the name of the base year (period) in the locale's alternative representation.

        '%Ex': '%m/%d/%y',                // Replaced by the locale's alternative date representation.

        '%EX': '%H:%M:%S',                // Replaced by the locale's alternative time representation.

        '%Ey': '%y',                      // Replaced by the offset from %EC (year only) in the locale's alternative representation.

        '%EY': '%Y',                      // Replaced by the full alternative year representation.

        '%Od': '%d',                      // Replaced by the day of the month, using the locale's alternative numeric symbols, filled as needed with leading zeros if there is any alternative symbol for zero; otherwise, with leading <space> characters.

        '%Oe': '%e',                      // Replaced by the day of the month, using the locale's alternative numeric symbols, filled as needed with leading <space> characters.

        '%OH': '%H',                      // Replaced by the hour (24-hour clock) using the locale's alternative numeric symbols.

        '%OI': '%I',                      // Replaced by the hour (12-hour clock) using the locale's alternative numeric symbols.

        '%Om': '%m',                      // Replaced by the month using the locale's alternative numeric symbols.

        '%OM': '%M',                      // Replaced by the minutes using the locale's alternative numeric symbols.

        '%OS': '%S',                      // Replaced by the seconds using the locale's alternative numeric symbols.

        '%Ou': '%u',                      // Replaced by the weekday as a number in the locale's alternative representation (Monday=1).

        '%OU': '%U',                      // Replaced by the week number of the year (Sunday as the first day of the week, rules corresponding to %U ) using the locale's alternative numeric symbols.

        '%OV': '%V',                      // Replaced by the week number of the year (Monday as the first day of the week, rules corresponding to %V ) using the locale's alternative numeric symbols.

        '%Ow': '%w',                      // Replaced by the number of the weekday (Sunday=0) using the locale's alternative numeric symbols.

        '%OW': '%W',                      // Replaced by the week number of the year (Monday as the first day of the week) using the locale's alternative numeric symbols.

        '%Oy': '%y',                      // Replaced by the year (offset from %C ) using the locale's alternative numeric symbols.

      };

      for (var rule in EXPANSION_RULES_1) {

        pattern = pattern.replace(new RegExp(rule, 'g'), EXPANSION_RULES_1[rule]);

      }

  

      var WEEKDAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

      var MONTHS = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

  

      function leadingSomething(value, digits, character) {

        var str = typeof value === 'number' ? value.toString() : (value || '');

        while (str.length < digits) {

          str = character[0]+str;

        }

        return str;

      }

  

      function leadingNulls(value, digits) {

        return leadingSomething(value, digits, '0');

      }

  

      function compareByDay(date1, date2) {

        function sgn(value) {

          return value < 0 ? -1 : (value > 0 ? 1 : 0);

        }

  

        var compare;

        if ((compare = sgn(date1.getFullYear()-date2.getFullYear())) === 0) {

          if ((compare = sgn(date1.getMonth()-date2.getMonth())) === 0) {

            compare = sgn(date1.getDate()-date2.getDate());

          }

        }

        return compare;

      }

  

      function getFirstWeekStartDate(janFourth) {

          switch (janFourth.getDay()) {

            case 0: // Sunday

              return new Date(janFourth.getFullYear()-1, 11, 29);

            case 1: // Monday

              return janFourth;

            case 2: // Tuesday

              return new Date(janFourth.getFullYear(), 0, 3);

            case 3: // Wednesday

              return new Date(janFourth.getFullYear(), 0, 2);

            case 4: // Thursday

              return new Date(janFourth.getFullYear(), 0, 1);

            case 5: // Friday

              return new Date(janFourth.getFullYear()-1, 11, 31);

            case 6: // Saturday

              return new Date(janFourth.getFullYear()-1, 11, 30);

          }

      }

  

      function getWeekBasedYear(date) {

          var thisDate = __addDays(new Date(date.tm_year+1900, 0, 1), date.tm_yday);

  

          var janFourthThisYear = new Date(thisDate.getFullYear(), 0, 4);

          var janFourthNextYear = new Date(thisDate.getFullYear()+1, 0, 4);

  

          var firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear);

          var firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear);

  

          if (compareByDay(firstWeekStartThisYear, thisDate) <= 0) {

            // this date is after the start of the first week of this year

            if (compareByDay(firstWeekStartNextYear, thisDate) <= 0) {

              return thisDate.getFullYear()+1;

            } else {

              return thisDate.getFullYear();

            }

          } else {

            return thisDate.getFullYear()-1;

          }

      }

  

      var EXPANSION_RULES_2 = {

        '%a': function(date) {

          return WEEKDAYS[date.tm_wday].substring(0,3);

        },

        '%A': function(date) {

          return WEEKDAYS[date.tm_wday];

        },

        '%b': function(date) {

          return MONTHS[date.tm_mon].substring(0,3);

        },

        '%B': function(date) {

          return MONTHS[date.tm_mon];

        },

        '%C': function(date) {

          var year = date.tm_year+1900;

          return leadingNulls((year/100)|0,2);

        },

        '%d': function(date) {

          return leadingNulls(date.tm_mday, 2);

        },

        '%e': function(date) {

          return leadingSomething(date.tm_mday, 2, ' ');

        },

        '%g': function(date) {

          // %g, %G, and %V give values according to the ISO 8601:2000 standard week-based year.

          // In this system, weeks begin on a Monday and week 1 of the year is the week that includes

          // January 4th, which is also the week that includes the first Thursday of the year, and

          // is also the first week that contains at least four days in the year.

          // If the first Monday of January is the 2nd, 3rd, or 4th, the preceding days are part of

          // the last week of the preceding year; thus, for Saturday 2nd January 1999,

          // %G is replaced by 1998 and %V is replaced by 53. If December 29th, 30th,

          // or 31st is a Monday, it and any following days are part of week 1 of the following year.

          // Thus, for Tuesday 30th December 1997, %G is replaced by 1998 and %V is replaced by 01.

  

          return getWeekBasedYear(date).toString().substring(2);

        },

        '%G': function(date) {

          return getWeekBasedYear(date);

        },

        '%H': function(date) {

          return leadingNulls(date.tm_hour, 2);

        },

        '%I': function(date) {

          var twelveHour = date.tm_hour;

          if (twelveHour == 0) twelveHour = 12;

          else if (twelveHour > 12) twelveHour -= 12;

          return leadingNulls(twelveHour, 2);

        },

        '%j': function(date) {

          // Day of the year (001-366)

          return leadingNulls(date.tm_mday+__arraySum(__isLeapYear(date.tm_year+1900) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, date.tm_mon-1), 3);

        },

        '%m': function(date) {

          return leadingNulls(date.tm_mon+1, 2);

        },

        '%M': function(date) {

          return leadingNulls(date.tm_min, 2);

        },

        '%n': function() {

          return '\n';

        },

        '%p': function(date) {

          if (date.tm_hour >= 0 && date.tm_hour < 12) {

            return 'AM';

          } else {

            return 'PM';

          }

        },

        '%S': function(date) {

          return leadingNulls(date.tm_sec, 2);

        },

        '%t': function() {

          return '\t';

        },

        '%u': function(date) {

          return date.tm_wday || 7;

        },

        '%U': function(date) {

          // Replaced by the week number of the year as a decimal number [00,53].

          // The first Sunday of January is the first day of week 1;

          // days in the new year before this are in week 0. [ tm_year, tm_wday, tm_yday]

          var janFirst = new Date(date.tm_year+1900, 0, 1);

          var firstSunday = janFirst.getDay() === 0 ? janFirst : __addDays(janFirst, 7-janFirst.getDay());

          var endDate = new Date(date.tm_year+1900, date.tm_mon, date.tm_mday);

  

          // is target date after the first Sunday?

          if (compareByDay(firstSunday, endDate) < 0) {

            // calculate difference in days between first Sunday and endDate

            var februaryFirstUntilEndMonth = __arraySum(__isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, endDate.getMonth()-1)-31;

            var firstSundayUntilEndJanuary = 31-firstSunday.getDate();

            var days = firstSundayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();

            return leadingNulls(Math.ceil(days/7), 2);

          }

  

          return compareByDay(firstSunday, janFirst) === 0 ? '01': '00';

        },

        '%V': function(date) {

          // Replaced by the week number of the year (Monday as the first day of the week)

          // as a decimal number [01,53]. If the week containing 1 January has four

          // or more days in the new year, then it is considered week 1.

          // Otherwise, it is the last week of the previous year, and the next week is week 1.

          // Both January 4th and the first Thursday of January are always in week 1. [ tm_year, tm_wday, tm_yday]

          var janFourthThisYear = new Date(date.tm_year+1900, 0, 4);

          var janFourthNextYear = new Date(date.tm_year+1901, 0, 4);

  

          var firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear);

          var firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear);

  

          var endDate = __addDays(new Date(date.tm_year+1900, 0, 1), date.tm_yday);

  

          if (compareByDay(endDate, firstWeekStartThisYear) < 0) {

            // if given date is before this years first week, then it belongs to the 53rd week of last year

            return '53';

          }

  

          if (compareByDay(firstWeekStartNextYear, endDate) <= 0) {

            // if given date is after next years first week, then it belongs to the 01th week of next year

            return '01';

          }

  

          // given date is in between CW 01..53 of this calendar year

          var daysDifference;

          if (firstWeekStartThisYear.getFullYear() < date.tm_year+1900) {

            // first CW of this year starts last year

            daysDifference = date.tm_yday+32-firstWeekStartThisYear.getDate()

          } else {

            // first CW of this year starts this year

            daysDifference = date.tm_yday+1-firstWeekStartThisYear.getDate();

          }

          return leadingNulls(Math.ceil(daysDifference/7), 2);

        },

        '%w': function(date) {

          return date.tm_wday;

        },

        '%W': function(date) {

          // Replaced by the week number of the year as a decimal number [00,53].

          // The first Monday of January is the first day of week 1;

          // days in the new year before this are in week 0. [ tm_year, tm_wday, tm_yday]

          var janFirst = new Date(date.tm_year, 0, 1);

          var firstMonday = janFirst.getDay() === 1 ? janFirst : __addDays(janFirst, janFirst.getDay() === 0 ? 1 : 7-janFirst.getDay()+1);

          var endDate = new Date(date.tm_year+1900, date.tm_mon, date.tm_mday);

  

          // is target date after the first Monday?

          if (compareByDay(firstMonday, endDate) < 0) {

            var februaryFirstUntilEndMonth = __arraySum(__isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, endDate.getMonth()-1)-31;

            var firstMondayUntilEndJanuary = 31-firstMonday.getDate();

            var days = firstMondayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();

            return leadingNulls(Math.ceil(days/7), 2);

          }

          return compareByDay(firstMonday, janFirst) === 0 ? '01': '00';

        },

        '%y': function(date) {

          // Replaced by the last two digits of the year as a decimal number [00,99]. [ tm_year]

          return (date.tm_year+1900).toString().substring(2);

        },

        '%Y': function(date) {

          // Replaced by the year as a decimal number (for example, 1997). [ tm_year]

          return date.tm_year+1900;

        },

        '%z': function(date) {

          // Replaced by the offset from UTC in the ISO 8601:2000 standard format ( +hhmm or -hhmm ).

          // For example, "-0430" means 4 hours 30 minutes behind UTC (west of Greenwich).

          var off = date.tm_gmtoff;

          var ahead = off >= 0;

          off = Math.abs(off) / 60;

          // convert from minutes into hhmm format (which means 60 minutes = 100 units)

          off = (off / 60)*100 + (off % 60);

          return (ahead ? '+' : '-') + String("0000" + off).slice(-4);

        },

        '%Z': function(date) {

          return date.tm_zone;

        },

        '%%': function() {

          return '%';

        }

      };

      for (var rule in EXPANSION_RULES_2) {

        if (pattern.includes(rule)) {

          pattern = pattern.replace(new RegExp(rule, 'g'), EXPANSION_RULES_2[rule](date));

        }

      }

  

      var bytes = intArrayFromString(pattern, false);

      if (bytes.length > maxsize) {

        return 0;

      }

  

      writeArrayToMemory(bytes, s);

      return bytes.length-1;

    }

  function _strftime_l(s, maxsize, format, tm) {

      return _strftime(s, maxsize, format, tm); // no locale support yet

    }

embind_init_charCodes();

BindingError = RoomsDivisionModule['BindingError'] = extendError(Error, 'BindingError');;

InternalError = RoomsDivisionModule['InternalError'] = extendError(Error, 'InternalError');;

init_emval();;

UnboundTypeError = RoomsDivisionModule['UnboundTypeError'] = extendError(Error, 'UnboundTypeError');;



  var FSNode = /** @constructor */ function(parent, name, mode, rdev) {

    if (!parent) {

      parent = this;  // root node sets parent to itself

    }

    this.parent = parent;

    this.mount = parent.mount;

    this.mounted = null;

    this.id = FS.nextInode++;

    this.name = name;

    this.mode = mode;

    this.node_ops = {};

    this.stream_ops = {};

    this.rdev = rdev;

  };

  var readMode = 292/*292*/ | 73/*73*/;

  var writeMode = 146/*146*/;

  Object.defineProperties(FSNode.prototype, {

   read: {

    get: /** @this{FSNode} */function() {

     return (this.mode & readMode) === readMode;

    },

    set: /** @this{FSNode} */function(val) {

     val ? this.mode |= readMode : this.mode &= ~readMode;

    }

   },

   write: {

    get: /** @this{FSNode} */function() {

     return (this.mode & writeMode) === writeMode;

    },

    set: /** @this{FSNode} */function(val) {

     val ? this.mode |= writeMode : this.mode &= ~writeMode;

    }

   },

   isFolder: {

    get: /** @this{FSNode} */function() {

     return FS.isDir(this.mode);

    }

   },

   isDevice: {

    get: /** @this{FSNode} */function() {

     return FS.isChrdev(this.mode);

    }

   }

  });

  FS.FSNode = FSNode;

  FS.staticInit();;

ERRNO_CODES = {

      'EPERM': 63,

      'ENOENT': 44,

      'ESRCH': 71,

      'EINTR': 27,

      'EIO': 29,

      'ENXIO': 60,

      'E2BIG': 1,

      'ENOEXEC': 45,

      'EBADF': 8,

      'ECHILD': 12,

      'EAGAIN': 6,

      'EWOULDBLOCK': 6,

      'ENOMEM': 48,

      'EACCES': 2,

      'EFAULT': 21,

      'ENOTBLK': 105,

      'EBUSY': 10,

      'EEXIST': 20,

      'EXDEV': 75,

      'ENODEV': 43,

      'ENOTDIR': 54,

      'EISDIR': 31,

      'EINVAL': 28,

      'ENFILE': 41,

      'EMFILE': 33,

      'ENOTTY': 59,

      'ETXTBSY': 74,

      'EFBIG': 22,

      'ENOSPC': 51,

      'ESPIPE': 70,

      'EROFS': 69,

      'EMLINK': 34,

      'EPIPE': 64,

      'EDOM': 18,

      'ERANGE': 68,

      'ENOMSG': 49,

      'EIDRM': 24,

      'ECHRNG': 106,

      'EL2NSYNC': 156,

      'EL3HLT': 107,

      'EL3RST': 108,

      'ELNRNG': 109,

      'EUNATCH': 110,

      'ENOCSI': 111,

      'EL2HLT': 112,

      'EDEADLK': 16,

      'ENOLCK': 46,

      'EBADE': 113,

      'EBADR': 114,

      'EXFULL': 115,

      'ENOANO': 104,

      'EBADRQC': 103,

      'EBADSLT': 102,

      'EDEADLOCK': 16,

      'EBFONT': 101,

      'ENOSTR': 100,

      'ENODATA': 116,

      'ETIME': 117,

      'ENOSR': 118,

      'ENONET': 119,

      'ENOPKG': 120,

      'EREMOTE': 121,

      'ENOLINK': 47,

      'EADV': 122,

      'ESRMNT': 123,

      'ECOMM': 124,

      'EPROTO': 65,

      'EMULTIHOP': 36,

      'EDOTDOT': 125,

      'EBADMSG': 9,

      'ENOTUNIQ': 126,

      'EBADFD': 127,

      'EREMCHG': 128,

      'ELIBACC': 129,

      'ELIBBAD': 130,

      'ELIBSCN': 131,

      'ELIBMAX': 132,

      'ELIBEXEC': 133,

      'ENOSYS': 52,

      'ENOTEMPTY': 55,

      'ENAMETOOLONG': 37,

      'ELOOP': 32,

      'EOPNOTSUPP': 138,

      'EPFNOSUPPORT': 139,

      'ECONNRESET': 15,

      'ENOBUFS': 42,

      'EAFNOSUPPORT': 5,

      'EPROTOTYPE': 67,

      'ENOTSOCK': 57,

      'ENOPROTOOPT': 50,

      'ESHUTDOWN': 140,

      'ECONNREFUSED': 14,

      'EADDRINUSE': 3,

      'ECONNABORTED': 13,

      'ENETUNREACH': 40,

      'ENETDOWN': 38,

      'ETIMEDOUT': 73,

      'EHOSTDOWN': 142,

      'EHOSTUNREACH': 23,

      'EINPROGRESS': 26,

      'EALREADY': 7,

      'EDESTADDRREQ': 17,

      'EMSGSIZE': 35,

      'EPROTONOSUPPORT': 66,

      'ESOCKTNOSUPPORT': 137,

      'EADDRNOTAVAIL': 4,

      'ENETRESET': 39,

      'EISCONN': 30,

      'ENOTCONN': 53,

      'ETOOMANYREFS': 141,

      'EUSERS': 136,

      'EDQUOT': 19,

      'ESTALE': 72,

      'ENOTSUP': 138,

      'ENOMEDIUM': 148,

      'EILSEQ': 25,

      'EOVERFLOW': 61,

      'ECANCELED': 11,

      'ENOTRECOVERABLE': 56,

      'EOWNERDEAD': 62,

      'ESTRPIPE': 135,

    };;

var ASSERTIONS = true;







/** @type {function(string, boolean=, number=)} */

function intArrayFromString(stringy, dontAddNull, length) {

  var len = length > 0 ? length : lengthBytesUTF8(stringy)+1;

  var u8array = new Array(len);

  var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length);

  if (dontAddNull) u8array.length = numBytesWritten;

  return u8array;

}



function intArrayToString(array) {

  var ret = [];

  for (var i = 0; i < array.length; i++) {

    var chr = array[i];

    if (chr > 0xFF) {

      if (ASSERTIONS) {

        assert(false, 'Character code ' + chr + ' (' + String.fromCharCode(chr) + ')  at offset ' + i + ' not in 0x00-0xFF.');

      }

      chr &= 0xFF;

    }

    ret.push(String.fromCharCode(chr));

  }

  return ret.join('');

}





var asmLibraryArg = {

  "__cxa_allocate_exception": ___cxa_allocate_exception,

  "__cxa_atexit": ___cxa_atexit,

  "__cxa_throw": ___cxa_throw,

  "_embind_register_bigint": __embind_register_bigint,

  "_embind_register_bool": __embind_register_bool,

  "_embind_register_emval": __embind_register_emval,

  "_embind_register_float": __embind_register_float,

  "_embind_register_function": __embind_register_function,

  "_embind_register_integer": __embind_register_integer,

  "_embind_register_memory_view": __embind_register_memory_view,

  "_embind_register_std_string": __embind_register_std_string,

  "_embind_register_std_wstring": __embind_register_std_wstring,

  "_embind_register_void": __embind_register_void,

  "abort": _abort,

  "emscripten_memcpy_big": _emscripten_memcpy_big,

  "emscripten_resize_heap": _emscripten_resize_heap,

  "environ_get": _environ_get,

  "environ_sizes_get": _environ_sizes_get,

  "fd_close": _fd_close,

  "fd_read": _fd_read,

  "fd_seek": _fd_seek,

  "fd_write": _fd_write,

  "getentropy": _getentropy,

  "setTempRet0": _setTempRet0,

  "strftime_l": _strftime_l

};

var asm = createWasm();

/** @type {function(...*):?} */

var ___wasm_call_ctors = RoomsDivisionModule["___wasm_call_ctors"] = createExportWrapper("__wasm_call_ctors");



/** @type {function(...*):?} */

var _free = RoomsDivisionModule["_free"] = createExportWrapper("free");



/** @type {function(...*):?} */

var _malloc = RoomsDivisionModule["_malloc"] = createExportWrapper("malloc");



/** @type {function(...*):?} */

var ___errno_location = RoomsDivisionModule["___errno_location"] = createExportWrapper("__errno_location");



/** @type {function(...*):?} */

var _fflush = RoomsDivisionModule["_fflush"] = createExportWrapper("fflush");



/** @type {function(...*):?} */

var ___getTypeName = RoomsDivisionModule["___getTypeName"] = createExportWrapper("__getTypeName");



/** @type {function(...*):?} */

var ___embind_register_native_and_builtin_types = RoomsDivisionModule["___embind_register_native_and_builtin_types"] = createExportWrapper("__embind_register_native_and_builtin_types");



/** @type {function(...*):?} */

var stackSave = RoomsDivisionModule["stackSave"] = createExportWrapper("stackSave");



/** @type {function(...*):?} */

var stackRestore = RoomsDivisionModule["stackRestore"] = createExportWrapper("stackRestore");



/** @type {function(...*):?} */

var stackAlloc = RoomsDivisionModule["stackAlloc"] = createExportWrapper("stackAlloc");



/** @type {function(...*):?} */

var _emscripten_stack_init = RoomsDivisionModule["_emscripten_stack_init"] = function() {

  return (_emscripten_stack_init = RoomsDivisionModule["_emscripten_stack_init"] = RoomsDivisionModule["asm"]["emscripten_stack_init"]).apply(null, arguments);

};



/** @type {function(...*):?} */

var _emscripten_stack_get_free = RoomsDivisionModule["_emscripten_stack_get_free"] = function() {

  return (_emscripten_stack_get_free = RoomsDivisionModule["_emscripten_stack_get_free"] = RoomsDivisionModule["asm"]["emscripten_stack_get_free"]).apply(null, arguments);

};



/** @type {function(...*):?} */

var _emscripten_stack_get_end = RoomsDivisionModule["_emscripten_stack_get_end"] = function() {

  return (_emscripten_stack_get_end = RoomsDivisionModule["_emscripten_stack_get_end"] = RoomsDivisionModule["asm"]["emscripten_stack_get_end"]).apply(null, arguments);

};



/** @type {function(...*):?} */

var dynCall_iij = RoomsDivisionModule["dynCall_iij"] = createExportWrapper("dynCall_iij");



/** @type {function(...*):?} */

var dynCall_jiji = RoomsDivisionModule["dynCall_jiji"] = createExportWrapper("dynCall_jiji");



/** @type {function(...*):?} */

var dynCall_viijii = RoomsDivisionModule["dynCall_viijii"] = createExportWrapper("dynCall_viijii");



/** @type {function(...*):?} */

var dynCall_iiiiij = RoomsDivisionModule["dynCall_iiiiij"] = createExportWrapper("dynCall_iiiiij");



/** @type {function(...*):?} */

var dynCall_iiiiijj = RoomsDivisionModule["dynCall_iiiiijj"] = createExportWrapper("dynCall_iiiiijj");



/** @type {function(...*):?} */

var dynCall_iiiiiijj = RoomsDivisionModule["dynCall_iiiiiijj"] = createExportWrapper("dynCall_iiiiiijj");











// === Auto-generated postamble setup entry stuff ===



if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "intArrayFromString")) RoomsDivisionModule["intArrayFromString"] = function() { abort("'intArrayFromString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "intArrayToString")) RoomsDivisionModule["intArrayToString"] = function() { abort("'intArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ccall")) RoomsDivisionModule["ccall"] = function() { abort("'ccall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "cwrap")) RoomsDivisionModule["cwrap"] = function() { abort("'cwrap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setValue")) RoomsDivisionModule["setValue"] = function() { abort("'setValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getValue")) RoomsDivisionModule["getValue"] = function() { abort("'getValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "allocate")) RoomsDivisionModule["allocate"] = function() { abort("'allocate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "UTF8ArrayToString")) RoomsDivisionModule["UTF8ArrayToString"] = function() { abort("'UTF8ArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "UTF8ToString")) RoomsDivisionModule["UTF8ToString"] = function() { abort("'UTF8ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stringToUTF8Array")) RoomsDivisionModule["stringToUTF8Array"] = function() { abort("'stringToUTF8Array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stringToUTF8")) RoomsDivisionModule["stringToUTF8"] = function() { abort("'stringToUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "lengthBytesUTF8")) RoomsDivisionModule["lengthBytesUTF8"] = function() { abort("'lengthBytesUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stackTrace")) RoomsDivisionModule["stackTrace"] = function() { abort("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "addOnPreRun")) RoomsDivisionModule["addOnPreRun"] = function() { abort("'addOnPreRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "addOnInit")) RoomsDivisionModule["addOnInit"] = function() { abort("'addOnInit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "addOnPreMain")) RoomsDivisionModule["addOnPreMain"] = function() { abort("'addOnPreMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "addOnExit")) RoomsDivisionModule["addOnExit"] = function() { abort("'addOnExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "addOnPostRun")) RoomsDivisionModule["addOnPostRun"] = function() { abort("'addOnPostRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeStringToMemory")) RoomsDivisionModule["writeStringToMemory"] = function() { abort("'writeStringToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeArrayToMemory")) RoomsDivisionModule["writeArrayToMemory"] = function() { abort("'writeArrayToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeAsciiToMemory")) RoomsDivisionModule["writeAsciiToMemory"] = function() { abort("'writeAsciiToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "addRunDependency")) RoomsDivisionModule["addRunDependency"] = function() { abort("'addRunDependency' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "removeRunDependency")) RoomsDivisionModule["removeRunDependency"] = function() { abort("'removeRunDependency' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_createFolder")) RoomsDivisionModule["FS_createFolder"] = function() { abort("'FS_createFolder' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_createPath")) RoomsDivisionModule["FS_createPath"] = function() { abort("'FS_createPath' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_createDataFile")) RoomsDivisionModule["FS_createDataFile"] = function() { abort("'FS_createDataFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_createPreloadedFile")) RoomsDivisionModule["FS_createPreloadedFile"] = function() { abort("'FS_createPreloadedFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_createLazyFile")) RoomsDivisionModule["FS_createLazyFile"] = function() { abort("'FS_createLazyFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_createLink")) RoomsDivisionModule["FS_createLink"] = function() { abort("'FS_createLink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_createDevice")) RoomsDivisionModule["FS_createDevice"] = function() { abort("'FS_createDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS_unlink")) RoomsDivisionModule["FS_unlink"] = function() { abort("'FS_unlink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getLEB")) RoomsDivisionModule["getLEB"] = function() { abort("'getLEB' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getFunctionTables")) RoomsDivisionModule["getFunctionTables"] = function() { abort("'getFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "alignFunctionTables")) RoomsDivisionModule["alignFunctionTables"] = function() { abort("'alignFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerFunctions")) RoomsDivisionModule["registerFunctions"] = function() { abort("'registerFunctions' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "addFunction")) RoomsDivisionModule["addFunction"] = function() { abort("'addFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "removeFunction")) RoomsDivisionModule["removeFunction"] = function() { abort("'removeFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getFuncWrapper")) RoomsDivisionModule["getFuncWrapper"] = function() { abort("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "prettyPrint")) RoomsDivisionModule["prettyPrint"] = function() { abort("'prettyPrint' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "dynCall")) RoomsDivisionModule["dynCall"] = function() { abort("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getCompilerSetting")) RoomsDivisionModule["getCompilerSetting"] = function() { abort("'getCompilerSetting' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "print")) RoomsDivisionModule["print"] = function() { abort("'print' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "printErr")) RoomsDivisionModule["printErr"] = function() { abort("'printErr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getTempRet0")) RoomsDivisionModule["getTempRet0"] = function() { abort("'getTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setTempRet0")) RoomsDivisionModule["setTempRet0"] = function() { abort("'setTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "callMain")) RoomsDivisionModule["callMain"] = function() { abort("'callMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "abort")) RoomsDivisionModule["abort"] = function() { abort("'abort' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "keepRuntimeAlive")) RoomsDivisionModule["keepRuntimeAlive"] = function() { abort("'keepRuntimeAlive' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "zeroMemory")) RoomsDivisionModule["zeroMemory"] = function() { abort("'zeroMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stringToNewUTF8")) RoomsDivisionModule["stringToNewUTF8"] = function() { abort("'stringToNewUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setFileTime")) RoomsDivisionModule["setFileTime"] = function() { abort("'setFileTime' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "abortOnCannotGrowMemory")) RoomsDivisionModule["abortOnCannotGrowMemory"] = function() { abort("'abortOnCannotGrowMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emscripten_realloc_buffer")) RoomsDivisionModule["emscripten_realloc_buffer"] = function() { abort("'emscripten_realloc_buffer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ENV")) RoomsDivisionModule["ENV"] = function() { abort("'ENV' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "withStackSave")) RoomsDivisionModule["withStackSave"] = function() { abort("'withStackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ERRNO_CODES")) RoomsDivisionModule["ERRNO_CODES"] = function() { abort("'ERRNO_CODES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ERRNO_MESSAGES")) RoomsDivisionModule["ERRNO_MESSAGES"] = function() { abort("'ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setErrNo")) RoomsDivisionModule["setErrNo"] = function() { abort("'setErrNo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "inetPton4")) RoomsDivisionModule["inetPton4"] = function() { abort("'inetPton4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "inetNtop4")) RoomsDivisionModule["inetNtop4"] = function() { abort("'inetNtop4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "inetPton6")) RoomsDivisionModule["inetPton6"] = function() { abort("'inetPton6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "inetNtop6")) RoomsDivisionModule["inetNtop6"] = function() { abort("'inetNtop6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "readSockaddr")) RoomsDivisionModule["readSockaddr"] = function() { abort("'readSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeSockaddr")) RoomsDivisionModule["writeSockaddr"] = function() { abort("'writeSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "DNS")) RoomsDivisionModule["DNS"] = function() { abort("'DNS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getHostByName")) RoomsDivisionModule["getHostByName"] = function() { abort("'getHostByName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "GAI_ERRNO_MESSAGES")) RoomsDivisionModule["GAI_ERRNO_MESSAGES"] = function() { abort("'GAI_ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "Protocols")) RoomsDivisionModule["Protocols"] = function() { abort("'Protocols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "Sockets")) RoomsDivisionModule["Sockets"] = function() { abort("'Sockets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getRandomDevice")) RoomsDivisionModule["getRandomDevice"] = function() { abort("'getRandomDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "traverseStack")) RoomsDivisionModule["traverseStack"] = function() { abort("'traverseStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "UNWIND_CACHE")) RoomsDivisionModule["UNWIND_CACHE"] = function() { abort("'UNWIND_CACHE' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "readAsmConstArgsArray")) RoomsDivisionModule["readAsmConstArgsArray"] = function() { abort("'readAsmConstArgsArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "readAsmConstArgs")) RoomsDivisionModule["readAsmConstArgs"] = function() { abort("'readAsmConstArgs' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "mainThreadEM_ASM")) RoomsDivisionModule["mainThreadEM_ASM"] = function() { abort("'mainThreadEM_ASM' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "jstoi_q")) RoomsDivisionModule["jstoi_q"] = function() { abort("'jstoi_q' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "jstoi_s")) RoomsDivisionModule["jstoi_s"] = function() { abort("'jstoi_s' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getExecutableName")) RoomsDivisionModule["getExecutableName"] = function() { abort("'getExecutableName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "listenOnce")) RoomsDivisionModule["listenOnce"] = function() { abort("'listenOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "autoResumeAudioContext")) RoomsDivisionModule["autoResumeAudioContext"] = function() { abort("'autoResumeAudioContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "dynCallLegacy")) RoomsDivisionModule["dynCallLegacy"] = function() { abort("'dynCallLegacy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getDynCaller")) RoomsDivisionModule["getDynCaller"] = function() { abort("'getDynCaller' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "dynCall")) RoomsDivisionModule["dynCall"] = function() { abort("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "callRuntimeCallbacks")) RoomsDivisionModule["callRuntimeCallbacks"] = function() { abort("'callRuntimeCallbacks' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "wasmTableMirror")) RoomsDivisionModule["wasmTableMirror"] = function() { abort("'wasmTableMirror' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setWasmTableEntry")) RoomsDivisionModule["setWasmTableEntry"] = function() { abort("'setWasmTableEntry' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getWasmTableEntry")) RoomsDivisionModule["getWasmTableEntry"] = function() { abort("'getWasmTableEntry' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "handleException")) RoomsDivisionModule["handleException"] = function() { abort("'handleException' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "runtimeKeepalivePush")) RoomsDivisionModule["runtimeKeepalivePush"] = function() { abort("'runtimeKeepalivePush' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "runtimeKeepalivePop")) RoomsDivisionModule["runtimeKeepalivePop"] = function() { abort("'runtimeKeepalivePop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "callUserCallback")) RoomsDivisionModule["callUserCallback"] = function() { abort("'callUserCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "maybeExit")) RoomsDivisionModule["maybeExit"] = function() { abort("'maybeExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "safeSetTimeout")) RoomsDivisionModule["safeSetTimeout"] = function() { abort("'safeSetTimeout' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "asmjsMangle")) RoomsDivisionModule["asmjsMangle"] = function() { abort("'asmjsMangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "asyncLoad")) RoomsDivisionModule["asyncLoad"] = function() { abort("'asyncLoad' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "alignMemory")) RoomsDivisionModule["alignMemory"] = function() { abort("'alignMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "mmapAlloc")) RoomsDivisionModule["mmapAlloc"] = function() { abort("'mmapAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "reallyNegative")) RoomsDivisionModule["reallyNegative"] = function() { abort("'reallyNegative' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "unSign")) RoomsDivisionModule["unSign"] = function() { abort("'unSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "reSign")) RoomsDivisionModule["reSign"] = function() { abort("'reSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "formatString")) RoomsDivisionModule["formatString"] = function() { abort("'formatString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "PATH")) RoomsDivisionModule["PATH"] = function() { abort("'PATH' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "PATH_FS")) RoomsDivisionModule["PATH_FS"] = function() { abort("'PATH_FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "SYSCALLS")) RoomsDivisionModule["SYSCALLS"] = function() { abort("'SYSCALLS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "syscallMmap2")) RoomsDivisionModule["syscallMmap2"] = function() { abort("'syscallMmap2' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "syscallMunmap")) RoomsDivisionModule["syscallMunmap"] = function() { abort("'syscallMunmap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getSocketFromFD")) RoomsDivisionModule["getSocketFromFD"] = function() { abort("'getSocketFromFD' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getSocketAddress")) RoomsDivisionModule["getSocketAddress"] = function() { abort("'getSocketAddress' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "JSEvents")) RoomsDivisionModule["JSEvents"] = function() { abort("'JSEvents' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerKeyEventCallback")) RoomsDivisionModule["registerKeyEventCallback"] = function() { abort("'registerKeyEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "specialHTMLTargets")) RoomsDivisionModule["specialHTMLTargets"] = function() { abort("'specialHTMLTargets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "maybeCStringToJsString")) RoomsDivisionModule["maybeCStringToJsString"] = function() { abort("'maybeCStringToJsString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "findEventTarget")) RoomsDivisionModule["findEventTarget"] = function() { abort("'findEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "findCanvasEventTarget")) RoomsDivisionModule["findCanvasEventTarget"] = function() { abort("'findCanvasEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getBoundingClientRect")) RoomsDivisionModule["getBoundingClientRect"] = function() { abort("'getBoundingClientRect' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillMouseEventData")) RoomsDivisionModule["fillMouseEventData"] = function() { abort("'fillMouseEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerMouseEventCallback")) RoomsDivisionModule["registerMouseEventCallback"] = function() { abort("'registerMouseEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerWheelEventCallback")) RoomsDivisionModule["registerWheelEventCallback"] = function() { abort("'registerWheelEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerUiEventCallback")) RoomsDivisionModule["registerUiEventCallback"] = function() { abort("'registerUiEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerFocusEventCallback")) RoomsDivisionModule["registerFocusEventCallback"] = function() { abort("'registerFocusEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillDeviceOrientationEventData")) RoomsDivisionModule["fillDeviceOrientationEventData"] = function() { abort("'fillDeviceOrientationEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerDeviceOrientationEventCallback")) RoomsDivisionModule["registerDeviceOrientationEventCallback"] = function() { abort("'registerDeviceOrientationEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillDeviceMotionEventData")) RoomsDivisionModule["fillDeviceMotionEventData"] = function() { abort("'fillDeviceMotionEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerDeviceMotionEventCallback")) RoomsDivisionModule["registerDeviceMotionEventCallback"] = function() { abort("'registerDeviceMotionEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "screenOrientation")) RoomsDivisionModule["screenOrientation"] = function() { abort("'screenOrientation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillOrientationChangeEventData")) RoomsDivisionModule["fillOrientationChangeEventData"] = function() { abort("'fillOrientationChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerOrientationChangeEventCallback")) RoomsDivisionModule["registerOrientationChangeEventCallback"] = function() { abort("'registerOrientationChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillFullscreenChangeEventData")) RoomsDivisionModule["fillFullscreenChangeEventData"] = function() { abort("'fillFullscreenChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerFullscreenChangeEventCallback")) RoomsDivisionModule["registerFullscreenChangeEventCallback"] = function() { abort("'registerFullscreenChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerRestoreOldStyle")) RoomsDivisionModule["registerRestoreOldStyle"] = function() { abort("'registerRestoreOldStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "hideEverythingExceptGivenElement")) RoomsDivisionModule["hideEverythingExceptGivenElement"] = function() { abort("'hideEverythingExceptGivenElement' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "restoreHiddenElements")) RoomsDivisionModule["restoreHiddenElements"] = function() { abort("'restoreHiddenElements' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setLetterbox")) RoomsDivisionModule["setLetterbox"] = function() { abort("'setLetterbox' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "currentFullscreenStrategy")) RoomsDivisionModule["currentFullscreenStrategy"] = function() { abort("'currentFullscreenStrategy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "restoreOldWindowedStyle")) RoomsDivisionModule["restoreOldWindowedStyle"] = function() { abort("'restoreOldWindowedStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "softFullscreenResizeWebGLRenderTarget")) RoomsDivisionModule["softFullscreenResizeWebGLRenderTarget"] = function() { abort("'softFullscreenResizeWebGLRenderTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "doRequestFullscreen")) RoomsDivisionModule["doRequestFullscreen"] = function() { abort("'doRequestFullscreen' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillPointerlockChangeEventData")) RoomsDivisionModule["fillPointerlockChangeEventData"] = function() { abort("'fillPointerlockChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerPointerlockChangeEventCallback")) RoomsDivisionModule["registerPointerlockChangeEventCallback"] = function() { abort("'registerPointerlockChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerPointerlockErrorEventCallback")) RoomsDivisionModule["registerPointerlockErrorEventCallback"] = function() { abort("'registerPointerlockErrorEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "requestPointerLock")) RoomsDivisionModule["requestPointerLock"] = function() { abort("'requestPointerLock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillVisibilityChangeEventData")) RoomsDivisionModule["fillVisibilityChangeEventData"] = function() { abort("'fillVisibilityChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerVisibilityChangeEventCallback")) RoomsDivisionModule["registerVisibilityChangeEventCallback"] = function() { abort("'registerVisibilityChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerTouchEventCallback")) RoomsDivisionModule["registerTouchEventCallback"] = function() { abort("'registerTouchEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillGamepadEventData")) RoomsDivisionModule["fillGamepadEventData"] = function() { abort("'fillGamepadEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerGamepadEventCallback")) RoomsDivisionModule["registerGamepadEventCallback"] = function() { abort("'registerGamepadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerBeforeUnloadEventCallback")) RoomsDivisionModule["registerBeforeUnloadEventCallback"] = function() { abort("'registerBeforeUnloadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "fillBatteryEventData")) RoomsDivisionModule["fillBatteryEventData"] = function() { abort("'fillBatteryEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "battery")) RoomsDivisionModule["battery"] = function() { abort("'battery' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerBatteryEventCallback")) RoomsDivisionModule["registerBatteryEventCallback"] = function() { abort("'registerBatteryEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setCanvasElementSize")) RoomsDivisionModule["setCanvasElementSize"] = function() { abort("'setCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getCanvasElementSize")) RoomsDivisionModule["getCanvasElementSize"] = function() { abort("'getCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "demangle")) RoomsDivisionModule["demangle"] = function() { abort("'demangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "demangleAll")) RoomsDivisionModule["demangleAll"] = function() { abort("'demangleAll' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "jsStackTrace")) RoomsDivisionModule["jsStackTrace"] = function() { abort("'jsStackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stackTrace")) RoomsDivisionModule["stackTrace"] = function() { abort("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getEnvStrings")) RoomsDivisionModule["getEnvStrings"] = function() { abort("'getEnvStrings' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "checkWasiClock")) RoomsDivisionModule["checkWasiClock"] = function() { abort("'checkWasiClock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeI53ToI64")) RoomsDivisionModule["writeI53ToI64"] = function() { abort("'writeI53ToI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeI53ToI64Clamped")) RoomsDivisionModule["writeI53ToI64Clamped"] = function() { abort("'writeI53ToI64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeI53ToI64Signaling")) RoomsDivisionModule["writeI53ToI64Signaling"] = function() { abort("'writeI53ToI64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeI53ToU64Clamped")) RoomsDivisionModule["writeI53ToU64Clamped"] = function() { abort("'writeI53ToU64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeI53ToU64Signaling")) RoomsDivisionModule["writeI53ToU64Signaling"] = function() { abort("'writeI53ToU64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "readI53FromI64")) RoomsDivisionModule["readI53FromI64"] = function() { abort("'readI53FromI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "readI53FromU64")) RoomsDivisionModule["readI53FromU64"] = function() { abort("'readI53FromU64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "convertI32PairToI53")) RoomsDivisionModule["convertI32PairToI53"] = function() { abort("'convertI32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "convertU32PairToI53")) RoomsDivisionModule["convertU32PairToI53"] = function() { abort("'convertU32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setImmediateWrapped")) RoomsDivisionModule["setImmediateWrapped"] = function() { abort("'setImmediateWrapped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "clearImmediateWrapped")) RoomsDivisionModule["clearImmediateWrapped"] = function() { abort("'clearImmediateWrapped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "polyfillSetImmediate")) RoomsDivisionModule["polyfillSetImmediate"] = function() { abort("'polyfillSetImmediate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "uncaughtExceptionCount")) RoomsDivisionModule["uncaughtExceptionCount"] = function() { abort("'uncaughtExceptionCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "exceptionLast")) RoomsDivisionModule["exceptionLast"] = function() { abort("'exceptionLast' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "exceptionCaught")) RoomsDivisionModule["exceptionCaught"] = function() { abort("'exceptionCaught' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ExceptionInfo")) RoomsDivisionModule["ExceptionInfo"] = function() { abort("'ExceptionInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "CatchInfo")) RoomsDivisionModule["CatchInfo"] = function() { abort("'CatchInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "exception_addRef")) RoomsDivisionModule["exception_addRef"] = function() { abort("'exception_addRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "exception_decRef")) RoomsDivisionModule["exception_decRef"] = function() { abort("'exception_decRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "Browser")) RoomsDivisionModule["Browser"] = function() { abort("'Browser' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "funcWrappers")) RoomsDivisionModule["funcWrappers"] = function() { abort("'funcWrappers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getFuncWrapper")) RoomsDivisionModule["getFuncWrapper"] = function() { abort("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setMainLoop")) RoomsDivisionModule["setMainLoop"] = function() { abort("'setMainLoop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "wget")) RoomsDivisionModule["wget"] = function() { abort("'wget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "FS")) RoomsDivisionModule["FS"] = function() { abort("'FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "MEMFS")) RoomsDivisionModule["MEMFS"] = function() { abort("'MEMFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "TTY")) RoomsDivisionModule["TTY"] = function() { abort("'TTY' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "PIPEFS")) RoomsDivisionModule["PIPEFS"] = function() { abort("'PIPEFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "SOCKFS")) RoomsDivisionModule["SOCKFS"] = function() { abort("'SOCKFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "_setNetworkCallback")) RoomsDivisionModule["_setNetworkCallback"] = function() { abort("'_setNetworkCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "tempFixedLengthArray")) RoomsDivisionModule["tempFixedLengthArray"] = function() { abort("'tempFixedLengthArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "miniTempWebGLFloatBuffers")) RoomsDivisionModule["miniTempWebGLFloatBuffers"] = function() { abort("'miniTempWebGLFloatBuffers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "heapObjectForWebGLType")) RoomsDivisionModule["heapObjectForWebGLType"] = function() { abort("'heapObjectForWebGLType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "heapAccessShiftForWebGLHeap")) RoomsDivisionModule["heapAccessShiftForWebGLHeap"] = function() { abort("'heapAccessShiftForWebGLHeap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "GL")) RoomsDivisionModule["GL"] = function() { abort("'GL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emscriptenWebGLGet")) RoomsDivisionModule["emscriptenWebGLGet"] = function() { abort("'emscriptenWebGLGet' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "computeUnpackAlignedImageSize")) RoomsDivisionModule["computeUnpackAlignedImageSize"] = function() { abort("'computeUnpackAlignedImageSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emscriptenWebGLGetTexPixelData")) RoomsDivisionModule["emscriptenWebGLGetTexPixelData"] = function() { abort("'emscriptenWebGLGetTexPixelData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emscriptenWebGLGetUniform")) RoomsDivisionModule["emscriptenWebGLGetUniform"] = function() { abort("'emscriptenWebGLGetUniform' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "webglGetUniformLocation")) RoomsDivisionModule["webglGetUniformLocation"] = function() { abort("'webglGetUniformLocation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "webglPrepareUniformLocationsBeforeFirstUse")) RoomsDivisionModule["webglPrepareUniformLocationsBeforeFirstUse"] = function() { abort("'webglPrepareUniformLocationsBeforeFirstUse' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "webglGetLeftBracePos")) RoomsDivisionModule["webglGetLeftBracePos"] = function() { abort("'webglGetLeftBracePos' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emscriptenWebGLGetVertexAttrib")) RoomsDivisionModule["emscriptenWebGLGetVertexAttrib"] = function() { abort("'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "writeGLArray")) RoomsDivisionModule["writeGLArray"] = function() { abort("'writeGLArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "AL")) RoomsDivisionModule["AL"] = function() { abort("'AL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "SDL_unicode")) RoomsDivisionModule["SDL_unicode"] = function() { abort("'SDL_unicode' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "SDL_ttfContext")) RoomsDivisionModule["SDL_ttfContext"] = function() { abort("'SDL_ttfContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "SDL_audio")) RoomsDivisionModule["SDL_audio"] = function() { abort("'SDL_audio' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "SDL")) RoomsDivisionModule["SDL"] = function() { abort("'SDL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "SDL_gfx")) RoomsDivisionModule["SDL_gfx"] = function() { abort("'SDL_gfx' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "GLUT")) RoomsDivisionModule["GLUT"] = function() { abort("'GLUT' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "EGL")) RoomsDivisionModule["EGL"] = function() { abort("'EGL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "GLFW_Window")) RoomsDivisionModule["GLFW_Window"] = function() { abort("'GLFW_Window' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "GLFW")) RoomsDivisionModule["GLFW"] = function() { abort("'GLFW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "GLEW")) RoomsDivisionModule["GLEW"] = function() { abort("'GLEW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "IDBStore")) RoomsDivisionModule["IDBStore"] = function() { abort("'IDBStore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "runAndAbortIfError")) RoomsDivisionModule["runAndAbortIfError"] = function() { abort("'runAndAbortIfError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emval_handle_array")) RoomsDivisionModule["emval_handle_array"] = function() { abort("'emval_handle_array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emval_free_list")) RoomsDivisionModule["emval_free_list"] = function() { abort("'emval_free_list' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emval_symbols")) RoomsDivisionModule["emval_symbols"] = function() { abort("'emval_symbols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "init_emval")) RoomsDivisionModule["init_emval"] = function() { abort("'init_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "count_emval_handles")) RoomsDivisionModule["count_emval_handles"] = function() { abort("'count_emval_handles' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "get_first_emval")) RoomsDivisionModule["get_first_emval"] = function() { abort("'get_first_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getStringOrSymbol")) RoomsDivisionModule["getStringOrSymbol"] = function() { abort("'getStringOrSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "Emval")) RoomsDivisionModule["Emval"] = function() { abort("'Emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emval_newers")) RoomsDivisionModule["emval_newers"] = function() { abort("'emval_newers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "craftEmvalAllocator")) RoomsDivisionModule["craftEmvalAllocator"] = function() { abort("'craftEmvalAllocator' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emval_get_global")) RoomsDivisionModule["emval_get_global"] = function() { abort("'emval_get_global' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emval_methodCallers")) RoomsDivisionModule["emval_methodCallers"] = function() { abort("'emval_methodCallers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "emval_registeredMethods")) RoomsDivisionModule["emval_registeredMethods"] = function() { abort("'emval_registeredMethods' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "InternalError")) RoomsDivisionModule["InternalError"] = function() { abort("'InternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "BindingError")) RoomsDivisionModule["BindingError"] = function() { abort("'BindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "UnboundTypeError")) RoomsDivisionModule["UnboundTypeError"] = function() { abort("'UnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "PureVirtualError")) RoomsDivisionModule["PureVirtualError"] = function() { abort("'PureVirtualError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "init_embind")) RoomsDivisionModule["init_embind"] = function() { abort("'init_embind' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "throwInternalError")) RoomsDivisionModule["throwInternalError"] = function() { abort("'throwInternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "throwBindingError")) RoomsDivisionModule["throwBindingError"] = function() { abort("'throwBindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "throwUnboundTypeError")) RoomsDivisionModule["throwUnboundTypeError"] = function() { abort("'throwUnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ensureOverloadTable")) RoomsDivisionModule["ensureOverloadTable"] = function() { abort("'ensureOverloadTable' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "exposePublicSymbol")) RoomsDivisionModule["exposePublicSymbol"] = function() { abort("'exposePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "replacePublicSymbol")) RoomsDivisionModule["replacePublicSymbol"] = function() { abort("'replacePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "extendError")) RoomsDivisionModule["extendError"] = function() { abort("'extendError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "createNamedFunction")) RoomsDivisionModule["createNamedFunction"] = function() { abort("'createNamedFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registeredInstances")) RoomsDivisionModule["registeredInstances"] = function() { abort("'registeredInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getBasestPointer")) RoomsDivisionModule["getBasestPointer"] = function() { abort("'getBasestPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerInheritedInstance")) RoomsDivisionModule["registerInheritedInstance"] = function() { abort("'registerInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "unregisterInheritedInstance")) RoomsDivisionModule["unregisterInheritedInstance"] = function() { abort("'unregisterInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getInheritedInstance")) RoomsDivisionModule["getInheritedInstance"] = function() { abort("'getInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getInheritedInstanceCount")) RoomsDivisionModule["getInheritedInstanceCount"] = function() { abort("'getInheritedInstanceCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getLiveInheritedInstances")) RoomsDivisionModule["getLiveInheritedInstances"] = function() { abort("'getLiveInheritedInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registeredTypes")) RoomsDivisionModule["registeredTypes"] = function() { abort("'registeredTypes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "awaitingDependencies")) RoomsDivisionModule["awaitingDependencies"] = function() { abort("'awaitingDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "typeDependencies")) RoomsDivisionModule["typeDependencies"] = function() { abort("'typeDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registeredPointers")) RoomsDivisionModule["registeredPointers"] = function() { abort("'registeredPointers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "registerType")) RoomsDivisionModule["registerType"] = function() { abort("'registerType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "whenDependentTypesAreResolved")) RoomsDivisionModule["whenDependentTypesAreResolved"] = function() { abort("'whenDependentTypesAreResolved' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "embind_charCodes")) RoomsDivisionModule["embind_charCodes"] = function() { abort("'embind_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "embind_init_charCodes")) RoomsDivisionModule["embind_init_charCodes"] = function() { abort("'embind_init_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "readLatin1String")) RoomsDivisionModule["readLatin1String"] = function() { abort("'readLatin1String' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getTypeName")) RoomsDivisionModule["getTypeName"] = function() { abort("'getTypeName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "heap32VectorToArray")) RoomsDivisionModule["heap32VectorToArray"] = function() { abort("'heap32VectorToArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "requireRegisteredType")) RoomsDivisionModule["requireRegisteredType"] = function() { abort("'requireRegisteredType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "getShiftFromSize")) RoomsDivisionModule["getShiftFromSize"] = function() { abort("'getShiftFromSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "integerReadValueFromPointer")) RoomsDivisionModule["integerReadValueFromPointer"] = function() { abort("'integerReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "enumReadValueFromPointer")) RoomsDivisionModule["enumReadValueFromPointer"] = function() { abort("'enumReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "floatReadValueFromPointer")) RoomsDivisionModule["floatReadValueFromPointer"] = function() { abort("'floatReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "simpleReadValueFromPointer")) RoomsDivisionModule["simpleReadValueFromPointer"] = function() { abort("'simpleReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "runDestructors")) RoomsDivisionModule["runDestructors"] = function() { abort("'runDestructors' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "new_")) RoomsDivisionModule["new_"] = function() { abort("'new_' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "craftInvokerFunction")) RoomsDivisionModule["craftInvokerFunction"] = function() { abort("'craftInvokerFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "embind__requireFunction")) RoomsDivisionModule["embind__requireFunction"] = function() { abort("'embind__requireFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "tupleRegistrations")) RoomsDivisionModule["tupleRegistrations"] = function() { abort("'tupleRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "structRegistrations")) RoomsDivisionModule["structRegistrations"] = function() { abort("'structRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "genericPointerToWireType")) RoomsDivisionModule["genericPointerToWireType"] = function() { abort("'genericPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "constNoSmartPtrRawPointerToWireType")) RoomsDivisionModule["constNoSmartPtrRawPointerToWireType"] = function() { abort("'constNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "nonConstNoSmartPtrRawPointerToWireType")) RoomsDivisionModule["nonConstNoSmartPtrRawPointerToWireType"] = function() { abort("'nonConstNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "init_RegisteredPointer")) RoomsDivisionModule["init_RegisteredPointer"] = function() { abort("'init_RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "RegisteredPointer")) RoomsDivisionModule["RegisteredPointer"] = function() { abort("'RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "RegisteredPointer_getPointee")) RoomsDivisionModule["RegisteredPointer_getPointee"] = function() { abort("'RegisteredPointer_getPointee' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "RegisteredPointer_destructor")) RoomsDivisionModule["RegisteredPointer_destructor"] = function() { abort("'RegisteredPointer_destructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "RegisteredPointer_deleteObject")) RoomsDivisionModule["RegisteredPointer_deleteObject"] = function() { abort("'RegisteredPointer_deleteObject' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "RegisteredPointer_fromWireType")) RoomsDivisionModule["RegisteredPointer_fromWireType"] = function() { abort("'RegisteredPointer_fromWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "runDestructor")) RoomsDivisionModule["runDestructor"] = function() { abort("'runDestructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "releaseClassHandle")) RoomsDivisionModule["releaseClassHandle"] = function() { abort("'releaseClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "finalizationGroup")) RoomsDivisionModule["finalizationGroup"] = function() { abort("'finalizationGroup' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "detachFinalizer_deps")) RoomsDivisionModule["detachFinalizer_deps"] = function() { abort("'detachFinalizer_deps' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "detachFinalizer")) RoomsDivisionModule["detachFinalizer"] = function() { abort("'detachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "attachFinalizer")) RoomsDivisionModule["attachFinalizer"] = function() { abort("'attachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "makeClassHandle")) RoomsDivisionModule["makeClassHandle"] = function() { abort("'makeClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "init_ClassHandle")) RoomsDivisionModule["init_ClassHandle"] = function() { abort("'init_ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ClassHandle")) RoomsDivisionModule["ClassHandle"] = function() { abort("'ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ClassHandle_isAliasOf")) RoomsDivisionModule["ClassHandle_isAliasOf"] = function() { abort("'ClassHandle_isAliasOf' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "throwInstanceAlreadyDeleted")) RoomsDivisionModule["throwInstanceAlreadyDeleted"] = function() { abort("'throwInstanceAlreadyDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ClassHandle_clone")) RoomsDivisionModule["ClassHandle_clone"] = function() { abort("'ClassHandle_clone' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ClassHandle_delete")) RoomsDivisionModule["ClassHandle_delete"] = function() { abort("'ClassHandle_delete' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "deletionQueue")) RoomsDivisionModule["deletionQueue"] = function() { abort("'deletionQueue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ClassHandle_isDeleted")) RoomsDivisionModule["ClassHandle_isDeleted"] = function() { abort("'ClassHandle_isDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ClassHandle_deleteLater")) RoomsDivisionModule["ClassHandle_deleteLater"] = function() { abort("'ClassHandle_deleteLater' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "flushPendingDeletes")) RoomsDivisionModule["flushPendingDeletes"] = function() { abort("'flushPendingDeletes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "delayFunction")) RoomsDivisionModule["delayFunction"] = function() { abort("'delayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "setDelayFunction")) RoomsDivisionModule["setDelayFunction"] = function() { abort("'setDelayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "RegisteredClass")) RoomsDivisionModule["RegisteredClass"] = function() { abort("'RegisteredClass' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "shallowCopyInternalPointer")) RoomsDivisionModule["shallowCopyInternalPointer"] = function() { abort("'shallowCopyInternalPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "downcastPointer")) RoomsDivisionModule["downcastPointer"] = function() { abort("'downcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "upcastPointer")) RoomsDivisionModule["upcastPointer"] = function() { abort("'upcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "validateThis")) RoomsDivisionModule["validateThis"] = function() { abort("'validateThis' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "char_0")) RoomsDivisionModule["char_0"] = function() { abort("'char_0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "char_9")) RoomsDivisionModule["char_9"] = function() { abort("'char_9' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "makeLegalFunctionName")) RoomsDivisionModule["makeLegalFunctionName"] = function() { abort("'makeLegalFunctionName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "warnOnce")) RoomsDivisionModule["warnOnce"] = function() { abort("'warnOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stackSave")) RoomsDivisionModule["stackSave"] = function() { abort("'stackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stackRestore")) RoomsDivisionModule["stackRestore"] = function() { abort("'stackRestore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stackAlloc")) RoomsDivisionModule["stackAlloc"] = function() { abort("'stackAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "AsciiToString")) RoomsDivisionModule["AsciiToString"] = function() { abort("'AsciiToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stringToAscii")) RoomsDivisionModule["stringToAscii"] = function() { abort("'stringToAscii' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "UTF16ToString")) RoomsDivisionModule["UTF16ToString"] = function() { abort("'UTF16ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stringToUTF16")) RoomsDivisionModule["stringToUTF16"] = function() { abort("'stringToUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "lengthBytesUTF16")) RoomsDivisionModule["lengthBytesUTF16"] = function() { abort("'lengthBytesUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "UTF32ToString")) RoomsDivisionModule["UTF32ToString"] = function() { abort("'UTF32ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "stringToUTF32")) RoomsDivisionModule["stringToUTF32"] = function() { abort("'stringToUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "lengthBytesUTF32")) RoomsDivisionModule["lengthBytesUTF32"] = function() { abort("'lengthBytesUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "allocateUTF8")) RoomsDivisionModule["allocateUTF8"] = function() { abort("'allocateUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "allocateUTF8OnStack")) RoomsDivisionModule["allocateUTF8OnStack"] = function() { abort("'allocateUTF8OnStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") };

RoomsDivisionModule["writeStackCookie"] = writeStackCookie;

RoomsDivisionModule["checkStackCookie"] = checkStackCookie;

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ALLOC_NORMAL")) Object.defineProperty(RoomsDivisionModule, "ALLOC_NORMAL", { configurable: true, get: function() { abort("'ALLOC_NORMAL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") } });

if (!Object.getOwnPropertyDescriptor(RoomsDivisionModule, "ALLOC_STACK")) Object.defineProperty(RoomsDivisionModule, "ALLOC_STACK", { configurable: true, get: function() { abort("'ALLOC_STACK' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)") } });



var calledRun;



/**

 * @constructor

 * @this {ExitStatus}

 */

function ExitStatus(status) {

  this.name = "ExitStatus";

  this.message = "Program terminated with exit(" + status + ")";

  this.status = status;

}



var calledMain = false;



dependenciesFulfilled = function runCaller() {

  // If run has never been called, and we should call run (INVOKE_RUN is true, and RoomsDivisionModule.noInitialRun is not false)

  if (!calledRun) run();

  if (!calledRun) dependenciesFulfilled = runCaller; // try this again later, after new deps are fulfilled

};



function stackCheckInit() {

  // This is normally called automatically during __wasm_call_ctors but need to

  // get these values before even running any of the ctors so we call it redundantly

  // here.

  // TODO(sbc): Move writeStackCookie to native to to avoid this.

  _emscripten_stack_init();

  writeStackCookie();

}



/** @type {function(Array=)} */

function run(args) {

  args = args || arguments_;



  if (runDependencies > 0) {

    return;

  }



  stackCheckInit();



  preRun();



  // a preRun added a dependency, run will be called later

  if (runDependencies > 0) {

    return;

  }



  function doRun() {

    // run may have just been called through dependencies being fulfilled just in this very frame,

    // or while the async setStatus time below was happening

    if (calledRun) return;

    calledRun = true;

    RoomsDivisionModule['calledRun'] = true;



    if (ABORT) return;



    initRuntime();



    if (RoomsDivisionModule['onRuntimeInitialized']) RoomsDivisionModule['onRuntimeInitialized']();



    assert(!RoomsDivisionModule['_main'], 'compiled without a main, but one is present. if you added it from JS, use RoomsDivisionModule["onRuntimeInitialized"]');



    postRun();

  }



  if (RoomsDivisionModule['setStatus']) {

    RoomsDivisionModule['setStatus']('Running...');

    setTimeout(function() {

      setTimeout(function() {

        RoomsDivisionModule['setStatus']('');

      }, 1);

      doRun();

    }, 1);

  } else

  {

    doRun();

  }

  checkStackCookie();

}

RoomsDivisionModule['run'] = run;



function checkUnflushedContent() {

  // Compiler settings do not allow exiting the runtime, so flushing

  // the streams is not possible. but in ASSERTIONS mode we check

  // if there was something to flush, and if so tell the user they

  // should request that the runtime be exitable.

  // Normally we would not even include flush() at all, but in ASSERTIONS

  // builds we do so just for this check, and here we see if there is any

  // content to flush, that is, we check if there would have been

  // something a non-ASSERTIONS build would have not seen.

  // How we flush the streams depends on whether we are in SYSCALLS_REQUIRE_FILESYSTEM=0

  // mode (which has its own special function for this; otherwise, all

  // the code is inside libc)

  var oldOut = out;

  var oldErr = err;

  var has = false;

  out = err = function(x) {

    has = true;

  }

  try { // it doesn't matter if it fails

    var flush = RoomsDivisionModule['_fflush'];

    if (flush) flush(0);

    // also flush in the JS FS layer

    ['stdout', 'stderr'].forEach(function(name) {

      var info = FS.analyzePath('/dev/' + name);

      if (!info) return;

      var stream = info.object;

      var rdev = stream.rdev;

      var tty = TTY.ttys[rdev];

      if (tty && tty.output && tty.output.length) {

        has = true;

      }

    });

  } catch(e) {}

  out = oldOut;

  err = oldErr;

  if (has) {

    warnOnce('stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the FAQ), or make sure to emit a newline when you printf etc.');

  }

}



/** @param {boolean|number=} implicit */

function exit(status, implicit) {

  EXITSTATUS = status;



  checkUnflushedContent();



  if (keepRuntimeAlive()) {

    // if exit() was called, we may warn the user if the runtime isn't actually being shut down

    if (!implicit) {

      var msg = 'program exited (with status: ' + status + '), but EXIT_RUNTIME is not set, so halting execution but not exiting the runtime or preventing further async execution (build with EXIT_RUNTIME=1, if you want a true shutdown)';

      err(msg);

    }

  } else {

    exitRuntime();

  }



  procExit(status);

}



function procExit(code) {

  EXITSTATUS = code;

  if (!keepRuntimeAlive()) {

    if (RoomsDivisionModule['onExit']) RoomsDivisionModule['onExit'](code);

    ABORT = true;

  }

  quit_(code, new ExitStatus(code));

}



if (RoomsDivisionModule['preInit']) {

  if (typeof RoomsDivisionModule['preInit'] == 'function') RoomsDivisionModule['preInit'] = [RoomsDivisionModule['preInit']];

  while (RoomsDivisionModule['preInit'].length > 0) {

    RoomsDivisionModule['preInit'].pop()();

  }

}



run();









