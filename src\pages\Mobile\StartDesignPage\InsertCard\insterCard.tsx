import React from 'react';
import useStyles from './style';
import search from '../img/search.svg';
import img from '../img/img.svg';
import CAD from '../img/CAD.svg';
import canvas from '../img/canvas.png';
import { ProCard } from '@ant-design/pro-components';
import { useCallback } from 'react';
import type { RcFile } from '@svg/antd/es/upload';
import type { MenuProps } from '@svg/antd';
import { checkIsMobile } from '@/config';
import useUploader from '../hooks/useUploader';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { useStore } from '@/models';

enum CardItem {
  Search = 'search',
  Upload = 'upload',
  CAD = 'CAD',
  Draw = 'draw',
}

interface InsertCardProps {
  toSelectHX: () => void;
}
const InsertCard: React.FC<InsertCardProps> = ({ toSelectHX }) => {
  const { styles } = useStyles();
  const store = useStore();
  const { saveImageToDB, saveDwgToDB, saveInfoToDB } = useUploader();
  // const [HxFormStatus, setHxFormStatus] = useState<0 | 1 | 2 | 3>(0);

  const List = [
    {
      key: CardItem.Search,
      title: '搜索户型图',
      description: '从户型库中搜索户型',
      img: search,
      styles: '#E1F2FF',
    },
    {
      key: CardItem.Upload,
      title: '上传临摹图',
      description: '通过照片草图进行AI生成',
      img: img,
      styles: '#FFF6DE',
    },
    // {
    //   key: CardItem.CAD,
    //   title: '导入CAD图纸',
    //   description: '识别图纸生成准确户型',
    //   img: CAD,
    //   styles: '#FFEFEB',
    // },
    // {
    //   key: CardItem.Draw,
    //   title: '自由绘制',
    //   description: '新建画布进行设计',
    //   img: canvas,
    //   styles: '#E6E2FF',
    // },
  ];

  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
  });

  const handleUploadImg = useCallback((hasForm?: boolean) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (event: any) => {
      const files = event.target.files;
      if (files.length > 0) {
        const file = files[0];
        const preview = await getBase64(file);

        // 保存到 IndexedDB
        // await saveImageToDB(preview);
        LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateImageFile, {imagePath: preview});
        store.homeStore.setShowEnterPage({show: false, source: ''});
        if (hasForm) {
          // 上传成功后打开表单输入户型信息
          // setHxFormStatus(3);
        } else {
          const param = {
            houseTypeName: '未命名',
            source: '3',
            from: 'myScheme',
          };
          console.log(param);
          // saveInfoToDB(param);
        }
      }
    };
    input.click();
  }, []);

  const handleUploadDwg = useCallback((hasForm?: boolean) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.dwg';
    input.onchange = async (event: any) => {
      const files = event.target.files;
      if (files.length > 0) {
        const file = files[0];
        const preview = await getBase64(file);
        // 保存到 DWG 表
        await saveDwgToDB(preview);

        if (hasForm) {
          // 打开表单输入户型信息
          // setHxFormStatus(2);
        } else {
          const param = {
            houseTypeName: '未命名',
            source: '2',
            from: 'myScheme'
          };
          saveInfoToDB(param);
        }
      }
    };
    input.click();
  }, []);


  const handleClick = (key: CardItem) => () => {
    if (key === CardItem.Search) {
      // store.homeStore.setShowEnterPage({show: true, source: null})
      // store.homeStore.setShowStartPage({show: false, source: null})
      toSelectHX()
    } else if (key === CardItem.Upload) {
      handleUploadImg(false);
    } else if (key === CardItem.CAD) {
      handleUploadDwg(false);
    } else if (key === CardItem.Draw) {
      // TODO 进入户型绘制
    }
    
  };

  return (
    <div className={styles.cardContainer}>
      {List.map((item) => (
        <ProCard
          style={{ margin: 20, cursor: 'pointer' }}
          key={item.key}
          // hoverable
          className={styles.card}
          onClick={handleClick(item.key)}
          bodyStyle={{ padding: '15px 20px', height: '72px', background: item.styles }}
        >
          <div className={styles.content}>
            <div className=''>
              <img src={item.img} alt="img" />
            </div>
            <div className={styles.right}>
              <div className={styles.title}>{item.title}</div>
              <div className={styles.desc}>{item.description}</div>
            </div>
          </div>
        </ProCard>
      ))}
    </div>
  );
};

export default InsertCard;
