import { <PERSON><PERSON>ontainer } from "@svg/antd-cloud-design";
import useStyles from "./style"
import { observer } from "mobx-react-lite";
import { t } from "i18next";
import { Button, message } from "@svg/antd";
import { useEffect, useState } from "react";
import { getSchemeInfo } from "../services/scheme";
import { PageContainer } from "@ant-design/pro-components";
import ImgList from "./ImgList/imgList";
import { SchemeEvent } from "../Content/schemeContent";
import { useStore } from "@/models";

type ListItem = {
    area: number;
    layoutSchemeName: string;
    id: string;
    coverImage: string;
    updateDate: string;
    address?: string;
};
interface Props {
    close: () => void;
    schemeItem: any;
    operation: (item: ListItem, event: SchemeEvent) => void
}

const tabConfig = [
    { key: 'layoutImage', label: '布局图' },
    { key: 'colorScreen', label: '彩平图' },
    { key: 'moveLine', label: '动线图' },
    { key: 'effectImage', label: '效果图' },
    { key: 'aidraw', label: 'AI绘图' },
    { key: 'birdEyeImage', label: '鸟瞰图' },
    { key: 'panoramaImage', label: '全景图' },
];


const SchemeDetail: React.FC<Props> = ({close, schemeItem, operation}) => {
    console.log('zzz', schemeItem)

    const store = useStore()
    const [_schemeItem, set_schemeItem] = useState<any>(schemeItem)
    const { styles } = useStyles();
    const [showIdTip, setShowIdTip] = useState(false);
    const [activeKey, setActiveKey] = useState('layoutImage');

    useEffect(() => {
        if(store.homeStore.item_toUpdateDetail?.id === schemeItem?.id){
            set_schemeItem(store.homeStore.item_toUpdateDetail)
        }
    }, [store.homeStore.item_toUpdateDetail])

    // const [scheme, setScheme] = useState<schemeInfoType>();
    // const getData = async () => {
    //     const res = await getSchemeInfo({id: _schemeItem.id});
    //     console.log('zzzx', res);
    //     if (res.success) {
    //         setScheme(res.result);
    //     }
    // };
    
    // useEffect(() => {
    //     getData();
    // }, []);

    const copyToClipboard = async (text: string) => {
        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(text);
                message.success('已复制到剪贴板');
            } else {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                message.success('已复制到剪贴板');
            }
        } catch (err) {
            message.error('复制失败');
        }
    };

    const handleIdClick = () => {
        setShowIdTip(true);
        setTimeout(() => setShowIdTip(false), 2000);
    };

    const infoList = [
        { key: 'id', label: '方案ID', value: _schemeItem?.id, copy: true },
        { key: 'address', label: '小区', value: _schemeItem?.address },
        { key: 'svjSchemeId', label: '户型', value: _schemeItem?.svjSchemeId },
        { key: 'area', label: '使用面积', value: _schemeItem?.area ? `${_schemeItem.area}m²` : '/' },
        { key: 'orientation', label: '朝向', value: _schemeItem?.orientation },
        { key: 'cntactMan', label: '姓名', value: _schemeItem?.cntactMan },
        { key: 'mobile', label: '手机号', value: _schemeItem?.mobile },
        { key: 'panoLink', label: '全景', value: _schemeItem?.panoLink ? <a href={_schemeItem.panoLink} target="_blank" rel="noopener noreferrer">{_schemeItem.panoLink}</a> : '/' },
        { key: 'funcRequire', label: '需求标签', value: _schemeItem?.funcRequire },
        { key: 'updateDate', label: '最后修改', value: _schemeItem?.updateDate },
    ];

    const openEditForm = () => {
        operation(_schemeItem, SchemeEvent.EditScheme)
    }

    const gotoDesign = () => {
        operation(_schemeItem, SchemeEvent.Open)
    }

    return (
        <PanelContainer
            className={styles.panel}
            title={t('方案详情')}
            center={true}
            width={979}
            height={700}
            onClose={close}
            draggable={true}
        >

            <PageContainer
                content={
                    <div className={styles.contain}>
                        <div className={styles.left}>
                            <img src={_schemeItem?.coverImage} alt="" />
                        </div>
                        
                        <div className={styles.right}>
                            <span className={styles.name}>{_schemeItem?.layoutSchemeName}</span>
                            <div className={styles.infoGrid}>
                            {infoList.map((item, idx) => (
                                <div className={styles.infoItem} key={idx}>
                                    <span className={styles.label}>{item.label}：</span>
                                    <span
                                        className={
                                            item.key === 'id'
                                                ? styles.idValue
                                                : styles.value
                                        }
                                        style={item.copy ? { cursor: 'pointer', userSelect: 'none' } : {}}
                                        title={item.copy ? "双击复制" : undefined}
                                        onDoubleClick={item.copy && item.value ? () => copyToClipboard(item.value) : undefined}
                                    >
                                        {item.value || '/'}
                                    </span>
                                </div>
                            ))}
                            </div>

                            <div className={styles.btnContain}>
                            <Button type="primary" className={styles.btnDesign} onClick={openEditForm}>编辑方案信息</Button>
                            <Button type="primary" className={styles.btnDesign} onClick={gotoDesign}>
                                去设计
                            </Button>
                            </div>
                        </div>
                    </div>
                }
                tabList={tabConfig.map(tab => ({
                    key: tab.key,
                    tab: (
                        <span className={activeKey === tab.key ? styles.activeTab : styles.tabTitle}>
                            {tab.label}
                        </span>
                    ),
                    closable: false,
                }))}
                onTabChange={(key) => setActiveKey(key)}
                >
                    <ImgList tabType={activeKey} id={_schemeItem.id} />,
                </PageContainer>
        </PanelContainer>
    )
}

export default observer(SchemeDetail)