// src/Apps/LayoutAI/Services/ViewCameraRules/SofaViewCameraRule.ts
import { ZRect } from "@layoutai/z_polygon";
import { BaseViewCameraRule } from "./BaseViewCameraRule";
import { TRoomEntity } from "../../TRoomEntity";
import { TViewCameraEntity } from "../TViewCameraEntity";
import { Matrix4, PerspectiveCamera, Vector3, Vector3Like, Vector4 } from "three";
import { TRoomShape } from "../../../TRoomShape";
import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../TBaseGroupEntity";
import { TFurnitureEntity } from "../../TFurnitureEntity";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TWindowDoorEntity } from "../../TWinDoorEntity";
import { EdgeProp_3DVisible, MinHallwayWidth } from "../../../IRoomInterface";
import { ZEdge } from "@layoutai/z_polygon";
export class BedRoomViewCameraRule extends BaseViewCameraRule {
    constructor() {
        super();
    }
    // 计算床视角
    static  generateViewCamera(bed_element: TFigureElement, room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}): TViewCameraEntity[] {
        if(!bed_element) return [];
        let view_cameras: TViewCameraEntity[] = [];
        let bed_rect = bed_element?.matched_rect || bed_element?.rect;
        let cabinet_entity = room_entity.furniture_entities.find(f => compareNames([...f.category], ["衣柜"]));
        // 创建视角
        const createViewCamera = (camera_rect: ZRect, point: Vector3, name: string, _target: string[] = [], dir: string = '', ukey: string = '') => {
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(camera_rect);
            view_camera._room_entity = room_entity;
            view_camera.name = name;
            view_camera._is_focus_mode = options.no_focus_mode;
            view_camera._view_center = point.clone();
            view_cameras.push(view_camera);
            view_camera._main_rect = bed_rect;
            view_camera.fov = 75;
            view_camera.hideFurnitures = this.hideFurnitures;
            view_camera._target = _target;
            view_camera._dir = dir;
            view_camera.ukey = ukey;
        }
        /**
         * 视角生成规则，沿着床的四周生成视角，距离1800mm
         */
        for(let edge of bed_rect.edges)
        {
            if(edge.center.distanceTo(bed_rect.backEdge.center) < 10) continue;
            let point = edge.unprojectEdge2d({x: edge.length / 2, y: 1600});
            let bed_camera_rect = new ZRect(500, 500);
            bed_camera_rect.nor = edge.nor.clone().negate();
            bed_camera_rect.rect_center = point.clone();
            bed_camera_rect.zval = 1150;
            let name = '卧室-朝向窗户';
            let _target: string[] = ['床','窗户'];
            let dir: string = '侧面';
            if(edge.center.distanceTo(bed_rect.frontEdge.center) < 10){
                name = '卧室-朝向床正面';
                _target = ['床'];
                dir = '正面';
            }
            if(cabinet_entity && edge.nor.dot(cabinet_entity.rect.nor) > 0.9) {
                name = '卧室-朝向衣柜';
                _target = ['床','衣柜'];
                dir = '侧面';
            }
            createViewCamera(bed_camera_rect, point, name, _target, dir, `bed_view_camera_${edge._edge_id}`);
        }

        
        // 大卧室才加斜对角视角
        if(room_entity._area > 10)
        {
            let fromEdge = this.updateTargetWallEdge(room_entity._room_poly.edges, bed_rect);
            let pointP = null;
            const origin = bed_rect.backEdge.center; // 坐标原点O (backEdge的中心点)
            const angle = 115; // 视角角度（从背靠边X轴顺时针计算）
            const distance = 3400; // 相机距离原点的距离
            // 1. 将角度转换为方向向量（在床的局部2D坐标系中）
            const angleRad = Math.PI * angle / 180;
            // 这里我们传入dir2D作为参数，表示从原点出发的方向
            const left_pointP = bed_rect.backEdge.unprojectEdge2d({
                x: (bed_rect.backEdge.length / 2) - Math.abs((Math.cos(angleRad))*distance),
                y: -Math.abs(Math.sin(angleRad)) * distance 
            });
            const rigth_pointP = bed_rect.backEdge.unprojectEdge2d({
                x: (bed_rect.backEdge.length / 2) + Math.abs((Math.cos(angleRad))*distance),
                y: -Math.abs(Math.sin(angleRad)) * distance
            });
            let left_len = fromEdge.projectEdge2d(left_pointP).x;
            let right_len = fromEdge.projectEdge2d(rigth_pointP).x;
            pointP = (left_len < 0 || left_len > fromEdge.length) ? rigth_pointP : left_pointP;
            // // 3. 计算从原点到edgeStartPoint的方向向量
            const direction3D = pointP.clone().sub(origin).normalize();
            // 创建视角
            let bed_camera_rect = new ZRect(500, 500);
            bed_camera_rect.nor = direction3D.negate();
            bed_camera_rect.rect_center = pointP.clone();
            bed_camera_rect.zval = 1150;
            createViewCamera(bed_camera_rect, pointP, '卧室-斜向床体',['床'],'斜向', 'bed_view_camera_oblique');
        }

        return view_cameras;
    }


    static updateTargetWallEdge = (edges: ZEdge[] , main_rect: ZRect) => {
        let nor = main_rect.frontEdge.nor;
        let m_dist = 999999999;
        let center = main_rect.frontEdge.center;
        let _target_wall_edge = null; 
        for(let edge of edges)
        {
            if((edge.nor.dot(nor) > -0.9)) continue;
            let pp = edge.projectEdge2d(center);
            if(pp.x < 0 || pp.x > edge.length) continue;
            if(!_target_wall_edge || pp.y < m_dist)
            {
                _target_wall_edge = edge;
                m_dist = pp.y;
             
            }


        }
        return _target_wall_edge
    }
}