import { AI2DesignBasicModes } from '@/Apps/AI2Design/AI2DesignManager';
import { EventName } from '@/Apps/EventSystem';
import { FigureDataList } from '@/Apps/LayoutAI/Drawing/FigureImagePaths';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { FigureList } from '@/components';
import { useStore } from '@/models';
import { message } from '@svg/antd';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useStyles from './style';

interface Module {
  image: string;
  png: string;
  title: string;
  label: string;
  icon: string;
}

/**
 * @description 按钮组件
 */
const HotelLayoutMenu: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [figureDataList, setFigureDataList] = useState<Module[]>([]);
  const [structural, setStructural] = useState<Module[]>([]);
  const menuList = FigureDataList;
  const store = useStore();
  let allList: any = [];

  const DRAW_WALL_MENU = [
    [
      {
        title: t('自动排房'),
        icon: 'iconfloorplan_nor',
        command:  EventName.ShowHotelDivisionDialog,
      },
      {
        title: t('区域绘制'),
        icon: 'iconpolygon',
        command:  LayoutAI_Events.DrawingBuildingSpace,
      }
    ],
    // 从形状绘制
    [{
      title: t('标准大床房A'),
      icon: 'iconrectangle1',
      command: "rectangle",
    },
    {
      title: t('标准双床房B'),
      icon: 'iconrectangle1',
      command: "rectangle",
    }
  ]]

  menuList.forEach((item) => {
    item.child.forEach((childItem) => {
      allList = allList.concat(childItem.figureList);
    });
  });
  
  const drawHotelHandle = (command: string) => {
    if(command === EventName.ShowHotelDivisionDialog)
    {
      LayoutAI_App.emit(EventName.ShowHotelDivisionDialog,true);
    }
    if(command === LayoutAI_Events.DrawingBuildingSpace)
    {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.DrawingBuildingSpace, {command:command,space_category:"建筑空间"});

    }
  }
  const object_id = "HotelLayoutMenu";

  const updateCurrentMenuList = () => {

    let mode = LayoutAI_App.instance._current_handler_mode;

  }
  useEffect(() => {

    LayoutAI_App.on_M(EventName.AIDesignModeChanged, object_id, () => {

      updateCurrentMenuList();
    });

    updateCurrentMenuList();

  }, []);


  return (
    <div className={styles.root}>
      <div className={styles.title}>{t('空间规划')}</div>
      <div className={styles.itemBox}>
        {DRAW_WALL_MENU[0].map((item,index) => (
          <div className={styles.item} key={item.command+"_"+index}
            onMouseDown={() => { drawHotelHandle(item.command) }}
          >
            <div className={styles.itemIcon}>
              <svg className="icon" aria-hidden="true" style={{ width: 60 ,height:60 }}>
                <use xlinkHref={`#${item.icon}`}></use>
              </svg>
            </div>
            <div className={styles.desc}>{item.title}</div>
          </div>
        ))}
      </div>

{/* 
      <div className={styles.title}>{t('标准房间')}</div>
      <div className={styles.itemBox}>
        {DRAW_WALL_MENU[1].map((item,index) => (
          <div className={styles.item} key={item.command+"_"+index}
            onMouseDown={() => { drawHotelHandle(item.command) }}
          >
            <div className={styles.itemIcon}>
              <svg className="icon" aria-hidden="true" style={{ width: 60,height:60 }}>
              <use xlinkHref={`#${item.icon}`}></use>
              </svg>
            </div>
            <div className={styles.desc}>{item.title}</div>
          </div>
        ))}
      </div> */}
      {/* <div className={styles.title}>{t('门窗')}</div>
      <FigureList data={figureDataList} />
      <div className={styles.title}>{t('结构件')}</div>
      <FigureList data={structural} /> */}
    </div>
  );
};
export default observer(HotelLayoutMenu);
