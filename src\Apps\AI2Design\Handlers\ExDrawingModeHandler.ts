import { I_MouseEvent } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { AI2DesignBasicModes, AI2DesignManager } from "../AI2DesignManager";
import { SimpleDrawingSubHandler } from "./SubHandlers/ExDrawingSubHandlers/simpleDrawingSubHandler";


/**
 *   扩展绘图模式
 *    --- 尝试一种新的写法
 */
export class ExDrawingModeHandler  extends AI2BaseModeHandler {

    constructor(manager:AI2DesignManager,name:string=AI2DesignBasicModes.ExDrawingMode)
    {
        super(manager,name);
        this._cad_default_sub_handler = new SimpleDrawingSubHandler(this);
        this._transform_elements = [];
    }

    setCadDefaultSubHandler(handler:SimpleDrawingSubHandler)
    {
        this._cad_default_sub_handler = handler;
    }
    enter(state?: number): void {
        super.enter(state);
        // console.log("进入画笔绘制模式");

        this.manager.layer_ExtDrawingBatchLayer.visible = true;
        this.manager.layer_CadSubAreaLayer.visible = true;

        this.update();

    }
    leave(state?: number): void {
        super.leave(state);
        // console.log("退出画笔绘制模式");
        this.manager.layer_ExtDrawingBatchLayer.visible = false;
        this.manager.layer_CadSubAreaLayer.visible = false;
    }


    onmousedown(ev: I_MouseEvent): void {

        if(this._active_sub_handler)
        {
            this._active_sub_handler.onmousedown(ev);
        }
        else if(this._cad_default_sub_handler)
        {
            // 默认方法mouse_down后, 有可能触发进入新的active_handler
            this._cad_default_sub_handler.onmousedown(ev);

            this._last_mouse_down_event = ev;
            if(this._active_sub_handler)
            {
                this._active_sub_handler.onmousedown(ev);
            }


        }

        this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

        this._is_painter_center_moving = false;
        this._is_moving_element = false;
        // super.onmousedown(ev);

    }
    onmousemove(ev: I_MouseEvent): void {

        if(this._active_sub_handler)
        {
            super.onmousemove(ev);
        }
        else{
            if(this._cad_default_sub_handler)
            {
                this._cad_default_sub_handler.onmousemove(ev);
            }

            super.onmousemove(ev);
        }
        // super.onmousemove(ev);
    }
    onmouseup(ev: I_MouseEvent): void {
        if(this._active_sub_handler)
        {
            super.onmouseup(ev);
        }
        else{
            if(this._cad_default_sub_handler)
            {
                this._cad_default_sub_handler.onmouseup(ev);
            }
            super.onmouseup(ev);
        }
        this._last_mouse_down_event = null;

    }

    onwheel(ev: WheelEvent): void {
        if(this._active_sub_handler)
        {
            super.onwheel(ev);
            this._active_sub_handler.onwheel(ev);
        }
        else{
            if(this._cad_default_sub_handler)
            {
                this._cad_default_sub_handler.onwheel(ev);
            }
            super.onwheel(ev);
        }
    }

    drawCanvas(): void {
        if(this.manager.layer_DefaultBatchLayer)
        {
            this.manager.layer_DefaultBatchLayer.drawLayer(false);
        }

        if(this.manager.layer_ExtDrawingBatchLayer)
        {
            this.manager.layer_ExtDrawingBatchLayer.drawLayer(true);
        }

        if(this._active_sub_handler)
        {
            this._active_sub_handler.drawCanvas();
        }
        else if(this._cad_default_sub_handler)
        {
            this._cad_default_sub_handler.drawCanvas();
        }
    }

    runCommand(cmd_name: string): void {
        if(this._active_sub_handler)
        {
            this._active_sub_handler.runCommand(cmd_name);
        }
        else if(this._cad_default_sub_handler)
        {
            this._cad_default_sub_handler.runCommand(cmd_name);
        }
        super.runCommand(cmd_name);
    }
    handleEvent(event_name: string, event_param: any): void {
        super.handleEvent(event_name,event_param);   
    }

}