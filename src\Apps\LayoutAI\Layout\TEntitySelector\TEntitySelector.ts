import { T_TransformElement } from "@/Apps/LayoutAI/Layout/TransformElements/T_TransformElement";
import { ZRect } from "@layoutai/z_polygon";
import { TBaseEntity } from "../TLayoutEntities/TBaseEntity";
import { I_EntitiesCollection, TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { TFurnitureEntity } from "../TLayoutEntities/TFurnitureEntity";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../TLayoutEntities/TBaseGroupEntity";
import { AI_PolyTargetType, DrawingFigureMode, IRoomEntityRealType, IRoomEntityType } from "../IRoomInterface";
import { TRoomEntity } from "../TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "../TLayoutEntities/TSubSpaceAreaEntity";
import { TWall } from "../TLayoutEntities/TWall";
import { TWindowDoorEntity } from "../TLayoutEntities/TWinDoorEntity";
import { TStructureEntity } from "../TLayoutEntities/TStructureEntity";
import { TCombinationEntity } from "../TLayoutEntities/TCombinationEntity";
import { LayoutContainerUtils } from "../TLayoutEntities/utils/LayoutContainerUtils";
import { Vector3, Vector3Like } from "three";
import { TPainter } from "../../Drawing/TPainter";
import { compareNames } from "@layoutai/z_polygon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TBaseSpaceEntity } from "../TLayoutEntities/TSpaceEntities/TBaseSpaceEntity";

export interface I_SelectedTarget
{
    selected_rect : ZRect | null;
    hover_rect : ZRect | null;

    selected_transform_element: T_TransformElement;
    hover_transform_element : T_TransformElement;
    selected_combination_entitys: TFurnitureEntity[];   // 组合内的所有元素


    selected_entity ?: TBaseEntity;
    selected_group_entity ?: TBaseGroupEntity;
    hovered_entity ?: TBaseEntity;
    
    selected_ceiling ?: TFigureElement;

}


export interface I_SelectedEntityInfo
{
    target_entity :TBaseEntity;
    room_entity ?: TRoomEntity;
    space_entity ?: TSubSpaceAreaEntity;
    group_entity ?: TBaseGroupEntity;
    furniture_entity ?: TFurnitureEntity;
    combination_entity ?: TCombinationEntity;
    _draw_combination_entitys ?: TFurnitureEntity[];
    _layout_combination_entitys?: TFurnitureEntity[];
    _matched_combination_entitys?: TFurnitureEntity[];
    _all_sub_entitys?: TFurnitureEntity[];

}


/**
 *  Entity选择器 2025.05.11
 *    --- 将场景中的实体选择动作做封装
 */
export class TEntitySelector
{
    protected _container : TLayoutEntityContainer;

    // 待选实体集合
    protected _candidate_entities_map : Map<string,TBaseEntity>;

    protected _InRoomEntitiesMap : Map<string,I_EntitiesCollection>; 
    protected _InSpaceEntitiesMap : Map<string,I_EntitiesCollection>;    

    protected _target_types : IRoomEntityType[];
    protected _filterConditions : {target_realtypes?: IRoomEntityRealType[],ignore_realtypes?: IRoomEntityRealType[]};

    protected static Key_OutOfRooms = "OutOfRooms";

    protected _selected_info : I_SelectedEntityInfo = null;
    protected _hover_info : I_SelectedEntityInfo = null;

    protected _selected_target : I_SelectedTarget = null;

    protected _transform_elements: T_TransformElement[];

    private _combination_edit: boolean = false;

    public _entityClickedCounter: { target_entity: TBaseEntity; counter: number; } = { target_entity: null, counter: 0 };

    // 记录上一次点击的位置
    private _lastClickPosition: Vector3 | null = null;
    private _clickPositionTolerance: number = 10; // 点击位置容差，像素单位

    constructor(container :TLayoutEntityContainer)
    {
        this._container = container;
        this._candidate_entities_map = new Map();
        this._InRoomEntitiesMap = new Map();
        this._InSpaceEntitiesMap = new Map();

        this._target_types = [];
        this._filterConditions = {};

        this._selected_info = {target_entity:null};
        this._selected_target = null;
    }

    
    bindSelectedTarget(selectTarget:I_SelectedTarget)
    {
        this._selected_target = selectTarget;
    }
    bindTransformers(transform_elements:T_TransformElement[])
    {
        this._transform_elements = transform_elements;
    }
    public get entityClickedCounter(): { target_entity: TBaseEntity; counter: number; } {
        return this._entityClickedCounter;
    }

    get selected_target()
    {
        return this._selected_target;
    }
    get transform_elements()
    {
        return this._transform_elements;
    }
    get combination_edit(): boolean {
        return this._combination_edit;
    }
    set combination_edit(value: boolean) {
        this._combination_edit = value;
    }

    get entity_combiner()
    {
        return this.container.entity_combiner;
    }
    cleanSelection() {
        // this.updateTransformElements();
        if(this.selected_target?.selected_entity)
        {
            this.selected_target.selected_entity.is_selected = false;
        }
        if(this.selected_target?.selected_combination_entitys && this.selected_target?.selected_combination_entitys.length > 0)
        {
            this.selected_target?.selected_combination_entitys.forEach(entity=>entity.is_selected=false);
        }
        this.selected_target.selected_rect = null;
        this.selected_target.hover_rect = null;
        this.selected_target.hover_transform_element = null;
        this.selected_target.selected_transform_element = null;
        this.selected_target.selected_entity = null;
        this.selected_target.hovered_entity = null;
        this.selected_target.selected_combination_entitys = [];
        
        // 重置点击位置记录
        this._lastClickPosition = null;
    }
    get container()
    {
        return this._container;
    }

    get selectedInfo()
    {
        return this._selected_info;
    }

    get hoverInfo()
    {
        return this._hover_info;
    }
    clear()
    {
        this._candidate_entities_map.clear();
        this._InRoomEntitiesMap.clear();
        this._InSpaceEntitiesMap.clear();
    }
    clearCandidates()
    {
        this._candidate_entities_map.clear();
    }
    updateCandidates(targets: IRoomEntityType[] = ["RoomArea", "Wall", "Door", "Window", 
        "StructureEntity", "Furniture"], filter_conditions: {
        target_realtypes?: IRoomEntityRealType[],
        ignore_realtypes?: IRoomEntityRealType[]})
    {
        this.clearCandidates();
        let candidate_entities = this.container.getCandidateEntities(targets,filter_conditions); 

     
        if(targets.includes("Furniture"))
        {
            let group_entities:TBaseGroupEntity[] = candidate_entities.filter((entity)=>entity.type==="BaseGroup" || entity.type==="Group" ) as TBaseGroupEntity[];
            group_entities.forEach(entity=>{
                if(entity.combination_entitys)
                {
                    candidate_entities.push(...entity._combination_entitys);
                }
            })
        }
        if(this.selected_target.selected_entity instanceof TBaseSpaceEntity)
        {
            this.selected_target.selected_entity.childrenSpaces.forEach((entity)=>{
                candidate_entities.push(entity);
            })
        }
        candidate_entities.forEach((entity)=>{
            this._candidate_entities_map.set(entity._uuid,entity);
        });

        // 框选后，将组合实体添加到候选实体中
        if(this.selected_target.selected_entity instanceof TCombinationEntity)
        {
            this._candidate_entities_map.set(this.selected_target.selected_entity._uuid,this.selected_target.selected_entity);
        }

        this.updateInRoomOrSpaceEntities();
    }

    get candidate_entities()
    {
        return Array.from( this._candidate_entities_map.values());
    }

    get valid_room_entities()
    {
        const { _drawing_layer_mode } = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const { _room_entities } = this._container;
        return _drawing_layer_mode === 'SingleRoom'
        ? _room_entities.filter(entity => entity.isSingle)
        : [..._room_entities].sort((a, b) => a._area - b._area);
    }


    /**
     *  <AUTHOR> Wang 2025.05.11
     * 
     *  用天然的房间/分区分割来加速选择
     *  --- 所以绑定一些房间\分区 和 其他图元的信息
     * 
     *  选择的时候, 就优先选择空间, 然后再看其中其他的实体
     * 
     *     暂时还不用考虑Space的信息, 后续到排房的时候再添加
     * 
     * --- 这是2D加速选择器;  
     * --- 3D的加速, 后续可能直接用shader图层的方式
     */
    updateInRoomOrSpaceEntities()
    {
        this._InRoomEntitiesMap.clear();
        this._InSpaceEntitiesMap.clear();
        // 初始化房间外的实体列表
        if(!this._InRoomEntitiesMap.get(TEntitySelector.Key_OutOfRooms))
        {
            this._InRoomEntitiesMap.set(TEntitySelector.Key_OutOfRooms,{});
        }
        let valid_room_entities = this.valid_room_entities;
        // 初始化房间内的实体列表
        valid_room_entities.forEach((targetRoomEntity)=>{
            if(!this._InRoomEntitiesMap.get(targetRoomEntity._uuid))
            {
                this._InRoomEntitiesMap.set(targetRoomEntity._uuid,{
                    _room_entities : [targetRoomEntity]                            
                });
            }
            
            let targetUuid = targetRoomEntity._uuid;
            let collections = this._InRoomEntitiesMap.get(targetUuid);

            let walls = targetRoomEntity.getWallEntities();
            let windows = targetRoomEntity.getWindowEntities();
            let structures = targetRoomEntity.getStructureEntities();
            let entities = [...walls,...windows,...structures];
            entities.forEach((entity)=>{
                if(this._candidate_entities_map.get(entity._uuid))
                {
                    LayoutContainerUtils.addEntityIntoCollection(entity,collections);
                }
            })
        });
        let candidate_entities = this.candidate_entities.filter((entity)=>entity.type!=="Wall"
        &&entity.type!=="StructureEntity"&&entity.type !=="RoomArea");

        // candidate_entities.forEach((entity)=>{
        //     //  这些在前面已经被绑定过了
        //     // 门窗在墙上,墙体在房间外所以房间外集合里也将门窗加上
        //     // 如果墙体需要在布局界面选中就将下面entity.type === "Wall" 条件去掉
        //     if(entity.type === "Wall" || entity.type ==="StructureEntity")
        //     {
        //         return;
        //     }
        //     let targetRoomEntity:TRoomEntity = null;
            
        //     // if(entity instanceof TFurnitureEntity)
        //     // {
        //     //     targetRoomEntity = entity.roomEntity;

        //     // }
        //     if(!targetRoomEntity)
        //     {
        //         targetRoomEntity = valid_room_entities.find((room_entity)=>{
        //             // 如果是空间要拿_main_rect.rect_center去判断
        //             if(entity instanceof TRoomEntity)
        //             {
        //                 return room_entity._main_rect.rect_center.equals(entity._main_rect.rect_center);
        //             }
        //             return room_entity._room_poly.containsPoint(entity.position);
        //         });
        //     }
        //     else{
        //     }

        //     let targetUuid = TEntitySelector.Key_OutOfRooms;
        //     if(targetRoomEntity)
        //     {
        //         if(!this._InRoomEntitiesMap.get(targetRoomEntity._uuid))
        //         {
        //             this._InRoomEntitiesMap.set(targetRoomEntity._uuid,{
        //                 _room_entities : [targetRoomEntity]                            
        //             });
        //         }
        //         targetUuid = targetRoomEntity._uuid;
        //     }
        //     let collections = this._InRoomEntitiesMap.get(targetUuid);
        //     if(!collections) return;

        //     LayoutContainerUtils.addEntityIntoCollection(entity,collections);            
        // });

        candidate_entities.forEach((entity)=>{

            // 找到 candidate_entity 应该归类到的房间（如果在墙体内（240mm偏差内），归类为在该房间中）
            let targetRoomEntities = valid_room_entities.filter((room_entity)=>{
                // 如果是空间要拿_main_rect.rect_center去判断
                if(entity instanceof TRoomEntity)
                {
                    return room_entity._main_rect.rect_center.equals(entity._main_rect.rect_center);
                }

                return room_entity._room_poly.containsPoint(entity.position, 240);
            });

            targetRoomEntities.forEach((targetRoomEntity) => {
                let targetUuid = TEntitySelector.Key_OutOfRooms;

                if(!this._InRoomEntitiesMap.get(targetRoomEntity._uuid))
                {
                    this._InRoomEntitiesMap.set(targetRoomEntity._uuid,{
                        _room_entities : [targetRoomEntity]                            
                    });
                }
                targetUuid = targetRoomEntity._uuid;

                // 所处房间的集合
                let collections = this._InRoomEntitiesMap.get(targetUuid);
                if(!collections) return;

                LayoutContainerUtils.addEntityIntoCollection(entity, collections); 
            })

            if(targetRoomEntities.length === 0){
                let targetUuid = TEntitySelector.Key_OutOfRooms;
                // 所处房间的集合
                let collections = this._InRoomEntitiesMap.get(targetUuid);
                if(!collections) return;
                LayoutContainerUtils.addEntityIntoCollection(entity, collections); 
            }

            // 将所有的实体推入OutOfRooms集合（在房间外均可选中）
            LayoutContainerUtils.addEntityIntoCollection(entity, this._InRoomEntitiesMap.get(TEntitySelector.Key_OutOfRooms)); 
        });
    }

    getTargetInRoomCollectionByPos(pos:Vector3Like)
    {
        let allCollections = Array.from(this._InRoomEntitiesMap.values());
        let targetCollections = allCollections.filter((collection)=>{
            if( collection._room_entities
                && collection._room_entities[0] 
                && collection._room_entities[0]._room_poly.containsPoint(pos))
            {   
                return true;
            }   
            return false;
        });
        if(targetCollections.length > 0)
        {
            const distToRoom = (collection:I_EntitiesCollection)=>{
                let dist = collection._room_entities[0]._room_poly.distanceToPoint(pos);
                return dist;
            }
            targetCollections.sort((a,b)=>{
                return Math.abs(distToRoom(a)) - Math.abs(distToRoom(b));
            })
        }
        let targetCollection = targetCollections[0];
        if(!targetCollection){
            targetCollection = this._InRoomEntitiesMap.get(TEntitySelector.Key_OutOfRooms);
        }
        return targetCollection;
    }
    /**
     *  通过顶点来选择
     */
    selectByPos(pos:Vector3, state:"Select"|"Hover"|"Temp"){
        
        let selection_info : I_SelectedEntityInfo = null;
        if(state === "Select")
        {
            selection_info = this._selected_info = {target_entity:null}
        }
        else if(state==="Hover")
        {
            selection_info = this._hover_info = {target_entity:null};
        }
        else{
            selection_info = {target_entity:null};

        }
        let targetCollection = this.getTargetInRoomCollectionByPos(pos);
        if(!targetCollection)  {
            return selection_info;
        };

        let target_entites : TBaseEntity[] = [];
        for(let key in targetCollection)
        {
            let entites = (targetCollection as any)[key];
            if(entites && entites instanceof Array)
            {
                target_entites.push(...entites);
            }
        }
        // hover 时存在组合内家具移动后组合实体还未更新的情况 因此组内编辑时hover不去寻找组合实体
        let isHoverCombEditState = state === "Hover" && this.combination_edit;

        let max_priority = -999;
        let target_entity : TBaseEntity = null;
        target_entites.forEach((entity)=>{
            if(isHoverCombEditState && entity.type === AI_PolyTargetType.BaseGroup)
            {
                return;
            }
            let dist = (entity.getDistanceForMousePosSelection(pos));
            if (dist < 0) {
                // 点击位置在实体内，选择优先级高的实体
                if (entity._priority_for_selection > max_priority) {
                    max_priority = entity._priority_for_selection;
                    target_entity = entity;
                }
            }
        });

        // 获取所有包含点击位置的分区实体，按距离排序（距离越小越内层）
        let space_dists: {entity: TSubSpaceAreaEntity, distance: number}[] = [];
        let target_space_entities: TSubSpaceAreaEntity[] = [];
        if(targetCollection._sub_area_entities)
        {
            targetCollection._sub_area_entities.forEach((entity)=>{
                let dist = (entity.getDistanceForMousePosSelection(pos,99999));
                if(dist <= 0){
                    space_dists.push({entity, distance: Math.abs(dist)});
                }
            });
            // 按距离排序，距离最小的在前面（最内层的分区在前面）
            space_dists.sort((a, b) => a.distance - b.distance);
            target_space_entities = space_dists.map(item => item.entity);
        }
        let target_group_entity : TBaseGroupEntity = null;
        if( !isHoverCombEditState && targetCollection._basegroup_entities)
        {
            let min_dist = 99999999;
            targetCollection._basegroup_entities.forEach((entity)=>{
                let dist = (entity.getDistanceForMousePosSelection(pos));
                if(dist <= 0 && Math.abs(dist) < Math.abs(min_dist)){
                    min_dist = dist;
                    target_group_entity = entity;
                }
            });
        }
        let target_combination_entity : TCombinationEntity = null;
        if(targetCollection._combination_entities)
        {
            let min_dist = 99999999;
            targetCollection._combination_entities.forEach((entity)=>{
                let dist = (entity.getDistanceForMousePosSelection(pos));
                if(dist <= 0 && Math.abs(dist) < Math.abs(min_dist)){
                    min_dist = dist;
                    target_combination_entity = entity;
                }
            });
        }
        
        let target_furniture_entity : TFurnitureEntity = null;
        if(targetCollection._furniture_entities)
        {
            let min_dist = 99999999;
            targetCollection._furniture_entities.forEach((entity)=>{
                let dist = (entity.getDistanceForMousePosSelection(pos));
                if(dist <= 0 && Math.abs(dist) < Math.abs(min_dist)){
                    min_dist = dist;
                    target_furniture_entity = entity;
                }
            });
        }

        target_entites = null;

        selection_info.target_entity = target_entity;
        selection_info.room_entity = targetCollection._room_entities? targetCollection._room_entities[0]||null :null;
        selection_info.space_entity = target_space_entities?.length > 0 ? target_space_entities[0] : null;
        selection_info.group_entity = target_group_entity;
        selection_info.furniture_entity = target_furniture_entity;
        selection_info.combination_entity = target_combination_entity;
        
        if(selection_info.target_entity)
        {
            if(selection_info.group_entity || selection_info.combination_entity || selection_info.furniture_entity)
            {
                let temp_entities = [selection_info.group_entity,selection_info.combination_entity,selection_info.furniture_entity];
                if(this.combination_edit)
                {
                    temp_entities.length = 0;
                    temp_entities.push(selection_info.furniture_entity,selection_info.group_entity);
                }
                temp_entities = temp_entities.filter((entity)=>entity); // 不为空
                selection_info.target_entity = temp_entities[0] || null;
            }
            
            let loopClickResult = this._getloopClickInRoomAndSubAreaEnt(selection_info,pos,target_space_entities);
            if(loopClickResult)
            {
                selection_info.target_entity = loopClickResult;
                selection_info.space_entity = selection_info.target_entity.type === AI_PolyTargetType.RoomSubArea ? selection_info.target_entity as TSubSpaceAreaEntity : null;
            }

        }
        // 只在Select状态下更新点击位置记录
        if(state === "Select")
        {
            this._lastClickPosition = pos.clone();
        }

        return selection_info;
    }
    
    /**
     * 在房间多个分区之间循环点击
     * @param selection_info 
     */
    private _getloopClickInRoomAndSubAreaEnt(selection_info: I_SelectedEntityInfo, pos: Vector3,target_space_entities: TSubSpaceAreaEntity[]) {
        // 检查是否在相同位置重复点击
        let loopClickResult = null;
        let isSamePositionClick = this._lastClickPosition && this._isPositionNearby(pos, this._lastClickPosition);

        // 处理多个分区嵌套的循环选择逻辑
        if (selection_info.target_entity.type !== AI_PolyTargetType.RoomSubArea) {
            return loopClickResult;
        }
        if (this._entityClickedCounter.target_entity && this._entityClickedCounter.counter >= 1 && isSamePositionClick) {
            // 构建循环选择的实体列表：所有分区（按距离从近到远排序）+ 房间
            // 分区按照嵌套层级排序：最内层 -> 外层 -> 房间
            let temp_entities: TBaseEntity[] = [...target_space_entities, selection_info.room_entity].filter((entity) => entity);
            if (temp_entities.length > 0) {
                let currentIndex = temp_entities.indexOf(this._entityClickedCounter.target_entity);

                if (currentIndex >= 0) {
                    // 选择下一个实体（从内层到外层，最后到房间）
                    let nextIndex = (currentIndex + 1) % temp_entities.length;
                    loopClickResult = temp_entities[nextIndex];
                }
                else {
                    // 如果当前选中的实体不在分区列表中，重新从最内层分区开始
                    loopClickResult = temp_entities[0];
                }
            }
        }else{
            loopClickResult = target_space_entities.length > 0 ? target_space_entities[0] : null;
        }
        return loopClickResult;
    }

    updateExsorbEntities(targets: IRoomEntityType[] = ["RoomArea", "Wall", "Door", "Window", 
    "StructureEntity", "Furniture"], filter_conditions: {
    target_realtypes?: IRoomEntityRealType[],
    ignore_realtypes?: IRoomEntityRealType[],
    target_figure_categories?:string[]}) : TBaseEntity[]
    {
        let selected_entity = this._selected_target.selected_entity;

        if(!selected_entity) return [];
        let collection = this.getTargetInRoomCollectionByPos(selected_entity.position);
        if(!collection) return [];

        let entities : TBaseEntity[] = [];
        for(let key in collection)
        {
            let t_entities :TBaseEntity[] = (collection as any)[key];
            if(t_entities instanceof Array)
            {
                t_entities.forEach((entity)=>{
                    if(entity === selected_entity) return;
                    if(!entity.type) return;
                    if(targets.includes(entity.type))
                    {
                        if(filter_conditions.ignore_realtypes)
                        {
                            if(filter_conditions.ignore_realtypes.includes(entity.realType))
                            {
                                return;
                            }
                        }
                        if(filter_conditions.target_realtypes)
                        {
                            if(!filter_conditions.target_realtypes.includes(entity.realType))
                            {
                                return;
                            }
                        }

                        if(entity.type === "Group" || entity.type === "BaseGroup")
                        {
                            let combination_entitys = (entity as TBaseGroupEntity).combination_entitys;
                            if(combination_entitys && combination_entitys.length > 0)
                            {
                                combination_entitys.forEach((sub_entity)=>{
                                    if(sub_entity === this.selected_target.selected_entity) return;
                                    if(filter_conditions.target_figure_categories)
                                    {
                                        if(compareNames(filter_conditions.target_figure_categories, [sub_entity.category]))
                                        {
                                            entities.push(sub_entity);
                                        }
                                    }
                                    else{
                                        entities.push(sub_entity);
                                    }

                                });
                            }
                        }
                        else{
                        

                            if(entity instanceof TFurnitureEntity && filter_conditions.target_figure_categories)
                            {
                                if(compareNames(filter_conditions.target_figure_categories, [entity.category]))
                                {
                                    entities.push(entity);
                                }
                            }
                            else{
                                entities.push(entity);
                            }

                        }
                        
                    }
                })
            }
        }

        return entities;


    }

    drawCanvas(painter:TPainter)
    {

    }
    updateSelectionState() {
        let candidate_entities = this.candidate_entities;
        for (let entity of candidate_entities) {

            if(!entity) continue;
            if (this.selected_target?.hovered_entity === entity) {
                entity.is_hovered = true;
            }
            else {
                entity.is_hovered = false;
            }
            if (this.selected_target?.selected_entity === entity) {
                entity.is_selected = true;
            }
            else {
                entity.is_selected = false;
            }
        }
    }
    updateSelectedEntityClickedCounter()
    {
        if(this.selected_target.selected_entity)
        {
            if(this._entityClickedCounter.target_entity === this.selected_target.selected_entity)
            {
                this._entityClickedCounter.counter++;
            }
            else{
                this._entityClickedCounter.target_entity = this.selected_target.selected_entity;
                this._entityClickedCounter.counter = 1;
            }
        }
        else{
            this._entityClickedCounter.target_entity = null;
            this._entityClickedCounter.counter = 0;
        }
    }
    drawSelectTarget(painter:TPainter, selectTarget:I_SelectedTarget,options:{is_moving_element?:boolean}={is_moving_element:false})
    {
        if (selectTarget) {

            if (selectTarget.selected_rect) {
                painter.fillStyle = "#04f";
                let entity = TBaseEntity.getEntityOfRect(selectTarget.selected_rect);
                if (entity) {
                    // 如果是地毯，并且不是移动状态，则不绘制地毯
                    let DrawCarpet = entity?.rect?.ex_prop?.label == '地毯' && !options.is_moving_element;
                    if (entity && !DrawCarpet) {

                        if(!DrawCarpet)
                        {
                            entity.drawEntity(painter, {
                                is_selected: true, is_draw_figure: true,
                                is_draw_texture: this.container.drawing_figure_mode === DrawingFigureMode.Texture,
                                is_draw_outline: this.container.drawing_figure_mode === DrawingFigureMode.Outline,
                                draw_decoration: true, is_mobile: this.container.manager.isMoblie
                            });
                        }
                        else{
                            entity.drawEntity(painter,)
                        }

                    }
                    if (DrawCarpet) {
                        this.container.manager.layer_CadFurnitureLayer.onDrawCarpet();
                    }
                }

            }
            if (selectTarget.selected_rect && TBaseEntity.get_polygon_type(selectTarget.selected_rect) !== 'BaseGroup' && selectTarget.selected_combination_entitys) {
                for (let entity of selectTarget.selected_combination_entitys) {
                    if (entity) {
                        entity.drawEntity(painter, {
                            is_selected: false, is_draw_figure: true,
                            is_draw_texture: this.container.drawing_figure_mode === DrawingFigureMode.Texture,
                            is_draw_outline: this.container.drawing_figure_mode === DrawingFigureMode.Outline,
                            draw_decoration: true, is_mobile: this.container.manager.isMoblie
                        });
                    }
                }
            }

            if (selectTarget.hover_rect && selectTarget.hover_rect != selectTarget.selected_rect) {
                painter.fillStyle = "#04f";
                let entity = TBaseEntity.getEntityOfRect(selectTarget.hover_rect);
                if (entity) {
                    if (entity?.rect?.ex_prop?.label=== '地毯') {
                        entity.drawEntity(painter, { is_hovered: true });
                    } else {
                        entity.drawEntity(painter, { 
                            is_hovered: true, 
                            is_draw_texture: this.container.drawing_figure_mode === DrawingFigureMode.Texture, 
                            is_draw_outline: this.container.drawing_figure_mode === DrawingFigureMode.Outline,
                            draw_decoration: true, is_mobile: this.container.manager.isMoblie
                        });
                    }
                }

            }
        }
    }


    updateCombinationTarget()
    {
        if (this.container.entity_selector.combination_edit) return;
        // console.info("CadBaseSubHandler.ondbclick()");
        if (this.selected_target.selected_entity?.type === AI_PolyTargetType.BaseGroup) {
            let entity =  this.selected_target.selected_entity as TBaseGroupEntity;
            let combinationRects: TFurnitureEntity[] = entity.recursivelyAddCombinationRects();
            let combination_target = this.entity_combiner.combination_target;
            combination_target.draw_group_rect = this.selected_target.selected_rect.clone();
            combination_target.draw_group_rect._attached_elements['combination_entitys'] = [...combinationRects];
            combination_target.draw_group_rect._attached_elements['layout_combination_entitys'] = entity._combination_entitys;
            combination_target.draw_group_rect._attached_elements['matched_combination_entitys'] = entity._matched_combination_entitys || [];
            // 这个不添加进撤销回退线程，用作短暂的编辑
            combination_target._draw_combination_entitys = entity.combination_entitys;
            combination_target._group_entity = entity;
            combination_target._layout_combination_entitys = entity._combination_entitys;
            combination_target._matched_combination_entitys = entity._matched_combination_entitys || [];
            combination_target._all_sub_entitys = combinationRects;
            entity.is_selected = false;

            this.container.entity_selector.combination_edit = true;

            
        }
    }

    /**
     * 检查两个位置是否在容差范围内
     */
    private _isPositionNearby(pos1: Vector3, pos2: Vector3): boolean {
        if (!pos1 || !pos2) return false;
        const distance = pos1.distanceTo(pos2);
        return distance <= this._clickPositionTolerance;
    }
}