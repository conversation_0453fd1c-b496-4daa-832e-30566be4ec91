import { Vector3, Vector3<PERSON><PERSON>, Matrix4 } from "three";
import { T_ScaleElement } from "./T_ScaleElement";
import { TPainter } from "../../Drawing/TPainter";
import { TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { I_SelectedTarget } from "../TEntitySelector/TEntitySelector";
import { LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { checkIsMobile } from "@/config";
import { ZPolygon } from "@layoutai/z_polygon";
import { DrawingFigureMode } from "../IRoomInterface";

export class T_ArrowScaleElement extends T_ScaleElement {
    protected _arrow_direction: Vector3; // 箭头的方向向量
    protected _arrow_width: number = 40; // 箭头宽度
    protected _arrow_length: number = 140; // 箭头长度
    protected _arrow_head_size: number = 60; // 箭头头部大小
    protected _offsetX: number = 500; // 偏移
    protected _offsetY: number = 0; // 偏移
    protected _arrow_polygon: ZPolygon; // 箭头多边形

    constructor(
        dir_val: number, 
        nor_val: number, 
        selected_target: I_SelectedTarget, 
        container: TLayoutEntityContainer,
        arrow_direction?: Vector3,
        arrowArea?:ZPolygon
    ) {
        super(dir_val, nor_val, selected_target, container);
        this._element_name = "ArrowScale-(" + this._dir_val + "," + this._nor_val + ")";
        this._arrow_direction = arrow_direction || new Vector3(dir_val, nor_val, 0).normalize();
        this._arrow_polygon = new ZPolygon();
    }

    get visible() {
        return this._target_rect && this._visible ;
    }

    checkEditRect(pos: Vector3Like, _p_sc: number): boolean {
        const is_mobile = checkIsMobile();
        if (!is_mobile || this._container.drawing_figure_mode ===DrawingFigureMode.Texture) return false;
        // 检查点是否在箭头区域内
        if (!this.isTargetRectTypeValid()) return false;
        if (!this._arrow_polygon) return false;
        
        // 检查点是否在箭头多边形内
        return this._arrow_polygon.containsPoint(new Vector3(pos.x, pos.y, pos.z));
    }

    updateElement(): void {
        if (!this._target_rect) return;
        
        // 计算基础位置（物体边缘中点）
        const basePos = this._target_rect.unproject({ 
            x: this._target_rect._w / 2 * this._dir_val, 
            y: this._target_rect._h / 2 * this._nor_val 
        });

        // 计算箭头的基础方向向量
        let arrowDir = new Vector3();

        if (this._dir_val !== 0) {
            // 水平方向的箭头
            arrowDir.copy(this._target_rect.dv).multiplyScalar(this._dir_val);
        } else {
            // 垂直方向的箭头
            arrowDir.copy(this._target_rect._nor).multiplyScalar(this._nor_val);
        }

        // 计算箭头的左侧方向（箭头方向逆时针旋转90度）
        let leftDir = new Vector3(-arrowDir.y, arrowDir.x, 0).normalize();
        
        // 应用偏移：
        // 1. 沿着箭头方向偏移_offsetX
        // 2. 沿着箭头左侧方向偏移_offsetY
        this.pos = basePos.clone()
            .add(arrowDir.multiplyScalar(this._offsetX))
            .add(leftDir.multiplyScalar(this._offsetY));

        let dv = this.pos.clone().sub(this._target_rect.rect_center);
        dv.normalize();

        this.currentLine();
    }

    drawCanvas(painter: TPainter): void {

        if(this._visible && this._align_line)
        {
            if (this.adsorb_refer_pos && this.adsorb_project_pos) {
                painter._context.lineWidth = 2;
                painter.drawLineSegment(this.adsorb_refer_pos, this.adsorb_project_pos, '#f23dd1');
            }

        }
        if (!this.visible) return;
        if (!this.isTargetRectTypeValid()) {
            return;
        }
        const is_mobile = checkIsMobile();
        if (!is_mobile || this._container.drawing_figure_mode ===DrawingFigureMode.Texture) return;
        let sc = painter._p_sc;   
        
        // 根据dir_val和nor_val计算基础旋转角度
        let baseAngle = 0;
        // 计算基础方向
        if(this._dir_val === -1 && this._nor_val === 0) {
            baseAngle = 0;  // 朝上
        } 
        else if(this._dir_val === 1 && this._nor_val === 0) {
            baseAngle = Math.PI;  // 朝下
        }
        else if(this._nor_val === -1 && this._dir_val === 0) {
            baseAngle = Math.PI/2;  // 朝右
        }
        else if(this._nor_val === 1 && this._dir_val === 0) {
            baseAngle = -Math.PI/2;  // 朝左
        }

        let arrowDir = this._target_rect.dv.clone().multiplyScalar(this._dir_val).add(this._target_rect.nor.clone().multiplyScalar(this._nor_val)).normalize();
        arrowDir.normalize();

        let leftDir = arrowDir.clone().cross({x:0,y:0,z:1}).normalize();

        // 根据缩放比例调整箭头大小
        const scaledWidth = this._arrow_width / sc;
        const scaledLength = this._arrow_length / sc;
        const scaledHeadSize = this._arrow_head_size / sc;

        // 创建箭头顶点
        const points = [
            new Vector3(-scaledWidth/2, 0, 0),
            new Vector3(-scaledWidth/2, scaledLength - scaledHeadSize, 0),
            new Vector3(-scaledWidth, scaledLength - scaledHeadSize, 0),
            new Vector3(0, scaledLength, 0),
            new Vector3(scaledWidth, scaledLength - scaledHeadSize, 0),
            new Vector3(scaledWidth/2, scaledLength - scaledHeadSize, 0),
            new Vector3(scaledWidth/2, 0, 0)
        ];

        const tpoints = points.map((p)=>{
            return leftDir.clone().multiplyScalar(p.x).add(arrowDir.clone().multiplyScalar(p.y));
        })

        this._arrow_polygon.initByVertices(tpoints);
        this._arrow_polygon.move(this.pos);        

        const lineWidth = 1; // 边框线宽
        // 绘制箭头多边形
        if (this._arrow_polygon && this.pos) {
            painter.fillStyle = !!this._dir_val? "#f22b0e " : "#0ef232";
            painter.strokeStyle = "#fe0000";
            painter._context.lineWidth = lineWidth;
            painter.fillPolygons([this._arrow_polygon], 0.6);
            
        }

    }
}
