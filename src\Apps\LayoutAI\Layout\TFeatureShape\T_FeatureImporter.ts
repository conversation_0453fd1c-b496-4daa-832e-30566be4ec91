import { TRoom } from "../TRoom";
import { I_TFeatureShape } from "./TFeatureShape";


export class T_FeatureImporter{

    
    static importFeatureShape(shape_data:I_TFeatureShape)
    {
        if(shape_data._room)
        {
             let room = new TRoom(shape_data._room);
            room.updateWindowsTypes();
            room.computeShapeList();
            room.computeFeatureShapes();
            let shape = (room as TRoom).feature_shapes[0];
            shape._updateShapeEdgeProp();
            shape.updateShapeCode();
            shape._room = room;
            return shape;
        }
        // let shape : TFeatureShape = null;
        // if(shape_data.shape_code=="L")
        // {
        //     shape = new T_L_Shape();
        // }
        // else if(shape_data.shape_code=="R")
        // {
        //     shape = new T_R_Shape();
        // }
        // else if(shape_data.shape_code.indexOf("R")==0)
        // {
        //     let num = ~~shape_data.shape_code[1];
        //     shape = new T_Rk_Shape(num);
        // }
        // else if(shape_data.shape_code == "S")
        // {
        //     shape = new T_S_Shape();
        // }
        // else if(shape_data.shape_code == "T")
        // {
        //     shape = new T_T_Shape();
        // }
        // else{
        //     new T_8V_Shape();
        //     console.log(shape_data);
        // }
        // if(shape)
        // {
        //     shape.importData(shape_data);

        //     // return shape;
        // }
        // return shape;
    }
}
