import React, { useState, useEffect } from 'react';
import { <PERSON>Container, Search } from '@svg/antd-cloud-design';
import { useStore } from '@/models';
import { Button, Input, MenuProps, message, Modal, Pagination, PaginationProps, Typography } from '@svg/antd';
import GridList from '../GridList';
import useStyles from "./style"
import NoData from '../NoData/noData';
import HeaderSearch from '../HeaderSearch';
import { copySheme, deleteScheme, editSchemeInfo, listByPage } from '../services/scheme';
import SchemeEditForm from '../SchemeEditForm';
import TableList from '../TableList';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import SchemeDetail from '../SchemeDetail/schemeDetail';

type ListItem = {
    area: number;
    layoutSchemeName: string;
    id: string;
    coverImage: string;
    updateDate: string;
    address?: string;
};

interface Props {
    source?: string | null
}


export enum SchemeEvent {
    Open = 'open',
    NewPageOpen = 'newPageOpen',
    ViewGallery = 'viewGallery',
    DeleteScheme = 'deleteScheme',
    CopyScheme = 'copyScheme',
    RenameScheme = 'renameScheme',
    ShareScheme = 'shareScheme',
    EditScheme = 'editScheme',
}

const allItems: MenuProps['items'] = [
    {
        key: SchemeEvent.Open,
        label: '打开方案',
    },
    {
        key: SchemeEvent.NewPageOpen,
        label: '新标签页打开方案',
    },
    {
        key: SchemeEvent.ViewGallery,
        label: '查看方案图册',
    },
    {
        type: 'divider',
    },
    {
        key: SchemeEvent.CopyScheme,
        label: '创建副本',
    },
    {
        key: SchemeEvent.RenameScheme,
        label: '重命名',
    },
    {
        key: SchemeEvent.ShareScheme,
        label: '分享方案',
        disabled: true,
    },
    {
        key: SchemeEvent.EditScheme,
        label: '编辑方案信息',
    },
    {
        type: 'divider',
    },
    {
        key: SchemeEvent.DeleteScheme,
        label: '删除',
    },
];

const itemsToDisplay: MenuProps['items'] = [
    {
        key: SchemeEvent.Open,
        label: '打开方案',
    },
    // {
    //     key: SchemeEvent.ViewGallery,
    //     label: '查看方案图册',
    // },
    {
        type: 'divider',
    },
    {
        key: SchemeEvent.CopyScheme,
        label: '创建副本',
    },
    {
        key: SchemeEvent.RenameScheme,
        label: '重命名',
    },
    {
        key: SchemeEvent.EditScheme,
        label: '编辑方案信息',
    },
    {
        type: 'divider',
    },
    {
        key: SchemeEvent.DeleteScheme,
        label: '删除',
    },
];

const SchemeContent: React.FC<Props> = ({source}) => {
    const realSource = source ?? null
    const store = useStore();
    const { styles } = useStyles();

    const [showType, setShowType] = useState<String>('card');
    const [sort, setSort] = useState('update_date desc');
    const [time, setTime] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalItems, setTotalItems] = useState(0);
    const [searchValue, setSearchValue] = useState<string>();
    const [isSearch, SetIsSearch] = useState(false);
    const [loading, setLoading] = useState(true);
    const [showDetail, setShowDetail] = useState(false)
    const [detailId, setDetailId] = useState(null)

    const updateType = (data: any) => {
        setShowType(data);
    };
    const selectedSort = (data: any) => {
        setSort(data);
    };
    const selectedTime = (data: any) => {
        setTime(data);
    };

    const refresh = () => {
        setCurrentPage(1);
        getSchemeList();
    };

    const [deleteVisible, setDeleteVisible] = useState(false);
    const [copyVisible, setCopyVisible] = useState(false);
    const [renameVisible, setRenameVisible] = useState(false);
    const [newSchemeName, setNewSchemeName] = useState('');
    const [isEditFormVisible, setEditFormVisible] = useState(false);
    const [currentItem, setCurrentItem] = useState<ListItem | undefined>(undefined);
    const [propItemForDetail, setPropItemForDetail] = useState(undefined)

    const onClickOpenLayoutScheme = (layoutSchemeData: any) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.Init, null);
        LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, layoutSchemeData);
        store.homeStore.setShowMySchemeList(false);
        LayoutAI_App.emit(EventName.OpenHouseSearching, false);
    }

    const operation = (item: ListItem, event: SchemeEvent) => {
        if (event === SchemeEvent.Open) {
            // window.parent.postMessage({type: 'open', message: `打开方案${item.id}`, data: item}, '*')
            onClickOpenLayoutScheme(item)
            store.homeStore.setShowEnterPage({show: false, source: ''})
        } else if (event === SchemeEvent.NewPageOpen) {
            // window.open(`${envConfig.layoutHostDomain}/editor/?schemeId=${item.id}`);
        } else if (event === SchemeEvent.ViewGallery) {
            setPropItemForDetail(item)
            setShowDetail(true)
            setDetailId(item?.id)
        } else if (event === SchemeEvent.DeleteScheme) {
            setDeleteVisible(true);
            setCurrentItem(item);
        } else if (event === SchemeEvent.CopyScheme) {
            setCopyVisible(true);
            setCurrentItem(item);
        } else if (event === SchemeEvent.RenameScheme) {
            setRenameVisible(true);
            setCurrentItem(item);
            setNewSchemeName(item.layoutSchemeName);
        } else if (event === SchemeEvent.ShareScheme) {
            //
        } else if (event === SchemeEvent.EditScheme) {
            setCurrentItem(item);
            setEditFormVisible(true);
        }
    };

    // 重命名操作
    const handleRename = async () => {
        const res = await editSchemeInfo({ id: currentItem?.id, layoutSchemeName: newSchemeName });
        if (res.success) {
            message.success('重命名成功');
            refresh();
        }
        setRenameVisible(false);
    };
    // 删除操作
    const deleteConfirm = async () => {
        const res = await deleteScheme({ ids: [currentItem?.id] });
        if (res.success) {
            setDeleteVisible(false);
            message.success('删除成功');
            setCurrentItem(undefined);
            refresh();
        } else {
            message.error(res.errorMessage);
        }
    };

    // 创建副本操作
    const copyConfirm = async () => {
        const res = await copySheme({ id: currentItem?.id });
        if (res.success) {
            setCopyVisible(false);
            message.success('创建副本成功');
            setCurrentItem(undefined);
            refresh();
        } else {
            message.error(res.errorMessage);
        }
    };

    const hideDeleteModal = () => {
        setDeleteVisible(false);
    };
    const hideCopyModal = () => {
        setCopyVisible(false);
    };

    const handleEditCancel = () => {
        setEditFormVisible(false);
    };

    useEffect(() => {
        getSchemeList();
    }, [sort, time, currentPage, isSearch]);

    useEffect(() => {
        setCurrentPage(1);
    }, [showType]);

    const [schemeList, setSchemeList] = useState([]);

    // 平滑滚动到页面顶部
    const scrollToTop = () =>
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });

    const handleSearch = (value: string) => {
        setSearchValue(value);
        SetIsSearch(!isSearch);
    };

    const getSchemeList = async () => {
        const params: any = {
            pageIndex: currentPage,
            pageSize: 18,
            orderBy: sort,
        };

        if (sort === 'create_date desc') {
            if (time === 'range1') {
                params.createTimeRange = 1;
            } else if (time === 'range3') {
                params.createTimeRange3 = 1;
            } else if (time === 'range6') {
                params.createTimeRange6 = 1;
            }
        } else if (sort === 'update_date desc') {
            if (time === 'range1') {
                params.updateTimeRange = 1;
            } else if (time === 'range3') {
                params.updateTimeRange3 = 1;
            } else if (time === 'range6') {
                params.updateTimeRange6 = 1;
            }
        }

        if (searchValue) {
            params.keyWord = searchValue;
        }

        setLoading(true);

        const res = await listByPage(params);
        if (res && res.success) {
            scrollToTop();
            setSchemeList(res.result.result);
            setTotalItems(res.result.recordCount);

            if(showDetail){
                const newItemForDetail = res.result.result.find((item: any) => item.id === detailId)
                if(newItemForDetail) store.homeStore.set_item_toUpdateDetail(newItemForDetail)
            }
        }
        

        setLoading(false);
    };

    useEffect(() => {
        const notifyParentPage = (status: string, message: string) => {
            window.parent.postMessage({
                type: 'iframeStatus',
                status,
                message,
            }, '*');
        };
        if (!loading) {
            notifyParentPage('loaded', 'Content 已加载完成');
        }
    }, [loading]);

    const CardPagination: React.FC = () => {
        const onChange: PaginationProps['onChange'] = (page) => {
            setCurrentPage(page);
        };

        return (
            <div key="cardPagination" className={styles.PageContainer}>
                <Pagination
                    simple={true}
                    defaultCurrent={1}
                    current={currentPage}
                    onChange={onChange}
                    total={totalItems}
                    pageSize={18}
                    showSizeChanger={false}
                />
            </div>
        );
    };

    const renderChildren = () => {
        if (totalItems === 0) {
            return <NoData />;
        } else {
            return [
                showType === 'card'
                    ? (<div style={{padding: '0 20px'}}>
                        <GridList key="gridList" list={schemeList} operation={operation} refresh={refresh} itemsToDisplay={itemsToDisplay}/>
                        {totalItems > 18 && <CardPagination />}
                    </div>)
                    : (<div style={{padding: '0 20px'}}>
                        <TableList key="tableList" list={schemeList} operation={operation} itemsToDisplay={itemsToDisplay}/>
                        {totalItems > 18 && <CardPagination />}
                    </div>),
            ];
        }
    };

    useEffect(() => {
        // 加载 JSON 文件
        fetch('https://3vj-fe.3vjia.com/fe_oss_prod/static/swj/district/prod/district-v3.json')
            .then((response) => response.json())
            .then((data) => store.homeStore.setDistrictData(data));
    }, []);

    useEffect(() => {
        const notifyParentPage = () => {
            window.parent.postMessage({
                type: 'iframeLoaded',
                message: 'Content 已加载完成',
            }, '*');
        };
        if (!loading) {
            notifyParentPage();
        }
    }, [loading]);

    const closeDetail = () => {
        setShowDetail(false)
    }

    return (
        <div className={styles.container}>
            <div className={styles.headerForSearch}>
                <div className={styles.search}>
                    <Search
                        placeholder="请输入"
                        onSearch={handleSearch}
                        onBlur={(e) => handleSearch(e.target.value)}
                    />
                </div>
                <HeaderSearch
                    updateData={updateType}
                    selectedSort={selectedSort}
                    selectedTime={selectedTime}
                />
            </div>

            <div className={styles.scrollContainer}>
                {loading
                    ? <></>
                    : renderChildren()
                }
            </div>

            <Modal
                title="删除方案"
                open={deleteVisible}
                onOk={deleteConfirm}
                onCancel={hideDeleteModal}
                className={styles.deleteModal}
                style={{zIndex: 12000}}
                footer={[
                    <Button key="back" onClick={hideDeleteModal} className={styles.cancelButton}>
                        取消
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        onClick={deleteConfirm}
                        className={styles.submitButton}
                    >
                        确定
                    </Button>,
                ]}
            >
                <div className={styles.deleteMessage}>
                    <p>确定删除该方案?</p>
                    <p>删除后30天内可以在【回收站】中恢复!</p>
                </div>
            </Modal>

            <Modal
                title="创建副本"
                open={copyVisible}
                onOk={copyConfirm}
                onCancel={hideCopyModal}
                style={{zIndex: 12000}}
                // className={styles.copyModal}
                footer={[
                    <Button key="back" onClick={hideCopyModal} className={styles.cancelButton}>
                        取消
                    </Button>,
                    <Button key="submit" type="primary" onClick={copyConfirm} className={styles.submitButton}>
                        确定
                    </Button>,
                ]}
            >
                {/* <div className={styles.copyMessage}> */}
                <div>
                    <p>确定创建该方案的副本吗？</p>
                </div>
            </Modal>

            <Modal
                title="重命名方案"
                open={renameVisible}
                onOk={handleRename}
                style={{zIndex: 12000}}
                onCancel={() => setRenameVisible(false)}
                footer={[
                    <Button
                        key="back"
                        onClick={() => setRenameVisible(false)}
                        className={styles.cancelButton}
                    >
                        取消
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        onClick={handleRename}
                        className={styles.submitButton}
                    >
                        确定
                    </Button>,
                ]}
            >
                <div style={{ display: 'flex', alignItems: 'center', padding: '10px 0' }}>
                    <Typography.Text style={{ width: '100px' }} strong>
                        方案名称：
                    </Typography.Text>
                    <Input
                        value={newSchemeName}
                        onChange={(e) => setNewSchemeName(e.target.value)}
                        placeholder="输入新方案名称"
                    />
                </div>
            </Modal>
            {isEditFormVisible && currentItem && (
                <SchemeEditForm data={currentItem} refresh={refresh} handleEditClose={handleEditCancel} />
            )}

            {showDetail && <SchemeDetail close={closeDetail} schemeItem={propItemForDetail} operation={operation}></SchemeDetail>}
        </div>
    );
};

export default SchemeContent;