import { makeAutoObservable } from 'mobx';
import { CombMenuObjData } from '@/pages/AddGroup/typing';

export interface metaImage {
    createDate: string;
    createUser: string;
    enable: number;
    id: string;
    isDelete: number;
    metaCateId: string;
    metaData: string,
    metaHeight: number,
    metaImage: string,
    metaLength: number,
    metaName: string,
    metaType: string,
    metaWidth: number,
    modelloc: string,
    subCateId: string,
    tenantId: string,
    updateDate: string,
    updateUser: string, 
  }

class AddGroupStore {
    addGroupData = {
        data: {} as CombMenuObjData,
        selected_key: '',
        sizeObj: {} as metaImage,
        type: '' as 'addMetaGroup' | 'addsizeGroup'
    } as {data: CombMenuObjData, selected_key: string, sizeObj: metaImage, type: 'addMetaGroup' | 'addsizeGroup'};
    constructor() {
        makeAutoObservable(this, {}, {autoBind: true});
    }
    setAddGroupData(data: {data: CombMenuObjData, selected_key: string, sizeObj: metaImage, type: 'addMetaGroup' | 'addsizeGroup'}) {
        this.addGroupData = data;
    }
}

export default AddGroupStore;