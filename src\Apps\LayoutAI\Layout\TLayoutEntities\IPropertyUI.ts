


/**
 *  用于跟UI交互的属性对象
 */
export interface IPropertyUI
{
    /**
     *  属性key
     */
    _key ?: string;


    name : string;
    type? :  'string' | 'object' | 'array' | 'number' | 'boolean' | 'void' | 'date' | 'datetime' | 'block' ;
    widget: 'LabelItem'|'SlideItem'|'infoItem' |'ButtonItem'|'RotationItem' | 'LineItem' | 'CustomItem' | "ColorWidget" | "SubAreaWidget";
    defaultValue? : string | number | boolean;
    min ?: number;
    max ?: number;
    step?:number;
    value? : {
        id?: string;
        name?: string;
        size?: string;
        hideId?: boolean;
        imageUrl?: string;
        type?: string;
    },
    props? : {
        type? : "input"|"select"|"number"|"checkbox"|"switch";
        suffix?: string;
        options ?: {label:string,value:string}[];
        optionWidth ?: number;
        [key:string]:any;
        space?:number;
        fullScreen?:boolean;
    },
    width?:number|string;
    rotationZ?:number;
    editable ?: boolean;
    disabled ?: boolean;
    onChange ?: (value:number|string)=>void;
    onClick ?:()=>void;
}