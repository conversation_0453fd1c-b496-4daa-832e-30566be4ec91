
import { LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { HouseDesignSubHandler } from "./HouseDesignBaseHandler";
import { Vector3 } from "three";
import { ZRect } from "@layoutai/z_polygon";
export class MoveCopyImageHandler extends HouseDesignSubHandler {

    _last_pos: Vector3;
    _origin_shape_rect: ZRect
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this._last_pos = null;
        this._origin_shape_rect = null;
    }

    onmousedown(ev: I_MouseEvent): void {
        this._last_pos = new Vector3(ev.posX, ev.posY, 0);
        if(this.container.copyImageRect)
        {
            this.container.copyImageRect._attached_elements.is_selected = true;
            this._origin_shape_rect = this.container.copyImageRect.clone();
        }
        this.update();

    }
    onmousemove(ev: I_MouseEvent): void {
        if (ev.buttons != 1) return;
        this._cad_mode_handler._is_moving_element = true;
        let copy_image_rect = this.container.copyImageRect;
        if(copy_image_rect)
        {   
            let current_pos = new Vector3(ev.posX, ev.posY, 0); 
            let movement = current_pos.clone().sub(this._last_pos);
            copy_image_rect.rect_center = this._origin_shape_rect.rect_center.clone().add(movement);
            copy_image_rect.updateRect();
        }
        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        this.container.copyImageRect._attached_elements.is_selected = false;
        this._cad_mode_handler._is_moving_element = false;
        this.manager.layer_CadCopyImageLayer.visible = true;
        this.update();
        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
    }

    drawCanvas(): void {
        if(this.container.copyImageRect)
        {
            this.painter._context.globalAlpha = 0.5;
            this.painter.fillImageInRect(this.container.copyImageRect._attached_elements.img, this.container.copyImageRect);
        }
    }

}