import { PageContainer } from '@ant-design/pro-components';
import { Button, message, Tooltip, Typography } from '@svg/antd';
import styles from './index.less';
import React, { useEffect, useState } from 'react';
import { Params, useParams } from 'react-router-dom';

import IconFont from '@/components/IconFont/iconFont';

import { getSchemeInfo } from '../services/scheme';
import SchemeEditForm from '../SchemeEditForm';
import ImgList from '../ImgList';

type schemeInfoType = {
  area: number;
  layoutSchemeName: string;
  id: string;
  coverImage: string;
  updateDate: string;
  address?: string;
  houseType?: string;
  orientation?: string;
  cntactMan?: string;
  mobile?: string;
  panoLink?: string;
  funcRequire?: string;
};

const SchemeDetail: React.FC = () => {
  const id = useParams();
  const [scheme, setScheme] = useState<schemeInfoType>();
  const [activeKey, setActiveKey] = useState('layoutImage');
  const [isEditFormVisible, setEditFormVisible] = useState(false);

  const getData = async () => {
    const res = await getSchemeInfo(id);
    console.log(res);
    if (res.success) {
      setScheme(res.result);
    }
  };

  const tablist = [
    {
      key: 'layoutImage',
      tab: (
        <span className={activeKey === 'layoutImage' ? styles.activeTab : styles.tabTitle}>
          布局图
        </span>
      ),
      closable: false,
    },
    {
      key: 'colorScreen',
      tab: (
        <span className={activeKey === 'colorScreen' ? styles.activeTab : styles.tabTitle}>
          彩平图
        </span>
      ),
      closable: false,
    },
    {
      key: 'moveLine',
      tab: (
        <span className={activeKey === 'moveLine' ? styles.activeTab : styles.tabTitle}>
          动线图
        </span>
      ),
      closable: false,
    },
    {
      key: 'effectImage',
      tab: (
        <span className={activeKey === 'effectImage' ? styles.activeTab : styles.tabTitle}>
          效果图
        </span>
      ),
      closable: false,
    },
    {
      key: 'aidraw',
      tab: (
        <span className={activeKey === 'aidraw' ? styles.activeTab : styles.tabTitle}>
          AI绘图
        </span>
      ),
      closable: false,
    },
    {
      key: 'birdEyeImage',
      tab: (
        <span className={activeKey === 'birdEyeImage' ? styles.activeTab : styles.tabTitle}>
          鸟瞰图
        </span>
      ),
      closable: false,
    },
    {
      key: 'panoramaImage',
      tab: (
        <span className={activeKey === 'panoramaImage' ? styles.activeTab : styles.tabTitle}>
          全景图
        </span>
      ),
      closable: false,
    },
  ]

  useEffect(() => {
    getData();
  }, []);


  const gotoDesign = () => {
    console.log(id);
    // window.open(`${envConfig.layoutHostDomain}/editor/?schemeId=${id.id}`);

    window.parent.postMessage({type: 'open', data: scheme}, '*')
  };

  const handleEditCancel = () => {
    setEditFormVisible(false);
  };
  const editForm = () => {
    setEditFormVisible(true);
  }

  const refresh = async() => {
    const res = await getSchemeInfo(id);
    if (res.success) {
      setScheme(res.result);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      // 尝试使用现代 Clipboard API
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text);
        message.success('已复制到剪贴板');
      } else {
        // 回退到旧的 execCommand 方法
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
        message.success('已复制到剪贴板');
      }
    } catch (err) {
      console.error('复制失败:', err);
      message.error('复制失败');
    }
  };

  return (
    <>
      <PageContainer
        header={{
          title: (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                style={{ cursor: 'pointer', marginRight: '8px' }}
                onClick={() => window.history.back()}
              >
                <IconFont type='icon-jiantou' />
              </span>
              方案详情
            </div>
          ),
        }}
        content={
          <div>
            <hr style={{ margin: '-18px -40px 20px -40px', border: 'none', borderTop: '1px solid #e5e5e5' }} />
            <div className={styles.contain}>
              <div className={styles.left}>
                <img src={scheme?.coverImage} alt="" />
              </div>
              <div className={styles.right}>
                <span className={styles.name}>{scheme?.layoutSchemeName}</span>
                <div className={styles.schemeDetailsContainer}>
                  <div className={styles.schemeDetails}>
                    <div className={styles.label}>方案ID：</div>
                    <Tooltip>
                      <Typography.Text title={"双击复制"}>
                        <div 
                          className={styles.value} 
                          onDoubleClick={() => scheme?.id && copyToClipboard(scheme.id)} 
                          style={{cursor: 'pointer', userSelect: 'none'}} 
                          title="双击复制">
                            {scheme?.id || '/'}
                        </div>
                      </Typography.Text>
                    </Tooltip>
                    <div className={styles.label}>小区：</div>
                    <div className={styles.value}>{scheme?.address || '/'}</div>
                    <div className={styles.label}>户型：</div>
                    <div className={styles.value}>{scheme?.houseType || '/'}</div>
                    <div className={styles.label}>使用面积：</div>
                    <div className={styles.value}>{scheme?.area ? `${scheme.area}m²` : '/'}</div>
                    <div className={styles.label}>朝向：</div>
                    <div className={styles.value}>{scheme?.orientation || '/'}</div>
                    
                  </div>
                  <div className={styles.schemeDetails}>
                    <div className={styles.label}>姓名：</div>
                      <div className={styles.value}>{scheme?.cntactMan || '/'}</div>
                      <div className={styles.label}>手机号：</div>
                      <div className={styles.value}>{scheme?.mobile || '/'}</div>
                      <div className={styles.label}>全景：</div>
                      <div className={styles.value}>
                        {scheme?.panoLink ? <a href={scheme?.panoLink} target="_blank" rel="noopener noreferrer">{scheme?.panoLink}</a> : '/'}
                      </div>
                      <div className={styles.label}>需求标签：</div>
                      <div className={styles.value}>
                        {scheme?.funcRequire || '/'}
                      </div>
                      <div className={styles.label}>最后修改时间：</div>
                      <div className={styles.value}>{scheme?.updateDate || '/'}</div>
                  </div>
    
                  </div>
                <div className={styles.btnContain}>
                  <Button type="primary" className={styles.btnDesign} onClick={editForm}>编辑方案信息</Button>
                  <Button type="primary" className={styles.btnDesign} onClick={gotoDesign}>
                    去设计
                  </Button>
                  {/* <Button className={styles.btnPPT}>生成提案PPT</Button> */}
                </div>
              </div>
            </div>
          </div>
        }
        // extra={[<RightContent key="rightContent" onSearch={handleSearch} />]}
        tabList={tablist}
        onTabChange={(key) => setActiveKey(key)}
      >
        <ImgList tabType={activeKey} id={id} />,
      </PageContainer>

      {isEditFormVisible && <SchemeEditForm data={scheme!} refresh={refresh} handleEditClose={handleEditCancel} />}
    </>
  );
};

export default SchemeDetail;
