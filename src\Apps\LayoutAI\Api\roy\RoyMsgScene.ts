import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { IframeBusinessType, IframeSceneStatus, IframeMessage, IframeMessageType, IframeSceneStatusType } from "./RoyEngineProtocol";
import { RoyMsgEngine } from "./RoyMsgEngine";

export enum RoySceneEvents {
    SceneInitFinished = "SceneInitFinished",
}


/**
* @description 场景消息处理
* <AUTHOR>
* @date 2025-04-11
* @lastEditTime 2025-04-11 14:27:34
* @lastEditors xuld
*/
export class RoyMsgScene {
    private static _isInitFinishedScene: boolean = false;
    private static _isInitFinishedEngine: boolean = false;
    private static _iframeRef: HTMLIFrameElement;

    public static onMessage(msg: IframeMessage) {
        switch (msg.handleType) {
            case IframeMessageType.SceneStatus:
                let data = msg.data as IframeSceneStatus;
                if (data.status == IframeSceneStatusType.EngineInit) {
                    this._isInitFinishedEngine = true;
                }
                else if (data.status == IframeSceneStatusType.SceneInit) {
                    if (data.success) {
                        this._isInitFinishedScene = true;
                        LayoutAI_App.instance.EventSystem.emit(RoySceneEvents.SceneInitFinished);
                    } else {
                        console.error(data.msg);
                    }
                }
                else if (data.status == IframeSceneStatusType.ClearScene) {
                    this._isInitFinishedScene = false;
                }
                break;
            case IframeMessageType.ChooseObject:
                console.log(JSON.stringify(msg.data));
                break;
            default:
                console.warn("unknown cmd type", JSON.stringify(msg));
                break;
        }
    }

    public static setIframeRef(iframeRef: HTMLIFrameElement) {
        this._iframeRef = iframeRef;
    }

    public static postMessage(msg: { handleType: IframeMessageType; data?: object | string; }) {
        if (!this._isInitFinishedEngine) {
            console.warn("engine not init finished");
            return;
        }

        if (!this._isInitFinishedScene) {
            console.warn("scene not init finished");
            return;
        }

        if (!this._iframeRef) {
            console.warn("iframeRef not set");
            return;
        }

        RoyMsgEngine.postMessage(this._iframeRef, {
            businessType: IframeBusinessType.SvgRoyScene,
            handleType: msg.handleType,
            data: msg.data || ""
        });
    }
}