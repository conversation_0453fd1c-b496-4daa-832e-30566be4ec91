
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { ZPolygon } from "@layoutai/z_polygon";
import { TAppManagerBase } from "../../AppManagerBase";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";

export class TCadRoomNameLayer extends TDrawingLayer
{
    


    _target_poly : ZPolygon;

    _name_mode : number = 0;

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadRoomStrucure,manager);
        this._target_poly = null;
        this._dirty = true;
    
    }
    
    get room_list()
    {
        return this.layout_container._rooms;
    }
     _updateLayerContent(): void {

    }
    
    onDraw(): void {
       
        if(this.layout_container._drawing_layer_mode === "SingleRoom") return; // 单空间模式 不绘制

        let entities = this.layout_container.getCandidateEntities(["RoomArea"]);

        for(let entity of entities)
        {
            if(entity)
            {
                if(TBaseEntity.is_deleted(entity.rect)) continue;
                entity.drawEntity(this.painter,{is_draw_figure:false,is_show_room_id:this._name_mode==1,is_draw_roomname: true});
            }

            
        }
        this.painter.strokeStyle= "#a42";
        this.painter.fillStyle = "#fff";
    }
}