"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4516],{34516:function(t,e,n){n.d(e,{A:function(){return O}});var s=n(5285),i=n(74484),a=n(56505);function o(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,(0,a.A)(t,e)}function r(t,e){return t.replace(new RegExp("(^|\\s)"+e+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}var p=n(41594),l=n.n(p),u=n(75206),c=n.n(u),d=!1,f=l().createContext(null),E=function(t){return t.scrollTop},h="unmounted",x="exited",m="entering",v="entered",C="exiting",g=function(t){function e(e,n){var s;s=t.call(this,e,n)||this;var i,a=n&&!n.isMounting?e.enter:e.appear;return s.appearStatus=null,e.in?a?(i=x,s.appearStatus=m):i=v:i=e.unmountOnExit||e.mountOnEnter?h:x,s.state={status:i},s.nextCallback=null,s}o(e,t),e.getDerivedStateFromProps=function(t,e){return t.in&&e.status===h?{status:x}:null};var n=e.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(t){var e=null;if(t!==this.props){var n=this.state.status;this.props.in?n!==m&&n!==v&&(e=m):n!==m&&n!==v||(e=C)}this.updateStatus(!1,e)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var t,e,n,s=this.props.timeout;return t=e=n=s,null!=s&&"number"!=typeof s&&(t=s.exit,e=s.enter,n=void 0!==s.appear?s.appear:e),{exit:t,enter:e,appear:n}},n.updateStatus=function(t,e){if(void 0===t&&(t=!1),null!==e)if(this.cancelNextCallback(),e===m){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:c().findDOMNode(this);n&&E(n)}this.performEnter(t)}else this.performExit();else this.props.unmountOnExit&&this.state.status===x&&this.setState({status:h})},n.performEnter=function(t){var e=this,n=this.props.enter,s=this.context?this.context.isMounting:t,i=this.props.nodeRef?[s]:[c().findDOMNode(this),s],a=i[0],o=i[1],r=this.getTimeouts(),p=s?r.appear:r.enter;!t&&!n||d?this.safeSetState({status:v},(function(){e.props.onEntered(a)})):(this.props.onEnter(a,o),this.safeSetState({status:m},(function(){e.props.onEntering(a,o),e.onTransitionEnd(p,(function(){e.safeSetState({status:v},(function(){e.props.onEntered(a,o)}))}))})))},n.performExit=function(){var t=this,e=this.props.exit,n=this.getTimeouts(),s=this.props.nodeRef?void 0:c().findDOMNode(this);e&&!d?(this.props.onExit(s),this.safeSetState({status:C},(function(){t.props.onExiting(s),t.onTransitionEnd(n.exit,(function(){t.safeSetState({status:x},(function(){t.props.onExited(s)}))}))}))):this.safeSetState({status:x},(function(){t.props.onExited(s)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(t,e){e=this.setNextCallback(e),this.setState(t,e)},n.setNextCallback=function(t){var e=this,n=!0;return this.nextCallback=function(s){n&&(n=!1,e.nextCallback=null,t(s))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(t,e){this.setNextCallback(e);var n=this.props.nodeRef?this.props.nodeRef.current:c().findDOMNode(this),s=null==t&&!this.props.addEndListener;if(n&&!s){if(this.props.addEndListener){var i=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],a=i[0],o=i[1];this.props.addEndListener(a,o)}null!=t&&setTimeout(this.nextCallback,t)}else setTimeout(this.nextCallback,0)},n.render=function(){var t=this.state.status;if(t===h)return null;var e=this.props,n=e.children,s=(e.in,e.mountOnEnter,e.unmountOnExit,e.appear,e.enter,e.exit,e.timeout,e.addEndListener,e.onEnter,e.onEntering,e.onEntered,e.onExit,e.onExiting,e.onExited,e.nodeRef,(0,i.A)(e,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return l().createElement(f.Provider,{value:null},"function"==typeof n?n(t,s):l().cloneElement(l().Children.only(n),s))},e}(l().Component);function N(){}g.contextType=f,g.propTypes={},g.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:N,onEntering:N,onEntered:N,onExit:N,onExiting:N,onExited:N},g.UNMOUNTED=h,g.EXITED=x,g.ENTERING=m,g.ENTERED=v,g.EXITING=C;var b=g,S=function(t,e){return t&&e&&e.split(" ").forEach((function(e){return s=e,void((n=t).classList?n.classList.remove(s):"string"==typeof n.className?n.className=r(n.className,s):n.setAttribute("class",r(n.className&&n.className.baseVal||"",s)));var n,s}))},k=function(t){function e(){for(var e,n=arguments.length,s=new Array(n),i=0;i<n;i++)s[i]=arguments[i];return(e=t.call.apply(t,[this].concat(s))||this).appliedClasses={appear:{},enter:{},exit:{}},e.onEnter=function(t,n){var s=e.resolveArguments(t,n),i=s[0],a=s[1];e.removeClasses(i,"exit"),e.addClass(i,a?"appear":"enter","base"),e.props.onEnter&&e.props.onEnter(t,n)},e.onEntering=function(t,n){var s=e.resolveArguments(t,n),i=s[0],a=s[1]?"appear":"enter";e.addClass(i,a,"active"),e.props.onEntering&&e.props.onEntering(t,n)},e.onEntered=function(t,n){var s=e.resolveArguments(t,n),i=s[0],a=s[1]?"appear":"enter";e.removeClasses(i,a),e.addClass(i,a,"done"),e.props.onEntered&&e.props.onEntered(t,n)},e.onExit=function(t){var n=e.resolveArguments(t)[0];e.removeClasses(n,"appear"),e.removeClasses(n,"enter"),e.addClass(n,"exit","base"),e.props.onExit&&e.props.onExit(t)},e.onExiting=function(t){var n=e.resolveArguments(t)[0];e.addClass(n,"exit","active"),e.props.onExiting&&e.props.onExiting(t)},e.onExited=function(t){var n=e.resolveArguments(t)[0];e.removeClasses(n,"exit"),e.addClass(n,"exit","done"),e.props.onExited&&e.props.onExited(t)},e.resolveArguments=function(t,n){return e.props.nodeRef?[e.props.nodeRef.current,t]:[t,n]},e.getClassNames=function(t){var n=e.props.classNames,s="string"==typeof n,i=s?""+(s&&n?n+"-":"")+t:n[t];return{baseClassName:i,activeClassName:s?i+"-active":n[t+"Active"],doneClassName:s?i+"-done":n[t+"Done"]}},e}o(e,t);var n=e.prototype;return n.addClass=function(t,e,n){var s=this.getClassNames(e)[n+"ClassName"],i=this.getClassNames("enter").doneClassName;"appear"===e&&"done"===n&&i&&(s+=" "+i),"active"===n&&t&&E(t),s&&(this.appliedClasses[e][n]=s,function(t,e){t&&e&&e.split(" ").forEach((function(e){return s=e,void((n=t).classList?n.classList.add(s):function(t,e){return t.classList?!!e&&t.classList.contains(e):-1!==(" "+(t.className.baseVal||t.className)+" ").indexOf(" "+e+" ")}(n,s)||("string"==typeof n.className?n.className=n.className+" "+s:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+s)));var n,s}))}(t,s))},n.removeClasses=function(t,e){var n=this.appliedClasses[e],s=n.base,i=n.active,a=n.done;this.appliedClasses[e]={},s&&S(t,s),i&&S(t,i),a&&S(t,a)},n.render=function(){var t=this.props,e=(t.classNames,(0,i.A)(t,["classNames"]));return l().createElement(b,(0,s.A)({},e,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},e}(l().Component);k.defaultProps={classNames:""},k.propTypes={};var O=k}}]);