# 套系应用流程详解

## 1. 什么是套系

套系是指一套具有统一风格的装修材料和家具的集合，在LayoutAI系统中，套系包含了软装（如沙发、床等）、定制柜（如衣柜、书柜等）和硬装（如地板、墙面等）的风格定义和素材。用户可以根据个人喜好选择不同风格的套系，系统会自动将套系应用到选定的空间中，实现快速、专业的设计方案。

![套系示例](https://img.freepik.com/free-photo/modern-bedroom-interior-design_23-2150798953.jpg)

## 2. 套系应用流程总览

套系应用是LayoutAI系统中的核心功能，它允许用户选择风格套系并将其应用到选定的空间中。整个流程始于用户在UI层选择套系，经过核心逻辑处理后，最终将匹配的素材应用到空间中并更新3D场景。

```
用户选择套系 → 触发系统事件 → 确定应用范围和房间 → 套系素材匹配 → 多样化处理 → 更新UI和3D场景 → 自动微调 → 生成报价
```

## 3. 核心方法：`onSeriesSampleSelected()`

当用户在UI层选择了一个套系后，系统会通过事件机制触发`TSeriesFurnisher.onSeriesSampleSelected()`方法。这是套系应用流程在系统内核层的起点。

```typescript
async onSeriesSampleSelected(
    scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean }, 
    series: TSeriesSample,
    targetRooms:TRoom[] = null,
    options: { 
        needsDiversifyMatched?:boolean, 
        updateTopViewBy3d?:boolean,
        needAutoFinetune?:boolean
    } = { 
        needsDiversifyMatched:true, 
        updateTopViewBy3d:true, 
        needAutoFinetune:true 
    }
) {
    // 方法实现...
}
```

### 3.1 参数说明

- **scope**: 应用范围，包含四个布尔值属性：
  - `soft`: 是否应用软装（沙发、床等）
  - `cabinet`: 是否应用定制柜（衣柜、书柜等）
  - `hard`: 是否应用硬装（地板、墙面等）
  - `remaining`: 是否进行补全操作（对未布置的区域进行套系应用）
  
- **series**: 风格套系对象，包含套系的风格信息和规则

- **targetRooms**: 需要应用套系的目标房间列表，默认为当前选中的房间

- **options**: 套系应用的选项
  - `needsDiversifyMatched`: 是否需要多样化匹配（默认为true）
  - `updateTopViewBy3d`: 是否需要更新3D视图（默认为true）
  - `needAutoFinetune`: 是否需要自动微调（默认为true）

## 4. 套系应用流程详解

### 4.1 准备阶段

流程开始时，系统会：

1. 取消当前被选中图元的选中状态
   ```typescript
   LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
   ```

2. 确定目标房间列表
   ```typescript
   if (!targetRooms) targetRooms = this.current_rooms;
   ```

3. 创建一个空数组用于记录需要应用套系的房间
   ```typescript
   let needFurnishRooms: TRoom[] = [];
   ```

### 4.2 遍历目标房间

系统对每个目标房间进行遍历，并根据不同情况进行处理：

```typescript
let allPromises = targetRooms.map(async (roomItem: TRoom) => {
    // 如果房间被锁定，则跳过
    if (roomItem.locked) return;
    
    // 将需要应用套系的房间添加到列表
    needFurnishRooms.push(roomItem);
    
    // 创建套系实例
    let newSeries = new TSeriesSample(series);
    let promise_list = null;
    
    // 根据是否为补全模式分别处理
    if (scope.remaining == false) {
        // 常规套系应用流程
        // ...
    } else {
        // 补全模式流程
        // ...
    }
});
```

### 4.3 常规套系应用流程

对于常规套系应用（非补全模式），系统执行以下操作：

1. 记录房间的应用范围和套系
   ```typescript
   roomItem.addApplyScope(scope, newSeries);
   ```

2. 设置当前应用范围
   ```typescript
   roomItem.setCurrentApplyScope(scope);
   ```

3. 清除当前范围内的匹配素材
   ```typescript
   roomItem.clearMatchedMaterialsInCurrentScope();
   ```

4. 记录房间与套系的映射关系
   ```typescript
   this.room2SeriesSampleMap.set(roomItem, newSeries);
   ```

5. 调用`tryToFurnishRooms`尝试应用套系
   ```typescript
   promise_list = this.tryToFurnishRooms([roomItem]);
   ```

### 4.4 补全模式流程

对于补全模式，系统执行以下操作：

1. 获取房间已应用的套系范围
   ```typescript
   let furnishedApplyScope = roomItem.furnishedApplyScope;
   ```

2. 计算并设置当前应用范围（仅包含未应用的部分）
   ```typescript
   let applyScope = { 
       soft: furnishedApplyScope && !furnishedApplyScope.soft, 
       cabinet: furnishedApplyScope && !furnishedApplyScope.cabinet, 
       hard: furnishedApplyScope && !furnishedApplyScope.hard, 
       remaining: true 
   };
   roomItem.setCurrentApplyScope(applyScope);
   ```

3. 调用`tryToFurnishRoomRemainings`尝试对剩余部分应用套系
   ```typescript
   promise_list = this.tryToFurnishRoomRemainings([roomItem], newSeries);
   ```

4. 记录房间的应用范围和套系
   ```typescript
   roomItem.addApplyScope(applyScope, newSeries);
   ```

### 4.5 素材匹配处理

套系应用的核心是将套系中的素材与房间中的图元进行匹配。这个过程由`TMaterialMatcher`类负责：

```typescript
// 常规套系应用
promises.push(TMaterialMatcher.instance.furnishRoom(roomItem, this.room2SeriesSampleMap.get(roomItem), logger, allMatchedMaterials));

// 补全模式
promises.push(TMaterialMatcher.instance.furnishRoom(roomItem, sereis, logger, allSoftMaterials));
```

在匹配过程中，系统会根据图元的类型、尺寸、位置等信息，从套系中选择最合适的素材进行匹配。

### 4.6 多样化处理

为了避免同一类型的图元在不同房间中使用相同的素材，系统会进行多样化处理：

```typescript
if(options.needsDiversifyMatched) {
    // 对需要应用套系的房间的图元进行多样化匹配
    this.diversifyMatchedMatetrial(needFurnishRooms);
}
```

多样化处理会根据图元的类型进行不同的处理，确保同类型图元在不同房间中尽可能使用不同的素材。

### 4.7 后处理和更新

所有房间处理完成后，系统进行以下后处理操作：

1. 更新UI层的房间和套系映射关系
   ```typescript
   LayoutAI_App.emit(EventName.Room2SeriesSampleRoom, this.orderRoom2SeriesSample(this.room2SeriesSampleMap, this.current_rooms));
   ```

2. 更新系统状态
   ```typescript
   this.update();
   ```

3. 更新3D场景
   ```typescript
   if (LayoutAI_App.instance.scene3D?.isValid()) {
       LayoutAI_App.emit(Scene3DEvents.UpdateScene3D, true);
   }
   ```

4. 生成报价数据
   ```typescript
   LayoutAI_App.emit(EventName.quoteDataSeries, series);
   ```

5. 如果需要，进行自动微调
   ```typescript
   if(options.needAutoFinetune) {
       await this.autoFineTuning();
   }
   ```

## 5. 素材匹配的核心过程

### 5.1 图元分类

在套系应用过程中，系统会根据图元的类型进行分类处理：

- **软装图元**：如沙发、床、餐桌等
- **定制柜图元**：如衣柜、书柜等
- **硬装图元**：如地面、墙面、天花板等
- **装饰图元**：如饰品、挂画等

### 5.2 素材匹配规则

系统根据不同类型的图元采用不同的匹配规则：

1. **尺寸匹配**：确保素材的尺寸与图元的尺寸相匹配
2. **功能匹配**：确保素材的功能与图元的功能相匹配
3. **风格匹配**：确保素材的风格与套系的风格相匹配
4. **位置匹配**：考虑图元的位置信息进行匹配

### 5.3 素材位置和尺寸调整

匹配完成后，系统会对素材的位置和尺寸进行调整，确保素材在3D场景中的合理布置：

```typescript
// 修改素材的目标布置位置和尺寸
MatchingPostProcesser.modifyMaterialPositionSize(figure_element, material);

// 同步素材位置到图元的matched_rect中
MatchingPostProcesser.syncMatchedRectAndModifyMaterialPositionZ(material, room);
```

调整内容包括：

- 调整素材的旋转角度
- 设置素材的目标布置位置
- 根据不同类型的素材设置目标尺寸
- 对特定类型的素材进行特殊调整（如贴顶、贴地等）

## 6. 套系应用结果

套系应用完成后，系统会：

1. 更新UI界面，显示套系与房间的匹配关系
2. 更新3D场景，展示应用套系后的效果
3. 生成报价数据，用于后续的报价计算

## 7. 自动微调

为了使套系应用的结果更加合理和美观，系统提供了自动微调功能：

```typescript
private async autoFineTuning() {
    TLayoutFineTuningManagerToolUtil.instance.postMatchAutoFineTuning(this.room_list);
    this.update();
}
```

自动微调会对家具的位置、尺寸、旋转等进行微调，确保家具之间的关系合理，避免家具之间的碰撞和重叠。

## 8. 总结

套系应用流程是LayoutAI系统中的核心功能，它通过一系列复杂的步骤，将用户选择的风格套系应用到目标空间中。整个流程涉及多个模块协同工作，包括素材匹配、后处理和自动微调等。

通过这种流程，系统能够快速、高效地实现空间的风格统一和合理布局，为用户提供专业的室内设计方案。

## 9. 详细方法流程图

### 9.1 套系应用总体流程

```mermaid
graph TD
    A[用户选择套系] --> B[触发UI事件]
    B --> C[onSeriesSampleSelected方法]
    C --> D[准备阶段]
    D --> E[遍历目标房间]
    E --> F{是否为补全模式?}
    F -->|否| G[常规套系应用流程]
    F -->|是| H[补全模式流程]
    G --> I[tryToFurnishRooms]
    H --> J[tryToFurnishRoomRemainings]
    I --> K[等待所有套系应用完成]
    J --> K
    K --> L[多样化处理]
    L --> M[更新UI和3D场景]
    M --> N[生成报价数据]
    N --> O[自动微调]
```

### 9.2 素材匹配详细流程

```mermaid
graph TD
    A[tryToFurnishRooms] --> B[遍历目标房间]
    B --> C{房间是否已应用套系?}
    C -->|是| D[获取套系样本]
    D --> E[TMaterialMatcher.furnishRoom]
    E --> F[筛选待匹配图元]
    F --> G[获取套系素材]
    G --> H[执行素材匹配]
    H --> I[后处理匹配结果]
    I --> J[调整素材位置和尺寸]
    J --> K[生成顶视图纹理]
    K --> L[调整背景墙相关图元]
    L --> M[隐藏重复素材]
    M --> N[处理装饰图元]
    N --> O[返回匹配结果]
```

## 10. 深层调用堆栈分析

### 10.1 `tryToFurnishRooms`方法详解

`tryToFurnishRooms`是套系应用流程中的核心方法，负责对指定的房间列表应用套系和匹配素材。其调用流程如下：

```typescript
tryToFurnishRooms(targetRooms: TRoom[]): Promise<any>[] {
    let logger = this._logger;
    let promises: Promise<any>[] = [];
    for (let roomItem of targetRooms) {
        // 绑定furniture和room
        roomItem._furniture_list.forEach(ele => ele._room = roomItem);
        // 更新房间已应用套系的范围
        roomItem.updateFurnishedApplyScope();
        // 如果房间已应用套系，并有图元，则匹配套系素材
        if (this.room2SeriesSampleMap.has(roomItem) && (roomItem._furniture_list.length > 0)) {
            let allMatchedMaterials: I_MaterialMatchingItem[] = [];
            // 进入房间匹配套系素材的流程
            promises.push(TMaterialMatcher.instance.furnishRoom(roomItem, this.room2SeriesSampleMap.get(roomItem), logger, allMatchedMaterials));
        }
    }
    return promises;
}
```

### 10.2 `TMaterialMatcher.furnishRoom`方法分析

这是套系素材匹配的核心方法，其调用堆栈包括：

1. **初始化和准备阶段**
   ```typescript
   async furnishRoom(roomItem: TRoom, seriesSampleItem: TSeriesSample, logger: Logger, matchedMaterials: I_MaterialMatchingItem[]) {
       // 获取需要匹配的图元列表
       let toBeMatchedFigureElements = this.getToBeMatchedElements(roomItem);
       
       // 记录调试信息
       this._log_debug_to_be_matched_elements(roomItem, toBeMatchedFigureElements, logger);
       
       // 获取套系的素材列表
       let seriesMaterials = await this.getSeriesMaterials(seriesSampleItem);
   }
   ```

2. **匹配软装和定制柜素材**
   ```typescript
   // 如果应用范围包含软装
   if (roomItem.currentApplyScope && roomItem.currentApplyScope.soft) {
       // 获取软装图元列表
       let softElements = toBeMatchedFigureElements.filter(fe => this.isSoftFurniture(fe));
       // 匹配软装素材
       let softMaterials = await this.matchSoftElements(softElements, seriesMaterials);
       allMatchedMaterials.push(...softMaterials);
   }
   
   // 如果应用范围包含定制柜
   if (roomItem.currentApplyScope && roomItem.currentApplyScope.cabinet) {
       // 获取定制柜图元列表
       let cabinetElements = toBeMatchedFigureElements.filter(fe => this.isCabinetFurniture(fe));
       // 匹配定制柜素材
       let cabinetMaterials = await this.matchCabinetElements(cabinetElements, seriesSampleItem);
       allMatchedMaterials.push(...cabinetMaterials);
   }
   ```

3. **匹配硬装素材**
   ```typescript
   // 如果应用范围包含硬装
   if (roomItem.currentApplyScope && roomItem.currentApplyScope.hard) {
       // 获取硬装图元列表（地面、墙面等）
       let hardElements = this.makeHardFurnishingElements(roomItem);
       // 匹配硬装素材
       let hardMaterials = await this.matchHardElements(hardElements, seriesMaterials);
       allMatchedMaterials.push(...hardMaterials);
   }
   ```

4. **后处理匹配结果**
   ```typescript
   // 调整匹配的素材和图元
   MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements(roomItem, allMatchedMaterials);
   
   // 创建顶视图纹理
   await MatchingPostProcesser.createTopViewTexturesForProductMaterial(roomItem, toBeMatchedFigureElements);
   
   // 如果有定制柜，创建定制柜的顶视图纹理
   if (cabinetElements.length > 0) {
       await MatchingPostProcesser.createTopViewTexturesForCabinetMatatrial(cabinetElements, seriesSampleItem, logger);
   }
   
   // 创建硬装的顶视图纹理
   if (hardElements.length > 0) {
       await MatchingPostProcesser.createTopViewTexuturesForHardMaterial(roomItem, seriesSampleItem, hardElements);
   }
   
   // 调整背景墙相关的图元
   MatchingPostProcesser.adjust_backgroundwall_related(roomItem);
   
   // 隐藏同属于同一种模型位的重复素材
   await MatchingPostProcesser.hideDuplicatedMaterialOfSameModelloc(toBeMatchedFigureElements, groupMemberElements, logger);
   
   // 若台面家具匹配到组合素材，则移除其装饰图元的素材
   MatchingPostProcesser.removeRedundantDecorations(toBeMatchedFigureElements);
   
   // 对未匹配到素材的图元添加高亮标记
   MatchingPostProcesser.spotlightingUnmatchedFigureElements(toBeMatchedFigureElements, allMatchedMaterials);
   
   // 如果台面家具匹配到组合素材，则隐藏附加在台面家具的装饰图元
   MatchingPostProcesser.hideDecorationMaterialIfNeeded(toBeMatchedFigureElements);
   
   // 执行其他后处理操作
   this.postProcessMatchedMaterials(allMatchedMaterials, roomItem);
   ```

## 11. 素材匹配流程详解

### 11.1 素材匹配的基本原理

素材匹配是基于图元与素材之间的多维度匹配，主要考虑以下因素：

1. **图元类型匹配**：确保素材的类型与图元的类型相匹配（如沙发素材匹配沙发图元）
2. **尺寸匹配**：确保素材的尺寸与图元的尺寸相匹配，或者可以进行适当的缩放
3. **功能匹配**：确保素材的功能与图元的功能相匹配（如餐桌与餐椅）
4. **位置匹配**：考虑图元在空间中的位置（如贴墙、靠窗等）
5. **风格匹配**：确保素材的风格与套系的风格相匹配

### 11.2 素材匹配算法流程

1. **筛选候选素材**
   ```typescript
   // 筛选符合图元类型的素材
   let candidateMaterials = seriesMaterials.filter(material => 
       material.modelLoc === figureElement.modelLoc
   );
   
   // 按照匹配度排序
   candidateMaterials.sort((a, b) => 
       this.calculateMatchScore(figureElement, b) - this.calculateMatchScore(figureElement, a)
   );
   ```

2. **计算匹配得分**
   ```typescript
   // 计算素材与图元的匹配得分
   calculateMatchScore(figureElement: TFigureElement, material: I_MaterialMatchingItem): number {
       let score = 0;
       
       // 尺寸匹配得分
       score += this.calculateSizeMatchScore(figureElement, material);
       
       // 功能匹配得分
       score += this.calculateFunctionMatchScore(figureElement, material);
       
       // 风格匹配得分
       score += this.calculateStyleMatchScore(figureElement, material);
       
       // 位置匹配得分
       score += this.calculatePositionMatchScore(figureElement, material);
       
       return score;
   }
   ```

3. **选择最佳匹配**
   ```typescript
   // 选择得分最高的素材作为匹配结果
   figureElement.setMatchedMaterial(candidateMaterials[0]);
   ```

4. **多样化处理**
   ```typescript
   // 对同类型图元进行多样化处理，避免使用相同素材
   diversifyMatchedMatetrial(rooms: TRoom[]) {
       // 对特定类型的图元进行多样化处理
       diversifyModelLocs.forEach(modelLoc => {
           // 获取所有该类型的图元
           let allModellocFigureElements = [];
           for (let room of rooms) {
               room._furniture_list.filter(ele => ele.modelLoc.endsWith(modelLoc))
                   .forEach(ele => allModellocFigureElements.push(ele));
           }
           
           // 避免使用相同的素材
           let allModellocMatchedMaterials = [];
           allModellocFigureElements.forEach(ele => {
               // 如果该图元的素材已经在其他图元中使用，尝试替换为其他素材
               // ...
           });
       });
   }
   ```

### 11.3 素材位置和尺寸调整

匹配完成后，系统会对素材的位置和尺寸进行调整，确保在3D场景中的合理布置：

```typescript
// 修改素材的位置和尺寸
modifyMaterialPositionSize(figure_element: TFigureElement, material: I_MaterialMatchingItem) {
    // 处理旋转问题
    if(compareNames([figure_element.sub_category], NoMirrorWithRotationModelLocs)) {
        // 处理镜像旋转
    }
    
    // 设置目标位置和旋转角度
    material.targetPosition = figure_element.rect.rect_center;
    material.targetRotation = { x: 0, y: 0, z: figure_element.rect.rotation_z };
    
    // 根据不同类型的素材设置目标尺寸
    if(material.modelLoc.endsWith("组合")) {
        // 组合素材使用原始尺寸
        material.targetSize = { length: material.length, width: material.width, height: material.height };
    } else if (lengthWidthHeightScaleableModelLocs.indexOf(material.modelLoc) > -1) {
        // 可缩放长宽高的素材
        material.targetSize = { length: figure_element.rect.w, width: figure_element.rect.h, height: figure_element.params.height };
    } else if (lengthWidthScaleableModelLocs.indexOf(material.modelLoc) > -1) {
        // 可缩放长宽的素材
        material.targetSize = { length: figure_element.rect.w, width: figure_element.rect.h, height: material.height };
    } 
    // 其他类型素材的处理...
    
    // 特殊处理（如定制柜、窗帘等）
    // ...
    
    // 限制素材高度不超过层高
    if (material.targetSize.height > _storey_height) {
        material.targetSize.height = _storey_height - material.targetPosition.z;
    }
}
```

### 11.4 特殊图元的处理

系统对特定类型的图元有特殊处理：

1. **背景墙处理**
   ```typescript
   // 调整背景墙深度和相关图元位置
   adjust_backgroundwall_related(room: TRoom) {
       // 获取所有背景墙图元
       let backgroundWalls = room._furniture_list.filter(fe => 
           fe.modelLoc.indexOf("背景墙") >= 0 && fe.haveMatchedMaterial()
       );
       
       // 对每个背景墙图元进行处理
       for (let backgroudwall of backgroundWalls) {
           // 限制背景墙深度
           let max_background_wall_depth = Math.min(backgroudwall._matched_material.width, 120);
           
           // 调整其他图元位置，避免与背景墙重叠
           room._furniture_list.forEach(other => {
               // 判断图元是否与背景墙相交
               // 如果相交，调整图元位置
           });
       }
   }
   ```

2. **贴顶墙和贴地处理**
   ```typescript
   // 设置素材的z位置
   if (_align_top_modelLoc_list.has(material.modelLoc) || 
       (material.modelLoc.indexOf("灯") >= 0 && material.modelLoc.indexOf("落地灯") < 0)) {
       // 贴顶处理
       material.targetPosition.z = _storey_height - material.height;
   } else if (_grounded_modelLoc_list.has(material.modelLoc)) {
       // 贴地处理
       material.targetPosition.z = room._room_entity.floor_thickness;
   } 
   // 其他特殊处理...
   ```

## 12. 套系应用流程的关键挑战

套系应用流程中存在几个关键挑战：

1. **素材丰富度**：套系中素材需要足够丰富，才能满足不同空间中各类图元的匹配需求。

2. **匹配精确度**：素材与图元之间的匹配需要尽可能精确，避免不合理的匹配结果。

3. **空间合理性**：素材布置在空间中需要考虑整体的合理性，避免碰撞、重叠等问题。

4. **风格一致性**：不同房间之间的素材需要保持风格一致性，同时又要避免重复单调。

5. **性能优化**：素材匹配过程可能涉及大量计算，需要进行性能优化，提高匹配速度。 