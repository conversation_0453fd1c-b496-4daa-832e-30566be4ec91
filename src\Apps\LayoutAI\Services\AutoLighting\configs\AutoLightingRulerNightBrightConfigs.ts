import { CategoryName } from "../../../Scene3D/NodeName";
import { AlignTypeXY, AutoLightingRulerType, LightingRuler } from "../AutoLightingRuler";

// 自动灯光规则配置
// lightMode.Night
export const AutoLightingRulerNightBrightConfigs: LightingRuler[] = [
    {
        name: '客餐厅吊顶灯槽灯光',
        typeId: AutoLightingRulerType.CeilingSlotLight,
        lighting: {
            type: 1, // 1 矩形灯光
            color: 0xffffff,
            intensity: 9,
            targetObjectName: 'id5251202_Node_3',
            materialId: '368346740',
            // materialId: '35944925',
            length: 16,
            width: 16,
        },
        condition: {
            roomName: '客餐厅',
        },
    },
    {
        name: '卧室吊顶灯槽灯光',
        typeId: AutoLightingRulerType.CeilingSlotLight,
        lighting: {
            type: 1, // 1 矩形灯光
            color: 0xffffff,
            intensity: 4,
            targetObjectName: 'id5251202_Node_3',
            materialId: '368346740',
            // materialId: '35944925',
            length: 16,
            width: 16,
        },
        condition: {
            roomName: '卧室|主卧|次卧|客卧',
        },
    },
    {
        name: '茶室和书房吊顶灯槽灯光',
        typeId: AutoLightingRulerType.CeilingSlotLight,
        lighting: {
            type: 1, // 1 矩形灯光
            color: 0xffffff,
            intensity: 6,
            targetObjectName: 'id5251202_Node_3',
            materialId: '368346740',
            // materialId: '35944925',
            length: 16,
            width: 16,
        },
        condition: {
            roomName: '书房|茶室',
        },
    },
    {
        name: '沙发灯光',
        typeId: AutoLightingRulerType.SofaLight,
        category: CategoryName.Sofa,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 5,
            width: '80%',
            length: '50%',
        },
        pose: {
            z: 2150,
            gapOffset: 100,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        typeId: AutoLightingRulerType.CorridorLight,
        name: '过道灯光',
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 0.8,
            length: '25%',
            width: '90%',
        },
        pose: {
            z: 2370,
        },
        condition: {
            spaceArea: '过道区',
        },
    },
    {
        name: '电视柜灯光',
        typeId: AutoLightingRulerType.TVCabinetLight,
        category: CategoryName.TVCabinet,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 6,
            width: '80%',
            length: '50%',
        },
        pose: {
            z: 2150,
            gapOffset: 425,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '餐桌灯光',
        typeId: AutoLightingRulerType.TableLight,
        category: CategoryName.Table,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 3,
            width: '100%',
            length: '100%',
        },
        pose: {
            z: 2150,
        },
        condition: {
            spaceArea: '餐厅区',
        },
    },
    {
        name: '床尾顶光',
        typeId: AutoLightingRulerType.BedFootLight,
        category: CategoryName.Bed,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 4,
            width: '50%',
            length: '50%',
        },
        pose: {
            z: 2500,
            align: AlignTypeXY.Bottom,
        },
        condition: {
            roomName: '卧室|主卧|次卧|客卧',
        },
    },
    {
        name: '床尾侧光',
        typeId: AutoLightingRulerType.BedFootSideLight,
        category: CategoryName.Bed,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 2,
            width: '50%',
            length: '50%',
        },
        pose: {
            gapOffset: 1200,
            floorOffset: 1000,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            roomName: '卧室|主卧|次卧|客卧',
        },
    },
    {
        name: '厨房灯光',
        typeId: AutoLightingRulerType.KitchenLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 45,
            width: '30%',
            length: '30%',
        },
        pose: {
            z: 2270,
        },
        condition: {
            roomName: '厨房',
        },
    },
    {
        name: '卫生间灯光',
        typeId: AutoLightingRulerType.BathroomLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 26,
            width: '30%',
            length: '30%',
        },
        condition: {
            roomName: '卫生间',
        },
    },
    {
        name: '书桌灯光',
        typeId: AutoLightingRulerType.DeskLight,
        category: CategoryName.Desk,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 25,
            width: '40%',
            length: '40%',
        },
        pose: {
            z: 2370,
        },
        condition: {
            roomName: '书房|茶室',
        },
    },
    {
        name: '茶台灯光',
        typeId: AutoLightingRulerType.TeaTableLight,
        category: CategoryName.TeaTable,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 2,
            width: '40%',
            length: '40%',
        },
        pose: {
            z: 2370,
        },
        condition: {
            roomName: '茶室',
        },
    },
];
