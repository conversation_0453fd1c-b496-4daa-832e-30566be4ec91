
import {  Vector3 } from "three";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { CadBaseSubHandler } from "./CadBaseSubHandler";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { checkIsMobile } from "@/config";
import { EventName } from "@/Apps/EventSystem";
import { DrawingFigureMode } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { AI2DesignBasicModes } from "@/Apps/AI2Design/AI2DesignManager";


export class AddFurnitureHandler extends CadBaseSubHandler
{

    _faraway_point : Vector3;
    _last_pos : Vector3;
        constructor(cad_mode_handler:AI2BaseModeHandler)
    {
        super(cad_mode_handler);
        this.name = "AddFurniture";
        this._cad_mode_handler = cad_mode_handler;
        this._drawing_rect = null;
        this._last_pos = new Vector3(0, 0, 0);
        this._faraway_point = new Vector3(-9999999,-9999999,0);
    }

    enter(state?: number): void {
        console.log("Enter AddFurniture");
        this._drawing_rect = null;
        this.cleanSelection();
        this.manager.layout_container.cleanDimension();
        this._cad_mode_handler._is_moving_element = false;
        this._cad_mode_handler._is_painter_center_moving = false;

        if(!this.ai_cad_data._adding_figure_entity) {
            this.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        else{
            this.ai_cad_data._adding_figure_entity.rect.rect_center = this.ai_cad_data._adding_figure_entity.rect.rect_center.clone();
            this._last_pos = this.ai_cad_data._adding_figure_entity.rect.rect_center.clone();
        }
        this.update();
        // this.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);
    }

    leave(state?: number): void {
        this._drawing_rect = null;
        console.log("Leave AddFurniture");
        
        // this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);  
    }

    onmousedown(ev: I_MouseEvent): void {
        if(ev.buttons == 2)
        {
            this.ai_cad_data._adding_figure_entity = null;
            this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        this.update();

    }

    onmousemove(ev: I_MouseEvent): void {
        
        this._cad_mode_handler._is_moving_element = true;

        this._adding_figure_entity = this.ai_cad_data._adding_figure_entity;
        if( this.ai_cad_data._adding_figure_entity)
        {
            this.updateAddFigureRect(ev);
            this._adding_figure_entity._rect.updateRect();  
            this.update();
        }
    }

    onmouseup(ev: I_MouseEvent): void {
        // 如果鼠标松开的时候在左侧的工具栏上，不做任何操作
        if(checkIsMobile())
        {
            let elementId = "#pad_left_panel";
            if(this.manager._current_handler_mode === AI2DesignBasicModes.HouseDesignMode)
            {
                elementId = "#pad_house_left_panel";
            }
            let scrollContainer = document.querySelector(elementId);
            const containerRect = scrollContainer.getBoundingClientRect();
            // 横屏下 鼠标松开的时候不超过侧边栏的右侧 则离开状态
            if((LayoutAI_App.instance._is_landscape))
            {
                if(ev._ev.x <= containerRect.right && ev._ev.y > containerRect.top && ev._ev.y < containerRect.bottom)
                {
                    this._leaveCurrentHandler();
                    return;
                }
            } else 
            {
                // 竖屏下 鼠标松开的时候不超过侧边栏顶部 则离开状态
                if(ev._ev.y > containerRect.top)
                {
                    this._leaveCurrentHandler();
                    return;
                }
            }
        } else 
        {
            if(ev._ev.x < 340)
            {
                this._leaveCurrentHandler();
                return;
            }
        }
        this._cad_mode_handler._is_moving_element = false;
        if( this.ai_cad_data._adding_figure_entity)
        {
            if (this.ai_cad_data._adding_figure_entity.realType == "SoftFurniture"  && !this.manager.layer_CadFurnitureLayer.visible ) {
                LayoutAI_App.emit(EventName.MessageTip,'当前布局中有隐藏的家具，可在显示进行管理')
            }
            if (this.ai_cad_data._adding_figure_entity.realType == "Cabinet" && !this.manager.layer_CadCabinetLayer.visible ) {
                LayoutAI_App.emit(EventName.MessageTip,'当前布局中有隐藏的定制柜，可在显示进行管理')
            }
    
            this.addNewEntitiy();
        }
    }

    updateAddFigureRect(ev: I_MouseEvent)
    {

        // 移动图元的操作
        if(this._adding_figure_entity)
        {
            let r_center = new Vector3(ev.posX, ev.posY, 0);
            let movement = r_center.clone().sub(this._last_pos);
            if(this._adding_figure_entity.rect)
            { 
                this._adding_figure_entity._rect.rect_center = r_center;
                this._adding_figure_entity._rect.updateRect();
               this._adding_figure_entity.updateOnMovement(null, movement);
            }
            this._adding_figure_entity.update();
            this.update();
        }
    }

    private _leaveCurrentHandler()
    {
        this.cleanSelection();
        this.ai_cad_data._adding_figure_entity = null;
        this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
    }

    drawCanvas(): void {
        if(this.ai_cad_data._adding_figure_entity && this._cad_mode_handler._is_moving_element)
        {
            this.ai_cad_data._adding_figure_entity.drawEntity(this.painter, 
                {is_selected: true, 
                    is_draw_texture: this.container.drawing_figure_mode === DrawingFigureMode.Texture, 
                    is_draw_figure: true, 
                    is_draw_outline: this.container.drawing_figure_mode === DrawingFigureMode.Outline 
                });
        }

    }
}