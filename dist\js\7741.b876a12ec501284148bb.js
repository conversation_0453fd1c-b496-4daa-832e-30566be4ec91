"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[7741],{3727:function(n,t,e){var r=e(23825),o=e(79874);function i(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function a(){var n=i(["\n      .swj-top-menu-button-item {\n        width: 80px;\n        text-align: center;\n      }\n      .ant-modal-content\n      {\n        padding: 10px 10px !important;\n      }\n      .svg-3key1f\n      {\n        margin-left: -60px !important;\n      }\n      /* @keyframes slideIn {\n        from {\n          transform: translateY(-48px);\n        }\n        to {\n          transform: translateY(0);\n        }\n      }\n      @keyframes slideIn1 {\n        from {\n          transform: translateX(-340px);\n        }\n        to {\n          transform: translateX(0);\n        }\n      }\n      .svg-friga1\n      {\n        animation: slideIn .8s forwards;\n      }\n      .swj-left-menu-bar-container{\n        animation: slideIn1 .8s forwards;\n      } */\n    "]);return a=function(){return n},n}function c(){var n=i(["\n        position:absolute;\n        -webkit-transform:rotate(90deg);\n        -webkit-transform-origin:0% 0%;/*1.重置旋转中心*/\n        \n        -moz-transform: rotate(90deg);\n        -moz-transform-origin:0% 0%;\n        \n        -ms-transform: rotate(90deg);\n        -ms-transform-origin:0% 0%;\n        \n        transform: rotate(90deg);\n        transform-origin:0% 0%;\n        \n        width: 100vh;/*2.利用 vh 重置 ‘宽度’ */\n        height: 100vw;/* 3.利用 vw 重置 ‘高度’ */\n        top: 0;\n        left: 100vw;\n\n    "]);return c=function(){return n},n}function u(){var n=i(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 100%;\n      height: calc(100% - 48px);\n      background-color: rgba(255, 255, 255);\n      z-index: 9999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    "]);return u=function(){return n},n}function l(){var n=i(["\n      position: absolute;\n      top: 48px;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n    "]);return l=function(){return n},n}function s(){var n=i(["\n      position: fixed;\n      top: 48px;\n      left: 0;\n      width: 0px;\n      bottom: 0;\n      background-color: #fff;\n      z-index:1;\n      .svg-maxyiq\n      {\n        background-color: #fff !important;\n      }\n    "]);return s=function(){return n},n}function f(){var n=i(["\n      left: -30px !important;\n      top: -200px !important;\n    "]);return f=function(){return n},n}function p(){var n=i(["\n      position: absolute;\n      left: 0px;\n      top: -100px;\n      background-color: #EAEAEB;\n      width : calc(100% + 100px);\n      height : calc(100% + 200px);\n      overflow: hidden;\n      .canvas {\n        position : absolute;\n        left: 0px;\n        top: 0px;\n        &.canvas_drawing {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n        }\n        &.canvas_moving {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png) 16 16,auto;\n        }\n        &.canvas_leftmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n        }\n        &.canvas_rightmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n        }\n        &.canvas_acrossmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n        }\n        &.canvas_verticalmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n        }\n        &.canvas_text {\n          cursor : text;\n        }\n        &.canvas_pointer {\n          cursor : pointer;\n        }\n        &.canvas_splitWall {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 8 16,auto;\n        }\n      }\n\n      .canvas_btns {\n        width: auto;\n        margin: 0 auto;\n        position: fixed;\n        display: flex;\n        justify-content: center;\n        bottom: 35px;\n        z-index:10;\n        left: 50%;\n        transform: translateX(-50%);\n        .btn {\n          ","\n          border-radius: 6px;\n          border: none;\n\n          font-weight: 600;\n          margin-right: 10px;\n          margin-left: 10px;\n        }\n        .design_btn {\n          background: #e6e6e6;\n          margin-right: 20px;\n        }\n        @media screen and (max-height: 600px){\n          bottom: 50px !important;\n        }\n      }\n    "]);return p=function(){return n},n}function d(){var n=i(["\n      position: absolute;\n      left: 50%;\n      transform: translateX(-50%);\n      margin-left: -120px;\n      width: 500px;\n      top: 20px;\n      z-index: 20;\n    "]);return d=function(){return n},n}function m(){var n=i(["\n      position: absolute;\n      top: 0%;\n      padding-top: 21%;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return m=function(){return n},n}function h(){var n=i(["\n      z-index: 99;\n  \n      ","\n    "]);return h=function(){return n},n}function g(){var n=i(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return g=function(){return n},n}function b(){var n=i(["\n        width: 400px;\n        height: 300px;\n        position:absolute;\n        right:245px;\n        top:5px;\n    "]);return b=function(){return n},n}function v(){var n=i(["\n      height: 48px;\n      width: 100%;\n      position: fixed;\n      top: 48px;\n      background-color: #262626;\n      text-align: center;\n      color: #fff;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    "]);return v=function(){return n},n}function x(){var n=i(["\n      /* z-index: 99999; */\n      display: flex;\n      position: fixed;\n      left: 50%;\n      top: 50%;\n      transform: translate(-50%, -50%);\n      justify-content: center;\n      text-align: center;\n      img{\n        width: 256px;\n        height: 256px;\n      }\n      .first\n      {\n        color: #1C1F23;\n        font-family: PingFang SC;\n        font-weight: medium;\n        font-size: 24px;\n        line-height: 32px;\n        font-weight: 600;\n        margin: 20px 0 8px 0;\n      }\n      .second\n      {\n        color: #00000072;\n        font-weight: regular;\n        font-size: 14px;\n        line-height: 22px;\n      }\n    "]);return x=function(){return n},n}function y(){var n=i(["\n      position: fixed;\n      height: 250px;\n      bottom: 0px;\n      background-color: #fff;\n      width: 100%;\n      padding: 15px 48px;\n      .shcmeName\n      {\n        color: #282828;\n        font-family: SF Pro Text;\n        font-weight: semibold;\n        font-size: 20px;\n        line-height: 1.4;\n        font-weight: 600;\n      }\n      .info\n      {\n        display: inline-flex;\n        font-weight: 600;\n        margin-top: 10px;\n        .name\n        {\n          color: #5B5E60;\n          font-family: PingFang SC;\n          font-weight: regular;\n          font-size: 16px;\n          line-height: 1.5;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-bottom: 5px;\n          width: 140px;\n        }\n      }\n      .btn\n      {\n        text-align: center;\n        margin: 0 auto;\n\n      }\n      .btnInfo\n      {\n        text-align: center;\n        margin-top: 20px;\n        .btn\n        {\n          border-radius: 8px;\n          background: #F4F5F5;\n          width: 200px;\n          height: 48px;\n          border: none;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: semibold;\n          font-size: 16px;\n          line-height: 1.5;\n          font-weight: 600;\n        }\n      }\n      .shareImage\n      {\n        color: #5B5E60;\n        margin-top: 12px;\n        font-size: 16px;\n        img{\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          margin-right: 5px;\n        }\n\n      }\n    "]);return y=function(){return n},n}function w(){var n=i(["\n      padding: 24px;\n      font-size: 14px; \n      font-weight: 600;\n      img{\n        width: 36px;\n        height: 36px;\n        border-radius: 50%;\n        margin-right: 10px;\n      }\n    "]);return w=function(){return n},n}function S(){var n=i(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return S=function(){return n},n}function j(){var n=i(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 9999;\n    "]);return j=function(){return n},n}t.A=(0,o.rU)((function(n){n.token;var t=n.css;return{root:t(a()),landscape:t(c()),loading:t(u()),content:t(l()),side_pannel:t(s()),canvas_pannel_mobile:t(f()),canvas_pannel:t(p(),(0,r.fZ)()?"\n            width: 120px;\n            height: 36px;\n            font-size: 14px;\n          ":"\n            width: 200px;\n            height: 48px;\n            font-size: 16px;\n          "),layout_steps:t(d()),progressInfo:t(m()),left_content:t(h(),(0,r.fZ)()?"\n        width: 180px !important;\n        min-width: 180px !important;\n      ":"\n        min-width: 280px !important;\n       "),overlay:t(g()),scene3d:t(b()),MeasurScaleMode:t(v()),expireEmpty:t(x()),mobileBottom:t(y()),shareBox:t(w()),canvas3d:t(S()),aiDraw:t(j())}}))},26966:function(n,t,e){e.d(t,{A:function(){return C}});var r=e(13274),o=e(41594);function i(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function a(){var n=i(["\n            background: #fff;\n            height: 100%;\n            padding: 16px 16px;\n            position: fixed;\n            z-index: 9;\n            left: 0px;\n            width: 280px;\n            box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);\n        "]);return a=function(){return n},n}function c(){var n=i(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 16px;\n        .title\n        {\n            font-size: 20px;\n            color: rgba(0, 0, 0, 0.85);\n            font-weight: 600;\n        }\n        .iconClose_Large\n        {\n            cursor: pointer;\n            font-size: 12px !important;\n        }\n    "]);return c=function(){return n},n}function u(){var n=i(["\n         display: flex;\n         height:25px;\n         input {\n            width:100%;\n            border-radius:6px;\n            height:20px;\n            border:1px solid #aaa;\n            color:#2b2b2b;\n         }\n         input:focus {\n            border:1px solid #aaa;\n            outline: none; \n        }\n    "]);return u=function(){return n},n}function l(){var n=i(["\n        overflow-y: scroll;\n        height: calc(100vh - 135px) !important;\n        ::-webkit-scrollbar {\n            display: none;\n        }\n    "]);return l=function(){return n},n}function s(){var n=i(["\n        cursor: pointer;\n        border-radius: 2px;\n        margin-bottom: 16px;\n        text-align: center;\n        img{\n            width: 100%;\n            height: 230px;\n            background-color: #f2f2f2;\n            transition: all .1s;\n        }     \n        img:hover{\n            box-shadow: 0 2px 8px 0 rgba(0,0,0,.16);\n        }\n        .title\n        {\n            font-weight: 600;\n            margin-top: 10px;\n            /* font-size: 14px; */\n        }\n   \n    "]);return s=function(){return n},n}var f=(0,e(79874).rU)((function(n){n.token;var t=n.css;return{root:t(a()),titleInfo:t(c()),keywords:t(u()),list:t(l()),item:t(s())}})),p=e(27347),d=e(15696),m=e(27164),h=e(9003),g=e(48402),b=e(85783),v=e(1870);function x(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function y(n,t,e){return t in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function w(n,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):function(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),e.push.apply(e,r)}return e}(Object(t)).forEach((function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))})),n}function S(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,o,i=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw o}}return i}}(n,t)||k(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(n){return function(n){if(Array.isArray(n))return x(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||k(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(n,t){if(n){if("string"==typeof n)return x(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);return"Object"===e&&n.constructor&&(e=n.constructor.name),"Map"===e||"Set"===e?Array.from(e):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?x(n,t):void 0}}var C=(0,d.observer)((function(){var n=(0,h.P)(),t=(0,b.B)().t,e=f().styles,i=S((0,o.useState)([]),2),a=i[0],c=i[1],u=S((0,o.useState)(""),2),l=u[0],s=u[1],d=g.V.find((function(n){return"组合"===n.label})),x=[];d.child.map((function(n){x=x.concat(n.figureList)}));var k=[];g.V.filter((function(n){return!["组合","户型","视角"].includes(n.label)})).forEach((function(n){var t=!0,e=!1,r=void 0;try{for(var o,i=function(){var t,e=o.value;(t=k).push.apply(t,j(e.figureList.map((function(t){return w(function(n){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{},r=Object.keys(e);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(e).filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})))),r.forEach((function(t){y(n,t,e[t])}))}return n}({},t),{keywords:[e.label,n.label,t.title].concat(j(t.roomType||[])).join(" ")})}))))},a=n.child[Symbol.iterator]();!(t=(o=a.next()).done);t=!0)i()}catch(n){e=!0,r=n}finally{try{t||null==a.return||a.return()}finally{if(e)throw r}}})),k.forEach((function(n){n.image&&n.image.endsWith("svg")&&n.png&&(n.image="https://3vj-fe.3vjia.com/layoutai/figures_imgs/"+n.png)}));var C="",_=function(){var n=j(k);n.sort((function(n,t){return(0,v.fM)(t.keywords,C)-(0,v.fM)(n.keywords,C)})),n.length=Math.min(n.length,15),c(n)};return(0,o.useEffect)((function(){var t,e,r=null===(e=n.homeStore.selectEntity)||void 0===e||null===(t=e._figure_element)||void 0===t?void 0:t.category,o=n.homeStore.selectEntity;if(null==o?void 0:o.is_single_furniture)s(r),C=r,_();else if("书桌"===r&&(r="桌几"),r&&x){var i=x.filter((function(n){return n.title.includes(r)}));c(i)}}),[n.homeStore.selectEntity]),(0,r.jsxs)("div",{className:e.root,children:[(0,r.jsxs)("div",{className:e.titleInfo,children:[(0,r.jsx)("div",{className:"title",children:t("替换产品")}),(0,r.jsx)(m.A,{iconClass:"iconClose_Large",onClick:function(){n.homeStore.setShowReplace(!1)}})]}),(0,r.jsx)("div",{className:e.keywords,children:(0,r.jsx)("input",{type:"text",placeholder:t("输入关键字筛选"),defaultValue:l,onChange:function(n){C=n.target.value,_()}})}),(0,r.jsx)("div",{className:e.list,children:a.map((function(n,o){return(0,r.jsxs)("div",{className:e.item,children:[(0,r.jsx)("img",{src:n.image,alt:"",onClick:function(){p.nb.DispatchEvent(p.n0.ReplaceEntity,n)}}),(0,r.jsx)("div",{className:"title",children:t(n.title)})]},o)}))})]})}))},90974:function(n,t,e){var r=e(13274),o=e(33313),i=e(41594),a=e(27347),c=e(88934),u=e(77320),l=e(36134),s=e(98612),f=e(15696),p=e(9003),d=e(85783),m=e(49063),h=e(23825),g=e(31281),b=e(50316),v=e(65640);function x(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function y(n,t,e,r,o,i,a){try{var c=n[i](a),u=c.value}catch(n){return void e(n)}c.done?t(u):Promise.resolve(u).then(r,o)}function w(n){return function(){var t=this,e=arguments;return new Promise((function(r,o){var i=n.apply(t,e);function a(n){y(i,r,o,a,c,"next",n)}function c(n){y(i,r,o,a,c,"throw",n)}a(void 0)}))}}function S(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,o,i=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw o}}return i}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return x(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return x(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(n,t){var e,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(u){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(e=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(n,i)}catch(n){c=[6,n],r=0}finally{e=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}var k=g.A.Platform,C=u.A.confirm;t.A=(0,f.observer)((function(n){var t=(0,d.B)().t,e=(0,p.P)(),u=(0,m.zy)(),f=(u.pathname,(0,o.A)().styles,S((0,i.useState)(!0),2)),g=f[0],x=f[1],y=S((0,i.useState)([]),2),_=y[0],M=y[1],A=n.disabled,O=function(){a.nb.instance&&a.nb.RunCommand(a._I.ApplyLayout)};return(0,i.useEffect)((function(){x(!0),a.nb.on_M(c.U.SubHandlerChanged,"DesignModeButtons",(function(n){x(!0),e.homeStore.designMode===s.f.ExDrawingMode?!1===n.is_default?M([{name:t("完成"),onClick:function(){a.nb.instance&&a.nb.RunCommand(a._I.LeaveSubHandler)}}]):M([{name:t("退出画笔"),onClick:function(){a.nb.instance&&(a.nb.RunCommand(s.f.AiCadMode),e.homeStore.setDesignMode(s.f.AiCadMode))}}]):e.homeStore.designMode===s.f.RemodelingMode&&("ResultShowSubHandler"==n.name||"HouseCorrectionBaseHandler"==n.name?M([{name:t("应用"),onClick:function(){a.nb.instance&&a.nb.DispatchEvent(a.n0.ApplyRemodelingScheme,null)}}]):x(!1))})),setTimeout((function(){e.homeStore.designMode===s.f.AiCadMode?(M([{name:t("返回户型编辑"),onClick:function(){var n,r;b.s.trackClickEvent(e.homeStore.designMode,"返回户型编辑"),h.Bj?a.nb.instance&&("SingleRoom"==(null===(r=a.nb.instance)||void 0===r||null===(n=r.layout_container)||void 0===n?void 0:n._drawing_layer_mode)&&a.nb.DispatchEvent(a.n0.leaveSingleRoomLayout,{}),a.nb.instance._current_handler_mode=s.f.HouseDesignMode,a.nb.RunCommand(s.f.HouseDesignMode),e.homeStore.setDesignMode(s.f.HouseDesignMode)):C({title:t("确认退出"),content:t("您有未保存的更改，退出后将丢失这些更改。是否确认退出？"),okText:t("取消"),cancelText:t("确认退出"),onOk:function(){v.log("取消退出")},onCancel:function(){v.log("获取宿主的APP_ID",h.sZ),k.Application.closeApp({appId:h.sZ})},okButtonProps:{type:"primary"},cancelButtonProps:{type:"default"}})}},{name:t("下一步"),onClick:function(){b.s.trackClickEvent(e.homeStore.designMode,"下一步"),O()}}]),"/dreamer"!==u.pathname&&"/Dreamer"!==u.pathname&&"dreamer"!==h.yH||M([{name:t("相似匹配"),onClick:function(){e.homeStore.setShowDreamerPopup(!0)}},{name:t("套系快搭"),onClick:function(){O()}}])):e.homeStore.designMode===s.f.ExDrawingMode?M([{name:t("退出画笔"),onClick:function(){a.nb.instance&&(a.nb.RunCommand(s.f.AiCadMode),e.homeStore.setDesignMode(s.f.AiCadMode))}}]):e.homeStore.designMode===s.f.RulerMode?M([{name:t("退出量尺"),onClick:function(){a.nb.instance&&(a.nb.RunCommand(a._I.LeaveSubHandler),e.homeStore.setDesignMode(s.f.AiCadMode))}}]):e.homeStore.designMode===s.f.MeasurScaleMode?M([{name:t("取消"),onClick:function(){a.nb.instance&&a.nb.DispatchEvent(a.n0.ExitMeasureScale,null)}},{name:t("确定"),onClick:function(){a.nb.instance&&a.nb.DispatchEvent(a.n0.ConfirmScale,{img_base64:e.homeStore.img_base64})}}]):e.homeStore.designMode===s.f.RemodelingMode?1==(null==_?void 0:_.length)&&"应用"==_[0].name?M([{name:t("应用"),onClick:function(){a.nb.instance&&a.nb.DispatchEvent(a.n0.ApplyRemodelingScheme,null)}}]):x(!1):e.homeStore.designMode===s.f.HouseCorrectionMode?M([{name:t("应用"),onClick:function(){a.nb.instance&&a.nb.DispatchEvent(a.n0.ApplyHouseCorrectionScheme,null)}}]):M([{name:t("进入布局"),onClick:w((function(){return j(this,(function(n){return b.s.trackClickEvent(e.homeStore.designMode,"进入布局"),a.nb.instance&&(a.nb.RunCommand(s.f.AiCadMode),e.homeStore.setDesignMode(s.f.AiCadMode),"local"===h.yH&&"DwgBase64"===h.Zx&&a.nb.DispatchEvent(a.n0.autoSave,null),"local"===h.yH&&"hxedit"===h.Zx&&a.nb.DispatchEvent(a.n0.autoSave,null)),[2]}))}))}])}),50)}),[e.homeStore.designMode]),(0,r.jsx)(r.Fragment,{children:g&&_.map((function(n){return(0,r.jsx)(l.A,{className:"btn",type:"返回户型编辑"===n.name?"default":"primary",onClick:n.onClick,disabled:A,children:t(n.name)},n.name)}))})}))}}]);