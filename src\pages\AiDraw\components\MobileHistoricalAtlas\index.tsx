import React, { useEffect, useState } from 'react';
import { observer } from "mobx-react-lite";
import { useTranslation } from 'react-i18next';
import { useStore } from '@/models';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { AI2DesignManager } from '@/Apps/AI2Design/AI2DesignManager';
import useStyles from './style';
import { Image, message, Modal, PaginationProps, Tooltip } from '@svg/antd';
import { AIGCService } from '@/Apps/LayoutAI/Services/AIGC/AIGCService';
import IconFont from '@/components/IconFont/iconFont';
import { RenderReqOffline } from '@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline';
import { getImgDomain } from '@svg/request';
import { Segmented } from '@svg/antd'
import { Pagination } from '@svg/antd'; // 引入分页组件
import CustomImagePreview from '@/components/CustomImagePreview/CustomImagePreview';
import { panoReplaceUrl } from '@/config';

interface MobileHistoricalAtlasProps {
    setZIndexOfMobileAtlas: (zIndex: number) => void; // 定义 props 类型
}

export enum atlasMode {
    aidraw = "aidraw",
    render = "render", // 标准渲染
    panoRender = "panoRender" // 全景渲染
}

const App: React.FC<MobileHistoricalAtlasProps> = ({ setZIndexOfMobileAtlas }) => {
    const { t } = useTranslation()
    let store = useStore();
    const { styles } = useStyles();
    const [picList, setPicList] = useState([]);
    const [pageIndexAidraw, setPageIndexAidraw] = useState(1);
    const [pageIndexRender, setPageIndexRender] = useState(1);
    const [pageIndexPanoRender, setPageIndexPanoRender] = useState(1);
    const [pageSize, setPageSize] = useState(15);
    const [total, setTotal] = useState(0);
    const [failedImages, setFailedImages] = useState<Set<string>>(new Set());
    const [previewVisible, setPreviewVisible] = useState<boolean>(false)
    // const [targetImage, setTargetImage] = useState<any>(null)
    const [targetImageIndex, setTargetImageIndex] = useState<number>(0)

    const topTabItems: { [key: string]: atlasMode } = {
        "标准渲染": atlasMode.render,
        ...(store.homeStore.showPanoRender && { "全景渲染": atlasMode.panoRender }),
        "AI绘图": atlasMode.aidraw
    };

    LayoutAI_App.UseApp(AI2DesignManager.AppName); // 确保当前的app_id
    if (LayoutAI_App.instance) {
        LayoutAI_App.t = t;
    }

    const setPicDefault = () => {
        setPicList([]);
        setTotal(0);
    }

    const getScheme = async () => {
        setPicDefault();
        let res = await AIGCService.instance.queryImageList(
            '',
            pageIndexAidraw,
            pageSize
        );
        if(res?.result){
            setPicList(res.result);
            setTotal(res.recordCount);
        } else {
            message.error('网络响应错误')
        }
    }

    const getRenderScheme = async () => {
        setPicDefault();
        let res = await RenderReqOffline.instance.requestAtlas(
            {
                userId: '', // 用户ID
                flag: 'normal_all', // 任务类型
                schemeId: '', // 方案ID
                pageIndex: pageIndexRender,
                pageSize: pageSize,
                all: '', // 标志
                isRtx: 0, // RTX 标识
                resolutionTag: '', // 分辨率标签
                type: 0, // 标识类型
                exclusiveRealAdjustLight: false, // 排除实时调光数据
                authCode: ''
            }
        );
        if (res.success) {
            let data = res?.res?.data
            let dataList = res?.res?.data?.ReturnList;
            if (dataList && dataList?.length > 0) {
                dataList.forEach((item: any) => {
                    item.imageResult = item.Status === 3 ? `${getImgDomain()}/${item.FileIdOutPut2}` : null;
                    item.layoutName = item.Remark
                    item.createDate = item.CreateDate
                    item.imageResultList = [''];
                });
                setPicList(dataList);
                setTotal(data.TotalResults);
            }
        }
    }

    const getPanoRenderScheme = async () => {
        setPicDefault();
        let res = await RenderReqOffline.instance.requestAtlas(
            {
                userId: '', // 用户ID
                flag: 'panorama_all', // 任务类型
                schemeId: '', // 方案ID
                pageIndex: pageIndexPanoRender,
                pageSize: pageSize,
                all: '', // 标志
                isRtx: 0, // RTX 标识
                resolutionTag: '', // 分辨率标签
                type: 1, // 标识类型
                exclusiveRealAdjustLight: false, // 排除实时调光数据
                authCode: ''
            }
        );
        if (res.success) {
            let data = res?.res?.data
            let dataList = res?.res?.data?.ReturnList;
            if (dataList && dataList?.length > 0) {
                dataList.forEach((item: any) => {
                    item.imageResult = item.Status === 3 ? `${getImgDomain()}/${item.FileIdOutPut2}` : null;
                    item.layoutName = item.Remark
                    item.createDate = item.CreateDate
                    item.imageResultList = [''];
                });
                dataList = dataList.filter((item: any) => item.BelongType == 10);
                setPicList(dataList);
                setTotal(data.TotalResults);
            }
        }
    }

    useEffect(() => {
        if (store.homeStore.atlasMode === atlasMode.aidraw) {
            getScheme();
        } else if (store.homeStore.atlasMode === atlasMode.render) {
            getRenderScheme();
        } else if (store.homeStore.atlasMode === atlasMode.panoRender) {
            getPanoRenderScheme();
        }
        setFailedImages(new Set());
    }, [pageIndexAidraw, pageIndexRender, pageIndexPanoRender, store.homeStore.atlasMode,]);

    useEffect(() => {
        if (store.homeStore.refreshAtlas) {
            setPageIndexAidraw(1);
            getScheme();
            store.homeStore.setRefreshAtlas(false);
        }
    }, [store.homeStore.refreshAtlas]);

    const scrollGet = (e: React.UIEvent<HTMLDivElement>) => {
        // const target = e.target as HTMLDivElement;
        // if (target.scrollTop + target.offsetHeight >= target.scrollHeight) {
        //     if (picList.length < total) {
        //         setPageIndex(pageIndex + 1);
        //     }
        // }
    }

    const handleImageError = (imageUrl: string) => {
        setFailedImages(prev => new Set(prev).add(imageUrl));
    };

    // 全景图预览
    const createPanoPreview = (item: any) => {
        const { SchemeId, QueueId, FileIdOutPut } = item;
        const host = panoReplaceUrl;
        const fullUrl = `https://3vj-render.3vjia.com${FileIdOutPut}`;
        return {
            imageRender: () => (
                <iframe
                    width="90%"
                    height="90%"
                    style={{ border: 'none' }}
                    src={`${host}/?schemeId=${SchemeId}&queueId=${QueueId}&url=${fullUrl}&t=${Date.now()}`}
                />
            ),
            toolbarRender: (): null => null,
        };
    };

    const CardPagination: React.FC = () => {
        const onChange: PaginationProps['onChange'] = (page) => {
            if (store.homeStore.atlasMode === atlasMode.aidraw) {
                setPageIndexAidraw(page);
            } else if (store.homeStore.atlasMode === atlasMode.render) {
                setPageIndexRender(page);
            } else if (store.homeStore.atlasMode === atlasMode.panoRender) {
                setPageIndexPanoRender(page);
            }
        };

        return (
            <Pagination
                simple={true}
                defaultCurrent={1}
                current={
                    store.homeStore.atlasMode === atlasMode.aidraw
                        ? pageIndexAidraw
                        : store.homeStore.atlasMode === atlasMode.render
                            ? pageIndexRender
                            : pageIndexPanoRender
                }
                onChange={onChange}
                total={total}
                pageSize={15}
                showSizeChanger={false}
            />
        );
    };

    const openPreview = (item: any, index: number) => {
        console.log(item)
        if (store.homeStore.atlasMode === atlasMode.panoRender) {
            // createPanoPreview(item)
            return
        }
        setPreviewVisible(true)
        // setTargetImage(item)
        setTargetImageIndex(index)
    }

    const deleteImage = async (item: any) => {
        if (!item) return

        // 确认弹窗
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除该图片吗？',
            okText: '删除',
            cancelText: '取消',
            onOk: async () => {
                try {
                    // 获取当前模式和对应的图片ID
                    const currentMode: keyof typeof modeConfig = store.homeStore.atlasMode as keyof typeof modeConfig;
                    const imageId = currentMode === atlasMode.aidraw
                        ? item?.id
                        : item?.QueueId;

                    // 验证图片ID是否存在
                    if (!imageId) {
                        throw new Error('图像ID缺失，无法执行删除操作');
                    }

                    // 定义不同模式对应的删除方法和刷新方法映射
                    const modeConfig = {
                        [atlasMode.aidraw]: {
                            deleteMethod: () => AIGCService.instance.deleteImage(imageId),
                            refreshMethod: getScheme,
                            successCheck: (res: any) => res.success
                        },
                        [atlasMode.render]: {
                            deleteMethod: () => RenderReqOffline.instance.delTargetAtlas(imageId),
                            refreshMethod: getRenderScheme,
                            successCheck: (res: any) => res.Status === 200
                        }
                    };

                    // 检查当前模式是否支持
                    if (!modeConfig[currentMode]) {
                        throw new Error(`不支持的模式: ${currentMode}，请补充对应删除接口`);
                    }

                    // 执行删除操作
                    const { deleteMethod, refreshMethod, successCheck } = modeConfig[currentMode];
                    const response = await deleteMethod();

                    // 处理删除结果
                    if (successCheck(response)) {
                        refreshMethod();
                        setPreviewVisible(false);
                        // setTargetImage(null);
                    } else {
                        Modal.error({
                            title: '删除失败',
                            content: `操作未成功完成，模式: ${currentMode}`
                        });
                    }

                } catch (err) {
                    const errorMessage = err instanceof Error ? err.message : '未知错误';
                    Modal.error({
                        title: '删除失败',
                        content: `错误信息: ${errorMessage}，请重试`
                    });
                    console.error('删除操作出错:', err);
                }
            }
        })
    }

    useEffect(() => {

        if (store.homeStore.atlasMode === atlasMode.aidraw) {
            getScheme();
        } else if (store.homeStore.atlasMode === atlasMode.render) {
            getRenderScheme();
        } else if (store.homeStore.atlasMode === atlasMode.panoRender) {
            getPanoRenderScheme();
        }

    }, [store.homeStore.genCount])


    return (
        <div className={styles.root}>
            <div className='atlas_header'>
                <Segmented options={Object.keys(topTabItems)}
                    value={Object.keys(topTabItems).find(key => topTabItems[key] === store.homeStore.atlasMode)}
                    onChange={(value => {
                        store.homeStore.setAtlasMode(topTabItems[value]);
                    })}
                    className='segmented' />
                {
                    store.homeStore.showAtlas === true
                        ? <div className='back_button' onClick={() => store.homeStore.setShowAtlas(false)}>
                            <IconFont style={{ margin: '7px 2px 7px 12px' }} type="icon-line_left" />
                            <span style={{ height: 22, width: 28 }}>{t('返回')}</span>
                        </div>
                        : null
                }
            </div>

            {previewVisible && <CustomImagePreview
                onClose={() => setPreviewVisible(false)}
                openIndex={targetImageIndex}
                items={picList}
                onDelete={(item) => deleteImage(item)}
                getImageUrl={(item) => item.imageResult}
                // btnConfig={{delete: false}}
            />}

            {picList.length <= 0 && (
                <div className={styles.noData}>{t('-暂无数据-')}</div>
            )}
            <div className={styles.content} onScroll={scrollGet}>
                {picList.map((item, index) => {
                    // console.log(`${index}, ${item.imageResult}`)
                    return (
                        <div className={styles.item} key={"history_" + index} onClick={() => null}>
                        {item.imageResult && !failedImages.has(item.imageResult) ? (
                            <div className='main_img_container'>
                                <Image
                                    draggable={false}
                                    src={item.imageResult+'?x-oss-process=image/resize,m_fixed,w_400'}
                                    preview={store.homeStore.atlasMode === atlasMode.panoRender
                                        ? createPanoPreview(item)
                                        : false
                                    }
                                    onClick={() => openPreview(item, index)}
                                    onError={e => {
                                        console.log('zzz 图片加载错误！')
                                        const imgElem = e.target as HTMLImageElement;
                                        let retryCount = imgElem.getAttribute('data-retry-count') ? Number(imgElem.getAttribute('data-retry-count')) : 0;
                                    
                                        // 如果已经在重试，直接返回，避免并发
                                        if (imgElem.getAttribute('data-retrying') === 'true') return;
                                    
                                        if (retryCount < 10) {
                                            imgElem.setAttribute('data-retrying', 'true');
                                            imgElem.src = '';
                                            imgElem.style.background = '#f7f5f2';
                                            setTimeout(() => {
                                                imgElem.setAttribute('data-retry-count', String(retryCount + 1));
                                                imgElem.setAttribute('data-retrying', 'false');
                                                imgElem.src = item.imageResult + `?retry=${Date.now()}`;
                                                console.log('zzz 重试请求图片');
                                            }, 1000);
                                        } else {
                                            handleImageError(item.imageResult);
                                        }
                                    }}
                                />
                                <div className='number_tag'>{index+1}</div>
                            </div>
                        ) : (
                            <div className='main_loading_container'>
                                <img src={'https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png'} alt="" style={{ height: '40px' }} />
                                <span>{t('生成中...')}</span>
                            </div>
                        )}
                        <div className='info_content'>
                            <Tooltip title={t(item.layoutName)}>
                                <span className='name'>{t(item.layoutName)}</span>
                            </Tooltip>
                            <Tooltip title={t(item.createDate)}>
                                <span className='time'>{t(item.createDate.split(' ')[0])}</span>
                            </Tooltip>
                        </div>
                    </div>
                    )
                })}
            </div>
            {picList.length > 0 && (
                <div key="tablePagination" className={styles.PageContainer}>
                    <CardPagination />
                </div>
            )}
        </div>
    );
};

export default observer(App);
