import { LayoutAI_App, LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { AI2DesignBasicModes, AI2DesignManager } from "../AI2DesignManager";
import { FileOpenResult, I_MouseEvent } from "@layoutai/z_polygon";
import { CadDrawingLayerType } from "@/Apps/LayoutAI/Drawing/TDrawingLayer";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { EzdxfSubHandler } from "./SubHandlers/EzdxfSubHandlers/EzdxfSubHandler";
import { EventName } from "@/Apps/EventSystem";
import { gatewayUrl } from "@/config";
import { openFileInput } from "@/Apps/LayoutAI/Utils/file_utils";


export class EzDxfEditModeHandler extends AI2BaseModeHandler {

    constructor(manager: AI2DesignManager) {
        super(manager, AI2DesignBasicModes.EzDxfEditMode);
        this._transform_elements = [];
        this._cad_default_sub_handler = new EzdxfSubHandler(this as any);

        this._initial_scheme_data = null;  // 初始的方案数据

    }

    get exsorb_rects() {
        return this._exsorb_target_rects;
    }

    get container() {
        return this.manager.layout_container;
    }

    get wall_entities() {
        return this.manager.layout_container._wall_entities;
    }

    set wall_entities(rects: TWall[]) {
        this.manager.layout_container._wall_entities = rects;
    }

    enter(state?: number): void {
        super.enter(state);
        console.log("enter EzdxfEditMode");

        if (!this.container.ezdxf_cad_data) {
            alert(LayoutAI_App.t("图纸数据为空"));

        }
        for (let layer of this.manager._visible_layers) {
            layer.visible = false;
        }
        this.manager.layer_CadEzdxfLayer.visible = true;

        if (this.manager.drawing_layers[CadDrawingLayerType.CadDecorates]) {
            this.manager.drawing_layers[CadDrawingLayerType.CadDecorates].visible = false;
        }

        if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.updateCandidateRects();
            this._cad_default_sub_handler.updateAttributes("hide", 1);

            this._cad_default_sub_handler.enter();
        }
        this.manager.onLayerVisibilityChanged();
        this.updateEzdxfLayers();
        this.painter._use_ezdxf_color = false;
        this.update();
    }
    leave(state?: number): void {
        super.leave(state);

        LayoutAI_App.emit_M(EventName.EzdxfEditLeaved, true);
        this.painter._use_ezdxf_color = true;
        if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.updateCandidateRects();
            this._cad_default_sub_handler.updateAttributes("hide", 0);
            this._cad_default_sub_handler.leave();


        }
        console.log("leave EzdxfEditMode");

    }

    updateEzdxfLayers() {

    }

    onmousedown(ev: I_MouseEvent): void {

        if (this._active_sub_handler) {
            this._active_sub_handler.onmousedown(ev);
        }
        else if (this._cad_default_sub_handler) {
            // 默认方法mouse_down后, 有可能触发进入新的active_handler
            this._cad_default_sub_handler.onmousedown(ev);

            if (this._active_sub_handler) {
                this._active_sub_handler.onmousedown(ev);
            }


        }

        this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

        this._is_painter_center_moving = false;
        // super.onmousedown(ev);

    }
    onmousemove(ev: I_MouseEvent): void {

        if (this._active_sub_handler) {
            super.onmousemove(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onmousemove(ev);
            }
            super.onmousemove(ev);
        }
        // super.onmousemove(ev);
    }
    onmouseup(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            super.onmouseup(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onmouseup(ev);
            }
            super.onmouseup(ev);
        }

    }

    onwheel(ev: WheelEvent): void {
        if (this._active_sub_handler) {
            super.onwheel(ev);
            this._active_sub_handler.onwheel(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onwheel(ev);
            }
            super.onwheel(ev);
        }
    }

    onkeydown(ev: KeyboardEvent): boolean {
        if (this._active_sub_handler) {
            this._active_sub_handler.onkeydown(ev);
        }
        else if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.onkeydown(ev);
        }


        if (ev.ctrlKey) {
            if (ev.code === 'KeyZ') {
                this.runCommand(LayoutAI_Commands.Undo);
            }
            else if (ev.code === "KeyY") {
                this.runCommand(LayoutAI_Commands.Redo);

            }
        }

        return true;
    }

    loadEzdxfCadData(ezdxf_data: any, needs_compute_layouts: boolean = true) {
        this.manager.layout_container._layout_scheme_id = null;
        this.manager.layer_CadEzdxfLayer.loadData(ezdxf_data);
        this.manager.layer_CadEzdxfLayer.makeDirty();
        this.manager.layer_CadEzdxfLayer._updateLayerContent();
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false });
        LayoutAI_App.emit(EventName.EzdxfFileLoaded, true);
        this._cad_default_sub_handler.enter();


        return false;

    }

    async openCadFileDialog() : Promise<any>{
        let fileResult: FileOpenResult = await openFileInput(".dwg", "Base64");
        const layoutSchemeName: string = fileResult.file;
        LayoutAI_App.emit(EventName.LayoutSchemeOpened, { id: null, name: layoutSchemeName });
        this.manager.layout_container._layout_scheme_name = layoutSchemeName;
        let base64_str: string = fileResult.content;
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true });
        let id = base64_str.indexOf("base64,");

        base64_str = base64_str.substring(id + 'base64,'.length);
        let res = await fetch(`${gatewayUrl}/oda-dxfjson/api/oda/dwg64json`, {
            method: 'POST',
            credentials: "include",
            mode: "cors", // no-cors, *cors, same-origin
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                'dwgFile': base64_str
            })
        }).then(val => val.json()).catch(err => {
            console.log(err);
            LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false });
            return null;
        });

        if (res) {

            let json_data = decodeURIComponent(res.data.replace(/\\\\U\+/gi, '%u'));

            let data = JSON.parse(json_data);

            this.loadEzdxfCadData(data);

        }
        else {
            // this.EventSystem.emit(EventName.OpenCADFileHandle,{opening:false});
            // return null;
        }
        this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
        return null;

    }

    async runCommand(cmd_name: string): Promise<void> {
        if (!cmd_name) return;

        super.runCommand(cmd_name);
        if (cmd_name === LayoutAI_Commands.Reset) {
            this.reset();
        }
        // 删除
        else if (cmd_name === LayoutAI_Commands.DeleteFurniture) {
            this._cad_default_sub_handler.deleteElement();
        }
        else if (cmd_name === LayoutAI_Commands.RotateFurniture) {
            this._cad_default_sub_handler.rotate();
        }
        if (cmd_name === LayoutAI_Commands.OpenDwgFile) {
            this.openCadFileDialog();
        }
    }
    handleEvent(event_name: string, event_param: any) {

        super.handleEvent(event_name, event_param);
    }
    reset() {

    }
    drawCanvas(): void {


        if (this.manager.layer_DefaultBatchLayer) {
            let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
            if (!batchDirty) {
                if (this._is_painter_center_moving
                    && this.manager.layer_DefaultBatchLayer.cleaned_drawing_count > 5) {
                    batchDirty = true;
                }
            }
            this.manager.layer_DefaultBatchLayer.drawLayer(batchDirty);

        }
        if (this._active_sub_handler) {
            this._active_sub_handler.drawCanvas();
        }
        else if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.drawCanvas();
        }
    }

}