import { Path, Vector2, Vector3, Vector3<PERSON><PERSON> } from "three";
import { TAppManagerBase } from "../../AppManagerBase";
import { I_EzdxfJsonData } from "../AICadData/EzdxfEntity";
import { TFigureElement } from "../Layout/TFigureElements/TFigureElement";
import { TWindowDoorEntity } from "../Layout/TLayoutEntities/TWinDoorEntity";
import { Math_Round } from "@layoutai/z_polygon";
import { I_EzdxfAttribs, I_EzdxfEntity, I_EzdxfLayout } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";

export class TExportCadDrawingLayer extends TDrawingLayer {


    _view_center: Vector3;
    constructor(manager: TAppManagerBase) {
        super(CadDrawingLayerType.ExportCadDrawing, manager);
        this._view_center = new Vector3();
    }


    protected _makePolygonEzdxfEntity(polygon: ZPolygon, attribs: I_EzdxfAttribs = {}, offset: Vector3Like = { x: 0, y: 0, z: 0 }) {
        let positions = polygon.positions;
        positions.push(polygon.vertices[0].pos.clone());

        let entity: I_EzdxfEntity = {
            type: "LWPOLYLINE",
            lwpoints: positions.map((v) => [Math_Round(v.x - offset.x, 1000), Math_Round(v.y - offset.y, 1000)]),
            attribs: attribs,
        }
        return entity;
    }

    protected _makeCurveEzdxfEntity(path: Path, attribs: I_EzdxfAttribs = {}, offset: Vector3Like = { x: 0, y: 0, z: 0 }) {
        let points: number[][] = [];
        let t_points: Vector2[] = [];
        for (let curve of path.curves) {
            if (curve.type === "LineCurve") {
                t_points.push(...curve.getPoints(2));
            }
            else {
                t_points.push(...curve.getPoints(20));
            }
        }
        let ans_points: Vector2[] = [];
        let last_point: Vector2 = null;
        for (let p of t_points) {
            if (!last_point) {
                ans_points.push(p);
                last_point = p;
            }
            else if (last_point.distanceTo(p) > 0.0001) {
                ans_points.push(p);
                last_point = p;
            }
        }

        points = ans_points.map((p) => [Math_Round(p.x - offset.x, 1000), Math_Round(p.y - offset.y, 1000)]);

        let entity: I_EzdxfEntity = {
            type: "LWPOLYLINE",
            lwpoints: points,
            attribs: attribs,
        }
        return entity;

    }

    protected _makeInsertEntity(category: string, rect: ZRect, attribs: I_EzdxfAttribs = {}, offset: Vector3Like = { x: 0, y: 0, z: 0 }) {
        let block_name = category;
        let entity: I_EzdxfEntity = {
            type: "INSERT",
            attribs: attribs,
        }
        entity.attribs.name = block_name;
        entity.insert = [Math_Round(rect.rect_center.x - offset.x), Math_Round(rect.rect_center.y - offset.y)];
        entity.attribs.xscale = rect.w;
        entity.attribs.yscale = rect.h;
        entity.attribs.rotation = Math_Round(rect.rotation_z / Math.PI * 180);

        return entity;
    }
    protected _makeFigureInsertEntity(figure: TFigureElement, attribs: I_EzdxfAttribs = {}, offset: Vector3Like = { x: 0, y: 0, z: 0 }) {
        let pattern = this.painter._svg_pattern_dict[figure.sub_category];
        if (!pattern) return null;
        return this._makeInsertEntity(figure.sub_category, figure.rect, attribs, offset);
    }

    protected _makeTextEntity(text: string, rect: ZRect, attribs: I_EzdxfAttribs = {}, offset: Vector3Like = { x: 0, y: 0, z: 0 }) {
        let entity: I_EzdxfEntity = {
            type: "MTEXT",
            attribs: attribs,
            text: text
        }
        entity.insert = [Math_Round(rect.rect_center.x - offset.x), Math_Round(rect.rect_center.y - offset.y)];
        entity.attribs.char_height = 150;
        entity.attribs.rotation = Math_Round(rect.rotation_z / Math.PI * 180 - 180);
        return entity;

    }
    exportEzdxfCadData() {
        let output: I_EzdxfJsonData = {
            modelspace: {
                entities: [],
                name: ""
            },
            blocks: {},
            layers: {}
        };


        this._view_center.copy(this.painter.p_center).setZ(0);


        for (let key in this.painter._svg_pattern_dict) {
            let pattern = this.painter._svg_pattern_dict[key];
            let block: I_EzdxfLayout = {
                name: key,
                entities: []
            }
            if (pattern.drawing_elements) {
                for (let ele of pattern.drawing_elements) {
                    if (!ele.curve_path) continue;
                    block.entities.push(this._makeCurveEzdxfEntity(ele.curve_path, {}));
                }
            }

            if (block.entities.length > 0) {
                output.blocks[key] = block;
            }
        }


        // 先画墙线

        let outter_polygons = this.layout_container._outter_border_polygons;

        let t_polygons: ZPolygon[] = [];

        let sub_polygons: ZPolygon[] = [];
        for (let wall of this.layout_container._wall_entities) {
            let rect = wall.rect.clone();

            let sub_rects: ZRect[] = [];

            for (let win_rect of wall._win_rects) {
                let entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(win_rect) as TWindowDoorEntity;

                if (entity.type === "Door") {
                    let t_rect = win_rect.clone();
                    let r_center = t_rect.rect_center;
                    t_rect._h += 5;
                    t_rect.rect_center = r_center;
                    if (t_rect.orientation_z_nor.z < 0) {
                        t_rect.invertOrder();
                    }
                    sub_rects.push(t_rect);
                }
            }

            let ans_polys = rect.substract_polygons(sub_rects);

            t_polygons.push(...ans_polys);
        }

        let ans_polygons = t_polygons[0].union_polygons(t_polygons);


        for (let poly of ans_polygons) {
            output.modelspace.entities.push(this._makePolygonEzdxfEntity(poly, { layer: "墙" }, this._view_center));
        }

        // for(let room of this.layout_container._room_entities)
        // {
        //     output.modelspace.entities.push(this._makePolygonEzdxfEntity(room._room_poly,{layer:"墙",color:0},this._view_center));
        // }


        for (let win of this.layout_container._window_entities) {
            if (win.type === "Door") {
                if (win.realType === "SlidingDoor") {
                    let entity = this._makeInsertEntity("推拉门", win.rect, { layer: "推拉门" }, this._view_center);
                    if (entity) {
                        output.modelspace.entities.push(entity);
                    }
                }
                else {
                    let rect = win.rect.clone();
                    rect.back_center = rect.front_center;
                    rect.nor = rect.nor.negate();
                    rect._h = rect._w;
                    rect.updateRect();
                    let entity = this._makeInsertEntity("单开门", rect, { layer: "单开门" }, this._view_center);
                    if (entity) {
                        output.modelspace.entities.push(entity);
                    }
                }
            }
            else {
                let entity = this._makeInsertEntity("一字窗", win.rect, { layer: "一字窗" }, this._view_center);
                if (entity) {
                    output.modelspace.entities.push(entity);
                }

            }
        }
        for (let structure of this.layout_container._structure_entities) {
            output.modelspace.entities.push(this._makePolygonEzdxfEntity(structure.rect, { layer: structure.ui_realType }, this._view_center));

        }

        for (let furniture of this.layout_container._furniture_entities) {
            for (let ele of furniture.displayed_figure_elements) {
                let pattern = this.painter._svg_pattern_dict[ele.sub_category];
                if (!pattern) continue;
                let entity = this._makeFigureInsertEntity(ele, { layer: furniture.ui_realType }, this._view_center);
                if (entity) {
                    output.modelspace.entities.push(entity);
                }
            }
        }

        for (let room_entity of this.layout_container._room_entities) {
            let room_name = LayoutAI_App.t(room_entity.name);

            let t_rect = new ZRect(10, 10);
            t_rect.rect_center = room_entity._main_rect.rect_center;
            let entity = this._makeTextEntity(room_name, t_rect, {}, this._view_center);
            if (entity) {
                output.modelspace.entities.push(entity);
            }
        }

        output.layers = {};
        output.layers["墙"] = { color: 0 };
        output.layers["一字窗"] = { color: 2 };
        output.layers["单开门"] = { color: 4 };
        output.layers["软装家具"] = { color: 3 };
        output.layers["家具"] = { color: 3 };
        output.layers["定制柜"] = { color: 6 }
        // saveJsonAs(output,"ezdxf_test.json");
        return output;


    }
    onDraw(): void {


    }
}