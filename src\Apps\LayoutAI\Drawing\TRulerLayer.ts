import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { LayoutAI_App } from '../../LayoutAI_App';
import { TAppManagerBase } from "../../AppManagerBase";

export class TRulerLayer extends TDrawingLayer
{    

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.RulerDrawing,manager);
        this._manager = manager;
    }

    onDraw()
    {   
        for(let entity of this.layout_container._ruler_entities)
        {
            if(entity.is_selected)
            {
                continue;
            }
            entity.drawEntity(this.painter,{});
        }
    }
}
