import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
    global: css`
        html, body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        }
    `,
    container: css`
        /* height: 100vh; */
        /* width: 100vw; */
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    `,
    header: css`
        display: flex;
        justify-content: space-between;
        padding: 10px 20px;
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 100;
        background: #f2f3f5;
        width: 100%;
    `,
    scrollContainer: css`
        flex: 1;
        overflow-y: auto;
    `,
    loading: css`
        height: 60vh;
        width: 100vw;
        display: flex;
        justify-content: center;
        align-items: center;
    `,
    headerForSearch: css`
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
    `,
    search: css`
        width: 250px;
    `,
    PageContainer: css`
        display: flex;
        flex-direction: row-reverse;
        position: sticky;
        bottom: 0;
        padding: 5px 0 20px 0;
    `,
    submitButton: css`
        background: linear-gradient(90deg, #ba63f0 0%, #5c42fb 100%) !important;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        text-align: center;
        transition: background 0.3s;
        &:hover {
        background: linear-gradient(90deg, #d07bff 0%, #7a5bff 100%) !important;
        }
    `,
    cancelButton: css`
        background: #f4f5f5 !important;
        color: #282828 !important;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        text-align: center;
        transition: background 0.3s;
    `,
    deleteModal: css`
    `,
    deleteMessage: css`
        margin: 40px 0;
        p:last-of-type {
            margin: 2px 0; // 两行文字上下间隔缩小
            color: #7d7d7d; // 第二行文字设置为深灰色
        }
    
        p {
            margin: 2px 0; // 两行文字上下间隔缩小
        }
    `
}));