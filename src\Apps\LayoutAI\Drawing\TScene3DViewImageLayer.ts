import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { Scene3D } from "../Scene3D/Scene3D";
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { CameraViewMode } from "../Scene3D/SceneMode";
import { BirdsEyeControls } from "../Scene3D/controls/BirdsEyeControls";
import { ZRect } from "@layoutai/z_polygon";
import { FloatType, NearestFilter, RGBAFormat, SRGBColorSpace, WebGLRenderTarget } from "three";
import { OutlinePostProcess } from "../Scene3D/builder/OutlinePostProcess";
import { Scene3DManager } from "../Scene3D/Scene3DManager";


/**
 *  3D跟2D对齐
 */
export class TScene3DViewImageLayer extends TDrawingLayer
{
    protected _img : HTMLImageElement;
    protected _rect : ZRect;
    protected _renderTarget : WebGLRenderTarget;
    constructor(manager : TAppManagerBase)
    {
        super(CadDrawingLayerType.Scene3DViewImageLayer,manager);
        this._img = new Image();
        this._rect = new ZRect(1,1);
        this._renderTarget = null;
    
    }
    get scene3D()
    {
        return this._manager.scene3D;
    }
    async updateImage(fixed_scale:number=1.)
    {

        this._img.src = "";
        this._img = null;
        let scene3D = this.scene3D as Scene3D;
        if(!scene3D || !scene3D.isValid()) return;

        if(!this._renderTarget)
        {
            this._renderTarget = new WebGLRenderTarget(scene3D.viewWidth,scene3D.viewHeight,{
                format : RGBAFormat,
                depthBuffer : true,
                stencilBuffer : true,
                colorSpace:SRGBColorSpace
            })
        }
        let controls = scene3D.controls[CameraViewMode.Perspective] as BirdsEyeControls;
        if(!controls) return;
        // AppSetting.instance.performanceMode = PerformanceMode.High;
        let res = controls.focusCenterWithFixedScale(1.0);
        controls.update();

        let outlinePostProcessing = scene3D.outlinePostProcessing;
        let origin_mode = scene3D.outlineMaterialMode;
        let outline_style_mode = outlinePostProcessing.outline_style_mode;
        scene3D.outlineMaterialMode = 0;
    
        let oldCamera = outlinePostProcessing.camera;
        outlinePostProcessing.camera = controls.camera;
        scene3D.makeDirty();
        outlinePostProcessing.renderToTarget(this._renderTarget);
        outlinePostProcessing.camera = oldCamera;
        

        this._img = new Image();
        this._img.src = Scene3DManager.getRenderTargetImg(scene3D.renderer,this._renderTarget);
        // this._img.src = (scene3D.renderer.domElement as HTMLCanvasElement).toDataURL();
        scene3D.outlineMaterialMode = origin_mode;
        outlinePostProcessing.outline_style_mode = outline_style_mode;
        (scene3D.active_controls as any)._updateMeshVisibleByCameraPosition();
        scene3D.makeDirty();
        scene3D.update();
        if(res.mainFlag)
        {
            this._rect._w = res.boxWidth;
            this._rect._h = res.boxWidth / res.width * res.height;
        }
        else{
            this._rect._w = res.boxHeight / res.height * res.width;
            this._rect._h = res.boxHeight ;
        }


        this._rect.nor.set(0,-1,0);
        this._rect.rect_center = {x:res.transform._p_center.x,y:res.transform._p_center.y,z:0};
        if(!this._img) return false;
        return new Promise((resolve,reject)=>{
            this._img.onload = ()=>{
                
                resolve(true);
            }
            if(this._img.complete)
            {
                resolve(true);
            }
        });

    }
    onDraw(): void {
        if(this._img && this._img.width > 1)
        {
            this.painter._context.globalAlpha = 1.;
            this.painter.fillImageInRect(this._img, this._rect);
            // this.painter.strokeStyle = "#f00";
            // this.painter.strokePolygons([this._rect]);

            // this.painter.strokeStyle = "#0f0";
            // if(this.layout_container._room_entities)
            // {
            //     this.painter.strokePolygons(this.layout_container._room_entities.map((e)=>e._room_poly));
            // }
        }
    }
}