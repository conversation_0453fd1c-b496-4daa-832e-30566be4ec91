import { I_MouseEvent } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { AI2DesignBasicModes, AI2DesignManager } from "../AI2DesignManager";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { I_SwjXmlScheme } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { EventName } from "@/Apps/EventSystem";
import { HouseCorrectionBaseHandler } from "./SubHandlers/HouseCorrectionSubHandlers/HouseCorrectionBaseHandler";

/**
 * 户型纠正模式
 */
export class HouseCorrectionModeHandler extends AI2BaseModeHandler {
    
    public original_swj_layout_data: I_SwjXmlScheme;
    private _correction_swj_layout_data: I_SwjXmlScheme;

    constructor(manager: AI2DesignManager) {
        super(manager, AI2DesignBasicModes.HouseCorrectionMode);
        this._cad_default_sub_handler = new HouseCorrectionBaseHandler(this);
        this._sub_handlers[LayoutAI_Commands.EnterHouseCorrection] = this._cad_default_sub_handler;
    }

    enter(state?: number): void {
        super.enter(state);

        // 设置户型纠正模式的图层状态
        this.manager.layer_CadFloorLayer.visible = true;
        this.manager.layer_CadRoomFrameLayer.visible = true;
        this.manager.layer_CadFurnitureLayer.visible = false;
        this.manager.layer_LightingLayer.visible = false;
        this.manager.layer_OutLineLayer.visible = true;
        this.manager.layer_CadRoomNameLayer.visible = true;
        this.manager.layer_CadEzdxfLayer.visible = true;
        this.manager.layer_CadCabinetLayer.visible = false;
        this.manager.layer_CadCopyImageLayer.visible = false;

        // 保存原始数据
        this.original_swj_layout_data = this.manager.layout_container.current_swj_layout_data;

        this.manager.onLayerVisibilityChanged();
        this.painter._p_sc = 0.05;
        this.manager.layout_container.focusCenter();

        this.runCommand(LayoutAI_Commands.EnterHouseCorrection);

        LayoutAI_App.emit(EventName.SetExitBarVisible, {
            visible: true,
            title: '户型纠正模式',
            onExit: () => this._onExitBar()
        });
        
        this.update();
    }

    leave(state?: number): void {
        super.leave(state);
    }

    private _leaveSubHandler(swj_layout_data: I_SwjXmlScheme | null) {
        if (this._active_sub_handler) {
            this._active_sub_handler.leave();
        }
        if (swj_layout_data) {
            this.manager.layout_container.fromXmlSchemeData(swj_layout_data);
        }
        LayoutAI_App.emit(EventName.SetExitBarVisible, { visible: false });
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
        this.manager.onLayerVisibilityChanged();
        this.manager.layout_container.focusCenter();
        this.painter._p_sc = 0.05;
        this.update();
        this._correction_swj_layout_data = null;
    }

    onmousedown(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            this._active_sub_handler.onmousedown(ev);
        }
        this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };
        this._is_painter_center_moving = false;
        this._is_moving_element = false;
    }

    onmousemove(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            super.onmousemove(ev);
        }
    }

    onmouseup(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            super.onmouseup(ev);
        }
        this._last_mouse_down_event = null;
    }

    onwheel(ev: WheelEvent): void {
        if (this._active_sub_handler) {
            super.onwheel(ev);
            this._active_sub_handler.onwheel(ev);
        }
    }

    drawCanvas(): void {
        if (this.manager.layer_DefaultBatchLayer) {
            this.manager.layer_DefaultBatchLayer.drawLayer(true);
        }

        if (this._active_sub_handler) {
            this._active_sub_handler.drawCanvas();
        }
    }

    handleEvent(event_name: string, event_param: any): void {
        super.handleEvent(event_name, event_param);
        if (event_name === LayoutAI_Events.ApplyHouseCorrectionScheme) {
            this._applyHouseCorrectionScheme();
        }
    }
    private _applyHouseCorrectionScheme() {
        this._leaveSubHandler(null);
    }

    private _onExitBar(): void {
        this._leaveSubHandler(this.original_swj_layout_data);
    }


} 