import { openApiRequest } from "@/utils";

/**
 * @description 组合入库
 */
export async function combinationStorage(params: any) {
    const res = await openApiRequest({
      method: 'post',
      url: `api/njvr/layoutMetaImage/insert`,
      data: {
          ...params,
      },
      timeout: 60000,
    }).catch((e:any): null => {
      return null;
    });
    return res;
  }
/**
 * @description 创建布局图元尺寸链
 */
export async function combinationSizeStorage(params: any) {
    const res = await openApiRequest({
      method: 'post',
      url: `api/njvr/layoutMetaImageSize/insert`,
      data: {
          ...params,
      },
      timeout: 60000,
    }).catch((e:any): null => {
      return null;
    });
  ;
    return res;
  }