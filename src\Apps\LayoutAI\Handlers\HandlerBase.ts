import { CommandHandler, I_CommandInput } from "../Utils/CommandInput/CommandInput";
import { I_MouseEvent } from "@layoutai/z_polygon";



export class T_Handlerbase extends CommandHandler
{

    constructor(name:string = "", command_inputs : I_CommandInput[] = [])
    {
        super(name,command_inputs);
    }
    async prepare(load_test_data:boolean = true)
    {

    }
    enter(state : number = 0)
    {
    }
    leave(state : number = 0)
    {
        
    }
    runCommand(cmd_name:string)
    {

    }

    handleEvent(evt_name:string, evt_param:any) {
    }

    onchange()
    {
        
    }

    undo()
    {

    }

    redo()
    {
        
    }

    onmousedown(ev : I_MouseEvent)
    {

    }
    onmousemove(ev : I_MouseEvent)
    {

    }
    onmouseup(ev : I_MouseEvent)
    {

    }

    /**
     * @param ev 
     * @returns boolean  :  true, 常规;  false: 停止向上冒泡
     */
    onkeydown(ev : KeyboardEvent) 
    {
    
        if(ev.code === "Delete")
        {
            this.runCommand("Delete");
        }
        return true;
    }
    onkeyup(ev : KeyboardEvent)
    {
        return true;
    }
    ondbclick(ev : I_MouseEvent)
    {
        this.onmousedown(ev);
        this.onmouseup(ev);
    }

    onwheel(ev : WheelEvent)
    {
        
    }
}