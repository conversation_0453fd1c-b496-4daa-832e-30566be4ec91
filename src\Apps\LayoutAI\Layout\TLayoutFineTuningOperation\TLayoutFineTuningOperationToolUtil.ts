import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { Vector3 } from "three";
import { TRoom } from "../TRoom";
import { TFurnitureEntity } from "../TLayoutEntities/TFurnitureEntity";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { ZRect } from "@layoutai/z_polygon";

export enum FineTuningType{
    k_left,
    k_right,
    k_front,
    k_back,
    k_leftFront,
    k_leftBack,
    k_rightFront,
    k_rightBack,
};


// 直接对原始体进行缩放
export class TLayoutFineTuningOperationToolUtil
{
    private static _instance: TLayoutFineTuningOperationToolUtil;

    public static get instance(): TLayoutFineTuningOperationToolUtil
    {
        if(!this._instance)
        {
            this._instance = new TLayoutFineTuningOperationToolUtil();
        }
        return this._instance;
    }

    // 移动
    public moveFigure(figure: TFigureElement, moveVec: Vector3, isNeedUpdate: boolean = false)
    {
        figure.rect.rect_center = figure.rect.rect_center.clone().add(moveVec);
        if(isNeedUpdate)
        {
            figure.updateFigure();
            LayoutAI_App.instance.update();
        }
    }

    public moveRect(rect: ZRect, moveVec: Vector3)
    {
        rect.rect_center = rect.rect_center.clone().add(moveVec);
    }

    // 旋转
    public rotateFigure(figure: TFigureElement, angle: number, isNeedUpdate: boolean = false)
    {
        let oldRectCenter: Vector3 = figure.rect.rect_center.clone();
        let axis = new Vector3(0, 0, 1);
        figure.rect.nor.applyAxisAngle(axis, figure.rect.u_dv_flag * angle);
        let rotateRectCenter: Vector3 = figure.rect.rect_center.clone();
        let moveVec: Vector3 = oldRectCenter.clone().sub(rotateRectCenter);
        this.moveFigure(figure, moveVec);
        if(isNeedUpdate)
        {
            figure.updateFigure();
            LayoutAI_App.instance.update();
        }
    }

    // 缩放
    public scaleFigure(figure: TFigureElement, scaleVec: Vector3, scaleType: FineTuningType, isNeedUpdate: boolean = false)
    {
        let dirValue: number = 0;
        let norValue: number = 0;
        switch(scaleType)
        {
            case FineTuningType.k_left:
            {
                dirValue = -1;
                norValue = 0;
                break;
            }
            case FineTuningType.k_right:
            {
                dirValue = 1;
                norValue = 0;
                break;
            }
            case FineTuningType.k_front:
            {
                dirValue = 0;
                norValue = 1;
                break;
            }
            case FineTuningType.k_back:
            {
                dirValue = 0;
                norValue = -1;
                break;
            }
            case FineTuningType.k_leftFront:
            {
                dirValue = -1;
                norValue = 1;
                break;
            }
            case FineTuningType.k_leftBack:
            {
                dirValue = -1;
                norValue = -1;
                break;
            }
            case FineTuningType.k_rightFront:
            {
                dirValue = 1;
                norValue = 1;
                break;
            }
            case FineTuningType.k_rightBack:
            {
                dirValue = 1;
                norValue = -1;
                break;
            }
            default:
                break;
        }

        let fixed_pos = figure.rect.unproject({x: figure.rect._w/2 * -dirValue, y: figure.rect._h/2 * -norValue});
        let pos = figure.rect.unproject({x: figure.rect._w/2 * dirValue, y: figure.rect._h/2 * norValue});
        let dir_dv = figure.rect.dv.clone().multiplyScalar(dirValue);
        let nor_dv = figure.rect.nor.clone().multiplyScalar(norValue);
        let ww_to_add = scaleVec.dot(dir_dv);
        let hh_to_add = scaleVec.dot(nor_dv);
  
        if((dirValue * norValue) !== 0)
        {
            // 计算原始的宽高比
            let aspect_ratio = figure.rect._w / figure.rect._h;

            // 根据原始的宽高比来调整新的宽度和高度
            if (aspect_ratio > 1) {
                hh_to_add = ww_to_add / aspect_ratio;
            } else {
                ww_to_add = hh_to_add * aspect_ratio;
            }   
        }


        let ww = figure.rect.w + ww_to_add;
        let hh = figure.rect.h + hh_to_add;

        if(ww < 0) ww = -ww;
        if(hh < 0) hh = -hh;
        ww_to_add = ww - figure.rect.w;
        hh_to_add = hh - figure.rect.h;
        let t_movement = dir_dv.clone().multiplyScalar(ww_to_add).add(nor_dv.multiplyScalar(hh_to_add));
        let t_pos = pos.add(t_movement);
        let t_center = (t_pos.add(fixed_pos)).multiplyScalar(0.5);


        figure.rect._w = ww;
        figure.rect._h = hh;
        figure.rect.rect_center = t_center;

        if(isNeedUpdate)
        {
            figure.updateFigure();
            LayoutAI_App.instance.update();
        }
    }

    // 删除
    public deleteFigure(figure: TFigureElement)
    {
        let figureElements: TFurnitureEntity[] = (LayoutAI_App.instance as TAppManagerBase).layout_container._furniture_entities;
        let entity = figure.furnitureEntity as TFurnitureEntity;
        if(!entity)
        {
            return;
        }
        let index: number = figureElements.indexOf(entity);
        if(index >= 0)
        {
            figureElements.splice(index, 1);
        }
        index = figure._room._furniture_list.indexOf(figure);
        if(index >= 0)
        {
            figure._room._furniture_list.splice(index, 1);
        }
        LayoutAI_App.instance.update();
    }

    public cleanRoomFurniture(room: TRoom)
    {
        (LayoutAI_App.instance as TAppManagerBase)?.layout_container.cleanInRoomFurnitures(room);
        room.resetFurnitureList();
        LayoutAI_App.instance.update();
    }

    public setRoomFurnitures(room: TRoom, figures: TFigureElement[])
    {
        this.cleanRoomFurniture(room);
        room.furnitureList = figures;
        (LayoutAI_App.instance as TAppManagerBase)?.layout_container.addFunitureEnitiesInRoom(room);
        LayoutAI_App.DispatchEvent(LayoutAI_Events.UpdateCandidateRects, true);
        LayoutAI_App.instance.update();
    }
}

export class TLayoutFineTuningBaseTool
{
    private static _instance: TLayoutFineTuningBaseTool;

    private constructor()
    {}

    public static get instance(): TLayoutFineTuningBaseTool
    {
        if(!TLayoutFineTuningBaseTool._instance)
        {
            TLayoutFineTuningBaseTool._instance = new TLayoutFineTuningBaseTool();
        }
        return TLayoutFineTuningBaseTool._instance;
    }

    public copyFigures(figures: TFigureElement[])
    {
        let copyFigures: TFigureElement[] = [];
        figures.forEach(figure => {
            let copyFigure: TFigureElement = figure.clone();
            copyFigures.push(copyFigure);
        });
    
        return copyFigures;
    }

    public getOtherFigures(allFigures: TFigureElement[], targetFigures: TFigureElement[]): TFigureElement[]
    {
        let otherFigures: TFigureElement[] = [];
        allFigures.forEach(figure => {
            if(targetFigures.indexOf(figure) < 0)
            {
                otherFigures.push(figure);
            }
        });
        return otherFigures;
    }

    public isSameForTwoFigures(figures1: TFigureElement[], figures2: TFigureElement[]): boolean
    {
        let figureSet1: TFigureElement[] = Array.from(new Set(figures1));
        let figureSet2: TFigureElement[] = Array.from(new Set(figures2));
        if(figureSet1.length != figureSet2.length)
        {
            return false;
        }
        let foundCount1: number = 0;
        for(let figure1 of figureSet1)
        {
            for(let figure2 of figureSet2)
            {
                if(figure1 == figure2)
                {
                    ++foundCount1;
                    break;
                }
            }
        }
        if(foundCount1 != figureSet1.length)
        {
            return false;
        }
    
        let foundCount2: number = 0;
        for(let figure2 of figureSet2)
        {
            for(let figure1 of figureSet1)
            {
                if(figure2 == figure1)
                {
                    ++foundCount2;
                    break;
                }
            }
        }
        if(foundCount2 != figureSet2.length)
        {
            return false;
        }
        
        return true;
    }
}