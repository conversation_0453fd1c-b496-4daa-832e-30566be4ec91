


import { LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { CadBaseSubHandler } from "./CadBaseSubHandler";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";



export class DimensionFurnitureHandler extends CadBaseSubHandler {
    _previous_rect: ZRect;
    _last_pos: Vector3;
    initialPosition: { x: number, y: number };
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this._cad_mode_handler = cad_mode_handler;
        this.name = LayoutAI_Commands.Transform_Dimension;
        this._previous_rect = null;

        this._last_pos = null;
        this.initialPosition = { x: 0, y: 0 };
    }

    get _existingInput()
    {
        return this._cad_mode_handler._existingInput;
    }
    // 触发编辑框确认逻辑
    updateEditNum(target_rect?: ZRect, event?: Event) {
        if(this.selected_target.selected_transform_element)
        {
            this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_transform_element._target_rect);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(this.selected_target.selected_combination_entitys);
        }
        let input = event?.target as HTMLInputElement || this._existingInput;
        if (!input || !input.value) return;
        let num = parseFloat(input.value);
        if (typeof num === 'number') {
            let _nor_val = JSON.parse(input.getAttribute('_nor_v3'));
            // 编辑前的值
            let origin_num = Number(input.getAttribute('origin_num'));

            // num是编辑的后的值
            // edit_num是差值
            let edit_num: any = origin_num - num;
            // if(edit_num < 0) return;
            let t_center = { x: target_rect.rect_center.x + _nor_val.x * edit_num, y: target_rect.rect_center.y + _nor_val.y * edit_num, z: target_rect.rect_center.z };
            target_rect.rect_center = t_center;

            // 组合内图元移动
            if(this.selected_target.selected_combination_entitys.length > 0)
            {
                for(let t_entity of this.selected_target.selected_combination_entitys)
                {
                    t_entity.rect.rect_center = { x: t_entity.rect.rect_center.x + _nor_val.x * edit_num, y: t_entity.rect.rect_center.y + _nor_val.y * edit_num, z: t_entity.rect.rect_center.z };
                }
            }
            if(this.selected_target.selected_transform_element)
            {
                let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
                if(info)
                {
                    this.manager.appendOperationInfo(info);
                }
            }
            target_rect.updateRect();
            this.update();
            this.leaveMode();
            return;
        }

    }

    // 修改长度
    updateLengthEditNum()
    {
        if (!this.selected_target.selected_transform_element) return;
        if(!this._existingInput) return;
        let num = parseFloat(this._existingInput.value);
        if (typeof num === 'number') {
            this.selected_target.selected_rect.length = num;
            this.selected_target.selected_rect.updateRect();
        }
    }
    leaveMode(): void {

        if(this._existingInput)
        {
            this._existingInput.setAttribute('origin_num',this._existingInput.value);
            this._existingInput.blur();
        }
        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
    }

    onmousedown(ev: I_MouseEvent): void {
    }

    onmousemove(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.buttons == 1)
        {
            this.update();
        }
        if (ev.buttons == 0) {
            this.updateHoverRect(pos);
        }

    }

    onmouseup(ev: I_MouseEvent): void {
        if(!this._existingInput) return;
 
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        let rectIndex = this._existingInput.getAttribute('rectIndex');
        let _figure_rects = this.container.getCandidateRects(["Furniture"]);
        let input_rect = _figure_rects[Number(rectIndex)];
        this.updateSelectedRect(pos);
        setTimeout(() => {
            this.updateEditNum(input_rect);
            this.leaveMode();
        },10)
        this.updateCandidateRects();
    }

    onwheel(ev: WheelEvent): void {

        // 
    }

    enter(state?: number): void {
        super.enter(state);
        
        this.manager.resetPointers();
    }

    onkeydown(ev: KeyboardEvent) {
        if(ev.key == "Enter")
        {
            if(!this.selected_target.selected_transform_element._is_update_length)
            {
                this.updateEditNum(this.selected_target.selected_rect, ev);
            } else 
            {
                this.updateLengthEditNum();
            }
           this.leaveMode();  
        }
        return true
    }
}