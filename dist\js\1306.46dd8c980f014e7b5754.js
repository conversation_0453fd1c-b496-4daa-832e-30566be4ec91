"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[1306],{81306:function(a,e,l){l.r(e),l.d(e,{default:function(){return o}});var i={items_per_page:"/ halaman",jump_to:"Menuju",jump_to_confirm:"konfi<PERSON><PERSON>",page:"Halaman",prev_page:"Halaman Sebelumnya",next_page:"Halaman Berikutnya",prev_5:"5 Halaman Sebelumnya",next_5:"5 Halaman Berikutnya",prev_3:"3 Halaman Sebelumnya",next_3:"3 Halaman Berikutnya",page_size:"ukuran halaman"},n=l(74866),r=l(50799),t=(0,n.A)((0,n.A)({},r.I),{},{locale:"id_ID",today:"Hari ini",now:"Sekarang",backToToday:"Ke<PERSON>li ke hari ini",ok:"Baik",clear:"Bersih",week:"Minggu",month:"Bulan",year:"Tahun",timeSelect:"pilih waktu",dateSelect:"pilih tanggal",weekSelect:"Pilih satu minggu",monthSelect:"Pilih satu bulan",yearSelect:"Pilih satu tahun",decadeSelect:"Pilih satu dekade",dateFormat:"D/M/YYYY",dateTimeFormat:"D/M/YYYY HH:mm:ss",previousMonth:"Bulan sebelumnya (PageUp)",nextMonth:"Bulan selanjutnya (PageDown)",previousYear:"Tahun lalu (Control + kiri)",nextYear:"Tahun selanjutnya (Kontrol + kanan)",previousDecade:"Dekade terakhir",nextDecade:"Dekade berikutnya",previousCentury:"Abad terakhir",nextCentury:"Abad berikutnya"});var u={placeholder:"Pilih waktu",rangePlaceholder:["Waktu awal","Waktu akhir"]};const s={lang:Object.assign({placeholder:"Pilih tanggal",yearPlaceholder:"Pilih tahun",quarterPlaceholder:"Pilih kuartal",monthPlaceholder:"Pilih bulan",weekPlaceholder:"Pilih minggu",rangePlaceholder:["Tanggal awal","Tanggal akhir"],rangeYearPlaceholder:["Tahun awal","Tahun akhir"],rangeQuarterPlaceholder:["Kuartal awal","Kuartal akhir"],rangeMonthPlaceholder:["Bulan awal","Bulan akhir"],rangeWeekPlaceholder:["Minggu awal","Minggu akhir"]},t),timePickerLocale:Object.assign({},u)};const m="${label} tidak valid ${type}";var o={locale:"id",Pagination:i,DatePicker:s,TimePicker:u,Calendar:s,global:{placeholder:"Silahkan pilih"},Table:{filterTitle:"Menu filter",filterConfirm:"OK",filterReset:"Reset",filterEmptyText:"Tidak ada filter",filterCheckAll:"Pilih semua item",filterSearchPlaceholder:"Cari di filter",emptyText:"Tidak ada data",selectAll:"Pilih halaman saat ini",selectInvert:"Balikkan halaman saat ini",selectNone:"Hapus semua data",selectionAll:"Pilih semua data",sortTitle:"Urutkan",expand:"Perluas baris",collapse:"Perkecil baris",triggerDesc:"Klik untuk mengurutkan secara menurun",triggerAsc:"Klik untuk mengurutkan secara menaik",cancelSort:"Klik untuk membatalkan pengurutan"},Tour:{Next:"Selanjutnya",Previous:"Sebelumnya",Finish:"Selesai"},Modal:{okText:"OK",cancelText:"Batal",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Batal"},Transfer:{titles:["",""],searchPlaceholder:"Cari di sini",itemUnit:"data",itemsUnit:"data",remove:"Hapus",selectCurrent:"Pilih halaman saat ini",removeCurrent:"Hapus halaman saat ini",selectAll:"Pilih semua data",deselectAll:"Batal pilih semua data",removeAll:"Hapus semua data",selectInvert:"Balikkan halaman saat ini"},Upload:{uploading:"Mengunggah...",removeFile:"Hapus file",uploadError:"Kesalahan pengunggahan",previewFile:"Pratinjau file",downloadFile:"Unduh file"},Empty:{description:"Tidak ada data"},Icon:{icon:"ikon"},Text:{edit:"Ubah",copy:"Salin",copied:"Disalin",expand:"Perluas",collapse:"Perkecil"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Kesalahan validasi untuk ${label}",required:"Tolong masukkan ${label}",enum:"${label} harus menjadi salah satu dari [${enum}]",whitespace:"${label} tidak boleh berupa karakter kosong",date:{format:"${label} format tanggal tidak valid",parse:"${label} tidak dapat diubah menjadi tanggal",invalid:"${label} adalah tanggal yang tidak valid"},types:{string:m,method:m,array:m,object:m,number:m,date:m,boolean:m,integer:m,float:m,regexp:m,email:m,url:m,hex:m},string:{len:"${label} harus berupa ${len} karakter",min:"${label} harus minimal ${min} karakter",max:"${label} harus maksimal ${max} karakter",range:"${label} harus diantara ${min}-${max} karakter"},number:{len:"${label} harus sama dengan ${len}",min:"${label} harus minimal ${min}",max:"${label} harus maksimal ${max}",range:"${label} harus di antara ${min}-${max}"},array:{len:"Harus ${len} ${label}",min:"Minimal ${min} ${label}",max:"Maksimal ${max} ${label}",range:"Jumlah ${label} harus di antara ${min}-${max}"},pattern:{mismatch:"${label} tidak sesuai dengan pola ${pattern}"}}},Image:{preview:"Pratinjau"},QRCode:{expired:"Kode QR sudah habis masa berlakunya",refresh:"Segarkan",scanned:"Dipindai"},ColorPicker:{presetEmpty:"Kosong",transparent:"Transparan",singleColor:"Warna tunggal",gradientColor:"Warna gradien"}}}}]);