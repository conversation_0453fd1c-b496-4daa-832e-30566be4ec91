import { getPrefix } from "@/utils/common";
import { compareNames } from "@layoutai/z_polygon";
import { AI_FurnitureType } from "./AI_CadData";
import { I_SwjCabinetData } from "./SwjLayoutData";

export class ModelLocPublicCategoryMap {
    static instance: ModelLocPublicCategoryMap = null;
    modelloc_public_category_map: { [key: string]: any };
    public_category_modelloc_map: { [key: string]: { [key: string]: Set<string> } };

    constructor() {
        ModelLocPublicCategoryMap.instance = this;
        this.init();
    }

    init() {
        this.modelloc_public_category_map = {
            "沙发组合": {
                "categoryNamesHuman": [
                    "沙发组合"
                ],
                "categoryItems": {
                    "7685156": "休闲椅组合",
                    "103445926": "沙发组合",
                    "154583": "沙发组合"
                },
                "categoryIds": [
                    7685156,
                    103445926,
                    154583
                ]
            },
            "沙发": {
                "categoryNamesHuman": [
                    "双人沙发",
                    "多人沙发",
                    "转角沙发"
                ],
                "categoryItems": {
                    "145465": "双人沙发",
                    "145466": "多人沙发",
                    "145468": "转角沙发"
                },
                "categoryIds": [
                    145465,
                    145466,
                    145468
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "休闲椅": {
                "categoryNamesHuman": [
                    "单人沙发",
                    "休闲沙发",
                    "懒人沙发",
                    "贵妃椅",
                    "休闲椅",
                    "按摩椅",
                    "圈椅/官帽椅",
                    "蛋壳椅/高背椅",
                    "躺椅",
                    "写字椅"
                ],
                "categoryItems": {
                    "14754": "休闲椅",
                    "14777": "写字椅",
                    "145464": "单人沙发",
                    "145470": "贵妃椅",
                    "145471": "休闲沙发",
                    "832066": "躺椅",
                    "3190898": "懒人沙发",
                    "3275228": "圈椅/官帽椅",
                    "3275230": "按摩椅",
                    "3275232": "蛋壳椅/高背椅"
                },
                "categoryIds": [
                    14754,
                    14777,
                    145464,
                    145470,
                    145471,
                    832066,
                    3190898,
                    3275228,
                    3275230,
                    3275232
                ],
                "spaces": [
                    "客餐厅", "阳台"
                ]
            },
            "茶几": {
                "categoryNamesHuman": [
                    "茶几"
                ],
                "categoryItems": {
                    "16139": "茶几"
                },
                "categoryIds": [
                    16139
                ],
                "spaces": [
                    "客餐厅", "阳台"
                ]
            },
            "脚踏": {
                "categoryNamesHuman": [
                    "脚踏",
                    "休闲凳"
                ],
                "categoryItems": {
                    "145469": "脚踏",
                    "3275480": "休闲凳"
                },
                "categoryIds": [
                    145469,
                    3275480
                ],
                "spaces": [
                    "客餐厅", "阳台"
                ]
            },
            "边几": {
                "categoryNamesHuman": [
                    "边几"
                ],
                "categoryItems": {
                    "14742": "边几"
                },
                "categoryIds": [
                    14742
                ],
                "spaces": [
                    "客餐厅", "阳台"
                ]
            },
            "电视柜": {
                "categoryNamesHuman": [
                    "电视柜"
                ],
                "categoryItems": {
                    "14729": "电视柜"
                },
                "categoryIds": [
                    14729,
                    14778,
                    14814,
                    19518
                ],
                "spaces": [
                    "客餐厅", "卧室"
                ]
            },
            "电视柜组合": {
                "categoryNamesHuman": [
                    "电视柜组合"
                ],
                "categoryItems": {
                    "103447318": "电视柜组合"
                },
                "categoryIds": [
                    103447318
                ]
            },
            "卧室电视柜组合": {
                "categoryNamesHuman": [
                    "电视柜组合"
                ],
                "categoryItems": {
                    "103447318": "电视柜组合"
                },
                "categoryIds": [
                    103447318
                ]
            },
            "餐边柜": {
                "categoryNamesHuman": [
                    "餐边柜",
                    "酒柜"
                ],
                "categoryItems": {
                    "14733": "餐边柜",
                    "14745": "边柜",
                    "14746": "餐车",
                    "14747": "酒柜"
                },
                "categoryIds": [
                    14733,
                    14745,
                    14746,
                    14747
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "玄关柜": {
                "categoryNamesHuman": [
                    "玄关柜",
                    "鞋柜"
                ],
                "categoryItems": {
                    "14750": "玄关柜",
                    "3277568": "鞋柜"
                },
                "categoryIds": [
                    14750,
                    3277568
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "餐桌": {
                "categoryNamesHuman": [
                    "餐桌"
                ],
                "categoryItems": {
                    "7075243": "方桌",
                    "7075245": "圆桌",
                    "14743": "餐桌"
                },
                "categoryIds": [
                    7075243,
                    7075245,
                    14743
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "餐椅": {
                "categoryNamesHuman": [
                    "餐椅",
                    "休闲椅",
                    "圈椅/官帽椅",
                    "蛋壳椅/高背椅"
                ],
                "categoryItems": {
                    "14744": "餐椅",
                    "14754": "休闲椅",
                    "3275228": "圈椅/官帽椅",
                    "3275232": "蛋壳椅/高背椅"
                },
                "categoryIds": [
                    14744,
                    14754,
                    3275228,
                    3275232
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "餐桌椅组合": {
                "categoryNamesHuman": [
                    "餐桌椅组合"
                ],
                "categoryItems": {
                    "154584": "餐桌椅组合"
                },
                "categoryIds": [
                    154584
                ]
            },
            "背景墙": {
                "categoryNamesHuman": [
                    "背景墙"
                ],
                "categoryItems": {
                    "16266": "背景墙",
                    "18686": "护墙板",
                    "138603": "软包类",
                    "138619": "墙纸类",
                    "138620": "木质类",
                    "138621": "石材类",
                    "138622": "玻璃类",
                    "138623": "装饰线条"
                },
                "categoryIds": [
                    16266,
                    18686,
                    138603,
                    138619,
                    138620,
                    138621,
                    138622,
                    138623
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "沙发背景墙": {
                "categoryNamesHuman": [
                    "背景墙"
                ],
                "categoryItems": {
                    "16266": "背景墙",
                    "18686": "护墙板",
                    "138603": "软包类",
                    "138619": "墙纸类",
                    "138620": "木质类",
                    "138621": "石材类",
                    "138622": "玻璃类",
                    "138623": "装饰线条"
                },
                "categoryIds": [
                    16266,
                    18686,
                    138603,
                    138619,
                    138620,
                    138621,
                    138622,
                    138623
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "电视背景墙": {
                "categoryNamesHuman": [
                    "背景墙"
                ],
                "categoryItems": {
                    "16266": "背景墙",
                    "18686": "护墙板",
                    "138603": "软包类",
                    "138619": "墙纸类",
                    "138620": "木质类",
                    "138621": "石材类",
                    "138622": "玻璃类",
                    "138623": "装饰线条"
                },
                "categoryIds": [
                    16266,
                    18686,
                    138603,
                    138619,
                    138620,
                    138621,
                    138622,
                    138623
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "主灯": {
                "categoryNamesHuman": [
                    "吊灯",
                    "吸顶灯"
                ],
                "categoryItems": {
                    "6882137": "多头吊灯",
                    "6882139": "鱼线吊灯",
                    "6882140": "水晶吊灯",
                    "6882373": "枝型吊灯",
                    "6883998": "其它吊灯",
                    "104474055": "吊灯",
                    "16253": "吊灯",
                    "16254": "吸顶灯",
                    "19370": "吊灯",
                    "3290924": "装饰吊灯"
                },
                "categoryIds": [
                    6882137,
                    6882139,
                    6882140,
                    6882373,
                    6883998,
                    104474055,
                    16253,
                    16254,
                    19370,
                    3290924
                ],
                "spaces": [
                    "卧室", "书房", "卫生间"
                ]
            },
            "餐厅主灯": {
                "categoryNamesHuman": [
                    "吊灯",
                    "吸顶灯"
                ],
                "categoryItems": {
                    "6882137": "多头吊灯",
                    "6882139": "鱼线吊灯",
                    "6882140": "水晶吊灯",
                    "6882373": "枝型吊灯",
                    "6883998": "其它吊灯",
                    "104474055": "吊灯",
                    "16253": "吊灯",
                    "16254": "吸顶灯",
                    "19370": "吊灯",
                    "3290924": "装饰吊灯"
                },
                "categoryIds": [
                    6882137,
                    6882139,
                    6882140,
                    6882373,
                    6883998,
                    104474055,
                    16253,
                    16254,
                    19370,
                    3290924
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "客厅主灯": {
                "categoryNamesHuman": [
                    "吊灯",
                    "吸顶灯"
                ],
                "categoryItems": {
                    "6882137": "多头吊灯",
                    "6882139": "鱼线吊灯",
                    "6882140": "水晶吊灯",
                    "6882373": "枝型吊灯",
                    "6883998": "其它吊灯",
                    "104474055": "吊灯",
                    "16253": "吊灯",
                    "16254": "吸顶灯",
                    "19370": "吊灯",
                    "3290924": "装饰吊灯"
                },
                "categoryIds": [
                    6882137,
                    6882139,
                    6882140,
                    6882373,
                    6883998,
                    104474055,
                    16253,
                    16254,
                    19370,
                    3290924
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "落地灯": {
                "categoryNamesHuman": [
                    "落地灯"
                ],
                "categoryItems": {
                    "16252": "落地灯"
                },
                "categoryIds": [
                    16252
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "地毯": {
                "categoryNamesHuman": [
                    "地毯"
                ],
                "categoryItems": {
                    "16289": "地毯",
                    "16290": "圆形",
                    "125953": "方形",
                    "570191": "异形"
                },
                "categoryIds": [
                    16289,
                    16290,
                    125953,
                    570191
                ],
                "spaces": [
                    "客餐厅", "卧室"
                ]
            },
            "墙饰": {
                "categoryNamesHuman": [
                    "挂画",
                    "墙饰"
                ],
                "categoryItems": {
                    "6665488": "壁龛",
                    "7320060": "油画",
                    "7320080": "抽象",
                    "7320105": "风景",
                    "7320108": "人像",
                    "7320109": "黑白",
                    "7320110": "植物",
                    "7320112": "动物",
                    "7320115": "卡通",
                    "7320116": "字母",
                    "7320117": "国画书法",
                    "7320122": "复古",
                    "7320125": "立体装置画",
                    "7320128": "手绘速写",
                    "7320132": "几何图案",
                    "7320791": "其它",
                    "7078299": "挂画",
                    "16282": "组合画",
                    "16283": "装饰画",
                    "16284": "挂饰",
                    "633443": "搁板",
                    "782591": "挂钩",
                    "518942": "墙饰"
                },
                "categoryIds": [
                    6665488,
                    7320060,
                    7320080,
                    7320105,
                    7320108,
                    7320109,
                    7320110,
                    7320112,
                    7320115,
                    7320116,
                    7320117,
                    7320122,
                    7320125,
                    7320128,
                    7320132,
                    7320791,
                    7078299,
                    16282,
                    16283,
                    16284,
                    633443,
                    782591,
                    518942
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "餐厅墙饰": {
                "categoryNamesHuman": [
                    "挂画",
                    "墙饰"
                ],
                "categoryItems": {
                    "6665488": "壁龛",
                    "7320060": "油画",
                    "7320080": "抽象",
                    "7320105": "风景",
                    "7320108": "人像",
                    "7320109": "黑白",
                    "7320110": "植物",
                    "7320112": "动物",
                    "7320115": "卡通",
                    "7320116": "字母",
                    "7320117": "国画书法",
                    "7320122": "复古",
                    "7320125": "立体装置画",
                    "7320128": "手绘速写",
                    "7320132": "几何图案",
                    "7320791": "其它",
                    "7078299": "挂画",
                    "16282": "组合画",
                    "16283": "装饰画",
                    "16284": "挂饰",
                    "633443": "搁板",
                    "782591": "挂钩",
                    "518942": "墙饰"
                },
                "categoryIds": [
                    6665488,
                    7320060,
                    7320080,
                    7320105,
                    7320108,
                    7320109,
                    7320110,
                    7320112,
                    7320115,
                    7320116,
                    7320117,
                    7320122,
                    7320125,
                    7320128,
                    7320132,
                    7320791,
                    7078299,
                    16282,
                    16283,
                    16284,
                    633443,
                    782591,
                    518942
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "客厅墙饰": {
                "categoryNamesHuman": [
                    "挂画",
                    "墙饰"
                ],
                "categoryItems": {
                    "6665488": "壁龛",
                    "7320060": "油画",
                    "7320080": "抽象",
                    "7320105": "风景",
                    "7320108": "人像",
                    "7320109": "黑白",
                    "7320110": "植物",
                    "7320112": "动物",
                    "7320115": "卡通",
                    "7320116": "字母",
                    "7320117": "国画书法",
                    "7320122": "复古",
                    "7320125": "立体装置画",
                    "7320128": "手绘速写",
                    "7320132": "几何图案",
                    "7320791": "其它",
                    "7078299": "挂画",
                    "16282": "组合画",
                    "16283": "装饰画",
                    "16284": "挂饰",
                    "633443": "搁板",
                    "782591": "挂钩",
                    "518942": "墙饰"
                },
                "categoryIds": [
                    6665488,
                    7320060,
                    7320080,
                    7320105,
                    7320108,
                    7320109,
                    7320110,
                    7320112,
                    7320115,
                    7320116,
                    7320117,
                    7320122,
                    7320125,
                    7320128,
                    7320132,
                    7320791,
                    7078299,
                    16282,
                    16283,
                    16284,
                    633443,
                    782591,
                    518942
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "茶几饰品": {
                "categoryNamesHuman": [
                    "台灯",
                    "摆件",
                    "工艺品",
                    "书籍杂志",
                    "陶瓷器皿",
                    "儿童玩具",
                    "办公文具",
                    "花艺",
                    "绿植",
                    "干枝",
                    "挂画组合",
                    "灯饰组合",
                    "绿植组合",
                    "其它摆件组合"
                ],
                "categoryItems": {
                    "7078284": "摆件",
                    "103445919": "挂画组合",
                    "103446898": "绿植组合",
                    "103446899": "其它摆件组合",
                    "103447301": "灯饰组合",
                    "104472974": "摆件",
                    "16251": "台灯",
                    "16286": "儿童玩具",
                    "16292": "花艺",
                    "108194": "陶瓷器皿",
                    "108197": "工艺品",
                    "108215": "书籍杂志",
                    "108220": "相框",
                    "108375": "绿植",
                    "124565": "干枝",
                    "125834": "烛台",
                    "132510": "办公文具",
                    "132801": "装饰托盘",
                    "157243": "钟表",
                    "172726": "装饰摆件",
                    "695518": "字母",
                    "695813": "佛像",
                    "695814": "棋盘",
                    "256675": "鱼缸",
                    "3213783": "茶具",
                    "3219196": "玩具车",
                    "3219257": "公仔",
                    "3219259": "其它",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树",
                    "3394711": "桌面花艺",
                    "3394712": "落地花艺",
                    "3394713": "壁挂花艺"
                },
                "categoryIds": [
                    7078284,
                    103445919,
                    103446898,
                    103446899,
                    103447301,
                    104472974,
                    16251,
                    16286,
                    16292,
                    108194,
                    108197,
                    108215,
                    108220,
                    108375,
                    124565,
                    125834,
                    132510,
                    132801,
                    157243,
                    172726,
                    695518,
                    695813,
                    695814,
                    256675,
                    3213783,
                    3219196,
                    3219257,
                    3219259,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726,
                    3394711,
                    3394712,
                    3394713
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "边几饰品": {
                "categoryNamesHuman": [
                    "台灯",
                    "摆件",
                    "工艺品",
                    "书籍杂志",
                    "陶瓷器皿",
                    "儿童玩具",
                    "办公文具",
                    "花艺",
                    "绿植",
                    "干枝",
                    "挂画组合",
                    "灯饰组合",
                    "绿植组合",
                    "其它摆件组合"
                ],
                "categoryItems": {
                    "7078284": "摆件",
                    "103445919": "挂画组合",
                    "103446898": "绿植组合",
                    "103446899": "其它摆件组合",
                    "103447301": "灯饰组合",
                    "104472974": "摆件",
                    "16251": "台灯",
                    "16286": "儿童玩具",
                    "16292": "花艺",
                    "108194": "陶瓷器皿",
                    "108197": "工艺品",
                    "108215": "书籍杂志",
                    "108220": "相框",
                    "108375": "绿植",
                    "124565": "干枝",
                    "125834": "烛台",
                    "132510": "办公文具",
                    "132801": "装饰托盘",
                    "157243": "钟表",
                    "172726": "装饰摆件",
                    "695518": "字母",
                    "695813": "佛像",
                    "695814": "棋盘",
                    "256675": "鱼缸",
                    "3213783": "茶具",
                    "3219196": "玩具车",
                    "3219257": "公仔",
                    "3219259": "其它",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树",
                    "3394711": "桌面花艺",
                    "3394712": "落地花艺",
                    "3394713": "壁挂花艺"
                },
                "categoryIds": [
                    7078284,
                    103445919,
                    103446898,
                    103446899,
                    103447301,
                    104472974,
                    16251,
                    16286,
                    16292,
                    108194,
                    108197,
                    108215,
                    108220,
                    108375,
                    124565,
                    125834,
                    132510,
                    132801,
                    157243,
                    172726,
                    695518,
                    695813,
                    695814,
                    256675,
                    3213783,
                    3219196,
                    3219257,
                    3219259,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726,
                    3394711,
                    3394712,
                    3394713
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "餐边柜饰品": {
                "categoryNamesHuman": [
                    "台灯",
                    "摆件",
                    "工艺品",
                    "书籍杂志",
                    "陶瓷器皿",
                    "儿童玩具",
                    "办公文具",
                    "花艺",
                    "绿植",
                    "干枝",
                    "挂画组合",
                    "灯饰组合",
                    "绿植组合",
                    "其它摆件组合"
                ],
                "categoryItems": {
                    "7078284": "摆件",
                    "103445919": "挂画组合",
                    "103446898": "绿植组合",
                    "103446899": "其它摆件组合",
                    "103447301": "灯饰组合",
                    "104472974": "摆件",
                    "16251": "台灯",
                    "16286": "儿童玩具",
                    "16292": "花艺",
                    "108194": "陶瓷器皿",
                    "108197": "工艺品",
                    "108215": "书籍杂志",
                    "108220": "相框",
                    "108375": "绿植",
                    "124565": "干枝",
                    "125834": "烛台",
                    "132510": "办公文具",
                    "132801": "装饰托盘",
                    "157243": "钟表",
                    "172726": "装饰摆件",
                    "695518": "字母",
                    "695813": "佛像",
                    "695814": "棋盘",
                    "256675": "鱼缸",
                    "3213783": "茶具",
                    "3219196": "玩具车",
                    "3219257": "公仔",
                    "3219259": "其它",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树",
                    "3394711": "桌面花艺",
                    "3394712": "落地花艺",
                    "3394713": "壁挂花艺"
                },
                "categoryIds": [
                    7078284,
                    103445919,
                    103446898,
                    103446899,
                    103447301,
                    104472974,
                    16251,
                    16286,
                    16292,
                    108194,
                    108197,
                    108215,
                    108220,
                    108375,
                    124565,
                    125834,
                    132510,
                    132801,
                    157243,
                    172726,
                    695518,
                    695813,
                    695814,
                    256675,
                    3213783,
                    3219196,
                    3219257,
                    3219259,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726,
                    3394711,
                    3394712,
                    3394713
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "电视柜饰品": {
                "categoryNamesHuman": [
                    "台灯",
                    "摆件",
                    "工艺品",
                    "书籍杂志",
                    "陶瓷器皿",
                    "儿童玩具",
                    "办公文具",
                    "花艺",
                    "绿植",
                    "干枝",
                    "挂画组合",
                    "灯饰组合",
                    "绿植组合",
                    "其它摆件组合"
                ],
                "categoryItems": {
                    "7078284": "摆件",
                    "103445919": "挂画组合",
                    "103446898": "绿植组合",
                    "103446899": "其它摆件组合",
                    "103447301": "灯饰组合",
                    "104472974": "摆件",
                    "16251": "台灯",
                    "16286": "儿童玩具",
                    "16292": "花艺",
                    "108194": "陶瓷器皿",
                    "108197": "工艺品",
                    "108215": "书籍杂志",
                    "108220": "相框",
                    "108375": "绿植",
                    "124565": "干枝",
                    "125834": "烛台",
                    "132510": "办公文具",
                    "132801": "装饰托盘",
                    "157243": "钟表",
                    "172726": "装饰摆件",
                    "695518": "字母",
                    "695813": "佛像",
                    "695814": "棋盘",
                    "256675": "鱼缸",
                    "3213783": "茶具",
                    "3219196": "玩具车",
                    "3219257": "公仔",
                    "3219259": "其它",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树",
                    "3394711": "桌面花艺",
                    "3394712": "落地花艺",
                    "3394713": "壁挂花艺"
                },
                "categoryIds": [
                    7078284,
                    103445919,
                    103446898,
                    103446899,
                    103447301,
                    104472974,
                    16251,
                    16286,
                    16292,
                    108194,
                    108197,
                    108215,
                    108220,
                    108375,
                    124565,
                    125834,
                    132510,
                    132801,
                    157243,
                    172726,
                    695518,
                    695813,
                    695814,
                    256675,
                    3213783,
                    3219196,
                    3219257,
                    3219259,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726,
                    3394711,
                    3394712,
                    3394713
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "床头柜饰品": {
                "categoryNamesHuman": [
                    "台灯",
                    "摆件",
                    "工艺品",
                    "书籍杂志",
                    "陶瓷器皿",
                    "儿童玩具",
                    "办公文具",
                    "花艺",
                    "绿植",
                    "干枝",
                    "挂画组合",
                    "灯饰组合",
                    "绿植组合",
                    "其它摆件组合"
                ],
                "categoryItems": {
                    "7078284": "摆件",
                    "103445919": "挂画组合",
                    "103446898": "绿植组合",
                    "103446899": "其它摆件组合",
                    "103447301": "灯饰组合",
                    "104472974": "摆件",
                    "16251": "台灯",
                    "16286": "儿童玩具",
                    "16292": "花艺",
                    "108194": "陶瓷器皿",
                    "108197": "工艺品",
                    "108215": "书籍杂志",
                    "108220": "相框",
                    "108375": "绿植",
                    "124565": "干枝",
                    "125834": "烛台",
                    "132510": "办公文具",
                    "132801": "装饰托盘",
                    "157243": "钟表",
                    "172726": "装饰摆件",
                    "695518": "字母",
                    "695813": "佛像",
                    "695814": "棋盘",
                    "256675": "鱼缸",
                    "3213783": "茶具",
                    "3219196": "玩具车",
                    "3219257": "公仔",
                    "3219259": "其它",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树",
                    "3394711": "桌面花艺",
                    "3394712": "落地花艺",
                    "3394713": "壁挂花艺"
                },
                "categoryIds": [
                    7078284,
                    103445919,
                    103446898,
                    103446899,
                    103447301,
                    104472974,
                    16251,
                    16286,
                    16292,
                    108194,
                    108197,
                    108215,
                    108220,
                    108375,
                    124565,
                    125834,
                    132510,
                    132801,
                    157243,
                    172726,
                    695518,
                    695813,
                    695814,
                    256675,
                    3213783,
                    3219196,
                    3219257,
                    3219259,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726,
                    3394711,
                    3394712,
                    3394713
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "餐桌饰品": {
                "categoryNamesHuman": [
                    "台灯",
                    "摆件",
                    "工艺品",
                    "书籍杂志",
                    "陶瓷器皿",
                    "儿童玩具",
                    "办公文具",
                    "花艺",
                    "绿植",
                    "干枝",
                    "挂画组合",
                    "灯饰组合",
                    "绿植组合",
                    "其它摆件组合"
                ],
                "categoryItems": {
                    "7078284": "摆件",
                    "103445919": "挂画组合",
                    "103446898": "绿植组合",
                    "103446899": "其它摆件组合",
                    "103447301": "灯饰组合",
                    "104472974": "摆件",
                    "16251": "台灯",
                    "16286": "儿童玩具",
                    "16292": "花艺",
                    "108194": "陶瓷器皿",
                    "108197": "工艺品",
                    "108215": "书籍杂志",
                    "108220": "相框",
                    "108375": "绿植",
                    "124565": "干枝",
                    "125834": "烛台",
                    "132510": "办公文具",
                    "132801": "装饰托盘",
                    "157243": "钟表",
                    "172726": "装饰摆件",
                    "695518": "字母",
                    "695813": "佛像",
                    "695814": "棋盘",
                    "256675": "鱼缸",
                    "3213783": "茶具",
                    "3219196": "玩具车",
                    "3219257": "公仔",
                    "3219259": "其它",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树",
                    "3394711": "桌面花艺",
                    "3394712": "落地花艺",
                    "3394713": "壁挂花艺"
                },
                "categoryIds": [
                    7078284,
                    103445919,
                    103446898,
                    103446899,
                    103447301,
                    104472974,
                    16251,
                    16286,
                    16292,
                    108194,
                    108197,
                    108215,
                    108220,
                    108375,
                    124565,
                    125834,
                    132510,
                    132801,
                    157243,
                    172726,
                    695518,
                    695813,
                    695814,
                    256675,
                    3213783,
                    3219196,
                    3219257,
                    3219259,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726,
                    3394711,
                    3394712,
                    3394713
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "餐具饰品": {
                "categoryNamesHuman": [
                    "餐具"
                ],
                "categoryItems": {
                    "132511": "餐具"
                },
                "categoryIds": [
                    132511
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "书桌饰品": {
                "categoryNamesHuman": [
                    "台灯",
                    "摆件",
                    "工艺品",
                    "书籍杂志",
                    "陶瓷器皿",
                    "儿童玩具",
                    "办公文具",
                    "花艺",
                    "绿植",
                    "干枝",
                    "挂画组合",
                    "灯饰组合",
                    "绿植组合",
                    "其它摆件组合"
                ],
                "categoryItems": {
                    "7078284": "摆件",
                    "103445919": "挂画组合",
                    "103446898": "绿植组合",
                    "103446899": "其它摆件组合",
                    "103447301": "灯饰组合",
                    "104472974": "摆件",
                    "16251": "台灯",
                    "16286": "儿童玩具",
                    "16292": "花艺",
                    "108194": "陶瓷器皿",
                    "108197": "工艺品",
                    "108215": "书籍杂志",
                    "108220": "相框",
                    "108375": "绿植",
                    "124565": "干枝",
                    "125834": "烛台",
                    "132510": "办公文具",
                    "132801": "装饰托盘",
                    "157243": "钟表",
                    "172726": "装饰摆件",
                    "695518": "字母",
                    "695813": "佛像",
                    "695814": "棋盘",
                    "256675": "鱼缸",
                    "3213783": "茶具",
                    "3219196": "玩具车",
                    "3219257": "公仔",
                    "3219259": "其它",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树",
                    "3394711": "桌面花艺",
                    "3394712": "落地花艺",
                    "3394713": "壁挂花艺"
                },
                "categoryIds": [
                    7078284,
                    103445919,
                    103446898,
                    103446899,
                    103447301,
                    104472974,
                    16251,
                    16286,
                    16292,
                    108194,
                    108197,
                    108215,
                    108220,
                    108375,
                    124565,
                    125834,
                    132510,
                    132801,
                    157243,
                    172726,
                    695518,
                    695813,
                    695814,
                    256675,
                    3213783,
                    3219196,
                    3219257,
                    3219259,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726,
                    3394711,
                    3394712,
                    3394713
                ],
                "spaces": [
                    "书房", "卧室"
                ]
            },
            "电视": {
                "categoryNamesHuman": [
                    "电视"
                ],
                "categoryItems": {
                    "16265": "电视"
                },
                "categoryIds": [
                    16265
                ],
                "spaces": [
                    "客餐厅", "卧室"
                ]
            },
            "绿植": {
                "categoryNamesHuman": [
                    "空调",
                    "绿植"
                ],
                "categoryItems": {
                    "16264": "空调",
                    "108375": "绿植",
                    "3241669": "落地盆栽",
                    "3241670": "藤蔓",
                    "3241671": "竹",
                    "3241672": "装饰绿植",
                    "3241675": "仙人掌",
                    "3242058": "多肉",
                    "3244225": "桌面绿植",
                    "3244230": "其它",
                    "3245726": "树"
                },
                "categoryIds": [
                    16264,
                    108375,
                    3241669,
                    3241670,
                    3241671,
                    3241672,
                    3241675,
                    3242058,
                    3244225,
                    3244230,
                    3245726
                ],
                "spaces": [
                    "客餐厅", "阳台"
                ]
            },
            "雕塑": {
                "categoryNamesHuman": [
                    "雕塑"
                ],
                "categoryItems": {
                    "125847": "雕塑"
                },
                "categoryIds": [
                    125847
                ],
                "spaces": [
                    "客餐厅"
                ]
            },
            "窗帘": {
                "categoryNamesHuman": [
                    "窗帘"
                ],
                "categoryItems": {
                    "9639039": "转角窗帘",
                    "16288": "窗帘",
                    "125700": "百叶窗帘",
                    "151404": "窗帘部件",
                    "155954": "双开帘",
                    "809654": "其它",
                    "308001": "罗马帘",
                    "3222148": "单开帘",
                    "3222150": "卷帘"
                },
                "categoryIds": [
                    9639039,
                    16288,
                    125700,
                    151404,
                    155954,
                    809654,
                    308001,
                    3222148,
                    3222150
                ],
                "spaces": [
                    "客餐厅", "卧室", "书房", "阳台"
                ]
            },
            "淋浴房": {
                "categoryNamesHuman": [
                    "淋浴房"
                ],
                "categoryItems": {
                    "14785": "淋浴房",
                    "14794": "矩形",
                    "14795": "钻石形",
                    "16000": "弧扇形"
                },
                "categoryIds": [
                    14785,
                    14794,
                    14795,
                    16000
                ],
                "spaces": [
                    "卫生间"
                ]
            },
            "洗衣机柜": {
                "categoryNamesHuman": [
                    "洗衣机柜"
                ],
                "categoryItems": {
                    "4115020": "洗衣机柜"
                },
                "categoryIds": [
                    4115020
                ],
                "spaces": [
                    "阳台"
                ]
            },
            "洗衣机": {
                "categoryNamesHuman": [
                    "洗衣机"
                ],
                "spaces": [
                    "阳台"
                ]
            },
            "花洒": {
                "categoryNamesHuman": [
                    "花洒"
                ],
                "categoryItems": {
                    "14786": "花洒",
                    "14796": "挂墙式",
                    "14797": "入墙式"
                },
                "categoryIds": [
                    14786,
                    14796,
                    14797
                ],
                "spaces": [
                    "卫生间"
                ]
            },
            "马桶": {
                "categoryNamesHuman": [
                    "马桶"
                ],
                "categoryItems": {
                    "14784": "马桶",
                    "14793": "蹲便器"
                },
                "categoryIds": [
                    14784,
                    14793
                ],
                "spaces": [
                    "卫生间"
                ]
            },
            "浴室柜": {
                "categoryNamesHuman": [
                    "浴室柜"
                ],
                "categoryItems": {
                    "14783": "浴室柜",
                    "14789": "落地式",
                    "14790": "挂墙式"
                },
                "categoryIds": [
                    14783,
                    14789,
                    14790
                ],
                "spaces": [
                    "卫生间"
                ]
            },
            "毛巾架": {
                "categoryNamesHuman": [
                    "毛巾架"
                ],
                "categoryItems": {
                    "145335": "毛巾架"
                },
                "categoryIds": [
                    145335
                ],
                "spaces": [
                    "卫生间"
                ]
            },
            "书桌组合-带椅子": {
                "categoryNamesHuman": [
                    "书桌组合",

                ],
                "categoryItems": {
                    "697365": "书桌组合"
                },
                "categoryIds": [
                    697365
                ],
                "spaces": [
                    "书房", "阳台"
                ]
            },
            "书桌": {
                "categoryNamesHuman": [
                    "书桌"
                ],
                "categoryItems": {
                    "14775": "书桌",
                    "14781": "书桌柜",
                    "614603": "直边书桌",
                    "614604": "转角书桌"
                },
                "categoryIds": [
                    14775,
                    14781,
                    614603,
                    614604
                ],
                "spaces": [
                    "书房", "卧室"
                ]
            },
            "书椅": {
                "categoryNamesHuman": [
                    "写字椅",
                    "休闲椅",
                    "蛋壳椅/高背椅"
                ],
                "categoryItems": {
                    "14754": "休闲椅",
                    "14777": "写字椅",
                    "3275232": "蛋壳椅/高背椅"
                },
                "categoryIds": [
                    14754,
                    14777,
                    3275232
                ],
                "spaces": [
                    "书房", "卧室"
                ]
            },
            "书柜": {
                "categoryNamesHuman": [
                    "书柜"
                ],
                "categoryItems": {
                    "14778": "书柜",
                    "14814": "书柜",
                    "19518": "书柜"
                },
                "categoryIds": [
                    14778,
                    14814,
                    19518
                ],
                "spaces": [
                    "书房"
                ]
            },
            "床头柜": {
                "categoryNamesHuman": [
                    "床头柜",
                ],
                "categoryItems": {
                    "14758": "床头柜",
                    "14802": "儿童床头柜"
                },
                "categoryIds": [
                    14742,
                    14758,
                    14802
                ],
                "spaces": [
                    "客餐厅", "卧室"
                ]
            },
            "梳妆台组合-带椅子": {
                "categoryNamesHuman": [
                    "梳妆台组合"
                ],
                "categoryItems": {
                    "103444969": "梳妆台组合"
                },
                "categoryIds": [
                    103444969
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "梳妆台": {
                "categoryNamesHuman": [
                    "梳妆台"
                ],
                "categoryItems": {
                    "14772": "梳妆台",
                    "14811": "儿童妆台"
                },
                "categoryIds": [
                    14772,
                    14811
                ],
                "spaces": [
                    "卧室", "阳台"
                ]
            },
            "梳妆凳": {
                "categoryNamesHuman": [
                    "梳妆凳",
                    "休闲凳"
                ],
                "categoryItems": {
                    "14773": "梳妆凳",
                    "14812": "儿童妆凳",
                    "3275480": "休闲凳"
                },
                "categoryIds": [
                    14773,
                    14812,
                    3275480
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "床": {
                "categoryNamesHuman": [
                    "床",
                    "单人床",
                    "双人床",
                    "儿童床",
                    "榻榻米"
                ],
                "categoryItems": {
                    "14764": "单人床",
                    "14765": "双人床",
                    "14800": "儿童床",
                    "14805": "单层床",
                    "14806": "双层床",
                    "15927": "婴儿床",
                    "202800": "榻榻米"
                },
                "categoryIds": [
                    14764,
                    14765,
                    14800,
                    14805,
                    14806,
                    15927,
                    202800
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "衣柜": {
                "categoryNamesHuman": [
                    "衣柜"
                ],
                "categoryItems": {
                    "14759": "衣柜",
                    "14801": "儿童衣柜"
                },
                "categoryIds": [
                    14759,
                    14801
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "床头吊灯": {
                "categoryNamesHuman": [
                    "吊灯"
                ],
                "categoryItems": {
                    "6882137": "多头吊灯",
                    "6882139": "鱼线吊灯",
                    "6882140": "水晶吊灯",
                    "6882373": "枝型吊灯",
                    "6883998": "其它吊灯",
                    "104474055": "吊灯",
                    "16253": "吊灯",
                    "19370": "吊灯",
                    "3290924": "装饰吊灯"
                },
                "categoryIds": [
                    6882137,
                    6882139,
                    6882140,
                    6882373,
                    6883998,
                    104474055,
                    16253,
                    19370,
                    3290924
                ],
                "spaces": [
                    "卧室"
                ]
            },
            "地面": {
                "categoryNamesHuman": [
                    "瓷砖",
                    "木地板",
                    "石材",
                    "装饰贴图"
                ],
                "categoryItems": {
                    "6283304": "内景图",
                    "6818934": "节日主题",
                    "8804042": "900*900",
                    "8804043": "300*600",
                    "8804044": "现代仿古砖",
                    "8804047": "抛釉砖",
                    "8804048": "抛晶砖",
                    "8804049": "通体砖",
                    "8804050": "大板砖",
                    "8810635": "釉面砖",
                    "100043396": "横幅挂画",
                    "100639923": "其他",
                    "103480272": "岩板",
                    "103552876": "水磨石",
                    "15082": "地砖",
                    "15548": "600*600",
                    "16083": "木地板",
                    "16085": "天花角线",
                    "16086": "踢脚线",
                    "16088": "错位铺",
                    "16102": "450*450",
                    "16103": "300*300",
                    "16105": "300*450",
                    "16108": "实木踢脚线",
                    "16109": "大理石踢脚线",
                    "16110": "石膏线",
                    "16179": "拼花",
                    "111632057": "微水泥",
                    "18529": "木地板",
                    "18532": "瓷砖",
                    "18642": "人字铺",
                    "18669": "800*800",
                    "18735": "300*300",
                    "19226": "PVC踢脚线",
                    "19392": "500*500",
                    "19393": "门",
                    "100048": "墙砖",
                    "100119": "石材",
                    "100120": "木纹",
                    "100134": "大理石",
                    "105725": "皮革",
                    "116982": "腰线",
                    "121673": "装饰砖",
                    "125179": "石英石",
                    "127017": "布艺",
                    "127054": "方形",
                    "127055": "圆形",
                    "127056": "儿童",
                    "127057": "椭圆",
                    "143801": "文化石",
                    "149696": "装饰贴图",
                    "150868": "瓷砖",
                    "150869": "木纹砖",
                    "150870": "仿古砖",
                    "150871": "马赛克",
                    "150872": "花砖",
                    "151172": "水刀拼花",
                    "151174": "抛光砖",
                    "152153": "烤漆",
                    "936385": "金属玻璃",
                    "193204": "集成吊顶",
                    "212613": "黑板",
                    "214361": "映画",
                    "304098": "水泥墙",
                    "371442": "集成墙面",
                    "371663": "集成吊顶",
                    "5406103": "油画",
                    "5406389": "抽象画",
                    "5406425": "国画书法",
                    "5407465": "风景画",
                    "5407743": "植物挂画",
                    "5407750": "动物挂画",
                    "5407965": "黑白画",
                    "5408117": "人像画",
                    "5408215": "现代创意画",
                    "5408317": "卡通挂画",
                    "5408656": "复古木板画",
                    "5408734": "字母挂画",
                    "5408869": "其它",
                    "3451866": "布纹"
                },
                "categoryIds": [
                    6283304,
                    6818934,
                    8804042,
                    8804043,
                    8804044,
                    8804047,
                    8804048,
                    8804049,
                    8804050,
                    8810635,
                    100043396,
                    100639923,
                    103480272,
                    103552876,
                    15082,
                    15548,
                    16083,
                    16085,
                    16086,
                    16088,
                    16102,
                    16103,
                    16105,
                    16108,
                    16109,
                    16110,
                    16179,
                    111632057,
                    18529,
                    18532,
                    18642,
                    18669,
                    18735,
                    19226,
                    19392,
                    19393,
                    100048,
                    100119,
                    100120,
                    100134,
                    105725,
                    116982,
                    121673,
                    125179,
                    127017,
                    127054,
                    127055,
                    127056,
                    127057,
                    143801,
                    149696,
                    150868,
                    150869,
                    150870,
                    150871,
                    150872,
                    151172,
                    151174,
                    152153,
                    936385,
                    193204,
                    212613,
                    214361,
                    304098,
                    371442,
                    371663,
                    5406103,
                    5406389,
                    5406425,
                    5407465,
                    5407743,
                    5407750,
                    5407965,
                    5408117,
                    5408215,
                    5408317,
                    5408656,
                    5408734,
                    5408869,
                    3451866
                ],
                "spaces": [
                    "客餐厅", "卧室", "书房"
                ]
            },
            "墙面": {
                "categoryNamesHuman": [
                    "瓷砖",
                    "墙纸",
                    "涂料",
                    "石材",
                    "装饰贴图"
                ],
                "categoryItems": {
                    "6283304": "内景图",
                    "6818934": "节日主题",
                    "8804042": "900*900",
                    "8804043": "300*600",
                    "8804044": "现代仿古砖",
                    "8804047": "抛釉砖",
                    "8804048": "抛晶砖",
                    "8804049": "通体砖",
                    "8804050": "大板砖",
                    "8810635": "釉面砖",
                    "100043396": "横幅挂画",
                    "100639923": "其他",
                    "103480272": "岩板",
                    "103552876": "水磨石",
                    "103910540": "墙面漆",
                    "15082": "地砖",
                    "15548": "600*600",
                    "15776": "涂料",
                    "15777": "硅藻泥",
                    "16082": "墙纸",
                    "16085": "天花角线",
                    "16086": "踢脚线",
                    "16091": "小花",
                    "16092": "大花",
                    "16093": "条纹",
                    "16094": "素色",
                    "16095": "儿童",
                    "16096": "壁纸画",
                    "16097": "暖色系",
                    "16098": "中色系",
                    "16099": "冷色系",
                    "16102": "450*450",
                    "16103": "300*300",
                    "16105": "300*450",
                    "16108": "实木踢脚线",
                    "16109": "大理石踢脚线",
                    "16110": "石膏线",
                    "111632057": "微水泥",
                    "16374": "墙布",
                    "18532": "瓷砖",
                    "18669": "800*800",
                    "18735": "300*300",
                    "19226": "PVC踢脚线",
                    "19392": "500*500",
                    "19393": "门",
                    "100048": "墙砖",
                    "100119": "石材",
                    "100120": "木纹",
                    "100134": "大理石",
                    "105725": "皮革",
                    "110874": "图案",
                    "110875": "花纹",
                    "116982": "腰线",
                    "121673": "装饰砖",
                    "125179": "石英石",
                    "127017": "布艺",
                    "127054": "方形",
                    "127055": "圆形",
                    "127056": "儿童",
                    "127057": "椭圆",
                    "143801": "文化石",
                    "149696": "装饰贴图",
                    "150868": "瓷砖",
                    "150869": "木纹砖",
                    "150870": "仿古砖",
                    "150871": "马赛克",
                    "150872": "花砖",
                    "151172": "水刀拼花",
                    "151174": "抛光砖",
                    "152153": "烤漆",
                    "936385": "金属玻璃",
                    "193204": "集成吊顶",
                    "212613": "黑板",
                    "214361": "映画",
                    "304098": "水泥墙",
                    "371442": "集成墙面",
                    "371663": "集成吊顶",
                    "5406103": "油画",
                    "5406389": "抽象画",
                    "5406425": "国画书法",
                    "5407465": "风景画",
                    "5407743": "植物挂画",
                    "5407750": "动物挂画",
                    "5407965": "黑白画",
                    "5408117": "人像画",
                    "5408215": "现代创意画",
                    "5408317": "卡通挂画",
                    "5408656": "复古木板画",
                    "5408734": "字母挂画",
                    "5408869": "其它",
                    "3451866": "布纹"
                },
                "categoryIds": [
                    6283304,
                    6818934,
                    8804042,
                    8804043,
                    8804044,
                    8804047,
                    8804048,
                    8804049,
                    8804050,
                    8810635,
                    100043396,
                    100639923,
                    103480272,
                    103552876,
                    103910540,
                    15082,
                    15548,
                    15776,
                    15777,
                    16082,
                    16085,
                    16086,
                    16091,
                    16092,
                    16093,
                    16094,
                    16095,
                    16096,
                    16097,
                    16098,
                    16099,
                    16102,
                    16103,
                    16105,
                    16108,
                    16109,
                    16110,
                    111632057,
                    16374,
                    18532,
                    18669,
                    18735,
                    19226,
                    19392,
                    19393,
                    100048,
                    100119,
                    100120,
                    100134,
                    105725,
                    110874,
                    110875,
                    116982,
                    121673,
                    125179,
                    127017,
                    127054,
                    127055,
                    127056,
                    127057,
                    143801,
                    149696,
                    150868,
                    150869,
                    150870,
                    150871,
                    150872,
                    151172,
                    151174,
                    152153,
                    936385,
                    193204,
                    212613,
                    214361,
                    304098,
                    371442,
                    371663,
                    5406103,
                    5406389,
                    5406425,
                    5407465,
                    5407743,
                    5407750,
                    5407965,
                    5408117,
                    5408215,
                    5408317,
                    5408656,
                    5408734,
                    5408869,
                    3451866
                ],
                "spaces": [
                    "客餐厅", "卧室", "书房"
                ]
            },
            "筒灯": {
                "categoryNamesHuman": [
                    "射灯"
                ],
                "categoryItems": {
                    "16256": "射灯"
                },
                "categoryIds": [
                    16256
                ],
                "spaces": [
                    "客餐厅", "卧室"
                ]
            },
            "其它": {
                "categoryNamesHuman": [
                    "书桌组合",
                    "梳妆台组合",
                    "沙发组合",
                    "床具组合",
                    "餐桌椅组合",
                    "电视柜组合",
                    "床头柜组合",
                    "其它摆件组合",
                    "卫浴组合",
                    "边柜组合",
                    "沙发组合",
                    "装饰柜架组合",
                    "绿植组合"
                ],
                "categoryItems": {
                    "697365": "书桌组合",
                    "103444969": "梳妆台组合",
                    "154583": "沙发组合",
                    "216053": "床具组合",
                    "154584": "餐桌椅组合",
                    "103447318": "电视柜组合",
                    "103447313": "床头柜组合",
                    "103446899": "其它摆件组合",
                    "192631": "卫浴组合",
                    "103447319": "边柜组合",
                    "103445926": "沙发组合",
                    "103445937": "装饰柜架组合",
                    "103446898": "绿植组合"
                },
                "categoryIds": [
                    697365,
                    103444969,
                    154583,
                    216053,
                    154584,
                    103447318,
                    103447313,
                    103446899,
                    192631,
                    103447319,
                    103445926,
                    103445937,
                    103446898
                ]
            }
        }
        this.public_category_modelloc_map = { "default": {} };

        for (let key in this.modelloc_public_category_map) {
            let ele = this.modelloc_public_category_map[key];
            let spaces = ["default"];
            if (ele.spaces) {
                spaces = [...ele.spaces.filter((sp: any) => sp), "default"];
            }

            for (let space of spaces) {
                if (!this.public_category_modelloc_map[space]) this.public_category_modelloc_map[space] = {};
                if (ele.categoryNamesHuman) {
                    for (let name of ele.categoryNamesHuman) {
                        if (!this.public_category_modelloc_map[space][name]) {
                            this.public_category_modelloc_map[space][name] = new Set<string>();
                        }
                        this.public_category_modelloc_map[space][name].add(key);
                    }

                }
                if (ele.categoryItems) {
                    for (let i_key in ele.categoryItems) {
                        if (!this.public_category_modelloc_map[space][ele.categoryItems[i_key]]) {
                            this.public_category_modelloc_map[space][ele.categoryItems[i_key]] = new Set<string>();
                        }
                        this.public_category_modelloc_map[space][ele.categoryItems[i_key]].add(key);
                    }
                }
            }


        }


    }

    async prepare_fromAiDesk() {
        let data = await fetch(getPrefix() + "./static/db/SizeRangeDB/aideskmodelloc.json").then(val => val.json()).catch(e => null);
        if (!data || !data.rooms) return;

        for (let room_data of data.rooms) {
            let room_name: string = room_data.name;
            let model_loc_list = room_data.modelloc;

            if (!this.public_category_modelloc_map[room_name]) {
                this.public_category_modelloc_map[room_name] = {};
            }


            let add_public_cate_model_loc = (pulic_cate: string, model_loc: string) => {
                if (!this.public_category_modelloc_map[room_name][pulic_cate]) {
                    this.public_category_modelloc_map[room_name][pulic_cate] = new Set<string>();
                }
                if (!this.public_category_modelloc_map['default'][pulic_cate]) {
                    this.public_category_modelloc_map['default'][pulic_cate] = new Set<string>();
                }
                this.public_category_modelloc_map[room_name][pulic_cate].add(model_loc);
                this.public_category_modelloc_map['default'][pulic_cate].add(model_loc);
            }

            if (model_loc_list) {
                for (let model_loc_data of model_loc_list) {
                    add_public_cate_model_loc(model_loc_data.name, model_loc_data.name);

                    let publicCategory_List = model_loc_data.publicCategory;
                    if (publicCategory_List) {
                        for (let public_category of publicCategory_List) {
                            add_public_cate_model_loc(public_category, model_loc_data.name);

                        }
                    }

                    let sub_loc_list = model_loc_data.subloc;

                    if (sub_loc_list) {
                        for (let sub_loc_data of sub_loc_list) {
                            let sub_name = sub_loc_data.name;
                            let t_sub_name = sub_name as string;

                            t_sub_name = t_sub_name.replace("(左)", "");
                            t_sub_name = t_sub_name.replace("(右)", "");

                            let f_model_loc: string = model_loc_data.name + "-" + t_sub_name;

                            let t_public_category = t_sub_name;
                            t_public_category = t_public_category.replace("(横封板)", "");
                            t_public_category = t_public_category.replace("(竖封板)", "");
                            t_public_category = t_public_category.replace("(侧吸)", "");


                            add_public_cate_model_loc(t_public_category, f_model_loc);
                            let sub_publicCategory_List = sub_loc_data.publicCategory;
                            if (sub_publicCategory_List) {
                                for (let public_category of sub_publicCategory_List) {
                                    add_public_cate_model_loc(public_category, f_model_loc);
                                    // console.log(public_category, f_model_loc);

                                }
                            }


                        }
                    }

                }

            }

        }

        // console.log(this.public_category_modelloc_map);
    }


    static getModelLocByPublicCategory(public_cate: string, space_name: string = "default", material_name: string = ""): string {
        if (public_cate == "其它") {
            return "其它";
        }
        if (!ModelLocPublicCategoryMap.instance) {
            ModelLocPublicCategoryMap.instance = new ModelLocPublicCategoryMap();
        }
        if (!ModelLocPublicCategoryMap.instance.public_category_modelloc_map[space_name]) {
            space_name = "default";
        }
        let modelLocsSet = ModelLocPublicCategoryMap.instance.public_category_modelloc_map[space_name][public_cate];
        if (!modelLocsSet && space_name != "default") {
            modelLocsSet = ModelLocPublicCategoryMap.instance.public_category_modelloc_map["default"][public_cate];
        }
        if (modelLocsSet) {

            let ans_model_loc = "";
            let ans_pos_id = -1;
            modelLocsSet.forEach((val) => {
                let pos_id = material_name.indexOf(val);
                if (ans_model_loc.length == 0 || pos_id > ans_pos_id) {
                    ans_model_loc = val;
                }
            })
            return ans_model_loc;
        } else {
            return "";
        }
    }

    static getMaterialNameToModelLoc(name: string, room_name: string = null) {
        // 先裸写一些逻辑

        let conditions: { [key: string]: { keywords: string[] } } = {
            "地毯": {
                keywords: ["地毯"]
            },
            "吊灯": {
                keywords: ["吊灯"]
            },
            "转角沙发": {
                keywords: ["转角沙发"]
            },
            "多人沙发": {
                keywords: ["三人沙发", "双人沙发", "多人沙发", "四人位沙发", "双扶手三位"]
            },
            "茶几": {
                keywords: ["茶几"]
            },
            "边几": {
                keywords: ["边几"]
            },
            "餐桌": {
                keywords: ["餐桌"]
            },
            "餐椅": {
                keywords: ["餐椅"]
            },
            "床": {
                keywords: ["双人床", "单人床", "单层床", "双层床", "儿童床", "儿童单层床"]
            },
            "休闲椅": {
                keywords: ["单人沙发", "单人位沙发", "休闲沙发", "休闲椅"]
            },
            "书桌": {
                keywords: ["书桌"]
            },
            "梳妆台": {
                keywords: ["梳妆台"]
            },
            "马桶": {
                keywords: ["马桶"]
            },
            "梳妆凳": {
                keywords: ["梳妆凳"]
            },
            "花洒": {
                keywords: ["花洒"]
            },
            "毛巾架": {
                keywords: ["毛巾架"]
            },
            "主灯": {
                keywords: ["吸顶灯"]
            },
            "筒灯": {
                keywords: ["筒灯", "射灯"]
            },
            "电视柜": {
                keywords: ["电视柜", "DSG"]
            },
            "床背": {
                keywords: ["床背"]
            },

            "插座": {
                keywords: ["插座", "单开五孔"]
            },
            "墙画": {
                keywords: ["装饰画"]
            },
            "背景墙": {
                keywords: ["背景墙"]
            },
            "投影幕布": {
                keywords: ["投影幕布"]
            },
            "岛台": {
                keywords: ["岛台"]
            },
            "消毒柜": {
                keywords: ["消毒柜"]
            },
            "冰箱": {
                keywords: ["冰箱"]
            },
            "书柜": {
                keywords: ["书柜"]
            },
            "地垫": {
                keywords: ["地垫"]
            },
            "坐垫": {
                keywords: ["坐垫"]
            },
            "办公桌": {
                keywords: ["办公桌"]
            },
            "热水器": {
                keywords: ["热水器"]
            },
            "厨具": {
                keywords: ["厨具"]
            },
            "食品": {
                keywords: ["食品", "马卡龙", "核桃"]
            },
            "储物瓶": {
                keywords: ["储物瓶"]
            },
            "锅具": {
                keywords: ["锅具"]
            },
            "浴室柜": {
                keywords: ["浴室柜"]
            },
            "餐边柜": {
                keywords: ["餐边柜", "CBG"]
            },
            "玄关柜": {
                keywords: ["玄关柜", "XGG"]
            },
            "窗帘": {
                keywords: ["双开帘", "窗帘", "单开帘"]
            },
            "办公文具": {
                keywords: ["笔记本电脑"]
            },
            "床头柜": {
                keywords: ["床头柜"]
            },
            "电视": {
                keywords: ["电视"]
            },
            "床尾凳": {
                keywords: ["床尾凳"]
            },
            "淋浴房": {
                keywords: ["淋浴房", "淋浴屏"]
            },
            "浴缸": {
                keywords: ["浴缸"]
            }

        }


        for (let key in conditions) {
            let condition = conditions[key];
            for (let word of condition.keywords) {
                if (name.indexOf(word) >= 0) {
                    return key;
                }
            }
        }

        return null;
    }


    static getCategoryOfCabinet(cabinet: I_SwjCabinetData, roomname: string = ""): { category: string, sub_category: string } {
        if (!cabinet.name) {
            return null;
        }
        else {
            let cate_dict: { [key: string]: { category: string, sub_category?: string } } = {
                "收口板": { category: "收口板" },
                "修口板": { category: "修口板" },
                "见光板": { category: "见光板" },
                "补板": { category: "补板" },
                "衣柜": { category: "衣柜" },
                "电视柜": { category: "电视柜" },
                "酒柜": { category: "餐边柜" },
                "玄关柜": { category: "玄关柜" },
                "鞋柜": { category: "玄关柜" },
                "转角地柜": { category: "转角地柜" },
                "转角水槽地柜": { category: "转角水槽地柜" },
                "双开门水盆柜": { category: "水槽地柜" },
                "双开门水槽柜": { category: "水槽地柜" },
                "左平开门水盆盲柜": { category: "水槽地柜" },
                "右平开门水盆盲柜": { category: "水槽地柜" },
                "消毒柜跨灶柜": { category: "消毒地柜" },
                "炉灶地柜": { category: "炉灶地柜" },
                "炉灶柜": { category: "炉灶地柜" },
                "灶台柜": { category: "炉灶地柜" },
                "消毒柜": { category: "消毒地柜" },
                "水盆柜": { category: "水槽地柜" },
                "水槽柜": { category: "水槽地柜" },
                "煤气柜": { category: "煤气地柜" },
                "左开门层板吊柜": { category: "单门吊柜" },
                "右开门层板吊柜": { category: "单门吊柜" },
                "双开门层板吊柜": { category: "双门吊柜" },
                "层板地柜": { category: "单门地柜" },
                "碗碟拉篮地柜": { category: "碗碟拉篮地柜" },
                "调味拉篮地柜": { category: "调味拉篮地柜" },
                "拉篮地柜": { category: "拉篮地柜" },
                "烟机柜": { category: "烟机吊柜" },
                "开放格": { category: "开放吊柜" },
                "吊柜": { category: "吊柜" },
                "地柜": { category: "地柜" }
            }
            for (let cate in cate_dict) {
                let res = { category: cate_dict[cate].category, sub_category: cate_dict[cate].sub_category || "" };
                if (!res.sub_category) res.sub_category = res.category;
                if (cabinet.name.indexOf(cate) >= 0) {
                    return res;
                }
            }

            let is_board = false;

            if (Math.min(cabinet.width, cabinet.deep) <= 120) {
                is_board = true;
            }
            if (cabinet.pos_z < 1200) {
                if (cabinet.height < 1300) {
                    return {
                        category: "地柜" + (is_board ? "板" : ""),
                        sub_category: "地柜" + (is_board ? "板" : "")
                    }
                }
                else {

                    return {
                        category: "高柜" + (is_board ? "板" : ""),
                        sub_category: "高柜" + (is_board ? "板" : ""),
                    }
                }

            }
            else {
                return {
                    category: "吊柜" + (is_board ? "板" : ""),
                    sub_category: "吊柜" + (is_board ? "板" : ""),
                }
            }
        }
    }

    static getModelLocOrder(model_cate: string) {
        let level_orders: string[][] = [
            ['沙发', '床', '椅'],
            ['桌', "岛台"],
            ['饰品'],
            ['灯']
        ];

        for (let li = level_orders.length - 1; li >= 0; li--) {
            for (let name of level_orders[li]) {
                if (model_cate.indexOf(name) >= 0) {
                    return li;
                }
            }
        }
        return 0;
    }

    static getModelLocByEnglishLabel(label: string): string[] {
        let category: string = null;
        let publicCategory: string = null;
        switch (label) {
            case AI_FurnitureType.muti_sofa:
                category = "多人沙发";
                publicCategory = "沙发";
                break;
            case AI_FurnitureType.single_sofa:
                category = "单人沙发";
                publicCategory = "沙发";
                break;
            case AI_FurnitureType.chaiseLongue:
                category = "贵妃椅";
                publicCategory = "沙发";
                break;
            case AI_FurnitureType.tea_table:
                category = "茶几";
                publicCategory = category;
                break;
            case AI_FurnitureType.rect_teapoy:
                category = "方几";
                publicCategory = category;
                break;
            case AI_FurnitureType.dining_table:
                category = "餐桌";
                publicCategory = category;
                break;
            case AI_FurnitureType.dining_chair:
                category = "餐椅";
                publicCategory = category;
                break;
            case AI_FurnitureType.desk:
                category = "书桌";
                publicCategory = category;
                break;
            case AI_FurnitureType.desk_chair:
                category = "书椅";
                publicCategory = category;
                break;
            case AI_FurnitureType.book_cabinet:
                category = "书柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.bedside_table:
                category = "床头柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.bed:
                category = "床";
                publicCategory = category;
                break;
            case AI_FurnitureType.double_bed:
                category = "双人床";
                publicCategory = category;
                break;
            case AI_FurnitureType.flue:
                category = "flue";
                publicCategory = category;
                break;
            case AI_FurnitureType.shower_room:
                category = "淋浴房";
                publicCategory = category;
                break;
            case AI_FurnitureType.shower_head:
                category = "花洒";
                publicCategory = category;
                break;
            case AI_FurnitureType.tv:
                category = "电视";
                publicCategory = "电视";
                break;
            case AI_FurnitureType.wardrobe:
                category = "衣柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.cupboard:
                category = "橱柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.gas_cooker:
                category = "gas_cooker";
                publicCategory = category;
                break;
            case AI_FurnitureType.vegatable_basin:
                category = "洗菜盆";
                publicCategory = category;
                break;
            case AI_FurnitureType.toilet:
                category = "蹲便器";
                publicCategory = "马桶";
                break;
            case "water_closet":
                category = "马桶";
                publicCategory = category;
                break;
            case AI_FurnitureType.refrigerator:
                category = "冰箱";
                publicCategory = category;
                break;
            case AI_FurnitureType.curtain:
                category = "窗帘";
                publicCategory = category;
                break;
            case AI_FurnitureType.side_table:
                category = "边几";
                publicCategory = category;
                break;
            case "group_dining_table_chair":
                category = "餐桌椅组合";
                publicCategory = category;
                break;
            case "group_sofa_table":
                category = "沙发组合";
                publicCategory = category;
                break;
            case "bathroom_cabinet":
                category = "浴室柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.meal_side_cabinet:
                category = "餐边柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.wine_cabinet:
                category = "酒柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.entrance_cabinet:
                category = "玄关柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.washing_machine:
                category = "洗衣机";
                publicCategory = category;
                break;
            case AI_FurnitureType.balcony_cabinet:
                category = "阳台柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.pipeline:
                category = "pipeline";
                publicCategory = category;
                break;
            case AI_FurnitureType.tv_stand:
                category = "电视柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.L_sofa:
                category = "沙发";
                publicCategory = "转角沙发";
                break;
            case AI_FurnitureType.leisure_chair:
                category = "休闲椅";
                publicCategory = category;
                break;
            case AI_FurnitureType.washing_cabinet:
                category = "洗衣机柜";
                publicCategory = category;
                break;
            case AI_FurnitureType.sculpture:
                category = "雕塑";
                publicCategory = category;
                break;
            case AI_FurnitureType.shower_room_s:
                category = "淋浴房-弧形";
                publicCategory = category;
                break;
            case AI_FurnitureType.stool:
                category = "凳子";
                publicCategory = category;
                break;
            case AI_FurnitureType.main_light:
                category = "主灯";
                publicCategory = category;
            case AI_FurnitureType.chandelier:
                category = "吊灯";
                publicCategory = "主灯";
                break;
            case AI_FurnitureType.floor_lamp:
                category = "落地灯";
                publicCategory = category;
                break;
            case AI_FurnitureType.dressing_table:
                category = "梳妆台";
                publicCategory = category;
                break;

            case AI_FurnitureType.foot_pedal:
                category = "脚踏";
                publicCategory = category;
                break;
            case AI_FurnitureType.shower_room_j:
                category = "矩形淋浴房";
                publicCategory = category;
                break;
            case AI_FurnitureType.shower_room_y:
                category = "一字形淋浴房";
                publicCategory = category;
                break;
            case AI_FurnitureType.shower_room_z:
                category = "钻石形淋浴房";
                publicCategory = category;
                break;
            case AI_FurnitureType.towel_rack:
                category = "毛巾架";
                publicCategory = category;
                break;
            case AI_FurnitureType.background_wall:
                category = "背景墙";
                publicCategory = category;
                break;
            case AI_FurnitureType.round_table:
                category = "圆几";
                publicCategory = category;
                break;
            default:
                console.log("getModelLocByEnglishLabel: " + label);
                break;
        }
        return [category, publicCategory];
    }

    static convertModelLocOfCabinetFigure(sub_category: string) {

        if (sub_category.endsWith("板")) // 板件结尾的
        {

        }
        else if (sub_category.endsWith("地柜")) {
            if (!sub_category.startsWith("地柜-")) {
                return "地柜-" + sub_category;
            }

        }
        else if (sub_category.endsWith("吊柜")) {
            if (!sub_category.startsWith("吊柜-")) {
                return "吊柜-" + sub_category;
            }
        }
        else if (sub_category.endsWith("高柜")) {
            if (!sub_category.startsWith("高柜-")) {
                return "高柜-" + sub_category;
            }
        }
        else if (sub_category.endsWith("冰箱")) {
            if (!sub_category.startsWith("")) {
                return "" + sub_category;
            }
        }

        return sub_category;

    }
}