"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[2640],{22640:function(n,e,t){t.d(e,{A:function(){return P}});var r=t(13274),i=t(93783),o=t(88934),a=t(1870),c=t(27347),s=t(93321),l=t(33100),u=t(41594),d=t(85783);function p(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function f(){var n=p(["\n          position: absolute;\n          top: 0%;\n          width: 100%;\n          height: 100%;\n          background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */\n          z-index: 999; /* 确保蒙层在其他元素之上 */\n        "]);return f=function(){return n},n}function h(){var n=p(["\n            position: absolute;\n            width: 1080px;\n            height: 671px;\n            left: 50%;\n            top: 50%;\n            background: #fff;\n            border-radius: 12px;\n            padding-bottom: 10px;\n            transform: translate(-50%,-50%);\n            @media screen and (max-width: 768px){\n                width: 580px;\n                min-height: 698px;\n            }\n            @media screen and (orientation: landscape){\n                width: 748px;\n                height: 520px;\n            }\n           \n        "]);return h=function(){return n},n}function m(){var n=p(["\n            width: 100%;\n            height:64px;\n            text-align:center;\n            color: #000000D8;\n            font-weight: 700;\n            font-size: 20px;\n            line-height: 64px;\n            letter-spacing: 0px;\n        "]);return m=function(){return n},n}function x(){var n=p(["\n          position:absolute;\n          top:14px;\n          right:14px;\n          width:24px;\n          height:24px;\n          font-size:20px !important;\n          line-height:24px;\n          text-align:center;\n          border-radius:4px;\n          color:#A2A2A5;\n          &:hover {\n            background :#A2A2A533;\n\n          }\n        "]);return x=function(){return n},n}function g(){var n=p(["\n            display: flex;\n            height: 100%;\n            \n        "]);return g=function(){return n},n}function v(){var n=p(["\n            width: 75%;\n            margin-left: 30px;\n            @media screen and (max-width: 768px){\n                width: 70%;\n            }\n        "]);return v=function(){return n},n}function b(){var n=p(["\n            width: 25%;\n            height:585px;\n            margin: 0px 0 0 20px;\n            background:#F4F5F5;\n            img {\n                width:100%;\n            }\n            @media screen and (max-width: 768px){\n                width: 30%;\n            }\n            @media screen and (orientation: landscape){\n                height: 438px;\n            }\n       "]);return b=function(){return n},n}function _(){var n=p(["\n            position:absolute;\n            bottom : 20px;\n            left:100px;\n            width:88px;\n            height:32px;\n            border-radius:6px;\n            background:#fff;\n            border: 1px solid #00000026;\n            cursor:pointer;\n            &:hover {\n                background:#ffffff88;\n            }\n       "]);return _=function(){return n},n}function y(){var n=p(["\n            .row_div {\n                display: flex;\n                .row_cate {\n                    margin-bottom:6px;\n                    padding-top:2px;\n                    padding-bottom:2px;                   \n                    border-radius:12px;\n                    width: 10%;\n                    color: #282828;\n                    font-weight: 700;\n                    font-size: 12px;\n                    line-height: 20px;\n                    letter-spacing: 0px;\n                    text-align: left;\n\n                }\n                .row_tags {\n                    /* max-width: 350px;\n                    overflow-x: scroll; */\n                    width: 90%;\n                    margin-bottom:6px;\n                    .tag {\n                        float:left;\n                        padding-left:10px;\n                        padding-right:10px;\n                        padding-top:2px;\n                        padding-bottom:2px;\n                        background:#fafafa;\n\n                        border-radius:12px;\n                        margin-right:8px;\n                        // width:66px;\n                        color: #5B5E60;\n                        font-size: 12px;\n                        line-height: 20px;\n                        letter-spacing: 0px;\n                        text-align: center;\n                        cursor:pointer;\n                        &:hover {\n                            color:#5B5E60;\n                            background:#aaaaaa33;\n                        }\n                        &.checked {\n                            color:#147FFA;\n                            background:#E6F6FF;\n                        }\n                    }\n                }\n            }\n       "]);return y=function(){return n},n}function w(){var n=p(["\n            width: 100%;\n            height: calc(100% - 200px);\n            position: relative;\n            overflow:scroll;\n            @media screen and (max-width: 768px){\n                height: calc(100% - 639px);\n            }\n            @media screen and (orientation: landscape){\n                height: calc(100% - 160px);\n            }\n       "]);return w=function(){return n},n}function k(){var n=p(["\n        position:absolute;\n        top:0;\n        left:0;\n        width:100%;\n        height:100%;\n        background: #fff;\n        overflow:hidden;\n        z-index: 9;\n       "]);return k=function(){return n},n}function j(){var n=p(["\n        width:140px;\n        height:50px;\n        line-height:50px;\n        font-size:20px;\n        border-radius:5px;\n        position:absolute;\n        text-align:center;\n        left:calc( 50% - 70px );\n        top:calc( 50% - 25px );\n       "]);return j=function(){return n},n}function A(){var n=p(["\n        float:left;\n        position: relative;\n        width:230px;\n        margin-right:20px;\n        margin-bottom:24px;\n        border-radius:4px;\n        cursor:pointer;\n        &:hover {\n            box-shadow:0px 2px 8px -1px rgba(30,41,59,0.12)\n        }\n        @media screen and (max-width: 768px){\n            width: 160px;\n        }\n        img {\n            width: 100%;\n            height:129px;\n            border-radius:4px;\n    \n        }\n        .img_element {\n            position:absolute;\n            width:80px;\n            height:60px;\n            top:4px;\n            left:4px;\n            border-radius: 4px;\n            background: #F8F8F8;\n        }\n        .layout2d_img {\n            width:52px;\n            height:52px;\n            position:absolute;\n            left:14px;\n            top:4px;\n        }\n        .result_title {\n            width:100%;\n            height:22px;\n            color: #25282D;\n            font-weight: 700;\n            font-size: 14px;\n            line-height: 22px;\n            letter-spacing: 0px;\n            text-align: left;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n            overflow:hidden;\n            margin-top: 4px;\n        }\n        .result_tags {\n            width:100%;\n            height:16px;\n            font-size:12px;\n            color:#595959;\n            margin-top: 4px;\n            .bestFit {\n                display:inline;\n                font-weight:700;\n                text-align:center;\n                color:#52C41A;\n                background:linear-gradient(90deg, #F6FFED 0%, #D9F7BE 100%);\n                border:1px solid #D9F7BE;\n                border-radius:4px;\n            }\n            .defaultFit {\n                display:inline;\n                text-align:center;\n                color:#5B5E60;\n                border-radius: 4px;\n                background: linear-gradient(90deg, #FAFAFA 0%, #EAEAEB 100%);\n                border: 1px solid #EAEAEB;\n            }\n        }\n       "]);return A=function(){return n},n}function N(){var n=p(["\n            display: flex;\n            flex-wrap: wrap;\n            height: 100%;\n            overflow: hidden;\n            .ant-skeleton-button\n            {\n                width: 100% !important;\n                /* height: 100% !important; */\n                margin: 10px 10px 0px 0px;\n            }\n            .Skeleton_content\n            {\n                width: 31%;\n                margin-right: 2%;\n                aspect-ratio: 1 / 1;\n                display: flex;\n                flex-direction: column;\n            }\n        "]);return N=function(){return n},n}var S=(0,t(79874).rU)((function(n){n.token;var e=n.css;return{popup_root:e(f()),main_dialog:e(h()),top_title:e(m()),close_btn:e(x()),container:e(g()),containerInfo:e(v()),room_layout_img:e(b()),reset_layout_btn:e(_()),tags_container:e(y()),result_container:e(w()),result_progress:e(k()),progress_title:e(j()),result_element:e(A()),skeletonBox:e(N())}})),F=t(65640);function E(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function I(n,e,t,r,i,o,a){try{var c=n[o](a),s=c.value}catch(n){return void t(n)}c.done?e(s):Promise.resolve(s).then(r,i)}function B(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){I(o,r,i,a,c,"next",n)}function c(n){I(o,r,i,a,c,"throw",n)}a(void 0)}))}}function z(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){c=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw i}}return o}}(n,e)||D(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(n){return function(n){if(Array.isArray(n))return E(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||D(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(n,e){if(n){if("string"==typeof n)return E(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?E(n,e):void 0}}function O(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&c[0]?r.return:c[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,c[1])).done)return i;switch(r=0,i&&(c=[2&c[0],i.value]),c[0]){case 0:case 1:i=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,r=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!i||c[1]>i[0]&&c[1]<i[3])){o.label=c[1];break}if(6===c[0]&&o.label<i[1]){o.label=i[1],i=c;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(c);break}i[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],r=0}finally{t=i=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}var P=function(){var n=function(){var n=Array.from({length:6}).map((function(n,e){return(0,r.jsxs)("div",{className:"Skeleton_content",children:[(0,r.jsx)(s.A.Button,{style:{height:"160px"},active:!0},"skeleton_button_1_".concat(e)),(0,r.jsx)(s.A.Button,{style:{height:"20px"},active:!0},"skeleton_button_2_".concat(e)),(0,r.jsx)(s.A.Button,{style:{height:"15px"},active:!0},"skeleton_button_3_".concat(e))]})}));return(0,r.jsx)("div",{className:t.skeletonBox,children:n})},e=(0,d.B)().t,t=S().styles,p=z((0,u.useState)(!1),2),f=(p[0],p[1]),h=z((0,u.useState)(null),2),m=h[0],x=h[1],g=z((0,u.useState)([{title:e("方案"),tags:[{name:e("单空间"),checked:!0}]},{title:e("归属"),tags:[{name:e("门店"),checked:!1},{name:e("企业"),checked:!1},{name:e("平台"),checked:!0}]},{title:e("空间"),tags:[{name:"客餐厅",checked:!1},{name:"厨房",checked:!1},{name:"卧室",checked:!1}]}]),2),v=g[0],b=g[1],_=z((0,u.useState)(!1),2),y=_[0],w=_[1],k=z((0,u.useState)([]),2),j=k[0],A=k[1],N="平台",E=!1;c.nb.on(o.U.ShowSchemeTestingLeftPanel,(function(n){f(n)}));var I=function(){var n=B((function(n){var t,r,i,o,a,c,s,u,d,p,f,h,m,x;return O(this,(function(g){switch(g.label){case 0:if(!1===n.enabled)return l.A.info(e("暂未开放")),[2];if(n.checked)return[2];if(n.checked=!n.checked,n.checked){r=!0,i=!1,o=void 0;try{for(a=v[Symbol.iterator]();!(r=(c=a.next()).done);r=!0)if((s=c.value).tags.includes(n)){u=!0,d=!1,p=void 0;try{for(f=s.tags[Symbol.iterator]();!(u=(h=f.next()).done);u=!0)(m=h.value).checked=m===n}catch(n){d=!0,p=n}finally{try{u||null==f.return||f.return()}finally{if(d)throw p}}}}catch(n){i=!0,o=n}finally{try{r||null==a.return||a.return()}finally{if(i)throw o}}}return x=(null===(t=v[1].tags.find((function(n){return n.checked})))||void 0===t?void 0:t.name)||"",x&&x!==N?(N=x,[4,D()]):[3,2];case 1:g.sent(),g.label=2;case 2:return b(C(v)),P(),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),D=function(){var n=B((function(){var n;return O(this,(function(e){switch(e.label){case 0:return n=c.nb.instance,"平台"!==N?[3,2]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer",!0,!0)];case 1:return e.sent(),[3,6];case 2:return"企业"!=N?[3,4]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer",!0,!1)];case 3:return e.sent(),[3,6];case 4:return"门店"!=N?[3,6]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer_store",!0,!1)];case 5:e.sent(),e.label=6;case 6:return E=!0,[2]}}))}));return function(){return n.apply(this,arguments)}}(),P=function(){var n=B((function(){var n,e,t,r,o,s,l;return O(this,(function(u){switch(u.label){case 0:return c.nb.instance?(n=c.nb.instance,w(!0),E?[3,2]:[4,D()]):[3,4];case 1:u.sent(),u.label=2;case 2:return e=v[2].tags.find((function(n){return n.checked})),t=C(n.layout_container._rooms),e&&(t=t.filter((function(n){return(0,a.MP)([(n.name||n.roomname)+n._t_id],[e.name])})))[0]&&(n.layer_CadRoomNameLayer&&(n.layer_CadRoomNameLayer._name_mode=1),x(n.layout_container.saveLayoutSchemeImage(560,1170,.8,!0,t[0])),n.layer_CadRoomNameLayer&&(n.layer_CadRoomNameLayer._name_mode=0)),r=n.layout_graph_solver.queryDreamerRoomsWithLayout(t),o=[],s=function(){var n=B((function(n){var e,t,r=arguments;return O(this,(function(a){switch(a.label){case 0:return e=r.length>1&&void 0!==r[1]?r[1]:90,[4,i.H.instance.getSchemeInfo(n)];case 1:return(t=a.sent())&&(t.fit_score=e,o.push(t)),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),l=r.map((function(n){return s(n.scheme_id,n.score)})),[4,Promise.all(l)];case 3:u.sent(),o.sort((function(n,e){return(e.fit_score||0)-(n.fit_score||0)})),F.log(r,o.map((function(n){return[n.schemeId,n.fit_score]}))),A(o),w(!1),u.label=4;case 4:return[2]}}))}));return function(){return n.apply(this,arguments)}}();return(0,u.useEffect)((function(){!function(){var n=c.nb.instance.layout_container,e=!0,t=!1,r=void 0;try{for(var i,o=n._furniture_entities[Symbol.iterator]();!(e=(i=o.next()).done);e=!0)i.value.is_selected=!1}catch(n){t=!0,r=n}finally{try{e||null==o.return||o.return()}finally{if(t)throw r}}n.updateRoomsFromEntities()}(),function(){if(c.nb.instance){var n=c.nb.instance;if(n.layout_container){n.layout_container.updateWholeBox();var e=v[2];e.tags=[];var t=C(n.layout_container._rooms),r=["入户花园","阳台","厨房","卧室","卫生间","客餐厅"];t.sort((function(n,e){return r.indexOf(e.roomname)+e.area/1e3-(r.indexOf(n.roomname)+n.area/1e3)})),t.forEach((function(n,e){return n._t_id=e+1})),e.tags=t.map((function(n,e){return{name:(n.name||n.roomname)+n._t_id,checked:0==e}})),b(C(v)),x(n.layout_container.saveLayoutSchemeImage(560,1170,.8,!0))}}}(),P()}),[]),(0,r.jsx)("div",{className:t.popup_root,id:"dramerPopUp",children:(0,r.jsxs)("div",{className:t.main_dialog,id:"dreamerDialog",children:[(0,r.jsx)("div",{className:t.top_title,children:e("布局方案")}),(0,r.jsx)("div",{className:"iconfont iconclose1 "+t.close_btn,onClick:function(){c.nb.emit(o.U.ShowDreamerPopup,!1)}}),(0,r.jsxs)("div",{className:t.container,children:[(0,r.jsx)("div",{className:t.room_layout_img,children:m&&(0,r.jsx)("img",{src:m})}),(0,r.jsxs)("div",{className:t.containerInfo,children:[(0,r.jsx)("div",{className:t.tags_container,children:v.map((function(n,e){return(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("div",{className:"row_cate",children:n.title}),(0,r.jsx)("div",{className:"row_tags",children:n.tags.map((function(n,e){return(0,r.jsx)("div",{className:"tag"+(n.checked?" checked":""),onClick:function(){return I(n)},children:n.name},"tag_ele_"+e)}))})]},"tags_row_"+e)}))}),(0,r.jsxs)("div",{className:t.result_container,id:"dreamhouse_result_container",children:[y&&(0,r.jsx)("div",{className:t.result_progress,children:(0,r.jsx)(n,{})}),j.map((function(n,i){return(0,r.jsxs)("div",{className:t.result_element,onClick:function(){return function(n){var e={origin:"layoutai.api",data:{schemeId:n.schemeId}};window.parent.postMessage(e,"*"),F.log("postMessage",JSON.stringify(e))}(n)},children:[(0,r.jsx)("div",{style:{position:"absolute"},children:(0,r.jsx)("div",{className:"img_element",children:(0,r.jsx)("img",{src:n.layout2d,className:"layout2d_img"})})}),(0,r.jsx)("img",{src:n.imagePath+"?x-oss-process=image/resize,w_600"}),(0,r.jsx)("div",{className:"result_title",title:n.schemeName,children:n.schemeName}),(0,r.jsx)("div",{className:"result_tags",children:n.fit_score>80?(0,r.jsx)("div",{className:"bestFit",children:e("匹配度")+":"+Math.min(Math.round(n.fit_score),95)+"%"}):(0,r.jsx)("div",{className:"defaultFit",children:e("匹配度")+":"+Math.min(Math.round(n.fit_score),80)+"%"})})]},"dreamhouse_result_"+i)}))]})]})]})]})})}}}]);