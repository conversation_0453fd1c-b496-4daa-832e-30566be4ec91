import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { AI_PolyTargetType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { ZDistanceDimension } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { T_TransformElement } from "./T_TransformElement";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { checkIsMobile } from "@/config";

interface IExistingInput {
    [key: string]: HTMLInputElement | undefined;
}
export class T_DimensionWallElement extends T_TransformElement
{
    wall_dimensions : ZDistanceDimension[];
    _vertical_neighbor_walls: TWall[] = [];
    _container:TLayoutEntityContainer;
    _existingInputs: IExistingInput = {};
    openEdit: boolean;
    draw_dimension: ZDistanceDimension[];
    constructor(container:TLayoutEntityContainer)
    {
        super();
        this._container = container;   
        this.wall_dimensions = [];
        this.draw_dimension = [];
        this._element_name = "T_DimensionWallElement";
        this.openEdit = false;
        this._isMovingVisible = true;
        this.IsDimensionElement = true;
        this._allow_entity_types =["Wall"];
    }
    get target_wall()
    {
        if(!this._target_rect) return null;
        return TBaseEntity.getEntityOfRect(this._target_rect) as TWall;
    }

    checkEditRect(): boolean {
        return false;
    }

    cleanDimensionInput() 
    {
        let elements = document.querySelectorAll('[id*="dimension_input"]');
        for(let i = 0; i < elements.length; i++) {
            let element = elements[i] as HTMLElement;
            element.style.display = "none";
        }
    }

    getNormal(center: Vector3) {
        let pp = this._target_rect.project(center);
        let pos = this._target_rect.unproject({x: pp.x, y: 0});
        let nor = center.clone().sub(pos).normalize();
        return nor;
    }

    draw_line_dimension() {
        for(let room of this._container._room_entities)
        {
            for(let edge of room._room_poly.edges)
            {
                let v0 = edge.v0.pos.clone().addScaledVector(edge.nor, -220);
                let v1 = edge.v1.pos.clone().addScaledVector(edge.nor, -220);
                let dim = new ZDistanceDimension(v0, v1);
                dim.offset_len = 0;
                dim.text_offset_len = 0;
                dim.nor.set(0,1,0);
                this.draw_dimension.push(dim);
            }
        }
    }

    updateElement(): void {
        this.wall_dimensions = [];
        this.draw_dimension = [];
        if(!this._target_rect)
        {
            this.cleanDimensionInput();
            this.draw_line_dimension();
            return;
        }
        let entity = TBaseEntity.getEntityOfRect(this._target_rect) as TWall;
        if(!entity) return;
        if(TBaseEntity.get_polygon_type(this._target_rect) !== AI_PolyTargetType.Wall) return;
        // 循环外轮廓，找到相邻的墙对应的轮廓
        for(let room of this._container._room_entities)
        {
            for(let edge of room._room_poly.edges)
            {
                if(Math.abs(edge.nor.dot(this._target_rect.nor)) > 0.9 ) continue;
                let pp = this._target_rect.project(edge.center);
                let p0 = this._target_rect.project(edge.v0.pos);
                let p1 = this._target_rect.project(edge.v1.pos);

                let t_w = this._target_rect.w;
                let t_h = this._target_rect.h;
                if(pp.x < -(t_w/2 + t_h) || pp.x > (t_w/2 + t_h)) continue;
                if(this._target_rect.h/2+1. - Math.min(Math.abs(p0.y),Math.abs(p1.y)) < 0) continue;
                let v0 = edge.v0.pos.clone().addScaledVector(edge.nor, -220);
                let v1 = edge.v1.pos.clone().addScaledVector(edge.nor, -220);
                let dim = new ZDistanceDimension(v0, v1);
                // if(dim.distance < 2) continue;
                if(dim.distance < 122) continue;
                dim.nor.set(0,1,0);
                dim.offset_len = 0; 
                dim.text_offset_len = 0;
                this.wall_dimensions.push(dim);
            }
        }
        let isMobile = checkIsMobile();
        for(let i = 0; i < this.wall_dimensions.length; i++) {
            // 创建 input 元素
            if(!this._existingInputs[`_existingInput${i}`])
            {
                let div = document.getElementById('Canvascontent');
                this._existingInputs[`_existingInput${i}`] = document.createElement('input') as HTMLInputElement;

                this._existingInputs[`_existingInput${i}`].style.display = "block";
                this.setInputStyle(this._existingInputs[`_existingInput${i}`], i, !isMobile);
                div.appendChild(this._existingInputs[`_existingInput${i}`]);
            } else {
                this._existingInputs[`_existingInput${i}`].style.display = "block";
            }
            if(!this.openEdit)
            {
               this.setAttributes(this._existingInputs[`_existingInput${i}`], i);
            }
      
            // 根据 this.wall_dimensions[i] 的值设置 input 元素的属性
            if(this._existingInputs[`_existingInput${i}`])
            {
                
                this._existingInputs[`_existingInput${i}`].style.width = ((this._existingInputs[`_existingInput${i}`].value.length + 1) * 7) + 'px';
                this._existingInputs[`_existingInput${i}`].onkeydown = (ev: KeyboardEvent) => {
                    if(ev.key == "Enter")
                    {
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.DimensionWall, this._existingInputs[`_existingInput${i}`]);
                        this._existingInputs[`_existingInput${i}`]?.blur();
                        this.openEdit = false;
                        this.updateElement();
                    }
                }

                this._existingInputs[`_existingInput${i}`].onfocus = () => {
                    this._existingInputs[`_existingInput${i}`].select();  
                    this.openEdit = true;
                }

                this._existingInputs[`_existingInput${i}`].onblur = () => {
                    this.openEdit = false;
                }

                this._existingInputs[`_existingInput${i}`].oninput = () => {
                    this._existingInputs[`_existingInput${i}`].value = this._existingInputs[`_existingInput${i}`].value.replace(/[^0-9]/g, '');
                    this._existingInputs[`_existingInput${i}`].style.width = ((this._existingInputs[`_existingInput${i}`].value.length + 1) * 7) + 'px';
                }
            }
            // 将 input 元素添加到 this._existingInputs 数组中
        }

        // 删除多余的输入框
        for(let i = this.wall_dimensions.length; this._existingInputs[`_existingInput${i}`]; i++) {
            this._existingInputs[`_existingInput${i}`].remove();
            this._existingInputs[`_existingInput${i}`] = null;
        }
    }

    setAttributes(input: HTMLInputElement, i: number) {
        input.setAttribute('origin_num',Math.round(this.wall_dimensions[i].distance).toString());
        input.value =  Math.round(this.wall_dimensions[i].distance).toString();
        let nor = this.getNormal(this.wall_dimensions[i].origin_center)
        input.setAttribute('_nor_v3',JSON.stringify(nor));
    }

    setInputStyle(input: HTMLInputElement, i: number, canEdit: boolean) {
        input.className = `dimension_input${i}`;
        input.id = `dimension_input${i}`;
        input.autocomplete = "off";
        // 设置是否可编辑
        input.readOnly = !canEdit;
        // 如果不可编辑，只改变背景和边框，但保持可以选中
        if (!canEdit) {
            input.style.background = 'transparent';
            input.style.border = 'none';
            input.style.cursor = 'default';
        } else {
            input.style.background = '#fff';
            input.style.border = '1px solid #d2d3d4';
            input.style.cursor = 'pointer';
        }
    }

    setAttribute(input: HTMLInputElement, value: number) {
        input.setAttribute("value", value.toString());
    }
    updateInputPosition(input: HTMLInputElement, pp: { x: number, y: number }) {
        input.style.left = (pp.x - 18) + "px";
        if(checkIsMobile)
        {
            input.style.top = (pp.y - 18) + "px";
        }
        else
        {
            input.style.top = (pp.y - 106) + "px";
        }
    }

    drawCanvas(painter: TPainter): void {
        // if(this.is_moving) return;
        // if(!this.visible) return;
        if(this.wall_dimensions)
        {
            for(let i = 0; i < this.wall_dimensions.length; i++) {
                // this.wall_dimensions[i]._font_size =  1.8 / painter._p_sc;
                // painter.drawDimension(this.wall_dimensions[i]);
                let pos0 = this.wall_dimensions[i].pos0.clone();
                let pos1 = this.wall_dimensions[i].pos1.clone();
                painter.strokeStyle = "#282828";
                painter.fillStyle = "#282828";
                painter._context.lineWidth = 0.5;
                painter._context.beginPath();
                painter.drawSegment(pos0.clone(),pos1.clone());
                painter._context.stroke();

                let center = this.wall_dimensions[i].origin_center;
                let pp = painter.worldToCanvas(center);
                this.updateInputPosition(this._existingInputs[`_existingInput${i}`], pp);
            }
        }
        painter._context.closePath();
    }

}