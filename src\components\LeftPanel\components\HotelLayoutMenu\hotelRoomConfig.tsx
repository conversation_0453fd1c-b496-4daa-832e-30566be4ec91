import React, { useState } from 'react';
import './HotelRoomConfig.css';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { I_SpaceTypeConfig, baseSpaceService } from '@/Apps/LayoutAI/Services/BaseSpace/BaseSpaceService';


interface HotelRoomConfigProps {
  onConfirm: (config: {
    wallThickness: number;
    roomTypes: I_SpaceTypeConfig[];
    corridorWidth: number;
    otherFacilities: string[];
  },
  ) => void;
  onClose:()=>void;

}


const HotelRoomConfig: React.FC<HotelRoomConfigProps> = ({ onConfirm,onClose }) => {
  const [wallThickness, setWallThickness] = useState<number>(120);
  const [corridorWidth, setCorridorWidth] = useState<number>(1.3);
  
  const [roomTypes, setRoomTypes] = useState<I_SpaceTypeConfig[]>(baseSpaceService.spaceNameConfigs);

  const configKeyName :{[key:string]:string}= {
    "defaultWidth":LayoutAI_App.t("默认宽度"),
    "minWidth": LayoutAI_App.t("最小宽度"),
    "defaultDepth":LayoutAI_App.t("默认深度"),
    "minDepth":LayoutAI_App.t("最小深度"),
    "targetCount":LayoutAI_App.t("目标数量"),
    "ratioWeight":LayoutAI_App.t("分布比重")

  }
  const [otherFacilities, setOtherFacilities] = useState<string[]>([
    '布草间',
    '回收间',
    '消洗间',
  ]);

  const facilityOptions = [
    '布草间',
    '回收间',
    '消洗间',
    '洗衣房',
    '健身房',
    '其他',
  ];

  const handleFacilityToggle = (facility: string) => {
    if (otherFacilities.includes(facility)) {
      setOtherFacilities(otherFacilities.filter(f => f !== facility));
    } else {
      setOtherFacilities([...otherFacilities, facility]);
    }
  };

  const handleRoomTypeChange = (index: number, field: keyof I_SpaceTypeConfig, value: string | number) => {
    const updatedRoomTypes = [...roomTypes];
    updatedRoomTypes[index] = {
      ...updatedRoomTypes[index],
      [field]: ( field === 'defaultWidth' || field === "defaultDepth") ? Number(value) : value
    };
    baseSpaceService.spaceNameConfigs = (updatedRoomTypes);
    setRoomTypes(updatedRoomTypes);
  };

  const handleConfirm = () => {
    onConfirm({
      wallThickness,
      roomTypes,
      corridorWidth,
      otherFacilities,
    });
  };

  return (
    <div className="hotel-room-config-container">
      <div className="config-content">
        <div className='closeBtn' onClick={()=>{
          onClose();
        }}>X</div>
        <div className="config-section">
            <h4>排房参数配置</h4>
        </div>
        <div className="config-section" style={{height:450,overflow:"auto"}}>
          {roomTypes.map((roomType,index)=><div key={"RoomType"+index} className='config-row'>
            <h4 className='config-name'>{roomType.name}</h4>
            <div className='config-item-list'>
                {Object.keys(roomType).map((key,index)=>{
                  if(key==="name") return null;
                  return <div className='config-item'>
                        <span>{configKeyName[key] || key}</span>
                        <input type="number" defaultValue={(roomType as any)[key]} onChange={(ev)=>{
                              handleRoomTypeChange(index,key as any,ev.target.value);
                        }}></input>
                  </div>
                }).filter(e=>e)}
            </div>

          </div>)}
        </div>
            
        <button className="confirm-button" onClick={handleConfirm}>
          确认
        </button>
      </div>
    </div>
  );
};

export default HotelRoomConfig;