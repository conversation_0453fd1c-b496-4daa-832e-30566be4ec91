import { Vector3 } from "three";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { ZRect } from "@layoutai/z_polygon";
import { TBaseRoomToolUtil } from "../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TLayoutFineTuningBaseTool, TLayoutFineTuningOperationToolUtil } from "./TLayoutFineTuningOperationToolUtil";
import { TLayoutGroupFineTuningData } from "./TLayoutGroupFineTuningData";
import { ZEdge } from "@layoutai/z_polygon";

interface IScoreFuncConfig {
    name: string;
    scoreFunc: any;
    params: any;
}

enum TLayoutGroupFineTuningScoreType {
    k_none,
    k_checkFigureOverlay,
    k_checkFlowBetweenFigures,
    k_checkFigureInOuterBox,

    k_checkRectOverlay,
    k_checkFlowBetweenRects,
    k_checkRectInOuterBox,
}

export enum TLayoutGroupFineTuningType {
    k_none,
    k_sofaGroupFigure,
    k_sofaGroupRect,
    k_diningTableGroupFigure,
    k_diningTableGroupRect,
    k_bedGroupFigure,
    k_bedGroupRect,
}

export class TLayoutGroupFineTuningManagerToolUtil {
    private static _instance: TLayoutGroupFineTuningManagerToolUtil;

    private static readonly _moveStep: number = 50;

    private _groupScoreFuncMap: Map<TLayoutGroupFineTuningScoreType, IScoreFuncConfig>;

    private constructor() {
        this._groupScoreFuncMap = new Map<TLayoutGroupFineTuningScoreType, IScoreFuncConfig>();
        this._groupScoreFuncMap.set(TLayoutGroupFineTuningScoreType.k_checkFigureOverlay, {
            name: "元素干涉",
            scoreFunc: calFigureOverlayInfo,
            params: {weight: -100},
        });
        this._groupScoreFuncMap.set(TLayoutGroupFineTuningScoreType.k_checkFlowBetweenFigures, {
            name: "过道检查",
            scoreFunc: calFlowBetweenFiguresInfo,
            params: { minFlowDistance: 300, subMinFlowDistance: 50, weight: 100},
        });
        this._groupScoreFuncMap.set(TLayoutGroupFineTuningScoreType.k_checkFigureInOuterBox, {
            name: "元素在包罗框外侧检查",
            scoreFunc: calOuterBoxScoreInfo,
            params:  {weight: -100},
        });

        this._groupScoreFuncMap.set(TLayoutGroupFineTuningScoreType.k_checkRectOverlay, {
            name: "矩形干涉",
            scoreFunc: calRectOverlayInfo,
            params: {weight: -100},
        });
        this._groupScoreFuncMap.set(TLayoutGroupFineTuningScoreType.k_checkFlowBetweenRects, {
            name: "矩形过道检查",
            scoreFunc: calFlowBetweenRectsInfo,
            params: { minFlowDistance: 200 , weight: 100},
        });
        this._groupScoreFuncMap.set(TLayoutGroupFineTuningScoreType.k_checkRectInOuterBox, {
            name: "矩形在包罗框外侧检查",
            scoreFunc: calOuterBoxScoreInfoByRects,
            params: {weight: -100},
        });
    }

    public static get instance(): TLayoutGroupFineTuningManagerToolUtil {
        if(!this._instance)
        {
            this._instance = new TLayoutGroupFineTuningManagerToolUtil();
        }
        return this._instance;
    }

    public fineTuningGroup(figures: TFigureElement[], targetLen: number, targetWidth: number, groupType: TLayoutGroupFineTuningType, center: Vector3 = null, rectNor: Vector3 = null){
        if(!figures || figures.length == 0)
        {
            return;
        }
        figures = figures.filter(figure => figure && (figure.rect || figure.matched_rect));
        let targetRect: ZRect = createRectByFigures(figures, targetLen, targetWidth, center, rectNor);
        let scoreTypes: TLayoutGroupFineTuningScoreType[] = convertGroupTypeToScoreTypes(groupType);
        let groupFigureData: TLayoutGroupFineTuningData = new TLayoutGroupFineTuningData(figures);
        let specialMainAdornmentContainMainFigures: Map<TFigureElement, TFigureElement[]> = groupFigureData.getSpecialMainAdornmentContainFigures();
        // 1. 整体矩形进行包围盒的外侧检查
        if(scoreTypes.includes(TLayoutGroupFineTuningScoreType.k_checkFigureInOuterBox))
        {
            let groupRect: ZRect = getGroupRect(groupFigureData);
            let oldGroupCenter: Vector3 = groupRect.rect_center.clone();
            this.fineTuningRects(targetRect, [groupRect], TLayoutGroupFineTuningManagerToolUtil._moveStep, [TLayoutGroupFineTuningScoreType.k_checkRectInOuterBox]);
            let moveVec: Vector3 = groupRect.rect_center.clone().sub(oldGroupCenter);
            groupFigureData.moveMainFigure(moveVec, true);

            let mainFigure: TFigureElement = groupFigureData.getMainFigure();
            let mainRect: ZRect = (mainFigure.matched_rect || mainFigure.rect).clone();
            let mainRectCenter: Vector3 = mainRect.rect_center.clone();
            this.fineTuningRects(targetRect, [mainRect], TLayoutGroupFineTuningManagerToolUtil._moveStep, [TLayoutGroupFineTuningScoreType.k_checkRectInOuterBox]);
            mainFigureBackCloseTargetRectEdge(targetRect, mainRect, groupType);
            moveVec = mainRect.rect_center.clone().sub(mainRectCenter);
            groupFigureData.moveMainFigure(moveVec, true);
        }
        // 下面这块可以进行循环
        let adornmentFineTuningTypes: TLayoutGroupFineTuningScoreType[] = [];
        if(scoreTypes.includes(TLayoutGroupFineTuningScoreType.k_checkFigureOverlay))
        {
            adornmentFineTuningTypes.push(TLayoutGroupFineTuningScoreType.k_checkFigureOverlay);
        }
        if(scoreTypes.includes(TLayoutGroupFineTuningScoreType.k_checkFigureInOuterBox))
        {
            adornmentFineTuningTypes.push(TLayoutGroupFineTuningScoreType.k_checkFigureInOuterBox);
        }
        
        while(true)
        {
            let allMainFigures: TFigureElement[] = groupFigureData.getAllMainFigures();
            let oldScore: number = this.calScoreByFigureAndBox(targetRect, allMainFigures, [], groupFigureData, scoreTypes).score;
            // 保持主图元不动，其他此图元进行微调
            let subMainFigures: TFigureElement[] = groupFigureData.getSubMainFigures();
            this.fineTuningGroupFigureInfos(targetRect, allMainFigures, subMainFigures, groupFigureData, TLayoutGroupFineTuningManagerToolUtil._moveStep, scoreTypes);

            // 主图元的饰品，与非主图元没有相交的图元通用需要进行微调，这个后面再说，先建立起评分器
            if(adornmentFineTuningTypes.length > 0)
            {
                let mainAdornmentFigures: TFigureElement[] = groupFigureData.getCanFineTuningAdornmentFigures();
                this.fineTuningGroupFigureInfos(
                    targetRect, [...allMainFigures, ...mainAdornmentFigures], mainAdornmentFigures, groupFigureData, TLayoutGroupFineTuningManagerToolUtil._moveStep, adornmentFineTuningTypes);
            }
            let newScore: number = this.calScoreByFigureAndBox(targetRect, allMainFigures, [], groupFigureData, scoreTypes).score;
            if(newScore - oldScore < 0.1)
            {
                break;
            }
        }
        let boxExtendRatio: number = 0.1;
        let canHideAdornmentFigures: TFigureElement[] = groupFigureData.getCanHideMainAdornmentFigures();
        if(canHideAdornmentFigures.length > 0)
        {
            let outerInfo: any = calOuterBoxScoreInfo(targetRect, canHideAdornmentFigures, null, null);
            // 重新计算核心图元的包络框
            let groupRect: ZRect = getGroupRect(groupFigureData);
            let groupRectDv: Vector3 = groupRect.dv.clone();
            let groupRectNor: Vector3 = groupRect.nor.clone();
            let len: number = groupRect.length;
            let width: number = groupRect.depth;
            let maxLen: number = Math.max(len, width);
            for(let figure of outerInfo.figures)
            {
                let subVec: Vector3 =  (figure.matched_rect || figure.rect).rect_center.clone().sub(center);
                let tempLen: number = Math.abs(subVec.clone().dot(groupRectDv));
                let tempdDepth: number = Math.abs(subVec.clone().dot(groupRectNor));

                if((tempLen - maxLen/2) > maxLen * boxExtendRatio || (tempdDepth - maxLen/2) > maxLen * boxExtendRatio)
                {
                    figure.markMaterialAsInvisible();
                }
            }
        }


        // 修正地毯特色图元的大小
        for(let entry of specialMainAdornmentContainMainFigures.entries())
        {
            let carpetFigure: TFigureElement = entry[0];
            let containFigures: TFigureElement[] = entry[1];
            let containRect: ZRect = getBoxRectByFigures(containFigures);
            if(Math.abs((carpetFigure.matched_rect || carpetFigure.rect).nor.clone().dot(containRect.nor)) > 0.5)
            {
                (carpetFigure.matched_rect || carpetFigure.rect).length = containRect.length;
                (carpetFigure.matched_rect || carpetFigure.rect).depth = containRect.depth;
            }
            else
            {
                (carpetFigure.matched_rect || carpetFigure.rect).length = containRect.depth;
                (carpetFigure.matched_rect || carpetFigure.rect).depth = containRect.length;
            }
            (carpetFigure.matched_rect || carpetFigure.rect).rect_center = containRect.rect_center.clone();
        }
    }

    protected fineTuningGroupFigureInfos(
        targetRect: ZRect, figures: TFigureElement[], fineTuningFigures: TFigureElement[],
        groupFigureData: TLayoutGroupFineTuningData, moveStep: number, scoreTypes: TLayoutGroupFineTuningScoreType[])
    {
        let fineTuningInfo: any = this.calScoreByFigureAndBox(targetRect, figures, fineTuningFigures, groupFigureData,scoreTypes);
        let newFineTuningFigures = fineTuningInfo.fineTuningFigures;
        if(newFineTuningFigures.length == 0)
        {
            return;
        }
        let fineTuningMoveInfos: Map<TFigureElement, any> = this.initFineTuningFigureMoveInfo(
            targetRect, figures, newFineTuningFigures, groupFigureData, moveStep, scoreTypes);
        let oldChangeStep: number = 0;
        while (true) {
            let lastScoreInfo: any = this.calScoreByFigureAndBox(targetRect, figures, fineTuningFigures, groupFigureData, scoreTypes);
            while (true) {
                let fineTuningFigureCount: number = 0;
                for (let entry of fineTuningMoveInfos.entries()) {
                    let fineTuningMoveInfo: any = entry[1];
                    if (Math.abs(fineTuningMoveInfo.moveStep) < 0.5) {
                        fineTuningFigureCount++;
                        continue;
                    }
                    let fineTuningFigure: TFigureElement = entry[0];
                    let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [fineTuningFigure]);
                    let moveVec: Vector3 = fineTuningMoveInfo.moveDir.clone().multiplyScalar(fineTuningMoveInfo.moveStep);
                    let moveScore = this.calScoreByMoveFigure(
                        targetRect, otherFigures, fineTuningFigure, groupFigureData, moveVec, scoreTypes);
                    let diffScore: number = moveScore - fineTuningMoveInfo.score;
                    fineTuningMoveInfo.score = moveScore;
                    if (diffScore < 0.1) {
                        fineTuningMoveInfo.moveStep = -fineTuningMoveInfo.moveStep / 2;
                    }
                }
                if (fineTuningFigureCount == fineTuningMoveInfos.size) {
                    break;
                }
            }
            let currentScoreInfo: any = this.calScoreByFigureAndBox(targetRect, figures, fineTuningFigures, groupFigureData, scoreTypes);
            let currentFineTuningFigures: TFigureElement[] = currentScoreInfo.fineTuningFigures;
            let currentChangeStep: number = Math.abs(currentScoreInfo.score - lastScoreInfo.score);
            if (!currentFineTuningFigures.length || Math.abs(currentChangeStep - oldChangeStep) < 0.1 || isSameFigures(currentFineTuningFigures, lastScoreInfo.fineTuningFigures)) {
                break;
            }
            oldChangeStep = currentChangeStep;
            fineTuningMoveInfos = this.initFineTuningFigureMoveInfo(targetRect, figures, currentFineTuningFigures, groupFigureData, moveStep, scoreTypes);
        }
    }

    protected calScoreByFigureAndBox(targetRect: ZRect, figures: TFigureElement[], fineTuningFigures: TFigureElement[], groupFigureData: TLayoutGroupFineTuningData, scoreTypes: TLayoutGroupFineTuningScoreType[]): any {
        let scoreFuncList: IScoreFuncConfig[] = [];
        for (let scoreType of scoreTypes) {
            let scoreFuncConfig: IScoreFuncConfig = this._groupScoreFuncMap.get(scoreType);
            if (scoreFuncConfig) {
                scoreFuncList.push(scoreFuncConfig);
            }
        }
        if (!scoreFuncList) {
            return { score: 0, fineTuningFigures: [] };
        }
        let tempFineTuningFigures: TFigureElement[] = [];
        let totalScore: number = 0;
        let parseInfo = (info: any) => {
            if (info) {
                totalScore += info.score;
                for (let figure of info.figures) {
                    tempFineTuningFigures.push(figure);
                }
            }
        };
        for (let scoreFunc of scoreFuncList) {
            let scoreInfo: any = scoreFunc.scoreFunc(targetRect, figures, groupFigureData, scoreFunc.params);
            parseInfo(scoreInfo);
        }
        tempFineTuningFigures = Array.from(new Set(tempFineTuningFigures));
        let resultFineTuningFigures: TFigureElement[] = [];
        tempFineTuningFigures.forEach(figure => {
            for(let fineTuningFigure of fineTuningFigures)
            {
                if(fineTuningFigure == figure)
                {
                    resultFineTuningFigures.push(figure);
                    return;
                }
            }
        });
        return { score: totalScore, fineTuningFigures: resultFineTuningFigures };
    }

    protected initFineTuningFigureMoveInfo(
        targetRect: ZRect, figures: TFigureElement[], fineTuningFigures: TFigureElement[], groupFigureData: TLayoutGroupFineTuningData,
        moveStep: number, scoreTypes: TLayoutGroupFineTuningScoreType[]): Map<TFigureElement, any> {
        let sourceScore: number = this.calScoreByFigureAndBox(targetRect, figures, fineTuningFigures, groupFigureData, scoreTypes).score;
        let fineTuningFigureMoveInfos: Map<TFigureElement, any> = new Map<TFigureElement, any>();
        for (let fineTuningFigure of fineTuningFigures) {
            let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [fineTuningFigure]);
            let diffDirScoreInfos: Map<Vector3, number> = this.getDiffDirScore(
                targetRect, otherFigures, fineTuningFigure, groupFigureData, moveStep, sourceScore, scoreTypes);
            let maxScore: number = Number.NEGATIVE_INFINITY;
            for (let entry of diffDirScoreInfos.entries()) {
                if (entry[1] > maxScore) {
                    maxScore = entry[1];
                }
            }
            let moveDirs: Vector3[] = [];
            for (let entry of diffDirScoreInfos.entries()) {
                if (entry[1] == maxScore) {
                    moveDirs.push(entry[0]);
                }
            }
            let targetMoveDir: Vector3 = null;
            if (moveDirs.length == 1) {
                targetMoveDir = moveDirs[0];
            }
            else if (moveDirs.length > 1) {
                // 再初步判断的时候，如果有多方向移动的效果一直，则判断哪个方向移动的到合适的位置最短,确定每个figure最后应当往方向进行移动
                let minMoveLen: number = Number.POSITIVE_INFINITY;
                for (let moveDir of moveDirs) {
                    let tempMoveLen: number = 0;
                    let tempMoveStep: number = moveStep;
                    let oldScore: number = sourceScore;
                    while (true) {
                        // TODO 将图元一直往那个方向进行移动，到合适的位置需要记录的长度
                        if (Math.abs(tempMoveStep) < 0.5) {
                            break;
                        }
                        tempMoveLen = tempMoveLen + tempMoveStep
                        let tempMoveVec: Vector3 = moveDir.clone().multiplyScalar(tempMoveLen);
                        let tempScore: number = this.calScoreByMoveFigure(targetRect, otherFigures, fineTuningFigure.clone(), groupFigureData, tempMoveVec, scoreTypes);
                        let tempDiffScore: number = tempScore - oldScore;
                        oldScore = tempScore;
                        if (tempDiffScore < 0.1) {
                            tempMoveStep = -tempMoveStep / 2;
                        }
                    }
                    if (minMoveLen > tempMoveLen) {
                        minMoveLen = tempMoveLen;
                        targetMoveDir = moveDir;
                    }
                }
            }
            if (targetMoveDir) {
                let fineTuningMoveInfo: any = {
                    moveLen: 0,
                    moveDir: targetMoveDir,
                    moveStep: targetMoveDir ? moveStep : 0,
                    score: sourceScore
                };
                fineTuningFigureMoveInfos.set(fineTuningFigure, fineTuningMoveInfo);
            }
        }
        return fineTuningFigureMoveInfos;
    }

    protected getDiffDirScore(targetRect: ZRect, otherFigures: TFigureElement[], fineTuningFigure: TFigureElement, groupFigureData: TLayoutGroupFineTuningData, moveStep: number, sourceScore: number, scoreTypes: TLayoutGroupFineTuningScoreType[]): Map<Vector3, number> {
        let upDir: Vector3 = (fineTuningFigure.matched_rect || fineTuningFigure.rect).nor.clone();
        let downDir: Vector3 = (fineTuningFigure.matched_rect || fineTuningFigure.rect).nor.clone().multiplyScalar(-1);
        let leftDir: Vector3 = (fineTuningFigure.matched_rect || fineTuningFigure.rect).dv.clone();
        let rightDir: Vector3 = (fineTuningFigure.matched_rect || fineTuningFigure.rect).dv.clone().multiplyScalar(-1);
        let upScore: number = this.calScoreByMoveFigure(targetRect, otherFigures, fineTuningFigure.clone(), groupFigureData, upDir.clone().multiplyScalar(moveStep), scoreTypes);
        let downScore: number = this.calScoreByMoveFigure(targetRect, otherFigures, fineTuningFigure.clone(), groupFigureData, downDir.clone().multiplyScalar(moveStep), scoreTypes);
        let leftScore: number = this.calScoreByMoveFigure(targetRect, otherFigures, fineTuningFigure.clone(), groupFigureData, leftDir.clone().multiplyScalar(moveStep), scoreTypes);
        let rightScore: number = this.calScoreByMoveFigure(targetRect, otherFigures, fineTuningFigure.clone(), groupFigureData, rightDir.clone().multiplyScalar(moveStep), scoreTypes);
        let recordDiffDirScoreInfo: Map<Vector3, number> = new Map<Vector3, number>();
        if (sourceScore < upScore) {
            recordDiffDirScoreInfo.set(upDir, upScore);
        }
        if (sourceScore < downScore) {
            recordDiffDirScoreInfo.set(downDir, downScore);
        }
        if (sourceScore < leftScore) {
            recordDiffDirScoreInfo.set(leftDir, leftScore);
        }
        if (sourceScore < rightScore) {
            recordDiffDirScoreInfo.set(rightDir, rightScore);
        }
        return recordDiffDirScoreInfo;
    }

    protected calScoreByMoveFigure(
        targetRect: ZRect, otherFigures: TFigureElement[], fineTuningFigure: TFigureElement, groupFigureData: TLayoutGroupFineTuningData, moveVec: Vector3, scoreTypes: TLayoutGroupFineTuningScoreType[]): number {
        if(groupFigureData)
        {
            groupFigureData.moveFigure(fineTuningFigure, moveVec, true);
        }
        else
        {
            TLayoutFineTuningOperationToolUtil.instance.moveFigure(fineTuningFigure, moveVec);
        }
        let scoreInfo: any = this.calScoreByFigureAndBox(targetRect, [...otherFigures, fineTuningFigure], [], groupFigureData, scoreTypes);
        return scoreInfo.score;
    }

    public fineTuningGroupRect(rects: ZRect[], targetLen: number, targetWidth: number, groupType: TLayoutGroupFineTuningType, center: Vector3 = null, rectNor: Vector3 = null){
        // 构造目标盒子
        let targetRect: ZRect = createRectByRects(rects, targetLen, targetWidth, center, rectNor);
        let scoreTypes: TLayoutGroupFineTuningScoreType[] = convertGroupTypeToScoreTypes(groupType);
        this.fineTuningRects(targetRect, rects, TLayoutGroupFineTuningManagerToolUtil._moveStep, scoreTypes);
    }
    
    protected fineTuningRects(targetRect: ZRect, rects: ZRect[], moveStep: number, scoreTypes: TLayoutGroupFineTuningScoreType[]) {
        let fineTuningInfo: any = this.calScoreByRectAndBox(targetRect, rects, scoreTypes);
        let fineTuningRects: ZRect[] = fineTuningInfo.fineTuningRects;
        let fineTuningMoveInfos: Map<ZRect, any> = this.initFineTuningRectMoveInfo(targetRect, rects, fineTuningRects, moveStep, scoreTypes);
        while (true) {
            let lastScoreInfo: any = this.calScoreByRectAndBox(targetRect, rects, scoreTypes);
            while (true) {
                let fineTuningFigureCount: number = 0;
                for (let entry of fineTuningMoveInfos.entries()) {
                    let fineTuningMoveInfo: any = entry[1];
                    if (Math.abs(fineTuningMoveInfo.moveStep) < 0.5) {
                        fineTuningFigureCount++;
                        continue;
                    }
                    let fineTuningRect: ZRect = entry[0];
                    let otherRects: ZRect[] = getOtherRects(rects, [fineTuningRect]);
                    let moveVec: Vector3 = fineTuningMoveInfo.moveDir.clone().multiplyScalar(fineTuningMoveInfo.moveStep);
                    let moveScore = this.calScoreByMoveRect(targetRect, otherRects, fineTuningRect, moveVec, scoreTypes);
                    let diffScore: number = moveScore - fineTuningMoveInfo.score;
                    fineTuningMoveInfo.score = moveScore;
                    if (diffScore < 0.1) {
                        fineTuningMoveInfo.moveStep = -fineTuningMoveInfo.moveStep / 2;
                    }
                }
                if (fineTuningFigureCount == fineTuningMoveInfos.size) {
                    break;
                }
            }
            let currentScoreInfo: any = this.calScoreByRectAndBox(targetRect, rects, scoreTypes);
            let currentFineTuningRects: ZRect[] = currentScoreInfo.fineTuningRects;
            if (!currentFineTuningRects.length || Math.abs(currentScoreInfo.score - lastScoreInfo.score) < 0.1) {
                break;
            }
            fineTuningMoveInfos = this.initFineTuningRectMoveInfo(targetRect, rects, currentFineTuningRects, moveStep, scoreTypes);
        }
    }

    protected calScoreByMoveRect(
        targetRect: ZRect, otherRects: ZRect[], fineTuningRect: ZRect, moveVec: Vector3, scoreTypes: TLayoutGroupFineTuningScoreType[]): number {
        TLayoutFineTuningOperationToolUtil.instance.moveRect(fineTuningRect, moveVec);
        let scoreInfo: any = this.calScoreByRectAndBox(targetRect, [...otherRects, fineTuningRect], scoreTypes);
        return scoreInfo.score;
    }

    protected calScoreByRectAndBox(targetRect: ZRect, rects: ZRect[], scoreTypes: TLayoutGroupFineTuningScoreType[]): any {
        let scoreFuncList: IScoreFuncConfig[] = [];
        for (let scoreType of scoreTypes) {
            let scoreFuncConfig: IScoreFuncConfig = this._groupScoreFuncMap.get(scoreType);
            if (scoreFuncConfig) {
                scoreFuncList.push(scoreFuncConfig);
            }
        }
        if (!scoreFuncList) {
            return { score: 0, fineTuningRects: [] };
        }
        let fineTuningRects: ZRect[] = [];
        let totalScore: number = 0;
        let parseInfo = (info: any) => {
            if (info) {
                totalScore += info.score;
                for (let rect of info.rects) {
                    fineTuningRects.push(rect);
                }
            }
        };
        for (let scoreFunc of scoreFuncList) {
            let scoreInfo: any = scoreFunc.scoreFunc(targetRect, rects, scoreFunc.params);
            parseInfo(scoreInfo);
        }
        rects = Array.from(new Set(rects));
        return { score: totalScore, fineTuningRects: rects };
    }

    protected initFineTuningRectMoveInfo(
        targetRect: ZRect, rects: ZRect[], fineTuningRects: ZRect[], moveStep: number, scoreTypes: TLayoutGroupFineTuningScoreType[]): Map<ZRect, any> {
        let sourceScore: number = this.calScoreByRectAndBox(targetRect, rects, scoreTypes).score;
        let fineTuningRectMoveInfos: Map<ZRect, any> = new Map<ZRect, any>();
        for (let fineTuningRect of fineTuningRects) {
            let otherRects: ZRect[] = getOtherRects(rects, [fineTuningRect]);
            let diffDirScoreInfos: Map<Vector3, number> = this.getDiffDirScoreByRect(targetRect, otherRects, fineTuningRect, moveStep, sourceScore, scoreTypes);
            let maxScore: number = Number.NEGATIVE_INFINITY;
            for (let entry of diffDirScoreInfos.entries()) {
                if (entry[1] > maxScore) {
                    maxScore = entry[1];
                }
            }
            let moveDirs: Vector3[] = [];
            for (let entry of diffDirScoreInfos.entries()) {
                if (entry[1] == maxScore) {
                    moveDirs.push(entry[0]);
                }
            }
            let targetMoveDir: Vector3 = null;
            if (moveDirs.length == 1) {
                targetMoveDir = moveDirs[0];
            }
            else if (moveDirs.length > 1) {
                // 再初步判断的时候，如果有多方向移动的效果一直，则判断哪个方向移动的到合适的位置最短,确定每个figure最后应当往方向进行移动
                let minMoveLen: number = Number.POSITIVE_INFINITY;
                for (let moveDir of moveDirs) {
                    let tempMoveLen: number = 0;
                    let tempMoveStep: number = moveStep;
                    let oldScore: number = sourceScore;
                    while (true) {
                        // TODO 将图元一直往那个方向进行移动，到合适的位置需要记录的长度
                        if (Math.abs(tempMoveStep) < 0.5) {
                            break;
                        }
                        tempMoveLen = tempMoveLen + tempMoveStep
                        let tempMoveVec: Vector3 = moveDir.clone().multiplyScalar(tempMoveLen);
                        let tempScore: number = this.calScoreByMoveRect(targetRect, otherRects, fineTuningRect.clone(), tempMoveVec, scoreTypes);
                        let tempDiffScore: number = tempScore - oldScore;
                        oldScore = tempScore;
                        if (tempDiffScore < 0.1) {
                            tempMoveStep = -tempMoveStep / 2;
                        }
                    }
                    if (minMoveLen > tempMoveLen) {
                        minMoveLen = tempMoveLen;
                        targetMoveDir = moveDir;
                    }
                }
            }
            if (targetMoveDir) {
                let fineTuningMoveInfo: any = {
                    moveLen: 0,
                    moveDir: targetMoveDir,
                    moveStep: targetMoveDir ? moveStep : 0,
                    score: sourceScore
                };
                fineTuningRectMoveInfos.set(fineTuningRect, fineTuningMoveInfo);
            }
        }
        return fineTuningRectMoveInfos;
    }

    protected getDiffDirScoreByRect(targetRect: ZRect, otherRects: ZRect[], fineTuningRect: ZRect, moveStep: number, sourceScore: number, scoreTypes: TLayoutGroupFineTuningScoreType[]): Map<Vector3, number> {
        let upDir: Vector3 = fineTuningRect.nor.clone();
        let downDir: Vector3 = fineTuningRect.nor.clone().multiplyScalar(-1);
        let leftDir: Vector3 = fineTuningRect.dv.clone();
        let rightDir: Vector3 = fineTuningRect.dv.clone().multiplyScalar(-1);
        let upScore: number = this.calScoreByMoveRect(targetRect, otherRects, fineTuningRect.clone(), upDir.clone().multiplyScalar(moveStep), scoreTypes);
        let downScore: number = this.calScoreByMoveRect(targetRect, otherRects, fineTuningRect.clone(), downDir.clone().multiplyScalar(moveStep), scoreTypes);
        let leftScore: number = this.calScoreByMoveRect(targetRect, otherRects, fineTuningRect.clone(), leftDir.clone().multiplyScalar(moveStep), scoreTypes);
        let rightScore: number = this.calScoreByMoveRect(targetRect, otherRects, fineTuningRect.clone(), rightDir.clone().multiplyScalar(moveStep), scoreTypes);
        let recordDiffDirScoreInfo: Map<Vector3, number> = new Map<Vector3, number>();
        if (sourceScore < upScore) {
            recordDiffDirScoreInfo.set(upDir, upScore);
        }
        if (sourceScore < downScore) {
            recordDiffDirScoreInfo.set(downDir, downScore);
        }
        if (sourceScore < leftScore) {
            recordDiffDirScoreInfo.set(leftDir, leftScore);
        }
        if (sourceScore < rightScore) {
            recordDiffDirScoreInfo.set(rightDir, rightScore);
        }
        return recordDiffDirScoreInfo;
    }
}

function convertGroupTypeToScoreTypes(groupType: TLayoutGroupFineTuningType): TLayoutGroupFineTuningScoreType[] {
    if(groupType == TLayoutGroupFineTuningType.k_sofaGroupFigure)
    {
        return [TLayoutGroupFineTuningScoreType.k_checkFigureOverlay, TLayoutGroupFineTuningScoreType.k_checkFlowBetweenFigures, TLayoutGroupFineTuningScoreType.k_checkFigureInOuterBox];
    }
    else if(groupType == TLayoutGroupFineTuningType.k_sofaGroupRect)
    {
        return [TLayoutGroupFineTuningScoreType.k_checkRectOverlay, TLayoutGroupFineTuningScoreType.k_checkFlowBetweenRects, TLayoutGroupFineTuningScoreType.k_checkRectInOuterBox];
    }
    else if(groupType == TLayoutGroupFineTuningType.k_diningTableGroupFigure)
    {
        return [TLayoutGroupFineTuningScoreType.k_checkFigureOverlay, TLayoutGroupFineTuningScoreType.k_checkFigureInOuterBox];
    }
    else if(groupType == TLayoutGroupFineTuningType.k_diningTableGroupRect)
    {
        return [TLayoutGroupFineTuningScoreType.k_checkRectOverlay, TLayoutGroupFineTuningScoreType.k_checkRectInOuterBox];
    }
    else if(groupType == TLayoutGroupFineTuningType.k_bedGroupFigure)
    {
        return [TLayoutGroupFineTuningScoreType.k_checkFigureOverlay, TLayoutGroupFineTuningScoreType.k_checkFigureInOuterBox];
    }
    else if(groupType == TLayoutGroupFineTuningType.k_bedGroupRect)
    {
        return [TLayoutGroupFineTuningScoreType.k_checkRectOverlay, TLayoutGroupFineTuningScoreType.k_checkRectInOuterBox];
    }
    return [TLayoutGroupFineTuningScoreType.k_none];
}

function getOtherRects(allRects: ZRect[], rects: ZRect[]): ZRect[]
{
    let otherRects: ZRect[] = [];
    allRects.forEach(rect => {
        if(rects.indexOf(rect) < 0)
        {
            otherRects.push(rect);
        }
    });
    return otherRects;
}

function createRectByRects(rects: ZRect[], length: number, width: number, center: Vector3 = null, nor: Vector3 = null): ZRect {
    if(!center)
    {
        center = TBaseRoomToolUtil.instance.calAverageCenterByRects(rects);
    }
    let targetRect: ZRect = new ZRect(length, width);
    targetRect.nor = nor ? nor : new Vector3(0, 1, 0);
    targetRect.rect_center = center.clone();
    targetRect.updateRect();
    return targetRect;
}

function createRectByFigures(figures: TFigureElement[], length: number, width: number, center: Vector3 = null, nor: Vector3 = null): ZRect {
    let rects: ZRect[] = TBaseRoomToolUtil.instance.getRectsByFigures(figures, true);
    let targetRect: ZRect = createRectByRects(rects, length, width, center, nor);
    return targetRect;
}

function getGroupRect(groupFigureData: TLayoutGroupFineTuningData): ZRect {
    let mainFigure: TFigureElement = groupFigureData.getMainFigure();
    let subMainFigures: TFigureElement[] = groupFigureData.getSubMainFigures();
    let figures: TFigureElement[] = [mainFigure, ...subMainFigures];
    let targetRect: ZRect = getBoxRectByFigures(figures);
    return targetRect;
}

function getBoxRectByFigures(figures: TFigureElement[]): ZRect{
    let rectRange: any = TBaseRoomToolUtil.instance.getBoxRangByFigurs(figures.filter(figure => figure && !figure.isMaterialMarkAsInvisible()), true);
    let rectRangeCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(rectRange);
    let targetRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(rectRange, rectRangeCenter);
    return targetRect;
}


function calFigureOverlayInfo(targetRect: ZRect, figures: TFigureElement[], groupFigureData: TLayoutGroupFineTuningData, extendParams: any = null): any {
    void (targetRect);
    void(groupFigureData);
    let overlayRatio: number = 0;
    let overlayFigures: TFigureElement[] = [];
    for (let figure of figures) {
        for (let otherFigure of figures) {
            if (otherFigure == figure) {
                continue;
            }
            if (!TBaseRoomToolUtil.instance.isOverlayByFigures(figure, otherFigure)) {
                continue;
            }
            // 计算重叠的面积比例（重叠面积比）
            overlayRatio += TBaseRoomToolUtil.instance.calOverlayRatioByFigures(figure, otherFigure, 0, false);
            overlayFigures.push(figure);
            overlayFigures.push(otherFigure);
        }
    }
    let weight: number = extendParams && "weight" in extendParams ? extendParams.weight : -100;
    let score: number = overlayRatio * weight;
    return { score: score, figures: overlayFigures };
}

function calRectOverlayInfo(targetRect: ZRect, rects: ZRect[], extendParams: any = null): any {
    void (targetRect);

    let overlayRatio: number = 0;
    let overlayRects: ZRect[] = [];
    for (let rect of rects) {
        for (let otherRect of rects) {
            if (otherRect == rect) {
                continue;
            }
            if (!TBaseRoomToolUtil.instance.isOverlayByRects(rect, otherRect)) {
                continue;
            }
            // 计算重叠的面积比例（重叠面积比）
            overlayRatio += TBaseRoomToolUtil.instance.calOverlayRatioByRects(rect, otherRect);
            overlayRects.push(rect);
            overlayRects.push(otherRect);
        }
    }
    let weight: number = extendParams && "weight" in extendParams ? extendParams.weight : -100;
    let score: number = overlayRatio * weight;
    return { score: score, rects: overlayRects };
}

function calFlowBetweenFiguresInfo(targetRect: ZRect, figures: TFigureElement[], groupFigureData: TLayoutGroupFineTuningData, extendParams: any = null): any {
    void (targetRect);

    let minFlowDistance: number = extendParams && "minFlowDistance" in extendParams ? extendParams.minFlowDistance : 200;
    if(minFlowDistance == 0)
    {
        minFlowDistance = 0.0001;
    }
    let subMinFlowDistance: number = extendParams && "subMinFlowDistance" in extendParams ? extendParams.subMinFlowDistance : 50;
    if(subMinFlowDistance == 0)
    {
        subMinFlowDistance = 0.0001;
    }
    let ratio: number = 0;
    let abnormalFigures: TFigureElement[] = [];
    let xDir: Vector3 = new Vector3(1, 0, 0);
    let yDir: Vector3 = new Vector3(0, 1, 0);
    let mainFigure: TFigureElement = null;
    if(groupFigureData)
    {
        mainFigure = groupFigureData.getMainFigure();
    }
   
    let isMainFigureFaceX: boolean = null;
    if(mainFigure)
    {
        isMainFigureFaceX = Math.abs((mainFigure.matched_rect || mainFigure.rect).nor.clone().dot(xDir)) > Math.abs((mainFigure.matched_rect || mainFigure.rect).nor.clone().dot(yDir)) ? true : false;
    }
    for (let figure of figures) {
        let figureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(figure.matched_rect || figure.rect);
        for (let otherFigure of figures) {
            if (figure == otherFigure) {
                continue;
            }
            // 过道计算这个想起来还是蛮复杂的，涉及到物体在物体之内，以及物体部分重叠和物体不重叠之间的衡量
            let otherFigureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(otherFigure.matched_rect || otherFigure.rect);
            let isXMix: boolean = figureRange.xMin < otherFigureRange.xMax && figureRange.xMax > otherFigureRange.xMin;
            let isYMix: boolean = figureRange.yMin < otherFigureRange.yMax && figureRange.yMax > otherFigureRange.yMin;
            let subX: number = Math.min(Math.abs(figureRange.xMax - otherFigureRange.xMin), Math.abs(otherFigureRange.xMax - figureRange.xMin));
            let subY: number = Math.min(Math.abs(figureRange.yMax - otherFigureRange.yMin), Math.abs(otherFigureRange.yMax - figureRange.yMin));
            if (isXMix && isYMix) {
                // 这种同样也是需要考虑两个方向，最好使用包围盒进行衡量, 两个方向的叠加
                let tempRatio: number = 0;
                if(mainFigure && (figure == mainFigure || otherFigure == mainFigure))
                {
                    if(isMainFigureFaceX)
                    {
                        tempRatio = (-subX - minFlowDistance) / minFlowDistance;
                        tempRatio += (-subY - subMinFlowDistance) / minFlowDistance;
                    }
                    else
                    {
                        tempRatio = (-subX - subMinFlowDistance) / minFlowDistance;
                        tempRatio += (-subY - minFlowDistance) / minFlowDistance;
                    }
                }
                else
                {
                    tempRatio = (-subX - minFlowDistance) / minFlowDistance;
                    tempRatio += (-subY - minFlowDistance) / minFlowDistance;
                }
                if(Math.abs(tempRatio) > 0.0001)
                {
                    ratio += tempRatio;
                    abnormalFigures.push(figure);
                    abnormalFigures.push(otherFigure);
                }
            }
            else if (isXMix && subY < minFlowDistance) {
                let tempRatio: number = 0;
                if(mainFigure && (figure == mainFigure || otherFigure == mainFigure))
                {
                    if(!isMainFigureFaceX)
                    {
                        tempRatio = (subY - minFlowDistance) / minFlowDistance;
                    }
                    else if(subY < subMinFlowDistance)
                    {
                        tempRatio = (subY - subMinFlowDistance) / minFlowDistance;
                    }
                }
                else
                {
                    tempRatio = (subY - minFlowDistance) / minFlowDistance;
                }
                if(Math.abs(tempRatio) > 0.0001)
                {
                    ratio += tempRatio;
                    abnormalFigures.push(figure);
                    abnormalFigures.push(otherFigure);
                }
            }
            else if (isYMix && subX < minFlowDistance) {
                let tempRatio: number = 0;
                if(mainFigure && (figure == mainFigure || otherFigure == mainFigure))
                {
                    if(isMainFigureFaceX)
                    {
                        tempRatio = (subX - minFlowDistance) / minFlowDistance;
                    }
                    else if(subX < subMinFlowDistance)
                    {
                        tempRatio = (subX - subMinFlowDistance) / minFlowDistance;
                    }
                }
                else
                {
                    tempRatio = (subX - minFlowDistance) / minFlowDistance;
                }
                if(Math.abs(tempRatio) > 0.0001)
                {
                    ratio += tempRatio;
                    abnormalFigures.push(figure);
                    abnormalFigures.push(otherFigure);
                }
            }
        }
    }
    let weight: number = extendParams && "weight" in extendParams ? extendParams.weight : 100;
    let score: number = ratio * weight;
    return { score: score, figures: abnormalFigures };
}

function calFlowBetweenRectsInfo(targetRect: ZRect, rects: ZRect[], extendParams: any = null): any {
    void (targetRect);

    let minFlowDistance: number = extendParams &&  "minFlowDistance" in extendParams ? extendParams.minFlowDistance : 200;
    let ratio: number = 0;
    let abnormalRects: ZRect[] = [];
    for (let rect of rects) {
        for (let otherRect of rects) {
            if (rect == otherRect) {
                continue;
            }
            // 过道计算这个想起来还是蛮复杂的，涉及到物体在物体之内，以及物体部分重叠和物体不重叠之间的衡量
            let figureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect);
            let otherFigureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(otherRect);
            let isXMix: boolean = figureRange.xMin < otherFigureRange.xMax && figureRange.xMax > otherFigureRange.xMin;
            let isYMix: boolean = figureRange.yMin < otherFigureRange.yMax && figureRange.yMax > otherFigureRange.yMin;
            let subX: number = Math.min(Math.abs(figureRange.xMax - otherFigureRange.xMin), Math.abs(otherFigureRange.xMax - figureRange.xMin));
            let subY: number = Math.min(Math.abs(figureRange.yMax - otherFigureRange.yMin), Math.abs(otherFigureRange.yMax - figureRange.yMin));
            if (isXMix && isYMix) {
                // 这种同样也是需要考虑两个方向，最好使用包围盒进行衡量, 两个方向的叠加
                ratio += (-subX - minFlowDistance) / minFlowDistance;
                ratio += (-subY - minFlowDistance) / minFlowDistance;
                abnormalRects.push(rect);
                abnormalRects.push(otherRect);
            }
            else if (isXMix && subY < minFlowDistance) {
                ratio += (subY - minFlowDistance) / minFlowDistance;
                abnormalRects.push(rect);
                abnormalRects.push(otherRect);
            }
            else if (isYMix && subX < minFlowDistance) {
                ratio += (subX - minFlowDistance) / minFlowDistance;
                abnormalRects.push(rect);
                abnormalRects.push(otherRect);
            }
        }
    }
    let weight: number = extendParams && "weight" in extendParams ? extendParams.weight : 100;
    let score: number = ratio * weight;
    return { score: score, rects: abnormalRects };
}

function calOuterBoxScoreInfo(targetRect: ZRect, figures: TFigureElement[], groupFigureData: TLayoutGroupFineTuningData, extendParams: any = null): any {
    let xDir: Vector3 = new Vector3(1, 0, 0);
    let yDir: Vector3 = new Vector3(0, 1, 0);
    let targetRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(targetRect);
    let targetHalfLen: number = (targetRange.xMax - targetRange.xMin) / 2;
    let targetHalfWidth: number = (targetRange.yMax - targetRange.yMin) / 2;
    let targetCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(targetRange);
    let ratio: number = 0;
    let overBoxFigures: TFigureElement[] = [];
    let weight: number = extendParams && "weight" in extendParams ? extendParams.weight : -100;
    // 先确定面积最大的主沙发
    let mainGroupFigure: TFigureElement = null;
    if(groupFigureData)
    {
        mainGroupFigure = groupFigureData.getMainFigure();
    }

    for (let figure of figures) {
        let figureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(figure.matched_rect || figure.rect);
        let rangeHalfLen: number = (figureRange.xMax - figureRange.xMin) / 2;
        let rangeHalfWidth: number = (figureRange.yMax - figureRange.yMin) / 2;
        let rangeCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(figureRange);
        let ProjectLen: number = Math.abs(rangeCenter.clone().sub(targetCenter).dot(xDir)) + rangeHalfLen;
        let ProjectWith: number = Math.abs(rangeCenter.clone().sub(targetCenter).dot(yDir)) + rangeHalfWidth;
        if (ProjectLen <= targetHalfLen && ProjectWith <= targetHalfWidth) {
            continue;
        }
        overBoxFigures.push(figure);
        let subWeight: number = 1;
        if(mainGroupFigure &&figure == mainGroupFigure)
        {
            subWeight = Math.abs(weight);
        }
        if (ProjectLen > targetHalfLen) {
            ratio += (ProjectLen - targetHalfLen) / targetHalfLen * subWeight;
        }
        if (ProjectWith > targetHalfWidth) {
            ratio += (ProjectWith - targetHalfWidth) / targetHalfWidth * subWeight;
        }
    }
    
    let score: number = weight * ratio;
    return { score: score, figures: overBoxFigures };
}

function calOuterBoxScoreInfoByRects(targetRect: ZRect, rects: ZRect[], extendParams: any = null): any {
    let xDir: Vector3 = new Vector3(1, 0, 0);
    let yDir: Vector3 = new Vector3(0, 1, 0);
    let targetRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(targetRect);
    let targetHalfLen: number = (targetRange.xMax - targetRange.xMin) / 2;
    let targetHalfWidth: number = (targetRange.yMax - targetRange.yMin) / 2;
    let targetCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(targetRange);
    let ratio: number = 0;
    let overBoxRects: ZRect[] = [];
    for (let rect of rects) {
        let rectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect);
        let rangeHalfLen: number = (rectRange.xMax - rectRange.xMin) / 2;
        let rangeHalfWidth: number = (rectRange.yMax - rectRange.yMin) / 2;
        let rangeCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(rectRange);
        let ProjectLen: number = Math.abs(rangeCenter.clone().sub(targetCenter).dot(xDir)) + rangeHalfLen;
        let ProjectWith: number = Math.abs(rangeCenter.clone().sub(targetCenter).dot(yDir)) + rangeHalfWidth;
        if (ProjectLen <= targetHalfLen && ProjectWith <= targetHalfWidth) {
            continue;
        }
        overBoxRects.push(rect);
        if (ProjectLen > targetHalfLen) {
            ratio += (ProjectLen - targetHalfLen) / targetHalfLen;
        }
        if (ProjectWith > targetHalfWidth) {
            ratio += (ProjectWith - targetHalfWidth) / targetHalfWidth;
        }
    }
    let weight: number = extendParams && "weight" in extendParams ? extendParams.weight : -100;
    let score: number = weight * ratio;
    return { score: score, rects: overBoxRects };
}

function mainFigureBackCloseTargetRectEdge(targetRect: ZRect, rect: ZRect, groupType: TLayoutGroupFineTuningType)
{
    if(groupType == TLayoutGroupFineTuningType.k_sofaGroupFigure || groupType == TLayoutGroupFineTuningType.k_bedGroupFigure)
    {
        let rectBackEdge: ZEdge = rect.backEdge;
        let closeEdge: ZEdge = null;
        let closeMinDistance: number = Number.POSITIVE_INFINITY;
        for(let edge of targetRect.edges)
        {
            if(Math.abs(edge.dv.clone().dot(rectBackEdge.dv)) > 0.5)
            {
                let distance: number = TBaseRoomToolUtil.instance.calDistance(edge, rectBackEdge);
                if(distance < closeMinDistance)
                {
                    closeMinDistance = distance;
                    closeEdge = edge;
                }
            }   
        }
        let moveVec: Vector3 = closeEdge.center.clone().sub(rectBackEdge.center);
        TLayoutFineTuningOperationToolUtil.instance.moveRect(rect, moveVec);
    }
}

function isSameFigures(figures1: TFigureElement[], figures2: TFigureElement[]): boolean
{
    if(figures1.length != figures2.length)
    {
        return false;
    }
    let isSame: boolean = true;
    for(let figure of figures1)
    {
        let isFind: boolean = false;
        for(let otherFigure of figures2)
        {
            if(figure == otherFigure)
            {
                isFind = true;
                break;
            }
        }
        if(!isFind)
        {
            isSame = false;
        }
    }
    return isSame;
}
