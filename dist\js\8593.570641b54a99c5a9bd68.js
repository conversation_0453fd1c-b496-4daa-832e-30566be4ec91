"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[8593],{10228:function(e,t,n){n.r(t),n.d(t,{default:function(){return Lr}});var r=n(13274),i=n(41594),a=n(46562),o=n(33100),l=n(36134),s=n(61214),u=n(17287),c=n(27347),d=n(88934),f=n(39454),h=n(98147),p=n(4173),m=n(31281),g=n(23825),y=n(40955),b=n(9003);function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var w=m.A.Platform,S=function(e){var t,n,a=e.updateIssueReportVisible,o=e.updateLayoutBatchTestVisible,s=e.updateModelLightConfigVisible,u=_((0,i.useState)("testFigureGroupBtn"),2),f=u[0],m=u[1],v=(0,y.A)().styles,S=(0,b.P)(),k={left:(0,r.jsx)("span",{className:"tabs-extra-left-title",style:{marginLeft:"10px",fontSize:"18px"},children:"训练后台"}),right:(0,r.jsx)(l.A,{type:"dashed",icon:(0,r.jsx)(p.A,{}),style:{marginRight:"10px"},className:"tabs-extra-right-button",onClick:function(){w.Application.closeApp({appId:g.sZ})}})},I=[{key:"LayoutGraphTesting",label:"单空间调试",command_name:"LayoutGraphTesting"},{key:"LayoutTaskTesting",label:"布局任务测试",command_name:"LayoutTaskTesting"},{key:"LayoutSchemeTesting",label:"全屋测试(旧)",command_name:"LayoutSchemeTesting"},{key:"ModelRoomList",label:"布局模板",command_name:"ModelRoomList"},{key:"FigureTemplateTesting",label:"组合模板",command_name:"FigureTemplateTesting"},{key:"LayoutScoreConfig",label:"布局评分配置",command_name:"LayoutScoreConfig"},{key:"ModelLightConfig",label:"灯光模板",command_name:"ModelLightConfig"}];null===(n=S.userStore)||void 0===n||null===(t=n.userInfo)||void 0===t||t.tenantId;var A=_((0,i.useState)(["left","right"]),2),T=A[0],N=(A[1],(0,i.useMemo)((function(){return 0===T.length?null:T.reduce((function(e,t){return j(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){x(e,t,n[t])}))}return e}({},e),x({},t,k[t]))}),{})}),[T])),C=function(e){m(e);var t=I.find((function(t){return t.key===e}));t&&c.nb.RunCommand(t.command_name),a("LayoutIssueReport"===e),o("LayoutBatchTest"===e),s("ModelLightConfig"===e),c.nb.instance.update()};return c.nb.on(d.U.TrainingTabChanged,(function(e){C(e)})),(0,i.useEffect)((function(){c.nb.instance._current_handler&&c.nb.instance._current_handler.enter()}),[]),(0,r.jsx)("div",{className:v.title_bar,children:(0,r.jsx)(h.A,{rootClassName:v.tab_root,size:"middle",centered:!0,defaultActiveKey:f,activeKey:f,tabBarExtraContent:N,items:I,onTabClick:C})})},k=n(45599),I=n(93646),A=n(88880),T=n(15696),N=n(65640);function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function O(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function L(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){O(a,r,i,o,l,"next",e)}function l(e){O(a,r,i,o,l,"throw",e)}o(void 0)}))}}function D(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return C(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var P=[{dataset_name:"基础空间-20",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/houseSchemeListA.json"},{dataset_name:"回归测试集-100",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/houseSchemeListB.json"},{dataset_name:"种子库-厨房",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/kitchenSeeds.json"},{dataset_name:"种子库-厨房(烟道)",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/kitchenFlueSeeds.json"},{dataset_name:"种子库-客餐厅",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/LivingRoomSeeds.json"},{dataset_name:"种子库-卫生间",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/WashroomSeeds.json"},{dataset_name:"种子库-卧室",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/BedroomSeeds.json"},{dataset_name:"种子库-书房",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/StudySeeds.json"},{dataset_name:"种子库-阳台",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/BalconySeeds.json"},{dataset_name:"测试集-卫生间-2000",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/WashroomTesting.json"},{dataset_name:"测试集-客餐厅-2000",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/LivingRoomTesting.json"},{dataset_name:"测试集-厨房(烟道)-2000",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/KitchenTesting.json"}],E=null,F=0,z="",M=(0,T.observer)((function(){var e=(0,I.A)().styles,t=D((0,i.useState)(0),2),n=t[0],a=t[1],o=D((0,i.useState)(z),2),l=o[0],s=o[1],u=D((0,i.useState)(!1),2),f=u[0],h=u[1],p=D((0,i.useState)({}),2),m=p[0],g=p[1],y=(0,i.useRef)(null),b=function(){var e=L((function(e){var t,r,i;return R(this,(function(o){switch(o.label){case 0:return!(E=e)||E.data_list?[3,4]:E.dataset_url||!E._query_by_hx_search?[3,2]:(t=prompt("输入 城市,关键字, 举例: 广州,云湖花城","广州"),[4,A.h.instance.getDatasetByHxSearch(t)]);case 1:return(r=o.sent())&&(E.data_list=r.data_list,r=null),[3,4];case 2:return[4,A.h.instance.getBasicDataset(E.dataset_url)];case 3:(i=o.sent())&&(E.data_list=i.data_list,i=null),o.label=4;case 4:return A.h.instance.loadLocalStorage_DatasetCounters(E),c.nb.DispatchEvent(c.n0.ShowCurrentTestingDataset,E),a(n+1),[2]}}))}));return function(t){return e.apply(this,arguments)}}(),v=function(){var e=L((function(){var e,t,n,r,i,a,o;return R(this,(function(l){switch(l.label){case 0:if(!localStorage)return[3,5];if(!(e=localStorage.getItem("houseSchemeListData")))return[3,5];l.label=1;case 1:return l.trys.push([1,4,,5]),t=JSON.parse(e),n=t.dataset_name,r=t.build_room_id,n&&r?(i=P.find((function(e){return e.dataset_name===n})))?[4,b(i)]:[3,3]:[3,3];case 2:l.sent(),r&&(s(r||""),(a=i.data_list.find((function(e){return e.buildingRoomId===r})))&&c.nb.DispatchEvent(c.n0.OnClickTestingHouseSchemeInfo,a)),l.label=3;case 3:return[3,5];case 4:return o=l.sent(),N.log(o),[3,5];case 5:return[2]}}))}));return function(){return e.apply(this,arguments)}}(),x=function(e,t){s((null==e?void 0:e.buildingRoomId)||""),z=l,c.nb.DispatchEvent(c.n0.OnClickTestingHouseSchemeInfo,e)},j="__houseSchemeList";return c.nb.on_M(d.U.SetStartRunningState,j,(function(e){0==e&&A.h.instance.saveLocalStorage_DatasetCounters(E),h(e)})),c.nb.on_M(d.U.SetHighlightSchemeIds,j,(function(e){if(g(e),e&&E){var t=E.data_list.findIndex((function(e){return e.buildingRoomId===l})),n=E.data_list.findIndex((function(n,r){return!!(e[n.schemeId]&&r>t)}));if(n<0&&(n=E.data_list.findIndex((function(t){return e[t.schemeId]}))),n>=0){var r=E.data_list[n];x(r)}}})),c.nb.on_M(d.U.ShowCurrentHouseSchemeInfo,j,(function(e){e&&(F=E.data_list.findIndex((function(t){return t.buildingRoomId===(null==e?void 0:e.buildingRoomId)})),s((null==e?void 0:e.buildingRoomId)||""),function(){if(localStorage){var e={dataset_name:E.dataset_name,build_room_id:l};localStorage.setItem("houseSchemeListData",JSON.stringify(e))}}(),z=l)})),E||P&&P[0]&&b(P[0]),(0,i.useEffect)((function(){v()}),[]),(0,i.useEffect)((function(){if(y){var e=y.current;e.scrollTo({top:45*(F-(e.clientHeight-45)/45/2),behavior:"smooth"})}}),[l]),(0,r.jsxs)("div",{className:e.houseSchemeList,id:"house_scheme_list_div",children:[(0,r.jsxs)("div",{className:"dataset_title",children:["测试集：",P.length>0&&(0,r.jsx)("select",{onChange:function(e){var t,n=(null==e||null===(t=e.target)||void 0===t?void 0:t.selectedIndex)||0;b(P[n]||P[0])},value:(null==E?void 0:E.dataset_name)||"",children:P.map((function(e,t){return(0,r.jsx)("option",{value:e.dataset_name,children:e.dataset_name},t)}))}),(0,r.jsx)("div",{className:e.startRunningBtn+" "+(f?"isRunning":""),onClick:function(){var e=!f;c.nb.DispatchEvent(c.n0.OnClickStartRuningTesting,e)},children:f?"测试中...":"开始测试"})]}),(0,r.jsx)("div",{ref:y,className:"listDiv",style:{overflow:"auto"},children:E&&E.data_list&&E.data_list.map((function(e,t){return(0,r.jsxs)("div",{className:"".concat(m[e.schemeId]?"highlight":""," ").concat(l===e.buildingRoomId?"active":"","  scheme_row_div"),id:"house_scheme-list-"+t,"data-room_name":e.buildingRoomId,onClick:function(){return x(e)},children:[t+1,":  ",e.buildingRoomId," ",(0,r.jsx)("br",{}),"     ",e.buildingName||""]},t)}))})]})}));function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function U(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return B(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return B(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var J=function(){var e=(0,I.A)().styles,t=U((0,i.useState)(!1),2),n=t[0],a=t[1],o=U((0,i.useState)(!0),2),l=o[0],s=o[1];return c.nb.on(d.U.ShowSchemeTestingLeftPanel,(function(e){a(e)})),c.nb.on_M(d.U.ShowCurrentTestingRoomInfo,"SchemeTestingLeftPanel",(function(e){s(!e)})),(0,i.useEffect)((function(){}),[]),(0,r.jsx)("div",{className:e.leftPanel,id:"SchemeTestingLeftPanel",style:{display:n?"block":"none"},children:n&&(0,r.jsxs)("div",{className:"leftPanel",children:[(0,r.jsx)("div",{style:{marginTop:"60px",borderTopWidth:"1px"},children:(0,r.jsx)(k.A,{width:410,showSchemeName:!0})}),l&&(0,r.jsx)(M,{})]})})},H=n(48402),V=n(33421);function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||K(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e){return function(e){if(Array.isArray(e))return G(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||K(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function K(e,t){if(e){if("string"==typeof e)return G(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?G(e,t):void 0}}var X=(0,T.observer)((function(e){var t=e.visible,n=(0,I.A)().styles,a=W((0,i.useState)(null),2),o=a[0],l=a[1],s=W((0,i.useState)([]),2),u=s[0],f=s[1],h=W((0,i.useState)(null),2),p=h[0],m=h[1],g=W((0,i.useState)(0),2),y=g[0],b=g[1],v=[],x=[],j="houseSchemeTestingPanel";c.nb.on_M(d.U.ShowCurrentHouseSchemeInfo,j,(function(e){l(e)})),c.nb.on_M(d.U.ShowCurrentTestingRoomInfo,j,(function(e){m(e)})),(0,i.useEffect)((function(){}),[]);var _=function(e){if(x=[],o&&(null==o?void 0:o.room_testing_info_list)){var t=!0,n=!1,r=void 0;try{for(var i,a=o.room_testing_info_list[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){var l=i.value;x.push({image_path:"https://test-3vj-pano.oss-cn-shenzhen-internal.aliyuncs.com//vr/layout/def.jpg?x-oss-process=image/resize,m_fixed,h_218,w_360",title:l.roomname+"-"+((null==l?void 0:l.uid)||""),centerTitle:"布局数量 ".concat(l.candidate_num||0," ").concat((null==l?void 0:l.solved)||""," "),bottomTitle:"".concat((null==l?void 0:l.transfer_from_scheme_room_id)||"")})}}catch(e){n=!0,r=e}finally{try{t||null==a.return||a.return()}finally{if(n)throw r}}}if(p){v=[];var s=$(p.room._furniture_list);s.sort((function(e,t){return t.default_drawing_order-e.default_drawing_order})),s.forEach((function(e){var t;(c.nb.IsDebug||!e._is_decoration&&!e._is_sub_board)&&v.push({image_path:(null===(t=H.eW[e.sub_category])||void 0===t?void 0:t.img_path)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",title:e.modelLoc+" | "+e.sub_category,centerTitle:"".concat(Math.round(e.length),"*").concat(Math.round(e.depth),"*").concat(Math.round(e.height)),bottomTitle:"",figure_element:e})}))}else v=[];f([{label:"房间",figureList:x,init_expanded:e},{label:"图例",figureList:v,init_expanded:!e}])};return(0,i.useEffect)((function(){_(!0)}),[o]),(0,i.useEffect)((function(){_(!p)}),[p]),(0,r.jsxs)("div",{className:n.houseSchemeTestingPanel,id:"houseSchemeTestingPanel",style:{display:t?"block":"none"},children:[(0,r.jsxs)("div",{className:"content",children:[(0,r.jsxs)("div",{className:"info_text",onClick:function(){var e;e=document.createElement("a"),document.body.appendChild(e),e.href="/Home?importType=importHouse",e.target="_blank",e.click(),document.body.removeChild(e)},children:[(0,r.jsx)("div",{children:"户型ID:"}),(0,r.jsx)("div",{children:null==o?void 0:o.buildingRoomId})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"方案ID:"}),(0,r.jsx)("div",{children:null==o?void 0:o.schemeId})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"户型名称:"}),(0,r.jsx)("div",{children:null==o?void 0:o.buildingName})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"空间分布:"}),(0,r.jsx)("div",{children:null==o?void 0:o.buildingRoomTypeName})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"面积:"}),(0,r.jsxs)("div",{children:[null==o?void 0:o.area,"㎡"]})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"标记信息:"}),(0,r.jsxs)("select",{value:(null==o?void 0:o.scheme_label)||"default",onChange:function(e){o.scheme_label=e.target.value,c.nb.DispatchEvent(c.n0.OnChangeTestingHouseSchemeLabel,o),b(y+1)},children:[(0,r.jsx)("option",{value:"default",children:"正常"}),(0,r.jsx)("option",{value:"room_error",children:"户型错误"}),(0,r.jsx)("option",{value:"layout_error",children:"布局异常"})]})]})]}),(0,r.jsx)("div",{style:{overflowY:"auto"},children:(0,r.jsx)(V.A,{menuList:u,showLayoutList:!0})})]})})),q=n(28888),Z=n(41980),Q=n(1870);function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ee(e,t,n[t])}))}return e}function ne(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Y(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var re=(0,T.observer)((function(e){var t,n=e.visible,a=(0,I.A)().styles,o=ne((0,i.useState)(null),2),l=o[0],s=o[1],u=ne((0,i.useState)(0),2),f=u[0],h=u[1],p=ne((0,i.useState)({}),2),m=p[0],g=p[1],y="datasetTestingPanel";c.nb.on_M(d.U.SetCurrentDataSet,y,(function(e){s(e)})),c.nb.on_M(d.U.UpdateDatasetInfo,y,(function(){h(f+1)}));var b=["客餐厅","卫生间","厨房","卧室","阳台"];(0,i.useEffect)((function(){var e=!0,t=!1,n=void 0;try{for(var r,i=b[Symbol.iterator]();!(e=(r=i.next()).done);e=!0){var a=r.value;m[a]=!0}}catch(e){t=!0,n=e}finally{try{e||null==i.return||i.return()}finally{if(t)throw n}}}),[]),(0,i.useEffect)((function(){}),[m]);var v=[{text:"空",name:"None"},{text:"相似",name:"Transfer"},{text:"自相似",name:"Self-Transfer"},{text:"逻辑",name:"Logic"}];return(0,r.jsx)("div",{className:a.houseSchemeTestingPanel,id:y,style:{display:n?"block":"none"},children:(0,r.jsxs)("div",{className:"content",children:[(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"数据集:"}),(0,r.jsx)("div",{children:null==l?void 0:l.dataset_name})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"户型数量:"}),(0,r.jsx)("div",{children:(null==l||null===(t=l.data_list)||void 0===t?void 0:t.length)||0})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"计算时间:"}),(0,r.jsx)("div",{children:(null==l?void 0:l._start_running_time)?((null==l?void 0:l._end_running_time)-(null==l?void 0:l._start_running_time))/1e3+"s":"---"})]}),(0,r.jsx)("div",{className:"info_text",children:(0,r.jsx)("button",{onClick:function(){if(l){var e=te({},l);e.data_list=l.data_list.map((function(e){var t=te({},e);return t.room_testing_info_list&&(t.room_testing_info_list=t.room_testing_info_list.map((function(e){var t=te({},e);return delete t.room,t}))),t})),(0,Q.c6)(JSON.stringify(e),"datatest.json")}},children:"导出结果"})}),(null==l?void 0:l.rooms_counter)&&b.map((function(e,t){var n;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"info_text",onClick:function(){return m[t=e]=!m[t],void g(te({},m));var t},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(Z.In,{iconClass:"".concat(m[e]?"iconcaretdown":"iconcaretright"),className:"icon",size:14}),e]}),(0,r.jsx)("div",{children:(null===(n=l.rooms_counter[e])||void 0===n?void 0:n.computed_count)||0})]}),v.map((function(n,i){var a,o;return(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"info_text",style:{display:m[e]?"flex":"none"},onClick:function(){var t;!function(e){if(null==e?void 0:e.scheme_ids){var t={},n=!0,r=!1,i=void 0;try{for(var a,o=e.scheme_ids[Symbol.iterator]();!(n=(a=o.next()).done);n=!0)t[a.value]=!0}catch(e){r=!0,i=e}finally{try{n||null==o.return||o.return()}finally{if(r)throw i}}c.nb.emit_M(d.U.SetHighlightSchemeIds,t)}else c.nb.emit_M(d.U.SetHighlightSchemeIds,{})}(null===(t=l.rooms_counter[e])||void 0===t?void 0:t.result_count[n.name])},children:[(0,r.jsx)("div",{className:"sub_info_text",children:n.text}),(0,r.jsx)("div",{children:(null===(o=l.rooms_counter[e])||void 0===o||null===(a=o.result_count[n.name])||void 0===a?void 0:a.text)||0})]})},y+"info_text_"+t+"_"+i)}))]},y+"info_list_"+t)}))]})})}));function ie(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ae(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ie(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ie(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var oe=function(){var e=(0,I.A)().styles,t=ae((0,i.useState)(!1),2),n=t[0],a=t[1],o=ae((0,i.useState)(null),2),l=o[0],s=o[1],u=ae((0,i.useState)("户型信息"),2),f=u[0],h=u[1];return c.nb.on(d.U.ShowSchemeTestingRightPanel,(function(e){a(e)})),c.nb.on_M(d.U.ComputingProgress,"SchemeTestingRightPanel",(function(e){s(e)})),(0,i.useEffect)((function(){}),[]),(0,r.jsxs)(r.Fragment,{children:[l&&(0,r.jsx)("div",{className:e.progressInfo,children:(0,r.jsx)(q.A,{title:l,color:"#000000"})}),(0,r.jsxs)("div",{className:e.rightPanel,id:"SchemeTestingRightPanel",style:{display:n?"block":"none"},children:[(0,r.jsxs)("div",{className:"title",children:[(0,r.jsx)("span",{className:"户型信息"==f?"active":"",onClick:function(){return h("户型信息")},children:"户型信息"}),"  |",(0,r.jsx)("span",{className:"测试汇总"==f?"active":"",onClick:function(){return h("测试汇总")},children:"测试汇总"})]}),(0,r.jsx)(re,{visible:"测试汇总"==f}),(0,r.jsx)(X,{visible:"户型信息"==f})]})]})},le=n(63436),se=n(79874);function ue(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function ce(){var e=ue([""]);return ce=function(){return e},e}function de(){var e=ue(["\n      position :absolute;\n      left:0;\n      top:0;\n      width: 320px;\n      height:100%;\n      z-index:5;\n      background:#fff;\n      padding-left: 15px;\n      .row_ele {\n        width:100%;\n        height:50px;\n        line-height:22px;\n        border-bottom:1px dashed #adf;\n        cursor:pointer;\n        user-select:text;\n        font-size:13px;\n         &:hover {\n          background :#efefef;\n         }\n         &.marked {\n            background :#ffefef;\n\n         }\n         &.checked {\n          background :#aaaaef;\n        }\n         select {\n          margin-left:10px;\n         }\n         input {\n            width:40px;\n            height:25px;\n            line-height:25px;\n            margin-left:10px;\n         }\n         .row_input {\n            margin-right:10px;\n         }\n         button {\n            height:25px;\n            line-height:15px;\n            border-radius :4px;\n            margin-left:5px;\n         }\n         .right_text {\n            float:right;\n            margin-right:5px;\n         }\n      }\n    "]);return de=function(){return e},e}function fe(){var e=ue(["\n      position :absolute;\n      right:0;\n      top:0;\n      width: 300px;\n      height:100%;\n      z-index:5;\n      background:#fff;\n      .Ctrlbtns {\n         position:absolute;\n         bottom:10px;\n         left:0;\n         width:100%;\n         text-align:center;\n         .btn_row {\n            line-height:30px;\n            margin-top:5px;\n         }\n      }\n      .tabs {\n         width:100%;\n         height:30px;\n         font-size: 18px;\n         line-height: 30px;\n         color:#aaa;\n         .tab {\n             display:inline-block;\n             padding:5px;\n             padding-left:10px;\n             padding-right:10px;\n             cursor:pointer;\n             &:hover {\n               color:#333333;\n             }\n             &.checked {\n               color:#333333;\n             }\n\n         }\n      }\n      .row_ele {\n         width:100%;\n         height:50px;\n         line-height:22px;\n         border-bottom:1px dashed #adf;\n         cursor:pointer;\n         user-select:text;\n         font-size:13px;\n          &:hover {\n           background :#efefef;\n          }\n          &.checked {\n           background :#aaaaef;\n         }\n          select {\n           margin-left:10px;\n          }\n          input {\n             width:40px;\n             height:25px;\n             line-height:25px;\n             margin-left:10px;\n          }\n          .row_input {\n             margin-right:10px;\n          }\n          button {\n             height:25px;\n             line-height:15px;\n             border-radius :4px;\n             margin-left:5px;\n          }\n          .right_text {\n             float:right;\n             margin-right:5px;\n          }\n       }\n    "]);return fe=function(){return e},e}function he(){var e=ue(["\n      float:left;\n      margin-left:20px;\n      margin-right:20px;\n      margin-top:20px;\n      padding:5px;\n      width: 300px;\n      height : calc(100% - 80px);\n      .report_row {\n         width:100%;\n         height:30px;\n         line-height:30px;\n         user-select:text;\n         font-size:14px;\n         &.checked {\n            background :#aaaaef;\n         }\n      }\n    "]);return he=function(){return e},e}function pe(){var e=ue(["\n         float:left;\n         margin-left:20px;\n         margin-right:20px;\n         margin-top:20px;\n         padding:5px;\n         width: 300px;\n         height : calc(100% - 40px);\n         border-radius : 10px;\n         background : #ededf9;\n         &.compared_report {\n            background:#f9edf7;\n            margin-left:5px;\n            // margin-right:0px;\n         }\n         &.right_report {\n            margin:0;\n            background:#fff;\n         }\n         .report_row {\n            width:100%;\n            height:30px;\n            line-height:30px;\n            cursor:pointer;\n            user-select:text;\n            font-size:16px;\n            &.checked {\n               background :#aaaaef;\n            }\n         }\n         "]);return pe=function(){return e},e}function me(){var e=ue(["\n         width:200px;\n         margin:5px auto;\n         background:#efefef;\n         border-radius:5px;\n         overflow:hidden;\n         color:#aaa;\n         font-size:14px;\n         .tab {\n            float:left;\n            padding:5px;\n            width:100px;\n            text-align:center;\n            cursor:pointer;\n            &.checked {\n               color:#fff;\n               background:#7777ff;\n            }\n         }\n      "]);return me=function(){return e},e}function ge(){var e=ue(["\n      position: absolute;\n      z-index:11;\n      left:340px;\n      min-width:120px;\n      top:5px;\n      background:#efefef;\n      border-radius:5px;\n      overflow:hidden;\n      color:#aaa;\n      .tab {\n         float:left;\n         padding:5px;\n         width:60px;\n         text-align:center;\n         cursor:pointer;\n         &.checked {\n            color:#fff;\n            background:#7777ff;\n         }\n      }\n\n    "]);return ge=function(){return e},e}function ye(){var e=ue(["\n         position : absolute;\n         right : 300px;\n         top: 0px;\n         z-index:11;\n\n      "]);return ye=function(){return e},e}function be(){var e=ue(["\n         position: absolute;\n         width: calc(100% - 60px);\n         height: 140px;\n         background: #fff;\n         left: 40px;\n         top: 20px;\n         border: 1px solid #eee;\n         padding: 10px;\n         box-shadow: 0 2px 24px rgba(0, 0, 0, .5);\n         border-radius: 5px;\n         .input_row {\n            width:100%;\n            height:30px;\n            margin-bottom:10px;\n            span {\n               cursor: pointer;\n            }\n\n       .methods_name {\n          padding: 10px;\n          text-align:center;\n          color : #aaa;\n          &:hover {\n            color : #aaaaff;\n\n          }  \n          &.checked {\n             color : #3333ff;\n          }\n       }\n    }\n    "]);return be=function(){return e},e}function ve(){var e=ue(["\n         position:absolute;\n         top:10px;\n         left:350px;\n         width: 1000px;\n         height : calc(100vh - 80px);\n         border-radius:10px;\n         border:1px solid #333; \n         background :#fff;\n         z-index:50;\n         .close_btn {\n            position:absolute;\n            right:10px;\n            top:10px;\n            cursor:pointer;\n            &:hover {\n               color:#0000ff;\n            }\n         }\n    "]);return ve=function(){return e},e}var xe=(0,se.rU)((function(e){var t=e.css;return{root:t(ce()),leftPanel:t(de()),rightPanel:t(fe()),resultInfo:t(he()),dataReport:t(pe()),schemeListModeTabs:t(me()),drawRoomModeTabs:t(ge()),rightToolBtns:t(ye()),dialogInputs:t(be()),test_report:t(ve())}})),je=n(64186),_e=n(69391),we=n(95391),Se=n(86417),ke=n(27143),Ie=n(41443),Ae=n(5640);function Te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ne(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function Ce(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){Ne(a,r,i,o,l,"next",e)}function l(e){Ne(a,r,i,o,l,"throw",e)}o(void 0)}))}}function Oe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Le(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||Re(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function De(e){return function(e){if(Array.isArray(e))return Te(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Re(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Re(e,t){if(e){if("string"==typeof e)return Te(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Te(e,t):void 0}}function Pe(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}function Ee(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}var Fe=!1,ze=!1,Me=60,Be=(0,T.observer)((function(){var e=xe().styles,t=Le((0,i.useState)(Fe),2),n=t[0],a=t[1],o=Le((0,i.useState)({room_name:"客餐厅",room_id:0,roomNum:1}),2),s=o[0],u=o[1],d=Le((0,i.useState)(!0),2),f=(d[0],d[1],Le((0,i.useState)(!1),2)),h=f[0],p=f[1],m=Le((0,i.useState)([]),2),g=m[0],y=m[1],b=Le((0,i.useState)(""),2),v=b[0],x=b[1],j=Le((0,i.useState)([{id:"Default",name:"默认数据集"}]),2),_=j[0],w=j[1],S=Le((0,i.useState)("Default"),2),k=S[0],I=S[1],T=Le((0,i.useState)(0),2),N=T[0],C=T[1],O=Le((0,i.useState)([{name:"广州市",code:"440100"}]),2),L=O[0],D=O[1],R=Le((0,i.useState)(!0),2),P=R[0],E=R[1],F=(0,i.useRef)(null),z=(0,i.useRef)(null),M="lastBuildingId",B="lastDatasetId",U=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e=e||k;var t=_.find((function(t){return t.id===e}));return(null==t?void 0:t.name)||"默认数据集"},J=function(){var e=Ce((function(e){var t,n,r,i,a,o,l,d,f,h,p=arguments;return Pe(this,(function(m){switch(m.label){case 0:return t=p.length>1&&void 0!==p[1]?p[1]:{},r=c.nb.instance.layout_container,(0,je.Ri)("authCode"),i=t.room_name||s.room_name,a=void 0===t.id?s.room_id:t.id,o=1,[4,we.n.instance.getDataById(e,we.n.BuildingSchemeDataTable)];case 1:return(d=null===(n=m.sent())||void 0===n?void 0:n.houseInfo)?[3,3]:[4,A.h.instance.makeHouseTestingInfoDataByBuildingId(e)];case 2:d=m.sent(),m.label=3;case 3:return(l=d)&&l.schemeXmlJson?[4,we.n.instance.addData({id:e,houseInfo:l},we.n.BuildingSchemeDataTable)]:[3,6];case 4:return m.sent(),[4,we.n.instance.addData({id:M,buildingId:e,roomInfo:{room_name:i,room_id:a,roomNum:o}},we.n.DefaultTable)];case 5:m.sent(),r.fromXmlSchemeData(l.schemeXmlJson,!0,Ie.N.LayoutLibrary),x(e),f=r._rooms.filter((function(e){return e.roomname.includes(i)})),o=f.length,(h=f[a]||f[0]||null)&&localStorage&&localStorage.setItem("layout_ai_training_current_room_data",JSON.stringify(h.exportExtRoomData())),(null==t?void 0:t.no_auto_layout)||c.nb.DispatchEvent(ke.c.TestingDatasetListOnRoomLoaded,!0),c.nb.instance.update(),m.label=6;case 6:return u({room_name:i,room_id:a,roomNum:o}),[2]}}))}));return function(t){return e.apply(this,arguments)}}(),H=function(e){F&&F.current&&(F.current.innerHTML=e)},V=function(){var e=Ce((function(){var e,t,n,r,i,a,o,l,s,u,c,d;return Pe(this,(function(f){switch(f.label){case 0:if(ze)return[2];ze=!0,e=De(g),t=e.length,n=t,r=Me/20,i=["","保","绿","新","恒大","城投","万科","碧桂","区","花"],a=!0,o=!1,l=void 0,f.label=1;case 1:f.trys.push([1,6,7,8]),s=function(){var t,a,o,l,s;return Pe(this,(function(u){switch(u.label){case 0:t=c.value,a=0,o=1,u.label=1;case 1:if(!(o<=r))return[3,10];if(e.length>5e3)return[3,10];H("   第".concat(o,"次请求 ")+t.name+", 数量"+a),l=1,u.label=2;case 2:return l<=4?[4,_e.Q.search(i[o-1]||"",t.code,20,l)]:[3,8];case 3:return s=u.sent(),((null==s?void 0:s.records)||[]).forEach((function(t){e.find((function(e){return e.buildingRoomId===t.id}))||(t.buildingRoomId=t.id,e.push(t),a++)})),H("   第".concat(o,"次请求 ")+t.name+", 数量"+a),e.length>n?[4,(0,Q.IP)(200)]:[3,6];case 4:return u.sent(),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:e})];case 5:u.sent(),n=e.length,y(De(e)),u.label=6;case 6:if(a>=Me)return[3,8];u.label=7;case 7:return l++,[3,2];case 8:if(a>=Me)return[3,10];u.label=9;case 9:return o++,[3,1];case 10:return[2]}}))},u=L[Symbol.iterator](),f.label=2;case 2:return(a=(c=u.next()).done)?[3,5]:[5,Ee(s())];case 3:f.sent(),f.label=4;case 4:return a=!0,[3,2];case 5:return[3,8];case 6:return d=f.sent(),o=!0,l=d,[3,8];case 7:try{a||null==u.return||u.return()}finally{if(o)throw l}return[7];case 8:return H(""),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:e})];case 9:return f.sent(),y(De(e)),confirm("新增户型"+(e.length-t)+"个"),ze=!1,[2]}}))}));return function(){return e.apply(this,arguments)}}(),G=function(){var e=Ce((function(){var e,t,n,r,i,a,o,l;return Pe(this,(function(s){switch(s.label){case 0:e=0,t=!0,n=!1,r=void 0,s.label=1;case 1:s.trys.push([1,6,7,8]),i=g[Symbol.iterator](),s.label=2;case 2:return(t=(a=i.next()).done)?[3,5]:(o=a.value,H("缓存"+e+"/"+g.length+": "+o.buildingRoomId+" "+o.buildingName),[4,J(o.buildingRoomId,{no_auto_layout:!0})]);case 3:s.sent(),e++,s.label=4;case 4:return t=!0,[3,2];case 5:return[3,8];case 6:return l=s.sent(),n=!0,r=l,[3,8];case 7:try{t||null==i.return||i.return()}finally{if(n)throw r}return[7];case 8:return H(""),[2]}}))}));return function(){return e.apply(this,arguments)}}(),W=function(){var e=Ce((function(){var e;return Pe(this,(function(t){switch(t.label){case 0:return[4,we.n.instance.getAll(we.n.BuildingSchemeDataTable)];case 1:return e=t.sent(),(0,Ae.c6)(JSON.stringify(e),"buildingData.json","text/json"),[2]}}))}));return function(){return e.apply(this,arguments)}}(),$=function(){var e=Ce((function(){var e,t,n,r,i,a,o,l,s;return Pe(this,(function(u){switch(u.label){case 0:return t=JSON.parse,[4,(0,Ae.L7)(".json","Text")];case 1:if(e=t.apply(JSON,[u.sent().content]),c=e,null!=(d=Array)&&"undefined"!=typeof Symbol&&d[Symbol.hasInstance]?d[Symbol.hasInstance](c):c instanceof d){n=e,r=!0,i=!1,a=void 0;try{for(o=n[Symbol.iterator]();!(r=(l=o.next()).done);r=!0)(s=l.value).houseInfo&&s.id&&we.n.instance.addData(s,we.n.BuildingSchemeDataTable)}catch(e){i=!0,a=e}finally{try{r||null==o.return||o.return()}finally{if(i)throw a}}}return[2]}var c,d}))}));return function(){return e.apply(this,arguments)}}(),K=function(){var e=Ce((function(){var e,t;return Pe(this,(function(n){switch(n.label){case 0:return e=(0,Q.AU)(),(t=prompt("数据集名称",e))?(z&&z.current&&(z.current.value=t),[4,we.n.instance.addTestingDataset({id:e,name:t})]):[2];case 1:return n.sent(),[4,Y()];case 2:return n.sent(),[4,Z(e)];case 3:return n.sent(),[2]}}))}));return function(){return e.apply(this,arguments)}}(),X=function(){var e=Ce((function(){var e;return Pe(this,(function(t){switch(t.label){case 0:return"Default"===k?(alert("默认数据集无法删除"),[2]):confirm("确认删除数据集"+k+" "+U()+"?")?[4,we.n.instance.removeTestingDataset(k)]:[2];case 1:return t.sent(),[4,Y()];case 2:return e=t.sent(),[4,Z(e)];case 3:return t.sent(),[2]}}))}));return function(){return e.apply(this,arguments)}}(),q=function(){var e=Ce((function(e){return Pe(this,(function(t){switch(t.label){case 0:return I(e),[4,we.n.instance.addData({id:B,dataset_id:e},we.n.DefaultTable)];case 1:return t.sent(),[2]}}))}));return function(t){return e.apply(this,arguments)}}(),Z=function(){var e=Ce((function(e){var t,n;return Pe(this,(function(r){switch(r.label){case 0:return[4,we.n.instance.getTestingDatasetById(e)];case 1:return(n=(null===(t=r.sent())||void 0===t?void 0:t.buildingList)||[])&&(y(n),q(e)),[2]}}))}));return function(t){return e.apply(this,arguments)}}(),Y=function(){var e=Ce((function(){var e,t,n;return Pe(this,(function(r){switch(r.label){case 0:return[4,we.n.instance.getTestingDatasetList()];case 1:return e=r.sent(),w(e),t=k,!e.find((function(e){return e.id==k}))&&e.length>0&&(n=e[0].id,I(n)),[2,t]}}))}));return function(){return e.apply(this,arguments)}}(),ee=function(){var e=Ce((function(){var e,t,n;return Pe(this,(function(r){switch(r.label){case 0:return e=prompt("请输出初始id:","0"),t=parseInt(e),isNaN(t)?[2]:t>=g.length?[2]:(t<0&&(t=0),(n=De(g)).splice(t,g.length),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:n})]);case 1:return r.sent(),y(n),[2]}}))}));return function(){return e.apply(this,arguments)}}(),te=function(){var e=Ce((function(){var e,t,n,r,i;return Pe(this,(function(a){switch(a.label){case 0:return(e=window.prompt("请输入户型ID",""))&&e.length>0?g.find((function(t){return t.buildingRoomId===e}))?(J(e),[3,6]):[3,1]:[3,6];case 1:return[4,J(e)];case 2:return a.sent(),[4,we.n.instance.getDataById(e,we.n.BuildingSchemeDataTable)];case 3:return(n=null===(t=a.sent())||void 0===t?void 0:t.houseInfo)?(r=De(g),i=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Oe(e,t,n[t])}))}return e}({},n),i.schemeXmlJson&&delete i.schemeXmlJson,r.push(i),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:r})]):[3,5];case 4:return a.sent(),y(r),alert("添加成功"),[3,6];case 5:alert("添加户型失败"),a.label=6;case 6:return[2]}}))}));return function(){return e.apply(this,arguments)}}();(0,i.useEffect)((function(){c.nb.on_M(ke.I.ShowTestingDatasetListPanel,"TestingDatasetListPanel",(function(e){a(Fe=!Fe);var t=function(){var e=Ce((function(){var e,t,n;return Pe(this,(function(r){switch(r.label){case 0:return[4,we.n.instance.getDataById(M,we.n.BuildingSchemeDataTable)];case 1:return(e=r.sent()||null)&&((t=e.buildingId)&&x(t),(n=e.roomInfo)&&u(n)),[2]}}))}));return function(){return e.apply(this,arguments)}}(),n=function(){var e=Ce((function(){var e,t;return Pe(this,(function(n){switch(n.label){case 0:return[4,Y()];case 1:return n.sent(),[4,we.n.instance.getDataById(B,we.n.DefaultTable)];case 2:return t=(null===(e=n.sent())||void 0===e?void 0:e.dataset_id)||null,[4,Z(t=t||k)];case 3:return n.sent(),[2]}}))}));return function(){return e.apply(this,arguments)}}();n(),t()}))}),[]);for(var ne=[],re=0;re<(s.roomNum||1);re++)ne.push(re);var ie=1e3,ae=Math.floor(g.length/ie)+1,oe=N;oe>=ae&&(oe=ae-1);for(var le=[],se=[],ue=0;ue<ie;ue++){var ce=g[oe*ie+ue];ce&&le.push(ce)}for(var de=0;de<ae;de++)se.push(de);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{id:"quickRoomTestingPanel",className:e.leftPanel,style:{display:n?"block":"none",position:"absolute",left:"0",top:"0",zIndex:10001,paddingLeft:"0"},children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"90px",paddingTop:"10px",lineHeight:"25px"},children:[(0,r.jsx)("select",{name:"dataset_list",onChange:function(e){Z(e.target.value)},defaultValue:k,children:_.map((function(e,t){return(0,r.jsx)("option",{value:e.id,children:e.name},"dataset_"+t)}))}),(0,r.jsx)(l.A,{onClick:function(){p(!0)},children:"编辑数据集"}),(0,r.jsx)(l.A,{onClick:function(){c.nb.emit_M(ke.I.ShowTestingDatasetListPanel,!1)},children:"关闭"}),(0,r.jsx)("br",{}),(0,r.jsx)("select",{name:"pageSelect",onChange:function(e){C(~~e.target.value)},children:se.map((function(e){return(0,r.jsxs)("option",{value:e,children:["第",e+1,"页"]},"pageSelect"+e)}))}),(0,r.jsx)("select",{name:"roomName",value:s.room_name,onChange:function(){var e=Ce((function(e){return Pe(this,(function(t){return J(v,{room_name:e.target.value}),[2]}))}));return function(t){return e.apply(this,arguments)}}(),children:["客餐厅","卫生间","厨房","卧室","入户花园"].map((function(e,t){return(0,r.jsx)("option",{value:e,children:e},"room_name"+t)}))}),(0,r.jsx)("select",{name:"roomId",value:s.room_id,onChange:function(){var e=Ce((function(e){return Pe(this,(function(t){return J(v,{id:~~e.target.value}),[2]}))}));return function(t){return e.apply(this,arguments)}}(),children:ne.map((function(e,t){return(0,r.jsxs)("option",{value:e,children:[e,":",s.roomNum-1]},"room_id_"+t)}))})]}),(0,r.jsx)("div",{style:{overflow:"auto",height:"calc(100vh - 150px)"},children:le.map((function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+(e.buildingRoomId===v?"checked":""),onClick:function(){return J(e.buildingRoomId)},children:[N*ie+t+1,":",e.buildingRoomId,"    ",e.cityName,(0,r.jsx)("br",{}),"     ",e.buildingName||"","    ",e.area?e.area+"m²":""]},"buildId_"+t)}))})]}),h&&(0,r.jsxs)("div",{className:"DistrictPopUp",style:{position:"fixed",zIndex:10001,width:"900px",top:"0px",left:"300px"},children:[(0,r.jsx)("div",{className:"closeBtn",style:{position:"absolute",top:"40px",right:"30px",fontSize:"16px",cursor:"pointer",zIndex:10001},onClick:function(){return p(!1)},children:"X"}),(0,r.jsxs)("div",{className:e.dialogInputs,children:[(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 测试集名称: "}),(0,r.jsx)("input",{ref:z,defaultValue:U(),onChange:function(e){!function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t=t||k;var n=_.find((function(e){return e.id===t}));n&&(n.name=e,w(De(_)))}(e.target.value)}}),(0,r.jsx)("span",{children:" 当前总数 "}),(0,r.jsxs)("span",{children:[" ",g.length]}),"    ",(0,r.jsx)(l.A,{onClick:function(){K()},children:" 新建数据集 "}),(0,r.jsx)(l.A,{onClick:function(){X()},children:"删除数据集"}),(0,r.jsx)("span",{ref:F})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)(l.A,{onClick:te,children:"手动添加"}),(0,r.jsx)(l.A,{onClick:function(){confirm("开始执行自动添加?")&&V()},children:" 自动添加 "}),(0,r.jsx)(l.A,{onClick:function(){ee()},children:" 清空列表 "}),(0,r.jsx)(l.A,{onClick:function(){we.n.instance.exportAllTestingDataset()},children:" 导出数据集 "}),(0,r.jsx)(l.A,{onClick:Ce((function(){var e;return Pe(this,(function(t){switch(t.label){case 0:return[4,(0,Ae.L7)(".json","Text")];case 1:e=t.sent().content,t.label=2;case 2:return t.trys.push([2,5,,6]),[4,we.n.instance.importTestingDataset(JSON.parse(e))];case 3:return t.sent(),[4,Z(k)];case 4:return t.sent(),[3,6];case 5:return t.sent(),[3,6];case 6:return[2]}}))})),children:" 导入数据集 "}),(0,r.jsx)(l.A,{onClick:function(){G()},children:" 缓存数据 "}),(0,r.jsx)(l.A,{onClick:function(){W()},children:" 导出户型缓存 "}),(0,r.jsx)(l.A,{onClick:function(){$()},children:" 导入户型缓存 "})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 目标城市 "}),(0,r.jsx)("span",{children:L.map((function(e,t){return(0,r.jsxs)("span",{onClick:function(){var t=De(L).filter((function(t){return t.code!==e.code}));D(t)},children:[" ",e.name," "]},"city_code_name_"+t)}))}),"      ",(0,r.jsx)("input",{type:"number",step:20,min:20,max:500,defaultValue:Me,onChange:function(e){Me=~~e.target.value}}),"请求/每城市",(0,r.jsx)("span",{style:{float:"right",marginRight:20,color:"#07f"},onClick:function(){E(!P)},children:P?"收起列表":"展开列表"})]})]}),(0,r.jsx)(Se.Z,{is_visible:h&&P,onSelected:function(e){var t=De(L);t.find((function(t){return t.code===e.code}))||(t.push(e),D(t))}})]})]})})),Ue=n(41140),Je=(n(74376),n(65640));function He(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ve(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function Ge(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){Ve(a,r,i,o,l,"next",e)}function l(e){Ve(a,r,i,o,l,"throw",e)}o(void 0)}))}}function We(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $e(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){We(e,t,n[t])}))}return e}function Ke(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return He(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return He(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xe(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var qe=function(){var e=[],t=(0,I.A)().styles,n=Ke((0,i.useState)(!1),2),a=(n[0],n[1],Ke((0,i.useState)(null),2)),o=a[0],l=a[1],s=Ke((0,i.useState)(e),2),u=s[0],f=s[1],h=Ke((0,i.useState)(null),2),p=h[0],m=h[1],g=Ke((0,i.useState)({pageIndex:1,pageSize:50,platList:[0,1]}),2),y=g[0],b=g[1],v=function(){var e=Ge((function(e){var t,n,r,i;return Xe(this,(function(a){switch(a.label){case 0:for(var o in n=$e({},e))void 0===n[o]&&delete n[o];return[4,(0,je.Ap)({method:"post",url:"/api/njvr/RoomLayoutTemplate/page",data:n,timeout:3e3}).catch((function(e){return null}))];case 1:return(null==(r=a.sent())||null===(t=r.result)||void 0===t?void 0:t.result)&&0!=r.result.result.length?((i=r.result.result)&&i.sort((function(e,t){return t.updateDate.localeCompare(e.updateDate)})),[2,i]):[2,[]]}}))}));return function(t){return e.apply(this,arguments)}}(),x=function(){var e=Ge((function(e){var t,n,r;return Xe(this,(function(i){if(t=c.nb.instance.layout_container,e.templateJson)try{null,(n=JSON.parse(e.templateJson))&&(r=c.nb.instance.Configs.saving_localstorage_layout_scheme,c.nb.instance.Configs.saving_localstorage_layout_scheme=!1,t.loadRoomEntityFromJson(n),c.nb.instance.Configs.saving_localstorage_layout_scheme=r)}catch(e){}return m(e),[2]}))}));return function(t){return e.apply(this,arguments)}}(),j=function(){var e=Ge((function(e){var t,n;return Xe(this,(function(r){switch(r.label){case 0:return e.id?[4,(0,je.Ap)({method:"post",url:"/api/njvr/RoomLayoutTemplate/delete",data:{ids:[e.id]},timeout:3e3}).catch((function(e){return null}))]:[2];case 1:return t=r.sent(),Je.log(t),m(null),[4,v(y)];case 2:return n=r.sent(),f(n||[]),[2]}}))}));return function(t){return e.apply(this,arguments)}}(),_=function(){var e=Ge((function(){var e,t;return Xe(this,(function(n){return p?(e="/Home?importType=importHouse&id="+p.id,t=document.createElement("a"),document.body.appendChild(t),t.href=e,t.target="_blank",t.click(),document.body.removeChild(t),[2]):[2]}))}));return function(){return e.apply(this,arguments)}}(),w=function(){var t=Ge((function(){var t;return Xe(this,(function(n){switch(n.label){case 0:return[4,v(y)];case 1:return t=n.sent(),f(e=t||[]),[2]}}))}));return function(){return t.apply(this,arguments)}}(),S=function(e,t){var n=$e({},y),r=~~t;if(0==t.length)return n[e]&&delete n[e],void b(n);n[e]=r&&r>0&&r<2e3?r:t,b(n)};return c.nb.on_M(d.U.ModelRoomListRightPanel,"RoomTemplateListsPanel",(function(e){l(e)})),(0,i.useEffect)((function(){w()}),[]),(0,r.jsxs)(r.Fragment,{children:[o&&(0,r.jsxs)("div",{className:t.leftPanel,children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"140px"},children:[(0,r.jsx)("span",{children:"当前页"}),(0,r.jsx)("input",{className:"row_input",defaultValue:y.pageIndex,min:1,type:"number",onChange:function(e){return S("pageIndex",e.target.value)}}),(0,r.jsx)("span",{children:"每页显示"}),(0,r.jsx)("input",{className:"row_input",defaultValue:y.pageSize,min:1,type:"number",onChange:function(e){return S("pageSize",e.target.value)}}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"空间类型"}),(0,r.jsxs)("select",{className:"row_input",onChange:function(e){S("roomType",e.target.value)},children:[(0,r.jsx)("option",{value:""}),(0,r.jsx)("option",{value:"客餐厅",children:"客餐厅"}),(0,r.jsx)("option",{value:"卧室",children:"卧室"}),(0,r.jsx)("option",{value:"卫生间",children:"卫生间"}),(0,r.jsx)("option",{value:"阳台",children:"阳台"}),(0,r.jsx)("option",{value:"厨房",children:"厨房"})]}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"schemeId"}),(0,r.jsx)("input",{className:"row_input",style:{width:"200px"},defaultValue:y.schemeId||"",min:1,onChange:function(e){return S("schemeId",e.target.value)}}),(0,r.jsx)("br",{}),(0,r.jsx)("button",{style:{height:"25px",width:"60px",lineHeight:"20px"},onClick:Ge((function(){var e;return Xe(this,(function(t){switch(t.label){case 0:return[4,v(y)];case 1:return e=t.sent(),f(e||[]),[2]}}))})),children:"搜索"})]}),null==u?void 0:u.map((function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+((null==p?void 0:p.id)==e.id?"checked":""),onClick:function(){x(e)},children:[e.schemeId,"-",e.roomName," ",(null==e?void 0:e.nickName)||""]},"room_template"+t)}))]}),(0,r.jsx)(r.Fragment,{children:o&&(0,r.jsxs)("div",{className:t.rightPanel,children:[p&&["id","layoutSchemeId","schemeId","roomId","nickName","houseTypeId","roomType","roomName","platform","codeA","codeB","codeC","createUser","createDate","updateDate"].map((function(e,t){return(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsxs)("span",{children:[e,": "]}),(0,r.jsx)("span",{children:p[e]})]},e+t)})),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"编辑布局模板"}),(0,r.jsx)("button",{onClick:_,children:"编辑"})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"删除布局模板"}),(0,r.jsx)("button",{onClick:function(){p.id&&confirm("确认删除布局模板"+p.id+"?")&&j(p)},children:"删除"})]})]})})]})},Ze=n(535),Qe=n(37859),Ye=n(47129),et=n(38964),tt=n(11164),nt=n(53232),rt=n(96892),it=n(85783);function at(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ot(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function lt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ot(e,t,n[t])}))}return e}function st(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function ut(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||dt(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ct(e){return function(e){if(Array.isArray(e))return at(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||dt(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dt(e,t){if(e){if("string"==typeof e)return at(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?at(e,t):void 0}}var ft=function(e){var t,n=e.name,i=e.data,a=e.path,o=e.onValueChange,l=(null===(t=i.name)||void 0===t?void 0:t.value)||n;return void 0!==i.value?(0,r.jsx)(Ze.A.Item,{label:l,style:{maxWidth:400},children:(0,r.jsx)(Qe.A,{value:i.value,onChange:function(e){"number"!=typeof e&&null!==e||o(ct(a).concat(["value"]),e)}})}):i.paramItems?(0,r.jsx)(Ye.A,{title:l,size:"small",style:{marginBottom:16,backgroundColor:"#ffffff",maxWidth:800,border:"1px solid #e8e8e8",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},headStyle:{backgroundColor:"#f0f5ff",fontWeight:"bold",borderBottom:"1px solid #e8e8e8",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"},children:i.paramItems.map((function(e,t){return(0,r.jsx)("div",{style:{marginBottom:8},children:(0,r.jsxs)(et.A,{wrap:!0,children:[(0,r.jsx)(Qe.A,{placeholder:"最小值",value:e.minParam,style:{width:100},onChange:function(e){"number"==typeof e&&o(ct(a).concat(["paramItems",String(t),"minParam"]),e)}}),(0,r.jsxs)(tt.A,{value:e.minEqual,onChange:function(e){return o(ct(a).concat(["paramItems",String(t),"minEqual"]),e)},style:{width:60},children:[(0,r.jsx)(tt.A.Option,{value:!0,children:"≤"}),(0,r.jsx)(tt.A.Option,{value:!1,children:"<"})]}),(0,r.jsx)("span",{children:"(参数值)"}),(0,r.jsxs)(tt.A,{value:e.maxEqual,onChange:function(e){return o(ct(a).concat(["paramItems",String(t),"maxEqual"]),e)},style:{width:60},children:[(0,r.jsx)(tt.A.Option,{value:!0,children:"≤"}),(0,r.jsx)(tt.A.Option,{value:!1,children:"<"})]}),(0,r.jsx)(Qe.A,{placeholder:"最大值",value:e.maxParam,style:{width:100},onChange:function(e){"number"==typeof e&&o(ct(a).concat(["paramItems",String(t),"maxParam"]),e)}}),"score"in e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"：(得分)"}),(0,r.jsx)(Qe.A,{placeholder:"得分",value:e.score,style:{width:80},onChange:function(e){"number"==typeof e&&o(ct(a).concat(["paramItems",String(t),"score"]),e)}})]}),"minScore"in e&&"maxScore"in e&&"intervalNum"in e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"：(最小得分)"}),(0,r.jsx)(Qe.A,{placeholder:"最小得分",value:e.minScore,style:{width:80},onChange:function(e){"number"==typeof e&&o(ct(a).concat(["paramItems",String(t),"score"]),e)}}),(0,r.jsx)("span",{children:"：(最大得分)"}),(0,r.jsx)(Qe.A,{placeholder:"最大得分",value:e.maxScore,style:{width:80},onChange:function(e){"number"==typeof e&&o(ct(a).concat(["paramItems",String(t),"score"]),e)}}),(0,r.jsx)("span",{children:"：(分段数)"}),(0,r.jsx)(Qe.A,{placeholder:"分段数",value:e.intervalNum,style:{width:80},onChange:function(e){"number"==typeof e&&o(ct(a).concat(["paramItems",String(t),"score"]),e)}})]})]})},t)}))}):i.child?(0,r.jsx)(Ye.A,{title:l,size:"small",style:{marginBottom:16,backgroundColor:"#ffffff",maxWidth:800,border:"1px solid #e8e8e8",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},headStyle:{backgroundColor:"#f6ffed",fontWeight:"bold",borderBottom:"1px solid #e8e8e8",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"},children:Object.entries(i.child).map((function(e){var t=ut(e,2),n=t[0],i=t[1];return(0,r.jsx)(ft,{name:n,data:i,path:ct(a).concat(["child",n]),onValueChange:o},n)}))}):null},ht=function(e){var t,n=e.groupName,i=e.groupData,a=e.path,o=e.onValueChange,l=(null===(t=i.name)||void 0===t?void 0:t.value)||n;return(0,r.jsx)(Ye.A,{title:l,style:{marginBottom:16,backgroundColor:"#f7f7f7",maxWidth:800,margin:"0 auto 16px auto",border:"1px solid #d9d9d9",borderRadius:"8px",boxShadow:"0 2px 12px rgba(0,0,0,0.08)"},styles:{header:{backgroundColor:"#e6f7ff",fontWeight:"bold",borderBottom:"1px solid #d9d9d9",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"}},children:Object.entries(i).map((function(e){var t=ut(e,2),n=t[0],i=t[1];return"name"===n?null:(0,r.jsx)(ft,{name:n,data:i,path:ct(a).concat([n]),onValueChange:o},n)}))})},pt=function(e){var t=ut((0,i.useState)(null),2),n=t[0],a=t[1],o=(0,I.A)().styles,s=(0,it.B)().t;(0,i.useEffect)((function(){u()}),[]);var u=function(){var e=nt.TLayoutParamConfigurationManager.instance.getRoomParamConfigs(),t={bathRoomParamConfig:{data:{}},livingRoomParamConfig:{data:{}},kitchenRoomParamConfig:{data:{}},bedRoomParamConfig:{data:{}},entranceRoomParamConfig:{data:{}}},n=function(e){return e.reduce((function(e,t){var n=ut(t,2),r=n[0],i=n[1];if(!i)return e;e[r]={data:{}},i.name&&(e[r].data.name=i.name);var a=function(e){if(!e)return null;var t={};return e.name&&(t.name=e.name),e.paramItems&&(t.paramItems=e.paramItems),void 0!==e.value&&(t.value=e.value),e.child&&(t.child={},Object.entries(e.child).forEach((function(e){var n=ut(e,2),r=n[0],i=n[1];t.child[r]=a(i)}))),t};return i&&Object.entries(i).forEach((function(t){var n=ut(t,2),i=n[0],o=n[1];"name"!==i&&o&&(e[r].data[i]=a(o))})),e}),{})};e.forEach((function(e){var r=e.getRuleParamGroupConfigs(),i=Array.from(r.entries());e.getRoomType()===rt.RoomLayoutScoreType.k_bathRoom?t.bathRoomParamConfig.data=n(i):e.getRoomType()===rt.RoomLayoutScoreType.k_livingRoom?t.livingRoomParamConfig.data=n(i):e.getRoomType()===rt.RoomLayoutScoreType.k_kitchenRoom?t.kitchenRoomParamConfig.data=n(i):e.getRoomType()===rt.RoomLayoutScoreType.k_bedRoom?t.bedRoomParamConfig.data=n(i):e.getRoomType()===rt.RoomLayoutScoreType.k_entranceRoom&&(t.entranceRoomParamConfig.data=n(i))})),a(t)},c=function(e,t){a((function(n){if(!n)return null;for(var r=lt({},n),i=r,a=[],o=0;o<e.length-1;o++)a.push(i),i=i[e[o]];var l,s=i[e[e.length-1]];"object"==(void 0===s?"undefined":(l=s)&&"undefined"!=typeof Symbol&&l.constructor===Symbol?"symbol":typeof l)&&null!==s&&"name"in s?i[e[e.length-1]]=st(lt({},s),{value:t}):i[e[e.length-1]]=t;var u=nt.TLayoutParamConfigurationManager.instance.getRoomParamConfigs(),c=ut(e[0].split("ParamConfig"),1)[0],d={bathRoom:rt.RoomLayoutScoreType.k_bathRoom,livingRoom:rt.RoomLayoutScoreType.k_livingRoom,kitchenRoom:rt.RoomLayoutScoreType.k_kitchenRoom,bedRoom:rt.RoomLayoutScoreType.k_bedRoom}[c],f=u.find((function(e){return e.getRoomType()===d}));if(f){var h=e[2],p=r[e[0]].data[h].data,m=Object.entries(p).reduce((function(e,t){var n=ut(t,2),r=n[0],i=n[1];return"name"===r||(e[r]=i),e}),{});f.setRuleParamGroupConfig(h,m)}return r}))};if(!n)return(0,r.jsxs)("div",{children:[s("加载中"),"..."]});var d="livingRoom";return e.defaultRoomKey&&(d={"客餐厅":"livingRoom","卧室":"bedRoom","厨房":"kitchenRoom","入户花园":"entranceRoom","卫生间":"bathRoom"}[e.defaultRoomKey]||e.defaultRoomKey||d),(0,r.jsxs)("div",{style:{position:"absolute",left:"50%",top:0,width:"100%",height:"100%",backgroundColor:"#fff",padding:"20px",zIndex:1e3,maxWidth:1e3,transform:"translate(-50%, 0px)"},children:[(0,r.jsx)("div",{style:{position:"absolute",left:0,right:0,top:0,bottom:50},children:(0,r.jsx)(h.A,{className:o.score_tabs,defaultActiveKey:"livingRoom",items:[{key:"bathRoom",label:"卫生间参数配置",children:Object.entries(n.bathRoomParamConfig.data).map((function(e){var t=ut(e,2),n=t[0],i=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:i.data,path:["bathRoomParamConfig","data",n,"data"],onValueChange:c},n)}))},{key:"livingRoom",label:"客餐厅参数配置",children:Object.entries(n.livingRoomParamConfig.data).map((function(e){var t=ut(e,2),n=t[0],i=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:i.data,path:["livingRoomParamConfig","data",n,"data"],onValueChange:c},n)}))},{key:"kitchenRoom",label:"厨房参数配置",children:Object.entries(n.kitchenRoomParamConfig.data).map((function(e){var t=ut(e,2),n=t[0],i=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:i.data,path:["kitchenRoomParamConfig","data",n,"data"],onValueChange:c},n)}))},{key:"bedRoom",label:"卧室参数配置",children:Object.entries(n.bedRoomParamConfig.data).map((function(e){var t=ut(e,2),n=t[0],i=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:i.data,path:["bedRoomParamConfig","data",n,"data"],onValueChange:c},n)}))},{key:"entranceRoom",label:"入户花园参数配置",children:Object.entries(n.entranceRoomParamConfig.data).map((function(e){var t=ut(e,2),n=t[0],i=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:i.data,path:["entranceRoomParamConfig","data",n,"data"],onValueChange:c},n)}))}],style:{backgroundColor:"#fafafa"}})}),(0,r.jsx)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",padding:"10px",backgroundColor:"#fff",borderTop:"1px solid #e8e8e8",textAlign:"center",zIndex:1001},children:(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=nt.TLayoutParamConfigurationManager.instance,t=e.toJson();e.saveJson(t)},style:{width:120},children:"保存配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=nt.TLayoutParamConfigurationManager.instance.toFormattedJson(),t=new Blob([e],{type:"application/json"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download="layoutScoreParamConfig.json",r.click()},style:{width:120},children:"下载配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){nt.TLayoutParamConfigurationManager.instance.resetToDefaultConfig(),u()},style:{width:120},children:"重置为默认配置"})]})})]})},mt=n(41014),gt=n(83813),yt=n(25076),bt=n(77320),vt=n(42234),xt=n(13880),jt=n(46909),_t=n(56980),wt=n(66192),St=n(82915),kt=n(14284),It=n(12637),At=n(57894),Tt=n(65329),Nt=n(20029),Ct=n(50372),Ot=n(27390),Lt=n(74159),Dt=n(16986),Rt=n(65640);function Pt(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function Et(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){Pt(a,r,i,o,l,"next",e)}function l(e){Pt(a,r,i,o,l,"throw",e)}o(void 0)}))}}function Ft(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function zt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Mt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){zt(e,t,n[t])}))}return e}function Bt(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var Ut=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"insertLightTemplate",value:function(){var e=Mt({},{dataUrl:"",templateData:"",templateDesc:"",templateImage:"",templateName:"",templateType:1},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});Rt.log("insertLightTemplate 提交数据:",e);try{(0,Dt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/insert",data:Mt({},e),timeout:3e4}).then((function(e){e&&e.success||Rt.error("Fail to insertLightTemplate.")})).catch((function(e){Rt.error(e)}))}catch(e){Rt.error(e)}}},{key:"editLightTemplate",value:function(e){var t=this;return Et((function(){var n,r,i,a;return Bt(this,(function(o){switch(o.label){case 0:if(!e.id)return Rt.error("editLightTemplate: id is required"),[2,Promise.reject(new Error("id is required"))];o.label=1;case 1:return o.trys.push([1,4,,5]),Rt.log("获取模板 ".concat(e.id," 的原有内容")),[4,t.getLightTemplate(e.id)];case 2:return(n=o.sent())?(r=Mt({},n,e),Rt.log("原有模板内容:",n),Rt.log("待修改的参数:",e),Rt.log("合并后的提交数据:",r),[4,(0,Dt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/edit",data:r,timeout:3e4})]):(Rt.error("未找到id为 ".concat(e.id," 的模板")),[2,Promise.resolve(!0)]);case 3:return(i=o.sent())&&i.success?[2,!1]:(Rt.error("Fail to editLightTemplate."),[2,!0]);case 4:return a=o.sent(),Rt.error("编辑模板时发生错误:",a),[2,Promise.reject(a)];case 5:return[2]}}))}))()}},{key:"getLightTemplate",value:function(e){var t={id:e};return Rt.log("getLightTemplate 请求参数:",t),(0,Dt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/get",data:t,timeout:3e4}).then((function(t){return t&&t.success?(Rt.log("getLightTemplate 成功获取模板 ".concat(e," 内容:"),t.result),t.result):(Rt.error("Fail to getLightTemplate."),null)})).catch((function(e){return Rt.error("getLightTemplate 发生错误:",e),null}))}},{key:"listLightTemplate",value:function(e){return Et((function(){var t;return Bt(this,(function(n){return t=e||{},[2,(0,Dt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/listByPage",data:Mt({},t),timeout:3e4}).then((function(e){return e&&e.success?(Rt.log("listLightTemplate response:",e.result.result),e.result):(Rt.error("Fail to listLightTemplate."),null)}))]}))}))()}},{key:"delete",value:function(e){return Et((function(){return Bt(this,(function(t){return[2,(0,Dt.Ap)({method:"post",url:"/api/njvr/LayoutLightTemplate/delete",data:{ids:e},timeout:3e4}).then((function(e){return e&&e.success?e.result:(Rt.error("Fail to delete light template."),null)}))]}))}))()}}],(n=null)&&Ft(t.prototype,n),r&&Ft(t,r),e}(),Jt=n(44497),Ht=n(65640);function Vt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Gt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Wt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $t(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Wt(e,t,n[t])}))}return e}function Kt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function Xt(e){return function(e){if(Array.isArray(e))return Vt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Vt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Vt(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var qt=[{groupName:"客餐厅区域灯光",groupData:[{lightName:"吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅门洞灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅阳台门灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅边缘灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"床体灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"书房区域",groupData:[{lightName:"书房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室区域",groupData:[{lightName:"茶室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"环境光",groupData:[{lightName:"环境光强度",brightness:50,color:"#F5F0E1"},{lightName:"阳光强度",brightness:50,color:"#F5F0E1"}]}],Zt=[{groupName:"客餐厅区域灯光",groupData:[{lightName:"客餐厅吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"电视柜灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"床尾顶光",brightness:50,color:"#F5F0E1"},{lightName:"床尾侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室/书房区域灯光",groupData:[{lightName:"茶室和书房吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]}],Qt=[{name:"吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:10,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"客餐厅|卧室|主卧|次卧|客卧"}},{typeId:101,name:"过道灯光",lighting:{type:1,color:16777215,intensity:.8,length:"25%",width:"90%"},pose:{z:2370},condition:{spaceArea:"过道区"}},{name:"沙发灯光",typeId:2,category:"沙发",lighting:{type:1,color:16777215,intensity:6,width:"80%",length:"50%"},pose:{z:2350,gapOffset:100},condition:{spaceArea:"客厅区"}},{name:"沙发侧光",typeId:102,category:"茶几",lighting:{type:1,color:16777215,intensity:2,width:600,length:600},pose:{gapOffset:550,floorOffset:1e3,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅窗户灯光",typeId:103,lighting:{type:1,color:16777215,intensity:4,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅门洞灯光",typeId:104,lighting:{type:1,color:16777215,intensity:4,width:"40%",length:"40%"},pose:{norOffset:300,floorOffset:1e3,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅阳台门灯光",typeId:105,lighting:{type:1,color:16777215,intensity:4,width:"40%",length:"40%"},pose:{norOffset:300,floorOffset:1e3,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅边缘灯光",typeId:106,lighting:{type:1,color:16777215,intensity:2.5,width:"40%",length:"40%"},pose:{z:1300,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"餐厅窗户灯光",typeId:107,lighting:{type:1,color:16777215,intensity:4,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{spaceArea:"餐厅区"}},{name:"餐桌灯光",typeId:3,category:"餐桌",lighting:{type:1,color:16777215,intensity:2,width:"35%",length:"35%"},pose:{z:2370},condition:{spaceArea:"餐厅区"}},{name:"厨房窗户灯光",typeId:108,lighting:{type:1,color:16777215,intensity:24,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"厨房"}},{name:"厨房灯光",typeId:4,lighting:{type:1,color:16777215,intensity:10,width:"30%",length:"30%"},pose:{z:2270},condition:{roomName:"厨房"}},{name:"卧室窗户灯光",typeId:109,lighting:{type:1,color:16777215,intensity:8,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"床体灯光",typeId:110,category:"床",lighting:{type:1,color:16777215,intensity:6,width:"40%",length:"40%"},pose:{z:2370,align:"center"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"卫生间窗户灯光",typeId:111,lighting:{type:1,color:16777215,intensity:8,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"卫生间"}},{name:"卫生间灯光",typeId:5,lighting:{type:1,color:16777215,intensity:8,width:"30%",length:"30%"},condition:{roomName:"卫生间"}},{name:"书房窗户灯光",typeId:112,lighting:{type:1,color:16777215,intensity:6,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"书房"}},{name:"书桌灯光",typeId:6,category:"书桌",lighting:{type:1,color:16777215,intensity:2,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"书房|茶室"}},{name:"茶室窗户灯光",typeId:113,lighting:{type:1,color:16777215,intensity:4,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"茶室"}},{name:"茶台灯光",typeId:7,category:"茶台",lighting:{type:1,color:16777215,intensity:2,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"茶室"}}],Yt=[{name:"客餐厅吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:11,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"客餐厅"}},{name:"卧室吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:6,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"茶室和书房吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:6,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"书房|茶室"}},{name:"沙发灯光",typeId:2,category:"沙发",lighting:{type:1,color:16777215,intensity:8,width:"80%",length:"50%"},pose:{z:2150,gapOffset:100},condition:{spaceArea:"客厅区"}},{typeId:201,name:"过道灯光",lighting:{type:1,color:16777215,intensity:.8,length:"25%",width:"90%"},pose:{z:2370},condition:{spaceArea:"过道区"}},{name:"电视柜灯光",typeId:202,category:"电视柜",lighting:{type:1,color:16777215,intensity:6,width:"80%",length:"50%"},pose:{z:2150,gapOffset:425},condition:{spaceArea:"客厅区"}},{name:"餐桌灯光",typeId:3,category:"餐桌",lighting:{type:1,color:16777215,intensity:6,width:"100%",length:"100%"},pose:{z:2150},condition:{spaceArea:"餐厅区"}},{name:"床尾顶光",typeId:203,category:"床",lighting:{type:1,color:16777215,intensity:7,width:"50%",length:"50%"},pose:{z:2500,align:"bottom"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"床尾侧光",typeId:204,category:"床",lighting:{type:1,color:16777215,intensity:5,width:"50%",length:"50%"},pose:{gapOffset:1200,floorOffset:1e3,lookAt:"center"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"厨房灯光",typeId:4,lighting:{type:1,color:16777215,intensity:45,width:"30%",length:"30%"},pose:{z:2270},condition:{roomName:"厨房"}},{name:"卫生间灯光",typeId:5,lighting:{type:1,color:16777215,intensity:26,width:"30%",length:"30%"},condition:{roomName:"卫生间"}},{name:"书桌灯光",typeId:6,category:"书桌",lighting:{type:1,color:16777215,intensity:28,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"书房|茶室"}},{name:"茶台灯光",typeId:7,category:"茶台",lighting:{type:1,color:16777215,intensity:2,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"茶室"}}],en=new(function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Wt(this,"cachedImage",null),Wt(this,"templateImage",null),Wt(this,"dayLightConfigs",[{groupName:"客餐厅区域灯光",groupData:[{lightName:"吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅门洞灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅阳台门灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅边缘灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"床体灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"书房区域",groupData:[{lightName:"书房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室区域",groupData:[{lightName:"茶室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"环境光",groupData:[{lightName:"环境光强度",brightness:50,color:"#F5F0E1"},{lightName:"阳光强度",brightness:50,color:"#F5F0E1"}]}]),Wt(this,"nightLightConfigs",[{groupName:"客餐厅区域灯光",groupData:[{lightName:"客餐厅吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"电视柜灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"床尾顶光",brightness:50,color:"#F5F0E1"},{lightName:"床尾侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室/书房区域灯光",groupData:[{lightName:"茶室和书房吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]}]),Wt(this,"templateData",null),Wt(this,"dataUrl",null),Wt(this,"templateInfo",{name:"",category:"dayLight"}),Wt(this,"templateList",[]),Wt(this,"updateLightBrightness",(function(e,n,r,i){var a="dayLight"===e?t.dayLightConfigs:t.nightLightConfigs;n>=0&&n<a.length&&r>=0&&r<a[n].groupData.length&&(a[n].groupData[r].brightness=i)})),Wt(this,"updateLightColor",(function(e,n,r,i){var a="dayLight"===e?t.dayLightConfigs:t.nightLightConfigs;n>=0&&n<a.length&&r>=0&&r<a[n].groupData.length&&(a[n].groupData[r].color=i)})),Wt(this,"resetLightConfigs",(function(e){"dayLight"===e?t.dayLightConfigs=JSON.parse(JSON.stringify(qt)):t.nightLightConfigs=JSON.parse(JSON.stringify(Zt))})),Wt(this,"setDayLightConfigs",(function(e){t.dayLightConfigs=e})),Wt(this,"setNightLightConfigs",(function(e){t.nightLightConfigs=e})),Wt(this,"restoreDefaultConfig",(function(){t.dayLightConfigs=JSON.parse(JSON.stringify(qt)),t.nightLightConfigs=JSON.parse(JSON.stringify(Zt)),t.cachedImage=null,t.templateImage=null,t.templateData=null,t.dataUrl=null,t.templateInfo={name:"",category:"dayLight"}})),Wt(this,"setCachedImage",(function(e,n){t.cachedImage=e})),Wt(this,"setTemplateImage",(function(e,n){t.templateImage=e})),Wt(this,"setDataUrl",(function(e,n){t.dataUrl=e})),Wt(this,"setCategory",(function(e){t.templateInfo.category=e})),Wt(this,"initTemplateList",(function(e){t.templateList=Xt(e)})),Wt(this,"updateTemplateImage",(function(e,n){t.templateList=t.templateList.map((function(t){return t.id===e?Kt($t({},t),{templateImage:n}):t})),t.setTemplateImage(n)})),Wt(this,"updateTemplateName",(function(e,n){t.templateList=t.templateList.map((function(t){return t.key===e?Kt($t({},t),{templateName:n}):t})),t.templateInfo.name=n})),Wt(this,"deleteTemplate",(function(e){t.templateList=t.templateList.filter((function(t){return t.key!==e}))})),Wt(this,"addTemplate",(function(e){var n=Kt($t({},e),{key:"".concat(Date.now())});t.templateList=Xt(t.templateList).concat([n])})),Wt(this,"formatLightConfigsByCategory",(function(e){var n="dayLight"===e?t.dayLightConfigs:t.nightLightConfigs,r="dayLight"===e?Qt:Yt,i=JSON.parse(JSON.stringify(r)),a=new Map;return n.forEach((function(e,t){e.groupData.forEach((function(e){a.set(e.lightName,{brightness:e.brightness,color:e.color})}))})),i.forEach((function(e,n){var r=a.get(e.name);if(r){var i;e.lighting.intensity=r.brightness;var o=r.color.trim();try{if(o.startsWith("rgba("))i=t.rgbaToHex(o);else if(o.startsWith("#")){var l=o.replace("#","");i=parseInt(l,16)}else Ht.warn("不支持的颜色格式: ".concat(o,"，使用默认值")),i=16777215;e.lighting.color=i}catch(t){Ht.error("颜色转换失败: ".concat(t.message,"，使用默认值")),e.lighting.color=16777215}}})),JSON.stringify(i,null,2)})),Wt(this,"validateJsonStructure",(function(e,t){if(e.length!==t.length)return Ht.log("结构验证失败：长度不匹配（导入: ".concat(e.length,", 模板: ").concat(t.length)),!1;for(var n=0;n<e.length;n++){var r=e[n],i=t[n];if(!r.name||!r.lighting)return Ht.log("结构验证失败：第".concat(n,"项缺少必要字段")),!1;if(r.name!==i.name)return Ht.log("结构验证失败：第".concat(n,"项名称不匹配（导入: ").concat(r.name,", 模板: ").concat(i.name)),!1;if("number"!=typeof r.lighting.intensity||"number"!=typeof r.lighting.color)return Ht.log("结构验证失败：第".concat(n,"项灯光属性格式错误")),!1}return!0})),Wt(this,"convertToLightConfigGroup",(function(e){var n=null;t.validateJsonStructure(e,Qt)?(Ht.log("JSON数据匹配成功：日间灯光"),n="dayLight",t.setCategory("dayLight")):t.validateJsonStructure(e,Yt)&&(Ht.log("JSON数据匹配成功：夜间灯光"),n="nightLight",t.setCategory("nightLight"));var r="dayLight"===n?qt:Zt,i=new Map;return e.forEach((function(e){i.set(e.name,{intensity:e.lighting.intensity,color:e.lighting.color})})),r.map((function(e){return Kt($t({},e),{groupData:e.groupData.map((function(e){var t=i.get(e.lightName);return t?Kt($t({},e),{brightness:t.intensity,color:"#".concat(t.color.toString(16).padStart(6,"0").toUpperCase())}):e}))})}))})),(0,Jt.makeAutoObservable)(this)}var t,n,r;return t=e,(n=[{key:"rgbaToHex",value:function(e){var t=e.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]*\s*\)/);if(!t)throw new Error("无效的RGBA颜色格式: ".concat(e));var n=Math.min(255,Math.max(0,parseInt(t[1],10))),r=Math.min(255,Math.max(0,parseInt(t[2],10))),i=Math.min(255,Math.max(0,parseInt(t[3],10))),a=function(e){var t=e.toString(16);return 1===t.length?"0".concat(t):t},o="".concat(a(n)).concat(a(r)).concat(a(i));return parseInt(o,16)}}])&&Gt(t.prototype,n),r&&Gt(t,r),e}()),tn=n(66044),nn=n(13915),rn=n(65640);function an(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function on(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function ln(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){on(a,r,i,o,l,"next",e)}function l(e){on(a,r,i,o,l,"throw",e)}o(void 0)}))}}function sn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function un(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){sn(e,t,n[t])}))}return e}function cn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function dn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||hn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fn(e){return function(e){if(Array.isArray(e))return an(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||hn(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hn(e,t){if(e){if("string"==typeof e)return an(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?an(e,t):void 0}}function pn(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var mn=function(){var e=ln((function(e){var t,n,r,i,a,o,l,s,u,c,d;return pn(this,(function(f){switch(f.label){case 0:return[4,(0,nn.o0)(12,e.name)];case 1:return t=f.sent(),n=t.accessKeyId,r=t.expireAt,i=t.readDomain,a=t.policy,o=t.securityToken,l=t.signature,s=t.keyPrefix,u=t.vendor,c=t.contentType,d=t.ossHost,[2,{uploader:new tn.d({contentType:c,policy:a,signature:l,accessKeyId:n,server:d.replace("-internal",""),expireAt:r,securityToken:o,path:s,vendor:u}),readDomain:i,ossHost:d}]}}))}));return function(t){return e.apply(this,arguments)}}(),gn=function(){var e=ln((function(e){var t,n,r,i,a;return pn(this,(function(l){switch(l.label){case 0:return l.trys.push([0,3,,4]),"image/jpeg"===e.type||"image/png"===e.type?[4,mn(e)]:(o.A.error("请上传JPG/PNG格式的图片"),[2]);case 1:return t=l.sent(),n=t.uploader,r=t.readDomain,i=t.ossHost,[4,n.upload(e)];case 2:return a=l.sent(),[2,"".concat(r||i.replace("-internal",""),"/").concat(a.key)];case 3:throw l.sent();case 4:return[2]}}))}));return function(t){return e.apply(this,arguments)}}(),yn=function(e){var t=e.groupName,n=e.groupData,a=e.category,o="dayLight"===a?en.dayLightConfigs.findIndex((function(e){return e.groupName===t})):en.nightLightConfigs.findIndex((function(e){return e.groupName===t})),l=dn((0,i.useState)({visible:!1,index:-1}),2),s=l[0],u=l[1],c=dn((0,i.useState)(0),2),d=(c[0],c[1]),f=function(e,t){-1!==o&&(d(t),en.updateLightBrightness(a,o,e,t))},h=function(e,t){-1!==o&&en.updateLightColor(a,o,e,t)},p=function(){return(0,r.jsx)(St.sk,{disableColorPanel:!0,onColorChanged:function(e){h(s.index,e)}})};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Ye.A,{title:t,style:{marginBottom:16,backgroundColor:"#ffffff",maxWidth:800,margin:"0 auto 16px auto",border:"1px solid #d9d9d9",borderRadius:"8px",boxShadow:"0 2px 12px rgba(0,0,0,0.08)"},styles:{header:{backgroundColor:"#ffffff",fontWeight:"bold",borderBottom:"1px solid #d9d9d9",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"}},children:n.map((function(e,t){return(0,r.jsxs)("div",{style:{padding:"16px",display:"flex",alignItems:"center",gap:"16px",width:"100%",height:"100%",position:"relative"},children:[(0,r.jsx)("div",{style:{width:"30%"},children:(0,r.jsx)("div",{style:{width:"200px",height:"60px",border:"1px solid #000",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"14px",flexShrink:0},children:e.lightName})}),(0,r.jsx)("div",{style:{width:"1px",height:"80px",backgroundColor:"#e8e8e8",flexShrink:0}}),(0,r.jsxs)("div",{style:sn({width:"70%",display:"flex",alignItems:"flex-start",gap:"16px"},"alignItems","center"),children:[(0,r.jsx)(mt.A.Text,{style:{display:"inline-block",marginBottom:"6px",width:"30px"},children:"亮度"}),(0,r.jsx)("div",{style:{flex:1,minWidth:0},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,r.jsx)(gt.A,{min:0,max:100,value:e.brightness,onChange:function(e){return f(t,e)},style:{flex:1,minWidth:0}}),(0,r.jsx)(yt.A,{type:"number",min:0,max:100,value:e.brightness,onChange:function(e){return f(t,Number(e.target.value))},style:{width:"80px"},suffix:"%"})]})}),(0,r.jsx)(mt.A.Text,{style:{display:"inline-block",marginBottom:"6px",width:"30px"},children:"颜色"}),(0,r.jsx)("div",{style:{flex:1,minWidth:0},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:e.color,borderRadius:"4px",border:"1px solid #d9d9d9",cursor:"pointer"},onClick:function(){u({visible:!0,index:t})}}),(0,r.jsx)(yt.A,{value:e.color,onChange:function(e){return h(t,e.target.value)},placeholder:"输入颜色值",style:{flex:1,minWidth:0}})]})})]})]},e.lightName)}))}),(0,r.jsx)(bt.A,{open:s.visible,onCancel:function(){u((function(e){return cn(un({},e),{visible:!1})}))},footer:null,children:(0,r.jsx)(p,{})})]})},bn=function(e){var t=e.templates,n=dn((0,i.useState)(t),2),a=n[0],s=n[1],u=dn((0,i.useState)(""),2),c=u[0],d=u[1],f=dn((0,i.useState)(""),2),h=f[0],p=f[1],m=(0,i.useRef)(null),g=(0,i.useRef)(null),y=dn((0,i.useState)("createTimeDesc"),2),b=y[0],v=y[1];(0,i.useEffect)((function(){en.initTemplateList(t)}),[t]),(0,i.useEffect)((function(){s(t)}),[t]);var x=function(){var e=ln((function(e){var t,n;return pn(this,(function(r){switch(r.label){case 0:return d(e),[4,Ut.listLightTemplate({templateName:e})];case 1:return(t=r.sent())&&(n=(t.result||[]).map((function(e){return cn(un({},e),{templateType:1===e.templateType?"日光":"夜光"})})),s(n),en.initTemplateList(n)),[2]}}))}));return function(t){return e.apply(this,arguments)}}(),j=function(){var e=ln((function(e,t){var n;return pn(this,(function(r){switch(r.label){case 0:return r.trys.push([0,3,,4]),[4,gn(e)];case 1:return n=r.sent(),s(a.map((function(e){return e.id===t?cn(un({},e),{templateImage:n}):e}))),en.updateTemplateImage(t,n),[4,Ut.editLightTemplate({id:t,templateImage:n})];case 2:return r.sent()||(s(a.map((function(e){return e.id===t?cn(un({},e),{templateImage:n}):e}))),en.updateTemplateImage(t,n),o.A.success("封面更新成功")),[3,4];case 3:return r.sent(),o.A.error("封面更新失败"),[3,4];case 4:return[2]}}))}));return function(t,n){return e.apply(this,arguments)}}(),_=function(){var e=ln((function(e){var t,n,r,i;return pn(this,(function(l){switch(l.label){case 0:if(!g.current)return[2];if(!(r=(null===(n=g.current.input)||void 0===n||null===(t=n.value)||void 0===t?void 0:t.trim())||""))return o.A.error("模板名称不能为空"),[2];l.label=1;case 1:return l.trys.push([1,3,,4]),[4,Ut.editLightTemplate({id:e,templateName:r})];case 2:return l.sent()?o.A.error("名称修改失败"):(s(a.map((function(t){return t.id===e?cn(un({},t),{templateName:r}):t}))),p(""),o.A.success("名称修改成功")),[3,4];case 3:return i=l.sent(),rn.error("修改名称接口调用失败:",i),o.A.error("名称修改失败"),[3,4];case 4:return[2]}}))}));return function(t){return e.apply(this,arguments)}}(),w=function(){var e=ln((function(e){var t,n,r,i,a,l;return pn(this,(function(s){switch(s.label){case 0:return s.trys.push([0,4,,5]),[4,Ut.getLightTemplate(e)];case 1:return t=s.sent(),n=t.dataUrl,[4,fetch(n)];case 2:if(!(r=s.sent()).ok)throw new Error("下载失败: ".concat(r.statusText));return[4,r.blob()];case 3:return i=s.sent(),a=document.createElement("a"),l=URL.createObjectURL(i),a.href=l,a.download="LightConfig.json",a.click(),URL.revokeObjectURL(l),o.A.info("正在下载模板 ".concat(e)),[3,5];case 4:return s.sent(),o.A.error("下载模板 ".concat(e," 失败")),[3,5];case 5:return[2]}}))}));return function(t){return e.apply(this,arguments)}}(),S=function(){var e=ln((function(e){var t;return pn(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,Ut.delete([e])];case 1:return n.sent()?(t=a.filter((function(t){return t.id!==e})),s(t),en.initTemplateList(t),o.A.success("模板删除成功")):o.A.error("模板删除失败：接口返回异常"),[3,3];case 2:return n.sent(),o.A.error("模板删除失败，请重试"),[3,3];case 3:return[2]}}))}));return function(t){return e.apply(this,arguments)}}(),k=[{title:"序号",key:"index",width:"6%",render:function(e,t,n){return(0,r.jsx)("span",{children:n+1})}},{title:"模板封面",dataIndex:"templateImage",key:"templateImage",width:"12%",render:function(e,t){return(0,r.jsxs)("div",{style:{position:"relative",display:"inline-block"},children:[(0,r.jsx)("img",{src:e,alt:"".concat(t.templateName,"的封面"),style:{width:90,height:90,objectFit:"cover",borderRadius:4}}),(0,r.jsx)("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.5)",borderRadius:4,opacity:0,transition:"opacity 0.3s",cursor:"pointer"},onMouseEnter:function(e){return e.currentTarget.style.opacity="1"},onMouseLeave:function(e){return e.currentTarget.style.opacity="0"},onClick:function(){var e;return null===(e=m.current)||void 0===e?void 0:e.click()},children:(0,r.jsx)(vt.A,{name:"avatar",listType:"picture-card",showUploadList:!1,beforeUpload:function(e){return j(e,t.id)},children:en.cachedImage?(0,r.jsx)("div",{style:{width:"100%",height:"100%",borderRadius:4,overflow:"hidden"},children:(0,r.jsx)("img",{src:en.cachedImage,alt:"模板封面",style:{width:"100%",height:"100%",objectFit:"cover",display:"block"}})}):(0,r.jsx)("div",{style:{width:"100%",height:"100%",borderRadius:4,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",cursor:"pointer"},children:(0,r.jsx)(l.A,{icon:(0,r.jsx)(kt.A,{}),size:"small",type:"text",style:{color:"white"},children:"替换"})})})})]})}},{title:"模板名称",dataIndex:"templateName",key:"templateName",width:"20%",render:function(e,t){var n=function(e){return e.id===h}(t);return n?(0,r.jsx)(yt.A,{ref:g,id:"name-input-".concat(t.id),defaultValue:e,autoFocus:!0,onPressEnter:function(){return _(t.id)},style:{width:"100%"},onClick:function(e){return e.stopPropagation()},onBlur:function(){return _(t.id)}}):(0,r.jsx)(mt.A.Text,{underline:!!n,onClick:function(){return function(e){p(e.id)}(t)},style:{cursor:"pointer"},children:e})}},{title:"ID",dataIndex:"id",key:"id",width:"15%"},{title:"创建时间",dataIndex:"createDate",key:"createDate",width:"15%"},{title:"分类",dataIndex:"templateType",key:"templateType",width:"12%"},{title:"操作",key:"action",width:"20%",render:function(e,t){return(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{icon:(0,r.jsx)(It.A,{}),type:"text",style:{color:"#1890ff"},onClick:function(){return w(t.id)},children:"下载"}),(0,r.jsx)(xt.A,{title:"确定要删除这个模板吗？",onConfirm:function(){return S(t.id)},okText:"是",cancelText:"否",children:(0,r.jsx)(l.A,{icon:(0,r.jsx)(At.A,{}),type:"text",style:{color:"#1890ff"},children:"删除"})})]})}}],I=a.filter((function(e){return e.templateName.includes(c)}));return(0,r.jsxs)("div",{style:{padding:"20px"},children:[(0,r.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"16px",borderRadius:"4px",marginBottom:"16px",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{icon:(0,r.jsx)(Tt.A,{}),size:"middle",onClick:function(){return x(c)}}),(0,r.jsx)(yt.A,{placeholder:"搜索模板",allowClear:!0,style:{width:300},value:c,onChange:function(e){return d(e.target.value)},onPressEnter:function(){return x(c)},onClear:function(){d(""),s(t),en.initTemplateList(t)}}),(0,r.jsx)(jt.A,{title:"category"===b?"当前按分类排序":"createTimeAsc"===b?"当前按创建时间正序排序":"当前按创建时间倒序排序",children:(0,r.jsx)(l.A,{icon:"category"===b?(0,r.jsx)(Nt.A,{}):"createTimeAsc"===b?(0,r.jsx)(Ct.A,{}):(0,r.jsx)(Ot.A,{}),onClick:function(){var e="category"===b?"createTimeAsc":"createTimeAsc"===b?"createTimeDesc":"category";v(e);var t=fn(a).sort((function(t,n){if("category"===e){var r="string"==typeof t.templateType?"日光"===t.templateType?1:0:t.templateType;return("string"==typeof n.templateType?"日光"===n.templateType?1:0:n.templateType)-r}return"createTimeAsc"===e?new Date(t.createDate).getTime()-new Date(n.createDate).getTime():new Date(n.createDate).getTime()-new Date(t.createDate).getTime()}));s(t)},size:"middle"})})]})}),(0,r.jsx)(_t.A,{columns:k,dataSource:I,pagination:!1,bordered:!0,rowKey:"key"})]})},vn=(0,T.observer)((function(e){var t=dn((0,i.useState)(null),2),n=t[0],a=t[1],s=(0,I.A)().styles,u=(0,it.B)().t,c=dn((0,i.useState)(!1),2),d=c[0],f=c[1],p=dn(Ze.A.useForm(),1)[0],m=dn((0,i.useState)([]),2),g=m[0],y=m[1],b=dn((0,i.useState)(1),2),v=b[0],x=b[1],j=dn((0,i.useState)(50),2),_=j[0],w=j[1],S=dn((0,i.useState)(0),2),k=S[0],A=S[1];(0,i.useEffect)((function(){N()}),[]);var T=function(){var e=ln((function(){var e,t,n;return pn(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,Ut.listLightTemplate({})];case 1:return e=r.sent(),t=(e.result||[]).map((function(e){return cn(un({},e),{templateType:1===e.templateType?"日光":"夜光"})})),y(t||[]),A(e.recordCount),rn.log("初始化模板数据:",e),[3,3];case 2:return n=r.sent(),rn.error("获取模板数据失败:",n),[3,3];case 3:return[2]}}))}));return function(){return e.apply(this,arguments)}}(),N=function(){a({dayLightParamConfig:{data:{}},nightLightParamConfig:{data:{}},modelLightParamConfig:{data:{}}}),T()},C=function(){var e=en.templateInfo.category,t=en.formatLightConfigsByCategory(e);return new File([t],"wireFrameImageJson.json",{type:"application/json"})},O=function(){var e=ln((function(e){var t;return pn(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,3,4]),[4,gn(e)];case 1:return t=n.sent(),en.setCachedImage(t),en.setTemplateImage(t),p.setFieldValue("cover",t),[3,4];case 2:return n.sent(),[3,4];case 3:return[7];case 4:return[2]}}))}));return function(t){return e.apply(this,arguments)}}(),L=function(){var e=ln((function(){var e,t,n,r;return pn(this,(function(i){switch(i.label){case 0:return e="dayLight"===en.templateInfo.category?1:2,[4,(0,Ae.Sf)(C())];case 1:return t=i.sent(),en.setDataUrl(t),n=en.templateImage,r=p.getFieldValue("name"),Ut.insertLightTemplate({dataUrl:en.dataUrl,templateImage:n,templateName:r,templateType:e}),f(!1),p.setFieldsValue({name:""}),en.setCachedImage(null),[2]}}))}));return function(){return e.apply(this,arguments)}}(),D=function(){var e=ln((function(e,t){var n,r;return pn(this,(function(i){switch(i.label){case 0:return x(e),w(t),[4,Ut.listLightTemplate({pageIndex:e,pageSize:t})];case 1:return(n=i.sent())&&(r=n.result.map((function(e){return cn(un({},e),{templateType:1===e.templateType?"日光":"夜光"})})),y(fn(r)),en.initTemplateList(fn(r))),[2]}}))}));return function(t,n){return e.apply(this,arguments)}}();return n?(0,r.jsxs)("div",{style:{position:"absolute",left:"50%",top:0,width:"100%",height:"100%",backgroundColor:"#fff",padding:"20px",zIndex:1e3,maxWidth:1e3,transform:"translate(-50%, 0px)"},children:[(0,r.jsx)("div",{style:{position:"absolute",left:0,right:0,top:0,bottom:50},children:(0,r.jsx)(h.A,{className:s.score_tabs,defaultActiveKey:"dayLight",items:[{key:"dayLight",label:"日光灯光模板配置",children:en.dayLightConfigs.map((function(e){return(0,r.jsx)(yn,{groupName:e.groupName,groupData:e.groupData,category:"dayLight"},e.groupName)}))},{key:"nightLight",label:"夜间灯光模板配置",children:en.nightLightConfigs.map((function(e){return(0,r.jsx)(yn,{groupName:e.groupName,groupData:e.groupData,category:"nightLight"},e.groupName)}))},{key:"modelLight",label:"灯光模板管理",children:(0,r.jsx)(bn,{templates:g})}],style:{backgroundColor:"#ffffff"},onChange:function(e){en.setCategory(e)}})}),("dayLight"===en.templateInfo.category||"nightLight"===en.templateInfo.category)&&(0,r.jsx)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",padding:"10px",backgroundColor:"#fff",borderTop:"1px solid #e8e8e8",textAlign:"center",zIndex:1001},children:(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{type:"primary",onClick:function(){f(!0)},style:{width:150},children:"保存当前配置为模板"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=URL.createObjectURL(C()),t=document.createElement("a");t.href=e,t.download="LightConfig.json",t.click()},style:{width:120},children:"下载配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){en.resetLightConfigs(en.templateInfo.category)},style:{width:120},children:"恢复默认配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=function(){var e=ln((function(e){var t,n,r,i,a,l,s;return pn(this,(function(u){switch(u.label){case 0:if(n=e.target,!(r=null===(t=n.files)||void 0===t?void 0:t[0]))return o.A.warning("未选择文件"),[2];if("application/json"!==r.type&&!r.name.endsWith(".json"))return o.A.error("请上传 JSON 格式的文件"),[2];u.label=1;case 1:return u.trys.push([1,3,,4]),[4,new Promise((function(e,t){var n=new FileReader;n.onload=function(){return e(n.result)},n.onerror=t,n.readAsText(r)}))];case 2:return i=u.sent(),a=JSON.parse(i),Array.isArray(a)?(l=en.convertToLightConfigGroup(a),rn.log("转换后的配置:",l),"dayLight"===en.templateInfo.category?(rn.log("类型",en.templateInfo.category),en.setDayLightConfigs(l),o.A.success("日光配置导入成功")):(rn.log("类型",en.templateInfo.category),en.setNightLightConfigs(l),o.A.success("夜景配置导入成功")),[3,4]):(o.A.error("JSON格式错误"),[2]);case 3:return s=u.sent(),o.A.error("加载失败: ".concat((c=s,(null!=(d=Error)&&"undefined"!=typeof Symbol&&d[Symbol.hasInstance]?d[Symbol.hasInstance](c):c instanceof d)?s.message:"解析错误"))),[3,4];case 4:return[2]}var c,d}))}));return function(t){return e.apply(this,arguments)}}(),e.click()},style:{width:120},children:"加载灯光配置"})]})}),"modelLight"===en.templateInfo.category&&(0,r.jsx)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",padding:"10px",backgroundColor:"#fff",borderTop:"1px solid #e8e8e8",textAlign:"center",zIndex:1001,display:"flex",justifyContent:"flex-end",alignItems:"center"},children:(0,r.jsx)(wt.A,{current:v,pageSize:_,total:k,onChange:D,onShowSizeChange:D,showSizeChanger:!0})}),(0,r.jsxs)(bt.A,{title:(0,r.jsxs)("div",{style:{textAlign:"center",width:"100%"},children:[(0,r.jsx)(mt.A.Title,{level:4,style:{margin:0},children:"保存为新灯光模板"}),(0,r.jsx)("div",{style:{height:"1px",backgroundColor:"#000",marginTop:8}})]}),open:d,onCancel:function(){f(!1)},footer:null,width:700,destroyOnClose:!0,children:[(0,r.jsxs)("div",{style:{display:"flex",gap:20},children:[(0,r.jsxs)("div",{style:{width:"30%",marginTop:0},children:[(0,r.jsx)(mt.A.Text,{type:"danger",style:{display:"inline-block"},children:"* "}),(0,r.jsx)(mt.A.Text,{style:{display:"inline-block"},children:"模板封面"}),(0,r.jsx)(vt.A,{name:"avatar",listType:"picture-card",className:"avatar-uploader",showUploadList:!1,beforeUpload:O,children:en.cachedImage?(0,r.jsx)("div",{style:{width:"100%",height:"100%",borderRadius:4,overflow:"hidden"},children:(0,r.jsx)("img",{src:en.cachedImage,alt:"模板封面",style:{width:"100%",height:"100%",objectFit:"cover",display:"block"}})}):(0,r.jsxs)("div",{style:{width:"100%",height:"100%",borderRadius:4,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",cursor:"pointer"},children:[(0,r.jsx)(Lt.A,{style:{fontSize:24,color:"#1890ff"}}),(0,r.jsx)("p",{style:{marginTop:8},children:"点击上传"}),(0,r.jsx)("p",{style:{fontSize:12,color:"#666"},children:"支持JPG/PNG格式"})]})})]}),(0,r.jsx)("div",{style:{width:"70%"},children:(0,r.jsxs)(Ze.A,{form:p,layout:"vertical",children:[(0,r.jsx)(Ze.A.Item,{name:"name",label:"模板名称",rules:[{required:!0,message:"请输入模板名称"},{max:50,message:"名称不能超过50个字符"}],children:(0,r.jsx)(yt.A,{placeholder:"请输入模板名称"})}),(0,r.jsx)(Ze.A.Item,{name:"category",label:"模板分类",children:(0,r.jsx)("div",{style:{display:"flex",gap:8},children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.A,{style:{backgroundColor:"dayLight"===en.templateInfo.category||"modelLight"===en.templateInfo.category?"#1890ff":"#ffffff",color:"dayLight"===en.templateInfo.category||"modelLight"===en.templateInfo.category?"#ffffff":"#000000",border:"1px solid #d9d9d9",borderRadius:4,padding:"8px 16px",cursor:"pointer",marginRight:8},children:"日光"}),(0,r.jsx)(l.A,{style:{backgroundColor:"nightLight"===en.templateInfo.category?"#1890ff":"#ffffff",color:"nightLight"===en.templateInfo.category?"#ffffff":"#000000",border:"1px solid #d9d9d9",borderRadius:4,padding:"8px 16px",cursor:"pointer"},children:"夜光"})]})})})]})})]}),(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"flex-end",marginTop:20,gap:10},children:[(0,r.jsx)(l.A,{onClick:function(){return f(!1)},style:{width:100},children:"取消"}),(0,r.jsx)(l.A,{type:"primary",style:{width:100},onClick:L,children:"保存"})]})]})]}):(0,r.jsxs)("div",{children:[u("加载中"),"..."]})})),xn=vn,jn=n(7729);function _n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function wn(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function Sn(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){wn(a,r,i,o,l,"next",e)}function l(e){wn(a,r,i,o,l,"throw",e)}o(void 0)}))}}function kn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||An(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function In(e){return function(e){if(Array.isArray(e))return _n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||An(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function An(e,t){if(e){if("string"==typeof e)return _n(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_n(e,t):void 0}}function Tn(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var Nn=function(e){var t=xe().styles,n=kn((0,i.useState)("Default"),2),a=n[0],o=n[1],s=kn((0,i.useState)(0),2),u=(s[0],s[1],kn((0,i.useState)("客餐厅"),2)),c=u[0],d=u[1],f=kn((0,i.useState)(["BasicTransfer","SpacePartition"]),2),h=f[0],p=f[1],m=e.datasetNameList||[],g=jn.gl,y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e=e||a;var t=m.find((function(t){return t.id===e}));return(null==t?void 0:t.name)||"默认数据集"},b=(0,Q.AU)({format_type:1}),v=function(){var t=Sn((function(){var t,n,r,i,o;return Tn(this,(function(l){switch(l.label){case 0:return t=(0,Q.w_)("Test"),n=a,r=c,i=0,e.taskList.forEach((function(e){return i=Math.max(e.task_count||0,i)})),o={id:t,task_count:i+1,dataset_id:n,dataset_name:y(n),room_name:r,methods:In(h),create_date:b},[4,we.n.instance.addData(o,we.n.TestingTaskTable)];case 1:return l.sent(),_(),[2]}}))}));return function(){return t.apply(this,arguments)}}(),x=function(){var e=Sn((function(e){return Tn(this,(function(t){return o(e),[2]}))}));return function(t){return e.apply(this,arguments)}}(),j=function(){var e=Sn((function(){var e,t;return Tn(this,(function(n){switch(n.label){case 0:return[4,we.n.instance.getDataById("lastDatasetId",we.n.DefaultTable)];case 1:return t=(null===(e=n.sent())||void 0===e?void 0:e.dataset_id)||null,x(t=t||a),[2]}}))}));return function(){return e.apply(this,arguments)}}(),_=function(){(null==e?void 0:e.onHide)&&e.onHide()};return(0,i.useEffect)((function(){j()}),[]),(0,r.jsxs)("div",{className:"CreateTaskPopUp",style:{position:"fixed",zIndex:10001,width:"900px",top:"0px",left:"300px"},children:[(0,r.jsx)("div",{className:"closeBtn",style:{position:"absolute",top:"40px",right:"30px",fontSize:"16px",cursor:"pointer",zIndex:10001},onClick:function(){return _()},children:"X"}),(0,r.jsxs)("div",{className:t.dialogInputs,style:{height:"400px"},children:[(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 测试集: "}),(0,r.jsx)("select",{name:"dataset_list",onChange:function(e){o(e.target.value)},defaultValue:a,children:m.map((function(e,t){return(0,r.jsx)("option",{value:e.id,children:e.name},"dataset_"+t)}))})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 空间: "}),(0,r.jsx)("select",{name:"roomname_list",onChange:function(e){d(e.target.value)},defaultValue:c,children:["客餐厅","厨房","卧室","卫生间","书房","阳台"].map((function(e,t){return(0,r.jsx)("option",{value:e,children:e},"room_type"+t)}))})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:"算法: "}),(0,r.jsx)("span",{children:Object.keys(g).map((function(e,t){return(0,r.jsx)("span",{className:"methods_name "+(h.includes(e)?"checked":""),onClick:function(){var t=In(h),n=t.indexOf(e);n<0?t.push(e):t.splice(n,1),p(t)},children:g[e]},"methods_"+e)}))})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 日期: "}),(0,r.jsxs)("span",{children:[b," "]})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)(l.A,{onClick:function(){return v()},children:"创建"}),(0,r.jsx)(l.A,{onClick:function(){return _()},children:"取消"})]})]})]})},Cn=jn.gl,On=[{key:"task_name",ui_name:"任务序号"},{key:"dataset_name",ui_name:"数据集"},{key:"room_name",ui_name:"空间类型"},{key:"methods",ui_name:"算法类型"},{key:"dataset_size",ui_name:"测试总量"},{key:"create_date",ui_name:"创建时间"},{key:"average_solving_time",ui_name:"平均计算时长"},{key:"layout_cover_rate",ui_name:"布局覆盖率"},{key:"empty_layout_num",ui_name:"空布局户型数"},{key:"layout_valid_cadidate_rate",ui_name:"布局准出率"},{key:"layout_valid_candidate_num",ui_name:"准出的布局数"},{key:"layout_total_candidates_num",ui_name:"推荐布局的总数"}],Ln=function(e,t){if(!e)return"";var n=e[t];return"methods"===t?n.map((function(e){return Cn[e]})).join(" "):"task_name"==t?e.dataset_name+" "+String(e.task_count||0).padStart(3,"0"):t.endsWith("time")?n+"ms":n},Dn=function(e){var t=xe().styles;return(0,r.jsxs)("div",{className:t.dataReport+" "+((null==e?void 0:e.className)||""),children:[(0,r.jsx)("div",{className:"report_row",style:{textAlign:"center"},children:e.title}),On.map((function(t,n){return(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsxs)("span",{children:[t.ui_name,":"]}),"    ",(0,r.jsx)("span",{children:Ln(e.task,t.key)})]},"report_row"+t.key+"_"+n)})),e.onEnterTask&&(0,r.jsx)("div",{className:"report_row",style:{textAlign:"center",marginTop:"20px"},children:(0,r.jsx)(l.A,{onClick:function(){return e.onEnterTask()},children:"进入任务"})})]})};function Rn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Pn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Rn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var En=function(e){(0,I.A)().styles;var t=xe().styles,n=(jn.gl,Pn((0,i.useState)(null),2)),a=n[0],o=n[1],l=e.taskList,s=e.current_task,u=l.filter((function(e){return e.id!=s.id&&e.dataset_id==s.dataset_id&&e.room_name===s.room_name}));return(0,i.useEffect)((function(){}),[]),(0,r.jsxs)("div",{className:t.test_report,children:[(0,r.jsx)("div",{className:"close_btn",onClick:function(){e.onHide&&e.onHide()},children:"X"}),(0,r.jsx)(Dn,{task:s,title:"当前任务",onEnterTask:e.onEnterTask}),(0,r.jsx)(Dn,{task:a,title:"对比任务",className:"compared_report"}),(0,r.jsxs)("div",{className:t.dataReport+" compared_report",children:[(0,r.jsx)("div",{className:"report_row",style:{textAlign:"center"},children:"关联任务列表"}),u.map((function(e,t){return(0,r.jsxs)("div",{className:"report_row "+(e.id===(null==a?void 0:a.id)?"checked":""),onClick:function(){o(e)},children:[(0,r.jsxs)("span",{children:[Ln(e,"task_name"),":"]}),"    ",(0,r.jsx)("span",{children:Ln(e,"methods")})]},"related_report_row_"+t)}))]})]})},Fn=n(50707),zn=n(65640);function Mn(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function Bn(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){Mn(a,r,i,o,l,"next",e)}function l(e){Mn(a,r,i,o,l,"throw",e)}o(void 0)}))}}function Un(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Jn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Hn(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var Vn=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Jn(this,"_task",void 0),Jn(this,"_building_list",void 0),Jn(this,"_is_running",void 0),Jn(this,"_start_id",void 0),Jn(this,"onClickBuildingId",void 0),Jn(this,"onFinish",void 0),e._instance=this,this._task=null,this._building_list=null,this._is_running=!1,this._start_id=-1}var t,n,r;return t=e,n=[{key:"setTask",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this._task=e,this._building_list=t,this._start_id=n}},{key:"readResultOnBuildingRoom",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=this;return Bn((function(){var r,i,a;return Hn(this,(function(o){switch(o.label){case 0:return r=n._task.id+"-"+e,[4,we.n.instance.getDataById(r,we.n.TestingTaskDetailTable)];case 1:return(i=o.sent())?(t&&i.layout_scheme_list&&(a=i.layout_scheme_list.map((function(e){var n=new Fn.F;return n.importScheme(e),n.room=t,n})),t._layout_scheme_list=a||[]),[2,i]):[2]}}))}))()}},{key:"summaryResults",value:function(){var e=this;return Bn((function(){var t,n,r,i,a,o,l,s,u,c,d,f,h;return Hn(this,(function(p){switch(p.label){case 0:if(!e._task)return[2];e._task.average_solving_time=0,e._task.total_solving_time=0,e._task.layout_cover_rate=0,e._task.layout_total_candidates_num=0,e._task.layout_valid_cadidate_rate=0,t=0,e._task.layout_valid_candidate_num=0,e._task.empty_layout_num=0,n=!0,r=!1,i=void 0,p.label=1;case 1:p.trys.push([1,8,9,10]),a=e._building_list[Symbol.iterator](),p.label=2;case 2:return(n=(o=a.next()).done)?[3,7]:(l=o.value,[4,e.readResultOnBuildingRoom(l.buildingRoomId,null)]);case 3:return s=p.sent()||{layout_scheme_list:[]},0==(u=s.layout_scheme_list.filter((function(e){return Fn.F.checkIsValidByScores(e)}))).length&&s.id?(e._task.empty_layout_num++,s.mark_content="有效布局为空",[4,we.n.instance.addData(s,we.n.TestingTaskDetailTable)]):[3,5];case 4:p.sent(),p.label=5;case 5:e._task.layout_total_candidates_num+=s.layout_scheme_list.length,e._task.layout_valid_candidate_num+=u.length,c=s.computing_duration,isNaN(c)&&(c=100),e._task.total_solving_time+=c,s.layout_scheme_list&&s.layout_scheme_list.length>0&&(t+=(u.length+1e-6)/(s.layout_scheme_list.length+1e-6)),p.label=6;case 6:return n=!0,[3,2];case 7:return[3,10];case 8:return d=p.sent(),r=!0,i=d,[3,10];case 9:try{n||null==a.return||a.return()}finally{if(r)throw i}return[7];case 10:return e._task.layout_valid_cadidate_rate=(0,Q.mk)(t/e._building_list.length,1e3),e._task.average_solving_time=(0,Q.mk)(e._task.total_solving_time/e._building_list.length),e._task.layout_cover_rate=(0,Q.mk)(1-e._task.empty_layout_num/e._building_list.length,1e3),[4,we.n.instance.addData(e._task,we.n.TestingTaskTable)];case 11:return p.sent(),f=nt.TLayoutParamConfigurationManager.instance.toJson(),[4,we.n.instance.getDataById(e._task.id,we.n.TestingTaskConfigsTable)];case 12:return(h=p.sent()||{})[nt.TLayoutParamConfigurationManager.IndexedDB_Prop_Key]?[3,14]:(h.id=e._task.id,h[nt.TLayoutParamConfigurationManager.IndexedDB_Prop_Key]=f,[4,we.n.instance.addData(h,we.n.TestingTaskConfigsTable)]);case 13:p.sent(),p.label=14;case 14:return[2]}}))}))()}},{key:"filterResults",value:function(e){var t=this;return Bn((function(){var n,r,i,a,o,l,s,u;return Hn(this,(function(c){switch(c.label){case 0:n=[],r=[],i=!0,a=!1,o=void 0;try{for(l=function(){var i=u.value;r.push(new Promise((function(r,a){t.readResultOnBuildingRoom(i.buildingRoomId,null).then((function(t){!0===e.marked&&(null==t?void 0:t.mark_content)&&(null==t?void 0:t.mark_content.trim().length)>0&&n.push(t),r(!0)}))})))},s=t._building_list[Symbol.iterator]();!(i=(u=s.next()).done);i=!0)l()}catch(e){a=!0,o=e}finally{try{i||null==s.return||s.return()}finally{if(a)throw o}}return[4,Promise.all(r)];case 1:return c.sent(),[2,n]}}))}))()}},{key:"solveInHouseByIndex",value:function(e){var t=this;return Bn((function(){var n,r,i,a,o,l,s,u,d,f,h,p,m;return Hn(this,(function(g){switch(g.label){case 0:return zn.clear(),r=t._building_list[e],t.onClickBuildingId&&t.onClickBuildingId(r.buildingRoomId),i=t._task.room_name,a=0,o=c.nb.instance.layout_container,l=c.nb.instance.layout_graph_solver,[4,we.n.instance.getDataById(r.buildingRoomId,we.n.BuildingSchemeDataTable)];case 1:return(s=null===(n=g.sent())||void 0===n?void 0:n.houseInfo)&&s.schemeXmlJson?(o.fromXmlSchemeData(s.schemeXmlJson),(u=o._rooms.filter((function(e){return e.roomname.includes(i)}))).length,(d=u[a]||u[0]||null)?(f=(new Date).getTime(),t._task.methods.includes("BasicTransfer")||t._task.methods.includes("GroupTransfer")?[4,l.queryModelRoomsFromServer([d])]:[3,3]):[2,!1]):[3,6];case 2:g.sent(),g.label=3;case 3:return[4,o.applyRoomWithSolvingMethods(d,t._task.methods,null)];case 4:return h=g.sent(),p=(new Date).getTime(),m={id:t._task.id+"-"+r.buildingRoomId,task_id:t._task.id,building_id:r.buildingRoomId,room:d.exportExtRoomData(),layout_scheme_list:h.map((function(e){return e.exportScheme({export_debug_data:!1})})),computing_duration:p-f,computing_date:(0,Q.AU)({format_type:1})},[4,we.n.instance.addData(m,we.n.TestingTaskDetailTable)];case 5:g.sent(),g.label=6;case 6:return[2,!0]}}))}))()}},{key:"runTask",value:function(){var e=this;return Bn((function(){var t,n;return Hn(this,(function(r){switch(r.label){case 0:if(!e._is_running)return[2,!0];if(!e._building_list)return[2,!0];t=Math.max(e._start_id,0),n=t,r.label=1;case 1:return n<e._building_list.length?e._is_running?[4,e.solveInHouseByIndex(n)]:[2]:[3,5];case 2:return r.sent(),[4,(0,Q.IP)(50)];case 3:r.sent(),e._task.testing_date=(0,Q.AU)({format_type:1}),r.label=4;case 4:return n++,[3,1];case 5:return[4,e.summaryResults()];case 6:return r.sent(),[2,!0]}}))}))()}}],r=[{key:"instance",get:function(){return e._instance||new e,e._instance}},{key:"setTask",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;e.instance.setTask(t,n,r)}},{key:"IsRunning",get:function(){return e.instance._is_running},set:function(t){e.instance._is_running!==t&&(e.instance._is_running=t,t&&e.instance.runTask().then((function(t){e.instance.onFinish&&e.instance.onFinish()})))}}],n&&Un(t.prototype,n),r&&Un(t,r),e}();function Gn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Wn(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function $n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Gn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Gn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kn(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}Jn(Vn,"_instance",null);var Xn=function(e){var t,n,a,o,l=xe().styles,s=$n((0,i.useState)((null===(t=e.roomResult)||void 0===t?void 0:t.fixed)||!1),2),u=s[0],c=s[1],d=e.roomResult,f=[];d&&(d.layout_scheme_list=d.layout_scheme_list||[],f=d.layout_scheme_list.filter((function(e){return Fn.F.checkIsValidByScores(e)})));var h,p=(0,i.useRef)(null),m=["有效布局为空"];"客餐厅"===(null==d||null===(a=d.room)||void 0===a||null===(n=a.room)||void 0===n?void 0:n.roomname)&&(h=m).push.apply(h,["沙发区朝向问题","缺失主要柜体","有更优解,但排序不靠前","餐厅区 位置并非最优","玄关柜有更优位置","柜体的尺寸比例看似不太合理","客厅区 和 餐厅区 分区不正确","客厅区 和 餐厅区 分区比例 不合理","电视-沙发距离太长，看起来不合理","沙发区 应用要允许比电视柜 宽","小空间（特别是狭长空间）分区不好"]);var g=function(){var t,n=(t=function(){var t;return Kn(this,(function(n){switch(n.label){case 0:return[4,we.n.instance.addData(d,we.n.TestingTaskDetailTable)];case 1:return n.sent(),e.markedData?(t=e.markedData,d.mark_content&&0!=d.mark_content.trim().length?t.markedDict[d.building_id]={mark_content:d.mark_content,fixed:d.fixed}:t.markedDict[d.building_id]&&delete t.markedDict[d.building_id],[4,we.n.instance.addData(t,we.n.TestingTaskMarkTable)]):[3,3];case 2:n.sent(),n.label=3;case 3:return[2]}}))},function(){var e=this,n=arguments;return new Promise((function(r,i){var a=t.apply(e,n);function o(e){Wn(a,r,i,o,l,"next",e)}function l(e){Wn(a,r,i,o,l,"throw",e)}o(void 0)}))});return function(){return n.apply(this,arguments)}}();return(0,i.useEffect)((function(){p.current&&(p.current.value=d.mark_content||""),c((null==d?void 0:d.fixed)||!1)}),[d]),d?(0,r.jsxs)("div",{className:l.resultInfo,children:[(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"列表序号:"}),"    ",(0,r.jsx)("span",{children:e.list_index+1})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"户型ID:"}),"    ",(0,r.jsx)("span",{children:d.building_id})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"测试日期:"}),"    ",(0,r.jsx)("span",{children:d.computing_date})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"计算耗时:"}),"    ",(0,r.jsxs)("span",{children:[d.computing_duration||0,"ms"]})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"布局数量:"}),"    ",(0,r.jsx)("span",{children:(d.layout_scheme_list||[]).length})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"准出数量:"}),"    ",(0,r.jsx)("span",{children:f.length})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"异常标记:"}),"    ",(0,r.jsx)("input",{style:{height:"22px"},ref:p,list:"mark_list",defaultValue:d.mark_content||"",onChange:function(e){d.mark_content=e.target.value,g()}}),(0,r.jsx)("datalist",{id:"mark_list",children:m.map((function(e,t){return(0,r.jsx)("option",{value:e,children:e},"mark_data_"+t)}))})]}),d.mark_content&&d.mark_content.length>0&&(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"是否修复:"}),"    ",(0,r.jsx)("span",{onClick:function(){return e=!u,d.fixed=e,g(),void c(e);var e},children:u?"✔":"-"})]}),(0,r.jsx)("br",{}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"异常数量:"}),"    ",(0,r.jsx)("span",{children:Object.keys((null==e||null===(o=e.markedData)||void 0===o?void 0:o.markedDict)||{}).length})]})]}):(0,r.jsx)(r.Fragment,{})};function qn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Zn(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function Qn(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){Zn(a,r,i,o,l,"next",e)}function l(e){Zn(a,r,i,o,l,"throw",e)}o(void 0)}))}}function Yn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function er(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||nr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tr(e){return function(e){if(Array.isArray(e))return qn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||nr(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nr(e,t){if(e){if("string"==typeof e)return qn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?qn(e,t):void 0}}function rr(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var ir=[],ar=function(e){var t="RunningTestingTaskPanel",n=e.current_task,a=xe().styles,o=er((0,i.useState)([]),2),s=o[0],u=o[1],f=er((0,i.useState)({}),2),h=f[0],p=f[1],m=er((0,i.useState)(""),2),g=m[0],y=m[1],b=er((0,i.useState)(!1),2),v=b[0],x=b[1],j=er((0,i.useState)(!1),2),_=j[0],w=j[1],S=er((0,i.useState)("SingleRoom"),2),I=S[0],T=S[1],N=er((0,i.useState)(0),2),C=(N[0],N[1],er((0,i.useState)(1e3),2)),O=C[0],L=C[1],D=er((0,i.useState)(0),2),R=D[0],P=D[1],E=[{mode:"SingleRoom",name:"单空间"},{mode:"FullHouse",name:"查看全屋"}],F=["测试结果","重新计算"],z=er((0,i.useState)("测试结果"),2),M=z[0],B=z[1],U=er((0,i.useState)("Info"),2),J=U[0],H=U[1],V=er((0,i.useState)(null),2),G=V[0],W=V[1],$=er((0,i.useState)({id:n.id,markedDict:{}}),2),K=$[0],X=$[1],q=er((0,i.useState)([]),2),Z=q[0],Q=q[1],Y=er((0,i.useState)(!1),2),ee=Y[0],te=Y[1],ne=function(){var e=Qn((function(){var e,t,r;return rr(this,(function(i){switch(i.label){case 0:return[4,we.n.instance.getTestingDatasetById(n.dataset_id)];case 1:return t=(null===(e=i.sent())||void 0===e?void 0:e.buildingList)||[],u(t),r={},t.forEach((function(e,t){return r[e.buildingRoomId]=t})),p(r),Vn.setTask(n,t),se(),[2]}}))}));return function(){return e.apply(this,arguments)}}();Vn.instance.onFinish=function(){w(!1)};var re=(0,i.useRef)(null),ie=K.markedDict,ae=function(){var e=Qn((function(e){var t,r,i,a,o;return rr(this,(function(l){switch(l.label){case 0:return t=c.nb.instance.layout_container,(r=t._selected_room)?e===M?[2]:"测试结果"!==e?[3,1]:(Z&&(null===(i=Z[0])||void 0===i?void 0:i.room)===r&&(t._selected_room._layout_scheme_list=tr(Z),c.nb.emit(d.U.LayoutSchemeList,{schemeList:r._layout_scheme_list,index:r.selectIndex})),[3,5]):[2];case 1:return a=c.nb.instance.layout_graph_solver,n.methods.includes("BasicTransfer")||n.methods.includes("GroupTransfer")?[4,a.queryModelRoomsFromServer([r])]:[3,3];case 2:l.sent(),l.label=3;case 3:return[4,t.applyRoomWithSolvingMethods(r,n.methods,null)];case 4:o=l.sent(),t._selected_room._layout_scheme_list=o,c.nb.emit(d.U.LayoutSchemeList,{schemeList:r._layout_scheme_list,index:r.selectIndex}),l.label=5;case 5:return B(e),[2]}}))}));return function(t){return e.apply(this,arguments)}}(),oe=function(e){c.nb.instance.layout_container._drawing_layer_mode=e,T(e),c.nb.instance.update()},le=function(){var e=Qn((function(){return rr(this,(function(e){switch(e.label){case 0:return[4,Vn.instance.summaryResults()];case 1:return e.sent(),[4,se(!0)];case 2:return e.sent(),[2]}}))}));return function(){return e.apply(this,arguments)}}(),se=function(){var t=Qn((function(){var t,n,r,i=arguments;return rr(this,(function(a){switch(a.label){case 0:return i.length>0&&void 0!==i[0]&&i[0]?[4,Vn.instance.filterResults({marked:!0})]:[3,4];case 1:return(t=a.sent())?(n={},t.forEach((function(e){n[e.building_id]={mark_content:e.mark_content,fixed:(null==e?void 0:e.fixed)||!1}})),K.markedDict=n,[4,we.n.instance.addData(K,we.n.TestingTaskMarkTable)]):[3,3];case 2:a.sent(),X(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Yn(e,t,n[t])}))}return e}({},K)),a.label=3;case 3:return window.confirm("更新标记列表"),[3,6];case 4:return[4,we.n.instance.getDataById(e.current_task.id,we.n.TestingTaskMarkTable)];case 5:(r=a.sent()||{id:e.current_task.id,markedDict:{}})&&X(r),a.label=6;case 6:return[2]}}))}));return function(){return t.apply(this,arguments)}}(),ue=function(){var e=Qn((function(e){var t,r,i,a,o,l,s,u,f,h=arguments;return rr(this,(function(h){switch(h.label){case 0:return r=c.nb.instance.layout_container,(0,je.Ri)("authCode"),i=n.room_name,a=0,[4,we.n.instance.getDataById(e,we.n.BuildingSchemeDataTable)];case 1:return(l=null===(t=h.sent())||void 0===t?void 0:t.houseInfo)?[3,3]:[4,A.h.instance.makeHouseTestingInfoDataByBuildingId(e)];case 2:l=h.sent(),h.label=3;case 3:return(o=l)&&o.schemeXmlJson?(r.fromXmlSchemeData(o.schemeXmlJson,!0,Ie.N.LayoutLibrary),r.focusCenter(),y(e),s=r._rooms.filter((function(e){return e.roomname.includes(i)})),s.length,(u=s[a]||s[0]||null)&&(localStorage&&localStorage.setItem("layout_ai_training_current_room_data",JSON.stringify(u.exportExtRoomData())),r._selected_room=u),c.nb.instance.update(),[4,Vn.instance.readResultOnBuildingRoom(e,u)]):[3,5];case 4:f=h.sent(),W(f),(null==u?void 0:u._layout_scheme_list)&&u._layout_scheme_list.length>0&&(Q(tr(u._layout_scheme_list)),ce({value:u._layout_scheme_list[0],index:0}),c.nb.emit(d.U.LayoutSchemeList,{schemeList:u._layout_scheme_list,index:u.selectIndex})),h.label=5;case 5:return[2]}}))}));return function(t){return e.apply(this,arguments)}}(),ce=function(e){var t;if(e.value&&(null===(t=e.value)||void 0===t?void 0:t.IsRoomLayoutScheme)){var n=e.value;if(n.room){var r=n.room;r.furnitureList=[],r.selectIndex=e.index;var i=tr(n.figure_list.figure_elements);i.sort((function(e,t){var n=e.default_drawing_order-t.default_drawing_order;return 0==n?(e.min_z||0)-(t.min_z||0):n}));var a=!0,o=!1,l=void 0;try{for(var s,u=i[Symbol.iterator]();!(a=(s=u.next()).done);a=!0){var d=s.value;r.addFurnitureElement(d),r.name.indexOf("厨房")<0&&d.clearMatchedMaterials()}}catch(e){o=!0,l=e}finally{try{a||null==u.return||u.return()}finally{if(o)throw l}}r._room_entity&&r._room_entity.updateSpaceLivingInfo({force_auto_sub_area:!0})}}c.nb.instance.update()},de=function(){var e=Qn((function(){var e,t;return rr(this,(function(r){return e=0,_||(t=prompt("从当前ID开始测试, 取消则从0开始",""+Math.max(s.findIndex((function(e){return e.buildingRoomId===g})),0)),e=Math.max(0,parseInt(t)||0),e%=he),Vn.instance.onClickBuildingId=function(e){ue(e),_e(e)},Vn.setTask(n,ir,e),Vn.IsRunning=!Vn.IsRunning,w(Vn.IsRunning),[2]}))}));return function(){return e.apply(this,arguments)}}(),fe=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(s)if(null==t?void 0:t.marked){if(!ie)return;for(var n=h[g]||0,r=null;n>=0||n<s.length;){var i;if(!(r=(null===(i=s[n+=e])||void 0===i?void 0:i.buildingRoomId)||null)||ie[r])break}r&&(ue(r),_e(r))}else{var a,o=h[g]||0;if(o<0)return;var l=(null===(a=s[o+e])||void 0===a?void 0:a.buildingRoomId)||null;l&&(ue(l),_e(l))}};(0,i.useEffect)((function(){return c.nb.on_M(ke.I.ShowTestingLayoutSchemeList,t,(function(e){x(e)})),c.nb.on_M(d.U.OnAILayoutSchemeSelected,t,ce),ne(),function(){c.nb.off_M(ke.I.ShowTestingLayoutSchemeList,t)}}),[]);var he=O,pe=Math.floor(s.length/he)+1,me=R;me>=pe&&(me=pe-1);var ge=[];ir.length=0;for(var ye=0;ye<he;ye++){var be=s[me*he+ye];be&&ir.push(be)}for(var ve=0;ve<pe;ve++)ge.push(ve);var _e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g,t=ir.findIndex((function(t){return t.buildingRoomId===e}));if(re){var n=re.current;n.scrollTo({top:50*(t-(n.clientHeight-50)/50/2),behavior:"smooth"})}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{id:"RunningTestingPanel",className:a.leftPanel,style:{position:"absolute",left:"0",top:"0",zIndex:10002,paddingLeft:"0"},children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"100px",paddingTop:"10px",lineHeight:"30px",borderBottom:"1px solid #adf"},children:[(0,r.jsx)(l.A,{onClick:function(){e.onHide&&e.onHide()},children:"返回"}),(0,r.jsx)("select",{name:"pageSelect",onChange:function(e){P(~~e.target.value)},children:ge.map((function(e){return(0,r.jsxs)("option",{value:e,children:["第",e+1,"页"]},"pageSelect"+e)}))}),(0,r.jsx)("input",{type:"number",style:{width:"60px",height:"20px"},step:500,min:500,max:1e4,defaultValue:O,onChange:function(e){L(~~e.target.value)}}),(0,r.jsx)("span",{children:"个/页"}),(0,r.jsx)("br",{}),(0,r.jsx)(l.A,{onClick:function(){de()},children:_?"停止测试":"启动测试"}),(0,r.jsx)(l.A,{onClick:function(){le()},children:"汇总结果"}),(0,r.jsx)(l.A,{onClick:function(){se()},children:"更新标记列表"}),(0,r.jsx)("br",{}),n.dataset_name+" "+String(n.task_count||0).padStart(3,"0"),"-",n.dataset_size,(0,r.jsx)("div",{className:"right_text",children:(n.methods||[]).map((function(e){return jn.gl[e]||e})).join(" ")})]}),(0,r.jsx)("div",{ref:re,style:{overflow:"auto",height:"calc(100vh - 140px)",display:v?"none":"block"},children:ir.map((function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+(ie[e.buildingRoomId]?"marked":"")+" "+(e.buildingRoomId===g?"checked":"")+" ",onClick:function(){return ue(e.buildingRoomId)},children:[R*he+t+1,":",e.buildingRoomId,"    ",e.cityName,(0,r.jsx)("br",{}),"     ",e.buildingName||"","    ",e.area?e.area+"m²":""]},"buildId_"+t)}))}),v&&(0,r.jsx)("div",{className:a.schemeListModeTabs,children:F.map((function(e,t){return(0,r.jsx)("div",{className:"tab "+(M===e?"checked":""),onClick:function(){if(M==e){var n=F[(t+1)%F.length];ae(n)}else ae(e)},children:e},"show_scheme_list_mode_"+t)}))}),(0,r.jsx)("div",{style:{overflow:"auto",height:"calc(100vh - 140px)",display:v?"block":"none"},children:(0,r.jsx)(k.A,{width:320,showSchemeName:!0})})]}),(0,r.jsx)("div",{className:a.drawRoomModeTabs,children:E.map((function(e,t){return(0,r.jsx)("div",{className:"tab "+(I==e.mode?"checked":""),onClick:function(){if(I==e.mode){var n=E[(t+1)%E.length];oe(n.mode)}else oe(e.mode)},children:e.name},"drawing_mode_"+t)}))}),(0,r.jsx)("div",{className:a.rightToolBtns,children:(0,r.jsx)(l.A,{className:"topBtn",onClick:function(){te(!ee)},children:"S"})}),ee&&(0,r.jsxs)("div",{style:{width:800,height:600,position:"absolute",right:320,top:50,border:"1px solid #999",zIndex:2},children:[" ",(0,r.jsx)(pt,{defaultRoomKey:n.room_name})]}),(0,r.jsxs)("div",{className:"rightPanel "+a.rightPanel,children:[(0,r.jsxs)("div",{className:"tabs",children:[(0,r.jsx)("div",{className:"tab "+("Info"===J?"checked":""),onClick:function(){H("Info")},children:"测试信息"}),(0,r.jsx)("div",{className:"tab "+("Total"===J?"checked":""),onClick:function(){H("Total")},children:"结果汇总"})]}),"Info"===J&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"Ctrlbtns",children:[(0,r.jsxs)("div",{className:"btn_row",children:[(0,r.jsx)(l.A,{onClick:function(){return fe(-1,{marked:!0})},children:"上一个标记"}),(0,r.jsx)(l.A,{onClick:function(){return fe(1,{marked:!0})},children:"下一个标记"})]}),(0,r.jsxs)("div",{className:"btn_row",children:[(0,r.jsx)(l.A,{onClick:function(){return fe(-1)},children:"上一个户型"}),(0,r.jsx)(l.A,{onClick:function(){return fe(1)},children:"下一个户型"})]})]}),(0,r.jsx)(Xn,{roomResult:G,markedData:K,list_index:h[g]||0})]}),"Total"===J&&(0,r.jsx)(Dn,{task:n,title:"测试任务",className:"right_report"})]})]})};function or(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function lr(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function sr(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){lr(a,r,i,o,l,"next",e)}function l(e){lr(a,r,i,o,l,"throw",e)}o(void 0)}))}}function ur(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return or(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return or(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cr(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var dr=!1,fr=function(){(0,I.A)().styles;var e=ur((0,i.useState)(dr),2),t=e[0],n=e[1],a=xe().styles,o=ur((0,i.useState)(null),2),s=o[0],u=o[1],d=ur((0,i.useState)([]),2),f=d[0],h=d[1],p=ur((0,i.useState)(!1),2),m=p[0],g=p[1],y=ur((0,i.useState)([]),2),b=y[0],v=y[1],x=ur((0,i.useState)(!1),2),j=x[0],_=x[1],w=jn.gl,S=function(){var e=sr((function(){var e;return cr(this,(function(t){switch(t.label){case 0:return[4,we.n.instance.getTestingDatasetList()];case 1:return e=t.sent(),v(e),[2,e]}}))}));return function(){return e.apply(this,arguments)}}(),k=function(){var e=sr((function(){var e,t,n,r,i,a,o,l;return cr(this,(function(s){switch(s.label){case 0:return[4,S()];case 1:return e=s.sent(),[4,we.n.instance.getAll(we.n.TestingTaskTable)];case 2:t=s.sent()||[],n=!0,r=!1,i=void 0;try{for(a=function(){var t=l.value,n=e.find((function(e){return e.id===t.dataset_id}));t.dataset_name=(null==n?void 0:n.name)||t.dataset_name,t.dataset_size=(null==n?void 0:n.count)||0},o=t[Symbol.iterator]();!(n=(l=o.next()).done);n=!0)a()}catch(e){r=!0,i=e}finally{try{n||null==o.return||o.return()}finally{if(r)throw i}}return t.sort((function(e,t){return t.create_date.localeCompare(e.create_date)})),h(t||[]),[2]}}))}));return function(){return e.apply(this,arguments)}}(),A=function(){var e=sr((function(){var e,t=arguments;return cr(this,(function(n){switch(n.label){case 0:return e=(e=t.length>0&&void 0!==t[0]?t[0]:null)||s,[4,we.n.instance.removeData(e.id,we.n.TestingTaskTable)];case 1:return n.sent(),[4,k()];case 2:return n.sent(),u(null),[2]}}))}));return function(){return e.apply(this,arguments)}}();return(0,i.useEffect)((function(){c.nb.on_M(ke.I.ShowTestingTaskListPanel,"TestingTaskList",(function(e){n(dr=null!==e?e:!dr),dr&&k()}))}),[]),t?j&&s?(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(ar,{current_task:s,onHide:function(){return _(!1)}})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{id:"TestingTaskListPanel",className:a.leftPanel,style:{position:"absolute",left:"0",top:"0",zIndex:9999,paddingLeft:"0"},children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"75px",paddingTop:"10px",lineHeight:"32px"},children:[(0,r.jsx)(l.A,{onClick:function(){c.nb.emit_M(ke.I.ShowTestingDatasetListPanel,!0)},children:"数据集列表"}),(0,r.jsx)(l.A,{onClick:function(){return g(!0)},children:"创建任务"}),(0,r.jsx)(l.A,{onClick:function(){return A()},children:"删除任务"}),(0,r.jsx)("br",{})]}),(0,r.jsx)("div",{style:{overflow:"auto",height:"calc(100vh - 150px)"},children:f.map((function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+(e.id===(null==s?void 0:s.id)?"checked":""),onClick:function(){u(e)},onDoubleClick:function(){u(e),_(!0)},title:"双击进入测试模式",children:[t+1,"、 ",e.dataset_name+" "+String(e.task_count||0).padStart(3,"0"),"-",e.dataset_size,(0,r.jsx)("div",{className:"right_text",children:e.create_date||""})," ",(0,r.jsx)("br",{}),"      ",e.room_name,(0,r.jsx)("div",{className:"right_text",children:(e.methods||[]).map((function(e){return w[e]||e})).join(" ")})]},"task_"+t)}))})]}),s&&(0,r.jsx)(En,{current_task:s,taskList:f,onHide:function(){u(null)},onEnterTask:function(){_(!0)}}),m&&(0,r.jsx)(Nn,{taskList:f,datasetNameList:b,onHide:function(){g(!1),k()}})]}):(0,r.jsx)(r.Fragment,{})},hr=n(52898),pr=n(15364),mr=n(70766);function gr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function yr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return gr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return gr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var br=function(){var e=(0,I.A)().styles,t=yr((0,i.useState)(null),2),n=t[0],a=t[1],o=yr((0,i.useState)(0),2),l=o[0],s=o[1];return(0,i.useEffect)((function(){c.nb.on(d.U.LayoutGraphTestingRightPanel,(function(e){a(e)}))}),[]),(0,r.jsxs)("div",{className:e.rightPanel,style:{zIndex:n?5:-2},children:[(0,r.jsx)(hr.A,{}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"显示控制"}),(0,r.jsxs)("select",{id:"select_visible_range_type",onChange:null==n?void 0:n.onSelectVisibleChange,children:[(0,r.jsx)("option",{value:"7",children:"所有元素"}),(0,r.jsx)("option",{value:"1",children:"地面元素"}),(0,r.jsx)("option",{value:"2",children:"悬挂元素"}),(0,r.jsx)("option",{value:"4",children:"顶部元素"})]})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"更新布局结果"}),(0,r.jsx)("button",{onClick:null==n?void 0:n.onUpdateAiLayoutBtnClick,children:"更新"})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"微调"}),(0,r.jsx)("button",{onClick:null==n?void 0:n.onFinetuning,children:"微调"})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"显示评分器"}),(0,r.jsx)("button",{onClick:function(){c.nb.emit(d.U.ShowLayoutScoreDialog,!0)},children:"显示&更新"})]}),(0,r.jsx)("div",{className:"row_container",style:{height:300},children:Object.keys(pr.Pv._layer_visible).map((function(e,t){return(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:pr.Pv._layer_visible[e].name}),(0,r.jsx)(mr.A,{style:{marginLeft:10},defaultChecked:pr.Pv._layer_visible[e].visible,onChange:function(t){pr.Pv._layer_visible[e].visible=t.target.checked,s(l+1),c.nb.instance.update()}})]},"item"+t)}))})]},"layout_graph_"+l)};function vr(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function xr(){var e=vr(["\n      display: flex;\n    "]);return xr=function(){return e},e}function jr(){var e=vr(["\n        /* background: #f2f2f2; */\n      padding: 14px 14px;\n      &:hover {\n          background-color: #d9d9d9;\n      }\n      &.checked {\n        background-color: #d9d9ff;\n      }\n    "]);return jr=function(){return e},e}var _r=(0,se.rU)((function(e){var t=e.css;return{menu_container:t(xr()),menu:t(jr())}}));function wr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Sr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return wr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ir={};function Ar(e){var t=_r().styles,n=kr((0,i.useState)(Ir),2),a=n[0],o=n[1],l=kr((0,i.useState)(""),2),s=l[0],u=l[1],d=function(e,t){e.subList&&(Ir[e.label]?delete Ir[e.label]:Ir[e.label]=!0,o(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Sr(e,t,n[t])}))}return e}({},Ir))),e.command_name&&c.nb.RunCommand(e.command_name),u(e.label)};return e.labelList&&0!=e.labelList.length?(0,r.jsx)("div",{children:e.labelList.map((function(e,n){return(0,r.jsxs)("div",{id:"left-menu-room-list-"+n,className:t.menu+" "+(e.label===s?"checked":""),"data-room_name":e.name,onClick:function(){return d(e)},children:[(0,r.jsx)("div",{className:"label-text",children:e.label}),a&&a[e.label]&&e.subList&&e.subList.map((function(e,n){return(0,r.jsx)("div",{className:t.menu+" "+(e.label===s?"checked":""),"data-room_name":e.name,onClick:function(t){t.stopPropagation(),d(e)},children:e.label},"subItem"+n)}))]},n)}))}):(0,r.jsx)(r.Fragment,{})}function Tr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Nr(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function Cr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Tr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Tr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Or(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var Lr=(0,Ue.observer)((function(){var e=(0,a.A)().styles,t=Cr((0,i.useState)(!1),2),n=t[0],h=t[1],p=Cr((0,i.useState)(!1),2),m=p[0],g=p[1],y=Cr((0,i.useState)(!1),2),b=y[0],v=y[1],x=Cr((0,i.useState)(!1),2),j=x[0],_=x[1],w=Cr((0,i.useState)(null),2),k=w[0],I=w[1],A=Cr((0,i.useState)(!1),2),T=A[0],N=A[1],C=Cr((0,i.useState)(),2),O=C[0],L=C[1],D=Cr(o.A.useMessage(),2),R=D[0],P=D[1],E="trainingMessage";c.nb.NewApp(f.I.AppName),c.nb.UseApp(f.I.AppName),(0,i.useEffect)((function(){m&&c.nb.instance.update()}),[m]),(0,i.useEffect)((function(){c.nb.instance&&(c.nb.t=function(e){return e},c.nb.instance.initialized||(c.nb.instance.init(),c.nb.instance.prepare().then((function(){c.nb.instance.update()}))),c.nb.instance.bindCanvas(document.getElementById("room_canvas")),c.nb.instance.update()),c.nb.instance&&(c.nb.on(d.U.LoadingProgress,(function(e){"start"===e.evt?h(!0):"end"===e.evt&&h(!1)})),c.nb.on(d.U.TrainingLabelsListHandle,(function(e){L(e)})),c.nb.on(d.U.IssueRoomSelected,(function(e){I(e)})),c.nb.on(d.U.TrainingMessage,(function(e){e.key=E,e.duration=3,e.style={marginTop:"4vh",zIndex:99},R.open(e)})),c.nb.on(d.U.setLayoutScoreConfigPanelVisible,(function(e){N(e)})))}),[]);var F=function(){var e,t=(e=function(e){return Or(this,(function(t){return e.preventDefault(),g(!1),localStorage&&k&&(localStorage.setItem("layout_ai_training_current_room_data",JSON.stringify(k.exportExtRoomData())),c.nb.RunCommand("LayoutGraphTesting")),[2]}))},function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){Nr(a,r,i,o,l,"next",e)}function l(e){Nr(a,r,i,o,l,"throw",e)}o(void 0)}))});return function(e){return t.apply(this,arguments)}}();return(0,r.jsxs)("div",{className:e.root,children:[(0,r.jsx)(S,{updateIssueReportVisible:g,updateLayoutBatchTestVisible:v,updateModelLightConfigVisible:_}),(0,r.jsxs)("div",{className:e.content,style:b?{display:"none"}:{display:"block"},children:[(0,r.jsx)("div",{className:e.left_panel_container,children:(0,r.jsxs)("div",{className:e.left_panel_menu_box,children:[(0,r.jsx)(Ar,{labelList:O}),(0,r.jsx)("div",{id:"side_list_div",className:e.left_panel_side_list})]})}),(0,r.jsx)(Be,{}),(0,r.jsx)(fr,{}),(0,r.jsxs)("div",{id:"body_container",className:e.canvas_pannel,children:[m&&(0,r.jsx)(l.A,{className:null!=k?e.enableAnalyseButton:e.disableAnalyseButton,disabled:null==k,type:"primary",onClick:F,children:"计算空间布局"}),(0,r.jsx)("div",{style:{position:"absolute"},children:(0,r.jsx)(le.A,{})}),(0,r.jsx)("canvas",{id:"room_canvas",className:"canvas"})]}),(0,r.jsx)(J,{}),(0,r.jsx)(oe,{}),(0,r.jsx)(qe,{}),(0,r.jsx)(br,{}),T&&(0,r.jsx)(pt,{}),j&&(0,r.jsx)(xn,{}),(0,r.jsx)("div",{id:"right_side_panel",className:e.right_side_panel}),(0,r.jsx)(u.Nt,{})]}),n&&(0,r.jsx)("div",{className:e.loading,children:(0,r.jsx)(s.A,{size:"large"})}),P]})}))},52898:function(e,t,n){var r=n(13274),i=n(33313),a=n(41594),o=n(27347),l=n(88934),s=n(41980),u=n(55111),c=n(62837);function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function f(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}t.A=function(){(0,i.A)().styles;var e=h((0,a.useState)(!1),2),t=e[0],n=e[1],d=h((0,a.useState)([]),2),m=d[0],g=d[1],y=o.nb.t,b=((0,a.useRef)(null),o.nb.instance.layout_container),v=function(){var e,t=(e=function(){var e,t;return p(this,(function(n){return(e=b._selected_room)&&(t=u.lj.ComputeScoreInRoom(e),g(t)),[2]}))},function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){f(a,r,i,o,l,"next",e)}function l(e){f(a,r,i,o,l,"throw",e)}o(void 0)}))});return function(){return t.apply(this,arguments)}}(),x=function(e){n(e)};return(0,a.useEffect)((function(){return o.nb.on(l.U.ShowLayoutScoreDialog,(function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];x(e),e&&v()})),o.nb.on_M(l.U.UpdateLayoutScore,"LayoutScoreDialog",(function(){v()})),function(){}}),[]),(0,r.jsx)(r.Fragment,{children:t&&(0,r.jsx)(s._w,{title:y("布局评分器"),right:250,width:265,height:600,resizable:!0,draggable:!0,onClose:function(){x(!1)},bodyStyle:{background:"#ffffff",border:"0",boxShadow:"0"},children:(0,r.jsx)(c.A,{layoutScoreList:m,style:0})})})}}}]);