import { LightingRuler } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { AutoLightingRulerType } from "../AutoLightingRuler";

import { CeilingSlotLightHandler } from "./CeilingSlotLightHandler";
import { HallwayLightHandler } from "./HallwayLightHandler";
import { RoomCategoryLightHandler } from "./RoomCategoryLightHandler";
import { RoomLightHandler } from "./RoomLightHandler";
import { RoomWindowLightHandler } from "./RoomWindowLightHandler";
import { RoomAreaWindowDoorLightHandler } from "./RoomAreaWindowDoorLightHandler";
import { LivingEdgeLightHandler } from "./LivingEdgeLightHandler";

export interface AutoLightingRulerHandler {
    handle(ruler: LightingRuler): Promise<TFillLightEntity[]>;
}

const handlers = new Map<AutoLightingRulerType, AutoLightingRulerHandler>();
let isInit: Boolean = false;
const handlerMappings: [AutoLightingRulerType[], new () => AutoLightingRulerHandler][] = [
    [[
        AutoLightingRulerType.CeilingSlotLight
    ], CeilingSlotLightHandler],
    [[
        AutoLightingRulerType.CorridorLight, 
        AutoLightingRulerType.CorridorDayLight
    ], HallwayLightHandler],
    [[
        AutoLightingRulerType.SofaLight,
        AutoLightingRulerType.TVCabinetLight,
        AutoLightingRulerType.TableLight,
        AutoLightingRulerType.BedFootLight,
        AutoLightingRulerType.BedFootSideLight,
        AutoLightingRulerType.BedCenterLight,
        AutoLightingRulerType.DeskLight,
        AutoLightingRulerType.TeaTableLight,
        AutoLightingRulerType.SofaSideLight
    ], RoomCategoryLightHandler],
    [[
        AutoLightingRulerType.KitchenLight, 
        AutoLightingRulerType.BathroomLight
    ], RoomLightHandler],
    [[
        AutoLightingRulerType.BedroomWindowLight,
        AutoLightingRulerType.KitchenWindowLight,
        AutoLightingRulerType.BathroomWindowLight,
        AutoLightingRulerType.StudyroomWindowLight,
        AutoLightingRulerType.TearoomWindowLight
    ], RoomWindowLightHandler],
    [[
        AutoLightingRulerType.LivingWindowLight,
        AutoLightingRulerType.DingingWindowLight,
        AutoLightingRulerType.LivingDoorHoleLight,
        AutoLightingRulerType.BalconyDoorLight
    ], RoomAreaWindowDoorLightHandler],
    [[
        AutoLightingRulerType.LivingEdgeLight
    ], LivingEdgeLightHandler]
];

export class AutoLightingRulerFactory {
    public static init(): void {
        // console.log("AutoLightingRulerFactory init");
        if(isInit) return;
        for (const [types, HandlerClass] of handlerMappings) {
            const handler = new HandlerClass();
            for (const type of types) {
                this.registerHandler(type, handler);
            }
        }
        isInit = true;
    }
    public static registerHandler(type: AutoLightingRulerType, handler: AutoLightingRulerHandler): void {
        handlers.set(type, handler);
    }

    public static getHandler(type: AutoLightingRulerType): AutoLightingRulerHandler {
        return handlers.get(type);
    }
}