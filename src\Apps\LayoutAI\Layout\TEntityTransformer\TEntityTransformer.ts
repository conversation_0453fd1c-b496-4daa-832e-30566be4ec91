import { T_MoveOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_MoveOperationInfo";
import { I_SelectedTarget } from "../TEntitySelector/TEntitySelector";
import { TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { Vector3, Vector3Like } from "three";
import { TBaseEntity } from "../TLayoutEntities/TBaseEntity";
import { TBaseGroupEntity } from "../TLayoutEntities/TBaseGroupEntity";
import { TFurnitureEntity } from "../TLayoutEntities/TFurnitureEntity";
import { AI_PolyTargetType, DrawingFigureMode } from "../IRoomInterface";
import { ZRect } from "@layoutai/z_polygon";
import { TSubSpaceAreaEntity } from "../TLayoutEntities/TSubSpaceAreaEntity";
import { RoomSubAreaService } from "../../Services/Basic/RoomSubAreaService";
import { TMatchingOrdering } from "../../Services/MaterialMatching/TMatchingOrdering";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import { TBaseSpaceEntity } from "../TLayoutEntities/TSpaceEntities/TBaseSpaceEntity";
import { baseSpaceService } from "../../Services/BaseSpace/BaseSpaceService";


export class TEntityTransformer
{
    protected _container:TLayoutEntityContainer;

    /**
     *   形状改变后的后处理函数
     */
    static OnDoneTransformFuncs : {[key:string]:(entity:TBaseEntity,combination_entities?:TBaseEntity[])=>void} = {};
    constructor(container:TLayoutEntityContainer)
    {
        this._container = container;
    }

    get container()
    {
        return this._container;
    }

    get manager()
    {
        return this._container.manager;
    }

    update()
    {
        this.container.manager.update();
    }
    get selected_target()
    {
        return this.container.entity_selector.selected_target;
    }
    // 旋转s
    rotate() {

        if (!this.selected_target.selected_rect) return;

        let _origin_shape_rect = this.selected_target.selected_rect.clone();

        let target_rect = this.selected_target.selected_rect;
        let r_center = target_rect.rect_center.clone();
        let operation_info = new T_MoveOperationInfo(this.manager as any);
        // 创建一个绕 Z 轴旋转 45 度的旋转
        if (this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'Furniture'
            || this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'Group'
            || this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'BaseGroup') {
            let axis = new Vector3(0, 0, 1);
            let angle = -Math.PI / 4;  // 45 度
            target_rect.nor.applyAxisAngle(axis, angle);

            target_rect.rect_center = r_center;
            if (this.selected_target.selected_entity.rect) {
                this.selected_target.selected_entity.rect.rect_center = r_center;
            }
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TFurnitureEntity;
            if(entity)
            {
                entity.figure_element.updateMesh3D();
                entity.update();
            }
   
        }

        if (this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'Door' || this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'Window') {
            let matching = [
                {
                    u_dv_flag: 1,
                    nor: new Vector3(0, 1, 0)
                },
                {
                    u_dv_flag: -1,
                    nor: new Vector3(0, 1, 0)
                },
                {
                    u_dv_flag: 1,
                    nor: new Vector3(0, -1, 0)
                },
                {
                    u_dv_flag: -1,
                    nor: new Vector3(0, -1, 0)
                },
            ]
            let matching2 = [
                {
                    u_dv_flag: 1,
                    nor: new Vector3(1, 0, 0)
                },
                {
                    u_dv_flag: -1,
                    nor: new Vector3(1, 0, 0)
                },
                {
                    u_dv_flag: 1,
                    nor: new Vector3(-1, 0, 0)
                },
                {
                    u_dv_flag: -1,
                    nor: new Vector3(-1, 0, 0)
                },
            ]
            let r_center = target_rect.rect_center;
            let nor = target_rect.nor;
            let u_dv_flag = target_rect.u_dv_flag;
            let index = matching.findIndex(item => {
                return item.nor.dot(nor) > 0.9 && item.u_dv_flag === u_dv_flag
            });
            let obj = null;
            // 如果在第一个映射表中找到匹配的对象
            if (index !== -1) {
                // 如果匹配的对象是数组的最后一个元素，返回数组的第一个元素
                if (index === matching.length - 1) {
                    obj = matching[0];
                }
                // 否则，返回数组中的下一个元素
                else {
                    obj = matching[index + 1];
                }
            } else {
                // 如果在第一个映射表中没有找到匹配的对象，那么在第二个映射表中查找
                let index1 = matching2.findIndex(item => {
                    return item.nor.dot(nor) > 0.9 && item.u_dv_flag === u_dv_flag
                });
                // 如果在第二个映射表中找到匹配的对象
                if (index1 !== -1) {
                    // 如果匹配的对象是数组的最后一个元素，返回数组的第一个元素
                    if (index1 === matching2.length - 1) {
                        obj = matching2[0];
                    }
                    // 否则，返回数组中的下一个元素
                    else {
                        obj = matching2[index1 + 1];
                    }
                }
            }
            target_rect._u_dv_flag = obj.u_dv_flag;
            target_rect._nor = obj.nor;
            target_rect.rect_center = r_center;
            target_rect.updateRect();
        }

        if (this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'Group' || this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'BaseGroup') {
            if (this.selected_target.selected_rect && TBaseEntity.get_polygon_type(this.selected_target.selected_rect) === 'BaseGroup') {
                let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TBaseGroupEntity;
                if(entity)
                {
                    this.selected_target.selected_combination_entitys = entity.combination_entitys;
                }
            }
            operation_info._history_info.previous_in_group_rects = this.selected_target.selected_combination_entitys.map(entity => entity.clone());
            let rects = this.selected_target.selected_combination_entitys;
            for (let t_entity of rects) {
                let pp = t_entity.rect._attached_elements['group_p_center'] as Vector3;
                let p_nor = t_entity.rect._attached_elements['group_p_nor'] as Vector3;

                let t_rect_center = target_rect.unproject(pp);  // 图元的中心坐标
                let t_rect_nor = target_rect.unproject(p_nor);  // 图元将要移动到的坐标

                t_rect_nor.sub(t_rect_center).normalize();   //算出来图元在世界坐标的法向

                t_entity.rect.nor = t_rect_nor;
                t_entity.rect.rect_center = t_rect_center;
                if (t_entity.matched_rect) {
                    t_entity.matched_rect.nor = t_rect_nor;
                    t_entity.matched_rect.rect_center = t_rect_center;
                }
            }
            operation_info._history_info.current_in_group_rects = rects.map(rect => rect.clone());
            operation_info.target_in_group_entitys = this.selected_target.selected_combination_entitys;
        }

        operation_info.target_rect = target_rect;
        operation_info._history_info.previous_rect = _origin_shape_rect.clone();
        operation_info._history_info.current_rect = target_rect.clone();
        operation_info._history_info.id = 0;
        operation_info._furniture_entities = [...this.container._furniture_entities];
        this.manager.appendOperationInfo(operation_info);

    }

    // 镜像
    flip() {
        if (!this.selected_target.selected_rect) return;

        let _origin_shape_rect = this.selected_target.selected_rect.clone();

        let target_rect = this.selected_target.selected_rect;

        target_rect._u_dv_flag = -target_rect.u_dv_flag;
        target_rect.updateRect();

        let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TFurnitureEntity;

        if(entity)
        {
            let operation_info = new T_MoveOperationInfo(this.manager as any);
            operation_info.target_rect = target_rect;
            operation_info._history_info.previous_rect = _origin_shape_rect.clone();
            operation_info._history_info.current_rect = target_rect.clone();
            operation_info._history_info.id = 0;
    
            this.manager.appendOperationInfo(operation_info);
            if (entity.matched_rect) {
                entity.rect.rect_center = entity.matched_rect.rect_center;
                entity.rect.updateRect();
            }
            entity.update();
        }

    }

    // 上下镜像
    flipVertical() {
        if (!this.selected_target.selected_rect) return;

        let _origin_shape_rect = this.selected_target.selected_rect.clone();
        let r_center = this.selected_target.selected_rect.rect_center.clone();
        let target_rect = this.selected_target.selected_rect;

        target_rect.nor.set(-target_rect.nor.x, -target_rect.nor.y, -target_rect.nor.z);
        target_rect._u_dv_flag = -target_rect.u_dv_flag;
        target_rect.updateRect();
        target_rect.rect_center = r_center;
        let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TFurnitureEntity;

        if(entity)
        {
            let operation_info = new T_MoveOperationInfo(this.manager as any);
            operation_info.target_rect = target_rect;
            operation_info._history_info.previous_rect = _origin_shape_rect.clone();
            operation_info._history_info.current_rect = target_rect.clone();
            operation_info._history_info.id = 0;
    
            this.manager.appendOperationInfo(operation_info);
            entity.update();
            if (entity.matched_rect) {
                entity.rect.rect_center = entity.matched_rect.rect_center.clone();
                entity.rect.updateRect();
            }
        }

    }

    onDimensionInput(num:number, origin_num:number, _nor_val:Vector3Like)
    {
        let target_rect = this.selected_target.selected_rect;
        if (this.selected_target.selected_transform_element) {
            this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_transform_element._target_rect);
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TBaseGroupEntity;
            if(!entity) return;
            if (entity.type === "BaseGroup") {
                let combinationRects: TFurnitureEntity[] = entity.recursivelyAddCombinationRects();
                this.selected_target.selected_transform_element.recordOriginCombinationRect(combinationRects);
            }
        }
        // num是编辑的后的值
        // edit_num是差值
        let edit_num = origin_num - num;
        // if(edit_num < 0) return;
        let t_center = { x: target_rect.rect_center.x + _nor_val.x * edit_num, y: target_rect.rect_center.y + _nor_val.y * edit_num, z: target_rect.rect_center.z };

        // 组合内图元移动
        target_rect.rect_center = t_center;
        if (TBaseEntity.get_polygon_type(target_rect) === AI_PolyTargetType.BaseGroup) {
            let groupEntity = TBaseEntity.getEntityOfRect(target_rect) as TBaseGroupEntity;
            if(groupEntity)
            {
                groupEntity._combination_entitys.forEach((entity) => {
                    entity.rect.rect_center = { x: entity.rect.rect_center.x + _nor_val.x * edit_num, y: entity.rect.rect_center.y + _nor_val.y * edit_num, z: entity.rect.rect_center.z };
                });
            }

        }
        if (TBaseEntity.get_polygon_type(target_rect) === AI_PolyTargetType.Group) {
            if (this.selected_target.selected_combination_entitys.length > 0) {
                for (let t_entity of this.selected_target.selected_combination_entitys) {
                    t_entity.rect.rect_center = { x: t_entity.rect.rect_center.x + _nor_val.x * edit_num, y: t_entity.rect.rect_center.y + _nor_val.y * edit_num, z: t_entity.rect.rect_center.z };
                }
            }
        }

        if (this.selected_target.selected_transform_element) {
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager as any);
            if (info) {
                this.manager.appendOperationInfo(info);
            }
        }
        target_rect.updateRect();
        this.update();
        
    }

    scalebyRect(targetRect:ZRect,srcRect:ZRect=null)
    {
        if(!this.selected_target.selected_entity)
        {
            return;
        }

        this.selected_target.selected_entity.rect.copy(targetRect);
        this.selected_target.selected_entity.update();
    }

    static onDoneTransform(entity:TBaseEntity,combination_entities?:TBaseEntity[])
    {
        if(!entity) return;
        let func = TEntityTransformer.OnDoneTransformFuncs[entity.type];
        if(func)
        {
            func(entity,combination_entities);
        }
    }
}

TEntityTransformer.OnDoneTransformFuncs[TBaseGroupEntity.EntityType] = (entity:TBaseEntity,combination_entities?:TBaseEntity[])=>{
    if(!(entity instanceof TBaseGroupEntity)) return;
    if(combination_entities)
    {
        combination_entities.forEach((sub_entity)=>{
            TBaseGroupEntity.recordGroupRectData(sub_entity as TFurnitureEntity,entity);
        })
    }
}
TEntityTransformer.OnDoneTransformFuncs[TSubSpaceAreaEntity.EntityType] = (entity:TBaseEntity,combination_entities?:TBaseEntity[])=>{
    if(!(entity instanceof TSubSpaceAreaEntity)) return;
    RoomSubAreaService.getInstance().updateSubAreaLayoutScheme(entity,true);
}
TEntityTransformer.OnDoneTransformFuncs[TBaseSpaceEntity.EntityType] = (entity:TBaseEntity,combination_entities?:TBaseEntity[])=>{
    if(!(entity instanceof TBaseSpaceEntity)) return;
    baseSpaceService.spiltIntoSubSpaces(entity);
}

TEntityTransformer.OnDoneTransformFuncs[TFurnitureEntity.EntityType] =  (entity:TBaseEntity,combination_entities?:TBaseEntity[])=>{
    if(!(entity instanceof TFurnitureEntity)) return;
    
    let figure_element = entity.figure_element;
    // 拉伸后同步rect的长宽
    if(figure_element?.matched_rect && ((LayoutAI_App.instance as TAppManagerBase).layout_container.drawing_figure_mode !== DrawingFigureMode.Figure2D))
    {
        figure_element.rect._w = figure_element?.matched_rect?.w;
        figure_element.rect._h = figure_element?.matched_rect?.h;
    }
    if(figure_element && figure_element.getMaterialID())
    {
        TMatchingOrdering.instance.sortMaterialsOfFigureElement(figure_element, figure_element._candidate_materials, '');
    }
    LayoutAI_App.emit_M(EventName.FigureElementSelected, figure_element);
    LayoutAI_App.emit(LayoutAI_Events.UpdateFigureElement, figure_element);
}
