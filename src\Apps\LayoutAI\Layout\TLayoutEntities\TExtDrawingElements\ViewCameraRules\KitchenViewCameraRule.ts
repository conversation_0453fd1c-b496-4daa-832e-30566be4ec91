// src/Apps/LayoutAI/Services/ViewCameraRules/SofaViewCameraRule.ts
import { ZRect } from "@layoutai/z_polygon";
import { BaseViewCameraRule } from "./BaseViewCameraRule";
import { TRoomEntity } from "../../TRoomEntity";
import { TViewCameraEntity } from "../TViewCameraEntity";
import { Matrix4, PerspectiveCamera, Vector3, Vector3Like, Vector4 } from "three";
import { TRoomShape } from "../../../TRoomShape";
import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../TBaseGroupEntity";
import { BedRoomViewCameraRule } from "./BedRoomViewCameraRule";
export class KitchenViewCameraRule extends BaseViewCameraRule {
    static test_rect = new ZRect(100, 100);
    static generateViewCamera(room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}, subArea?: ZRect): TViewCameraEntity[] {
        let main_rect = room_entity._main_rect;
        let camera = new PerspectiveCamera(75, 3.0 / 4.0, 300, 20000);
        let view_cameras: TViewCameraEntity[] = [];
        let room_poly = room_entity._room_poly.clone();
        let stove = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], ["炉灶地柜"]))[0];
        let stove_rect = null;
        if(stove) {
            stove_rect = stove.matched_rect || stove.rect;
        }
        TRoomShape.optimizePoly(room_poly);

        const min_wall_length = 600;
        const min_hallway_length = 1800;
        let wall_edges = main_rect.edges.filter((edge) => edge.length > min_wall_length);
        let wins = room_entity._room.windows.filter((win) => win.length > 1300);
        if (wins.length > 0) {
            wins.sort((a, b) => b.length - a.length);
            wall_edges.sort((a, b) => Math.abs(a.projectEdge2d(wins[0].rect.rect_center).y) - Math.abs(b.projectEdge2d(wins[0].rect.rect_center).y))
        }
        wall_edges.forEach((edge) => {
            if (!edge) return;
            if(!stove_rect) {
                return;
            }
            if (edge.nor.dot(stove_rect.nor) > 0.9) {
                return;
            }
            let int_data = room_poly.getRayIntersection(edge.unprojectEdge2d({ x: edge.length / 2, y: -5 }), edge.nor.clone().negate());
            if (!int_data || !int_data.point) return;
            let target_center = edge.center;
            let dist_to_front_wall = int_data.point.clone().sub(target_center).length();
            let max_dist = dist_to_front_wall + min_hallway_length;
            let view_camera_name = '厨房-朝向炉灶正面';
            let _target: string[] = ['炉灶'];
            let _dir: string = '正面';
            // 左右两侧使用墙内
            if(Math.abs(edge.nor.dot(stove_rect.nor)) < 0.1)
            {
                max_dist = dist_to_front_wall + 600;   // 聚焦模式下, 相机离墙300mm 
                view_camera_name = '厨房-朝向炉灶侧面';
                _dir = '侧面';
            }
            let t_dist = Math.min(max_dist, edge.length);
            let t_rect = new ZRect(500, 500);
            t_rect.nor = edge.nor;
            t_rect.zval = 1400; // 默认高度还是高一些1400mm更合理
            let dist_step = 200;

            let iter = 20;
            while (iter--) {
                let pos = edge.unprojectEdge2d({ x: edge.length / 2, y: -t_dist });

                t_rect.rect_center = pos;

                TViewCameraEntity.updateCameraByRect(camera, t_rect);

                let mvp_matrix = camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse);


                let s_p0 = TViewCameraEntity.cameraProjectPos(mvp_matrix, edge.v0.pos);

                let xx = Math.abs(s_p0.x);
                let yy = Math.abs(s_p0.y);

                let ml = Math.max(xx, yy);

                if (ml < 0.40 || t_dist > max_dist - 10) {
                    break;
                }

                t_dist += dist_step;
                if (t_dist > max_dist) t_dist = max_dist;
            }
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(t_rect);
            view_camera._target = _target;
            view_camera._dir = _dir;
            view_camera.name = view_camera_name;
            view_camera._is_focus_mode = (options.no_focus_mode || false) ? false : true;
            if (!view_camera.is_focus_mode) {
                view_camera.near = 600;
            }
            view_camera._view_center = main_rect.rect_center;
            view_camera._room_entity = room_entity;
            view_camera.ukey = `kitchen_view_camera_${edge._edge_id}`;
            view_cameras.push(view_camera);
            view_camera._main_rect = main_rect;
            view_camera.fov = 75;
            view_camera.hideFurnitures = this.hideFurnitures;
        })
        return view_cameras;
    }
}