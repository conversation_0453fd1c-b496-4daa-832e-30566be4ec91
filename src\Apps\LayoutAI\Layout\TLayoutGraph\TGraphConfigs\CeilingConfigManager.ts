import { DefaultCeilingType, DefaultCeilingLayerTemplate, I_CeilingLayerData, cloneSectionData, DefaultSection, DefaultSectiontype } from "../../IRoomInterface";

/**
 * 自定义吊顶配置接口
 */
export interface ICustomCeilingConfig extends I_CeilingLayerData {
    englishId: string;
    name: string;
}

/**
 * 吊顶配置管理器
 * 负责管理自定义吊顶配置的增删改查
 */
export class CeilingConfigManager {
    private static readonly STORAGE_KEY = 'ceiling_configs';
    private static readonly DEFAULT_CONFIG_NAME = '默认配置';
    public static _idCounter = 0;
    
    /**
     * 获取所有保存的吊顶配置
     */
    public static getAllConfigs(): { [key: string]: ICustomCeilingConfig } {
        const stored = localStorage.getItem(this.STORAGE_KEY);
        if (!stored) {
            return {};
        }
        
        const configs = JSON.parse(stored);
        return configs || {};
    }

    /**
     * 获取所有自定义配置（不包括默认配置）
     */
    public static getCustomConfigs(): { [key: string]: ICustomCeilingConfig } {
        const allConfigs = this.getAllConfigs();
        const customConfigs: { [key: string]: ICustomCeilingConfig } = {};
        
        // 过滤掉默认配置，只返回自定义配置
        Object.keys(allConfigs).forEach(key => {
            const config = allConfigs[key];
            if (config.name !== this.DEFAULT_CONFIG_NAME) {
                customConfigs[key] = config;
            }
        });
        
        return customConfigs;
    }

    /**
     * 获取所有可用的吊顶类型（包括默认和自定义）
     */
    public static getAllCeilingTypes(): { [key: string]: string } {
        const customConfigs = this.getCustomConfigs();
        const types: { [key: string]: string } = {};

        // 添加默认类型
        Object.keys(DefaultCeilingLayerTemplate).forEach(key => {
            types[key] = key;
        });

        // 添加自定义类型
        Object.keys(customConfigs).forEach(key => {
            const config = customConfigs[key];
            types[key] = config.name || key;
        });

        return types;
    }

    /**
     * 获取吊顶列表（用于下拉选择）
     * 返回格式：{ [显示名称]: 配置ID }
     */
    public static getCeilingList(): { [key: string]: string } {
        const customConfigs = this.getCustomConfigs();
        const list: { [key: string]: string } = {};

        // 添加默认配置到列表最前面
        list[this.DEFAULT_CONFIG_NAME] = 'default';

        // 添加自定义配置
        Object.keys(customConfigs).forEach(key => {
            const config = customConfigs[key];
            list[config.name || key] = key;
        });

        return list;
    }

    /**
     * 保存吊顶配置
     */
    public static saveConfig(name: string, config: I_CeilingLayerData): { success: boolean; message: string } {
        if (!name || name.trim() === '') {
            return { success: false, message: '吊顶名称不能为空' };
        }

        // 不允许保存默认配置
        if (name === this.DEFAULT_CONFIG_NAME) {
            return { success: false, message: '不能保存默认配置' };
        }

        const configs = this.getAllConfigs();
        const englishId = this.generateEnglishId(name);
        
        // 检查ID是否重复
        if (configs[englishId]) {
            return { success: false, message: `吊顶ID "${englishId}" 已存在，请使用其他名称` };
        }

        configs[englishId] = {
            ...config,
            name: name,
            englishId: englishId
        };

        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs));
        return { success: true, message: '配置保存成功' };
    }

    /**
     * 保存吊顶配置到指定ID
     */
    public static saveConfigToId(englishId: string, name: string, config: I_CeilingLayerData): { success: boolean; message: string } {
        if (!name || name.trim() === '') {
            return { success: false, message: '吊顶名称不能为空' };
        }

        // 不允许保存默认配置
        if (name === this.DEFAULT_CONFIG_NAME) {
            return { success: false, message: '不能保存默认配置' };
        }

        const configs = this.getAllConfigs();
        
        // 如果ID不存在，检查名称是否重复
        if (!configs[englishId]) {
            const existingConfig = Object.values(configs).find(c => c.name === name);
            if (existingConfig) {
                return { success: false, message: `吊顶名称 "${name}" 已存在，请使用其他名称` };
            }
        }

        configs[englishId] = {
            ...config,
            name: name,
            englishId: englishId
        };

        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs));
        return { success: true, message: '配置保存成功' };
    }

    /**
     * 更新吊顶配置
     */
    public static updateConfig(englishId: string, config: I_CeilingLayerData): { success: boolean; message: string } {
        const configs = this.getAllConfigs();
        
        if (!configs[englishId]) {
            return { success: false, message: '配置不存在' };
        }

        configs[englishId] = {
            ...config,
            englishId: englishId,
            name: configs[englishId].name
        };

        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs));
        return { success: true, message: '配置更新成功' };
    }

    /**
     * 删除吊顶配置
     */
    public static deleteConfig(englishId: string): { success: boolean; message: string } {
        const configs = this.getAllConfigs();
        
        if (!configs[englishId]) {
            return { success: false, message: '配置不存在' };
        }

        delete configs[englishId];
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs));
        return { success: true, message: '配置删除成功' };
    }

    /**
     * 清空所有自定义吊顶配置
     */
    public static clearAllConfigs(): { success: boolean; message: string } {
        try {
            localStorage.removeItem(this.STORAGE_KEY);
            return { success: true, message: '所有自定义配置已清空' };
        } catch (error) {
            return { success: false, message: '清空配置失败' };
        }
    }

    /**
     * 获取默认吊顶配置
     */
    public static getDefaultConfig(): I_CeilingLayerData {
        return {
            name: this.DEFAULT_CONFIG_NAME,
            method: "Rect",
            zvalToTop: 400,
            zvalToTopFunc: "(maxCeilingHeight)",
            strokeStyle: "#aaa",
            children: [
                {
                    method: "Offset",
                    zvalToTop: 1,
                    offset_value: 100,
                    strokeStyle: "#aaa",
                    lightSlotData: {
                        direction: -1,
                        extrude_length: 50,
                        extrude_height: 20,
                        section_data: cloneSectionData(DefaultSection[DefaultSectiontype.LShapeSection]),
                    }
                }
            ]
        };
    }

    /**
     * 根据名称生成英文ID（使用Three.js风格的ID生成）
     */
    public static generateEnglishId(name: string): string {
        // 使用Three.js风格的ID生成方式
        this._idCounter++;
        
        // 获取当前配置数量作为序号
        const configs = this.getAllConfigs();
        const configCount = Object.keys(configs).length + 1;
        
        // 生成防重复的随机字符串
        const timestamp = Date.now().toString();
        const randomStr = timestamp.slice(-4); // 取时间戳后4位作为防重复字符串
        
        return `${configCount}_Ceiling_${randomStr}`;
    }

    /**
     * 导出所有配置
     */
    public static exportAllConfigs(): string {
        const configs = this.getAllConfigs();
        const exportData = {
            exportTime: new Date().toISOString(),
            configs: configs,
            defaultTemplates: DefaultCeilingLayerTemplate
        };
        
        return JSON.stringify(exportData, null, 2);
    }

    /**
     * 导入配置
     */
    public static importConfigs(jsonData: string): { success: boolean; message: string } {
        const data = JSON.parse(jsonData);
        
        if (!data.configs || typeof data.configs !== 'object') {
            return { success: false, message: '导入数据格式错误' };
        }

        const existingConfigs = this.getAllConfigs();
        const mergedConfigs = { ...existingConfigs, ...data.configs };
        
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mergedConfigs));
        return { success: true, message: '配置导入成功' };
    }

    /**
     * 检查是否为默认类型
     */
    public static isDefaultType(type: string): boolean {
        return Object.keys(DefaultCeilingLayerTemplate).includes(type);
    }

    /**
     * 检查是否为默认配置
     */
    public static isDefaultConfig(configId: string): boolean {
        return configId === 'default';
    }

    /**
     * 获取配置信息
     */
    public static getConfigInfo(type: string): { name: string; isDefault: boolean; config: I_CeilingLayerData } {
        // 如果是默认配置
        if (type === 'default') {
            return {
                name: this.DEFAULT_CONFIG_NAME,
                isDefault: true,
                config: this.getDefaultConfig()
            };
        }

        const isDefault = this.isDefaultType(type);
        
        if (isDefault) {
            return {
                name: type,
                isDefault: true,
                config: DefaultCeilingLayerTemplate[type]
            };
        }

        const customConfigs = this.getAllConfigs();
        const config = customConfigs[type];
        
        return {
            name: config?.name || type,
            isDefault: false,
            config: config || this.getDefaultConfig()
        };
    }

    /**
     * 新增配置
     * 自动生成配置名称和ID，并保存到配置中
     */
    public static addNewConfig(): { success: boolean; configId: string; configName: string; message: string } {
        // 生成配置名称
        const configName = this.generateConfigName();
        
        // 生成配置ID
        const configId = this.generateEnglishId(configName);
        
        // 获取默认配置
        const defaultConfig = this.getDefaultConfig();
        
        // 保存配置
        const result = this.saveConfigToId(configId, configName, defaultConfig);
        
        if (result.success) {
            return {
                success: true,
                configId: configId,
                configName: configName,
                message: '新增配置成功'
            };
        } else {
            return {
                success: false,
                configId: '',
                configName: '',
                message: result.message
            };
        }
    }

    /**
     * 生成配置名称
     */
    private static generateConfigName(): string {
        const configs = this.getAllConfigs();
        let counter = 1;
        let name = `${counter}_吊顶配置`;
        
        // 检查名称是否已存在，如果存在则递增计数器
        while (Object.values(configs).some(config => config.name === name)) {
            counter++;
            name = `${counter}_吊顶配置`;
        }
        
        return name;
    }
} 