import { openApiRequest } from "@/utils/request";

function formatDate(date: any) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

interface InsertLightTemplateParams {
    dataUrl?: string;
    templateData?: string;
    templateDesc?: string;
    templateImage?: string;
    templateName?: string;
    templateType?: number;
}

interface EditLightTemplateParams {
    id: string; // 修改为必填项
    dataUrl?: string;
    templateData?: string;
    templateDesc?: string;
    templateImage?: string;
    templateName?: string;
    templateType?: number;
}

interface ListLightTemplateParams {
    createUser?: string;
    dataUrl?: string;
    ids?: string[];
    orderBy?: string;
    templateData?: string;
    templateDesc?: string;
    templateImage?: string;
    templateName?: string;
    tenantId?: string;
    updateUser?: string;
    pageIndex?: number;
    pageSize?: number;
}

// 定义完整的灯光模板接口，用于获取原有内容
interface LightTemplate extends EditLightTemplateParams {
    // 可能存在的其他字段
    createTime?: string;
    updateTime?: string;
    createUser?: string;
    updateUser?: string;
    tenantId?: string;
}

export class LightTemplateService {
    /**
     * 创建AI布局灯光模版
     * @param dataUrl 模版数据链接
     * @param templateData 模板数据内容
     * @param templateDesc 模板数据内容
     * @param templateImage 模板封面
     * @param templateName 模板名称
     * @param templateType 模版类型1日间2夜间 
     */
    static insertLightTemplate(
        params: InsertLightTemplateParams = {}
    ): void {
        // 设置默认值
        const defaultParams = {
            dataUrl: "",
            templateData: "",
            templateDesc: "",
            templateImage: "",
            templateName: "",
            templateType: 1
        };

        // 合并默认值和传入参数
        const postReqBody = { ...defaultParams, ...params };

        // 打印提交前的数据
        console.log('insertLightTemplate 提交数据:', postReqBody);

        try {
            openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutLightTemplate/insert`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (!response || !response.success) {
                    console.error("Fail to insertLightTemplate.");
                } else {
                    // onSuccess();
                }
            }).catch((e: any) => {
                console.error(e);
                // nFail();
            });
        } catch (error) {
            console.error(error);
            // onFail();
        }
    }

    /**
     * 编辑灯光模板
     * 先获取原有内容，将修改项覆盖，然后发送请求
     * @returns Promise<boolean> 成功返回false，失败返回true
     */
    static async editLightTemplate(
       params: EditLightTemplateParams
    ): Promise<boolean> {  // 明确返回Promise<boolean>
        // 验证id是否存在
        if (!params.id) {
            console.error('editLightTemplate: id is required');
            return Promise.reject(new Error('id is required'));
        }

        try {
            // 1. 获取原有模板内容
            console.log(`获取模板 ${params.id} 的原有内容`);
            const originalTemplate = await this.getLightTemplate(params.id);
            
            if (!originalTemplate) {
                console.error(`未找到id为 ${params.id} 的模板`);
                return Promise.resolve(true);
            }

            // 2. 合并原有内容和修改项，修改项会覆盖原有内容
            const updatedTemplate = { ...originalTemplate, ...params };
            
            // 3. 打印调试信息
            console.log('原有模板内容:', originalTemplate);
            console.log('待修改的参数:', params);
            console.log('合并后的提交数据:', updatedTemplate);

            // 4. 发送编辑请求
            const response = await openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutLightTemplate/edit`,
                data: updatedTemplate,
                timeout: 30000,
            });

            if (!response || !response.success) {
                console.error("Fail to editLightTemplate.");
                return true;  // 失败
            } else {
                return false;  // 成功
            }
        } catch (e: any) {
            console.error('编辑模板时发生错误:', e);
            return Promise.reject(e);
        }
    }

    /**
     * 获取灯光模板详情
     * 修改为返回完整的模板对象而不仅仅是dataUrl
     */
    static getLightTemplate(id: string): Promise<LightTemplate | null> {
        const postReqBody = { id };

        console.log(`getLightTemplate 请求参数:`, postReqBody);
        
        return openApiRequest({
            method: 'post',
            url: `/api/njvr/layoutLightTemplate/get`,
            data: postReqBody,
            timeout: 30000,
        }).then((response: any) => {
            if (!response || !response.success) {
                console.error("Fail to getLightTemplate.");
                return null;
            } else {
                console.log(`getLightTemplate 成功获取模板 ${id} 内容:`, response.result);
                return response.result;
            }
        }).catch((e: any) => {
            console.error(`getLightTemplate 发生错误:`, e);
            return null;
        });
    }

    static async listLightTemplate(params: ListLightTemplateParams) {
        let postReqBody = params || {};

        return openApiRequest({
            method: 'post',
            url: `/api/njvr/layoutLightTemplate/listByPage`,
            data: {
                ...postReqBody,
            },
            timeout: 30000,
        }).then((response: any) => {
            if (!response || !response.success) {
                console.error("Fail to listLightTemplate.");
                return null;
            }
            console.log("listLightTemplate response:", response.result.result);
            return response.result;
        })
    }

    static async delete(ids: string[]) {
        return openApiRequest({
            method: 'post',
            url: `/api/njvr/LayoutLightTemplate/delete`,
            data: {
                ids,
            },
            timeout: 30000,
        }).then((response: any) => {
            if (!response || !response.success) {
                console.error("Fail to delete light template.");
                return null;
            }
            return response.result;
        })
    }
}
