import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";

import { TAppManagerBase } from "../../AppManagerBase";
import { DrawingFigureMode, IRoomFloorColor } from "../Layout/IRoomInterface";

export class TCadFloorDrawingLayer extends TDrawingLayer
{    

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadFurniture,manager);
        this._manager = manager;
    }


    onDraw()
    {   
        this.painter.strokeStyle= "#f00";

        let rooms = this.layout_container._drawing_layer_mode==="SingleRoom" && this.layout_container._selected_room?[this.layout_container._selected_room] : this.layout_container._rooms;
        // 临摹图不绘制地板样式
        if(this._manager?.layer_CadCopyImageLayer?.img?.src && this._manager.layout_container.drawing_figure_mode==DrawingFigureMode.Figure2D)
        {
            if(this._manager.layer_CadCopyImageLayer.visible) return;
        }
        
        for(let room of rooms)
        {
            if (this._manager.layout_container.drawing_figure_mode==DrawingFigureMode.Texture
                && room.tile && room.tile?.pictureViewImg) 
            {
                this.painter.fillPolygonWithImage(
                    room._room_entity._room_poly, 
                    room.tile?.pictureViewImg,
                    true,0.8);
            } else 
            {
                let color = IRoomFloorColor[room.room_type] || "#fff";;
                this.painter.fillStyle = color;
                this.painter.fillPolygon(room._room_entity._room_poly,1.);
                // let pattern = this.painter.getPattern(room.roomname+"-RoomArea") || this.painter.getPattern("RoomArea");
                // if(!pattern) return;
                // this.painter.fillPolygonWithImage(room._room_entity._room_poly, 
                //     pattern.img,false,0.75);
            }
            this.painter.fillStyle = "#fff";
        }

    }
}
