import { any, instance } from "three/src/nodes/TSL.js";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TRoom } from "../TRoom";
import { Range2DtoRect, splitSpaceForLivingRoom } from "../TLayoutEntities/utils/SplitSpaceForLivingRoom";
import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { I_Range2D, TBaseRoomToolUtil } from "../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TLayoutFineTuningBaseTool, TLayoutFineTuningOperationToolUtil } from "./TLayoutFineTuningOperationToolUtil";
import { init } from "i18next";
import { TCheckRule } from "../TLayoutScore/CheckRules/TCheckRule";
import { TLayoutJudgeContainter } from "../TLayoutScore/TLayoutJudge";
import { TLivingRoomJudge } from "../TLayoutScore/TLivingRoomJudge";
import { TGroupCheckRule } from "../TLayoutScore/CheckRules/TGroupCheckRule";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";

export class TSplitSpaceFineTuningToolUtil {
    private static _instance: TSplitSpaceFineTuningToolUtil = null;

    public static readonly step: number = 5;

    private _splitSpaceInfo: {[key: string]: ZRect};

    private constructor() {
        this._splitSpaceInfo = null;
     }

    public static get instance(): TSplitSpaceFineTuningToolUtil
    {
        if(!this._instance)
        {
            this._instance = new TSplitSpaceFineTuningToolUtil();
        }
        return this._instance;
    }

    public calSplitSpaceInfo(room: TRoom, figures: TFigureElement[])
    {
        this._splitSpaceInfo = getSplitSpaceInfo(room, figures);
    }

    // 目前这个更多地是用在客餐厅上，因为只有客餐厅分区，后续扩的的时候也会加入其他户型，目前的微调是将内部的认定为需要移动的图元进行移动以及删除操作
    // TODO 这个需要做个接口转换，确保下面的逻辑思想能够转接到生成逻辑上，尽量在下班前能接入生成逻辑
    public splitSpaceFineTuning(room: TRoom, figures: TFigureElement[], isAuto: boolean = false): TFigureElement[]
    {
        let targetFigureElements: TFigureElement[] = figures;
        // 目前这套方案应该是对所有的分区微调都是使用的这一套方案， 这个微调目前最好是先别放在生成微调这里，没办法实时查看生成效果，最好是将其抽成一个微调接口
        // 1. 对当前的布局方案进行分区, 这里应该避开交互的影响，不能在位置摆放完成后再做分区
        // 接入接口后需要将下面这个分区信息删除掉
        if(isAuto)
        { 
            this.calSplitSpaceInfo(room, figures);
        }
        // 2. 确定区域内的随机点
        let randomPostion: {[key: string]: Vector3[]} = getRandomPostionInSplitSpace(this._splitSpaceInfo);
        if(!randomPostion)
        {
            return targetFigureElements;
        }
        // 2.1 确定当当前好布局的评分规则
        let checkRules: TCheckRule[] = getBasicCheckRules(room, figures);
        if(!checkRules.length)
        {
            return targetFigureElements;
        }

        // 3. 这些如果将图元挪到这些随机点上，检查是否会越过区域边界，若越过区域边界则微调进到区域内
        // 这个目前来看士伟是直接拿餐桌区的target_rect直接赋值给餐桌，将其作为餐桌的一个大轮廓，再参与布局评分计算直到得到满意的值，
        // 现在的主要问题是将这些点应用在哪个图元上面的问题，这个需要进行特殊处理
        let getMoveFigures = (key: string, figures: TFigureElement[]): TFigureElement[] => {
            let modifyFigures: TFigureElement[] = [];
            // 1. 确定每个区域内的主图元，（默认这些主图元都是有组别的）
            let mainFigure: TFigureElement = figures.find(figure => {
                if(key == "livingSpace")
                {
                    return (figure.sub_category.includes("沙发") && figure._group_uuid != "")
                }
                else if(key == "diningSpace")
                {
                    return (figure.sub_category.includes("餐桌") && figure._group_uuid != "")
                }
                return false;
            });

            figures.forEach((figure: TFigureElement) => {
                // 这个最好是那组合图元那个信息一起收集会比较稳定
                if(figure._group_uuid == mainFigure._group_uuid)
                {
                    modifyFigures.push(figure);
                }
            });
            
            return modifyFigures;
        };
        
        // 4. 在上面确定随机撒点位置之后，在这些撒点位置找布局评分表现最好的那个作为最终的结构即可
        let maxLayoutScore: number = null;
        let maxMoveFigures: TFigureElement[] = [];
        let maxMoveOtherFigures: TFigureElement[] = [];
        Object.entries(randomPostion).forEach(([key, value]) => {
            let moveFigures: TFigureElement[] = getMoveFigures(key, figures);
            if(moveFigures.length == 0)
            {
                return targetFigureElements;
            }
            let otherFigures: TFigureElement[] = figures.filter(figure => !moveFigures.includes(figure));
            let moveRange: any = TBaseRoomToolUtil.instance.getBoxRangByFigurs(moveFigures);
            let moveRect: ZRect = Range2DtoRect(moveRange);
            let moveRangeCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(moveRange);
            [moveRangeCenter, ...value].forEach(position => {
                let oldLayoutScore: number = calLayoutScore(figures, room, checkRules) + calInRangeScore(moveRect, this._splitSpaceInfo[key], -0.001);
                let moveRectClone: ZRect = moveRect.clone();
                let moveVec: Vector3 = position.clone().sub(moveRangeCenter);
                TLayoutFineTuningOperationToolUtil.instance.moveRect(moveRectClone, moveVec);
                // 将moveRect微调到区域内部
                let oldPosition: Vector3 = moveRectClone.rect_center.clone();
                fineTuningMoveRectToInnerRange(moveRectClone, this._splitSpaceInfo[key]);
                let newPosition: Vector3 = moveRectClone.rect_center.clone();
                let moveDir: Vector3 = newPosition.clone().sub(oldPosition);
                let cloneMoveFigures: TFigureElement[] = [];
                moveFigures.forEach(figure => {
                    let cloneFigure: TFigureElement = figure.clone();
                    TLayoutFineTuningOperationToolUtil.instance.moveFigure(cloneFigure, moveDir);
                    cloneMoveFigures.push(cloneFigure);
                });
                let newLayoutScore: number = calLayoutScore([...cloneMoveFigures, ...otherFigures], room, checkRules) 
                    + calInRangeScore(moveRectClone, this._splitSpaceInfo[key], -0.001);
                if(newLayoutScore <= oldLayoutScore)
                {
                    return;
                }
                if(maxLayoutScore == null)
                {
                    maxLayoutScore = newLayoutScore;
                    maxMoveFigures = cloneMoveFigures;
                    maxMoveOtherFigures = otherFigures;
                    return;
                }
                if(maxLayoutScore < newLayoutScore)
                {
                    maxLayoutScore = newLayoutScore;
                    maxMoveFigures = cloneMoveFigures;
                    maxMoveOtherFigures = otherFigures;
                }
            });

            
        });
        if(maxLayoutScore == null)
        {
            return targetFigureElements;
        }
        targetFigureElements = [...maxMoveFigures,...maxMoveOtherFigures];
        // 5. 有些对布局评分影响比较大的图元但不重要，会被删掉
        // 比如餐厅区会善待哦餐边柜，客厅区这里暂时不进行处理
        for(let deleteName of ["餐边柜"])
        {
            let tempOldLayoutScore: number = calLayoutScore(targetFigureElements, room, checkRules, false);
            let tempFilterFigures: TFigureElement[] = targetFigureElements.filter(figure => !figure.sub_category.includes(deleteName));
            let tempLayoutScore: number = calLayoutScore(tempFilterFigures, room, checkRules, false);
            if(tempOldLayoutScore < tempLayoutScore)
            {
                targetFigureElements = tempFilterFigures;
            }
        }
        if(isAuto)
        {
            TLayoutFineTuningOperationToolUtil.instance.setRoomFurnitures(room, targetFigureElements);
        }
        return targetFigureElements;
    }
}

// 分不同的空间获取不同的分区信息，内部分区信息附带的名称不一致，最好是加入中间映射层
//
function getSplitSpaceInfo(room: TRoom, figures: TFigureElement[]): any
{
    let splitSpaceInfo: any = null;
    if(!room)
    {
        return splitSpaceInfo;
    }
    if(room.name.includes(LayoutAI_App.t("客餐厅")))
    {
        let tempSplitSpaceInfo: any = splitSpaceForLivingRoom(room, figures);
        if(!tempSplitSpaceInfo)
        {
            return splitSpaceInfo;
        }
        // 目前客餐厅是只需要调整客厅区和调整餐厅区
        splitSpaceInfo = {
            "livingSpace": tempSplitSpaceInfo?.livingSpace.length > 0 ? Range2DtoRect(tempSplitSpaceInfo.livingSpace[0]) : null,
            "diningSpace": tempSplitSpaceInfo?.diningSpace.length > 0 ? Range2DtoRect(tempSplitSpaceInfo.diningSpace[0]) : null,
        }
    }
    return splitSpaceInfo;
}

// 从不同的区域内获取随机点
function getRandomPostionInSplitSpace(splitSpaceInfo: {[key: string]: ZRect}): {[key: string]: Vector3[]}
{
    let spaceRandomPositionInfos: {[key: string]: Vector3[]} = null;
    Object.entries(splitSpaceInfo).forEach(([key, value]) => {
       if(!value)
       {
            return;
       }
       let sourceRect: ZRect = value; 
       // 计划将原矩形区域内部继续进行划分，这里暂定划分为4块区域，中心点是一个固定点采样点，其余那四个区域会在每个区域内部随机搞两个采样点
       let subRanges: I_Range2D[] = getSubSplitSpaceRanges(sourceRect);
       let randomPositions: Vector3[] = [sourceRect.rect_center.clone(), ...getRandomPositionFromSubRanges(subRanges, 2)];
       if(!spaceRandomPositionInfos)
       {
            spaceRandomPositionInfos = {};
       }
       spaceRandomPositionInfos[key] = randomPositions;
    });
    return spaceRandomPositionInfos;
}

function getSubSplitSpaceRanges(sourceRect: ZRect): I_Range2D[]
{
    let sourceRectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(sourceRect);
    let centerX = (sourceRectRange.xMin + sourceRectRange.xMax) / 2;
    let centerY = (sourceRectRange.yMin + sourceRectRange.yMax) / 2;
    let subRange1: any = {
        xMin: sourceRectRange.xMin,
        xMax: centerX,
        yMin: centerY,
        yMax: sourceRectRange.yMax,
    };
    let subRange2: any = {
        xMin: centerX,
        xMax: sourceRectRange.xMax,
        yMin: centerY,
        yMax: sourceRectRange.yMax,
    };
    let subRange3: any = {
        xMin: sourceRectRange.xMin,
        xMax: centerX,
        yMin: sourceRectRange.yMin,
        yMax: centerY,
    };
    let subRange4: any = {
        xMin: centerX,
        xMax: sourceRectRange.xMax,
        yMin: sourceRectRange.yMin,
        yMax: centerY, 
    };
    let subRanges: I_Range2D[] = [
        subRange1,
        subRange2,
        subRange3,
        subRange4,
    ];
    return subRanges;
}

function getRandomPositionFromSubRanges(subRanges: I_Range2D[], subNum: number): Vector3[]
{
    let positions: Vector3[] = [];
    for(let subRange of subRanges)
    {
        let subRandomPostions: Vector3[] = getRandomPositionFromSubRange(subRange, subNum);
        positions.push(...subRandomPostions);
    }
    return positions;
}

function getRandomPositionFromSubRange(subRange: I_Range2D, subNum: number): Vector3[]
{
    let randomPositions: Vector3[] = [];
    let xMin: number = subRange.xMin;
    let xMax: number = subRange.xMax;
    let yMin: number = subRange.yMin;
    let yMax: number = subRange.yMax;
    let getRandomNumber = (min: number, max: number) => {
        return Math.random() * (max - min) + min;
    }
    for(let i = 0; i < subNum; i++)
    {
        let x: number = getRandomNumber(xMin, xMax);
        let y: number = getRandomNumber(yMin, yMax);
        let position: Vector3 = new Vector3(x, y, 0);
        randomPositions.push(position);
    }
    return randomPositions;
}

function fineTuningMoveRectToInnerRange(rect: ZRect, rangeRect: ZRect, weight: number = -100)
{
    // 收集一个初始解
    let initMoveInfo: any = calInitMoveInfo(rect, rangeRect, weight, TSplitSpaceFineTuningToolUtil.step);
    if(!initMoveInfo)
    {
        return;
    }
    while(true)
    {
        if(initMoveInfo.step < 0.5)
        {
            break;
        }
        let oldScore: number = calInRangeScore(rect, rangeRect, weight);
        let moveDir: Vector3 = initMoveInfo.moveDir.clone().multiplyScalar(initMoveInfo.step);
        TLayoutFineTuningOperationToolUtil.instance.moveRect(rect, moveDir);
        let newScore: number = calInRangeScore(rect, rangeRect, weight);
        if(newScore <= oldScore)
        {
            initMoveInfo.step /= 2;
        }
    }
}

function calInRangeScore(rect: ZRect, rangeRect: ZRect, weight: number): number
{
    let overRangeScore: number = 0;
    let innereRange: I_Range2D = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect);
    let outerRange: I_Range2D = TBaseRoomToolUtil.instance.getRange2dByPolygon(rangeRect);
    let isXOverRange: boolean = !(innereRange.xMax <= outerRange.xMax && innereRange.xMin >= outerRange.xMin);
    let isYOverRange: boolean = !(innereRange.yMax <= outerRange.yMax && innereRange.yMin >= outerRange.yMin);
    let overLen:number = 0;
    if(isXOverRange)
    {
        let overX: number = 0;
        if(innereRange.xMax > outerRange.xMax)
        {
            overX += innereRange.xMax - outerRange.xMax;
        }
        if(innereRange.xMin < outerRange.xMin)
        {
            overX += outerRange.xMin - innereRange.xMin;
        }
        overLen += overX;
    }
    if(isYOverRange)
    {
        let overY: number = 0;
        if(innereRange.yMax > outerRange.yMax)
        {
            overY += innereRange.yMax - outerRange.yMax;
        }
        if(innereRange.yMin < outerRange.yMin)
        {
            overY += outerRange.yMin - innereRange.yMin;
        }
        overLen += overY;
    }
    overRangeScore = overLen * weight;
    return overRangeScore;
}

function calInitMoveInfo(rect: ZRect, rangeRect: ZRect, weight: number, step: number = 50): any
{
    let oldScore: number = calInRangeScore(rect, rangeRect, weight);
    if(Math.abs(oldScore) < 0.01)
    {
        return null;
    }
    let upDir: Vector3 = rect.frontEdge.v0.pos.clone().sub(rect.frontEdge.v1.pos).normalize();
    let downDir: Vector3 = rect.backEdge.v0.pos.clone().sub(rect.backEdge.v1.pos).normalize();
    let leftDir: Vector3 = rect.leftEdge.v0.pos.clone().sub(rect.leftEdge.v1.pos).normalize();
    let rightDir: Vector3 = rect.rightEdge.v0.pos.clone().sub(rect.rightEdge.v1.pos).normalize();

    // 将rect分别向这四个方向挪动一定的步长
    let moveOneStepScoreInfo = (rect: ZRect, rangeRect: ZRect, weight: number, step: number, dir: Vector3) => {
        let rectClone: ZRect = rect.clone();
        TLayoutFineTuningOperationToolUtil.instance.moveRect(rectClone, dir.clone().multiplyScalar(step))
        let oneStepScore: number = calInRangeScore(rectClone, rangeRect, weight);
        if(oneStepScore < oldScore)
        {
            return null;
        }
        return {score: oneStepScore, moveDir: dir};
    }
    // 在其中抉择出最好的一个移动方向
    let moveOneStepScoreInfos: any[] = [];
    for(let dir of [upDir, downDir, leftDir, rightDir])
    {
        let oneStepScoreInfo: any = moveOneStepScoreInfo(rect, rangeRect, weight, step, dir);
        if(oneStepScoreInfo)
        {
            moveOneStepScoreInfos.push(oneStepScoreInfo);
        }
    }
    let maxScore: number = null;
    for(let moveOneStepScoreInfo of moveOneStepScoreInfos)
    {
        let oneStepScore: number = moveOneStepScoreInfo.score;
        if(maxScore == null)
        {
            maxScore = oneStepScore;
            continue;
        }
        if(maxScore < oneStepScore)
        {
            maxScore = oneStepScore;
        }
    }

    // 统计最大的是哪个并将其进行执行
    let maxDirs:Vector3[] = [];
    for(let moveOneStepScoreInfo of moveOneStepScoreInfos)
    {
        if(Math.abs(moveOneStepScoreInfo.score - maxScore) < 0.01)
        {
            maxDirs.push(moveOneStepScoreInfo.moveDir);
        }
    }
    if(maxDirs.length == 1)
    {
        return {score: oldScore, moveDir: maxDirs[0], step: step};
    }
    else if(maxDirs.length > 1)
    {
        // 对于多个方向移动一个步长后，分值都一样的情况，则那个移动到较好的情况下则那个执行得比较少就用哪个
        let minMoveLen: number = null;
        let minMoveDir: Vector3 = null;
        for(let maxDir of maxDirs)
        {
            let moveLen: number = 0;
            let moveStep: number = step;
            while(true)
            {
                if(moveStep < 0.5)
                {
                    break;
                }
                moveLen += moveStep;
                let rectClone: ZRect = rect.clone();
                let beforMoveScore: number = calInRangeScore(rectClone, rangeRect, weight);
                TLayoutFineTuningOperationToolUtil.instance.moveRect(rectClone, maxDir.clone().multiplyScalar(moveLen));
                let afterMoveScore: number = calInRangeScore(rectClone, rangeRect, weight);
                if(afterMoveScore < beforMoveScore)
                {
                    moveStep /= 2;
                    rectClone = null;
                    continue;
                }
                rectClone = null;
            }
            if(minMoveLen == null)
            {
                minMoveLen = moveLen;
                minMoveDir = maxDir;
            }
        }
        return {score: oldScore, moveDir: minMoveDir, step: step};
    }
    return null;
}

function getBasicCheckRules(room: TRoom, figures: TFigureElement[]): TCheckRule[]
{
    let roomJudgeName: string = getLayoutJudgeName(room);
    if(!roomJudgeName) return [];
    let judger = TLayoutJudgeContainter.instance.judge_list.find(judge=>judge.name === roomJudgeName);
    if(!judger) return [];
    // 备选的评分器(这个目前是为了效率问题是只局限于不准允出的规则)
    let check_rules : TCheckRule[] = []; 
    // 筛选出哪些是不准予出的评分规则
    judger._check_rules.forEach((rule)=>{
        if((rule as TGroupCheckRule)._children)
        {
            for(let sub_rule of (rule as TGroupCheckRule)._children)
            {
                if(sub_rule.isBasicCheck)
                {
                    check_rules.push(sub_rule);
                }
            }
        }
    });
    return check_rules;
}

function getLayoutJudgeName(room: TRoom): string
{
    if(room.name == "客餐厅")
    {
        return TLivingRoomJudge.name;
    }
    return null;
}

function calLayoutScore(figure_elements:TFigureElement[], room:TRoom, check_rules:TCheckRule[], isNotAllow: boolean = true){
    let score = 0;
    check_rules.forEach((check_rule)=>{
        let layout_score = check_rule.computeLayoutScore(room,figure_elements);
        if(isNotAllow)
        {
            if(Math.abs(-100 - layout_score.score) < 0.01)
            {
                score += layout_score.score * check_rule.weight;
            }
        }
        else
        {
            score += layout_score.score * check_rule.weight;
        }
    });     
    return score; 
}