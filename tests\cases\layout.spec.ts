import { test, expect } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import { takeScreenshot } from '../utils/screenshotUtils';
import { openAnyScheme, searchAndOpen } from '../utils/basicProcess';

test.describe('布局界面相关的测试', () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('更改全屋布局', async ({ page }) => {
    // 1.点击首页中的搜索户型库
    // 2.输入户型id（1ILLHIIKJFLCDDJGKC），点击搜户型
    // 3.选择户型点击开始设计
    searchAndOpen(page, designPage);

    // 4.选择左侧布局列表中的第二个全屋方案
    const secondScheme = page.locator('#side_list_div > div:nth-child(2)');
    await secondScheme.waitFor({ state: 'visible' });
    await secondScheme.click();

    // TODO 判定是否展示出第二个方案
  });

  test('更改单空间布局', async ({ page }) => {
    // 1.点击首页中的搜索户型库
    // 2.输入户型id（1ILLHIIKJFLCDDJGKC），点击搜户型
    // 3.选择户型点击开始设计
    searchAndOpen(page, designPage);

    // 4.选中客餐厅空间，点击选择左侧布局列表中的第二个客餐厅布局方案（来自smoke-testing.spec.ts）
    const layoutScheme = page.locator('#layout_whole_scheme_cavans_1');
    await layoutScheme.waitFor({ state: 'visible' });
    await layoutScheme.click();
    console.info('点击左侧面板第二个布局方案');

    // 截图保存并比较
    await page.waitForTimeout(5000);
    await takeScreenshot(page, 'smoke-testing', 'smoke-case1-layout.png');
    console.info('对户型布局图进行截图比较');

    let roomsInfo = await designPage.getRoomInfo();
    if (roomsInfo.length > 0) {
      for (let i = 0; i < roomsInfo.length; i++) {
        const room = roomsInfo[i];
        console.info(
          '点击房间: ' +
          room.roomName +
          ' (id=' +
          room.roomId +
          ', area=' +
          room.area.toFixed(2) +
          '㎡)'
        );
        designPage.clickRoom(room);
        await page.waitForTimeout(1000);
      }
    }

    if (roomsInfo.length > 0) {
      for (let i = 0; i < roomsInfo.length; i++) {
        const room = roomsInfo[i];
        const roomId = room.roomId;

        // 获取该房间中的家具信息
        const furnitureInfo = await designPage.getFurnitureInfo(roomId);

        console.info(
          '####### 房间: ' +
          room.roomName +
          ' (id=' +
          room.roomId +
          ', area=' +
          room.area.toFixed(2) +
          '㎡)'
        );
        if (furnitureInfo.length > 0) {
          for (let i = 0; i < furnitureInfo.length; i++) {
            let furniture = furnitureInfo[i];
            console.info(
              '  点击家具图元: ' +
              furniture.modelLoc +
              ' (id=' +
              furniture.modelId +
              ', name=' +
              furniture.materialName +
              ')'
            );
            designPage.clickFurniture(furniture);
            await page.waitForTimeout(500);
          }
        }

        // 验证家具信息
        expect(furnitureInfo.length).toBeGreaterThan(0);
        break;
      }
    }
  });

  test('清空布局', async ({ page }) => {
    // 1.打开任意户型
    await openAnyScheme(page, designPage);

    // 2.点击顶菜清空按钮，弹框中选择确认清空
    await designPage.clickTopMenuResetItem();
    const comfirmResetButton = page.locator('div.ant-modal-confirm-btns').getByText('确认清空');
    await comfirmResetButton.waitFor({ state: 'visible', timeout: 5000 });
    await comfirmResetButton.click();

    // 判定是否清空布局（涉及canvas内部元素，需要使用预留接口）
    // TODO 接口有bug
    let roomsInfo = await designPage.getRoomInfo();
    const furnitureCount = roomsInfo.reduce(
      (totalCount: number, room) => totalCount + room.furnitureCount,
      0
    );
    expect(furnitureCount).toBe(0);
  });

  test('添加图元', async ({ page }) => {
    // 1.点击首页中的搜索户型库
    // 2.输入户型id（1ILLHIIKJFLCDDJGKC），点击搜户型
    // 3.选择户型点击开始设计
    await searchAndOpen(page, designPage);

    // 4.选中客餐厅空间，左侧栏切换至素材tab
    // 5.选择沙发组合类目下第二个沙发组合，拖动至客餐厅空间

    const layoutScheme = page.locator('#layout_whole_scheme_cavans_1');
    await layoutScheme.waitFor({ state: 'visible' });
    await layoutScheme.click();
    console.info('点击左侧面板第二个布局方案');

    let roomsInfo = await designPage.getRoomInfo();
    if (roomsInfo.length > 0) {
      for (let i = 0; i < roomsInfo.length; i++) {
        const room = roomsInfo[i];
        if(room.roomName === '客餐厅'){
          const KeCanTing = room
          designPage.clickRoom(room);
          await page.waitForTimeout(1000);

           // 切换到素材tab
          const tab_fodder = page.getByRole('radiogroup').getByText('素材');
          await expect(tab_fodder).toBeVisible();
          await tab_fodder.click();
          await page.waitForTimeout(1000);

          const ShaFaZuHe = page.locator('div.item').getByText('沙发组合', { exact: true });
          await ShaFaZuHe.waitFor({ state: 'visible', timeout: 10000 });
          await ShaFaZuHe.click();

          const ShaFaZuHe_item2_img = page
            .locator('div.content > div.item > div.image > div')
            .locator('img[alt="沙发组合-2"]'); // 这里需要注意，要点击到图片上才能拖移
          await ShaFaZuHe_item2_img.waitFor({ state: 'visible', timeout: 10000 });

          // 获取DOM“沙发组合-2”的坐标，并拖移到canvas中
          const sofaBoundingBox = await ShaFaZuHe_item2_img.boundingBox();
          if (!sofaBoundingBox) {
            throw new Error('无法获取沙发组合元素的位置信息');
          }
          // 计算元素的中心点坐标（作为拖拽起点）
          const sourcePosition = {
            x: sofaBoundingBox.x + sofaBoundingBox.width / 2,
            y: sofaBoundingBox.y + sofaBoundingBox.height / 2
          };
          const targetPosition = KeCanTing.posInScreen;

          // 拖移
          await page.mouse.move(sourcePosition.x, sourcePosition.y);
          await page.mouse.down();
          await page.mouse.move(
            targetPosition.x,
            targetPosition.y,
            { steps: 10 } // 添加中间步骤模拟真人拖拽
          );
          await page.mouse.up();

          break;
        }
      }
    }

    // // 获取房间信息
    // const roomInfo = await designPage.getRoomInfo();
    // const KeCanTing = roomInfo.find(room => room.roomName === '客餐厅');
    // if (!KeCanTing) {
    //   throw new Error('未找到客餐厅房间信息');
    // }

    // const canvas = page.locator('#cad_canvas'); // 获取 canvas 元素
    // await canvas.click({
    //   position: KeCanTing.posInScreen
    // }); // 点击选中“客餐厅”

    // 切换到素材tab
    // const tab_fodder = page.getByRole('radiogroup').getByText('素材');
    // await expect(tab_fodder).toBeVisible();
    // await tab_fodder.click();
    // await page.waitForTimeout(1000);

    // 选择“沙发组合”类目下第二个沙发组合，拖动至客餐厅空间
    // const ZuHe = page.locator('div.svg-wnba3r > div.item').getByText('组合', { exact: true });
    // await ZuHe.waitFor({ state: 'visible', timeout: 10000 });
    // await ZuHe.click();
    // const ShaFaZuHe = page.locator('div.item').getByText('沙发组合', { exact: true });
    // await ShaFaZuHe.waitFor({ state: 'visible', timeout: 10000 });
    // await ShaFaZuHe.click();

    // const ShaFaZuHe_item2_img = page
    //   .locator('div.content > div.item > div.image > div')
    //   .locator('img[alt="沙发组合-2"]'); // 这里需要注意，要点击到图片上才能拖移
    // await ShaFaZuHe_item2_img.waitFor({ state: 'visible', timeout: 10000 });

    // // 获取DOM“沙发组合-2”的坐标，并拖移到canvas中
    // const sofaBoundingBox = await ShaFaZuHe_item2_img.boundingBox();
    // if (!sofaBoundingBox) {
    //   throw new Error('无法获取沙发组合元素的位置信息');
    // }
    // // 计算元素的中心点坐标（作为拖拽起点）
    // const sourcePosition = {
    //   x: sofaBoundingBox.x + sofaBoundingBox.width / 2,
    //   y: sofaBoundingBox.y + sofaBoundingBox.height / 2
    // };
    // const targetPosition = KeCanTing.posInScreen;

    // // 拖移
    // await page.mouse.move(sourcePosition.x, sourcePosition.y);
    // await page.mouse.down();
    // await page.mouse.move(
    //   targetPosition.x,
    //   targetPosition.y,
    //   { steps: 10 } // 添加中间步骤模拟真人拖拽
    // );
    // await page.mouse.up();
  });
});
