import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomSplitByPathClassfierCheckRule } from "./TLivingRoomSplitByPathClassfierCheckRule";

export class TLivingRoomSpaceUsageCheckRule extends TLivingRoomSplitByPathClassfierCheckRule
{
    private minUsageRatio: number = 70; // 单位百分比

    private maxUsageRatio: number = 85; // 单位百分比

    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        void(figure_element);
        void(main_figures);

        let score: number = 0;
        let roomArea: number = TBaseRoomToolUtil.instance.calPolygonArea(room.room_shape._poly);
        if(this.minLivingSpaceRect && this.minDiningSpaceRect)
        {
            let livingArea: number = this.minLivingSpaceRect.area;
            let diningArea: number = this.minDiningSpaceRect.area;
            let usageRatio: number = (livingArea + diningArea) / roomArea * 100;
            if(usageRatio >= this.minUsageRatio && usageRatio <= this.maxUsageRatio)
            {
                score += 30;
            }
            else if(usageRatio < this.minUsageRatio)
            {
                // 递增得分
               score += calLinearScore(usageRatio, 0, this.minUsageRatio, 0, 30);
            }
            else if(usageRatio > this.maxUsageRatio)
            {
                // 递减得分
                score += calLinearScore(usageRatio, this.maxUsageRatio, 100, 30, 0);
            }  
        }
        return {score: score};
    }
}

function calLinearScore(value: number, value1: number, value2: number, score1: number, score2: number): number
{
    let k: number = (score1 - score2) / (value1 - value2);
    let targetScore: number = Math.ceil(k * value + (score1 - k * value1));
    return targetScore;
}