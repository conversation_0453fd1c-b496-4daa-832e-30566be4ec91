import { ZRect } from "@layoutai/z_polygon";
import { TBaseEntity } from "../TBaseEntity";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";


export class TExtDrawingEntity extends TBaseEntity
{

    _color_style : string;

    _line_style : string;
    constructor()
    {
        super(new ZRect(1,1));
        this.type = "ExDrawing";

        this._color_style = "#66b8ff";
        this._line_style = "";
    }

    get color_style()
    {
        return this._color_style;
    }

    set color_style(color:string)
    {
        this._color_style = color;
    }


    protected getCandidateNames()
    {
        return [{label:"",value:""}];
    }

    set candidate_name(str:string)
    {
        this._name = str;
    }
    initProperties(): void {
        if(!this._ui_properties)
        {
            this._ui_properties = {};
        }
        this._ui_properties["ui_type"] = {
            name : LayoutAI_App.t("图层"),
            widget :"LabelItem",
            type : "string",
            defaultValue : ''+this.ui_type,
            editable : false,
            props : {
                type :"input",
            }
        }
        this._ui_properties["ui_realType"] = {
            name : LayoutAI_App.t("模块"),
            widget :"LabelItem",
            type : "string",
            defaultValue : ''+this.ui_realType,
            props : {
                type :"select",
                options : this.getRealTypeOptions(),
                optionWidth : 100
            }
        }
        this._ui_properties["name"] = {
            name : LayoutAI_App.t("名称"),
            widget :"LabelItem",
            type : "string",
            defaultValue : ''+this.name,
            props : {
                type :"input",
            }
        }

        this._ui_properties["candidate_name"] = {
            name : "",
            _key:"candidate_name",
            widget :"LabelItem",
            type : "string",
            defaultValue : ''+this.name,
            props : {
                type :"select",
                options : this.getCandidateNames(),
                optionWidth : 100
            }
        }

        this._ui_properties["color_style"] = {
            name : LayoutAI_App.t("颜色样式"),
            widget :"ColorWidget",
            type : "string",
            defaultValue : ''+this.color_style,
            props : {
                type :"select",
                optionWidth : 100

            }
        }

        this._ui_props_keys = ["ui_type","ui_realType","name","candidate_name","color_style"];
        this._bindPropertiesOnChange();
    }
}