import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { Vector3 } from "three";
import { TAppManagerBase } from "../../AppManagerBase";
import { ZRect } from "@layoutai/z_polygon";
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";

export class TCadCopyImageLayer extends TDrawingLayer {

    img: HTMLImageElement;
    _rect: ZRect;
    constructor(manager: TAppManagerBase) {
        super(CadDrawingLayerType.CadCopyImageDrawing, manager);
        this._dirty = true;
        this.img = new Image();
        this._rect = new ZRect(0, 0);
    }

    loadData(img: HTMLImageElement, scaleNum?: number) {
        if (!img) return;
        this.img = img;
        if (scaleNum) {
            this._rect = new ZRect((this.img.width / scaleNum), this.img.height / scaleNum);
            this._rect.nor = new Vector3(0, -1, 0);
            let r_center = new Vector3(0, 0, 0);
            this._rect.rect_center = r_center;
        } else {
            let scaleNum: any = this.painter._p_sc;
            this._rect = new ZRect((this.img.width / Number(scaleNum)), this.img.height / Number(scaleNum));
            this._rect.nor = new Vector3(0, -1, 0);
            let r_center = new Vector3(this._rect._w / 2, 0, 0);
            this._rect.rect_center = r_center;
            this.painter.p_center = r_center;
        }


        this._manager.layout_container.copyImageRect = this._rect;
        this._manager.layout_container.copyImageRect._attached_elements.img = this.img;

    }

    _updateLayerContent(): void {

    }

    clean() {
        this.img = null;
        this._manager.layout_container.copyImageRect = null;
    }

    onDraw(): void {
        //  这里是设置canvas一些属性
        this.painter._context.imageSmoothingEnabled = true;
        if (this._manager.layout_container.copyImageRect && this._manager.layout_container.copyImageRect._attached_elements.is_selected) return;
        if (this.img?.src && this._manager.layout_container.copyImageRect && this._manager.layout_container.copyImageRect._w > 0 && this.img.width > 0) {
            this.painter._context.globalAlpha = LayoutAI_App.instance.getPainterGlobalAlpha();
            this.painter.fillImageInRect(this.img, this._manager.layout_container.copyImageRect);
        }
    }
}