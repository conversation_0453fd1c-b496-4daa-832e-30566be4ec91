import { AI2<PERSON><PERSON><PERSON><PERSON>Hand<PERSON> } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { I_MouseEvent, compareNames } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { ZEdge } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { ZRect } from "@layoutai/z_polygon";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";



export class EditCeilingSubHandler extends CadBaseSubHandler
{

    _src_rect : ZRect;
    _src_pos : Vector3;
    _target_pos : Vector3;

    _selected_edge_id : number;


    _exsorb_edges : ZEdge[];

    _exsorb_target_point : Vector3;

    _ceiling_in_room : TRoom;
    constructor(cad_mode_handler:<PERSON>2BaseModeHandler)
    {
        super(cad_mode_handler);
    }
    enter(state?: number): void {
        if(!this.selected_target.selected_ceiling)
        {
            LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        this.selected_target.selected_ceiling._ex_prop['is_selected'] = 1;
        
        if(this.selected_target.selected_ceiling)
        {
            let room = this.room_list.find((room)=>room._ceilling_list.includes(this.selected_target.selected_ceiling));
            this._ceiling_in_room = room;
        }
        this.update();
    }
    leave(state?: number): void {
        this._selected_edge_id = -1;
        this._src_rect = null;
        this._src_pos = null;
        this._target_pos = null;
        this._exsorb_edges = null;
        this._exsorb_target_point = null;
        this._ceiling_in_room = null;

        if(this.selected_target.selected_ceiling?._ex_prop['is_selected'])
        {
            delete this.selected_target.selected_ceiling?._ex_prop['is_selected'];
        }
    }

    updateExsorbEdges()
    {
        this._exsorb_edges = [];

        let target_room : TRoom = null;
        if(this.selected_target.selected_ceiling)
        {
            target_room = this.selected_target.selected_ceiling._room;
            if(target_room)
            {
                // console.log(target_room);
                this._exsorb_edges.push(...target_room.room_shape._poly.edges);

                let cabinets = target_room._furniture_list.filter((figure)=>compareNames([figure.category],["柜"]));

                cabinets.forEach((fig)=>{
                    this._exsorb_edges.push(...fig.rect.edges);
                });

                target_room.windows.forEach((win)=>{
                    if(win.rect)
                    {
                        this._exsorb_edges.push(...win.rect.edges);
                    }
                })
            }
        }

    }

    exsorbCeiling()
    {
        if(this._selected_edge_id < 0) return;
        if(this._exsorb_edges)
        {
            let rect = this.selected_target.selected_ceiling.rect;
            let t_edge = rect.edges[this._selected_edge_id];

            let oy = 50;
            let needs_exsorb = false;
            this._exsorb_target_point = null;
            this._exsorb_edges.forEach((e_edge)=>{
                let py = t_edge.projectEdge2d(e_edge.v0.pos).y;

                if(Math.abs(py) < Math.abs(oy))
                {
                    oy = py;
                    this._exsorb_target_point = e_edge.v0.pos.clone();
                    needs_exsorb = true;
                }
            });

            if(needs_exsorb)
            {
                let offset = t_edge.nor.clone().multiplyScalar(oy);
                t_edge.moveEdge(offset);

                rect.reParaFromVertices();
            }

        }
    }
    onmousedown(ev: I_MouseEvent): void {

        if(this.selected_target.selected_ceiling)
        {
            this.updateExsorbEdges();

            this._src_pos = new Vector3(ev.posX,ev.posY,0);
            
            this._selected_edge_id = -1;
            let min_dist = 1000000;
            this.selected_target.selected_ceiling.rect.edges.forEach((edge,index)=>{
                let pp = edge.projectEdge2d(this._src_pos);

                let dist = Math.abs(pp.y) + edge.center.distanceTo(this._src_pos);
                if(dist < min_dist)
                {
                    min_dist = dist;
                    this._selected_edge_id = index;
                }

            });

            this._src_rect = this.selected_target.selected_ceiling.rect.clone();
        }

        this._cad_mode_handler._is_moving_element = true;
        this.update();

    }

    onmousemove(ev: I_MouseEvent): void {
        if(this._selected_edge_id>=0)
        {
            this._target_pos = new Vector3(ev.posX,ev.posY,0);

            let offset = this._target_pos.clone().sub(this._src_pos);

            let rect = this.selected_target.selected_ceiling.rect;
            rect.copy(this._src_rect);
            let edge = rect.edges[this._selected_edge_id];

            offset = edge.nor.clone().multiplyScalar(offset.dot(edge.nor));
            edge.moveEdge(offset);

            rect.reParaFromVertices();


            if(!ev.shiftKey)
            {
                this.exsorbCeiling();
            }
            if(this._ceiling_in_room && this._ceiling_in_room._room_entity?.room_ceiling_entity)
            {
                this._ceiling_in_room._room_entity.room_ceiling_entity.update();
            }

            this.update();
        }
    }

    onmouseup(ev: I_MouseEvent): void {
        this._cad_mode_handler._is_moving_element = false;
        this._cad_mode_handler._is_painter_center_moving = false;
        LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);

    }

    updateAttributes(mode?: "init" | "hide" | "edit"): Promise<void> {

        return;
    }


    drawCanvas(): void {
        if(this.selected_target.selected_ceiling)
        {
            let rect = this.selected_target.selected_ceiling.rect;

            this.painter.strokeStyle = "#f77";
            this.painter.fillStyle = "#aaa";            
            
            this.painter.fillPolygons([rect],0.1);
            this.painter.strokePolygons([rect]);

            if(this._selected_edge_id>=0)
            {
                let t_edge = rect.edges[this._selected_edge_id];
                if(this._exsorb_target_point)
                {
                    let n_edge = new ZEdge({pos:this._exsorb_target_point},{pos:t_edge.center});
                    this.painter.strokeStyle = '#f23dd1';
                    this.painter.drawEdges([n_edge]);

                }
                this.painter.strokeStyle = "#14ffff"; // 设置描边颜色为青色
                this.painter.drawEdges([t_edge]);


     
            }



        }
    }

    
}