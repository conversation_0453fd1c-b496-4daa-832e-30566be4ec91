import { TFeatureShape } from "../TFeatureShape";
import { DefaultRoomEncoder } from "./DefaultRoomEncoder";
import { FeatureEncoder } from "./FeatureEncoder";




export class FeatureEncodersDict
{
    encoders : {[key:string]:FeatureEncoder};
    static instance : FeatureEncodersDict;
    constructor()
    {
        FeatureEncodersDict.instance = this;
        this.encoders = {};

        this.encoders["Default"] = new DefaultRoomEncoder();
        

    }
}


export function encodeFeatureShape(feature_shape:TFeatureShape) : string[]
{
    if(!FeatureEncodersDict.instance)
    {
        FeatureEncodersDict.instance = new FeatureEncodersDict();
    }
    let encoder = FeatureEncodersDict.instance.encoders[feature_shape._room.roomname] || FeatureEncodersDict.instance.encoders['Default'];
    if(!encoder) return [];
    return encoder.encode(feature_shape);
}