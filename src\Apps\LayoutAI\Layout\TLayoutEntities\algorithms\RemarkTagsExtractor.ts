import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../TFigureElements/TFigureElement";

export interface I_RemarkTagMapping
{
    /**
     *  标签名称
     */
    tagName : string;
    
    categories : string[];
}
export class RemarkTagsExtractor
{
    private static _instance : RemarkTagsExtractor = null;

    private _tags_dict : I_RemarkTagMapping[] = [];
    constructor()
    {
        this._tags_dict = [
            {
                tagName:"收纳",
                categories : ["电视柜","衣柜","书柜","餐边柜","浴室柜","阳台柜"]
            },
            {
                tagName:"阅读",
                categories : ["书桌","床头吊灯"]
            },
            {
                tagName:"健身",
                categories : ["跑步机","健身架","瑜伽垫"]
            },
            {
                tagName:"品茗",
                categories : ["茶台","茶几"]
            },
            {
                tagName:"聚会",
                categories : ["吧台"]
            },
            {
                
                tagName:"梳妆",
                categories : ["梳妆台","梳妆凳"]
            },
            {
                tagName:"影音",
                categories:["投影仪","投影布","音响","电视柜"]
            },
            {
                tagName:"装饰",
                categories:["雕塑","绿植","落地灯"]
            },
            {
                tagName:"酒水",
                categories:["餐边柜","吧台"]
            }
        ]
    }   

    static get instance()
    {
        if(!RemarkTagsExtractor._instance){
            RemarkTagsExtractor._instance = new RemarkTagsExtractor();
        }
        return RemarkTagsExtractor._instance;
    }

    static ExtractTags(figure_elements:TFigureElement[])
    {
        return RemarkTagsExtractor.instance.extractTagsFromFigures(figure_elements);
    }
    private extractTagsFromFigures(figure_elements:TFigureElement[])
    {
        let tags_flags:{[key:string]:number} = {};
        figure_elements.forEach((ele)=>{
            this.extractTagsFromSingleFigure(ele,tags_flags);
        });
        let tags : string[] = [];
        // 让Tags输出按照给定的顺序
        this._tags_dict.forEach((tag_data)=>{
            if(tags_flags[tag_data.tagName])
            {
                tags.push(tag_data.tagName);
            }
        });
        return tags;
    }
    private extractTagsFromSingleFigure(ele:TFigureElement,tags_flags:{[key:string]:number})
    {
        this._tags_dict.forEach((tag_data)=>{
            if(compareNames([ele.category,ele.sub_category],tag_data.categories))
            {
                tags_flags[tag_data.tagName] = 1;
            }
        });
        return tags_flags;
    }
}