import { Vector3 } from "three";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { splitSpaceForLivingRoom, calMinDiningSpaceRanges, calMinLivingSpaceRanges, calNewEntranceSpaceRanges, calNewHallwaySpaceRanges } from "../../../TLayoutEntities/utils/SplitSpaceForLivingRoom";
import { TRoom } from "../../../TRoom";
import { I_Range2D, TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { TLivingRoomClassfierCheckRule } from "../LivingRoomCheckRules/TLivingRoomClassfierCheckRule";
import { I_CheckRuleOptions } from "../TCheckRule";
import { ZRect } from "@layoutai/z_polygon";
import { WPolygon } from "../../../TFeatureShape/WPolygon";

// TODO 下面要拆解出最小客餐厅区以及扩展的客餐厅边界
export class TLivingRoomSplitByPathClassfierCheckRule extends TLivingRoomClassfierCheckRule
{
    private _livingSpaceRect: ZRect;

    private _diningSpaceRect: ZRect;

    private _otherSpaceRects: ZRect[];

    private _minLivingSpaceRect: ZRect;

    private _minDiningSpaceRect: ZRect;

    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
        this._livingSpaceRect = null;
        this._diningSpaceRect = null;
        this._otherSpaceRects = null;
        this._minLivingSpaceRect = null;
        this._minDiningSpaceRect = null;
    }

    protected clear()
    {
        super.clear();
        this._livingSpaceRect = null;
        this._diningSpaceRect = null;
        this._otherSpaceRects = null;
        this._minLivingSpaceRect = null;
        this._minDiningSpaceRect = null;
    }

    protected processFigure(room: TRoom, figureElements: TFigureElement[]): void
    {
        if(this._livingSpaceRect && this._diningSpaceRect)
        {
            return;
        }
        let splitSpaceInfo = splitSpaceForLivingRoom(room, figureElements);
        if(splitSpaceInfo)
        {
            if(splitSpaceInfo.livingSpace && splitSpaceInfo.livingSpace.length)
            {
                let livingRange: any = splitSpaceInfo.livingSpace[0];
                let minLivingRange: any = splitSpaceInfo.minLivingSpace[0];
                this._livingSpaceRect = TBaseRoomToolUtil.instance.getRectByRange2d(livingRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(livingRange));
                this._minLivingSpaceRect = TBaseRoomToolUtil.instance.getRectByRange2d(minLivingRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(minLivingRange));
                let sofaRect: ZRect = figureElements.find(figure => figure.sub_category.includes("沙发") && !figure.sub_category.includes("背景墙"))?.rect;
                if(sofaRect)
                {
                    let oldCenter: Vector3 = this._livingSpaceRect.rect_center.clone();
                    let oldMinCenter: Vector3 = this._minLivingSpaceRect.rect_center.clone();
                    if(Math.abs(sofaRect.nor.dot(this._livingSpaceRect.nor)) < 0.9)
                    {
                        let oldLen: number = this._livingSpaceRect.length;
                        let oldWidth: number = this._livingSpaceRect.depth;
                        this._livingSpaceRect.length = oldWidth;
                        this._livingSpaceRect.depth = oldLen;

                        let oldMinLen: number = this._minLivingSpaceRect.length;
                        let oldMinWidth: number = this._minLivingSpaceRect.depth;
                        this._minLivingSpaceRect.length = oldMinWidth;
                        this._minLivingSpaceRect.depth = oldMinLen;
                    }
                    this._livingSpaceRect.nor = sofaRect.nor.clone();
                    this._livingSpaceRect.rect_center = oldCenter;
                    this._livingSpaceRect.updateRect();

                    this._minLivingSpaceRect.nor = sofaRect.nor.clone();
                    this._minLivingSpaceRect.rect_center = oldMinCenter;
                    this._minLivingSpaceRect.updateRect();
                }
            }
            if(splitSpaceInfo.diningSpace && splitSpaceInfo.diningSpace.length)
            {
                let diningRange: any = splitSpaceInfo.diningSpace[0];
                let minDiningRange: any = splitSpaceInfo.minDiningSpace[0];
                this._diningSpaceRect = TBaseRoomToolUtil.instance.getRectByRange2d(diningRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(diningRange));
                this._minDiningSpaceRect = TBaseRoomToolUtil.instance.getRectByRange2d(minDiningRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(minDiningRange));
                let diningTableRect: ZRect = figureElements.find(figure => figure.sub_category.includes("餐桌"))?.rect;
                if(diningTableRect)
                {
                    let oldCenter: Vector3 = this._diningSpaceRect.rect_center.clone();
                    let oldMinCenter: Vector3 = this._minDiningSpaceRect.rect_center.clone();
                    if(Math.abs(diningTableRect.nor.dot(this._diningSpaceRect.nor)) < 0.9)
                    {
                        let oldLen: number = this._diningSpaceRect.length;
                        let oldWidth: number = this._diningSpaceRect.depth;
                        this._diningSpaceRect.length = oldWidth;
                        this._diningSpaceRect.depth = oldLen;

                        let oldMinLen: number = this._minDiningSpaceRect.length;
                        let oldMinWidth: number = this._minDiningSpaceRect.depth;
                        this._minDiningSpaceRect.length = oldMinWidth;
                        this._minDiningSpaceRect.depth = oldMinLen;
                    }
                    this._diningSpaceRect.nor = diningTableRect.nor.clone();
                    this._diningSpaceRect.rect_center = oldCenter;
                    this._diningSpaceRect.updateRect();

                    this._minDiningSpaceRect.nor = diningTableRect.nor.clone();
                    this._minDiningSpaceRect.rect_center = oldMinCenter;
                    this._minDiningSpaceRect.updateRect();
                }
            }
        }
    }

    public get livingSpaceRect(): ZRect
    {
        return this._livingSpaceRect;
    }

    public get diningSpaceRect(): ZRect
    {
        return this._diningSpaceRect;
    }

    public set livingSpaceRect(rect: ZRect)
    {
        this._livingSpaceRect = rect;
    }

    public set diningSpaceRect(rect: ZRect)
    {
        this._diningSpaceRect = rect;
    }

    public get minLivingSpaceRect(): ZRect
    {
        return this._minLivingSpaceRect;
    }

    public get minDiningSpaceRect(): ZRect
    {
        return this._minDiningSpaceRect;
    }

    protected get otherSpaceRects(): ZRect[]
    {
        return this._otherSpaceRects;
    }

    protected decreaseLinearLenAndWidthRatioFunc(value: number, minValue: number, maxValue: number): number
    {
        return Math.ceil((value < 1 ? value : 1) * (minValue - maxValue) + maxValue);
    }
}
