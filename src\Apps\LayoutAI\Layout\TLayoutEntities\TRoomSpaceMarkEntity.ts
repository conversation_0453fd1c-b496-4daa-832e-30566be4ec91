import { Vector3 } from "three";
import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { ZPolygon } from "@layoutai/z_polygon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { IRoomEntityType } from '../IRoomInterface';

export class TRoomSpaceMarkEntity extends TBaseEntity {

    private _position: Vector3;
    private _color: string;
    private _isHovered: boolean = false;

    constructor(name: string, position: Vector3) {
        super();
        this._name = name;
        this.type = "RoomSpaceMark" as IRoomEntityType;
        this._position = position;
        this._color = ROOM_MARK_COLOR_MAP.get(name) || '#4A90E2';
        this._default_priority_for_selection = this._priority_for_selection = 3;
    }

    drawEntity(painter: TPainter, options?: any): void {
        const radius = 200;
        const borderWidth = 40;
        const segments = 64;

        // 创建圆形的顶点（用多边形近似圆形）
        const vertices: Vector3[] = [];

        // 先绘制白色边框圆
        for (let i = 0; i <= segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            const x = this._position.x + (radius + borderWidth) * Math.cos(angle);
            const y = this._position.y + (radius + borderWidth) * Math.sin(angle);
            vertices.push(new Vector3(x, y, 0));
        }

        let whiteBorderCircle = new ZPolygon();
        whiteBorderCircle.initByVertices(vertices);

        painter.fillStyle = '#FFFFFF';
        painter.strokeStyle = '#FFFFFF';
        painter._context.lineWidth = 0;
        painter.fillPolygons([whiteBorderCircle]);

        // 再绘制内部彩色圆
        vertices.length = 0;
        for (let i = 0; i <= segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            const x = this._position.x + radius * Math.cos(angle);
            const y = this._position.y + radius * Math.sin(angle);
            vertices.push(new Vector3(x, y, 0));
        }

        let coloredCircle = new ZPolygon();
        coloredCircle.initByVertices(vertices);

        painter.fillStyle = this._color;
        painter.strokeStyle = this._color;
        painter._context.lineWidth = 0;
        painter.fillPolygons([coloredCircle]);

        // 绘制文字
        painter.fillStyle = '#595959';
        const textY = this._position.y - radius - borderWidth - 120;
        let text = LayoutAI_App.t(this._name);
        painter.drawText(text, new Vector3(this._position.x, textY, 0), 0, 36, 10, false, true);

        // 如果被选中或悬停，绘制特殊效果
        if (options?.is_selected || this._isHovered) {
            painter.strokeStyle = "#147FFA";
            painter._context.lineWidth = 2;
            painter.strokePolygons([coloredCircle]);
        }
    }

    // 检查点是否在标记范围内
    containsPoint(point: Vector3): boolean {
        const dx = point.x - this._position.x;
        const dy = point.y - this._position.y;
        return Math.sqrt(dx * dx + dy * dy) <= 200;
    }

    // 移动位置
    movePosition(offset: Vector3): void {
        this._position.add(offset);
    }

    get position(): Vector3 {
        return this._position;
    }

    set position(pos: Vector3) {
        this._position = pos;
    }

    get name(): string {
        return this._name;
    }

    // 添加悬停状态设置方法
    setHovered(hovered: boolean): void {
        this._isHovered = hovered;
    }

    isHovered(): boolean {
        return this._isHovered;
    }
}

// 房间类型对应的颜色映射
export const ROOM_MARK_COLOR_MAP: Map<string, string> = new Map([
    ['客餐厅', '#8dc452'],
    ['卧室', '#c64883'],
    ['厨房', '#6a9cc4'],
    ['卫生间', '#78438b'],
    ['阳台', '#3a5574']
]);