import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { LayoutAI_App } from '../../LayoutAI_App';
import { TAppManagerBase } from "../../AppManagerBase";
import { TFurnitureEntity } from "../Layout/TLayoutEntities/TFurnitureEntity";
import { DrawingFigureMode } from "../Layout/IRoomInterface";
import { TViewCameraEntity } from "../Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";

export class TCadFurnitureLayer extends TDrawingLayer
{    

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadFurniture,manager);
        this._manager = manager;
    }



    get furnitureRects() {
        return this.ai_cad_data._figure_rects;
    }

    onDraw()
    {   
        if(this.ai_cad_data) {
            LayoutAI_App.instance._haveData = true;
        };

        this.painter.strokeStyle= "#f00";

        let entities = this.layout_container.getCandidateEntities(["Furniture"],{target_realtypes:["SoftFurniture","ViewCamera"]});

        entities.sort((a,b)=>{
            if (!a || !b) return 0;
            let a_entity = a as TFurnitureEntity;
            let b_entity =b as TFurnitureEntity;
            if (!a_entity || !b_entity) return 0;
            // 如果高度相同，则按照绘制顺序排序
            let h_offset = a_entity.rect.zval + a_entity.height - b_entity.rect.zval - b_entity.height;
            if(h_offset == 0)
                return a_entity._drawing_order - b_entity._drawing_order;
            else
                return h_offset;
        });

        // if(TViewCameraEntity._test_rect)
        // {
        //     this.painter.fillStyle = "#66b8ff";
        //     this.painter.fillPolygon(TViewCameraEntity._test_rect, 0.5);
        // }
        for(let entity of entities)
        {
            if(entity.type == "Group") continue;
            if(entity)
            {
                if(entity.is_selected)
                {
                    continue;
                }
                // console.log((entity as TFurnitureEntity).category, entity.rect.min_hh);
                entity.drawEntity(
                    this.painter,
                    {
                        is_draw_figure:true,
                        is_draw_texture:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Texture,
                        draw_decoration:true,
                        is_draw_outline:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Outline
                    }
                );   
            }

        }
    }


    onDrawCarpet()
    {
        if(this.ai_cad_data) {
            LayoutAI_App.instance._haveData = true;
        };

        this.painter.strokeStyle= "#f00";

        let entities = this.layout_container.getCandidateEntities(["Furniture"],{target_realtypes:["SoftFurniture","ViewCamera"]});

        entities.sort((a,b)=>{
            if (!a || !b) return 0;
            let a_entity = a as TFurnitureEntity;
            let b_entity = b as TFurnitureEntity;
            if (!a_entity || !b_entity) return 0;
            let h_offset = a_entity.rect.zval + a_entity.height - b_entity.rect.zval - b_entity.height;
            if(h_offset == 0)
                return a_entity._drawing_order - b_entity._drawing_order;
            else
                return h_offset;
        });
        for(let entity of entities)
        {
            if(entity.type== "Group") continue;
            if(entity)
            {
                if(entity.is_selected)
                {
                    entity.drawEntity(
                        this.painter,{is_selected:true,
                            is_draw_figure:true,
                            is_draw_texture:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Texture,
                            is_draw_outline:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Outline
                        }
                    );   
                    continue;
                }
                entity.drawEntity(
                    this.painter,{is_draw_figure:true,
                        is_draw_texture:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Texture,
                        is_draw_outline:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Outline
                    }
                );   
            }

        }
    }
}
