import { makeAutoObservable } from "mobx";

interface LightConfigGroupProps {
  groupName: string;
  groupData: LightParamData[];
}

interface LightParamData {
  lightName: string;
  brightness: number;
  color: string;
}

interface Template {
  id: string,
  templateName: string,
  templateImage: string,
  createDate: string,
  templateType: number,
  key: string,
}

const INITIAL_DAY_LIGHT_CONFIGS: LightConfigGroupProps[] = [
  {
    groupName: "客餐厅区域灯光",
    groupData: [
      { lightName: "吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "餐厅窗户灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "餐桌灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "客厅窗户灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "客厅门洞灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "客厅阳台门灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "客厅边缘灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "沙发灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "沙发侧光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "卧室区域灯光",
    groupData: [
      { lightName: "卧室窗户灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "床体灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "书房区域",
    groupData: [
      { lightName: "书房窗户灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "书桌灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "茶室区域",
    groupData: [
      { lightName: "茶室窗户灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "茶台灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "厨房区域",
    groupData: [
      { lightName: "厨房窗户灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "厨房灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "卫生间区域",
    groupData: [
      { lightName: "卫生间窗户灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "卫生间灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "公共区域",
    groupData: [
      { lightName: "过道灯光", brightness: 50, color: "#F5F0E1" },
    ]
  }
];
const INITIAL_NIGHT_LIGHT_CONFIGS: LightConfigGroupProps[] = [
  {
    groupName: "客餐厅区域灯光",
    groupData: [
      { lightName: "客餐厅吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "电视柜灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "餐桌灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "沙发灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "卧室区域灯光",
    groupData: [
      { lightName: "卧室吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "床尾顶光", brightness: 50, color: "#F5F0E1" },
      { lightName: "床尾侧光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "茶室/书房区域灯光",
    groupData: [
      { lightName: "茶室和书房吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "书桌灯光", brightness: 50, color: "#F5F0E1" },
      { lightName: "茶台灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "公共区域",
    groupData: [
      { lightName: "过道灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "厨房区域",
    groupData: [
      { lightName: "厨房灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
  {
    groupName: "卫生间区域",
    groupData: [
      { lightName: "卫生间灯光", brightness: 50, color: "#F5F0E1" },
    ]
  },
];
const init_envLightConfigs: LightParamData[] = [
  { lightName: "环境光", brightness: 50, color: "#F5F0E1" },
  { lightName: "阳光", brightness: 50, color: "#F5F0E1" },
  { lightName: "辅光", brightness: 50, color: "#F5F0E1" },
];
let DAYLIGHT_TARGET_TEMPLATE_LATEST =
{
  "lightMode": "Day",
  "autoLight": [] as any,
  "envLight": {}
};
const DAYLIGHT_TARGET_TEMPLATE = [
  {
    "name": "吊顶灯槽灯光",
    "typeId": 1,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 10,
      "targetObjectName": "id5251202_Node_3",
      "materialId": "368346740",
      "length": 16,
      "width": 16
    },
    "condition": {
      "roomName": "客餐厅|卧室|主卧|次卧|客卧"
    }
  },
  {
    "typeId": 101,
    "name": "过道灯光",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 0.8,
      "length": "25%",
      "width": "90%"
    },
    "pose": {
      "z": 2370
    },
    "condition": {
      "spaceArea": "过道区"
    }
  },
  {
    "name": "沙发灯光",
    "typeId": 2,
    "category": "沙发",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 6,
      "width": "80%",
      "length": "50%"
    },
    "pose": {
      "z": 2350,
      "gapOffset": 100
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "name": "沙发侧光",
    "typeId": 102,
    "category": "茶几",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 2,
      "width": 600,
      "length": 600
    },
    "pose": {
      "gapOffset": 550,
      "floorOffset": 1000,
      "lookAt": "center"
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "name": "客厅窗户灯光",
    "typeId": 103,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 4,
      "width": "140%",
      "length": "140%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1500,
      "lookAt": "center"
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "name": "客厅门洞灯光",
    "typeId": 104,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 4,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1000,
      "lookAt": "center"
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "name": "客厅阳台门灯光",
    "typeId": 105,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 4,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1000,
      "lookAt": "center"
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "name": "客厅边缘灯光",
    "typeId": 106,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 2.5,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "z": 1300,
      "lookAt": "center"
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "name": "餐厅窗户灯光",
    "typeId": 107,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 4,
      "width": "140%",
      "length": "140%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1500,
      "lookAt": "center"
    },
    "condition": {
      "spaceArea": "餐厅区"
    }
  },
  {
    "name": "餐桌灯光",
    "typeId": 3,
    "category": "餐桌",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 2,
      "width": "35%",
      "length": "35%"
    },
    "pose": {
      "z": 2370
    },
    "condition": {
      "spaceArea": "餐厅区"
    }
  },
  {
    "name": "厨房窗户灯光",
    "typeId": 108,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 24,
      "width": "140%",
      "length": "140%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1500,
      "lookAt": "center"
    },
    "condition": {
      "roomName": "厨房"
    }
  },
  {
    "name": "厨房灯光",
    "typeId": 4,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 10,
      "width": "30%",
      "length": "30%"
    },
    "pose": {
      "z": 2270
    },
    "condition": {
      "roomName": "厨房"
    }
  },
  {
    "name": "卧室窗户灯光",
    "typeId": 109,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 8,
      "width": "140%",
      "length": "140%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1500,
      "lookAt": "center"
    },
    "condition": {
      "roomName": "卧室|主卧|次卧|客卧"
    }
  },
  {
    "name": "床体灯光",
    "typeId": 110,
    "category": "床",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 6,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "z": 2370,
      "align": "center"
    },
    "condition": {
      "roomName": "卧室|主卧|次卧|客卧"
    }
  },
  {
    "name": "卫生间窗户灯光",
    "typeId": 111,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 8,
      "width": "140%",
      "length": "140%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1500,
      "lookAt": "center"
    },
    "condition": {
      "roomName": "卫生间"
    }
  },
  {
    "name": "卫生间灯光",
    "typeId": 5,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 8,
      "width": "30%",
      "length": "30%"
    },
    "condition": {
      "roomName": "卫生间"
    }
  },
  {
    "name": "书房窗户灯光",
    "typeId": 112,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 6,
      "width": "140%",
      "length": "140%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1500,
      "lookAt": "center"
    },
    "condition": {
      "roomName": "书房"
    }
  },
  {
    "name": "书桌灯光",
    "typeId": 6,
    "category": "书桌",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 2,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "z": 2370
    },
    "condition": {
      "roomName": "书房|茶室"
    }
  },
  {
    "name": "茶室窗户灯光",
    "typeId": 113,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 4,
      "width": "140%",
      "length": "140%"
    },
    "pose": {
      "norOffset": 300,
      "floorOffset": 1500,
      "lookAt": "center"
    },
    "condition": {
      "roomName": "茶室"
    }
  },
  {
    "name": "茶台灯光",
    "typeId": 7,
    "category": "茶台",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 2,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "z": 2370
    },
    "condition": {
      "roomName": "茶室"
    }
  }
];
const NIGHTLIGHT_TARGET_TEMPLATE = [
  {
    "name": "客餐厅吊顶灯槽灯光",
    "typeId": 1,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 11,
      "targetObjectName": "id5251202_Node_3",
      "materialId": "368346740",
      "length": 16,
      "width": 16
    },
    "condition": {
      "roomName": "客餐厅"
    }
  },
  {
    "name": "卧室吊顶灯槽灯光",
    "typeId": 1,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 6,
      "targetObjectName": "id5251202_Node_3",
      "materialId": "368346740",
      "length": 16,
      "width": 16
    },
    "condition": {
      "roomName": "卧室|主卧|次卧|客卧"
    }
  },
  {
    "name": "茶室和书房吊顶灯槽灯光",
    "typeId": 1,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 6,
      "targetObjectName": "id5251202_Node_3",
      "materialId": "368346740",
      "length": 16,
      "width": 16
    },
    "condition": {
      "roomName": "书房|茶室"
    }
  },
  {
    "name": "沙发灯光",
    "typeId": 2,
    "category": "沙发",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 8,
      "width": "80%",
      "length": "50%"
    },
    "pose": {
      "z": 2150,
      "gapOffset": 100
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "typeId": 201,
    "name": "过道灯光",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 0.8,
      "length": "25%",
      "width": "90%"
    },
    "pose": {
      "z": 2370
    },
    "condition": {
      "spaceArea": "过道区"
    }
  },
  {
    "name": "电视柜灯光",
    "typeId": 202,
    "category": "电视柜",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 6,
      "width": "80%",
      "length": "50%"
    },
    "pose": {
      "z": 2150,
      "gapOffset": 425
    },
    "condition": {
      "spaceArea": "客厅区"
    }
  },
  {
    "name": "餐桌灯光",
    "typeId": 3,
    "category": "餐桌",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 6,
      "width": "100%",
      "length": "100%"
    },
    "pose": {
      "z": 2150
    },
    "condition": {
      "spaceArea": "餐厅区"
    }
  },
  {
    "name": "床尾顶光",
    "typeId": 203,
    "category": "床",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 7,
      "width": "50%",
      "length": "50%"
    },
    "pose": {
      "z": 2500,
      "align": "bottom"
    },
    "condition": {
      "roomName": "卧室|主卧|次卧|客卧"
    }
  },
  {
    "name": "床尾侧光",
    "typeId": 204,
    "category": "床",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 5,
      "width": "50%",
      "length": "50%"
    },
    "pose": {
      "gapOffset": 1200,
      "floorOffset": 1000,
      "lookAt": "center"
    },
    "condition": {
      "roomName": "卧室|主卧|次卧|客卧"
    }
  },
  {
    "name": "厨房灯光",
    "typeId": 4,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 45,
      "width": "30%",
      "length": "30%"
    },
    "pose": {
      "z": 2270
    },
    "condition": {
      "roomName": "厨房"
    }
  },
  {
    "name": "卫生间灯光",
    "typeId": 5,
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 26,
      "width": "30%",
      "length": "30%"
    },
    "condition": {
      "roomName": "卫生间"
    }
  },
  {
    "name": "书桌灯光",
    "typeId": 6,
    "category": "书桌",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 28,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "z": 2370
    },
    "condition": {
      "roomName": "书房|茶室"
    }
  },
  {
    "name": "茶台灯光",
    "typeId": 7,
    "category": "茶台",
    "lighting": {
      "type": 1,
      "color": 16777215,
      "intensity": 2,
      "width": "40%",
      "length": "40%"
    },
    "pose": {
      "z": 2370
    },
    "condition": {
      "roomName": "茶室"
    }
  }
];

export class LightTemplateStore {
  cachedImage: string | null = null;
  templateImage: string | null = null;
  envLightConfigs: LightParamData[] = [
    { lightName: "环境光", brightness: 50, color: "#F5F0E1" },
    { lightName: "阳光", brightness: 50, color: "#F5F0E1" },
    { lightName: "辅光", brightness: 50, color: "#F5F0E1" },
  ];
  dayLightConfigs: LightConfigGroupProps[] = [
    {
      groupName: "客餐厅区域灯光",
      groupData: [
        { lightName: "吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "餐厅窗户灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "餐桌灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "客厅窗户灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "客厅门洞灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "客厅阳台门灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "客厅边缘灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "沙发灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "沙发侧光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "卧室区域灯光",
      groupData: [
        { lightName: "卧室窗户灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "床体灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "书房区域",
      groupData: [
        { lightName: "书房窗户灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "书桌灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "茶室区域",
      groupData: [
        { lightName: "茶室窗户灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "茶台灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "厨房区域",
      groupData: [
        { lightName: "厨房窗户灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "厨房灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "卫生间区域",
      groupData: [
        { lightName: "卫生间窗户灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "卫生间灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "公共区域",
      groupData: [
        { lightName: "过道灯光", brightness: 50, color: "#F5F0E1" },
      ]
    }
  ];
  nightLightConfigs: LightConfigGroupProps[] = [
    {
      groupName: "客餐厅区域灯光",
      groupData: [
        { lightName: "客餐厅吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "电视柜灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "餐桌灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "沙发灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "卧室区域灯光",
      groupData: [
        { lightName: "卧室吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "床尾顶光", brightness: 50, color: "#F5F0E1" },
        { lightName: "床尾侧光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "茶室/书房区域灯光",
      groupData: [
        { lightName: "茶室和书房吊顶灯槽灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "书桌灯光", brightness: 50, color: "#F5F0E1" },
        { lightName: "茶台灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "公共区域",
      groupData: [
        { lightName: "过道灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "厨房区域",
      groupData: [
        { lightName: "厨房灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
    {
      groupName: "卫生间区域",
      groupData: [
        { lightName: "卫生间灯光", brightness: 50, color: "#F5F0E1" },
      ]
    },
  ];
  templateData: string | null = null;
  dataUrl: string | null = null;
  templateInfo = {
    name: "",
    category: "dayLight",
  };
  templateList: Template[] = [];

  constructor() {
    makeAutoObservable(this);
  }

  /**
  * 更新指定分类下的灯光亮度
  * @param category 分类（dayLight / nightLight）
  * @param groupIndex 配置组索引（如“客餐厅区域灯光”所在的数组索引）
  * @param lightIndex 灯光项索引（如“客餐厅吊顶灯槽灯光”所在的数组索引）
  * @param brightness 新的亮度值
  */
  updateLightBrightness = (
    category: "dayLight" | "nightLight" | "envLight",
    groupIndex: number,
    lightIndex: number,
    brightness: number
  ) => {
    let configs = null;

    if (category === "envLight") {
      configs = this.envLightConfigs;
      configs[lightIndex].brightness = brightness;
    }
    else {
      if (category === "dayLight") configs = this.dayLightConfigs;
      else configs = this.nightLightConfigs;
      if (
        groupIndex >= 0 && groupIndex < configs?.length &&
        lightIndex >= 0 && lightIndex < configs[groupIndex].groupData?.length
      ) {
        configs[groupIndex].groupData[lightIndex].brightness = brightness;
      }
    }
  };

  /**
   * 更新指定分类下的灯光颜色
   * @param category 分类（dayLight / nightLight）
   * @param groupIndex 配置组索引
   * @param lightIndex 灯光项索引
   * @param color 新的颜色值（如#FFFFFF）
   */
  updateLightColor = (
    category: "dayLight" | "nightLight" | "envLight",
    groupIndex: number,
    lightIndex: number,
    color: string
  ) => {

    let configs = null;

    if (category === "envLight") {
      configs = this.envLightConfigs;
      configs[lightIndex].color = color;
    }
    else {
      if (category === "dayLight") configs = this.dayLightConfigs;
      else configs = this.nightLightConfigs;
      if (
        groupIndex >= 0 && groupIndex < configs?.length &&
        lightIndex >= 0 && lightIndex < configs[groupIndex].groupData?.length
      ) {
        configs[groupIndex].groupData[lightIndex].color = color;
      }
    }
  };

  /**
  * 重置指定分类的灯光配置为初始值
  * @param category 分类（dayLight / nightLight）
  */
  resetLightConfigs = (category: string) => {
    if (category === "dayLight") {
      // 使用初始配置常量重置      
      this.dayLightConfigs = JSON.parse(JSON.stringify(INITIAL_DAY_LIGHT_CONFIGS));
      this.envLightConfigs = JSON.parse(JSON.stringify(init_envLightConfigs));
    } else {
      this.nightLightConfigs = JSON.parse(JSON.stringify(INITIAL_NIGHT_LIGHT_CONFIGS));
    }
  };
  setDayLightConfigs = (value: LightConfigGroupProps[]) => {
    this.dayLightConfigs = value;
  }
  setDayLightEnvLight = (original: any) => {
    const nameMap = {
      ambient: "环境光",
      sunlight: "阳光",
      subSunLight: "辅光"
    };

    this.envLightConfigs = Object.entries(original).map(([key, value]) => ({
      lightName: nameMap[key as keyof typeof nameMap],
      brightness: (value as any).intensity,
      color: `#${(value as any).color.toString(16).padStart(6, '0').toUpperCase()}`
    }));

    console.log('setDayLightEnvLight', this.envLightConfigs)

  }
  setNightLightConfigs = (value: LightConfigGroupProps[]) => {
    this.nightLightConfigs = value;
  }
  /**
   * 恢复所有配置为默认值
   * 包括灯光配置、模板信息和图片相关属性
   */
  restoreDefaultConfig = () => {
    // 重置灯光配置    
    this.dayLightConfigs = JSON.parse(JSON.stringify(INITIAL_DAY_LIGHT_CONFIGS));
    this.nightLightConfigs = JSON.parse(JSON.stringify(INITIAL_NIGHT_LIGHT_CONFIGS));
    // 重置图片相关属性    
    this.cachedImage = null;
    this.templateImage = null;
    this.templateData = null;
    this.dataUrl = null;
    // 重置模板信息    
    this.templateInfo = {
      name: "",
      category: "dayLight",
    };
  };
  /**
   * 设置缓存图片（用于上传预览）
   * @param imageUrl 图片预览URL
   * @param file 原始文件对象（可选，用于后续提交）
   */
  setCachedImage = (imageUrl: string | null, file?: File) => {
    this.cachedImage = imageUrl;
  };
  setTemplateImage = (imageUrl: string | null, file?: File) => {
    this.templateImage = imageUrl;
  };
  setDataUrl = (Url: string | null, file?: File) => {
    this.dataUrl = Url;
  };

  /**
   * 设置灯光类型
   * @param category 灯光类型
   */
  setCategory = (category: string) => {
    this.templateInfo.category = category;
  };

  /** 初始化模板列表 */
  initTemplateList = (templates: Template[]) => {
    this.templateList = [...templates];
  };

  /** 更新模板封面 */
  updateTemplateImage = (id: string, imageUrl: string) => {
    this.templateList = this.templateList.map(tpl =>
      tpl.id === id ? { ...tpl, templateImage: imageUrl } : tpl
    );
    this.setTemplateImage(imageUrl); // 同步更新当前模板图片
  };

  /** 更新模板名称 */
  updateTemplateName = (key: string, newName: string) => {
    this.templateList = this.templateList.map(tpl =>
      tpl
        .key === key ? { ...tpl, templateName: newName } :
        tpl
    );
    this.templateInfo.name = newName; // 同步更新当前模板信息
  };

  /** 删除模板 */
  deleteTemplate = (key: string) => {
    this.templateList = this.templateList.filter(tpl => tpl.key !== key);
  };

  /** 新增模板 */
  addTemplate
    = (template: Omit<Template, 'key'>) => {
      const newTemplate = { ...template, key: `${Date.now()}` }; // 生成唯一key
      this.templateList = [...this.templateList, newTemplate];
    };

  /**
 * 将RGBA颜色字符串转换为十六进制颜色数值
 * @param rgbaStr RGBA颜色字符串，如"rgba(255, 255, 255, 1)"
 * @returns 十六进制颜色数值
 */
  private rgbaToHex(rgbaStr: string): number {
    // 匹配RGBA格式，提取红、绿、蓝分量    
    const rgbaMatch = rgbaStr.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]*\s*\)/);
    if (!rgbaMatch) {
      throw new Error(`无效的RGBA颜色格式: ${rgbaStr}`);
    }
    // 提取RGB值并确保在0-255范围内    
    const r = Math.min(255, Math.max(0, parseInt(rgbaMatch[1], 10)));
    const g = Math.min(255, Math.max(0, parseInt(rgbaMatch[2], 10)));
    const b = Math.min(255, Math.max(0, parseInt(rgbaMatch[3], 10)));
    // 转换为十六进制字符串    
    const toHex = (value: number) => {
      const hex = value.toString(16);
      return hex?.length === 1 ? `0${hex}` : hex;
    };
    const hexStr = `${toHex(r)}${toHex(g)}${toHex(b)}`;
    return parseInt(hexStr, 16);
  }
  /**
     * 格式化灯光配置，根据类别自动处理并使用内部存储的目标模板，返回JSON字符串
     * @param category 配置类别 ('dayLight' 或 'nightLight')
     * @returns 更新后的灯光配置模板的JSON字符串
     */
  formatLightConfigsByCategory = (
    category: 'dayLight' | 'nightLight'
  ): string => {
    const sourceConfigs = category === 'dayLight'
      ? this.dayLightConfigs
      : this.nightLightConfigs;

    const targetTemplates = category === 'dayLight'
      ? DAYLIGHT_TARGET_TEMPLATE
      : NIGHTLIGHT_TARGET_TEMPLATE;

    const updatedTemplates = JSON.parse(JSON.stringify(targetTemplates));

    const sourceMap = new Map<string, { brightness: number; color: string }>();
    sourceConfigs.forEach((group, groupIndex) => {
      group.groupData.forEach(light => {
        sourceMap.set(light.lightName, {
          brightness: light.brightness,
          color: light.color
        });;
      });
    });

    updatedTemplates.forEach((template: any, templateIndex: any) => {
      const sourceLight = sourceMap.get(template.name);

      if (sourceLight) {
        template.lighting.intensity = sourceLight.brightness;
        let colorValue: number;
        const colorStr = sourceLight.color.trim();
        try {
          if (colorStr.startsWith('rgba(')) {
            colorValue = this.rgbaToHex(colorStr);
          } else if (colorStr.startsWith('#')) {
            const colorHex = colorStr.replace('#', '');
            colorValue = parseInt(colorHex, 16);
          } else {
            console.warn(`不支持的颜色格式: ${colorStr}，使用默认值`);
            colorValue = 16777215;
          }
          template.lighting.color = colorValue;
        } catch (error) {
          console.error(`颜色转换失败: ${(error as Error).message}，使用默认值`);
          template.lighting.color = 16777215;
        }
      }
    });

    let jsonResult = '';
    if (category === 'dayLight') {
      const nameMap = {
        "环境光": "ambient",
        "阳光": "sunlight",
        "辅光": "subSunLight"
      };

      const colorForm = (colorStr:any)=>{
        if (colorStr.startsWith('rgba(')) {
          return this.rgbaToHex(colorStr);
        } else if (colorStr.startsWith('#')) {
          const colorHex = colorStr.replace('#', '');
          return parseInt(colorHex, 16);
        } 
      }

      const downloadEnvLight = Object.fromEntries(
        this.envLightConfigs.map(item => [
          nameMap[item.lightName as keyof typeof nameMap],
          {
            intensity: item.brightness,
            color: colorForm(item.color)
          }
        ])
      );

      DAYLIGHT_TARGET_TEMPLATE_LATEST["autoLight"] = updatedTemplates;
      DAYLIGHT_TARGET_TEMPLATE_LATEST["envLight"] = downloadEnvLight;
      jsonResult = JSON.stringify(DAYLIGHT_TARGET_TEMPLATE_LATEST, null, 2);

    } else {
      jsonResult = JSON.stringify(updatedTemplates, null, 2);
    }

    return jsonResult;
  };
  /**
   * 验证JSON结构是否匹配目标模板
   * @param jsonData 导入的JSON数据
   * @param targetTemplate 目标模板
   * @returns 是否匹配
   */
  validateJsonStructure = (jsonData: any[], targetTemplate: any[]): boolean => {
    // 验证数组长度是否一致
    if (jsonData?.length !== targetTemplate?.length) {
      console.log(`结构验证失败：长度不匹配（导入: ${jsonData?.length}, 模板: ${targetTemplate?.length}`);
      return false;
    }

    // 验证每个项目的结构
    for (let i = 0; i < jsonData?.length; i++) {
      const importItem = jsonData[i];
      const templateItem = targetTemplate[i];

      // 验证必须包含的字段
      if (!importItem.name || !importItem.lighting) {
        console.log(`结构验证失败：第${i}项缺少必要字段`);
        return false;
      }

      // 验证name是否匹配
      if (importItem.name !== templateItem.name) {
        console.log(`结构验证失败：第${i}项名称不匹配（导入: ${importItem.name}, 模板: ${templateItem.name}`);
        return false;
      }

      // 验证lighting对象结构
      if (typeof importItem.lighting.intensity !== 'number' || typeof importItem.lighting.color !== 'number') {
        console.log(`结构验证失败：第${i}项灯光属性格式错误`);
        return false;
      }
    }

    return true;
  };

  /**
 * 将导入的JSON数据转换为LightConfigGroupProps格式
 * @param jsonData 导入的JSON数据
 * @param category 配置类别
 * @returns 转换后的LightConfigGroupProps数组
 */
  convertToLightConfigGroup = (jsonData: any[]): LightConfigGroupProps[] => {
    // 验证结构并判断类型
    let category: 'dayLight' | 'nightLight' | null = null;

    if (this.validateJsonStructure(jsonData, DAYLIGHT_TARGET_TEMPLATE)) {
      console.log('JSON数据匹配成功：日间灯光');
      category = 'dayLight';
      this.setCategory('dayLight');
    } else if (this.validateJsonStructure(jsonData, NIGHTLIGHT_TARGET_TEMPLATE)) {
      console.log('JSON数据匹配成功：夜间灯光');
      category = 'nightLight';
      this.setCategory('nightLight');
    }

    // 根据类别获取对应的初始配置作为结构参考
    const referenceConfig = category === 'dayLight'
      ? INITIAL_DAY_LIGHT_CONFIGS
      : INITIAL_NIGHT_LIGHT_CONFIGS;

    // 创建映射表便于快速查找
    const dataMap = new Map<string, { intensity: number; color: number }>();
    jsonData.forEach(item => {
      dataMap.set(item.name, {
        intensity: item.lighting.intensity,
        color: item.lighting.color
      });
    });

    // 按照参考配置的结构转换数据
    return referenceConfig.map(group => ({
      ...group,
      groupData: group.groupData.map((light: LightParamData) => {
        const matchedData = dataMap.get(light.lightName);
        if (matchedData) {
          return {
            ...light,
            brightness: matchedData.intensity,
            // 将数值颜色转换为十六进制字符串
            color: `#${matchedData.color.toString(16).padStart(6, '0').toUpperCase()}`
          };
        }
        return light;
      })
    }));
  };
}

// 创建实例并导出
export const lightTemplateStore = new LightTemplateStore();
