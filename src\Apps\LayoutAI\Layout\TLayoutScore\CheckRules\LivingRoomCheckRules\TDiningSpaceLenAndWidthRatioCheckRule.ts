import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomSplitByPathClassfierCheckRule } from "./TLivingRoomSplitByPathClassfierCheckRule";

export class TDiningSpaceLenAndWidthRatioCheckRule extends TLivingRoomSplitByPathClassfierCheckRule
{
    private minLinearScore: number = 0;
    private maxLinearScore: number = 20;
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) 
    {
        void(room);
        void(figure_element);
        void(main_figures);

        let  score: number = 0;
        if(this.diningSpaceRect)
        {
            let len: number = this.diningSpaceRect.length;
            let depth: number = this.diningSpaceRect.depth;
            let ratio: number = len / depth;
            score = this.decreaseLinearLenAndWidthRatioFunc(Math.abs(1 - ratio), this.minLinearScore, this.maxLinearScore);
        }

        return {score: score};
    }
}