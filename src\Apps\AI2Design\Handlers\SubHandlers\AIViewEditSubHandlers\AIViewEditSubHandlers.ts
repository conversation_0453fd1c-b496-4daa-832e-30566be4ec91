import { AI2Base<PERSON>odeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { IRoomEntityRealType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TBaseGroupEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity";


export class AIViewEditSubHandlers extends CadBaseSubHandler
{
    constructor(cad_mode_handler:AI2BaseModeHandler)
    {
        super(cad_mode_handler);
    }
    

    onmousedown(ev: I_MouseEvent): void {
        this.updateCandidateRects();
        super.onmousedown(ev);
    }
    // 空间被选中
    onRoomEntitySelected(entity: TRoomEntity): Promise<void> {

        
        if(!entity)
        {
            this.manager.layout_container._selected_room = null;
        }
        else{
            let room_entity = entity as TRoomEntity;
            this.manager.layout_container._selected_room = room_entity.makeTRoom(room_entity.furniture_entities, false);
        }
        return;
    }

    updateAttributes(mode?: "init" | "hide" | "edit"): Promise<void> {

        return;
    }
    updateCandidateRects() {
        let ignore_realtypes: IRoomEntityRealType[] = [
            "Decoration",
            "Electricity",
            "BayWindow",
            "OneWindow",
            ...(!this.manager.layer_CadFurnitureLayer.visible ? ["SoftFurniture" as IRoomEntityRealType] : []),
            ...(!this.manager.layer_CadCabinetLayer.visible ? ["Cabinet" as IRoomEntityRealType] : []),
            ...(!this.manager.layer_LightingLayer.visible ? ["Lighting" as IRoomEntityRealType] : [])
        ];

        this.candidate_rects.length = 0;

        let candidate_rects = this.container.getCandidateRects([]
             ,{ignore_realtypes:ignore_realtypes}, {"Funiture":10,"Door":2,"Window":2},true);

        if (this.container.entity_selector.combination_edit == true) {
            let new_candidate_rects = [];
            for (let rect of candidate_rects) {
                if (rect.ex_prop["poly_target_type"] !== "BaseGroup") {
                    new_candidate_rects.push(rect);
                } else {
                    let group_entity:TBaseGroupEntity = TBaseGroupEntity.getOrMakeEntityOfCadRect(rect) as TBaseGroupEntity;
                    group_entity.combination_entitys.forEach(entity => {
                        new_candidate_rects.push(entity.rect);
                    });
                }
            }
            candidate_rects = new_candidate_rects;
        }

        this.candidate_rects.push(...candidate_rects);

        this.updateExsorbRects();
    }
}