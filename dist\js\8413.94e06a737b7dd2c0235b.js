"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[8413],{68413:function(t,e,o){o.d(e,{A:function(){return B}});var n=o(41594),r=o(97500),a=o.n(r),i=o(31135),s=o(98352),l=o(46965),c=o(39064),d=o(58058),u=o(94054),m=o(91483),b=o(8931),g=o(22623);const p=new d.<PERSON>("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),f=new d.Mo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),$=new d.Mo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),h=new d.Mo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),v=new d.Mo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),y=new d.Mo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),O=t=>{const{fontHeight:e,lineWidth:o,marginXS:n,colorBorderBg:r}=t,a=e,i=o,s=t.colorTextLightSolid,l=t.colorError,c=t.colorErrorHover;return(0,b.oX)(t,{badgeFontHeight:a,badgeShadowSize:i,badgeTextColor:s,badgeColor:l,badgeColorHover:c,badgeShadowColor:r,badgeProcessingDuration:"1.2s",badgeRibbonOffset:n,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},C=t=>{const{fontSize:e,lineHeight:o,fontSizeSM:n,lineWidth:r}=t;return{indicatorZIndex:"auto",indicatorHeight:Math.round(e*o)-2*r,indicatorHeightSM:e,dotSize:n/2,textFontSize:n,textFontSizeSM:n,textFontWeight:"normal",statusSize:n/2}};var S=(0,g.OF)("Badge",(t=>(t=>{const{componentCls:e,iconCls:o,antCls:n,badgeShadowSize:r,textFontSize:a,textFontSizeSM:i,statusSize:s,dotSize:l,textFontWeight:c,indicatorHeight:b,indicatorHeightSM:g,marginXS:O,calc:C}=t,S=`${n}-scroll-number`,x=(0,m.A)(t,((t,o)=>{let{darkColor:n}=o;return{[`&${e} ${e}-color-${t}`]:{background:n,[`&:not(${e}-count)`]:{color:n},"a:hover &":{background:n}}}}));return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${e}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:t.indicatorZIndex,minWidth:b,height:b,color:t.badgeTextColor,fontWeight:c,fontSize:a,lineHeight:(0,d.zA)(b),whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:C(b).div(2).equal(),boxShadow:`0 0 0 ${(0,d.zA)(r)} ${t.badgeShadowColor}`,transition:`background ${t.motionDurationMid}`,a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},[`${e}-count-sm`]:{minWidth:g,height:g,fontSize:i,lineHeight:(0,d.zA)(g),borderRadius:C(g).div(2).equal()},[`${e}-multiple-words`]:{padding:`0 ${(0,d.zA)(t.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${e}-dot`]:{zIndex:t.indicatorZIndex,width:l,minWidth:l,height:l,background:t.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${(0,d.zA)(r)} ${t.badgeShadowColor}`},[`${e}-count, ${e}-dot, ${S}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${o}-spin`]:{animationName:y,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${e}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${e}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:s,height:s,verticalAlign:"middle",borderRadius:"50%"},[`${e}-status-success`]:{backgroundColor:t.colorSuccess},[`${e}-status-processing`]:{overflow:"visible",color:t.colorInfo,backgroundColor:t.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:r,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:p,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${e}-status-default`]:{backgroundColor:t.colorTextPlaceholder},[`${e}-status-error`]:{backgroundColor:t.colorError},[`${e}-status-warning`]:{backgroundColor:t.colorWarning},[`${e}-status-text`]:{marginInlineStart:O,color:t.colorText,fontSize:t.fontSize}}}),x),{[`${e}-zoom-appear, ${e}-zoom-enter`]:{animationName:f,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`${e}-zoom-leave`]:{animationName:$,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`&${e}-not-a-wrapper`]:{[`${e}-zoom-appear, ${e}-zoom-enter`]:{animationName:h,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`${e}-zoom-leave`]:{animationName:v,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`&:not(${e}-status)`]:{verticalAlign:"middle"},[`${S}-custom-component, ${e}-count`]:{transform:"none"},[`${S}-custom-component, ${S}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[S]:{overflow:"hidden",transition:`all ${t.motionDurationMid} ${t.motionEaseOutBack}`,[`${S}-only`]:{position:"relative",display:"inline-block",height:b,transition:`all ${t.motionDurationSlow} ${t.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${S}-only-unit`]:{height:b,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${S}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${e}-count, ${e}-dot, ${S}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}})(O(t))),C);var x=(0,g.OF)(["Badge","Ribbon"],(t=>(t=>{const{antCls:e,badgeFontHeight:o,marginXS:n,badgeRibbonOffset:r,calc:a}=t,i=`${e}-ribbon`,s=`${e}-ribbon-wrapper`,l=(0,m.A)(t,((t,e)=>{let{darkColor:o}=e;return{[`&${i}-color-${t}`]:{background:o,color:o}}}));return{[s]:{position:"relative"},[i]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,u.dF)(t)),{position:"absolute",top:n,padding:`0 ${(0,d.zA)(t.paddingXS)}`,color:t.colorPrimary,lineHeight:(0,d.zA)(o),whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,[`${i}-text`]:{color:t.badgeTextColor},[`${i}-corner`]:{position:"absolute",top:"100%",width:r,height:r,color:"currentcolor",border:`${(0,d.zA)(a(r).div(2).equal())} solid`,transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),l),{[`&${i}-placement-end`]:{insetInlineEnd:a(r).mul(-1).equal(),borderEndEndRadius:0,[`${i}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${i}-placement-start`]:{insetInlineStart:a(r).mul(-1).equal(),borderEndStartRadius:0,[`${i}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}})(O(t))),C);var w=t=>{const{className:e,prefixCls:o,style:r,color:i,children:l,text:d,placement:u="end",rootClassName:m}=t,{getPrefixCls:b,direction:g}=n.useContext(c.QO),p=b("ribbon",o),f=`${p}-wrapper`,[$,h,v]=x(p,f),y=(0,s.nP)(i,!1),O=a()(p,`${p}-placement-${u}`,{[`${p}-rtl`]:"rtl"===g,[`${p}-color-${i}`]:y},e),C={},S={};return i&&!y&&(C.background=i,S.color=i),$(n.createElement("div",{className:a()(f,m,h,v)},l,n.createElement("div",{className:a()(O,h),style:Object.assign(Object.assign({},C),r)},n.createElement("span",{className:`${p}-text`},d),n.createElement("div",{className:`${p}-corner`,style:S}))))};const N=t=>{const{prefixCls:e,value:o,current:r,offset:i=0}=t;let s;return i&&(s={position:"absolute",top:`${i}00%`,left:0}),n.createElement("span",{style:s,className:a()(`${e}-only-unit`,{current:r})},o)};function j(t,e,o){let n=t,r=0;for(;(n+10)%10!==e;)n+=o,r+=o;return r}var k=t=>{const{prefixCls:e,count:o,value:r}=t,a=Number(r),i=Math.abs(o),[s,l]=n.useState(a),[c,d]=n.useState(i),u=()=>{l(a),d(i)};let m,b;if(n.useEffect((()=>{const t=setTimeout(u,1e3);return()=>clearTimeout(t)}),[a]),s===a||Number.isNaN(a)||Number.isNaN(s))m=[n.createElement(N,Object.assign({},t,{key:a,current:!0}))],b={transition:"none"};else{m=[];const e=a+10,o=[];for(let t=a;t<=e;t+=1)o.push(t);const r=c<i?1:-1,l=o.findIndex((t=>t%10===s));m=(r<0?o.slice(0,l+1):o.slice(l)).map(((e,o)=>{const a=e%10;return n.createElement(N,Object.assign({},t,{key:e,value:a,offset:r<0?o-l:o,current:o===l}))})),b={transform:`translateY(${-j(s,a,r)}00%)`}}return n.createElement("span",{className:`${e}-only`,style:b,onTransitionEnd:u},m)},E=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(o[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(o[n[r]]=t[n[r]])}return o};const z=n.forwardRef(((t,e)=>{const{prefixCls:o,count:r,className:i,motionClassName:s,style:d,title:u,show:m,component:b="sup",children:g}=t,p=E(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=n.useContext(c.QO),$=f("scroll-number",o),h=Object.assign(Object.assign({},p),{"data-show":m,style:d,className:a()($,i,s),title:u});let v=r;if(r&&Number(r)%1==0){const t=String(r).split("");v=n.createElement("bdi",null,t.map(((e,o)=>n.createElement(k,{prefixCls:$,count:Number(r),value:e,key:t.length-o}))))}return(null==d?void 0:d.borderColor)&&(h.style=Object.assign(Object.assign({},d),{boxShadow:`0 0 0 1px ${d.borderColor} inset`})),g?(0,l.Ob)(g,(t=>({className:a()(`${$}-custom-component`,null==t?void 0:t.className,s)}))):n.createElement(b,Object.assign({},h,{ref:e}),v)}));var I=z,F=function(t,e){var o={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(o[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(o[n[r]]=t[n[r]])}return o};const R=n.forwardRef(((t,e)=>{var o,r,d,u,m;const{prefixCls:b,scrollNumberPrefixCls:g,children:p,status:f,text:$,color:h,count:v=null,overflowCount:y=99,dot:O=!1,size:C="default",title:x,offset:w,style:N,className:j,rootClassName:k,classNames:E,styles:z,showZero:R=!1}=t,T=F(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:B,direction:M,badge:P}=n.useContext(c.QO),A=B("badge",b),[D,H,W]=S(A),Z=v>y?`${y}+`:v,X="0"===Z||0===Z,q=(null!=f||null!=h)&&(null===v||X&&!R),_=O&&!X,Q=_?"":Z,L=(0,n.useMemo)((()=>(null==Q||""===Q||X&&!R)&&!_),[Q,X,R,_]),V=(0,n.useRef)(v);L||(V.current=v);const Y=V.current,G=(0,n.useRef)(Q);L||(G.current=Q);const J=G.current,K=(0,n.useRef)(_);L||(K.current=_);const U=(0,n.useMemo)((()=>{if(!w)return Object.assign(Object.assign({},null==P?void 0:P.style),N);const t={marginTop:w[1]};return"rtl"===M?t.left=parseInt(w[0],10):t.right=-parseInt(w[0],10),Object.assign(Object.assign(Object.assign({},t),null==P?void 0:P.style),N)}),[M,w,N,null==P?void 0:P.style]),tt=null!=x?x:"string"==typeof Y||"number"==typeof Y?Y:void 0,et=L||!$?null:n.createElement("span",{className:`${A}-status-text`},$),ot=Y&&"object"==typeof Y?(0,l.Ob)(Y,(t=>({style:Object.assign(Object.assign({},U),t.style)}))):void 0,nt=(0,s.nP)(h,!1),rt=a()(null==E?void 0:E.indicator,null===(o=null==P?void 0:P.classNames)||void 0===o?void 0:o.indicator,{[`${A}-status-dot`]:q,[`${A}-status-${f}`]:!!f,[`${A}-color-${h}`]:nt}),at={};h&&!nt&&(at.color=h,at.background=h);const it=a()(A,{[`${A}-status`]:q,[`${A}-not-a-wrapper`]:!p,[`${A}-rtl`]:"rtl"===M},j,k,null==P?void 0:P.className,null===(r=null==P?void 0:P.classNames)||void 0===r?void 0:r.root,null==E?void 0:E.root,H,W);if(!p&&q){const t=U.color;return D(n.createElement("span",Object.assign({},T,{className:it,style:Object.assign(Object.assign(Object.assign({},null==z?void 0:z.root),null===(d=null==P?void 0:P.styles)||void 0===d?void 0:d.root),U)}),n.createElement("span",{className:rt,style:Object.assign(Object.assign(Object.assign({},null==z?void 0:z.indicator),null===(u=null==P?void 0:P.styles)||void 0===u?void 0:u.indicator),at)}),$&&n.createElement("span",{style:{color:t},className:`${A}-status-text`},$)))}return D(n.createElement("span",Object.assign({ref:e},T,{className:it,style:Object.assign(Object.assign({},null===(m=null==P?void 0:P.styles)||void 0===m?void 0:m.root),null==z?void 0:z.root)}),p,n.createElement(i.Ay,{visible:!L,motionName:`${A}-zoom`,motionAppear:!1,motionDeadline:1e3},(t=>{let{className:e}=t;var o,r;const i=B("scroll-number",g),s=K.current,l=a()(null==E?void 0:E.indicator,null===(o=null==P?void 0:P.classNames)||void 0===o?void 0:o.indicator,{[`${A}-dot`]:s,[`${A}-count`]:!s,[`${A}-count-sm`]:"small"===C,[`${A}-multiple-words`]:!s&&J&&J.toString().length>1,[`${A}-status-${f}`]:!!f,[`${A}-color-${h}`]:nt});let c=Object.assign(Object.assign(Object.assign({},null==z?void 0:z.indicator),null===(r=null==P?void 0:P.styles)||void 0===r?void 0:r.indicator),U);return h&&!nt&&(c=c||{},c.background=h),n.createElement(I,{prefixCls:i,show:!L,motionClassName:e,className:l,count:J,title:tt,style:c,key:"scrollNumber"},ot)})),et))})),T=R;T.Ribbon=w;var B=T}}]);