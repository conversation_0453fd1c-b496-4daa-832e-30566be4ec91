import { Vector3 } from "three";
import { BaseLightHandler } from "./BaseLightHandler";
import { LightingRuler } from "../AutoLightingRuler";
import { RealType } from "../../../Scene3D/NodeName";
import { IRoomEntityRealType } from "../../../Layout/IRoomInterface";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { TWindowDoorEntity } from "../../../Layout/TLayoutEntities/TWinDoorEntity";

export class RoomWindowLightHandler extends BaseLightHandler {

    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        let entities: TFillLightEntity[] = [];
        let layout_container = this.getLayoutContainer();
        layout_container._room_entities.forEach(roomEntity => {
            if (this.checkCondition(ruler, roomEntity)) {
                let entityList = roomEntity.getWindowEntities()
                    .filter((winDoor) => {
                        return (winDoor as TWindowDoorEntity).isWindow &&
                            !((winDoor as TWindowDoorEntity).realType === RealType.Railing as IRoomEntityRealType)
                    }) as TWindowDoorEntity[];

                entityList.forEach(entity => {
                    let dot = this.getEntityToRoomDot(entity, roomEntity._main_rect.rect_center);
                    // 判断向量方向
                    let offset = ruler.pose.norOffset + entity.thickness;
                    if (dot > 0) {
                        offset = -offset;
                    }
                    const newRuler = { ...ruler, pose: { ...ruler.pose, norOffset: offset } };
                    let lightEntity = this.createWinDoorLight(newRuler, entity);
                    entities.push(lightEntity);
                });
            }
        });
        return entities;
    }

    protected getEntityToRoomDot(entity: TWindowDoorEntity, center: Vector3): number {
        let nor = entity.rect.nor;
        let entityCenter = entity.rect.rect_center;
        let roomCenter = center;
        let toRoom = new Vector3(roomCenter.x - entityCenter.x, roomCenter.y - entityCenter.y, 0);
        toRoom.normalize();
        return nor.dot(toRoom);
    }

    protected createWinDoorLight(ruler: LightingRuler, entity: TWindowDoorEntity): TFillLightEntity {
        let rect = entity.rect;
        let pos = this.getLightPos(rect, ruler);
        let rotation = this.getLightRotation(rect, ruler, pos);
        let lightEntity = this._createFillLightEntity(
            ruler.lighting.intensity,
            ruler.lighting.color,
            { x: pos.x, y: pos.y, z: pos.z },
            this.getSize(ruler.lighting.width, entity.length),
            this.getSize(ruler.lighting.length, entity.height),
            rotation.x,
            rotation.y,
            rotation.z + Math.PI / 2,
            ruler.name,
        );
        return lightEntity;
    }
}