import {
    IEdgeLayout,
    IKitchenLayout,
    KitchenAreaType,
    KitchenLayoutType
} from "@layoutai/layout_service";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoomLayoutScheme } from "../../../TLayoutScheme/TRoomLayoutScheme";
import { TRoom } from "../../../TRoom";
import { TCheckRule } from "../TCheckRule";

/**
 * @description 相邻评分
 * 烹饪区、水盆区左右至少有一个操作区
 * <AUTHOR>
 * @date 2025-08-30
 * @lastEditTime 2025-08-30 17:32:58
 * @lastEditors xuld
 */
export class TKitchenAreaOpCheckRule extends TCheckRule {
    checkValue(
        room: TRoom,
        figure_elements?: TFigureElement[],
        layout_scheme: TRoomLayoutScheme = null
    ) {
        if (!layout_scheme) {
            return { value: 0 };
        }

        let extra = layout_scheme.extra as IKitchenLayout;
        if (!extra) {
            return { value: 0 };
        }

        // 操作区相邻约束
        let hasOpArea = false;
        if (extra.layoutType === KitchenLayoutType.I_TYPE) {
            hasOpArea = this.hasOpArea(
                extra.edgeLayouts[0].areaTypeRects.map(item => item.areaType)
            );
        } else if (extra.layoutType === KitchenLayoutType.II_TYPE) {
            hasOpArea = this.hasOpArea(
                extra.edgeLayouts[0].areaTypeRects.map(item => item.areaType)
            );
            if (hasOpArea) {
                hasOpArea = this.hasOpArea(
                    extra.edgeLayouts[1].areaTypeRects.map(item => item.areaType)
                );
            }
        } else if (extra.layoutType === KitchenLayoutType.L_TYPE) {
            const edge0 = extra.edgeLayouts[0];
            const tps0 = extra.edgeLayouts[0].areaTypeRects.map(item => item.areaType);
            const tps1 = extra.edgeLayouts[1].areaTypeRects.map(item => item.areaType);
            hasOpArea = this.hasOpArea(tps0);
            if (hasOpArea) {
                hasOpArea = this.hasOpArea(tps1);
            }
            if (!hasOpArea) {
                hasOpArea = this.hasOpArea([...tps0, ...tps1]);
            }
        } else if (extra.layoutType === KitchenLayoutType.U_TYPE) {
            const tps0 = extra.edgeLayouts[0].areaTypeRects.map(item => item.areaType);
            const tps1 = extra.edgeLayouts[1].areaTypeRects.map(item => item.areaType);
            const tps2 = extra.edgeLayouts[2].areaTypeRects.map(item => item.areaType);

            hasOpArea = this.hasOpArea(tps0);
            if (hasOpArea) {
                hasOpArea = this.hasOpArea(tps1);
            }
            if (hasOpArea) {
                hasOpArea = this.hasOpArea(tps2);
            }
            if (!hasOpArea) {
                hasOpArea = this.hasOpArea([...tps0, ...tps1, ...tps2]);
            }
        }

        let score = 0;
        if (hasOpArea) {
            score += 1;
        }

        return { value: score };
    }

    private hasOpArea(tps: KitchenAreaType[]) {
        const indexCookingArea = tps.indexOf(KitchenAreaType.CookingArea);

        let cookHasOpArea = false;
        if (indexCookingArea !== -1) {
            const pre = indexCookingArea - 1;
            const next = indexCookingArea + 1;
            if (pre >= 0 && tps[pre] === KitchenAreaType.OperationArea) {
                cookHasOpArea = true;
            }
            if (next < tps.length && tps[next] === KitchenAreaType.OperationArea) {
                cookHasOpArea = true;
            }
        }

        const indexSinkArea = tps.indexOf(KitchenAreaType.SinkArea);

        let sinkHasOpArea = false;
        if (indexSinkArea !== -1) {
            const pre = indexSinkArea - 1;
            const next = indexSinkArea + 1;
            if (pre >= 0 && tps[pre] === KitchenAreaType.OperationArea) {
                sinkHasOpArea = true;
            }
            if (next < tps.length && tps[next] === KitchenAreaType.OperationArea) {
                sinkHasOpArea = true;
            }
        }

        return cookHasOpArea && sinkHasOpArea;
    }
}
