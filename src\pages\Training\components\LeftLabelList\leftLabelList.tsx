
import { useState } from 'react';
import useStyles from './style';
import { LayoutAI_App} from '@/Apps/LayoutAI_App';

export interface I_LableCommand{
    label: string, name: string, command_name?: string, subList?:I_LableCommand[]
}

export const ExpandLabelsData:{[key:string]:boolean} = {};
/**
 * @description 按钮组件
 */
export default function LeftLabelList(props:{labelList:I_LableCommand[]}) {
    const { styles } = useStyles();
    const [ExpandLabels, setExpandLabels] = useState<{[key:string]:boolean}>(ExpandLabelsData);
    const [checkLabel,setCheckLabel] = useState<string>("");
    const handleLabelNameClick = (item:I_LableCommand, index: number) => {
        if(item.subList)
        {
            if(ExpandLabelsData[item.label])
            {
                delete ExpandLabelsData[item.label];
            }
            else{
                ExpandLabelsData[item.label] = true;
            }
            setExpandLabels({...ExpandLabelsData});
        }
        if(item.command_name)
        {
            LayoutAI_App.RunCommand(item.command_name);
        }
        setCheckLabel(item.label);
    };
    if(!props.labelList || props.labelList.length == 0) return <></>;
  
    return (
        <div>
            {props.labelList.map((item, index) => (
                <div key={index} id={"left-menu-room-list-"+index} className={styles.menu+" "+(item.label===checkLabel?"checked":"")} data-room_name={item["name"]} onClick={()=>handleLabelNameClick(item,index)} >
                    <div className='label-text'>{item["label"]}</div>
                    {ExpandLabels && ExpandLabels[item.label] && item.subList && (item.subList as any[]).map((subItem,index)=><div key={"subItem"+index}
                     className={styles.menu+" "+(subItem.label===checkLabel?"checked":"")} data-room_name={subItem["name"]} onClick={(ev)=>{
                        ev.stopPropagation();
                        handleLabelNameClick(subItem,index)}}>
                         {subItem["label"]}
                    </div>)}
                </div>
            ))}
        </div>
    );
  }
  