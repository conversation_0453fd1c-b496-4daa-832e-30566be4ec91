import React, { useState } from 'react';
import { Uploader } from "@svg/oss-upload";
import { getSign } from '@/services/user';
import { message, Upload } from '@svg/antd';
import { UploadProps } from '@svg/antd/es/upload';

interface OssUploaderProps {
  /** 上传类型，默认12 */
  type?: number;
  /** 上传成功回调 */
  onSuccess?: (url: string) => void;
  /** 上传失败回调 */
  onError?: (error: any) => void;
  /** 是否显示上传列表 */
  showUploadList?: boolean;
  /** 上传列表类型 */
  listType?: UploadProps['listType'];
  /** 自定义上传按钮 */
  uploadButton?: React.ReactNode;
  /** 禁用上传 */
  disabled?: boolean;
}

const OssUploader: React.FC<OssUploaderProps> = ({
  type = 12,
  onSuccess,
  onError,
  showUploadList = false,
  listType = "picture-card",
  uploadButton,
  disabled = false,
}) => {
  const [loading, setLoading] = useState(false);

  /**
   * 创建上传实例
   * @param file 待上传文件
   * @returns 上传实例及相关配置
   */
  const createUploader = async (file: File) => {
    try {
      const res = await getSign(type, file.name);
      const {
        accessKeyId,
        expireAt,
        readDomain,
        policy,
        securityToken,
        signature,
        keyPrefix,
        vendor,
        contentType,
        ossHost
      } = res;

      return {
        uploader: new Uploader({
          contentType,
          policy,
          signature,
          accessKeyId,
          server: ossHost.replace('-internal', ''),
          expireAt,
          securityToken,
          path: keyPrefix,
          vendor: vendor as any
        } as any),
        readDomain,
        ossHost
      };
    } catch (error) {
      message.error('获取上传凭证失败');
      throw error;
    }
  };

  /**
   * 执行文件上传
   * @param file 待上传文件
   * @returns 上传后的文件URL
   */
  const uploadFile = async (file: File) => {
    try {
      const { uploader, readDomain, ossHost } = await createUploader(file);
      const data = await uploader.upload(file);
      const imageUrl = `${readDomain || ossHost.replace('-internal', '')}/${data.key}`;
      return imageUrl;
    } catch (error) {
      message.error('文件上传失败');
      throw error;
    }
  };

  /**
   * 上传前处理函数
   * @param file 待上传文件
   */
  const beforeUpload = async (file: File) => {
    if (disabled) return false;
    
    setLoading(true);
    try {
      const url = await uploadFile(file);
      onSuccess?.(url);
      message.success('上传成功');
    } catch (error) {
      onError?.(error);
    } finally {
      setLoading(false);
    }
    
    // 阻止默认上传行为，因为我们使用了自定义上传逻辑
    return false;
  };

  /**
   * 默认上传按钮
   */
  const defaultUploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <Upload
      name="avatar"
      listType={listType}
      className="avatar-uploader"
      showUploadList={showUploadList}
      beforeUpload={beforeUpload}
      disabled={disabled}
    >
      {uploadButton || defaultUploadButton}
    </Upload>
  );
};

export default OssUploader;
