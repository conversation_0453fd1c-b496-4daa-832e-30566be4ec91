
import { AI2<PERSON><PERSON><PERSON>odeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { I_MouseEvent, compareNames } from "@layoutai/z_polygon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";

import { BasicRoomAILayoutSubHandler } from "./BasicRoomAILayoutSubHandler";
import { TPostLayoutKitchenPart } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutKitchenPart";
import { ZPolygon } from "@layoutai/z_polygon";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { SolverMethods } from "@/Apps/LayoutAI/Layout/TAppSolvers/TSwjLayoutGraphSolver";


export class KitchenLayoutSubHand<PERSON> extends BasicRoomAILayoutSubHandler
{
    constructor( handler:AI2BaseModeHandler)
    {
        super(handler);
        this.name = "KitchenLayout";
    }   


    enter(state?: number): void {
        super.enter(state);


        this.postProccessKitchenLayout();
        this.update();
    }

    leave(state?: number): void {
        super.leave(state);
        if(this.current_room)
        {

            let room = this.current_room;
            if(room._room_entity)
            {
                this.container.cleanInRoomFurnitures(room);
                this.container.addFunitureEnitiesInRoom(room);
            }
            this.updateCandidateRects();
            // LayoutAI_App.emit(EventName.SelectingRoom, {current_rooms: this.current_rooms, event_param: data.value} );    
            this.update();
        }
    }
    async computeRoom(room:TRoom)
    {
        let solver_methods:SolverMethods[] = ["BasicTransfer","SpacePartition"];


        let result_scheme_list = await this.manager.layout_container.applyRoomWithSolvingMethods(room,solver_methods);

        this.update();

        return result_scheme_list || [];

  
    }

    postProccessKitchenLayout()
    {
        if(this.current_room)
        {
            let tri_figure_elements = this.current_room._furniture_list.filter((ele)=>compareNames([ele.sub_category],
                ["冰箱","水槽地柜","炉灶地柜"]));

            let group_templates = TPostLayoutKitchenPart.instance.postInfillLayout(this.current_room);

            if(group_templates)
            {
                this.current_room.resetFurnitureList();
                for(let group_template of group_templates)
                {
                    if(group_template.current_s_group)
                    {
                        if(compareNames([group_template.current_s_group.group_code],["冰箱","水槽地柜","炉灶地柜"]))
                        {

                            let ele = tri_figure_elements.find((ele)=>compareNames([group_template.current_s_group.group_code],[ele.sub_category]));
                            if(ele){
                                ele.rect.copy(group_template._target_rect);
                                this.current_room.addFurnitureElement(ele);
                                continue;
                            }
                        }
                        this.current_room.addFurnitureElements(group_template.current_s_group.figure_elements);
                    }
                }
            }
        }
    }

    postProcessInRoom(room: TRoom): void {
        this.postProccessKitchenLayout();
    }
    onkeyup(ev: KeyboardEvent): boolean {
        return true;
    }
    onmousemove(ev: I_MouseEvent): void {
        super.onmousemove(ev);

        if(this.handler._is_moving_element)
        {
            this.postProcessInRoom(this.current_room);
        }
    }
    onmouseup(ev: I_MouseEvent): void {
        super.onmouseup(ev);
        if(this.current_room)
        {

            let room = this.current_room;
            if(room._room_entity)
            {
                this.container.cleanInRoomFurnitures(room);
                this.container.addFunitureEnitiesInRoom(room);
            }
            this.updateCandidateRects();
            this.update();
        }
    }
    onEntityPanelUpdate()
    {
        this.postProccessKitchenLayout();

        if(LayoutAI_App.instance)
        {
            LayoutAI_App.instance.update();
        }
    }
    predictRoom(): Promise<void> {
        return;
    }
    drawCanvas(): void {
        super.drawCanvas();

        if(this.current_room)
        {
            let table_top_polys :ZPolygon[] = [];
            if(this.current_room._tabletop_list)
            {
                table_top_polys = this.current_room._tabletop_list.map((val)=>val._polygon);

                this.painter.strokeStyle = "#282828";
                this.painter.fillStyle = "#0af";

                // this.painter.fillPolygons(table_top_polys,0.05);
            
                for(let poly of table_top_polys)
                {
                    for(let edge of poly.edges)
                    {
                        if(edge._ex_props[TFigureElement.IsTableTopFrontEdge])
                        {
                            this.painter.strokeStyle = "#f00";
                        }
                        else{
                            this.painter.strokeStyle = "#282828";
                        }
                        this.painter.drawEdges([edge]);
                    }
                }
            }
        }
    }
    

    
}