import { openApiRequest } from "@/utils";
import { AxiosInstance } from "axios";


export type I_SdkMsgType = "Command" | "Event" | "Async" | "Connected"|"OpenRequest"|"Result";
export interface I_LayoutAI_MsgData {
    msgId?: string;

    msgType?: I_SdkMsgType;
    command?: string;
    eventName?: string;

    eventData?: { [key: string]: any };
    /**
     *  异步事件ID
     */
    asyncId?: string;

    /**
     *  异步事件名称
     */
    asyncEventName?: string;
    /**
     * asyncState: 0: querying; 1: done; -1: error;
     */
    asyncState?: number;
    /**
     *  异步事件结果
     */
    asyncResult?: any;

    aysncError?: string;

    aysncTimeout?: number;
    [key: string]: any;
}
export interface I_WorkerTask
{
    worker?:Worker, worker_id?:number, msgData?:I_LayoutAI_MsgData,resultData?:I_LayoutAI_MsgData,taskCount?:number
}/**
 *   读写Model3d文件的线程池
 */
export class Model3dWorkerPool 
{
    protected _workerUrl = "./js/model3d_api.js";
    protected _workerUrlCandidates = ["./js/model3d_api.js","./model3d_api.js"];
    protected _is_worker_ready = false;

    protected _async_queue: { msgData: I_LayoutAI_MsgData, callback?:(msgData:I_LayoutAI_MsgData)=>void, worker_task?:I_WorkerTask}[];

    protected _maxWorkNum = 4; // 暂时只支持4线程
    protected _maxTaskOnWorker = 2;

    protected _worker_task_list : I_WorkerTask[];
    protected _worker_id_counter : number;

    private static  _instance : Model3dWorkerPool = null;

    private _openRequest : AxiosInstance = null;
    constructor()
    {
        this._worker_task_list = [];
        this._async_queue = [];
        this._worker_id_counter = 0;

        this._openRequest = openApiRequest;
        this._openRequest.defaults.withCredentials = true;
    }
    public static get instance()
    {
        if(!Model3dWorkerPool._instance)
        {
            Model3dWorkerPool._instance = new Model3dWorkerPool();
        }
        return Model3dWorkerPool._instance;

    }
    public set workerUrl(s:string)
    {
        this._workerUrl = s;
        this._is_worker_ready = true;
    }
    public exec(data: I_LayoutAI_MsgData,callback?:(result:I_LayoutAI_MsgData)=>void) {
        if(!this._is_worker_ready) return false;
        this._async_queue.push({ msgData: data,callback:callback });
        this.startProcessAsyncMsg();
        return true;
    }

    public async execAsync(data:I_LayoutAI_MsgData) : Promise<I_LayoutAI_MsgData>
    {
        return await (new Promise<I_LayoutAI_MsgData>((resolve,reject)=>{
           let flag = this.exec(data,(result_data)=>{
                resolve(result_data);
            });
            if(!flag) resolve(null);
        }).catch((e)=>{
            console.log(e);
            return null;
        }));
    }
    setWorkNum(t:number)
    {
        this._maxWorkNum = t;
    }
    protected async _loadModelByWorker(worker_task:I_WorkerTask)
    {
        let worker = worker_task.worker;
        return await new Promise<I_WorkerTask>((resolve,reject)=>{
            worker.postMessage(worker_task.msgData);
            worker.onmessage = (ev)=>{
                let data = ev.data as I_LayoutAI_MsgData;
                if(data.msgType === "OpenRequest")
                {
                    let bodyData = data.eventData;
                    this._openRequest(bodyData).then((res)=>{
                        data.asyncResult = res;
                        worker.postMessage(data);
                    })
       
                }
                else{
                        if(data.msgType === "Async" && data.asyncResult)
                        {
                            worker_task.resultData = data;
                        }
                        resolve(worker_task);
                    }
                }

        }).catch(e=>{
            console.log(e);
            return worker_task;
        })

    }



    public async tryToFindModel3dWorkerUrl()
    {
        let isFound = false;
        for(let url of this._workerUrlCandidates)
        {
            let res = await fetch(url).then((val)=>true).catch((e)=>false);
            if(res){
                this._workerUrl = url;
                isFound = true;
                break;
            }
        }
        this._is_worker_ready = isFound;
        return isFound;
    }
    

    private _createWorkerTask()
    {
        this._worker_id_counter++;
        return {worker:new Worker(this._workerUrl,{type:"module"}),worker_id:this._worker_id_counter,taskCount:0};
    }

    protected _onDoneWorkerTask(worker_task:I_WorkerTask)
    {
        let queueId = this._async_queue.findIndex((data)=>data.msgData===worker_task.msgData);
        let result_data = worker_task.resultData;
        worker_task.msgData = null;
        worker_task.resultData = null;

        // if(worker_task.taskCount >= this._maxWorkNum)
        {
            worker_task.worker.terminate(); 
            let workerTaskId = this._worker_task_list.findIndex((data)=>data==worker_task);
            if(workerTaskId > -1)
            {
                this._worker_task_list.splice(workerTaskId,1);
            }
        }
        if(queueId >= 0)
        {
            let qData = this._async_queue[queueId];
            this._async_queue.splice(queueId,1);
            
            this.startProcessAsyncMsg();
            
            if(qData.callback)
            {
                qData.callback(result_data);
            }
        }



    }
    protected startProcessAsyncMsg() {
        let active_msg = this._async_queue.find((data)=>!data.worker_task); // 找一个不忙的
        if(!active_msg) return;

        if(this._worker_task_list.length < this._maxWorkNum)
        {
            this._worker_task_list.push(this._createWorkerTask());
        }
        let valid_task_worker = this._worker_task_list.find(task=>task.worker && !task.msgData);
        
        if(valid_task_worker)
        {
            active_msg.worker_task = valid_task_worker;
            valid_task_worker.msgData = active_msg.msgData;
            valid_task_worker.taskCount++;
            let scope = this;
            this._loadModelByWorker(valid_task_worker).then((task)=>{
                scope._onDoneWorkerTask(task);
            })
        }


    }


}