import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { OperationInfo } from "@/Apps/LayoutAI/OperationInfos/OperationInfo";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { Vector3, Vector3Like } from "three";
import { AI2DesignManager } from "../../../AI2Design/AI2DesignManager";
import { T_MoveWallOpertaionInfo } from "../../OperationInfos/Operations/T_MoveWallOpertaionInfo";
import { T_TransformElement } from "./T_TransformElement";
import { I_SelectedTarget } from "@/Apps/LayoutAI/Layout/TEntitySelector/TEntitySelector";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { AI_PolyTargetType } from "@/Apps/LayoutAI/Layout/IRoomInterface";




export class T_ScaleDWElement extends T_TransformElement {
    _dir_val: number;
    _nor_val: number;

    _fixed_pos: Vector3;

    protected _selected_target: I_SelectedTarget;
    _container: TLayoutEntityContainer;

    _step_len: number = 1;

    private _isMovePoint: boolean = false;
    
    constructor(dir_val: number, nor_val: number, _selected_target: I_SelectedTarget, container: TLayoutEntityContainer, step_len: number = 1) {
        super();
        this._dir_val = dir_val;
        this._nor_val = nor_val;

        this._element_name = "DWScale-(" + this._dir_val + "," + this._nor_val + ")";

        this._fixed_pos = null;
        this._origin_shape_rect = null;
        this._selected_target = _selected_target;

        this._container = container;
        this._step_len = step_len;
        this._allow_entity_types = ["Door","Window"];
        this._isMovingVisible = true;
        this._radius = 15;
    }

    checkEditRect(pos: Vector3Like, _p_sc: number) {
        if (this.isTargetRectTypeValid()) {
            let t = this._element_rect.rect_center.distanceTo(pos) < this.radius / _p_sc;
            this._isMovePoint = t;
            return t;
        } else {
            return false;
        }
    }

    updateElement(): void {
        if (!this._target_rect) return;
        this.pos = this._target_rect.unproject({ x: this._target_rect._w / 2 * this._dir_val, y: this._target_rect._h / 2 * this._nor_val });

        let dv = this.pos.clone().sub(this._target_rect.rect_center);
        dv.normalize();

        if (Math.abs(this._target_rect._nor.x) > 0.9 && Math.abs(this._target_rect._nor.y) < 0.1 || Math.abs(this._target_rect._nor.y) > 0.9 && Math.abs(this._target_rect._nor.x) < 0.1) {
            // 偏向于90度垂直鼠标样式判断
            this.vertical(dv);
        }
        else {
            // 有角度的鼠标样式判断
            this.horizontal(dv);
        }
    }

    vertical(dv: Vector3) {
        if (Math.abs(dv.x) > 0.9 && Math.abs(dv.y) < 0.01) {
            this._cursor_state = LayoutAI_CursorState.Acrossmove;
        }
        else if (Math.abs(dv.y) > 0.9 && Math.abs(dv.x) < 0.01) {
            this._cursor_state = LayoutAI_CursorState.verticalmove;
        }
        else {
            if (dv.x > 0) {
                if (dv.y > 0) {
                    this._cursor_state = LayoutAI_CursorState.Rightmove;

                }
                else {
                    this._cursor_state = LayoutAI_CursorState.Leftmove;

                }
            }
            else {
                if (dv.y > 0) {
                    this._cursor_state = LayoutAI_CursorState.Leftmove;

                }
                else {
                    this._cursor_state = LayoutAI_CursorState.Rightmove;

                }
            }

        }
    }
    horizontal(dv: Vector3) {
        const epsilon = 0.00001; // 容差值
        let minX = Math.min(...this._target_rect.vertices.map(v => v.pos.x));
        let maxX = Math.max(...this._target_rect.vertices.map(v => v.pos.x));
        let minY = Math.min(...this._target_rect.vertices.map(v => v.pos.y));
        let maxY = Math.max(...this._target_rect.vertices.map(v => v.pos.y));

        if (Math.abs(this.pos.x - minX) < epsilon || Math.abs(this.pos.x - maxX) < epsilon) {
            this._cursor_state = LayoutAI_CursorState.Acrossmove;
            return;
        }
        else if (Math.abs(this.pos.y - minY) < epsilon || Math.abs(this.pos.y - maxY) < epsilon) {
            this._cursor_state = LayoutAI_CursorState.verticalmove;
            return;
        }
        if (dv.x > 0) {
            if (dv.y > 0) {
                this._cursor_state = LayoutAI_CursorState.Rightmove;

            }
            else {
                this._cursor_state = LayoutAI_CursorState.Leftmove;

            }
        }
        else {
            if (dv.y > 0) {
                this._cursor_state = LayoutAI_CursorState.Leftmove;

            }
            else {
                this._cursor_state = LayoutAI_CursorState.Rightmove;

            }
        }
    }
    onhover(): void {

    }

    onselect(): void {

        LayoutAI_App.RunCommand(LayoutAI_Commands.Transform_Scaling);

    }



    applyTransformByMovement(movement: Vector3, adsorb_rects: ZRect[] = []): void {
        if (!this._origin_shape_rect) return;
        if (!this._target_rect) return;

        this._fixed_pos = this._origin_shape_rect.unproject({ x: this._origin_shape_rect._w / 2 * -this._dir_val, y: this._origin_shape_rect._h / 2 * -this._nor_val });
        let pos = this._origin_shape_rect.unproject({ x: this._origin_shape_rect._w / 2 * this._dir_val, y: this._origin_shape_rect._h / 2 * this._nor_val });

        let dir_dv = this._origin_shape_rect.dv.clone().multiplyScalar(this._dir_val);
        let nor_dv = this._origin_shape_rect.nor.clone().multiplyScalar(this._nor_val);
        let ww_to_add = movement.dot(dir_dv);
        let hh_to_add = movement.dot(nor_dv);

        if ((this._dir_val * this._nor_val) !== 0) {
            // 计算原始的宽高比
            let aspect_ratio = this._origin_shape_rect._w / this._origin_shape_rect._h;

            // 根据原始的宽高比来调整新的宽度和高度
            if (aspect_ratio > 1) {
                hh_to_add = ww_to_add / aspect_ratio;
            } else {
                ww_to_add = hh_to_add * aspect_ratio;
            }
        }
        else {
            ww_to_add = Math.floor(ww_to_add / this._step_len) * this._step_len;
            hh_to_add = Math.floor(hh_to_add / this._step_len) * this._step_len;
        }
        let ww = this._origin_shape_rect.w + ww_to_add;
        let hh = this._origin_shape_rect.h + hh_to_add;

        if (ww < 0) ww = -ww;
        if (hh < 0) hh = -hh;
        ww_to_add = ww - this._origin_shape_rect.w;
        hh_to_add = hh - this._origin_shape_rect.h;
        let t_movement = dir_dv.clone().multiplyScalar(ww_to_add).add(nor_dv.multiplyScalar(hh_to_add));

        let t_pos = pos.add(t_movement);
        let t_center = (t_pos.add(this._fixed_pos)).multiplyScalar(0.5);


        this._target_rect._w = ww;
        this._target_rect._h = hh;
        this._target_rect.rect_center = t_center;


        this.alignment();

        let entity = TBaseEntity.getEntityOfRect(this._target_rect);
        if (entity) {
            entity._scale_fixed_point = this._fixed_pos.clone();
            entity.update();

        }
    }


    alignment(adsorb_dist: number = 50) {
        let alignment_rects = this._container.getCandidateRects(["Wall"]);

        let scale_dir_flag = this._dir_val;
        if (Math.abs(scale_dir_flag) < 0.1) return;


        let t_rect = this._target_rect.clone();
        let r_center = t_rect.rect_center;
        t_rect._h += 100;
        t_rect.rect_center = r_center;
        let t_edge = scale_dir_flag > 0 ? t_rect.rightEdge : t_rect.leftEdge;



        let target_edge: ZEdge = null;
        let min_dist: number = adsorb_dist;
        alignment_rects.forEach((rect) => {
            if (rect === this._target_rect) return;
            // if(rect.is_simple_shape_equal_to(this._target_rect)) return;

            rect.edges.forEach((edge) => {
                if (edge.islayOn(t_edge, Math.abs(min_dist), 0.01)) {
                    let dist = edge.projectEdge2d(t_edge.center).y;
                    if (dist < min_dist) {
                        min_dist = dist;
                        target_edge = edge;
                    }
                }
            });
        });


        if (target_edge) {
            let t_dist = t_edge.projectEdge2d(target_edge.center).y;


            let ww = this._target_rect.w + t_dist;
            let pos = this._target_rect.unproject({ x: t_dist / 2 * scale_dir_flag, y: 0 });

            this._target_rect._w = ww;
            this._target_rect.rect_center = pos;

        }


    }

    updateOperaionInfo(manager: AI2DesignManager): OperationInfo {
        this.stop_rect = null;

        this._Against_x = false;  // x轴是否靠墙
        this._Against_y = false;  // y轴是否靠墙
        if (!this._origin_shape_rect) return null;
        if (!this._target_rect) return null;
        if (this._origin_shape_rect.rect_center.distanceTo(this._target_rect.rect_center) < 1) return;
        // if(this._origin_shape_rect.is_shape_equal_to(this._target_rect)) return null;
        let move_rect_opertion_info = new T_MoveWallOpertaionInfo(manager);

        move_rect_opertion_info.target_rect = this._target_rect;
        move_rect_opertion_info._history_info.previous_rect = this._origin_shape_rect.clone();
        move_rect_opertion_info._history_info.current_rect = this._target_rect.clone();

        this._origin_in_group_shape_rects && (move_rect_opertion_info._history_info.previous_in_group_rects = [...this._origin_in_group_shape_rects]);

        this._target_in_group_entitys && (move_rect_opertion_info._history_info.current_in_group_rects = this._target_in_group_entitys.map((val => val.clone())));

        this._target_in_group_entitys && (move_rect_opertion_info.target_in_group_entitys = [...this._target_in_group_entitys]);
        if (manager.layout_container) {
            manager.layout_container._computeRoomPolysByWall();
        }

        return move_rect_opertion_info;
    }

    drawCanvas(painter: TPainter): void {
        if (!this.visible || this.is_moving && !this._isMovePoint) return;
        let type = TBaseEntity.get_polygon_type(this._target_rect);
        if (type !== AI_PolyTargetType.Door && type !== AI_PolyTargetType.Window) return;

        // 设置填充和描边样式
        painter.fillStyle = "#fff";
        painter.strokeStyle = "#147FFA";
        painter._context.lineWidth = 5;

        // 绘制圆形
        painter._context.beginPath();
        let pos = painter.project2D(this.pos);
        painter._context.arc(pos.x, pos.y, this._radius, 0, Math.PI * 2);
        painter._context.fill();
        painter._context.stroke();
    }

}