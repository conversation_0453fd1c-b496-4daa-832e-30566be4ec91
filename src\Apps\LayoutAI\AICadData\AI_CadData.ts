import { Box3, Vector3, Vector3<PERSON><PERSON> } from "three"
import { ZEdge } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { g_FigureImagePaths } from "../Drawing/FigureImagePaths";
import { AI_PolyTargetType, I_Window, IRoomEntityRealType, IRoomEntityType, KeyEntity } from "../Layout/IRoomInterface";
import { TWall } from "../Layout/TLayoutEntities/TWall";
import { TRoomEntity } from "../Layout/TLayoutEntities/TRoomEntity";
import { TWindowDoorEntity } from "../Layout/TLayoutEntities/TWinDoorEntity";
import { TStructureEntity } from "../Layout/TLayoutEntities/TStructureEntity";
import { TFurnitureEntity } from "../Layout/TLayoutEntities/TFurnitureEntity";
import { TCombinationEntity } from "../Layout/TLayoutEntities/TCombinationEntity";
import { FigureShapeType, TFigureElement } from "../Layout/TFigureElements/TFigureElement";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";
import { GenDateUUid, computeViewCenterOfPoints } from "@layoutai/z_polygon";
import { TGroupTemplate } from "../Layout/TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TGroupTemplateEntity } from "../Layout/TLayoutEntities/TGroupTemplateEntity";
import { TBaseGroupEntity } from "../Layout/TLayoutEntities/TBaseGroupEntity";


export enum AI_FurnitureType {
    bed = "bed",
    cupboard = "cupboard",
    tv_stand = "tv_stand",
    tv = "tv",
    dining_table = "dining_table",
    single_sofa = "single_sofa",
    chaiseLongue = "chaiseLongue",
    curtain = "curtain",
    dining_chair = "dining_chair",
    wardrobe = "wardrobe",
    side_table = "side_table",
    muti_sofa = "muti_sofa",
    desk_chair = "desk_chair",
    gas_cooker = "gas_cooker",
    desk = "desk",
    tea_table = "tea_table",
    vegatable_basin = "vegatable_basin",
    bedside_table = "bedside_table",
    wine_cabinet = "wine_cabinet",
    leisure_chair = "leisure_chair",
    toilet = "toilet",
    refrigerator = "refrigerator",
    book_cabinet = "book_cabinet",
    balcony_cabinet = "balcony_cabinet",
    washing_machine = "washing_machine",
    pipeline = "pipeline",
    cabinet = "cabinet",
    flue = "flue",
    entrance_cabinet = "entrance_cabinet",
    tatami_bed = "tatami_bed",
    shower_room = "shower_room",
    shower_head = "shower_head",
    test_type = "test_type",
    L_sofa = "L_sofa",
    rect_teapoy = "rect_teapoy",
    washing_cabinet = "washing_cabinet",
    sculpture = "sculpture",
    shower_room_s = "shower_room_s",
    stool = "stool",
    chandelier = "chandelier",
    main_light = "main_light",
    dressing_table = "dressing_table",
    floor_lamp = "floor_lamp",
    foot_pedal = "foot_pedal",
    shower_room_j = "shower_room_j",
    shower_room_y = "shower_room_y",
    shower_room_z = "shower_room_z",
    towel_rack = "towel_rack",
    background_wall = "background_wall",
    double_bed = "double_bed",
    meal_side_cabinet = "meal_side_cabinet",
    round_table = "round_table",
}
export interface I_AI_Line {
    id?: number;
    startPoint: number[];
    endPoint: number[];

    startPoint_v3?: Vector3;
    endPoint_v3?: Vector3;
}
export interface I_AI_WallWinDoor {
    id?: number;
    points: number[][];
    points_v3: Vector3[];
    label?: string;
}



export interface I_AI_Furniture {
    id?: number;
    label?: string | AI_FurnitureType;
    points: number[][];

    points_v3?: Vector3[];
}

export interface I_AI_NamePos {
    id?: number;
    pos?: number[];
    uuid?: string;
    pos_v3?: Vector3;
    name?: string;
}
export interface I_AI_CadData {
    line?: I_AI_Line[];
    wall_win_door?: I_AI_WallWinDoor[];
    furniture?: I_AI_Furniture[];
    namePos?: I_AI_NamePos[];
    input?: {
        Background: string,
        ImageAddress: string,
        OriginPicture: string,
        entity_data: string,
        img_bg_size: number,
        index: number,
        physical_size: number,
        uid: string,
    }
}

function make_V3(data: { [key: string]: any }) {
    let t_data: { [key: string]: any } = {};
    for (let key in data) {
        let lower_key = key;
        lower_key = lower_key.toLowerCase();
        if (lower_key.indexOf("pos") >= 0 || lower_key.indexOf("point") >= 0) {
            let t_list = data[key];
            if (t_list instanceof Array) {
                if (t_list.length > 0) {
                    let list_data = t_list[0];
                    if (list_data instanceof Array) {
                        let ans_list: Vector3[] = [];
                        for (let tt_data of t_list) {
                            let x = tt_data[0] || 0;
                            let y = tt_data[1] || 0;
                            let z = tt_data[2] || 0;

                            ans_list.push(new Vector3(x, y, z));
                        }
                        t_data[key + "_v3"] = ans_list;
                    }
                    else { // 说明是向量
                        let x = t_list[0] || 0;
                        let y = t_list[1] || 0;
                        let z = t_list[2] || 0;

                        t_data[key + "_v3"] = new Vector3(x, y, z);

                    }
                }
            }
        }
    }

    for (let key in data) {
        t_data[key] = data[key];
    }
    return t_data;
}
/**
 *  CAD-AI处理后的数据层
 */
export class AI_CadData {


    /**
     *  墙体数据
     */
    _wall_rects?: ZRect[];

    /**
     *  门、窗数据
     */
    _window_rects?: ZRect[];
    /**
     *  烟道
     */
    _strucutre_rects?: ZRect[];



    _room_polys?: ZPolygon[];





    lines?: I_AI_Line[];
    wall_win_doors?: I_AI_WallWinDoor[];
    furnitures?: I_AI_Furniture[];
    namePosList?: I_AI_NamePos[];

    _line_edges: ZEdge[];



    _simple_wall_rects: ZRect[];

    _figure_rects?: ZRect[];

    _temporary_rects?: ZRect[];   //临时存放所有的图元
    _wall_polygons?: ZPolygon[];

    _bbox: Box3;
    _center: Vector3;

    _maxSize?: number   //历史记录保存的最大长度

    _adding_figure_entity?: TFurnitureEntity|TWindowDoorEntity|TStructureEntity;  //新增的图元



    _room_structure_update_timestamp: number;
    static _timestamp: number = 0;


    static _RoomNameDict: { [key: string]: string } = {
        "书房": "书房",
        "客": "客房",
        "餐": "客餐厅",
        "厨": "厨房",
        "休闲阳台": "阳台",
        "洗衣阳台": "阳台",
        "主": "卧室",
        "次": "卧室"
    };

    constructor(ai_cad_data: I_AI_CadData = {}, ezdxf_offset: Vector3Like = { x: 0, y: 0, z: 0 }) {
        this.lines = [];
        this.wall_win_doors = [];
        this.furnitures = [];
        this.namePosList = [];
        this._temporary_rects = [];
        this._adding_figure_entity = null;
        this._bbox = new Box3();
        this._center = new Vector3();
        this._room_polys = [];

        // console.log("ezdxf offset",ezdxf_offset);
        let points: Vector3[] = [];
        if (ai_cad_data.line) {
            for (let l_data of ai_cad_data.line) {
                let t_data = make_V3(l_data) as I_AI_Line;
                points.push(t_data.startPoint_v3.clone());
                points.push(t_data.endPoint_v3.clone());
                this.lines.push(t_data);
            }
        }

        let view_data = computeViewCenterOfPoints(points);

        let view_offset = new Vector3().copy(ezdxf_offset).sub(view_data.center);

        for (let t_data of this.lines) {
            t_data.startPoint_v3.add(view_offset);
            t_data.endPoint_v3.add(view_offset);
        }
        // if(ai_cad_data.furniture)
        // {
        //     for(let l_data of ai_cad_data.furniture)
        //     {
        //         this.furnitures.push(make_V3(l_data) as I_AI_Furniture);
        //     }
        // }
        if (ai_cad_data.wall_win_door) {
            for (let l_data of ai_cad_data.wall_win_door) {
                this.wall_win_doors.push(make_V3(l_data) as I_AI_WallWinDoor);
            }

            for (let data of this.wall_win_doors) {
                for (let p of data.points_v3) {
                    p.add(view_offset);
                }
            }
        }

        if (ai_cad_data.namePos) {
            for (let l_data of ai_cad_data.namePos) {
                this.namePosList.push(make_V3(l_data) as I_AI_WallWinDoor);
            }

            for (let name_pos of this.namePosList) {
                name_pos.pos_v3.add(view_offset);
            }
        }

        this._line_edges = [];
        for (let line of this.lines) {
            this._line_edges.push(new ZEdge({ pos: line.startPoint_v3 }, { pos: line.endPoint_v3 }));
        }

        for (let edge of this._line_edges) {
            edge.computeNormal();
        }

        this._window_rects = [];
        this._wall_rects = [];
        this._strucutre_rects = [];


        for (let data of this.wall_win_doors) {
            let rect = new ZRect(1, 1);
            rect.initByVertices(data.points_v3);
            rect.reParaFromVertices(2);
            rect.computeZNor();
            rect._attached_elements['data'] = data;
            rect.ex_prop['label'] = data.label;

            if (data.label !== "wall") {
                // console.log(data.label);
                this._window_rects.push(rect);
            }

            this._wall_rects.push(rect);

            if (data.label === "wall") {
                AI_CadData.set_polygon_type(rect, AI_PolyTargetType.Wall);
            } else if (data.label === "window") {
                AI_CadData.set_polygon_type(rect, AI_PolyTargetType.Window);
            } else {
                AI_CadData.set_polygon_type(rect, AI_PolyTargetType.Door);
            }
        }


        if (this.wall_win_doors.length > 0) {
            console.log(this.wall_win_doors);
        }


        this._figure_rects = [];

        let label_dict = {};
        // for(let data of this.furnitures)
        // {
        //     let rect = new ZRect(1,1);
        //     rect.initByVertices(data.points_v3);
        //     rect.reParaFromVertices(2);
        //     rect.ex_prop['label'] = data.label;
        //     label_dict[data.label] = {img_path:""};
        //     AI_CadData.set_poly_target_type(rect, AI_PolyTargetType.Furniture);
        //     this._figure_rects.push(rect);
        // }

        this._simple_wall_rects = [];

        this.updateLayoutData();
        this.updateSimpleWallRects();
        this._wall_rects = [...this._simple_wall_rects];

        this._room_structure_update_timestamp = ++AI_CadData._timestamp;
        // console.log(JSON.stringify(label_dict));
    }


    static refineWalls(t_wall_rects: ZRect[], offset_len: number = -1) {

        t_wall_rects.sort((a, b) => a.h - b.h);
        for (let rect0 of t_wall_rects) {
            if (AI_CadData.is_deleted(rect0)) continue;

            let ll = -rect0.w / 2;
            let rr = rect0.w / 2;

            let left_pos = rect0.unproject({ x: -rect0.w / 2, y: 0 });
            let right_pos = rect0.unproject({ x: rect0.w / 2, y: 0 });

            for (let rect1 of t_wall_rects) {
                if (AI_CadData.is_deleted(rect1)) continue;

                if (rect1 === rect0) continue;

                // 如果两个矩形垂直
                if (Math.abs(rect0.nor.dot(rect1.nor)) < 0.1) {

                    let pp = rect0.project(rect1.rect_center);
                    if (pp.x < 0) {
                        let left_p1 = rect1.project(left_pos);
                        if (Math.abs(left_p1.x) < rect1.w / 2 + 10 && Math.abs(left_p1.y) < rect1.h) {
                            ll = Math.min(ll, pp.x);
                        }
                    }
                    else {
                        let right_p1 = rect1.project(right_pos);
                        if (Math.abs(right_p1.x) < rect1.w / 2 + 10 && Math.abs(right_p1.y) < rect1.h) {
                            rr = Math.max(rr, pp.x);
                        }
                    }
                }
                else if (Math.abs(rect0.nor.dot(rect1.nor)) > 0.9 && rect0.h <= rect1.h + 0.0001) {
                    let pp = rect0.project(rect1.rect_center);
                    if (pp.x < 0) {
                        let left_p1 = rect1.project(left_pos);
                        if (Math.abs(left_p1.x) < rect1.w / 2 + 10 && Math.abs(left_p1.y) < rect1.h / 2) {
                            ll = Math.min(ll, pp.x + rect1._w / 2 - rect1.h / 2)
                        }
                    }
                    else {
                        let right_p1 = rect1.project(right_pos);
                        if (Math.abs(right_p1.x) < rect1.w / 2 + 10 && Math.abs(right_p1.y) < rect1.h / 2) {
                            rr = Math.max(rr, pp.x - rect1._w / 2 + rect1.h / 2);
                        }
                    }
                }


            }

            if (rr > rect0.w / 2) {
                if (offset_len > 0) {
                    rr = rect0.w / 2 + offset_len;
                }
            }

            if (ll < -rect0.w / 2) {
                if (offset_len > 0) {
                    ll = -rect0.w / 2 - offset_len;
                }
            }
            let t_center = rect0.unproject({ x: (ll + rr) / 2, y: 0 });
            rect0._w = rr - ll;
            rect0.rect_center = t_center;
        }


        for (let n_wall of t_wall_rects) {
            if (AI_CadData.is_deleted(n_wall)) continue;
            for (let l_wall of t_wall_rects) {
                if (AI_CadData.is_deleted(l_wall)) continue;
                if (n_wall == l_wall) continue;
                if (n_wall.containsPoly(l_wall, 0.5)) {
                    AI_CadData.set_deleted(l_wall, true);
                }
            }
        }
        let ans_rects: ZRect[] = [];
        for (let rect of t_wall_rects) {
            if (AI_CadData.is_deleted(rect)) continue;
            ans_rects.push(rect);
        }
        return ans_rects;
    }


    updateFiguresProps() {

        for (let rect of this._figure_rects) {
            // 计算贴墙
            let r_edge: ZEdge = null;
            for (let edge of rect.edges) {
                for (let poly of this._room_polys) {
                    for (let t_edge of poly.edges) {
                        if (t_edge.islayOn(edge, 30, 0.5)) {
                            if (!r_edge || edge.length > r_edge.length) {
                                r_edge = edge;
                            }
                        }
                    }
                }
            }

            if (r_edge) // 此边贴墙
            {
                let back_center = r_edge.center;
                let w = r_edge.length;
                let h = r_edge.next_edge.length;
                let nor = r_edge.next_edge.dv.clone();
                let u_dv = r_edge.dv.clone();

                rect._nor.copy(nor);
                rect._w = w;
                rect._h = h;
                rect.u_dv = u_dv;
                rect._back_center.copy(back_center);
                rect.updateRect();

            }

        }
        // console.log(cupboard_rects);
    }

    computeBBox() {
        this._bbox.makeEmpty();
        // console.log(this.lines);
        for (let line of this.lines) {
            this._bbox.expandByPoint(line.startPoint_v3);
            this._bbox.expandByPoint(line.endPoint_v3);
        }

        this._center = this._bbox.getCenter(new Vector3());
    }

    get center() {
        if (this._bbox.isEmpty()) {
            this.computeBBox();
        }
        return this._center;
    }


    updateEntities() {
        let polys = [...this._wall_rects, ...this._window_rects, ...this._strucutre_rects, ...this._room_polys];
        for (let poly of polys) {
            AI_CadData.getOrMakeEntityOfCadRect(poly);
        }

        for (let poly of this._room_polys) {
            let entity = AI_CadData.getOrMakeEntityOfCadRect(poly) as TRoomEntity;
            entity.name = (AI_CadData.get_room_poly_name(poly));
            entity._uuid = (AI_CadData.get_room_poly_uuid(poly));
        }
    }
    updateLayoutData() {
        try {
            /**
             *  先记录当前的NamePostList
             */
            this.updateNamePosListByRooms();

            this.updateRoomPolys();
            this.updateEntities();


            this.updateRoomWindowDoorAndStructures();

            this.predictUntitledRoomNames();


            // 最后再更新一次NamePostList
            this.updateNamePosListByRooms();


            // 更新当前的房型结构
            this._room_structure_update_timestamp = ++AI_CadData._timestamp;


        } catch (error) {
            console.log(error);
        }



    }

    /**
     *  预测未命名房间的名字
     */
    predictUntitledRoomNames() {
        let entities: TRoomEntity[] = [];
        for (let poly of this._room_polys) {
            if (poly.orientation_z_nor.z > 0) continue;
            entities.push(AI_CadData.getOrMakeEntityOfCadRect(poly) as TRoomEntity);
        }

        entities.sort((a, b) => b._area - a._area);

        for (let entity of entities) {
            entity.predictRoomName();
        }


    }
    updateRoomWindowDoorAndStructures() {
        if (!this._room_polys || this._room_polys.length == 0) return;
        let entities: TRoomEntity[] = [];
        for (let poly of this._room_polys) {
            if (poly.orientation_z_nor.z > 0) continue;
            let entity: TRoomEntity = AI_CadData.getOrMakeEntityOfCadRect(poly) as TRoomEntity;

            if (entity) {
                entities.push(entity);

            }
            entity.update();
        }


        for (let win of this._window_rects) {
            AI_CadData.clean_room_entity_of_win_rect(win);
        }

        for (let entity of entities) {
            entity._num_of_all_rooms = entities.length;
            entity.initWindowRects();
            entity.initStructureRects();
            if (this._window_rects) {
                for (let win of this._window_rects) {
                    entity.addAndBindWinRect(win);
                }
            }


            let structure_rects = [...this._strucutre_rects];
            for (let structure_rect of structure_rects) {
                entity.addAndBindStructureRect(structure_rect);
            }
        }

        for (let entity of entities) {
            entity.updateRoomNeighbors();

        }





    }

    protected updateRoomPolys() {

        this._room_polys = [];
        // 尝试合并墙体, 需要各种充分测试
        let expand_len = 10;
        let tmp_polys: ZPolygon[] = [];
        for (let rect of this._wall_rects) {
            rect.computeZNor();
        }

        let wall_rects = [...this._wall_rects, ...this._window_rects];
        for (let rect of this._strucutre_rects) {
            let entity = AI_CadData.getOrMakeEntityOfCadRect(rect);
            if (entity.realType === "Pillar" || entity.realType === "Flue") {
                wall_rects.push(rect);
            }
        }
        for (let rect of wall_rects) {
            if (AI_CadData.is_deleted(rect)) {
                continue;
            }
            let t_rect = rect.clone();
            let rect_center = t_rect.rect_center;

            t_rect._w += expand_len;
            t_rect._h += expand_len;
            t_rect.rect_center = rect_center;
            let poly = new ZPolygon();
            poly.initByVertices(t_rect.positions);
            poly.computeZNor();

            if (poly.orientation_z_nor.z < 0) {
                poly.inverse();
            }

            tmp_polys.push(poly);
        }

        if (tmp_polys.length > 0) {
            // console.log(tmp_polys,this._wall_rects);
            this._room_polys = tmp_polys[0].union_polygons(tmp_polys);


            for (let poly of this._room_polys) {
                if (poly.orientation_z_nor.z < 0) {
                    poly.expandPolygon(expand_len / 2);
                }
                else {
                    poly.expandPolygon(-expand_len / 2);
                }
            }

            // console.log(this._wall_layout_polys);
        }






        for (let poly of this._room_polys) {
            AI_CadData.set_polygon_type(poly, AI_PolyTargetType.RoomArea);
        }


        for (let name_pos of this.namePosList) {
            let pos = name_pos.pos_v3;
            let name = name_pos.name;

            let res = ZPolygon.getMinPolyContainsPoint(this._room_polys, pos);
            let target_poly: ZPolygon = res.poly as any as ZPolygon;



            if (target_poly) {
                AI_CadData.set_room_poly_name(target_poly, AI_CadData._RoomNameDict[name] || name);
                AI_CadData.set_room_poly_pos(target_poly, pos.clone());
                AI_CadData.set_room_poly_uuid(target_poly, name_pos.uuid || GenDateUUid());
                AI_CadData.set_room_poly_id(target_poly, name_pos.id || 0);
            }

        }

        this._room_polys.sort((a, b) => (AI_CadData.get_room_poly_id(a) - AI_CadData.get_room_poly_id(b)));
        // console.log(this._room_polys);
        this.updateWindowsInWall();

    }

    updateWindowsInWall() {

        let wall_only_rects: ZRect[] = [];
        for (let rect of this._wall_rects) {
            if (AI_CadData.is_deleted(rect)) continue;
            // if(rect.ex_prop['label']=='wall') continue;

            wall_only_rects.push(rect);

        }
        for (let rect of this._wall_rects) {
            AI_CadData.clean_window_rects_of_wall_rect(rect);
        }
        for (let window_rect of this._window_rects) {
            let rect_center = window_rect.rect_center;
            let res = ZPolygon.getMinPolyContainsPoint(wall_only_rects, rect_center);
            if (res && res.poly) {
                let wall_rect = res.poly as ZRect;
                if (window_rect.ex_prop.label === "BayWindow") {
                }
                AI_CadData.add_window_rects_to_wall_rect(wall_rect, window_rect);

            }

        }
    }

    updateDoorDir() {
        let doors: ZRect[] = [];
        for (let rect of this._window_rects) {
            if (rect.ex_prop['label'] == "door") {
                doors.push(rect);
            }
        }

        let main_poly: ZPolygon = null;
        let t_polys: ZPolygon[] = [];
        for (let poly of this._room_polys) {
            if (poly.orientation_z_nor.z > 0) continue;
            poly.computeBBox();
            poly._attached_elements['main_rect'] = ZRect.computeMainRect(poly);

            t_polys.push(poly);
        }

        t_polys.sort((a, b) => { return (a._attached_elements['main_rect'] as ZRect).min_hh - (b._attached_elements['main_rect'] as ZRect).min_hh; })

        if (t_polys.length == 0) return;
        main_poly = t_polys[t_polys.length - 1];
        // console.log(t_polys,main_poly);

        for (let poly of t_polys) {
            // if(poly == main_poly) continue;
            let target_rects: ZRect[] = [];
            for (let rect of doors) {
                if (rect.ex_prop['is_visited_rect'] == '1') continue;
                let rect_center = rect.rect_center;
                let min_dist = rect.h + 10;

                for (let edge of poly.edges) {
                    if (Math.abs(edge.nor.dot(rect.nor)) < 0.5) continue;

                    let pp = edge.projectEdge2d(rect_center);

                    if (pp.x < 0 || pp.x >= edge.length) continue;

                    let dist = Math.abs(pp.y);

                    if (dist < min_dist) {
                        target_rects.push(rect);
                        rect._attached_elements['target_edge'] = edge;
                        break;
                    }
                }
            }

            for (let target_rect of target_rects) {
                target_rect.ex_prop['is_visited_rect'] = '1';
                let target_edge = target_rect._attached_elements['target_edge'] as ZEdge;
                if (!target_edge) continue;
                let rect_center = target_rect.rect_center;
                target_rect.nor = target_edge.nor.clone();
                target_rect.rect_center = rect_center;

                let pp = target_edge.projectEdge2d(target_rect.rect_center);

                if (Math.abs(pp.x - target_edge.length / 2) < Math.min(target_edge.length / 4, 100)) {

                    if (target_edge.next_edge.length < target_edge.prev_edge.length) {
                        target_rect.u_dv = target_edge.dv;
                    }
                    else {
                        target_rect.u_dv = target_edge.dv.clone().negate();

                    }
                }
                else {
                    if (pp.x < target_edge.length / 2) {
                        target_rect.u_dv = target_edge.dv;
                    }
                    else {
                        target_rect.u_dv = target_edge.dv.clone().negate();
                    }
                }
                target_rect.updateRect();

            }
        }
        main_poly.computeBBox();
        // console.log(main_poly._boundingbox.getCenter(new Vector3()));
    }

    updateNamePosListByRooms() {
        if (this._room_polys.length > 0) {
            this.namePosList = [];
            for (let room_poly of this._room_polys) {
                if (!room_poly) continue;
                if (room_poly.orientation_z_nor.z > 0) continue;
                let entity = AI_CadData.getOrMakeEntityOfCadRect(room_poly) as TRoomEntity;
                entity.update();
                if (entity._area < 0.01) continue;
                let rect_center = entity._main_rect.rect_center;
                this.namePosList.push({
                    id: this.namePosList.length,
                    uuid: entity._uuid,
                    name: entity.name,
                    pos: rect_center.toArray(),
                    pos_v3: rect_center
                });
            }
        }

    }

    removeWallRect(rect: ZRect) {


        AI_CadData.set_deleted(rect, true);

        let id = this._wall_rects.indexOf(rect);

        if (id >= 0) {
            this._wall_rects.splice(id, 1);
        }
        // this.updateRoomPolys();
    }

    // 去除图元家具
    removeFigureRect(rect: ZRect, historyInfo: any) {
        let id = historyInfo.id;
        if (id >= 0) {
            this._figure_rects.splice(id, 1);
        }
    }

    public updateWallRect(oldRect: ZRect, newRect: ZRect) {
        // 先找到旧墙在列表中的索引
        // let id;
        // if (oldRect) {
        //     id = this._wall_rects.findIndex((item: ZRect) => item.back_center === oldRect.back_center);
        // }
        // if(newRect)
        // {
        //     if(id >= 0) {
        //         // 如果找到了，替换成新的墙
        //         this._wall_rects[id] = newRect;
        //         this.updateRoomPolys();
        //         console.log('this._wall_rects',this._wall_rects);

        //     }
        // }
    }

    addWallRect(rect: ZRect) {
        AI_CadData.set_deleted(rect, false);
        let id = this._wall_rects.indexOf(rect);
        if (id < 0) {
            this._wall_rects.push(rect);
        }
        // this.updateRoomPolys();
    }


    addFigureRect(rect: ZRect) {
        AI_CadData.set_deleted(rect, false);
        let id = this._figure_rects.indexOf(rect);

        if (id < 0) {
            this._figure_rects.push(rect);
        }
    }

    // 记录当前的
    // recordRectHistory(rect: ZRect, historyInfo: I_MovingHistoryInfo)
    // {
    //     let id = historyInfo.id;
    //     if(id >= 0)
    //     {

    //         if(historyInfo.current_rect.ex_prop.poly_target_type === 'Wall')
    //         {
    //             this._wall_rects[id] = historyInfo.current_rect;
    //         }
    //         if(historyInfo.current_rect.ex_prop.poly_target_type === 'Furniture')
    //         {
    //             this._figure_rects[id] = historyInfo.current_rect;
    //         }

    //     }
    //     // this.updateRoomPolys();
    // }

    // // 恢复
    // popRectHistory(rect : ZRect, historyInfo: I_MovingHistoryInfo)
    // {  
    //     let id = historyInfo.id;
    //     if(id >= 0 && rect)
    //     {
    //         if(historyInfo.previous_rect.ex_prop.poly_target_type === 'Wall')
    //         {
    //             this._wall_rects[id] = rect;
    //         }
    //         if(historyInfo.previous_rect.ex_prop.poly_target_type === 'Furniture')
    //         {
    //             this._figure_rects[id] = rect;
    //         }

    //     }
    //     // this.updateRoomPolys();
    // }

    // 清空图元
    emptyFurniture() {
        this._temporary_rects = this._figure_rects;
        this._figure_rects = [];
    }
    // 新增所有图元
    addAllFurniture() {
        this._figure_rects = this._temporary_rects;
    }
    updateSimpleWallRectsByRoomPolys() {
        if (!this._room_polys || this._room_polys.length == 0) return;

        console.time("updateSimpleWallRectsByRoomPolys");
        this._simple_wall_rects = [];

        let wall_edges: ZEdge[] = [];
        for (let poly of this._room_polys) {
            for (let edge of poly.edges) {
                let t_edge = edge._deep_clone();
                t_edge._nor.copy(edge.dv.clone().cross(new Vector3(0, 0, 1)).normalize().negate());

                wall_edges.push(t_edge);
            }
        }


        wall_edges.sort((a, b) => b.length - a.length);

        let t_rects: ZRect[] = [];
        for (let edge of wall_edges) {
            let m_dist = 1000;
            for (let other_edge of wall_edges) {
                if (edge == other_edge) continue;
                let dist = other_edge.center.clone().sub(edge.center).dot(edge.nor);
                if (dist < 0) continue;


                if (other_edge.islayOn(edge, 1000, 0.1)) {
                    if (dist < m_dist) {
                        m_dist = dist;
                    }
                }
            }
            if (m_dist > 1000 - 0.1) continue;

            let rect = new ZRect(edge.length, m_dist);
            rect._back_center.copy(edge.center);
            rect.nor = edge.nor;
            rect.updateRect();

            // this._simple_wall_rects.push(rect);
            t_rects.push(rect);
        }



        t_rects.sort((a, b) => {
            let df_h = b.min_hh - a.min_hh;
            if (Math.abs(df_h) < 3) {
                return b.max_hh - a.max_hh;
            }
            else {
                return df_h;
            }
        });


        let target_rects: ZRect[] = [];
        for (let rect of t_rects) {
            if (rect.orientation_z_nor.z < 0) {
                rect.u_dv = rect.dv.negate();
                rect.updateRect();
            }
            if (target_rects.length == 0) {

                target_rects.push(rect);
            }
            else {

                let tt_rects: ZRect[] = [];
                for (let rect1 of target_rects) {
                    if (rect.containsPoly(rect1)) {
                        tt_rects.push(rect1);
                    }
                }

                for (let t_rect of tt_rects) {
                    let id = target_rects.indexOf(t_rect);
                    if (id >= 0) {
                        target_rects.splice(id, 1);
                    }
                }

                let ans_polys = rect.clone().substract_polygons(target_rects);
                for (let poly of ans_polys) {
                    let main_rect = ZRect.computeMainRect(poly);
                    if (main_rect.orientation_z_nor.z < 0) {
                        main_rect.u_dv = main_rect.dv.negate();
                        main_rect.updateRect();
                    }
                    target_rects.push(main_rect);
                }

            }
        }

        // this._simple_wall_rects = t_rects;

        target_rects.sort((a, b) => {
            let df_h = b.min_hh - a.min_hh;
            if (Math.abs(df_h) < 1) {
                return b.max_hh - a.max_hh;
            }
            else {
                return df_h;
            }
        });
        let check_rect_connected = (rect0: ZRect, rect1: ZRect) => {
            for (let edge0 of rect0.edges) {
                for (let edge1 of rect1.edges) {
                    if (edge0.nor.dot(edge1.nor) > -0.9) continue;
                    let dist = edge0.center.distanceTo(edge1.center);
                    let ldff = Math.abs(edge0.length - edge1.length);

                    let t_diff = Math.max(dist, ldff);

                    if (t_diff < 3) {
                        return true;
                    }

                }
            }
            return false;
        }

        for (let rect of target_rects) {
            if (rect.ex_prop['is_merged']) continue;

            if (rect.h > rect.w) {
                let tmp = rect.h; rect._h = rect.w; rect._w = tmp;
                let r_center = rect.rect_center;
                rect.nor.copy(rect.dv);

                rect.rect_center = r_center;
                if (rect.orientation_z_nor.z < 0) {
                    rect.u_dv = rect.dv.negate();
                    rect.updateRect();
                }
            }

            let iter_num = Math.min(10, target_rects.length);


            while (iter_num--) {

                let side_edges = [rect.edges[0], rect.edges[2]];
                for (let edge of side_edges) {
                    let t_dist = 1000
                    let target_rect: ZRect = null;
                    for (let other_rect of target_rects) {
                        if (other_rect == rect) continue;
                        if (other_rect.ex_prop['is_merged']) continue;

                        for (let o_edge of other_rect.edges) {
                            if (o_edge.islayOn(edge, rect.h * 2, 0.1)) {
                                let dist = o_edge.center.sub(edge.center).dot(edge.nor);
                                if (dist < t_dist) {
                                    t_dist = dist;
                                    target_rect = other_rect;
                                }
                            }
                        }
                    }

                    if (target_rect && t_dist > 1) {
                        rect._w += t_dist;
                        rect._back_center.add(edge.nor.clone().multiplyScalar(t_dist / 2));
                        rect.updateRect();
                    }
                }


                for (let other_rect of target_rects) {
                    if (other_rect.ex_prop['is_merged']) continue;

                    if (check_rect_connected(rect, other_rect)) {
                        other_rect.ex_prop['is_merged'] = "1";


                        let pll_x = 0;
                        for (let v of other_rect.vertices) {
                            let pp = rect.project(v.pos);

                            if (Math.abs(pp.x) > Math.abs(pll_x)) {
                                pll_x = pp.x;
                            }
                        }
                        let w = rect.w / 2. + Math.abs(pll_x);
                        let df = Math.abs(pll_x) - rect.w / 2;
                        let offset = rect.dv.clone().multiplyScalar(pll_x < 0 ? -df / 2 : df / 2);
                        rect._back_center.add(offset);
                        rect._w = w;
                        rect.updateRect();
                        break;
                    }
                }
            }

        }
        this._simple_wall_rects = [];

        for (let rect of target_rects) {
            if (rect.ex_prop['is_merged']) continue;
            this._simple_wall_rects.push(rect);
        }

        for (let rect of this._simple_wall_rects) {
            AI_CadData.set_polygon_type(rect, AI_PolyTargetType.Wall);
        }

        console.timeEnd("updateSimpleWallRectsByRoomPolys");


        for (let window of this._window_rects) {
            for (let rect of this._simple_wall_rects) {
                if (rect.containsPoint(window.rect_center)) {
                    AI_CadData.add_window_rects_to_wall_rect(rect, window);
                }
            }
        }


    }


    updateSimpleWallRectsByWallPolys(wall_poly: ZPolygon[]) {

    }
    updateSimpleWallRects() {
        this.updateSimpleWallRectsByRoomPolys();
        return;

    }






    static clean_window_rects_of_wall_rect(wall_rect: ZRect) {
        if (wall_rect._attached_elements['window_rects']) {
            delete wall_rect._attached_elements['window_rects'];
        }
    }
    static add_window_rects_to_wall_rect(wall_rect: ZRect, window: ZRect) {
        if (!wall_rect._attached_elements['window_rects']) {
            wall_rect._attached_elements['window_rects'] = [];
        }
        wall_rect._attached_elements['window_rects'].push(window);
    }

    static clean_room_entity_of_win_rect(win: ZRect) {
        const roomarea_entity = TRoomEntity.EntityName;
        if (win && win._attached_elements[roomarea_entity]) {
            win._attached_elements[roomarea_entity] = null;
        }
    }

    static get_room_entities_of_win_rect(win: ZRect) {

        const roomarea_entity = TRoomEntity.EntityName;
        if (win?._attached_elements && win._attached_elements[roomarea_entity]) {
            return win._attached_elements[roomarea_entity] as TRoomEntity[]
        } else {
            return null;
        }
    }
    static add_room_entity_to_win_rect(room: TRoomEntity, win: ZRect) {
        const roomarea_entity = TRoomEntity.EntityName;
        if (!win._attached_elements[roomarea_entity]) {
            win._attached_elements[roomarea_entity] = [];
        }
        if (win._attached_elements[roomarea_entity].indexOf(room) < 0) {
            win._attached_elements[roomarea_entity].push(room);
        }
    }


    static get_window_rects_of_wall_rect(wall_rect: ZRect): ZRect[] {
        return wall_rect._attached_elements['window_rects'] || [];
    }



    static get_room_poly_of_room_rect(room_rect: ZRect) {
        return room_rect._attached_elements["room_poly"] || null;
    }
    static get_room_rect_of_room_poly(room_poly: ZPolygon, need_update: boolean = false) {
        let rect: ZRect = room_poly._attached_elements['room_rect'] || null;
        if (!rect || need_update) {
            rect = ZRect.fromBox3(room_poly.computeBBox());
            if (rect.w < rect.h) {
                rect.swapWidthAndHeight();
            }
            room_poly._attached_elements['room_rect'] = rect;
            rect._attached_elements['room_poly'] = room_poly;


            // 直接共享参数值
            rect.ex_prop = room_poly.ex_prop;
        }
        return rect;
    }



    /**
     * 
     * @param wall_rect  获取cad矩形绑定的实体数据
     * @returns 
     */
    static get_wall_entity_of_wall_rect(wall_rect: ZRect) {
        let twall: TWall = wall_rect._attached_elements[KeyEntity] || null;
        if (!twall) {
            twall = new TWall({ boundary: [] });
            twall._rect = wall_rect;

            wall_rect._attached_elements[KeyEntity] = twall;

        }
        twall.update();
        twall.updateDataByRect();
        return twall;
    }

    static bindEntityOfPolygon(rect: ZPolygon, entity: TBaseEntity) {
        rect._attached_elements[KeyEntity] = entity;
    }


    static get_roomarea_entity_of_room_poly(room_poly: ZPolygon) {
        let roomarea: TRoomEntity = room_poly._attached_elements[KeyEntity] || null;
        if (!roomarea) {
            roomarea = new TRoomEntity(room_poly, AI_CadData.get_room_poly_name(room_poly) || "未命名");
            roomarea.update();
            room_poly._attached_elements[KeyEntity] = roomarea;
        }
        roomarea._rect._attached_elements[KeyEntity] = roomarea;
        return roomarea;
    }
    static get_window_of_win_rect(win_rect: ZRect) {
        let win = win_rect._attached_elements['window'] as I_Window;

        if (!win) {
            let r_center = win_rect.rect_center;
            win = {
                type: "Window",
                realType: "OneWindow",
                posX: r_center.x,
                posY: r_center.y,
                posZ: win_rect.zval,
                rotateZ: win_rect.rotation_z,
                center: r_center,
                rect: win_rect,
                nor: win_rect.nor

            }

            win_rect._attached_elements['window'] = win;
        }
        let label = win_rect.ex_prop.label;
        win.length = win_rect.w;
        win.width = win_rect.h;
        win.center = win_rect.rect_center;
        win.rect = win_rect;
        win.nor = win_rect.nor;

        if (label == "window") {
            win.type = "Window";
            win.realType = "OneWindow";
        } else if (label == "baywindow") {
            win.type = "Window";
            win.realType = "BayWindow";
        }
        else if (label == "door") {
            win.type = "Door";
            win.realType = "SingleDoor";
            win.openDirection = win_rect.u_dv_flag > 0 ? 1 : 0;
        }
        else if (label == "slidedoor") {
            win.type = "Door";
            win.realType = "SlidingDoor";
            win.openDirection = 1;
        }
        else if (label == "doorhole") {
            win.type = "Door";
            win.realType = "DoorHole";
        }
        else if (label == "railing") {
            win.type = "Window";
            win.realType = "Railing";
        }
        else if (label == "passdoor") {
            win.type = "Door";
            win.realType = "PassDoor";
        }
        else if (label == "safetydoor") {
            win.type = "Door";
            win.realType = "SafetyDoor";
        }
        else if (label == "doubledoor") {
            win.type = "Door";
            win.realType = "DoubleDoor";
        }
        return win || null;

    }
    static get_window_entity_of_win_rect(win_rect: ZRect) {
        let win_entity: TWindowDoorEntity = win_rect._attached_elements[KeyEntity] || null;
        if (!win_entity) {
            let win = AI_CadData.get_window_of_win_rect(win_rect);
            win_entity = new TWindowDoorEntity(win);
            win_entity._rect = win_rect;
            win_entity.update();
            win_rect._attached_elements[KeyEntity] = win_entity;

        }

        return win_entity;
    }
    static get_structure_entity_of_rect(rect: ZRect) {
        let entity: TStructureEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            entity = new TStructureEntity({ type: "StructureEntity", realType: rect.ex_prop.label as IRoomEntityRealType || "Pillar" });
            entity._rect = rect;
            if (entity.realType === "Platform") {
                entity.height = 500;

            }
            else if (entity.realType === "Beam") {
                entity.height = 200;
            }
            else {
                entity.height = 2800;
            }
            entity.update();
            rect._attached_elements[KeyEntity] = entity;

        }


        return entity;
    }

    static get_figure_element_of_rect(rect: ZRect) {
        let figure_element: TFigureElement = rect._attached_elements[TFigureElement.EntityName];
        let furnitureObj = g_FigureImagePaths[rect.ex_prop.label];
        rect.cornerDepth = furnitureObj?.corner_depth || null;
        rect.cornerWidth = furnitureObj?.corner_width || null;
        if (!figure_element) {
            let category = rect.ex_prop['label'];
            let model_loc = category;
            if (furnitureObj?.public_category && !furnitureObj?.modelLoc) {
                // model_loc = ModelLocPublicCategoryMap.getModelLocByPublicCategory(furnitureObj.public_category);
            }
            model_loc = furnitureObj?.modelLoc || '';

            figure_element = new TFigureElement({
                category: model_loc,
                modelLoc: furnitureObj?.modelLoc,
                public_category: furnitureObj?.public_category,
                sub_category: furnitureObj?.subCategory,
                params: {
                    length: rect._w,
                    depth: rect._h,
                    height: furnitureObj?.height || 2400,
                    off_land_rule: furnitureObj?.off_land_rule || null,
                    l_ex_width: furnitureObj?.corner_width,
                    l_ex_depth: furnitureObj?.corner_depth
                },
                shape: furnitureObj?.shape as FigureShapeType || null,
                min_z: furnitureObj?.zval || 0,
                max_z: 2400,
                image_path: furnitureObj?.img_path || '',
            });

            // 直接绑定rect
            figure_element.bindRect(rect);
            rect.zval = furnitureObj?.zval || 0;
            if (furnitureObj?.rotation_z) {
                rect.rotation_z = furnitureObj.rotation_z;
            }
        }
        return figure_element;
    }

    static get_furniture_entity_of_rect(rect: ZRect) {
        let entity: TFurnitureEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            if (rect._attached_elements[TGroupTemplate.EntityName]) {
                entity = new TGroupTemplateEntity(rect._attached_elements[TGroupTemplate.EntityName]);
                entity._rect = rect;
                entity.update();

                rect._attached_elements[KeyEntity] = entity;

            }
            else {
                let figure_element = AI_CadData.get_figure_element_of_rect(rect);

                entity = new TFurnitureEntity(figure_element);

                entity._rect = rect;
                entity.update();

                rect._attached_elements[KeyEntity] = entity;
            }

        }
        return entity;
    }

    static get_combination_entity_of_rect(rect: ZRect) {
        let entity: TCombinationEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            let figure_element = AI_CadData.get_figure_element_of_rect(rect);
            entity = new TCombinationEntity(figure_element);
            entity._rect = rect;
            entity.update();
            rect._attached_elements[KeyEntity] = entity;
        }
        return entity;
    }

    static get_BaseGroup_entity_of_rect(rect: ZRect) {
        let entity: TBaseGroupEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            let figure_element = AI_CadData.get_figure_element_of_rect(rect);
            entity = new TBaseGroupEntity(figure_element);
            entity._rect = rect;
            entity._rect._attached_elements[KeyEntity] = entity;
            entity._combination_entitys = rect._attached_elements["layout_combination_entitys"] || [];
            entity._matched_combination_entitys = rect._attached_elements["matched_combination_entitys"] || [];
            entity.update();
            rect._attached_elements[KeyEntity] = entity;
        }
        return entity;
    }


    /**
     * 对外常规使用的, 用这种驼峰命名法
     * @param rect  
     * @returns 
     */
    static getOrMakeEntityOfCadRect(rect: ZPolygon | ZRect) {
        if (!rect) return null;
        if (TBaseEntity.get_polygon_type(rect) == AI_PolyTargetType.Wall) {
            return AI_CadData.get_wall_entity_of_wall_rect(rect as ZRect);
        }
        else if (TBaseEntity.get_polygon_type(rect) === AI_PolyTargetType.RoomArea) {
            return AI_CadData.get_roomarea_entity_of_room_poly(rect);
        }
        else if (TBaseEntity.get_polygon_type(rect) === AI_PolyTargetType.Window) {
            return AI_CadData.get_window_entity_of_win_rect(rect as ZRect);
        }
        else if (TBaseEntity.get_polygon_type(rect) === AI_PolyTargetType.Door) {
            return AI_CadData.get_window_entity_of_win_rect(rect as ZRect);
        }
        else if (TBaseEntity.get_polygon_type(rect) === AI_PolyTargetType.StructureEntity) {
            return AI_CadData.get_structure_entity_of_rect(rect as ZRect);
        }
        else if (TBaseEntity.get_polygon_type(rect) === AI_PolyTargetType.Furniture) {
            return AI_CadData.get_furniture_entity_of_rect(rect as ZRect);
        }
        else if (TBaseEntity.get_polygon_type(rect) === AI_PolyTargetType.Group) {
            return AI_CadData.get_combination_entity_of_rect(rect as ZRect);
        }
        else if (TBaseEntity.get_polygon_type(rect) === AI_PolyTargetType.BaseGroup) {
            return AI_CadData.get_BaseGroup_entity_of_rect(rect as ZRect);
        }
        else {
            let entity: TBaseGroupEntity = rect._attached_elements[KeyEntity] || null;
            if (entity) {
                return entity;
            }
            else {
                // console.log(rect, TBaseEntity.get_polygon_type(rect));
                return null;
            }
        }
    }

    static is_deleted(wall_rect: ZPolygon) {
        return wall_rect.ex_prop['is_deleted'] === '1';
    }

    static set_deleted(wall_rect: ZPolygon, t: boolean) {
        // 如果删除了，就设置is_deleted属性为1
        wall_rect.ex_prop['is_deleted'] = t ? "1" : "0";
        if (!t) {
            delete wall_rect.ex_prop['is_deleted'];
        }
    }

    static is_in_group(wall_rect: ZPolygon) {
        return wall_rect.ex_prop['figure_in_group'] || null;
    }

    static set_in_group(wall_rect: ZPolygon, group_uuid: string) {
        wall_rect.ex_prop['figure_in_group'] = group_uuid;
        if (!group_uuid) delete wall_rect.ex_prop['figure_in_group'];
    }

    static set_history(wall_rect: ZPolygon) {
        wall_rect._attached_elements['is_history'] = wall_rect;
    }


    static set_polygon_type(poly: ZPolygon, type: IRoomEntityType) {
        poly.ex_prop['poly_target_type'] = type;
    }

    static get_label(poly: ZPolygon) {
        return poly.ex_prop['label'] || null;
    }
    static get_polygon_type(poly: ZPolygon): IRoomEntityType {
        if (!poly) return null;
        return (poly.ex_prop['poly_target_type'] || null) as IRoomEntityType;
    }

    static get_room_poly_furnitures(poly: ZPolygon): ZRect[] {
        const furnitures_name = "furnitures";
        if (!poly._attached_elements[furnitures_name]) {
            poly._attached_elements[furnitures_name] = [];
        }

        return poly._attached_elements[furnitures_name];
    }

    static set_room_poly_name(poly: ZPolygon, name: string) {
        poly.ex_prop['roomname'] = name;
    }

    static get_room_poly_name(poly: ZPolygon) {
        return poly.ex_prop['roomname'] || "未命名";
    }

    static set_room_poly_pos(poly: ZPolygon, pos: Vector3Like) {
        poly._attached_elements['roompos'] = pos;
    }
    static set_room_poly_uuid(poly: ZPolygon, uuid: string) {
        poly.ex_prop['room_poly_uuid'] = '' + uuid;
    }
    static get_room_poly_uuid(poly: ZPolygon) {
        return poly.ex_prop['room_poly_uuid'];
    }
    static set_room_poly_id(poly: ZPolygon, id: number) {
        poly.ex_prop['room_poly_id'] = '' + id;
    }
    static get_room_poly_id(poly: ZPolygon) {
        return ~~(poly.ex_prop['room_poly_id'] || '-1');
    }
    static get_room_poly_pos(poly: ZPolygon): Vector3Like {
        return poly._attached_elements['roompos'] || null;
    }
    static get_poly_target_props(poly: ZPolygon) {
        let params: { [key: string]: {} } = {};
    }


}