import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { Button, Modal, Radio } from '@svg/antd';
import { useEffect, useState } from 'react';
import useStyles from './style/index'; // 导入样式
import { LayoutAI_Configs } from '@/Apps/LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs';


const RenderPerformanceModeDialog: React.FC<{onCancel:()=>void}> = ({onCancel}) => {
    const t = LayoutAI_App.t;
    const { styles } = useStyles();
    const dict:{[key:string]:string} = {};
    dict["Default"] = t("均衡模式");
    dict["SingleRoom"] = t("性能模式");

    let modes = LayoutAI_Configs.GetRenderPerformanceModes();
    const [currentMode,setCurrentMode] = useState<string>(LayoutAI_Configs.Configs.renderPerformanceMode);

    const handleCancel = ()=>{
        onCancel()
    }

    const handleOk = ()=>{
        onCancel()
    }

    const onChangeMode = (val:string)=>{
        if(modes.includes(val as any))
        {
            LayoutAI_Configs.SetRenderPerformanceMode(val as any);
            setCurrentMode(val);
        }
    }

    useEffect(()=>{
        setCurrentMode(LayoutAI_Configs.Configs.renderPerformanceMode);
    },[])
    return <Modal
        title={t("模型切换设置")}
        open={true}
        destroyOnClose
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel} >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleOk}
            className={styles.customSubmitButton}
          >
            确定
          </Button>
        ]}>
            <Radio.Group value={currentMode} onChange={(ev)=>{
                onChangeMode(ev.target.value);
            }}>
                {modes.map((value,index)=><Radio key={"rpd_radio"+index} value={value}>
                    {dict[value]||value}
                </Radio>)}

            </Radio.Group>
            
    </Modal>
}

export default RenderPerformanceModeDialog;