import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { useStore } from "@/models";
import HouseLeftPanels from "./components/HouseLeftPanel/HouseLeftPanel";
import HousePropertyPanel from "./components/HousePropertyPanel/HousePropertyPanel";
import useStyles from './PadhousePanel.style';
import { AI2DesignBasicModes } from "@/Apps/AI2Design/AI2DesignManager";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import SideToolbar from "@/components/Statusbars/sideToolbar";
import PadStatusbar from "../StatusBar/padStatusbar";
import { LayoutPopEvents } from "@/pages/LightMobile/layoutPopup/layoutPopup";
import { If,Then } from "react-if";
import { EventName } from "@/Apps/EventSystem";

/**
 * Pad端户型编辑器主界面
 */
const PadHousePanel: React.FC = () => {
    const objectId =  "PadHousePanel";
    const store = useStore();
    const [isLeftPanelCollapse, setIsLeftPanelCollapse] = useState<boolean>(false);
    const { styles } = useStyles();

    const [leftPanelType, setLeftPanelType] = useState<"attribute" | "menu">("menu");

    const handleEnterLayout = () => {
        LayoutAI_App.instance._current_handler_mode = AI2DesignBasicModes.AiCadMode;
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
        store.homeStore.setDesignMode(AI2DesignBasicModes.AiCadMode);
    }

    useEffect(() => {
        LayoutAI_App.on_M(LayoutPopEvents.showPopup, objectId, (popupType) => {
            if(popupType === "attribute")
            {
                setLeftPanelType("attribute");
            }else
            {
                setLeftPanelType("menu");
            }
        });

        LayoutAI_App.on_M(EventName.SelectingTarget, objectId, () => {
            setLeftPanelType("menu");
        });

        return () => {
            LayoutAI_App.off_M(LayoutPopEvents.showPopup, objectId);
            LayoutAI_App.off_M(EventName.SelectingTarget, objectId);
        }
    }, []);

    return (
        <div className={styles.padHousePanelRoot}>
            <If condition={leftPanelType === "menu"}>
                <Then>
                    <HouseLeftPanels 
                        isCollapse={isLeftPanelCollapse}
                        onCollapseChange={setIsLeftPanelCollapse}
                    />
                </Then>

            </If>
            <If condition={leftPanelType === "attribute"}>
                <Then>
                    <HousePropertyPanel 
                        isCollapse={isLeftPanelCollapse}
                        onCollapseChange={setIsLeftPanelCollapse}
                    />
                </Then>

            </If>
            <div className={styles.sideToolbarContainer}>
                <SideToolbar ></SideToolbar>
                <PadStatusbar></PadStatusbar>
            </div>
            <div className={styles.topBar}>
                户型编辑模式
            </div>
            <div className={styles.bottomButtons}>
                <div className="btn" onClick={handleEnterLayout}>进入布局</div>
            </div>
        </div>
    );
};

export default observer(PadHousePanel); 