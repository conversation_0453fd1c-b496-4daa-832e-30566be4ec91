import { AutoLayoutService, ILayoutStyle, RoomTypeConfig } from "@layoutai/layout_service";
import { LayoutRoom } from "./LayoutRoom";
import { TFigureList } from "../TFigureElements/TFigureList";
import { TGroupTemplate } from "../TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TRoomLayoutScheme } from "../TLayoutScheme/TRoomLayoutScheme";
import { TBaseRoomToolUtil } from "../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TRoom } from "../TRoom";
import { TRoomNameType } from "../IRoomInterface";

/**
 * @description 自动布局适配器,桥接新旧布局规则
 * <AUTHOR>
 * @date 2025-08-04
 * @lastEditTime 2025-08-04 15:53:02
 * @lastEditors xuld
 */
export class AutoLayoutAdapter {
    public static applyRoom(room: TRoom) {
        console.log("is_new_bed_room_layout_rule");

        let result_scheme_list: TRoomLayoutScheme[] = [];
        let layoutRoom = new LayoutRoom(room);
        let layoutAreas = AutoLayoutService.instance.getAreaStyles(layoutRoom) || [];
        for (let i = 0; i < layoutAreas.length; i++) {
            const layoutStyle: ILayoutStyle = layoutAreas[i];
            let layout_scheme = this._createBedRoomLayoutScheme(i, layoutStyle, room);
            result_scheme_list.push(layout_scheme);
        }
        return result_scheme_list;
    }

    private static _createBedRoomLayoutScheme(
        i: number,
        layoutStyle: ILayoutStyle,
        room: TRoom
    ): TRoomLayoutScheme {
        const groupTemplates: TGroupTemplate[] = [];
        for (let area of layoutStyle.areas) {
            const groupTemplate = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(
                area.areaType,
                room.roomname,
                area.rect,
                { consider_depth: false }
            );
            if (groupTemplate) {
                groupTemplates.push(groupTemplate);
            } else {
                console.error(`未找到区域类型: ${area.areaType}`);
            }
        }

        if (groupTemplates.length === 0) {
            console.error(
                `未找到组合模板: ${layoutStyle.areas.map(area => area.areaType).join(",")}`
            );
            return;
        }

        const figureElements =
            TBaseRoomToolUtil.instance.getAllFiguresFromGroupTemplates(groupTemplates);
        figureElements.sort((a, b) => b.default_drawing_order - a.default_drawing_order);

        let layoutScheme = new TRoomLayoutScheme();
        layoutScheme.figure_list = new TFigureList({
            target_room_names: room.roomname,
            figure_elements: figureElements as any
        });
        layoutScheme._scheme_name = `${room.roomname}布局-${layoutStyle.layoutName}-${i + 1}`;
        layoutScheme.room = room;
        // 厨房 computeScores 依赖 extra 内容
        layoutScheme.extra = layoutStyle.extra;
        layoutScheme.computeScores();
        return layoutScheme;
    }
}
(globalThis as any).AutoLayoutAdapter = AutoLayoutAdapter;
