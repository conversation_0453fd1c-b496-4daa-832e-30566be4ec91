import { Object3D, Vector3, Vector3Like } from "three";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TLayoutEntityContainer } from "../../../Layout/TLayoutEntities/TLayoutEntityContainter";
import { AlignTypeXY, LightingRuler } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { AutoLightingRulerHandler } from "./AutoLightingRulerFactory";
import { IType2UITypeDict } from "../../../Layout/IRoomInterface";
import { TRoomEntity } from "../../../Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "../../../Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { LightRuleService } from "../../../Scene3D/light/rule/LightRuleService";
import { ZRect } from "@layoutai/z_polygon";

export abstract class BaseLightHandler implements AutoLightingRulerHandler {
    abstract handle(ruler: LightingRuler): Promise<TFillLightEntity[]>;

    protected getLayoutContainer(): TLayoutEntityContainer {
        return (LayoutAI_App.instance as TAppManagerBase).layout_container as TLayoutEntityContainer;
    }

    /**
    * @description 分区条件是否满足, 如果满足则返回true, 否则返回false
    * @param ruler 灯光规则
    * @param subRoomAreas 分区列表,一个满足即可
    * @return boolean
    */
    protected checkCondition(ruler: LightingRuler, roomEntity: TRoomEntity): boolean {
        // 房间ID条件
        let subRoomAreas: TSubSpaceAreaEntity[] = roomEntity._sub_room_areas;
        let roomId = ruler?.condition?.roomId;
        if (roomId) {
            let roomIds = (roomId as string).trim().split('|');
            return roomIds.includes(roomEntity._room.uuid);
        }

        // 分区条件
        let spaceArea = ruler?.condition?.spaceArea;
        if (spaceArea) {
            // 多个区域用|分隔
            let spaceAreaNames = spaceArea.trim().split('|');
            // 分区列表
            let areaNames = subRoomAreas.map(area => IType2UITypeDict[area.space_area_type]);
            // 判断是否满足条件
            return spaceAreaNames.some(name => areaNames.includes(name));
        }

        // 房间名称条件
        let roomName = ruler?.condition?.roomName;
        if (roomName) {
            let roomNames = (roomName as string).trim().split('|');
            return roomNames.includes(roomEntity._room.name);
        }

        return true;
    }

    protected getSize(value: number | string, defaultSize: number) {
        if (typeof value === 'string') {
            let percent = parseFloat(value.substring(0, value.length - 1));
            return Math.max(Math.round(defaultSize * percent / 100), 1);
        } else if (typeof value === 'number') {
            return value;
        }
        return defaultSize;
    }

    protected getLightPos(rect: ZRect, ruler: LightingRuler): { x: number, y: number, z: number } {
        let center = rect.rect_center_3d;
        let nor = rect.nor;
        let pos = { x: center.x, y: center.y, z: center.z };

        if (!ruler.pose) {
            return pos;
        }

        // 绝对位置
        if (ruler.pose.x) {
            pos.x = ruler.pose.x;
        }
        if (ruler.pose.y) {
            pos.y = ruler.pose.y;
        }
        if (ruler.pose.z) {
            pos.z = ruler.pose.z;
        }

        // x y 偏移
        if (ruler.pose.norOffset) {
            let vec = new Vector3(pos.x, pos.y, 0);
            vec.add(nor.clone().multiplyScalar(ruler.pose.norOffset));
            pos.x = vec.x;
            pos.y = vec.y;
        }
        else if (ruler.pose.gapOffset) {
            // let width = this.getSize(ruler.lighting.width, rect.h);
            // let wDiff = (rect.w + width) * 0.5 + ruler.pose.gapOffset;
            // let norOffset = ruler.pose.gapOffset + wDiff;
            // let vec = new Vector3(pos.x, pos.y, 0);
            // vec.add(nor.clone().multiplyScalar(norOffset * 0.5));
            let norOffset = rect.h * 0.5 + ruler.pose.gapOffset;
            let vec = new Vector3(pos.x, pos.y, 0);
            vec.add(nor.clone().multiplyScalar(norOffset));
            pos.x = vec.x;
            pos.y = vec.y;
        }

        // z 偏移
        if (ruler.pose.floorOffset) {
            pos.z = ruler.pose.floorOffset;
            if (ruler.pose.lookAt) {
                let width = this.getSize(ruler.lighting.width, rect.h);
                let z = width * 0.5;
                pos.z = pos.z + z;
            }
        }

        // 相对位置
        if (ruler.pose.align) {
            let align = ruler.pose.align;
            switch (align) {
                case AlignTypeXY.Center:
                    // 默认的方式，不用修改
                    break;
                // case AlignTypeXY.Left:
                //     break;
                // case AlignTypeXY.Right:
                //     break;
                case AlignTypeXY.Top:
                case AlignTypeXY.Bottom:
                    let vec = new Vector3(pos.x, pos.y, 0);
                    let width = this.getSize(ruler.lighting.width, rect.h);
                    let dir = align === AlignTypeXY.Top ? -0.5 : 0.5;
                    let norOffset = (rect.h - width) * dir;
                    vec.add(nor.clone().multiplyScalar(norOffset));
                    pos.x = vec.x;
                    pos.y = vec.y;
                    break;
                default:
                    console.error(`未实现对齐方式为${align}的灯光规则`);
                    break;
            }
        }

        return pos;
    }

    protected getLightRotation(rect: ZRect, ruler: LightingRuler, pos: Vector3Like): { x: number, y: number, z: number } {
        let rotation = {
            x: 0,
            y: 0,
            z: rect.rotation_z,
        };
        if (!ruler.pose) {
            return rotation;
        }

        if (ruler.pose.rotateX) {
            rotation.x = Math.PI * ruler.pose.rotateX / 180;
        }
        if (ruler.pose.rotateY) {
            rotation.y = Math.PI * ruler.pose.rotateY / 180;
        }
        if (ruler.pose.rotateZ) {
            rotation.z = Math.PI * ruler.pose.rotateZ / 180;
        }

        if (ruler.pose.lookAt) {
            let lookAt = ruler.pose.lookAt;
            switch (lookAt) {
                case AlignTypeXY.Center:
                    // 根据 lookAt 计算旋转角度
                    let target = new Vector3(rect.rect_center.x, rect.rect_center.y, pos.z);
                    let obj3D = new Object3D();
                    obj3D.position.copy(target);
                    obj3D.lookAt(new Vector3(pos.x, pos.y, pos.z));
                    rotation.x = obj3D.rotation.x;
                    rotation.y = obj3D.rotation.y;
                    rotation.z = obj3D.rotation.z + rect.rotation_z;
                    break;
                default:
                    console.error(`未实现对齐方式为${lookAt}的灯光规则`);
            }
        }

        return rotation;
    }

    protected _createFillLightEntity(intensity: number, color: number, pos: Vector3Like,
        width: number, length: number,
        rotationX: number,
        rotationY: number,
        rotationZ: number,
        name: string,
    ): TFillLightEntity {
        let light = {
            "type": "LightFillPlate",
            "lightBrightness": intensity,
            "color": color.toString(),
            "posX": pos.x,
            "posY": pos.y,
            "posZ": pos.z,
            "rotateX": rotationX,
            "rotateY": rotationY,
            "rotateZ": rotationZ,
            "length": length,
            "width": width,
        }
        let entity = LightRuleService.createLightEntityByData(light);
        entity.name = name;
        entity.update3D();
        (LayoutAI_App.instance as TAppManagerBase).layout_container.addFillLightEntity(entity);
        return entity;
    }
}
