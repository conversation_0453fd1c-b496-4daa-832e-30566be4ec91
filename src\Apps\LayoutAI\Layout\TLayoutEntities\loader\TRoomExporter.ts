import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { I_SwjFurnitureData, I_SwjRoomFloorData, SwjPropskeyDict } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { LayoutContainerUtils } from "../utils/LayoutContainerUtils";
import { TRoomShape } from "../../TRoomShape";
import { ZPolygon } from "@layoutai/z_polygon";
import { I_Entity3D, I_Window } from "../../IRoomInterface";
import { Math_Round, Vec3toMeta, Vec3toMetaRounded } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { TFigureElement } from "../../TFigureElements/TFigureElement";


export class TRoomExporter
{
    static exportRoomFloor(roomUid: string, boundary: ZPolygon): I_SwjRoomFloorData {
        let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let lastUid = LayoutContainerUtils.getMaximalEntityUid(layout_container);
        let floorUid = lastUid + 1;
        let roomUidNumber = Number(roomUid);

        if (isNaN(roomUidNumber)) {
            roomUidNumber = lastUid + 2;
        }

        let floor = {
            uid: floorUid,
            roomUid: roomUidNumber,
            anchorPos: TRoomShape.getAnchorPointInBoundary(boundary)
        };
        return floor;
    }

    static exportEntity3Ddata(window: I_Entity3D, use_swj_props: boolean = false) {
        if (!window.center) {
            if (window.rect) {
                window.center = window.rect.rect_center;
            }
            else {
                window.center = new Vector3(window.posX || 0, window.posY || 0, window.posZ || 0);
                if (window._nor_data) {
                    window.nor = new Vector3().copy(window._nor_data);
                }

            }

        }
        let res: I_Entity3D = {
            id: window.id,
            type: window.type,
            realType: window.realType,
            width: Math_Round(window.width),
            length: Math_Round(window.length),
            _nor_data: Vec3toMeta(window.nor),
            _center_data: Vec3toMetaRounded(window.center),
            posX: Math_Round(window.center.x),
            posY: Math_Round(window.center.y),
            mirror: window.mirror,
            rotateZ: window.rotateZ,
        }
        if (use_swj_props) {
            if (window.rect) {
                res.rotateZ = window.rect.rotation_z;
                res.mirror = window.rect.u_dv_flag > 0 ? 0 : 1;
            }


            for (let key in SwjPropskeyDict) {
                if (res[key] !== undefined) {
                    res[SwjPropskeyDict[key]] = res[key];

                    delete res[key];
                }
            }

        }
        if ((window as I_Window).room_names) {
            res.room_names = window.room_names;
        }

        return res;
    }

    static exportEntity3DList(windows: I_Entity3D[], use_swj_props: boolean = false) {
        if (!windows) return [];
        let res: I_Entity3D[] = [];
        for (let window of windows) {
            res.push(TRoomExporter.exportEntity3Ddata(window, use_swj_props));
        }
        return res;

    }

    static exportNoEntityFurnitureList(furnitureElementList: TFigureElement[]) : I_SwjFurnitureData[] {
        let swjFurnitureDataList: I_SwjFurnitureData[]  = [];
        for (let element of furnitureElementList) {
            if (element.furnitureEntity) continue;
            let swjFurnitureData: I_SwjFurnitureData = {};
            let hasMatchedMaterial = element?._matched_material?.targetPosition && element?._matched_material?.targetSize && element?._matched_material?.targetRotation;
            if(hasMatchedMaterial){
                swjFurnitureData.pos_x = element._matched_material.targetPosition.x;
                swjFurnitureData.pos_y = element._matched_material.targetPosition.y;
                swjFurnitureData.pos_z = element._matched_material.targetPosition.z;
                
                swjFurnitureData.height = element._matched_material.targetSize.height;
                swjFurnitureData.width = element._matched_material.targetSize.width;
                swjFurnitureData.length = element._matched_material.targetSize.length;

                swjFurnitureData.rotate_x = element._matched_material.targetRotation.x;
                swjFurnitureData.rotate_y = element._matched_material.targetRotation.y;
                swjFurnitureData.rotate_z = element._matched_material.targetRotation.z;

            }else if(element?.rect){
                swjFurnitureData.height = element.height;
                swjFurnitureData.width = element.width;
                swjFurnitureData.length = element.length;

                swjFurnitureData.pos_x = element.rect.rect_center.x;
                swjFurnitureData.pos_y = element.rect.rect_center.y;
                swjFurnitureData.pos_z = element.rect.zval;

                swjFurnitureData.rotate_z = element.rect.rotation_z;    

            }
            swjFurnitureData._figure_element = element.exportJson();
            swjFurnitureDataList.push(swjFurnitureData);
        }
        return swjFurnitureDataList;
    }

}