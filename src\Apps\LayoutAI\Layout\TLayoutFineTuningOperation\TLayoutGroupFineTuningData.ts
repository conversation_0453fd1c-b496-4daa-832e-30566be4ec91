import { Vector3 } from "three";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TBaseRoomToolUtil } from "../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TLayoutFineTuningOperationToolUtil } from "./TLayoutFineTuningOperationToolUtil";

export interface IGroupFigureInfo {
    figure: TFigureElement;
    parentFigure?: TFigureElement;
    subCategory?: string;
    isMain?: boolean;
    isSub?: boolean;
    isAdornment?: boolean;
    adornmentFigures?: TFigureElement[];
}

// 通用数据类，零散图元组装成有结构的数据
export class TLayoutGroupFineTuningData
{
    public static mainCategory: string[] = ["沙发", "餐桌", "床"];

    public static subMainCategory: string[] = ["茶几", "边几", "脚踏", "休闲椅","餐椅", "床头柜", "床尾凳"];

    // 单品扩展，后续继续待补充
    public static singleSameLevelCategory: string[] = ["沙发", "餐桌", "床", "茶几", "边几", "脚踏", "休闲椅","餐椅", "床头柜", "衣柜", "背景墙", "梳妆台", "书桌", "玄关柜", "餐边柜", "电视", "落地灯", "床尾凳"];

    // 在微调之后可以进行删除的单品图元
    public static canDeleteNoMainCategory: string[] = ["床尾凳"];

    private _groupFigureInfo: any;

    private _singleFigureInfos: IGroupFigureInfo[];

    private readonly _likeCaprpetMaxHeight: number = 40;

    // 目前是假设传入的图元基本上都是同一个组合的数据
    constructor(figures: TFigureElement[], isGroup: boolean = true)
    {
        this._groupFigureInfo = null;
        if(isGroup)
        {
            this.buildGroupHierarchy(figures);
        }
        else
        {
            // 这个只需要处理某些特殊情况，比如床头柜上有饰品，则床头柜的饰品会随着床头柜同步进行移动，地毯会随着床沙发等主家具一起移动
            this.buildSingleToGroupHierarchy(figures);
        }
    }

    private buildGroupHierarchy(figures: TFigureElement[]) {
        // 初始化
        let allGroupInfos: Map<TFigureElement, IGroupFigureInfo> = new Map<TFigureElement, IGroupFigureInfo>();
        figures.forEach(figure => {
            if(figure.isMaterialMarkAsInvisible())
            {
                return;
            }
            const node: IGroupFigureInfo = {
                figure: figure,

                adornmentFigures: [],
                parentFigure: null,
                subCategory: figure.sub_category,
                isMain: false,

                isSub: false,
                isAdornment: false
            };
            allGroupInfos.set(figure, node);
        });



        // 2. 先确定图元内部的关系, 分主次关系，主次关系确定后，再确定饰品关系，
        // 并且一个组合内部可能存在多个同类别的图元
        let mainFigures: TFigureElement[] = [];
        let subMainFigures: TFigureElement[] = [];
        let adornmentFigures: TFigureElement[] = [];
        let noIdentigyFigures: TFigureElement[] = [];
        for(let figure of figures)
        {
            if(figure.isMaterialMarkAsInvisible())
            {
                continue;
            }
            if(!figure.sub_category.length)
            {
                noIdentigyFigures.push(figure);
                continue;
            }
            if(isContainFigureSubCategory(figure, TLayoutGroupFineTuningData.mainCategory))
            {
                mainFigures.push(figure);
            }
            else if(isContainFigureSubCategory(figure, TLayoutGroupFineTuningData.subMainCategory))
            {
                subMainFigures.push(figure);
            }
            else
            {
                adornmentFigures.push(figure);
            }
        }
        // 对无法进行识别的图元进行归类
        let maxVolumeFigure: TFigureElement = null;
        let maxVolume: number = Number.NEGATIVE_INFINITY;
        for(let figure of noIdentigyFigures)
        {
            let area: number = figure?.matched_rect?.area || figure?.rect?.area;
            let tempVolume: number = area * figure.height;
            if(tempVolume > maxVolume)
            {
                maxVolume = tempVolume;
                maxVolumeFigure = figure;
            }
        }
        for(let figure of noIdentigyFigures)
        {
            if((figure?.matched_rect?.zval || figure?.rect?.zval) > 100)
            {
                adornmentFigures.push(figure);
            }
            else if(figure.height < 100)
            {
                adornmentFigures.push(figure);
            }
            else if(mainFigures.length > 0)
            {
                subMainFigures.push(figure);
            }
            else
            {
                if(figure == maxVolumeFigure)
                {
                    mainFigures.push(figure);
                }
                else
                {
                    subMainFigures.push(figure);
                }
            }
        }
        
        let mainFigure: TFigureElement = null;
        let maxMainFigureArea: number = Number.NEGATIVE_INFINITY;
        for(let figure of mainFigures)
        {
            let area: number = figure?.matched_rect?.area || figure?.rect?.area;
            if(area > maxMainFigureArea)
            {
                maxMainFigureArea = area;
                mainFigure = figure;
            }
        }

        if(mainFigure)
        {
            for(let otherMainFigure of mainFigures)
            {
                if(otherMainFigure == mainFigure)
                {
                    continue;
                }
                subMainFigures.push(otherMainFigure);
            }
        }

        let subMainAsAdornmentFigures: TFigureElement[] = [];
        for(let figure of mainFigure ? [mainFigure, ...subMainFigures] : subMainFigures)
        {
            if(figure == mainFigure)
            {
                let mainFigureNode: IGroupFigureInfo = allGroupInfos.get(mainFigure);
                mainFigureNode.isMain = true;
            }
            else if(TBaseRoomToolUtil.instance.isOverlayByRects(mainFigure.matched_rect ||  mainFigure.rect, figure.matched_rect || figure.rect, true))
            {
                adornmentFigures.push(figure);
                subMainAsAdornmentFigures.push(figure);
            }
            else
            {
                let figureNode: IGroupFigureInfo = allGroupInfos.get(figure);
                figureNode.isSub = true;
            }
        }

        subMainFigures = subMainFigures.filter(figure => !subMainAsAdornmentFigures.includes(figure));

        for(let figure of adornmentFigures)
        {
            let figureNode: IGroupFigureInfo = allGroupInfos.get(figure);
            figureNode.isAdornment = true;
        }

        let tol: number = 50;

        for(let figure of adornmentFigures)
        {
            let node: IGroupFigureInfo = allGroupInfos.get(figure);
            let parentFigure: TFigureElement = mainFigure;
            let adornmentRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(figure.matched_rect || figure.rect);
            if(figure.sub_category != "地毯")
            {
                for(let subFigure of subMainFigures)
                {
                    let subFigureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(subFigure.matched_rect ||  subFigure.rect);
                    if((adornmentRange.xMin - subFigureRange.xMax) <= tol && (subFigureRange.xMin - adornmentRange.xMax) <= tol && (adornmentRange.yMin - subFigureRange.yMax) <= tol && (subFigureRange.yMin - adornmentRange.yMax) <= tol 
                        && (Math.abs((subFigure.matched_rect || subFigure.rect).zval + subFigure.height - (figure.matched_rect || figure.rect).zval) <= tol || (Math.abs((subFigure.matched_rect || subFigure.rect).zval - ((figure.matched_rect || figure.rect).zval + figure.height)) <= tol)))
                    // if(TBaseRoomToolUtil.instance.isContainByFigures(subFigure, figure, false, 2, true))
                    {
                        parentFigure = subFigure;
                        break;
                    }
                }
            }
            node.parentFigure = parentFigure;
            let parentNode: IGroupFigureInfo = allGroupInfos.get(parentFigure);
            if(parentNode)
            {
                parentNode.adornmentFigures.push(figure);   
            }
        }


        let groupSubMainFigureInfos: IGroupFigureInfo[] = [];
        for(let figure of subMainFigures)
        {
            let node: IGroupFigureInfo = allGroupInfos.get(figure);
            groupSubMainFigureInfos.push(node);
        }


        let groupMainFigureInfo: IGroupFigureInfo = allGroupInfos.get(mainFigure);


        this._groupFigureInfo = {mainFigureInfo: groupMainFigureInfo, subMainFigureInfos: groupSubMainFigureInfos};
    }

    public getGroupNodeInfo(): any
    {
        return this._groupFigureInfo;
    }

    public getMainFigure(): TFigureElement
    {
        return this._groupFigureInfo?.mainFigureInfo?.figure;
    }

    public getSubMainFigures(): TFigureElement[]
    {
        let subMainFigures: TFigureElement[] = [];
        this._groupFigureInfo.subMainFigureInfos.forEach((item: any) => subMainFigures.push(item.figure));
        return subMainFigures;
    }
    
    public getAllMainFigures(): TFigureElement[]
    {
        let allMainFigures: TFigureElement[] = [this.getMainFigure()];
        allMainFigures.push(...this.getSubMainFigures());
        return allMainFigures;
    }

    public getAdornmentFigures(figure: TFigureElement): TFigureElement[]
    {
        if(!figure)
        {
            return [];
        }
        let adornmentFigures: TFigureElement[] = [];
        if(this._groupFigureInfo.mainFigureInfo.figure == figure)
        {
            adornmentFigures = this._groupFigureInfo.mainFigureInfo .adornmentFigures;
        }
        else
        {
            this._groupFigureInfo.subMainFigureInfos.forEach((item: any) => {
                if(item.figure == figure)
                {
                    adornmentFigures = item.adornmentFigures;
                }   

            });
        } 
        let decorationElements: TFigureElement[] = figure.decorationElements;
        if(decorationElements)
        {
            adornmentFigures.push(...decorationElements);
        }       
        return Array.from(new Set(adornmentFigures));
    }

    public moveFigure(figure: TFigureElement, moveVec: Vector3, isMoveAdornment: boolean = true)
    {
        TLayoutFineTuningOperationToolUtil.instance.moveRect(figure.matched_rect || figure.rect, moveVec);
        if(isMoveAdornment)
        {
            let adornmentFigures: TFigureElement[] = this.getAdornmentFigures(figure);
            adornmentFigures.forEach(item => {
                TLayoutFineTuningOperationToolUtil.instance.moveRect(item.matched_rect || item.rect, moveVec);
            });
        }
    }

    public moveMainFigure(moveVec: Vector3, isMoveSub: boolean = true)
    {
        let mainFigure: TFigureElement = this.getMainFigure();
        let moveFigures: TFigureElement[] = [];
        moveFigures.push(mainFigure);
        if(isMoveSub)
        {
            let subMainFigures: TFigureElement[] = this.getSubMainFigures();
            moveFigures.push(...subMainFigures);
        }
        moveFigures.forEach(figure => {
            this.moveFigure(figure, moveVec, true);
        });
    }

    public getCanFineTuningAdornmentFigures(): TFigureElement[]
    {
        let canFineTuningAdornmentFigures: TFigureElement[] = [];
        let mainFigure: TFigureElement = this.getMainFigure();
        for(let figure of this._groupFigureInfo.mainFigureInfo.adornmentFigures)
        {
            if((figure.matched_rect || figure.rect).zval + figure.height < this._likeCaprpetMaxHeight)
            {
                continue;
            }
            if(!TBaseRoomToolUtil.instance.isOverlayByRects(mainFigure.matched_rect || mainFigure.rect, figure.matched_rect || figure.rect, true))
            {
                canFineTuningAdornmentFigures.push(figure);
            }
        }
        return canFineTuningAdornmentFigures;
    }

    public getMainAdornmentFigures(): TFigureElement[]
    {
        let mainFigure: TFigureElement = this.getMainFigure();
        let adornmentFigures: TFigureElement[] = this.getAdornmentFigures(mainFigure);
        return adornmentFigures;
    }

    public getCanHideMainAdornmentFigures(): TFigureElement[]
    {
        let canHideMainAdornmentFigures: TFigureElement[] = [];
        let mainFigure: TFigureElement = this.getMainFigure();
        let adornmentFigures: TFigureElement[] = this.getAdornmentFigures(mainFigure);
        adornmentFigures.forEach(figure => {
            if((figure.matched_rect || figure.rect).zval + figure.height < 50 || TBaseRoomToolUtil.instance.isContainByFigures(mainFigure, figure, true))
            {
                return;
            }
            canHideMainAdornmentFigures.push(figure);
        });
        return canHideMainAdornmentFigures;
    }

    public getSpecialMainAdornmentFigure(): TFigureElement[]
    {
        let mainFigure: TFigureElement = this.getMainFigure();
        let adornmentFigures: TFigureElement[] = this.getAdornmentFigures(mainFigure);
        let specialAdornmenFigures: TFigureElement[] = [];
        for(let figure of adornmentFigures)
        {
            if((figure.sub_category.includes("地毯") || ((figure.matched_rect || figure.rect).zval + figure?.height < this._likeCaprpetMaxHeight)) && !figure.sub_category.includes("书"))
            {
                specialAdornmenFigures.push(figure);
            }
        }
        return specialAdornmenFigures;
    }

    public getSpecialMainAdornmentContainFigures(): Map<TFigureElement, TFigureElement[]>
    {
        let specialMainAdornmentContainMainFigures: Map<TFigureElement, TFigureElement[]> = new Map<TFigureElement, TFigureElement[]>();
        let specialMainAdornmentFigures: TFigureElement[] = this.getSpecialMainAdornmentFigure();
        let  allFigures: TFigureElement[] = this.getAllMainFigures();
        let mainAdornmentFigures: TFigureElement[] = this.getMainAdornmentFigures();
        allFigures.push(...mainAdornmentFigures);
        for(let figure of specialMainAdornmentFigures)
        {
            let containFigures: TFigureElement[] = [];
            for(let otherFigure of allFigures)
            {
                if(figure == otherFigure)
                {
                    continue;
                }
                if(TBaseRoomToolUtil.instance.isOverlayByRects(figure.matched_rect || figure.rect, otherFigure.matched_rect || otherFigure.rect, true))
                {
                    containFigures.push(otherFigure);
                }
            }
            specialMainAdornmentContainMainFigures.set(figure, containFigures);
        }
        return specialMainAdornmentContainMainFigures;
    }

    // 专门针对matchrect
    private buildSingleToGroupHierarchy(figures: TFigureElement[])
    {
        let matchedFigures: TFigureElement[] = figures.filter(figure => figure.matched_rect || figure.rect)
        // 构造数据级别
        let singleLevelFigures: TFigureElement[] = [];
        let adornmentFigures: TFigureElement[] = [];
        for(let figure of matchedFigures)
        {
            if(isContainFigureSubCategory(figure, TLayoutGroupFineTuningData.singleSameLevelCategory))
            {
                singleLevelFigures.push(figure);
            }
            else
            {
                adornmentFigures.push(figure);
            }
        }

        let singleToAdornments: TFigureElement[] = specialProcess(singleLevelFigures);
        singleLevelFigures = singleLevelFigures.filter(figure => {
            let isFind: boolean = false;
            for(let tempAdornmentFigure of singleToAdornments)
            {
                if(tempAdornmentFigure == figure)
                {
                    isFind = true;
                    break;
                }
            }
            return !isFind;
        });
        adornmentFigures.push(...singleToAdornments);

        // 整合饰品跟随某个主图元进行移动,  找可能跟随性最大解
        let singleWithAdornmentInfo: Map<TFigureElement, TFigureElement[]> = new Map<TFigureElement, TFigureElement[]>();
        let tol: number = 50;
        for(let singleFigure of singleLevelFigures)
        {
            let singleRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(singleFigure.matched_rect || singleFigure.rect);
            singleWithAdornmentInfo.set(singleFigure, []);
            for(let adornmentFigure of adornmentFigures)
            {
                let adornmentRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(adornmentFigure.matched_rect || adornmentFigure.rect);
                if((adornmentRange.xMin - singleRange.xMax) <= tol && (singleRange.xMin - adornmentRange.xMax) <= tol && (adornmentRange.yMin - singleRange.yMax) <= tol && (singleRange.yMin - adornmentRange.yMax) <= tol 
                    && (Math.abs((singleFigure.matched_rect || singleFigure.rect).zval + singleFigure.height - (adornmentFigure.matched_rect || adornmentFigure.rect).zval) <= tol 
                    || (Math.abs((singleFigure.matched_rect || singleFigure.rect).zval - ((adornmentFigure.matched_rect || adornmentFigure.rect).zval + adornmentFigure.height)) <= tol)))
                {
                    if(adornmentFigure.sub_category == "地毯")
                    {
                        if(singleFigure.sub_category == "沙发" || singleFigure.sub_category == "床")
                        {
                            singleWithAdornmentInfo.get(singleFigure).push(adornmentFigure);
                        }
                    }
                    else
                    {
                        singleWithAdornmentInfo.get(singleFigure).push(adornmentFigure);
                    }
                    
                }
            }
        }

        let figureInfos: IGroupFigureInfo[] = [];
        for(let entry of singleWithAdornmentInfo.entries())
        {
            let node: IGroupFigureInfo = {
                figure: entry[0],
                adornmentFigures: entry[1],
                subCategory: entry[0].sub_category,
            }
            figureInfos.push(node);
        }
        this._singleFigureInfos = figureInfos;
    }

    public moveSingleFigure(figure: TFigureElement, moveVec: Vector3)
    {
        TLayoutFineTuningOperationToolUtil.instance.moveRect(figure.matched_rect || figure.rect, moveVec);
        let adornmentFigures: TFigureElement[] = this.getSingleAdornmenFigures(figure);
        if(adornmentFigures)
        {
            adornmentFigures.forEach(item => {
                TLayoutFineTuningOperationToolUtil.instance.moveRect(item.matched_rect || item.rect, moveVec)
            });
        }
    }

    public getSingleMainFigures()
    {
        let figures: TFigureElement[] = [];
        this._singleFigureInfos.forEach(itemInfo => figures.push(itemInfo.figure));
        return figures;
    }

    private getSingleAdornmenFigures(figure: TFigureElement): TFigureElement[]
    {
        let decorationElements: TFigureElement[] = figure.decorationElements;
        if(!decorationElements)
        {
            decorationElements = []
        }
        for(let singleFigureInfo of this._singleFigureInfos)
        {
            if(singleFigureInfo.figure == figure)
            {
                return Array.from(new Set([...singleFigureInfo.adornmentFigures, ...decorationElements]));
            }
        }
        return null;
    }
}

function isContainFigureSubCategory(figure: TFigureElement, subCategory: string[])
{
    return subCategory.some(item => figure.sub_category.includes(item));
}

function specialProcess(singleFigures: TFigureElement[]): TFigureElement[]
{
    let diningTables: TFigureElement[] = singleFigures.filter(figure => figure.sub_category.includes("餐桌"));
    let diningChairs: TFigureElement[] = singleFigures.filter(figure => figure.sub_category.includes("餐椅"));
    let diningFigureInfo: Map<TFigureElement, TFigureElement[]> = new Map<TFigureElement, TFigureElement[]>();
    let tempAdornmentFigures: TFigureElement[] = [];
    for(let diningTable of diningTables)
    {
        diningFigureInfo.set(diningTable, []);
        for(let diningChair of diningChairs)
        {
            if(!TBaseRoomToolUtil.instance.isOverlayByRects(diningTable.matched_rect || diningTable.rect, diningChair.matched_rect || diningChair.rect))
            {
                continue;
            }
            diningFigureInfo.get(diningTable).push(diningChair);
            tempAdornmentFigures.push(diningChair);
        }
    }
    tempAdornmentFigures = Array.from(new Set(tempAdornmentFigures));
    return tempAdornmentFigures;
}