// src/Apps/LayoutAI/Services/ViewCameraRules/SofaViewCameraRule.ts
import { ZRect } from "@layoutai/z_polygon";
import { BaseViewCameraRule } from "./BaseViewCameraRule";
import { TRoomEntity } from "../../TRoomEntity";
import { TViewCameraEntity } from "../TViewCameraEntity";
import { Matrix4, PerspectiveCamera, Vector3, Vector3Like, Vector4 } from "three";
import { TRoomShape } from "../../../TRoomShape";
import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../TBaseGroupEntity";
import { BedRoomViewCameraRule } from "./BedRoomViewCameraRule";
import { ZEdge } from "@layoutai/z_polygon";
export class BathRoomViewCameraRule extends BaseViewCameraRule {
    // 计算卫生间视角
    static generateViewCamera(room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}, subArea?: ZRect): TViewCameraEntity[] {
        let main_rect = room_entity._main_rect;
        let camera = new PerspectiveCamera(75, 3.0 / 4.0, 300, 20000);
        let view_cameras: TViewCameraEntity[] = [];
        let room_poly = room_entity._room_poly.clone();
        let cabinet_rect = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], ["浴室柜"]))[0]?.matched_rect || room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], ["浴室柜"]))[0]?.rect;
        let closetool_rect = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], ["马桶"]))[0]?.matched_rect || room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], ["马桶"]))[0]?.rect;
        TRoomShape.optimizePoly(room_poly);
        const min_hallway_length = 850;
        let wall_edges = main_rect.edges;
        let view_camera_name = '卫生间-侧面';
        wall_edges.forEach((edge) => {
            if (!edge) return;
            let _target: string[] = ['马桶','浴室柜'];
            let _dir: string = '侧面';
            const _local_filter_cabinet_rect = (rect: ZRect) => {
                if(rect.backEdge.nor.dot(edge.nor) < -0.9)
                {
                    return true;
                }
                return false;
            }
            let target_edge = null as ZEdge;
            // 过滤掉一些浴室柜背靠的视角，左右两侧离墙最近的视角
            if((cabinet_rect && _local_filter_cabinet_rect(cabinet_rect)))
            {
                let edges = [cabinet_rect.leftEdge, cabinet_rect.rightEdge];
   
                edges.forEach((edge) => {
                    let int_data = room_poly.getRayIntersection(edge.unprojectEdge2d({ x: edge.length / 2, y: -5 }), edge.nor.clone());
                    if (!int_data || !int_data.point) return;
                    let target_center = edge.center;
                    let dist_to_front_wall = int_data.point.clone().sub(target_center).length();
                    if(dist_to_front_wall < 50)
                    {
                        target_edge = edge;
                    }
                });
                return;
            } else if(closetool_rect && _local_filter_cabinet_rect(closetool_rect))
            {
                return;
            }
    
            if(target_edge && target_edge.nor.dot(edge.nor) < -0.9)
            {
                return;
            }
            let position_rect = closetool_rect || cabinet_rect;
            if(position_rect)
            {
                if(Math.abs(position_rect.nor.dot(edge.nor)) > 0.9)
                {
                    view_camera_name = '卫生间-朝向正面';
                    _dir = '正面';
                }
            }
            let int_data = room_poly.getRayIntersection(edge.unprojectEdge2d({ x: edge.length / 2, y: -5 }), edge.nor.clone().negate());
            if (!int_data || !int_data.point) return;
            let target_center = edge.center;
            let dist_to_front_wall = int_data.point.clone().sub(target_center).length();
            let max_dist = dist_to_front_wall + min_hallway_length;
            let t_dist = max_dist;
            let t_rect = new ZRect(500, 500);
            t_rect.nor = edge.nor;
            t_rect.zval = 1400; // 默认高度还是高一些1400mm更合理
            let dist_step = 200;

            let iter = 20;
            while (iter--) {
                let pos = edge.unprojectEdge2d({ x: edge.length / 2, y: -t_dist });

                t_rect.rect_center = pos;

                TViewCameraEntity.updateCameraByRect(camera, t_rect);

                let mvp_matrix = camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse);


                let s_p0 = TViewCameraEntity.cameraProjectPos(mvp_matrix, edge.v0.pos);

                let xx = Math.abs(s_p0.x);
                let yy = Math.abs(s_p0.y);

                let ml = Math.max(xx, yy);

                if (ml < 0.40 || t_dist > max_dist - 10) {
                    break;
                }

                t_dist += dist_step;
                if (t_dist > max_dist) t_dist = max_dist;
            }
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(t_rect);
            view_camera.name = view_camera_name;
            view_camera._is_focus_mode = (options.no_focus_mode || false) ? false : true;
            view_camera._target = _target;
            view_camera._dir = _dir;
            view_camera._view_center = main_rect.rect_center;
            view_camera._room_entity = room_entity;
            view_camera.ukey = `bathroom_view_camera_${edge._edge_id}`;
            view_cameras.push(view_camera);
            view_camera._main_rect = main_rect;
            view_camera.fov = 75;
            view_camera.near = min_hallway_length + 100;
        })
        return view_cameras;
    }
}