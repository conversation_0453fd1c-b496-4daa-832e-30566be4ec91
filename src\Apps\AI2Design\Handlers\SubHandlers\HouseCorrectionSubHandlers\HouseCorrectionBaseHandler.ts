import { I_MouseEvent } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../../../AI2BaseModeHandler";
import { HouseCorrectionModeHandler } from "../../HouseCorrectionModeHandler";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import createProRequest from "@/utils/request/request";
import { openApiHost } from "@/config/host";
import { EventName } from "@/Apps/EventSystem";
import { t } from "i18next";
import { message } from "@svg/antd";
import { SchemeSourceType } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";

/**
 * 户型纠正基础子处理器
 */
export class HouseCorrectionBaseHandler extends CadBaseSubHandler {
    protected _parent_handler: HouseCorrectionModeHandler;
    protected _hover_rect: ZRect | null = null;
    protected _selected_rect: ZRect | null = null;

    constructor(parent_handler: AI2BaseModeHandler) {
        super(parent_handler);
        this.name = "HouseCorrectionBaseHandler";
        this._parent_handler = parent_handler as HouseCorrectionModeHandler;
    }

    enter(): void {
        super.enter();
        LayoutAI_App.emit_M(EventName.SubHandlerChanged, { is_default: false, name: this.name });
        this._postCorrectionSchemeRequest();
        this.updateCandidateRects();
    }


    leave(): void {
        super.leave();
        this._hover_rect = null;
        this._selected_rect = null;
    }
    onmousedown(ev: I_MouseEvent): void {
        // TODO: 实现鼠标按下的处理逻辑
    }

    onmousemove(ev: I_MouseEvent): void {
        // TODO: 实现鼠标移动的处理逻辑
    }

    onmouseup(ev: I_MouseEvent): void {
        // TODO: 实现鼠标抬起的处理逻辑
    }

    drawCanvas(): void {
        // 绘制悬停和选中的矩形
    }

    runCommand(cmd_name: string): void {
        switch (cmd_name) {
            case LayoutAI_Commands.Undo:
                // TODO: 实现撤销操作
                break;
            case LayoutAI_Commands.Redo:
                // TODO: 实现重做操作
                break;
            default:
                super.runCommand(cmd_name);
                break;
        }
    }


    private _postCorrectionSchemeRequest() {
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "户型纠正中..." });
        let data = this._getCorrectionPostData();
        const houseCorrectionApiRequest = createProRequest();
        houseCorrectionApiRequest.defaults.withCredentials = true;
        houseCorrectionApiRequest.defaults.baseURL = openApiHost;
        houseCorrectionApiRequest.defaults.headers = {
            'Content-Type': 'application/json',
        };
        houseCorrectionApiRequest({
            method: 'post',
            url: `/api/turing/api/houseplan/v1/GetChangeLayout`,
            data: data,
            timeout: 10000,
        }).then(async (response: any) => {
            if (!response) {
                console.error("Fail to get house correction scheme.");
            } else {
                let responseJson = JSON.parse(response);
                if (responseJson?.result) {
                    let result = responseJson.result;
                    if(!!result?.flag) {
                        // 将base64内容解码为字符串
                        let decodedContent = Buffer.from(result.content, 'base64').toString('utf-8');
                        let content = JSON.parse(decodedContent);
                        console.log(content);
                        this.manager.layout_container.fromXmlSchemeData(content,true,SchemeSourceType.LayoutCorrection);
                        this.manager.onLayerVisibilityChanged();
                        this.manager.layout_container.focusCenter();
                        
                        message.success({
                            duration: 2,
                            content: t('户型纠正成功').toString(),
                            style: {
                                marginTop: '4vh',
                            }});
                    } else {
                        message.warning({
                            duration: 2,
                            content: t('户型纠正失败，接口返回了原方案').toString(),
                            style: {
                                marginTop: '4vh',
                            }});
                    }
                } else {
                    console.error("response.result is null");
                    message.error({
                        duration: 2,
                        content: t('户型纠正失败，接口未获取到纠正结果').toString(),
                        style: {
                            marginTop: '4vh',
                        }});
                }
                this.update();                                  
            }
            LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: "" });
        }).catch((e: any) => {
            console.error(e);
            message.error({
                duration: 2,
                content: t('户型纠正失败，接口请求失败').toString(),
                style: {
                    marginTop: '4vh',
                }});
            LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: "" });
        });
    }
    // 获取户型纠正的请求数据
    private _getCorrectionPostData() {
        let schemeData = this.manager.layout_container.toXmlSchemeData();
        let layoutJson = JSON.stringify(schemeData);
        
        // 将 layoutJson 转换为 base64
        const layoutJsonBase64 = Buffer.from(layoutJson).toString('base64');
        
        // 获取当前时间并格式化为年月日时分秒
        const now = new Date();
        const timeStr = now.getFullYear().toString() +
            (now.getMonth() + 1).toString().padStart(2, '0') +
            now.getDate().toString().padStart(2, '0') +"_"+
            now.getHours().toString().padStart(2, '0') +
            now.getMinutes().toString().padStart(2, '0') +
            now.getSeconds().toString().padStart(2, '0');
        
        const traceId = `${this.manager.layout_container._scheme_id}_${timeStr}`;
        
        return {
            traceId: traceId,
            userId: "",
            sourceSysCode: "layoutai",
            layoutJsonBase64: layoutJsonBase64
        };
    }
} 