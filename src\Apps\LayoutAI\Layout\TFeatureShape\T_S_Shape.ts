
import { ZPolygon } from "@layoutai/z_polygon";
import { T_8V_Shape } from "./T_8V_Shape";
import { HallwayWidth, MinHallwayWidth } from "../IRoomInterface";


export class T_S_Shape extends T_8V_Shape
{
    constructor()
    {
        super();
        this.shape_code = "S";
        this._order = 1;

    }
    checkPolyFeature(poly : ZPolygon)
    {
        for(let edge of poly.edges)
        {
            if(edge.length  < 200)
            {
                return false;
            }
        }

        for(let edge of poly.edges)
        {

            if(edge.prev_edge.dv.dot(edge.next_edge.dv)<-0.5)
            {
                if(edge.length < MinHallwayWidth * 1.5) // 过道判断(1)
                {
                    return false;
                }

                let mLen = Math.min(edge.prev_edge.length, edge.next_edge.length);
                if(edge.length < HallwayWidth  && mLen > edge.length * 2.) // 过道判断(2)
                {
                    return false; 
                }

            }

        }


        return true;
    }
}