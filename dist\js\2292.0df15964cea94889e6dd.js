"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[2292],{11192:function(n,e,t){t.d(e,{a:function(){return r}});var i=t(27347),r=function(n){var e=new Date,t=new Date(n),r=e.getTime()-t.getTime(),a=i.nb.t,o=Math.floor(r/1e3),s=Math.floor(o/60),l=Math.floor(s/60),c=Math.floor(l/24),d=Math.floor(c/30);return o>0&&o<60?"".concat(o).concat(a("秒前")):o<=0?a("刚刚"):s<60?"".concat(s).concat(a("分钟前")):l<24?"".concat(l).concat(a("小时前")):c<31?"".concat(c).concat(a("天前")):d<12?"".concat(d).concat(a("个月前")):n.split(" ")[0]}},11940:function(n,e,t){t.r(e),t.d(e,{default:function(){return de}});var i=t(13274),r=t(41594),a=t(15696),o=t(85783),s=t(9003),l=t(27347),c=t(98612),d=t(88934),u=t(23825),p=t(84872),f=t(79874);function h(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function g(){var n=h(["\n      width:100%;\n      height:100vh;\n      display:flex;\n      flex-direction: column;\n      background: #F6F7F9;\n    "]);return g=function(){return n},n}function x(){var n=h(["\n      width:100%;\n      padding: 20px;\n      display:flex;\n      justify-content: space-between;\n\n      @media screen and (max-width: 450px){\n        padding: 8px;\n      }\n      .left{\n        display:flex;\n        align-items: center;\n        span{\n          color: #000000;\n          font-family: PingFang SC;\n          font-weight: 600;\n          font-size: 16px;\n          line-height: 1.5;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n        .back_button{\n          display:flex;\n          align-items: center;\n          height: 30px;\n          width: 74px;\n          border-radius: 8px;\n          background: #FFFFFF;\n          border: 1px solid #00000026;\n          cursor: pointer;\n          span{\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 14px;\n            line-height: 1.57;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n        }\n      }\n      .right {\n        display: flex;\n        align-items: center;\n        justify-content: flex-end;\n                \n        .history_button{\n          display:flex;\n          align-items: center;\n          justify-content: center;\n          height: 30px;\n          width: 98px;\n          // margin-right: 12px;\n          border-radius: 8px;\n          background: #FFFFFF;\n          border: 1px solid #00000026;\n          cursor: pointer;\n          gap: 2px;\n          span{\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 12px;\n            line-height: 1.67;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n          svg {\n            width: 16px;\n            height: 16px;\n          }\n        }\n      }\n    "]);return x=function(){return n},n}function m(){var n=h(["\n      display: flex;\n      width: 100%;\n      height: calc(var(--vh, 1vh) * 100 - 72px);\n      padding: 0 20px 20px 20px;\n      gap: 20px;\n      position: relative;\n    "]);return m=function(){return n},n}function b(){var n=h(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-direction: column;\n      width: 100%;\n      height: calc(var(--vh, 1vh) * 100 - 72px);\n      padding: 20px;\n      gap: 20px;\n      position: relative;\n\n      @media screen and (max-width: 450px){\n        padding: 8px;\n        gap: 8px;\n        height: calc(var(--vh, 1vh) * 100 - 48px);\n      }\n\n      .FilterFieldBtn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 254px;\n        height: 40px;\n        border-radius: 6px;\n        background: #EAEAEB;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        gap: 8px;\n      }\n    "]);return b=function(){return n},n}function v(){var n=h(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 100%;\n\n      @media screen and (max-width: 450px){\n        height: 120px;\n      }\n    "]);return v=function(){return n},n}function y(){var n=h(["\n      padding: 20px;\n      position:absolute;\n      left: 0;\n      top: 0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n      background: #f6f7f9;\n    "]);return y=function(){return n},n}function w(){var n=h(["\n      position:absolute;\n      left: -1500px;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return w=function(){return n},n}function F(){var n=h(["\n      position: absolute;\n      top: 48px;\n      left: 500px; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n      visibility:hidden;\n    "]);return F=function(){return n},n}function _(){var n=h(["\n      position: absolute;\n      left: 0px;\n      top: -100px;\n      background-color: #EAEAEB;\n      width : calc(100% + 100px);\n      height : calc(100% + 200px);\n      overflow: hidden;\n      .canvas {\n        position : absolute;\n        left: 0px;\n        top: 0px;\n        &.canvas_drawing {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n        }\n        &.canvas_moving {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png) 16 16,auto;\n        }\n        &.canvas_leftmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n        }\n        &.canvas_rightmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n        }\n        &.canvas_acrossmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n        }\n        &.canvas_verticalmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n        }\n        &.canvas_text {\n          cursor : text;\n        }\n        &.canvas_pointer {\n          cursor : pointer;\n        }\n        &.canvas_splitWall {\n          cursor : url(./static/icons/split.png) 8 16,auto;\n        }\n      }\n\n      .canvas_btns {\n        width: auto;\n        margin: 0 auto;\n        position: fixed;\n        display: flex;\n        justify-content: center;\n        bottom: 35px;\n        z-index:10;\n        left: 50%;\n        transform: translateX(-50%);\n        .btn {\n          ","\n          border-radius: 6px;\n          border: none;\n\n          font-weight: 600;\n          margin-right: 10px;\n          margin-left: 10px;\n        }\n        .design_btn {\n          background: #e6e6e6;\n          margin-right: 20px;\n        }\n        @media screen and (max-height: 600px){\n          bottom: 50px !important;\n        }\n      }\n    "]);return _=function(){return n},n}var j=(0,f.rU)((function(n){var e=n.css;return{root:e(g()),topMenu:e(x()),main_container:e(m()),IsLandscape_main_container:e(b()),drawer_btn:e(v()),mobile_atlas_container:e(y()),canvas3d:e(w()),content:e(F()),Scene3DDivcanvas_pannel:e(_(),(0,u.fZ)()?"\n            width: 120px;\n            height: 36px;\n            font-size: 14px;\n          ":"\n            width: 200px;\n            height: 48px;\n            font-size: 16px;\n          ")}})),k=t(10371),S=t(76330);function A(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function I(){var n=A(["\n      width: 440px;\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      padding: 20px;\n\n      @media screen and (max-width: 1600px) {\n        width: 302px;\n      }\n    "]);return I=function(){return n},n}function C(){var n=A(["\n      width: 100%;\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      padding: 20px;\n      min-height: 200px;\n    "]);return C=function(){return n},n}function N(){var n=A(["\n      width: 100%;\n      height: calc(100% - 64px);\n      overflow-y: auto;\n      /* 隐藏滚动条 - Webkit浏览器 */\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      \n      /* 隐藏滚动条 - Firefox */\n      scrollbar-width: none;\n      \n      /* 隐藏滚动条 - IE */\n      -ms-overflow-style: none;\n    "]);return N=function(){return n},n}function E(){var n=A(["\n      width: 100%;\n      height: auto;\n\n      .title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n      .Hx_button{\n        display:flex;\n        align-items: center;\n        justify-content: center;\n        height: 28px;\n        width: 92px;\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        cursor: pointer;\n\n        span{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n\n      .btn_group {\n        height: auto;\n        width: 100%;\n        margin-top: 12px;\n        display: flex;\n        flex-wrap: wrap;\n        gap: 10px;\n\n        .room_button {\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: auto;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 20px;\n          letter-spacing: 0px;\n          text-align: center;\n          padding: 0 12px;\n\n          &.selected {\n            background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n            border: 1px solid transparent;\n            background-clip: padding-box;\n            position: relative;\n\n            &::after {\n              content: '';\n              position: absolute;\n              inset: 0;\n              border-radius: 4px;\n              padding: 1px;\n              background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n              mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n              mask-composite: exclude;\n              -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                           linear-gradient(#fff 0 0);\n              -webkit-mask-composite: xor;\n              pointer-events: none;\n            }\n          }\n        }\n      }\n      \n      .HxSearch {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 120px;\n        margin-top: 12px;\n        border-radius: 8px;\n        cursor: pointer;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n\n        svg {\n          width: 54.05px;\n          height: 45.81px;\n          color: #736AFF;\n        }\n\n        .HxSearch_text {\n          display: flex;\n          flex-direction: column;\n          margin-left: 8px;\n          .HxSearch_text_title {\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: 600;\n            font-size: 16px;\n            line-height: 1.5;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n          .HxSearch_text_content {\n            color: #959598;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 14px;\n            line-height: 1.57;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n        }\n      }\n    "]);return E=function(){return n},n}function z(){var n=A(["\n      width: 100%;\n      margin-top: 12px;\n      \n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 300px;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        // @media screen and (max-width: 1600px) {\n        //   height: 197px;\n        // }\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: regular;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .loading_list_container{\n        display: flex;\n        align-items: center;\n        width: 100%;\n        gap: 8px;\n        overflow-x: auto;\n        flex-wrap: nowrap;\n        margin-top: 8px;\n        position: relative;\n          \n        scrollbar-width: none;\n        -ms-overflow-style: none;\n        &::-webkit-scrollbar {\n          display: none;\n        }\n        \n        .list_loading_item{\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 94px;\n          width: 94px;\n          height: 70.5px;\n          flex-shrink: 0;\n          border-radius: 8px;\n          background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n          position: relative;\n          &::before {\n            content: '';\n            position: absolute;\n            inset: 0;\n            border-radius: 8px;\n            padding: 1px;\n            background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n            mask: linear-gradient(#fff 0 0) content-box, \n                  linear-gradient(#fff 0 0);\n            mask-composite: exclude;\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                         linear-gradient(#fff 0 0);\n            -webkit-mask-composite: xor;\n            pointer-events: none;\n          }\n        }\n        .IsLandscape_loading_item {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 94px;\n          width: 127px;\n          height: 95.25px;\n          flex-shrink: 0;\n          border-radius: 8px;\n          background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n          position: relative;\n                      \n          @media screen and (max-width: 450px) {\n            width: 94px;\n            height: 70.5px;\n          }\n\n          &::before {\n            content: '';\n            position: absolute;\n            inset: 0;\n            border-radius: 8px;\n            padding: 1px;\n            background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n            mask: linear-gradient(#fff 0 0) content-box, \n                  linear-gradient(#fff 0 0);\n            mask-composite: exclude;\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                         linear-gradient(#fff 0 0);\n            -webkit-mask-composite: xor;\n            pointer-events: none;\n          }\n        }\n      }\n\n      .main_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 300px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: 1px solid #0000000F;\n        position: relative;\n\n        // @media screen and (max-width: 1600px) {\n        //   height: 197px;\n        // }\n\n        .IsLandscape_main_img{\n          width: auto;\n          height: 100%;\n          border-radius: 4px;\n          border: 1px solid #0000000F;\n        }\n        .main_img{\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          border: 1px solid #0000000F;\n        }\n        .edit_btn{\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 4px;\n          background: #0000007F;\n          width: 64px;\n          height: 28px;\n          color: #FFFFFF;\n          font-family: PingFang SC;\n          font-weight: 600;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          position: absolute;\n          bottom: 12px;\n          cursor: pointer;\n        }\n\n        .page_button {\n          position: absolute;\n          top: 50%;\n          transform: translateY(-50%);\n          width: 14px;\n          height: 26px;\n          color: white;\n          border-radius: 2px;\n          background: #0000007F;\n          border: none;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 2;\n          padding: 0;\n\n          &:hover {\n            background: #0000007F;\n          }\n\n          &.left {\n            left: 12px;\n          }\n\n          &.right {\n            right: 12px;\n          }\n        }\n      }\n\n      .list_wrapper {\n        position: relative;\n        width: 100%;\n        margin-top: 8px;\n\n        .scroll_button {\n          position: absolute;\n          top: 50%;\n          transform: translateY(-50%);\n          width: 14px;\n          height: 26px;\n          border-radius: 2px;\n          background: #0000004C;\n          border: none;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 2;\n          padding: 0;\n          color: #FFFFFF;\n\n          &:hover {\n            background: #00000066;\n          }\n\n          &.left {\n            left: 4px;\n          }\n\n          &.right {\n            right: 4px;\n          }\n        }\n\n        .list_container{\n          display: flex;\n          width: 100%;\n          gap: 8px;\n          overflow-x: auto;\n          flex-wrap: nowrap;\n          position: relative;\n          \n          scrollbar-width: none;\n          -ms-overflow-style: none;\n          &::-webkit-scrollbar {\n            display: none;\n          }\n\n          .list_img {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            min-width: 94px;\n            width: 94px;\n            height: 70.5px;\n            flex-shrink: 0;\n            border-radius: 4px;\n            border: 1px solid #0000000F;\n            position: relative;\n            cursor: pointer;\n\n            &:hover {\n              border: 2px solid #3D9EFF;\n            }\n\n            &.select {\n              border: 2px solid #147FFA;\n            }\n          }\n          .IsLandscape_list_img {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            min-width: 94px;\n            width: 127px;\n            height: 95.25px;\n            flex-shrink: 0;\n            border-radius: 4px;\n            border: 1px solid #0000000F;\n            position: relative;\n            cursor: pointer;\n            \n            @media screen and (max-width: 450px) {\n              width: 94px;\n              height: 70.5px;\n            }\n\n            &.select {\n              border: 2px solid #147FFA;\n            }\n          }\n          .icon-check {\n            position: absolute;\n            top: 0;\n            right: 0;\n            z-index: 1;\n          }\n        }\n      }\n    "]);return z=function(){return n},n}function D(){var n=A(["\n      margin-top: 20px;\n      width: 100%;\n      height: auto;\n      .title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n      .btn_group {\n        height: auto;\n        width: 100%;\n        margin-top: 12px;\n        display: flex;\n        flex-wrap: wrap;\n        gap: 10px;\n\n        button {\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: auto;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 20px;\n          letter-spacing: 0px;\n          text-align: center;\n          padding: 0 12px;\n          &.selected {\n            background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n            border: 1px solid transparent;\n            background-clip: padding-box;\n            position: relative;\n\n            &::after {\n              content: '';\n              position: absolute;\n              inset: 0;\n              border-radius: 4px;\n              padding: 1px;\n              background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n              mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n              mask-composite: exclude;\n              -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                           linear-gradient(#fff 0 0);\n              -webkit-mask-composite: xor;\n              pointer-events: none;\n            }\n          }\n        }\n      }\n    "]);return D=function(){return n},n}function P(){var n=A(["\n      margin-top: 20px;\n      width: 100%;\n      height: auto;\n      .title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n      .btn_group {\n        height: auto;\n        width: 100%;\n        margin-top: 12px;\n        display: flex;\n        flex-wrap: wrap;\n        gap: 10px;\n\n        button {\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: 58px;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 20px;\n          letter-spacing: 0px;\n          text-align: center;\n\n          &.selected {\n            background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n            border: 1px solid transparent;\n            background-clip: padding-box;\n            position: relative;\n\n            &::after {\n              content: '';\n              position: absolute;\n              inset: 0;\n              border-radius: 4px;\n              padding: 1px;\n              background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n              mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n              mask-composite: exclude;\n              -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                           linear-gradient(#fff 0 0);\n              -webkit-mask-composite: xor;\n              pointer-events: none;\n            }\n          }\n        }\n      }\n    "]);return P=function(){return n},n}function O(){var n=A(["\n      margin-top: 20px;\n      width: 100%;\n      height: 44px;\n      max-width: 300px;\n      border-radius: 8px;\n      border: none;\n      background: #EAEAEB;\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: 600;\n      font-size: 14px;\n      line-height: 1.5;\n      letter-spacing: 0px;\n      text-align: center;\n      box-shadow: none;\n      margin-right: 12px;\n    "]);return O=function(){return n},n}function B(){var n=A(["\n      margin-top: 20px;\n      width: 100%;\n      height: 44px;\n      max-width: 300px;\n      border-radius: 8px;\n      border: none;\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #FFFFFF;\n      font-family: PingFang SC;\n      font-weight: 600;\n      font-size: 14px;\n      line-height: 1.57;\n      letter-spacing: 0px;\n      text-align: center;\n      box-shadow: none;\n\n      &:hover {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%) !important;\n        color: #FFFFFF !important;\n      }\n\n      &:disabled {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%) !important;\n        color: #FFFFFF !important;\n        cursor: not-allowed;\n        opacity: 0.5;\n      }\n    "]);return B=function(){return n},n}function L(){var n=A(["\n      position:absolute;\n      width:100%;\n      height:100%;\n    "]);return L=function(){return n},n}function R(){var n=A(["\n      width: 1000px;\n      height: 500px;      \n      canvas {\n        position:absolute;\n        left:0;\n        top:0;\n      }  \n    "]);return R=function(){return n},n}function M(){var n=A(["\n      width: 50%;\n      // height: 700px;\n      height: 100%;\n      position: relative;\n\n      .name_tag {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 58px;\n        height: 24px;\n        position: absolute;\n        top: 10px;\n        left: 10px;\n        border-radius: 4px;\n        background: #0000007F;\n        color: #FFFFFF;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        z-index: 4;\n      }\n\n      .list_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 60px;\n        position: absolute;\n        bottom: 12px;\n        gap: 8px;\n        z-index: 4;\n\n        .list_img {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 94px;\n          width: 94px;\n          height: 70.5px;\n          flex-shrink: 0;\n          border-radius: 4px;\n          border: 1px solid #0000000F;\n          position: relative;\n          cursor: pointer;\n\n          &:hover {\n            border: 2px solid #3D9EFF;\n          }\n\n          &.select {\n            border: 2px solid #147FFA;\n          }\n        }\n      }\n    "]);return M=function(){return n},n}function T(){var n=A(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n      height: calc(100% - 40px);\n      width: 100%;\n    "]);return T=function(){return n},n}function H(){var n=A(["\n      position: relative;\n      width: 44%;\n      height: 100%;  \n      overflow: hidden;\n    "]);return H=function(){return n},n}function U(){var n=A(["\n      position: absolute;\n      left: 0px;\n      top: 0px;\n      background-color: #EAEAEB;\n      width : 100%;\n      height : 100%;\n      overflow: hidden;\n      canvas{\n        position: absolute;\n        left: 0px;\n        top: 0px;\n      }\n    "]);return U=function(){return n},n}function Z(){var n=A(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 16px;\n      height: 32px;\n      width: 100%;\n      position: absolute;\n      bottom: 20px;\n      z-index: 4;\n\n      button{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        cursor: pointer;\n        width: 93px;\n        height: 32px;\n        border-radius: 6px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n    "]);return Z=function(){return n},n}function W(){var n=A(["\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      flex-direction: column;\n      width: 284px;\n      height: 162px;\n      position: absolute;\n      padding: 20px;\n      border-radius: 12px;\n      background: #FFFFFF;\n      box-shadow: 0px 8px 24px 0px #00000028;\n      z-index: 5;\n      bottom: 40px;\n      left: 500px;\n      transform: translateX(-50%);\n      .setting_item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: auto;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n    "]);return W=function(){return n},n}function V(){var n=A(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      width: 100%;\n      height: 40px;\n      .confirm_btn {\n        width: 160px;\n        height: 40px;\n        border-radius: 8px;\n        border: none;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        color: #FFFFFF;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 14px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: center;\n        box-shadow: none;\n\n        &:hover {\n          background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%) !important;\n          color: #FFFFFF !important;\n        }\n      }\n      .cancel_btn {\n        width: 160px;\n        height: 40px;\n        border-radius: 8px;\n        border: none;\n        background: #EAEAEB;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 14px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: center;\n        box-shadow: none;\n        margin-right: 12px;\n\n        &:hover {\n          background: #EAEAEB !important;\n          color: #282828 !important;\n        }\n      }\n    "]);return V=function(){return n},n}function X(){var n=A(["\n      position: relative;\n      width: 100%;\n      margin-top: 8px;\n\n      .scroll_button {\n        position: absolute;\n        top: 50%;\n        transform: translateY(-50%);\n        width: 14px;\n        height: 26px;\n        border-radius: 2px;\n        background: #0000004C;\n        border: none;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 2;\n        padding: 0;\n        color: #FFFFFF;\n\n        &:hover {\n          background: #00000066;\n        }\n\n        &.left {\n          left: 4px;\n        }\n\n        &.right {\n          right: 4px;\n        }\n      }\n\n      .btn_group {\n        display: flex;\n        width: 100%;\n        gap: 10px;\n        overflow-x: auto;\n        flex-wrap: nowrap;\n        position: relative;\n        \n        scrollbar-width: none;\n        -ms-overflow-style: none;\n        &::-webkit-scrollbar {\n          display: none;\n        }\n\n        .room_button {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          flex-shrink: 0;\n          position: relative;\n\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: 58px;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 20px;\n          letter-spacing: 0px;\n          text-align: center;\n          padding: 0;\n\n          &.selected {\n            background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n            border: 1px solid transparent;\n            background-clip: padding-box;\n            position: relative;\n\n            &::after {\n              content: '';\n              position: absolute;\n              inset: 0;\n              border-radius: 4px;\n              padding: 1px;\n              background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n              mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n              mask-composite: exclude;\n              -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                           linear-gradient(#fff 0 0);\n              -webkit-mask-composite: xor;\n              pointer-events: none;\n            }\n          }\n        }\n      }\n    "]);return X=function(){return n},n}var Y=(0,f.rU)((function(n){var e=n.css;return{root:e(I()),IsLandscape_root:e(C()),info_content:e(N()),Housetype_perspective:e(E()),Housetype_Info:e(z()),style_type:e(D()),quantity:e(P()),cancelBtn:e(O()),submitBtn:e(B()),viewerContainer:e(L()),scene3D:e(R()),canvas3d:e(M()),edit_content:e(T()),content:e(H()),canvas_pannel:e(U()),operation_panel:e(Z()),cameraSettingsPopup:e(W()),footer:e(V()),mobile_btn_container:e(X())}})),q=t(36134),G=t(79750),$=t(99030),J=t(78644),K=t(41980),Q=t(5640),nn=t(14181),en=t(33100),tn=t(13880),rn=t(11164),an=t(83813),on=t(17365),sn=t(65640);function ln(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function cn(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function dn(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var a=n.apply(e,t);function o(n){cn(a,i,r,o,s,"next",n)}function s(n){cn(a,i,r,o,s,"throw",n)}o(void 0)}))}}function un(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ln(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ln(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pn(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var fn=(0,a.observer)((function(){var n,e=(0,o.B)().t,t=(0,s.P)(),a=Y().styles,c=un((0,r.useState)(null),2),p=c[0],f=c[1],h=un((0,r.useState)(null),2),g=h[0],x=h[1],m=un((0,r.useState)(0),2),b=m[0],v=m[1],y=un((0,r.useState)(!1),2),w=y[0],F=y[1],_=(0,r.useRef)(null),j=un((0,r.useState)({designStyle:[],rooms:[]}),2),A=j[0],I=j[1],C=(0,r.useRef)([]),N=un((0,r.useState)(window.innerWidth<window.innerHeight),2),E=N[0],z=N[1],D=un((0,r.useState)(!1),2),P=D[0],O=D[1],B=un((0,r.useState)(null),2),L=B[0],R=B[1],M=un((0,r.useState)(null),2),T=M[0],H=M[1],U=un((0,r.useState)(!1),2),Z=U[0],W=U[1],V=[{id:"现代",title:e("现代")},{id:"中式",title:e("中式")},{id:"北欧",title:e("北欧")},{id:"奶油风",title:e("奶油风")},{id:"法式",title:e("法式")},{id:"混搭",title:e("混搭")},{id:"轻奢",title:e("轻奢")},{id:"诧寂",title:e("诧寂")}],X=[{id:1,title:"1"},{id:2,title:"2"}],ln=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("edit_canvas")),l.nb.instance.update()),l.nb.instance&&(l.nb.instance._is_landscape=E),z(window.innerWidth<window.innerHeight)};(0,r.useEffect)((function(){window.addEventListener("resize",ln),ln();var n=function(){var n=dn((function(){var n,e;return pn(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,fetch("https://3vj-render.3vjia.com/config/3d/aidraw.json")];case 1:return[4,t.sent().json()];case 2:return n=t.sent(),I(n),sn.log("获取到配置数据:",A),[3,4];case 3:return e=t.sent(),sn.error("获取配置失败:",e),[3,4];case 4:return[2]}}))}));return function(){return n.apply(this,arguments)}}();n()}),[]);var cn=un((0,r.useState)(V[0].id),2),fn=cn[0],hn=cn[1],gn=un((0,r.useState)(X[0].id),2),xn=gn[0],mn=gn[1];(0,r.useEffect)((function(){var n=l.nb.instance.layout_container;on.f.updateAliasName(l.nb.instance.layout_container),t.homeStore.setRoomInfos(n._rooms);var e=t.homeStore.roomInfos||n._rooms;if(e.length>0){!function(){var n=l.nb.instance.layout_container,e=l.nb.instance.scene3D;l.nb.emit($.r.UpdateScene3D,!1),e.setCemeraMode(k.I5.FirstPerson),sn.time("updateViewCamera"),G.q.updateViewCameraEntities(n,null,{methods:2}),sn.timeEnd("updateViewCamera")}();var i=setTimeout(dn((function(){var n;return pn(this,(function(t){return e.length>0&&(n=C.current[0])&&n.click(),v(b+1),[2]}))})),1e3);return function(){return clearTimeout(i)}}}),[]),(0,r.useEffect)((function(){t.homeStore.setAiDrawLoad(!0)}),[]);var bn=function(n){n.currentTarget.scrollLeft+=n.deltaY,n.preventDefault()},vn=(0,r.useRef)(null),yn=un((0,r.useState)(!1),2),wn=yn[0],Fn=yn[1],_n=un((0,r.useState)(!1),2),jn=_n[0],kn=_n[1],Sn=function(){if(vn.current){Fn(vn.current.scrollLeft>0);var n=vn.current.scrollLeft+vn.current.clientWidth>=vn.current.scrollWidth-1;kn(!n)}},An=function(n){if(vn.current){var e="left"===n?vn.current.scrollLeft-102:vn.current.scrollLeft+102;vn.current.scrollTo({left:e,behavior:"smooth"})}};(0,r.useEffect)((function(){Sn(),p&&sn.log("viewCameras",p._view_cameras)}),[p]);var In=function(){var n=dn((function(){return pn(this,(function(n){return l.nb.DispatchEvent(l.n0.SaveLayoutSchemeAs,{address:void 0,schemename:l.nb.instance.layout_container._layout_scheme_name,telephone:void 0,username:void 0}),[2]}))}));return function(){return n.apply(this,arguments)}}(),Cn=function(){var n=dn((function(){var n,i,r,a,o,s,c,d;return pn(this,(function(f){switch(f.label){case 0:return null!==p&&g?l.nb.instance.layout_container._layout_scheme_id?[3,2]:[4,In()]:(en.A.warning(e("请选择视角")),[2]);case 1:f.sent(),f.label=2;case 2:return t.homeStore.openFilterField&&t.homeStore.setOpenFilterField(!1),r=null===(n=A.designStyle)||void 0===n?void 0:n.find((function(n){return n.text===fn})),a=null===(i=A.rooms)||void 0===i?void 0:i.find((function(n){var e,t;return(null==p||null===(e=p.aliasName)||void 0===e?void 0:e.includes(n.text))||(null===(t=n.text)||void 0===t?void 0:t.includes((null==p?void 0:p.aliasName)||""))})),[4,(0,Q.nA)(g._perspective_img.src)];case 3:return o=f.sent(),s=l.nb.instance.layout_container,c={schemeId:s._layout_scheme_id,diffuseImage:o,imageNum:xn,stylizationImage:"",imageWidth:1920,imageHeight:1080,inspiration:(null==a?void 0:a.inspire)||(null==p?void 0:p.aliasName),stylized:"",rooms:(null==a?void 0:a.value)||(null==p?void 0:p.aliasName),designStyle:(null==r?void 0:r.value)||"",layoutId:s.hxId,layoutName:s._layout_scheme_name,aiModel:0===p._room.furnitureList.length?3:0},[4,nn.w.instance.aiDrawImage(c)];case 4:return(d=f.sent()).success&&(t.homeStore.setCurrentAIDrawImageID(d.result),(0,u.fZ)()||t.homeStore.setRefreshAtlas(!0)),[2]}}))}));return function(){return n.apply(this,arguments)}}(),Nn=function(){sn.log("handleDeleteView")},En=["客餐厅","卧室","厨房","卫生间","阳台","其他"],zn=function(n,e){var t=En.findIndex((function(e){return n._room_entity.aliasName.includes(e)})),i=En.findIndex((function(n){return e._room_entity.aliasName.includes(n)}));return-1!==t&&-1!==i?t-i:-1!==t?-1:-1!==i?1:n._room_entity.aliasName.localeCompare(e._room_entity.aliasName)};return(0,i.jsxs)("div",{className:E?a.IsLandscape_root:a.root,children:[(0,i.jsxs)("div",{className:a.info_content,children:[(0,i.jsxs)("div",{className:a.Housetype_perspective,children:[(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,i.jsx)("span",{className:"title",children:e("选择视角")}),0==t.homeStore.roomInfos.length&&(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"Hx_button",children:(0,i.jsx)("span",{onClick:function(){l.nb.emit(d.U.OpenHouseSearching,!0)},style:{height:20,width:60},children:e("搜索户型图")})})})]}),t.homeStore.roomInfos.length>0&&(0,i.jsxs)("div",{className:a.Housetype_Info,children:[(0,i.jsx)("div",{className:"btn_group",style:{marginBottom:10},children:t.homeStore.roomInfos.slice().sort(zn).map((function(n,r){return(0,i.jsx)("button",{ref:function(n){return C.current[r]=n},className:"room_button ".concat(p===n._room_entity?"selected":""),onClick:dn((function(){var e,i,r,a,o,s,c,d,u;return pn(this,(function(p){switch(p.label){case 0:t.homeStore.setAiDrawLoad(!0),f(n._room_entity),e=l.nb.instance.scene3D,i=!0,r=!1,a=void 0,p.label=1;case 1:p.trys.push([1,7,8,9]),o=n._room_entity._view_cameras[Symbol.iterator](),p.label=2;case 2:return(i=(s=o.next()).done)?[3,6]:!(c=s.value)._perspective_img||c._perspective_img.width<=1?(e.active_controls.bindViewEntity(c),e.update(),d=l.nb.instance.layout_container,[4,c.updatePerspectiveViewImg(d.painter)]):[3,4];case 3:p.sent(),p.label=4;case 4:x(n._room_entity._view_cameras[0]),v(b+1),p.label=5;case 5:return i=!0,[3,2];case 6:return[3,9];case 7:return u=p.sent(),r=!0,a=u,[3,9];case 8:try{i||null==o.return||o.return()}finally{if(r)throw a}return[7];case 9:return t.homeStore.setAiDrawLoad(!1),x(n._room_entity._view_cameras[0]),v(b+1),[2]}}))})),children:e(n._room_entity.aliasName)},n.uid)}))}),t.homeStore.aiDrawLoad&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"main_loading_container",children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"28px"}}),(0,i.jsx)("span",{children:e("AI视角生成中...")})]}),(0,i.jsx)("div",{className:"loading_list_container",children:Array.from({length:4}).map((function(n,e){return(0,i.jsx)("div",{className:E?"IsLandscape_loading_item":"list_loading_item"},e)}))})]}),p&&!t.homeStore.aiDrawLoad&&g&&(0,i.jsxs)("div",{className:"main_container",onMouseEnter:function(){return O(!0)},onMouseLeave:function(){return O(!1)},children:[(0,i.jsx)("img",{className:E?"IsLandscape_main_img":"main_img",src:g._perspective_img.src}),(0,i.jsx)("button",{className:"page_button left",onClick:function(){if(p&&g){var n=p._view_cameras.findIndex((function(n){return n===g}));n>0&&x(p._view_cameras[n-1])}},style:{display:p&&g&&0!==p._view_cameras.findIndex((function(n){return n===g}))&&(P||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangzuo",size:24})}),(0,i.jsx)("button",{className:"page_button right",onClick:function(){if(p&&g){var n=p._view_cameras.findIndex((function(n){return n===g}));n<p._view_cameras.length-1&&x(p._view_cameras[n+1])}},style:{display:p&&g&&p._view_cameras.findIndex((function(n){return n===g}))!==p._view_cameras.length-1&&(P||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangyou",size:24})})]},b),p&&!t.homeStore.aiDrawLoad&&(0,i.jsxs)("div",{className:"list_wrapper",children:[(0,i.jsx)("div",{className:"list_container",ref:vn,onWheel:bn,onScroll:Sn,children:p._view_cameras.map((function(n,e){return(0,i.jsxs)("div",{style:{position:"relative"},children:[(0,i.jsx)("img",{className:"".concat(E?"IsLandscape_list_img":"list_img"," ").concat(g===n?"select":""),src:n._perspective_img.src,onClick:function(){x(n),l.nb.instance.scene3D.active_controls.bindViewEntity(n)}}),g===n&&(0,i.jsx)(S.A,{className:"icon-check",type:"icon-check"})]},e)}))},b),wn&&(0,i.jsx)("button",{className:"scroll_button left",onClick:function(){return An("left")},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangzuo"})}),jn&&(0,i.jsx)("button",{className:"scroll_button right",onClick:function(){return An("right")},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangyou"})})]})]}),t.homeStore.roomInfos.length<=0&&(0,i.jsxs)("div",{className:"HxSearch",onClick:function(){l.nb.emit(d.U.OpenHouseSearching,!0)},children:[(0,i.jsx)(S.A,{type:"icon-huxing_L"}),(0,i.jsxs)("div",{className:"HxSearch_text",children:[(0,i.jsx)("span",{className:"HxSearch_text_title",children:e("搜索户型图")}),(0,i.jsx)("span",{className:"HxSearch_text_content",children:e("从户型库中搜索户型")})]})]})]}),(0,i.jsxs)("div",{className:a.style_type,children:[(0,i.jsx)("span",{className:"title",children:e("风格类型")}),(0,i.jsx)("div",{className:"btn_group",children:V.map((function(n){return(0,i.jsx)("button",{onClick:function(){return e=n.id,void hn(e);var e},className:fn===n.id?"selected":"",children:n.title},n.id)}))})]}),(0,i.jsxs)("div",{className:a.quantity,children:[(0,i.jsx)("span",{className:"title",children:e("数量")}),(0,i.jsx)("div",{className:"btn_group",children:X.map((function(n){return(0,i.jsx)("button",{onClick:function(){return mn(n.id)},className:xn===n.id?"selected":"",children:n.title},n.id)}))})]})]}),(0,i.jsxs)("div",{style:{width:"100%",display:"flex",justifyContent:"center",alignContent:"center"},children:[t.homeStore.currentAIDrawImageID&&E&&(0,i.jsx)(q.A,{className:a.cancelBtn,onClick:function(){return t.homeStore.setOpenFilterField(!1)},children:e("取消")}),(0,i.jsx)(q.A,{className:a.submitBtn,onClick:Cn,disabled:t.homeStore.aiDrawLoad||t.homeStore.roomInfos.length<=0,children:e("提交任务")})]}),(0,i.jsx)(K._w,{center:!0,height:800,width:1500,draggable:!0,title:e("编辑视角"),onClose:function(){F(!1)},style:{zIndex:w?1e3:-1},children:(0,i.jsxs)("div",{style:{padding:"20px",display:"flex",flexDirection:"column",height:"100%"},children:[(0,i.jsxs)("div",{className:a.edit_content,children:[(0,i.jsxs)("div",{className:"3d_container "+a.canvas3d,children:[(0,i.jsx)(J.A,{defaultViewMode:7}),(0,i.jsx)("div",{className:"name_tag",children:null==T?void 0:T.aliasName}),(0,i.jsx)("div",{className:"list_container",ref:vn,onWheel:bn,onScroll:Sn,children:null==T||null===(n=T._view_cameras)||void 0===n?void 0:n.map((function(n,e){return(0,i.jsx)("div",{style:{position:"relative"},children:(0,i.jsx)("img",{className:"list_img ".concat(L===n?"select":""),src:n._perspective_img.src,onClick:function(){R(n),l.nb.instance.scene3D.active_controls.bindViewEntity(n)}})},e)}))},b)]}),(0,i.jsxs)("div",{id:"Canvascontent",className:a.content,children:[(0,i.jsx)("div",{ref:_,id:"body_container",className:a.canvas_pannel,children:(0,i.jsx)("canvas",{id:"edit_canvas",className:"canvas",onMouseEnter:function(){t.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){t.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,i=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+i*i);t.homeStore.setInitialDistance(r/t.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,i=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+i*i)/t.homeStore.initialDistance;r>5?r=5:r<.05&&(r=.05),t.homeStore.setScale(r),l.nb.DispatchEvent(l.n0.scale,r)}},onTouchEnd:function(){t.homeStore.setInitialDistance(null)}})}),(0,i.jsxs)("div",{className:a.operation_panel,children:[(0,i.jsx)("button",{children:e("新增视角")}),(0,i.jsx)(tn.A,{title:e("确认删除当前视角?"),onConfirm:Nn,okText:e("确定"),cancelText:e("取消"),children:(0,i.jsx)("button",{children:e("删除视角")})}),(0,i.jsx)(tn.A,{title:e("确认重置当前视角？"),onConfirm:Nn,okText:e("确定"),cancelText:e("取消"),children:(0,i.jsx)("button",{children:e("重置视角")})}),(0,i.jsx)("button",{onClick:function(){W(!Z)},children:e("相机设置")}),Z&&(0,i.jsxs)("div",{className:a.cameraSettingsPopup,children:[(0,i.jsxs)("div",{className:"setting_item",children:[(0,i.jsx)("span",{style:{marginRight:"12px"},children:e("空间")}),(0,i.jsx)(rn.A,{defaultValue:null==T?void 0:T.aliasName,style:{width:"80%"},onChange:function(n){var e=t.homeStore.roomInfos.find((function(e){return e._room_entity.aliasName===n}));H(e._room_entity)},options:t.homeStore.roomInfos.slice().sort(zn).map((function(n){return{value:n._room_entity.aliasName,label:n._room_entity.aliasName}}))})]}),(0,i.jsxs)("div",{className:"setting_item",children:[(0,i.jsx)("span",{style:{marginRight:"12px"},children:e("镜头")}),(0,i.jsx)(an.A,{style:{width:"75%"},defaultValue:33,marks:{0:e("特写"),33:e("人眼"),66:e("标准"),100:e("广角")}})]}),(0,i.jsxs)("div",{className:"setting_item",children:[(0,i.jsx)("span",{style:{marginRight:"12px"},children:e("裁剪")}),(0,i.jsx)(an.A,{style:{width:"75%"},defaultValue:50})]})]})]})]})]}),(0,i.jsxs)("div",{className:a.footer,children:[(0,i.jsx)(q.A,{className:"confirm_btn",onClick:function(){sn.log("handleEditConfirm")},children:e("确定")}),(0,i.jsx)(q.A,{className:"cancel_btn",onClick:function(){return F(!1)},children:e("取消")})]})]})})]})})),hn=t(17830);function gn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function xn(){var n=gn(["\n      width: ",";\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      border-left: 1px solid #0000000F;\n      padding: 20px;\n      \n      @media screen and (max-width: 1600px) {\n        width: ",";\n      }\n    "]);return xn=function(){return n},n}function mn(){var n=gn(["\n      width: 100%;\n      height: 100%;\n      min-height: 400px;\n      border-radius: 12px;\n      background: #FFFFFF;\n      border-left: 1px solid #0000000F;\n      padding: 20px;\n              \n      @media screen and (max-width: 450px) {\n        padding: 8px 8px 20px 8px;\n      }\n    "]);return mn=function(){return n},n}function bn(){var n=gn(["\n      display: flex;\n      width: 100%;\n      height: 56px;\n    "]);return bn=function(){return n},n}function vn(){var n=gn(["\n      display: flex;\n      flex-direction: column;\n      margin-left: 8px;\n      .title_info_name {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        max-width: 60vw;\n        @media screen and (max-width: 450px) { // 手机宽度\n          font-size: 14px;\n        }\n      }\n      .design_button{\n        display:flex;\n        align-items: center;\n        justify-content: center;\n        height: 28px;\n        width: 92px;\n        margin-left: 12px;\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        cursor: pointer;\n\n        span{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n      .title_info_other{\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        margin-top: 4px;\n        color: #5B5E60;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n\n      }\n    "]);return vn=function(){return n},n}function yn(){var n=gn(["\n      width: 100%;\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      position: relative;\n\n      .page_button {\n        position: absolute;\n        top: calc(50% - 55px);\n        transform: translateY(-50%);\n        width: ",";\n        height: ",";\n        color: white;\n        border-radius: 2px;\n        background: #0000004C;\n        border: none;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 2;\n        padding: 0;\n\n        &:hover {\n          background: #0000007F;\n        }\n\n        &.left {\n          left: 12px;\n        }\n\n        &.right {\n          right: 12px;\n        }\n      }\n      \n      .empty_content {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        flex: 1;\n        min-height: 0;\n        border-radius: 8px;\n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          margin-top: 10px;\n        }\n        svg {\n          width: 100px;\n          height: 100px;\n        }\n      }\n      \n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        flex: 1;\n        min-height: 0;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: regular;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          margin-top: 4px;\n          @media screen and (max-width: 450px) {\n            width: 65%;\n          }\n        }\n      }\n\n      .main_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        flex: 1;\n        min-height: 100px;\n        background: #EAEAEB;\n        position: relative;\n\n        @media screen and (max-width: 450px) {\n          background: #F5F5F5;\n        }\n        .ant-image-mask\n        {\n          display: none !important;\n        }\n        img{\n          max-height: 100%;\n          max-width: 100%;\n        }\n      }\n\n      .bottom_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 8px;\n        margin-top: 20px;\n        width: 100%;\n        overflow: hidden;\n      }\n\n      .diffuse_img_container {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        position: relative;\n        width: 120px;\n        height: 90px;\n        flex-shrink: 0;\n\n        .view_icon {\n          position: absolute;\n          left: 0;\n          top: 0;\n          z-index: 1;\n          width: 46px;\n          height: 26px;\n          border-radius: 4px;\n          background: #0000007F;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n\n          span {\n            color: #FFFFFF;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 12px;\n            line-height: 1.67;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n        }\n\n        .diffuse_img {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          border: 1px solid #FFFFFF;\n        }\n      }\n\n      .list_container{\n        display: flex;\n        align-items: center;\n        margin: 0;\n        gap: 8px;\n        min-width: 0;\n        overflow-x: auto;\n        // padding: 0 20px;\n        position: relative;\n        \n        /* 隐藏滚动条 */\n        &::-webkit-scrollbar {\n          display: none;\n        }\n        -ms-overflow-style: none;\n        scrollbar-width: none;\n\n        .img_item{\n          flex-shrink: 0;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 120px;\n          height: 90px;\n          border-radius: 4px;\n          border: 1px solid #FFFFFF;\n          cursor: pointer;\n\n          &.selected {\n            border-radius: 4px;\n            border: 2px solid #147FFA;\n          }\n        }\n        .loading_item{\n          flex-shrink: 0;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 120px;\n          height: 90px;\n          border-radius: 8px;\n          background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n          position: relative;\n          &::before {\n            content: '';\n            position: absolute;\n            inset: 0;\n            border-radius: 8px;\n            padding: 1px;\n            background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n            mask: linear-gradient(#fff 0 0) content-box, \n                  linear-gradient(#fff 0 0);\n            mask-composite: exclude;\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                        linear-gradient(#fff 0 0);\n            -webkit-mask-composite: xor;\n            pointer-events: none;\n          }\n        }\n      }\n    "]);return yn=function(){return n},n}function wn(){var n=gn(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n      position: absolute;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      z-index: 1;\n\n      .shareBtn {\n        width: 60px;\n        height: 28px;\n        border-radius: 15px;\n        border: none;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        color: #EEEEEE;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        box-shadow: none;\n        &:hover {\n          background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%) !important;\n          color: #EEEEEE !important;\n          box-shadow: none !important;\n        }\n\n        @media screen and (max-width: 320px) {\n          display: none;\n        }\n      }\n      \n      .iconBtn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 28px;\n        height: 28px;\n        border-radius: 2.4px;\n        background: #FFFFFF33;\n        backdrop-filter: blur(4px);\n        box-shadow: ;\n        cursor: pointer;\n        color: #F2F2F2;\n        svg{ \n          width: 16px;\n          height: 16px;\n        }\n      }\n      .mobile_icon_btn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 48px;\n        height: 36px;\n        border-radius: 4px;\n        background: #0000004C;\n        backdrop-filter: blur(4px);\n        box-shadow: ;\n        cursor: pointer;\n        color: #FFFFFF;\n        svg{ \n          width: 16px;\n          height: 16px;\n        }\n        span{\n          color: #FFFFFF;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 10px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n        }\n      }\n    "]);return wn=function(){return n},n}var Fn=(0,f.rU)((function(n){var e=n.css;return{root:e(xn(),(0,u.fZ)()?"calc(100% - 460px)":"calc(100% - 760px)",(0,u.fZ)()?"calc(100% - 322px)":"calc(100% - 582px)"),IsLandscape_root:e(mn()),title:e(bn()),title_info:e(vn()),img_content:e(yn(),(0,u.fZ)()?"20px":"28px",(0,u.fZ)()?"42px":"56px"),operation_container:e(wn())}})),_n=t(46909),jn=t(65640);function kn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Sn(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function An(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var a=n.apply(e,t);function o(n){Sn(a,i,r,o,s,"next",n)}function s(n){Sn(a,i,r,o,s,"throw",n)}o(void 0)}))}}function In(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return kn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return kn(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Cn(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var Nn=(0,a.observer)((function(){var n,e=(0,o.B)().t,t=(0,s.P)(),a=Fn().styles,d=(l.nb.instance.layout_container,In((0,r.useState)(""),2)),p=(d[0],d[1]),f=In((0,r.useState)(""),2),h=(f[0],f[1]),g=In((0,r.useState)(""),2),x=(g[0],g[1]),m=In((0,r.useState)(""),2),b=(m[0],m[1]),v=In((0,r.useState)({designStyle:[],rooms:[]}),2),y=v[0],w=v[1],F=In((0,r.useState)([]),2),_=F[0],j=F[1],k=In((0,r.useState)(null),2),A=k[0],I=k[1],C=In((0,r.useState)(null),2),N=C[0],E=C[1],z=In((0,r.useState)(!1),2),D=z[0],P=z[1],O=(0,r.useRef)(null),B=In((0,r.useState)(window.innerWidth<window.innerHeight),2),L=B[0],R=B[1],M=In((0,r.useState)(0),2),T=M[0],H=M[1],U=In((0,r.useState)(!1),2),Z=U[0],W=U[1];l.nb.UseApp(c.e.AppName),l.nb.instance&&(l.nb.t=e);var V=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("cad_canvas")),l.nb.instance.update()),l.nb.instance&&(l.nb.instance._is_landscape=L),R(window.innerWidth<window.innerHeight)};(0,r.useEffect)((function(){if(j([]),p(l.nb.instance.layout_container._layout_scheme_name),l.nb.instance.layout_container.aidraw_img){var n,e=(null===(n=l.nb.instance.layout_container.aidraw_img)||void 0===n?void 0:n.startsWith("https://"))?l.nb.instance.layout_container.aidraw_img:"https://img3.admin.3vjia.com/".concat(l.nb.instance.layout_container.aidraw_img);h(e),jn.log("imageUrl",e)}}),[l.nb.instance.layout_container._layout_scheme_id]),(0,r.useEffect)((function(){window.addEventListener("resize",V),V();var n=function(){var n=An((function(){var n,e;return Cn(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,fetch("https://3vj-render.3vjia.com/config/3d/aidraw.json")];case 1:return[4,t.sent().json()];case 2:return n=t.sent(),w(n),jn.log("获取到配置数据:",y),[3,4];case 3:return e=t.sent(),jn.error("获取配置失败:",e),[3,4];case 4:return[2]}}))}));return function(){return n.apply(this,arguments)}}();n()}),[]);var X=function(){var e=An((function(e){var i;return Cn(this,(function(r){switch(r.label){case 0:return j([]),I(null),[4,nn.w.instance.queryImageInfo(e)];case 1:return i=r.sent(),jn.log("获取到图片列表:",i),j(i.imageResultList),I(i.imageResultList[0]),1===i.state&&(jn.log("关闭定时器"),clearInterval(n),(0,u.fZ)()||t.homeStore.setRefreshAtlas(!0)),E(i),[2]}}))}));return function(n){return e.apply(this,arguments)}}();(0,r.useEffect)((function(){var n,e,t=null===(n=y.designStyle)||void 0===n?void 0:n.find((function(n){return n.value===(null==N?void 0:N.designStyle)})),i=null===(e=y.rooms)||void 0===e?void 0:e.find((function(n){return n.value===(null==N?void 0:N.rooms)}));x(null==t?void 0:t.text),b(null==i?void 0:i.text)}),[N]),(0,r.useEffect)((function(){if(""!==t.homeStore.currentAIDrawImageID)return X(t.homeStore.currentAIDrawImageID),n=setInterval((function(){X(t.homeStore.currentAIDrawImageID)}),5e3),function(){return clearInterval(n)}}),[t.homeStore.currentAIDrawImageID]);var Y=function(){var n=An((function(){var n,t,i,r;return Cn(this,(function(a){switch(a.label){case 0:if(!A)return[2];a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(A.imageResult)];case 2:return[4,a.sent().blob()];case 3:return n=a.sent(),t=window.URL.createObjectURL(n),(i=document.createElement("a")).href=t,i.download="image_".concat(Date.now(),".png"),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(t),[3,5];case 4:return r=a.sent(),jn.error("下载失败:",r),en.A.error(e("下载失败")),[3,5];case 5:return[2]}}))}));return function(){return n.apply(this,arguments)}}(),q=function(){var n=An((function(){var n;return Cn(this,(function(i){switch(i.label){case 0:return _.length<=1?(en.A.warning(e("当前任务只有一张图片")),[3,5]):[3,1];case 1:return i.trys.push([1,4,,5]),[4,nn.w.instance.deleteImageItem(A.id)];case 2:return[4,i.sent()];case 3:return i.sent(),en.A.success(e("删除成功")),X(t.homeStore.currentAIDrawImageID),[3,5];case 4:return n=i.sent(),jn.error("删除失败:",n),en.A.error(e("删除失败")),[3,5];case 5:return[2]}}))}));return function(){return n.apply(this,arguments)}}(),G=function(){if(_&&A){var n=_.findIndex((function(n){return n===A}));n>0&&I(_[n-1])}},$=function(){if(_&&A){var n=_.findIndex((function(n){return n===A}));n<_.length-1&&I(_[n+1])}};return(0,i.jsx)("div",{className:L?a.IsLandscape_root:a.root,children:(0,i.jsxs)("div",{className:a.img_content,children:[(0,i.jsx)("button",{className:"page_button left",onClick:G,style:{display:_&&A&&0!==_.findIndex((function(n){return n===A}))&&(D||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangzuo",size:24})}),(0,i.jsx)("button",{className:"page_button right",onClick:$,style:{display:_&&A&&_.findIndex((function(n){return n===A}))!==_.length-1&&(D||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangyou",size:24})}),t.homeStore.currentAIDrawImageID&&!A&&(0,i.jsxs)("div",{className:"main_loading_container",onMouseEnter:function(){return P(!0)},onMouseLeave:function(n){var e=n.relatedTarget;(null==e?void 0:e.classList.contains("page_button"))||P(!1)},children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,i.jsx)("span",{children:e("AI效果图生成中，预计等待1分钟。若退出本页，可在历史任务查看结果。")})]}),!t.homeStore.currentAIDrawImageID&&(0,i.jsxs)("div",{className:"empty_content",children:[(0,i.jsx)(S.A,{type:"icon-none"}),(0,i.jsxs)("span",{children:["-",e("暂无内容"),"-"]})]}),_&&_.length>0&&A&&(0,i.jsxs)("div",{className:"main_container",onMouseEnter:function(){return P(!0)},onMouseLeave:function(n){var e=n.relatedTarget;(null==e?void 0:e.classList.contains("page_button"))||P(!1)},onTouchStart:function(n){var e=n.touches[0];H(e.clientX),W(!1)},onTouchMove:function(n){if(!Z){var e=n.touches[0],t=e.clientX-T;Math.abs(t)>50&&(W(!0),t>0?G():$(),H(e.clientX))}},children:[(0,i.jsx)(hn.A,{height:"100%",width:"100%",style:{objectFit:"contain"},src:A.imageResult}),(0,i.jsx)(i.Fragment,{children:(0,u.fZ)()?(0,i.jsxs)("div",{className:a.operation_container,children:[(0,i.jsxs)("div",{className:"mobile_icon_btn",onClick:Y,children:[(0,i.jsx)(S.A,{type:"icon-xiazai"}),(0,i.jsx)("span",{children:e("下载")})]}),(0,i.jsxs)("div",{className:"mobile_icon_btn",onClick:q,children:[(0,i.jsx)(S.A,{type:"icon-DeleteFilled1"}),(0,i.jsx)("span",{children:e("删除")})]})]}):(0,i.jsxs)("div",{className:a.operation_container,children:[(0,i.jsx)(_n.A,{title:e("下载"),children:(0,i.jsx)("div",{className:"iconBtn",onClick:Y,children:(0,i.jsx)(S.A,{type:"icon-xiazai"})})}),(0,i.jsx)(_n.A,{title:e("删除"),children:(0,i.jsx)("div",{className:"iconBtn",onClick:q,children:(0,i.jsx)(S.A,{type:"icon-DeleteFilled1"})})})]})})]}),(0,i.jsxs)("div",{className:"bottom_container",children:[(null==N?void 0:N.diffuseImage)&&(0,i.jsxs)("div",{className:"diffuse_img_container",children:[(0,i.jsx)("div",{className:"view_icon",children:(0,i.jsx)("span",{children:e("视角")})}),(0,i.jsx)("img",{className:"diffuse_img",src:N.diffuseImage,alt:""})]}),_&&_.length>0?(0,i.jsx)("div",{className:"list_container",ref:O,onWheel:function(n){O.current&&(n.preventDefault(),O.current.scrollLeft+=n.deltaY)},children:_.map((function(n,e){return(0,i.jsx)("img",{className:"img_item ".concat(A===n?"selected":""),src:n.imageResult,alt:"",onClick:function(){I(n)}},e)}))}):(0,i.jsx)("div",{className:"list_container",children:Array.from({length:null==N?void 0:N.imageNum}).map((function(n,e){return(0,i.jsx)("div",{className:"loading_item"},e)}))})]})]})})}));function En(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function zn(){var n=En(["\n      width: 280px;\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      border-left: 1px solid #0000000F;\n      padding: 20px;\n      display: flex;\n      flex-direction: column;\n      \n      @media screen and (max-width: 1600px) {\n        width: 240px;\n      }\n\n      .content_container {\n        flex: 1;\n        overflow-y: auto;\n        margin-right: -8px;\n        padding-right: 8px;\n\n        /* 隐藏滚动条 - Webkit浏览器 */\n        &::-webkit-scrollbar {\n          display: none;\n        }\n        \n        /* 隐藏滚动条 - Firefox */\n        scrollbar-width: none;\n        \n        /* 隐藏滚动条 - IE */\n        -ms-overflow-style: none;\n      }\n    "]);return zn=function(){return n},n}function Dn(){var n=En(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: 600;\n      font-size: 16px;\n      line-height: 1.5;\n      letter-spacing: 0px;\n      text-align: left;\n      margin-bottom: 12px;\n\n      .more_button{\n        display:flex;\n        align-items: center;\n        justify-content: center;\n        height: 28px;\n        width: 80px;\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        cursor: pointer;\n\n        span{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n    "]);return Dn=function(){return n},n}function Pn(){var n=En(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      position: relative;\n      margin-bottom: 12px;\n\n      .backdrop_content{\n        border-radius: 4px 4px 0 0;\n        background: #E0E1E1;\n        width: calc(100% - 20px);\n        height: 10px;\n      }\n\n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        height: 181px;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        \n        &.select{\n          border: 2px solid #147FFA;\n        }\n\n        @media screen and (max-width: 1600px) {\n          height: 150.83px;\n        }\n\n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin-top: 7px;\n        .name{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          max-width: 65%;\n        }\n        .time{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n        }\n        svg {\n          color: #5B5E60 !important;\n        }\n\n        .icon_wrapper {\n            padding: 4px;\n            margin-left: 4px;\n            cursor: pointer;\n            border-radius: 2px;\n            \n            &:hover, &.hover {\n                background: #EAEAEB;\n            }\n\n            svg {\n                color: #5B5E60 !important;\n            }\n        }\n      }\n\n      .main_img_container{\n        width: 100%;\n        height: 181px;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n\n        &.select{\n          border: 2px solid #147FFA;\n        }\n\n        @media screen and (max-width: 1600px) {\n          height: 150.83px;\n        }\n\n        img {\n            width: 100%;\n            height: 100%;\n            border-radius: 4px;\n            object-fit: cover;\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n      }\n    "]);return Pn=function(){return n},n}function On(){var n=En(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      flex-direction: column;\n      gap: 4px;\n      height: 44px;\n      width: 100%;\n      .more_text {\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: center;\n      }\n      .more_button{\n        color: #147FFA;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: center;\n        cursor: pointer;\n      }\n    "]);return On=function(){return n},n}function Bn(){var n=En(["\n      .ant-modal-content {\n        height: 80vh;\n        overflow-y: auto;\n        padding: 0;\n      }\n      .ant-modal-header {\n        height: 40px;\n        background: #F4F5F5;\n        padding: 8px 20px;\n        margin-bottom: 0;\n      }\n      \n      .ant-modal-close {\n        width: 24px;\n        height: 24px;\n        top: 8px;\n        &:hover {\n          background: #F4F5F5;\n        }\n      }\n      .ant-modal-title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n    "]);return Bn=function(){return n},n}function Ln(){var n=En(["\n      display: grid;\n      grid-gap: 20px;\n      padding: 20px 0 20px 20px;\n      max-height: calc(80vh - 40px);\n      overflow-y: auto;\n      position: relative;\n\n      @media screen and (min-width: 1400px) {\n          grid-template-columns: repeat(5, calc(20% - 20px));\n      }\n      \n      @media screen and (max-width: 1400px) and (min-width: 960px) {\n          grid-template-columns: repeat(4, calc(25% - 20px));\n      }\n      \n      @media screen and (max-width: 960px) {\n          grid-template-columns: repeat(3, calc(33.33% - 20px));\n      }\n\n      &::-webkit-scrollbar {\n          width: 6px;\n      }\n      \n      &::-webkit-scrollbar-thumb {\n          background: #00000026;\n          border-radius: 3px;\n      }\n      \n      &::-webkit-scrollbar-track {\n          background: transparent;\n      }\n    "]);return Ln=function(){return n},n}function Rn(){var n=En(["\n      border: 1px solid transparent;\n      position: relative;\n      .main_img_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n        overflow: hidden;\n        \n        img {\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          object-fit: contain;\n          position: absolute;\n          left: 50%;\n          transform: translateX(-50%);\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n      }\n\n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin: 8px 0;\n        padding: 0 8px;\n        \n        .name{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          max-width: 180px;\n          \n          @media screen and (max-width: 1400px) and (min-width: 960px) {\n            max-width: 150px;\n          }\n          \n          @media screen and (max-width: 960px) {\n            max-width: 120px;\n          }\n\n        }\n        \n        .time {\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          // text-overflow: ellipsis;\n          max-width: 80px;\n        }\n      }\n      .view_button\n      {\n        position: absolute;\n        bottom: 50%;\n        left: 50%;\n        transform: translate(-50%, 50%);\n        width: 148px;\n        height: 32px;\n        border-radius: 8px;\n        opacity: 0;\n        transition: all 0.3s;\n        border-radius: 6px;\n        background: #FFFFFF;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        cursor: pointer;\n      }\n      &:hover {\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #0000000F;\n        box-shadow: 0px 20px 40px 0px #0000000A;\n        .view_button {\n          opacity: 1;\n        }\n      }\n    "]);return Rn=function(){return n},n}var Mn=(0,f.rU)((function(n){var e=n.css;return{root:e(zn()),title:e(Dn()),img_content:e(Pn()),more_content:e(On()),history_modal:e(Bn()),modal_content:e(Ln()),modal_item:e(Rn())}})),Tn=t(86470),Hn=t(77320),Un=t(11192),Zn=t(65640);function Wn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Vn(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function Xn(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var a=n.apply(e,t);function o(n){Vn(a,i,r,o,s,"next",n)}function s(n){Vn(a,i,r,o,s,"throw",n)}o(void 0)}))}}function Yn(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function qn(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){Yn(n,e,t[e])}))}return n}function Gn(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function $n(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||Kn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jn(n){return function(n){if(Array.isArray(n))return Wn(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||Kn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kn(n,e){if(n){if("string"==typeof n)return Wn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Wn(n,e):void 0}}function Qn(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var ne=(0,a.observer)((function(){var n=(0,o.B)().t,e=(0,s.P)(),t=Mn().styles,a=$n((0,r.useState)([]),2),d=a[0],u=a[1],p=$n((0,r.useState)([]),2),f=p[0],h=p[1],g=$n((0,r.useState)(!1),2),x=g[0],m=g[1],b=$n((0,r.useState)(1),2),v=b[0],y=b[1],w=$n((0,r.useState)(15),2),F=w[0],_=(w[1],$n((0,r.useState)(0),2)),j=_[0],k=_[1];l.nb.instance.layout_container;l.nb.UseApp(c.e.AppName),l.nb.instance&&(l.nb.t=n);var A={items:[{key:"reEdit",label:n("重新查看")},{key:"delete",label:n("删除"),danger:!0}],onClick:function(n){var e=n.key,t=n.item;return I(e,t)}},I=function(){var n=Xn((function(n,t){return Qn(this,(function(i){switch(i.label){case 0:return Zn.log("clicked",n,t),"addToAssets"!==n?[3,1]:[3,5];case 1:return"reEdit"!==n?[3,2]:(Zn.log("重新查看",t),e.homeStore.setCurrentAIDrawImageID(t.id),[3,5]);case 2:return"delete"!==n?[3,4]:[4,nn.w.instance.deleteImage(t.id)];case 3:return i.sent().success&&N(),[3,5];case 4:i.label=5;case 5:return[2]}}))}));return function(e,t){return n.apply(this,arguments)}}(),C=function(){var n=Xn((function(){var n;return Qn(this,(function(e){switch(e.label){case 0:return[4,nn.w.instance.queryImageList("",v,F)];case 1:return n=e.sent(),u(v>1?Jn(d).concat(Jn(n.result)):n.result),k(n.recordCount),[2]}}))}));return function(){return n.apply(this,arguments)}}(),N=function(){var n=Xn((function(){var n;return Qn(this,(function(e){switch(e.label){case 0:return[4,nn.w.instance.queryImageList("",1,30)];case 1:return n=e.sent(),h(n.result),k(n.recordCount),[2]}}))}));return function(){return n.apply(this,arguments)}}();(0,r.useEffect)((function(){C()}),[v]),(0,r.useEffect)((function(){N()}),[]),(0,r.useEffect)((function(){e.homeStore.refreshAtlas&&(N(),e.homeStore.setRefreshAtlas(!1))}),[e.homeStore.refreshAtlas]);var E=function(){m(!0),y(1),C()};return(0,i.jsxs)("div",{className:t.root,children:[(0,i.jsxs)("div",{className:t.title,children:[(0,i.jsx)("span",{children:n("历史任务")}),(0,i.jsx)("div",{className:"more_button",onClick:E,children:(0,i.jsx)("span",{style:{height:20,width:48},children:n("查看更多")})})]}),(0,i.jsxs)("div",{className:"content_container",children:[f.map((function(r,a){return(0,i.jsxs)("div",{className:t.img_content,children:[(0,i.jsx)("div",{className:"backdrop_content"}),r.imageResult?(0,i.jsxs)("div",{className:"main_img_container ".concat(e.homeStore.currentAIDrawImageID===r.id?"select":""),children:[(0,i.jsx)("img",{src:r.imageResult,alt:""}),(0,i.jsx)("div",{className:"number_tag",children:r.imageResultList.length})]}):(0,i.jsxs)("div",{className:"main_loading_container ".concat(e.homeStore.currentAIDrawImageID===r.id?"select":""),children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,i.jsx)("span",{children:n("生成中...")})]}),(0,i.jsxs)("div",{className:"info_content",children:[(0,i.jsx)(_n.A,{title:n(r.layoutName),children:(0,i.jsx)("span",{className:"name",children:n(r.layoutName)})}),(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,i.jsx)(_n.A,{title:n(r.createDate),children:(0,i.jsx)("span",{className:"time",children:(0,Un.a)(r.createDate)})}),(0,i.jsx)(Tn.A,{menu:Gn(qn({},A),{onClick:function(n){var e=n.key;return A.onClick({key:e,item:r})}}),trigger:["click"],children:(0,i.jsx)("div",{className:"icon_wrapper",children:(0,i.jsx)(S.A,{type:"icon-gengduo_bold"})})})]})]})]},a)})),j>30&&(0,i.jsxs)("div",{className:t.more_content,children:[(0,i.jsx)("span",{className:"more_text",children:n("-显示近30条任务-")}),(0,i.jsx)("span",{className:"more_button",onClick:E,children:n("查看更多")})]})]}),(0,i.jsx)(Hn.A,{title:n("历史记录"),open:x,onCancel:function(){return m(!1)},width:"80%",footer:null,className:t.history_modal,children:(0,i.jsx)("div",{className:t.modal_content,onScroll:function(n){var e=n.target;e.scrollTop+e.offsetHeight>=e.scrollHeight&&d.length<j&&y(v+1)},children:d.map((function(r,a){return(0,i.jsxs)("div",{className:t.modal_item,children:[r.imageResult?(0,i.jsxs)("div",{className:"main_img_container",children:[(0,i.jsx)("img",{src:r.imageResult,alt:""}),(0,i.jsx)("div",{className:"number_tag",children:r.imageResultList.length})]}):(0,i.jsxs)("div",{className:"main_loading_container",children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,i.jsx)("span",{children:n("生成中...")})]}),(0,i.jsxs)("div",{className:"info_content",children:[(0,i.jsx)(_n.A,{title:n(r.layoutName),children:(0,i.jsx)("span",{className:"name",children:n(r.layoutName)})}),(0,i.jsx)(_n.A,{title:n(r.createDate),children:(0,i.jsx)("span",{className:"time",children:n(r.createDate.split(" ")[0])})})]}),(0,i.jsx)("div",{className:"view_button",onClick:function(){return function(n){m(!1),e.homeStore.setCurrentAIDrawImageID(n.id)}(r)},children:n("查看")})]},a)}))})})]})})),ee=t(75670),te=t(6768),ie=t(73465),re=t(49063),ae=t(65810);function oe(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function se(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function le(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return oe(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return oe(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ce(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var de=(0,a.observer)((function(n){var e=n.modelType,t=(0,o.B)().t,a=(0,s.P)(),f=j().styles,h=(0,r.useRef)(),g=(0,r.useRef)(null),x=le((0,r.useState)(-2),2),m=x[0],b=x[1],v=le((0,r.useState)(window.innerWidth<window.innerHeight),2),y=v[0],w=v[1],F=le((0,r.useState)(!1),2),_=F[0],A=F[1],I=(0,re.Zp)();l.nb.UseApp(c.e.AppName),l.nb.instance&&(l.nb.t=t);var C=function(){A(!0),a.homeStore.setRefreshAtlas(!0),b(3)},N=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("edit_canvas")),l.nb.instance.update()),l.nb.instance&&(l.nb.instance._is_landscape=y),w(window.innerWidth<window.innerHeight)},E=function(){var n,e=(n=function(){var n,e,t;return ce(this,(function(i){switch(i.label){case 0:return u.uN?(n={isDelete:0,pageIndex:1,pageSize:9,keyword:u.uN},[4,p.D.getLayoutSchemeList(n)]):[2];case 1:return e=i.sent(),t=e.layoutSchemeDataList,e.total,t&&(l.nb.DispatchEvent(l.n0.OpenMyLayoutSchemeData,t[0]),l.nb.emit(d.U.OpenHouseSearching,!1)),[2]}}))},function(){var e=this,t=arguments;return new Promise((function(i,r){var a=n.apply(e,t);function o(n){se(a,i,r,o,s,"next",n)}function s(n){se(a,i,r,o,s,"throw",n)}o(void 0)}))});return function(){return e.apply(this,arguments)}}();(0,r.useEffect)((function(){var n;window.addEventListener("resize",N),N(),l.nb.instance&&!e&&(l.nb.instance.initialized||(l.nb.instance.init(),l.nb.RunCommand(c.f.AiCadMode),l.nb.instance.prepare().then((function(){E()})),l.nb.instance.bindCanvas(document.getElementById("edit_canvas"))),l.nb.instance.update());(null===(n=window)||void 0===n?void 0:n.URLSearchParams)&&("atlas"===new URLSearchParams(window.location.search).get("from")&&C())}),[]);return(0,i.jsxs)("div",{className:f.root,children:[(0,i.jsxs)("div",{className:f.topMenu,style:{height:window.innerWidth<450?"48px":"72px",borderBottom:y?"1px solid #00000026":"none"},children:[(0,i.jsxs)("div",{className:"left",children:[window.innerWidth>=460&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"28px"}}),(0,i.jsx)("div",{style:{borderLeft:"2px solid #a1a3a5",height:"16px",margin:"0 12px"}}),(0,i.jsx)("span",{style:{marginRight:"24px"},children:t("AI绘图")})]}),(0,i.jsxs)("div",{className:"back_button",onClick:function(){l.nb.instance.layout_container;var n,e=l.nb.instance.scene3D;if(_)A(!1),b(-2);else if(l.nb.emit_M(ae.z.showLight3DViewer,!0),e.setCemeraMode(k.I5.FirstPerson),null===(n=window)||void 0===n?void 0:n.URLSearchParams){var t=new URLSearchParams(window.location.search).get("from");switch((0,u.fZ)(!0),t){case"pchome":default:I("/");break;case"pcdesign":I("/design?from=2dedit");break;case"mobilehome":I("/padMobile");break;case"atlas":a.homeStore.setZIndexOf3DViewer(4),I("/");break;case"mobileResult":I("/trial?from=aidraw")}}},children:[(0,i.jsx)(S.A,{style:{margin:"7px 2px 7px 12px"},type:"icon-line_left"}),(0,i.jsx)("span",{style:{height:22,width:28},children:t("返回")})]})]}),(0,i.jsx)("div",{className:"right",children:(0,u.fZ)()&&(0,i.jsxs)("div",{className:"history_button",onClick:C,children:[(0,i.jsx)(S.A,{type:"icon-tuku"}),(0,i.jsx)("span",{style:{height:20,width:48},children:t("历史任务")})]})})]}),(0,i.jsx)(i.Fragment,{children:(0,u.fZ)()?y?a.homeStore.currentAIDrawImageID?(0,i.jsxs)("div",{className:f.IsLandscape_main_container,children:[(0,i.jsx)(Nn,{}),(0,i.jsx)(ie.A,{title:(0,i.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,i.jsx)("span",{children:t("参数编辑")}),(0,i.jsx)(S.A,{type:"icon-Close_Large",onClick:function(){return a.homeStore.setOpenFilterField(!1)},style:{cursor:"pointer"}})]}),placement:"bottom",onClose:function(){return a.homeStore.setOpenFilterField(!1)},closable:!1,open:a.homeStore.openFilterField,height:"90%",styles:{body:{padding:"4px"}},style:{borderRadius:"12px 12px 0px 0px"},children:(0,i.jsx)(fn,{})},"bottom"),(0,i.jsx)("div",{className:f.drawer_btn,children:(0,i.jsxs)("div",{className:"FilterFieldBtn",onClick:function(){return a.homeStore.setOpenFilterField(!0)},children:[t("参数编辑"),(0,i.jsx)(S.A,{type:"icon-a-fangxiangshang"})]})}),(0,i.jsx)("div",{className:f.mobile_atlas_container,style:{zIndex:m},children:(0,i.jsx)(ee.A,{setZIndexOfMobileAtlas:b})})]}):(0,i.jsxs)("div",{className:f.IsLandscape_main_container,children:[(0,i.jsx)(fn,{}),(0,i.jsx)("div",{className:f.mobile_atlas_container,style:{zIndex:m},children:(0,i.jsx)(ee.A,{setZIndexOfMobileAtlas:b})})]}):(0,i.jsxs)("div",{className:f.main_container,children:[(0,i.jsx)(fn,{}),(0,i.jsx)(Nn,{}),(0,i.jsx)("div",{className:f.mobile_atlas_container,style:{zIndex:m},children:(0,i.jsx)(ee.A,{setZIndexOfMobileAtlas:b})})]}):(0,i.jsxs)("div",{className:f.main_container,children:[(0,i.jsx)(fn,{}),(0,i.jsx)(Nn,{}),(0,i.jsx)(ne,{})]})}),(0,i.jsx)(te.A,{ref:h}),!e&&(0,i.jsx)("div",{id:"Canvascontent",className:f.content,children:(0,i.jsx)("div",{ref:g,id:"body_container",children:(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas"})})})]})}))},13880:function(n,e,t){t.d(e,{A:function(){return i}});var i=t(30819).A}}]);