import { Group, Object3D, Vector3Like } from "three";
import { I_SwjEntityBase, I_SwjWindow } from "../../AICadData/SwjLayoutData";
import { TPainter } from "../../Drawing/TPainter";
import { compareNames } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { IRoomEntityRealType, IType2UITypeDict, IUIType2TypeDict, KeyEntity } from "../IRoomInterface";

import { LayoutAI_App } from "../../../LayoutAI_App";
import { get_window_of_win_rect } from "../../AICadData/SwjDataBasicFuncs";
import { SceneMaterialMode } from "../../Scene3D/MaterialManager";
import { MeshName, UserDataKey } from "../../Scene3D/NodeName";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TMaterialMatchingConfigs } from "../../Services/MaterialMatching/TMaterialMatchingConfigs";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TRoomEntity } from "./TRoomEntity";
import { TWinDoorDrawingUtils } from "./utils/TWinDoorDrawingUtils";
import { checkIsMobile } from "@/config";


export class TWindowDoorEntity extends TBaseEntity {

    _wall_rect: ZRect;

    _win_figure_element: TFigureElement;
    // 用于记录
    _default_material_id: string;

    _room_entities: TRoomEntity[];
    constructor(data: I_SwjWindow, win_rect: ZRect = null) {
        super(win_rect);
        this.importData(data);
        this._wall_rect = null;
        this._win_figure_element = null;
        this._room_entities = [];

        this._default_priority_for_selection = this._priority_for_selection = 3;

    }

    toWindowData() {
        let win = get_window_of_win_rect(this._rect);
        win.type = this.type;
        win.realType = this.realType;
        return win;
    }
    getRealTypeOptions(): { label: string; value: string; }[] {
        let values: IRoomEntityRealType[] = [];

        if (this.type === "Window") {
            values = ["OneWindow", "BayWindow", "SingleDoor", "SlidingDoor", "Railing"];

        }
        else if (this.type === "Door") {
            values = ["SingleDoor", "SlidingDoor", "DoubleDoor", "OneWindow", "BayWindow", "DoorHole", "SafetyDoor"];
        }
        let ans: { label: string, value: string }[] = [];

        for (let val of values) {
            ans.push({ label: LayoutAI_App.t(IType2UITypeDict[val]) || LayoutAI_App.t(val), value: IType2UITypeDict[val] || val });
        }
        return ans;
    }

    get ui_realType() {
        return super.ui_realType;
    }

    set ui_realType(s: string) {
        this.realType = IUIType2TypeDict[s] as IRoomEntityRealType || this.realType;


        if (compareNames([this.realType], ["OneWindow", "BayWindow", "Railing"])) {
            this.type = "Window";
        }
        else if (compareNames([this.realType], ["SingleDoor", "SlidingDoor", "DoubleDoor", "DoorHole", "SafetyDoor"])) {
            this.type = "Door";
        }
        else {
            this.type = this.realType.toLocaleLowerCase().indexOf("window") >= 0 ? "Window" : "Door";
        }
        this.setRectProperties();
    }

    get isWindow() {
        return this.type === "Window";
    }

    get isDoor() {
        return this.type === "Door";
    }

    static getOrMakeEntityOfCadRect(win_rect: ZRect): TWindowDoorEntity {
        let win_entity: TWindowDoorEntity = win_rect._attached_elements[KeyEntity] || null;
        if (!win_entity) {
            let win = get_window_of_win_rect(win_rect);
            win_entity = new TWindowDoorEntity(win);
            win_entity._rect = win_rect;
            win_entity.update();
            win_rect._attached_elements[KeyEntity] = win_entity;

        }
        return win_entity;
    }
    static RegisterGenerators() {
        TBaseEntity.RegisterPolyEntityGenerator("Window", this.getOrMakeEntityOfCadRect);
        TBaseEntity.RegisterPolyEntityGenerator("Door", this.getOrMakeEntityOfCadRect);
    }
    initRoomEntities() {
        this._room_entities = [];
    }
    addRoomEntity(room: TRoomEntity) {
        if (!this._room_entities) this.initRoomEntities();
        if (!this._room_entities.includes(room)) {
            this._room_entities.push(room);
        }
    }

    get roomtypes() {
        return this._room_entities.map((room) => room.room_type);
    }

    bindWallRect(wall_rect: ZRect) {
        if (Math.abs(wall_rect.nor.dot(this._rect.nor)) < 0.99) return false;

        let pp = wall_rect.project(this.rect.rect_center);

        if (Math.abs(pp.x) > wall_rect.w / 2) return false;

        if (Math.abs(pp.y) > this._rect.h) return false;

        if (!this._wall_rect || wall_rect.w > this._wall_rect.w) {
            this._wall_rect = wall_rect;
        }
        // 由于从3D解析来一字窗和推拉门的实际厚度与墙的厚度不一致，所以需要调整
        if (this.realType == "SlidingDoor" || this.realType == "OneWindow") {
            if (this.rect.h != wall_rect.h) {
                const center = this.rect.rect_center_3d.clone();
                this.rect.setDepth(wall_rect.h);
                this.rect.rect_center_3d = center;
                this.rect.updateRect();
            }
        }
        if(this._win_figure_element)
        {
            this._win_figure_element.rect.copy(this.rect);
            this._win_figure_element.rect.updateRect();
        }
    }
    importData(data: I_SwjEntityBase): void {
        super.importData(data);

        if (data.material_id) {
            if (data.material_id === "24661567"
                || data.material_id === "110170234"
                || data.material_id === "00415358") {
                data.realType = "BayWindow";
            }
            if (data.material_id === "00120381"
                || data.material_id === "28977182"
                || data.material_id === "28977181"
                || data.material_id === "28997326"
                || data.material_id === "29144834"
            ) {
                data.realType = "Railing";
            }
        }
        let t_type = data.realType || data.type || "";
        if (t_type.indexOf("SingleDoor") >= 0) {
            this.type = "Door";
            this.realType = "SingleDoor";
        }
        else if (t_type.indexOf("DoubleDoor") >= 0) {
            this.type = "Door";
            this.realType = "DoubleDoor"
        }
        else if (t_type.indexOf("SlidingDoor") >= 0) {
            this.type = "Door";
            this.realType = "SlidingDoor";
        }
        else if (t_type.indexOf("BayWindow") >= 0) {
            this.type = "Window";
            this.realType = "BayWindow";
        }
        else if (t_type.indexOf("PassDoor") >= 0 || t_type.indexOf("passdoor") >= 0) {
            this.type = "Door";
            this.realType = "PassDoor";
        }
        else if (t_type.indexOf("AluminumUnit") >= 0 || t_type.indexOf("OneWindow") >= 0 || t_type.indexOf("onewindow") >= 0
            || t_type.toLocaleLowerCase().indexOf("window") >= 0) {
            this.type = "Window";
            this.realType = "OneWindow";
        }
        else if (t_type.indexOf("DoorHole") >= 0 || t_type.indexOf("doorhole") >= 0) {
            this.type = "Door";
            this.realType = "DoorHole";
        }
        else if (t_type.indexOf("SafetyDoor") >= 0 || t_type.indexOf("safetydoor") >= 0) {
            this.type = "Door";
            this.realType = "SafetyDoor";
        }
        else if (t_type.indexOf("Railing") >= 0 || t_type.indexOf("railing") >= 0) {
            this.type = "Window";
            this.realType = "Railing";
        }
        else if (t_type.indexOf("Door") >= 0 || t_type.indexOf("door") >= 0) {
            this.type = "Door";
            this.realType = "SingleDoor";

            if (data.material_id && (
                data.material_id === "00057554"
                || data.material_id === "135714326"
                || data.material_id === "121365706"
                || data.material_id === "163968978"
                || data.material_id === "163981229")) {
                this.realType = "SlidingDoor";
            } else if(data.material_id && (
                data.material_id === "39269169"
                || data.material_id === "39269115")) {
                this.realType = "DoorHole";
            }
            else{
                if (data.length > 1250) {
                    this.realType = "SlidingDoor";
                }
                if (compareNames((data as I_SwjWindow).room_names || [], ["厨房"])) {
                    this.realType = "SlidingDoor";
                }
            }
        }
        else {
            this.realType = this.type as any;
        }

        if (data.realType === "BayWindow" || this.realType === "BayWindow") {
            if (this.thickness < 600) {
                this.thickness = 600;
            }
        }
        data.realType = this.realType;
        this._default_material_id = data.material_id;
    }

    getModelLocName() {
        if (this.realType === "Railing"
            || this.realType === "SafetyDoor"
            || this.realType === "DoorHole") {

            return IType2UITypeDict[this.realType];
        }
        if (this.type === "Door") {
            let room_types = this.roomtypes;
            room_types.sort((a, b) => TMaterialMatchingConfigs.roomTypesOrders.indexOf(b) - TMaterialMatchingConfigs.roomTypesOrders.indexOf(a));
            let first_room_type = room_types[0] || "";
            let doorModelLocName = TMaterialMatchingConfigs.roomType2DoorModellocMap.get(first_room_type);
            if (doorModelLocName == null) {
                doorModelLocName = '其它门';
            }
            return doorModelLocName;

        }
        else {
            return "一字窗";
        }
    }
    initWinFigureElement() {
        let doorModelLocName = this.getModelLocName();

        if (!this._win_figure_element) {
            this._win_figure_element = TFigureElement.createSimple(doorModelLocName);
        }

        this._win_figure_element.category = doorModelLocName;
        this._win_figure_element.sub_category = this.ui_realType;

        this._win_figure_element.rect.copy(this.rect);

        if (this.type === "Window" && (this.realType !== "Railing" && this.realType !== "BayWindow")) {
            this._win_figure_element.depth = Math.min(this._win_figure_element.depth, 120);
            this._win_figure_element.rect.updateRect();
        }

        if (this.height >= 2800 - 0.5) // 说明未被初始化过
        {
            if (this.type === "Window") // 如果是窗
            {
                if (this.realType === "BayWindow") {
                    this.pos_z = 600;
                    this.height = 1600;
                } else if (this.realType === "Railing") {
                    this.pos_z = 0;
                    this.height = 2400;
                }
                else {
                    this.pos_z = 950;
                    this.height = 1250;
                }

            }
            else {
                this.pos_z = 0;
                this.height = 2200;
            }
        }
        this._win_figure_element.height = this.height;
        this._win_figure_element.rect.zval = this.pos_z;

    }

    updateMesh3D(mode?: SceneMaterialMode): Object3D {
        if (!this._mesh3d) {
            this._mesh3d = new Group();
            this._mesh3d.name = MeshName.WinDoor;
        }
        for (let child of this._mesh3d.children) {
            this._mesh3d.remove(child);
        }

        this.initWinFigureElement();

        let figure_ele = this._win_figure_element;
        if (!figure_ele) return this._mesh3d;

        figure_ele.updateMesh3D();

        if (figure_ele._solid_mesh3D) {
            this._mesh3d.add(figure_ele._solid_mesh3D);
        }

        if (figure_ele._simple_mesh3D) {
            this._mesh3d.add(figure_ele._simple_mesh3D);
        }


        if (figure_ele._solid_mesh3D) {
            figure_ele._solid_mesh3D.visible = true;
        }
        if (figure_ele._simple_mesh3D) {
            figure_ele._simple_mesh3D.visible = false;
        }
        this._mesh3d.userData[UserDataKey.EntityOfMesh] = this;
        return this._mesh3d;
    }

    predictRealType() {
        if (this.realType) return;
        if (this.type === "Window") {
            this.realType = "OneWindow";
        }
        else if (this.type === "Door") {
            this.realType = "SingleDoor";
            if (this.length > 1250) {
                this.realType = "SlidingDoor";
            }
            let src_data = this._src_data as I_SwjWindow;
            if (src_data?.room_names) {
                if (compareNames((src_data as I_SwjWindow).room_names || [], ["厨房"])) {
                    this.realType = "SlidingDoor";
                }
            }
        }
    }
    setRectProperties(): void {
        super.setRectProperties();

        let win = get_window_of_win_rect(this._rect);

        if (this._src_data && (this._src_data as I_SwjWindow).room_names) {
            if (!win.room_names) {
                win.room_names = [...(this._src_data as I_SwjWindow).room_names];
            }
        }
        this._rect.ex_prop.label = (this.realType || "").toLocaleLowerCase();
    }
    initProperties(): void {
        let isMobile = checkIsMobile();
        super.initProperties();
        if(isMobile)
        {
            this._ui_properties["length"] = {
                name: LayoutAI_App.t("宽度"),
                widget: "LabelItem",
                type: "string",
                min: 1,
                disabled: isMobile,
                defaultValue: '' + Math.round(this.length),
                props: {
                    type: "input",
                    suffix: "mm"
                }
            }
        }
        this._ui_props_keys = ["ui_type", "ui_realType", "length", "rotation_z_degree"];
        this._bindPropertiesOnChange();
    }

    getDistanceForMousePosSelection(point: Vector3Like, tol: number = 300) {
        // 基础矩形距离
        let dist = this.getPointdistanceToBoundary(point);

        if (this.type === "Door" && this.realType === "SingleDoor") {
            const rect = this.rect;
            // 计算点到门的本地坐标
            const localPoint = rect.project(point);

            // 检查是否在门的矩形区域内
            const normalizedX = Math.abs(localPoint.x) / rect.w * 2; // 归一化到 [0,1] 范围
            const normalizedY = Math.abs(localPoint.y) / rect.h;     // 归一化到 [0,1] 范围

            if (normalizedX <= 1 && normalizedY <= 1) {
                dist = -tol; // 在矩形内部
            }
            // 检查弧形区域
            else if (localPoint.y < 0) {
                // 单开门：计算点到弧形的距离
                // 弧形中心在门的左边缘
                let arc_poly = TWinDoorDrawingUtils.getSingleDoorSectorArea(rect);
                dist = arc_poly.distanceToPoint(point);
            }
        } else if (this.type === "Door" && this.realType === "DoubleDoor") {
            const rect = this.rect;
            // 计算点到门的本地坐标
            const localPoint = rect.project(point);

            // 检查是否在门的矩形区域内
            const normalizedX = Math.abs(localPoint.x) / rect.w * 2; // 归一化到 [0,1] 范围
            const normalizedY = Math.abs(localPoint.y) / rect.h;     // 归一化到 [0,1] 范围

            if (normalizedX <= 1 && normalizedY <= 1) {
                dist = -tol; // 在矩形内部
            }
            // 检查弧形区域
            else if (localPoint.y < 0) {
                // 获取左右两个扇形区域
                const [arc_poly_left, arc_poly_right] = TWinDoorDrawingUtils.getDoubleDoorSectorAreas(rect);
                // 计算点到两个扇形的最短距离
                const dist_left = arc_poly_left.distanceToPoint(point);
                const dist_right = arc_poly_right.distanceToPoint(point);
                dist = Math.min(dist_left, dist_right);
            }
        } else if (this.type === "Door" && this.realType === "SafetyDoor") {
            const rect = this.rect;
            // 计算点到门的本地坐标
            const localPoint = rect.project(point);

            // 检查是否在门的矩形区域内
            const normalizedX = Math.abs(localPoint.x) / rect.w * 2; // 归一化到 [0,1] 范围
            const normalizedY = Math.abs(localPoint.y) / rect.h;     // 归一化到 [0,1] 范围

            if (normalizedX <= 1 && normalizedY <= 1) {
                dist = -tol; // 在矩形内部
            }
            // 检查弧形区域
            else if (localPoint.y < 0) {
                // 获取子母门左右两个扇形区域
                const [arc_poly_left, arc_poly_right] = TWinDoorDrawingUtils.getSafetyDoorSectorAreas(rect);
                // 计算点到两个扇形的最短距离
                const dist_left = arc_poly_left.distanceToPoint(point);
                const dist_right = arc_poly_right.distanceToPoint(point);
                dist = Math.min(dist_left, dist_right);
            }
        }
        return dist;
    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (this.realType === "DoorHole") {
            TWinDoorDrawingUtils.drawDoorHole(painter, this.rect, options);
        }
        else if (this.realType === "Railing") {
            TWinDoorDrawingUtils.drawRailing(painter, this.rect, options);
        }
        else if (this.realType === "SafetyDoor") {
            TWinDoorDrawingUtils.drawSafetyDoor(painter, this.rect, options);
        }
        else if (this.realType === "PassDoor") {
            TWinDoorDrawingUtils.drawPassDoor(painter, this.rect, options);
        }
        else {
            TWinDoorDrawingUtils.drawWinDoorRect(painter, this.rect, this.type, this.realType, options);
        }

    }
}