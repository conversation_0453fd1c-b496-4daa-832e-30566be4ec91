import {
    IEdgeLayout,
    IKitchenLayout,
    KitchenAreaType,
    KitchenLayoutType
} from "@layoutai/layout_service";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoomLayoutScheme } from "../../../TLayoutScheme/TRoomLayoutScheme";
import { TRoom } from "../../../TRoom";
import { TCheckRule } from "../TCheckRule";

/**
 * @description 相邻禁区评分
 * 烹饪区与水盆区、鲜储区不相邻
 * <AUTHOR>
 * @date 2025-08-30
 * @lastEditTime 2025-08-30 17:32:58
 * @lastEditors xuld
 */
export class TKitchenAreaNeighborCheckRule extends TCheckRule {
    checkValue(
        room: TRoom,
        figure_elements?: TFigureElement[],
        layout_scheme: TRoomLayoutScheme = null
    ) {
        if (!layout_scheme) {
            return { value: 0 };
        }

        let extra = layout_scheme.extra as IKitchenLayout;
        if (!extra) {
            return { value: 0 };
        }

        // 不相邻约束
        let isForbidNeighbor = false;
        if (extra.layoutType === KitchenLayoutType.I_TYPE) {
            isForbidNeighbor = this.isForbidNeighborCooking(extra.edgeLayouts[0]);
        } else if (extra.layoutType === KitchenLayoutType.II_TYPE) {
            isForbidNeighbor = this.isForbidNeighborCooking(extra.edgeLayouts[0]);
            if (!isForbidNeighbor) {
                isForbidNeighbor = this.isForbidNeighborCooking(extra.edgeLayouts[1]);
            }
        } else if (extra.layoutType === KitchenLayoutType.L_TYPE) {
            const edge0 = extra.edgeLayouts[0];
            isForbidNeighbor = this.isForbidNeighborCooking(edge0);

            const edge1 = extra.edgeLayouts[1];
            if (!isForbidNeighbor) {
                isForbidNeighbor = this.isForbidNeighborCooking(edge1);
            }

            // 转角处
            if (!isForbidNeighbor) {
                isForbidNeighbor = this.isForbidNeighborCookingOnCorner(edge0, edge1);
            }
        } else if (extra.layoutType === KitchenLayoutType.U_TYPE) {
            isForbidNeighbor = this.isForbidNeighborCooking(extra.edgeLayouts[0]);
            if (!isForbidNeighbor) {
                isForbidNeighbor = this.isForbidNeighborCooking(extra.edgeLayouts[1]);
            }
            if (!isForbidNeighbor) {
                isForbidNeighbor = this.isForbidNeighborCooking(extra.edgeLayouts[2]);
            }
            // 转角处
            if (!isForbidNeighbor) {
                isForbidNeighbor = this.isForbidNeighborCookingOnCorner(
                    extra.edgeLayouts[0],
                    extra.edgeLayouts[1]
                );
            }
            if (!isForbidNeighbor) {
                isForbidNeighbor = this.isForbidNeighborCookingOnCorner(
                    extra.edgeLayouts[1],
                    extra.edgeLayouts[2]
                );
            }
        }

        let score = 0;
        if (!isForbidNeighbor) {
            score += 1;
        }

        return { value: score };
    }

    private isForbidNeighborCooking(edgeLayout: IEdgeLayout) {
        let isForbidNeighbor = false;
        const indexCookingArea = edgeLayout.areaTypeRects.findIndex(
            item => item.areaType === KitchenAreaType.CookingArea
        );
        const indexSinkArea = edgeLayout.areaTypeRects.findIndex(
            item => item.areaType === KitchenAreaType.SinkArea
        );
        const indexStorageArea = edgeLayout.areaTypeRects.findIndex(
            item => item.areaType === KitchenAreaType.FreshStorageArea
        );
        if (indexCookingArea !== -1) {
            if (indexSinkArea !== -1 && Math.abs(indexCookingArea - indexSinkArea) === 1) {
                // 烹饪区与水盆区相邻
                isForbidNeighbor = true;
            }
            if (indexStorageArea !== -1 && Math.abs(indexCookingArea - indexStorageArea) === 1) {
                // 烹饪区与鲜储区相邻
                isForbidNeighbor = true;
            }
        }
        return isForbidNeighbor;
    }

    private isForbidNeighborCookingOnCorner(edge0: IEdgeLayout, edge1: IEdgeLayout) {
        let isForbidNeighbor = false;

        const edge0EndType = edge0.areaTypeRects[edge0.areaTypeRects.length - 1].areaType;
        const edge1StartType = edge1.areaTypeRects[0].areaType;

        const tps = [edge0EndType, edge1StartType];

        const indexCookingArea = tps.indexOf(KitchenAreaType.CookingArea);
        const indexSinkArea = tps.indexOf(KitchenAreaType.SinkArea);
        const indexStorageArea = tps.indexOf(KitchenAreaType.FreshStorageArea);
        if (indexCookingArea !== -1) {
            if (indexSinkArea !== -1 && Math.abs(indexCookingArea - indexSinkArea) === 1) {
                // 烹饪区与水盆区相邻
                isForbidNeighbor = true;
            }
            if (indexStorageArea !== -1 && Math.abs(indexCookingArea - indexStorageArea) === 1) {
                // 烹饪区与鲜储区相邻
                isForbidNeighbor = true;
            }
        }

        return isForbidNeighbor;
    }
}
