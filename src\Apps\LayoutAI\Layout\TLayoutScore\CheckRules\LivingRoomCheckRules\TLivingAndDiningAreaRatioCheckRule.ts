import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomSplitByPathClassfierCheckRule } from "./TLivingRoomSplitByPathClassfierCheckRule";

export class TLivingAndDiningAreaRatioCheckRule extends TLivingRoomSplitByPathClassfierCheckRule
{
    private minLenAndDepth: number = 3500;

    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]): any
    {
        void(room);
        void(figure_element);
        void(main_figures);

        let score: number = 0;
        if(this.livingSpaceRect && this.diningSpaceRect)
        {
            let livingArea: number = this.livingSpaceRect.area;
            let diningArea: number = this.diningSpaceRect.area;
            if(this.livingSpaceRect.min_hh < this.minLenAndDepth && 
                this.diningSpaceRect.min_hh < this.minLenAndDepth)
            {
                if(livingArea >  diningArea)
                {
                    score += 20;
                }
            }
        }
        return {score: score};   
    }
}