"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[8348],{82028:function(n,e,t){t.r(e),t.d(e,{default:function(){return qt}});var i=t(13274),r=t(85783),o=t(66910),a=t(15696),c=t(77320),l=t(33100),s=t(36134),u=t(41594),d=t.n(u),g=t(27347),M=t(98612),p=t(9003),f=t(88934),x=t(78644),h=t(83657),N=t(23825),m=t(17287),j=t(90503),D=t(19356),y=t(25629),I=t(22640),b=t(84872),A=t(58567),w=t(20995),v=t(56697),z=t(42751),S=t(49816),T=t(90112),E=t(51010),O=t(75670),C=t(53704),L=t(99030),k=t(17365),U=t(61928),Q=t(79874);function Y(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function P(){var n=Y(["\n      position:fixed;\n      left:0;\n      bottom:0;\n      height: calc(var(--vh, 1vh) * 100);\n      width:100%;\n      z-index: 999;\n      background: #fff;\n      .slide-enter {\n        transform: translateX(-100%);\n        opacity: 0;\n      }\n\n      .slide-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-exit-active {\n        transform: translateX(100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n\n\n      .slide-reverse-enter {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n\n      .slide-reverse-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-reverse-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-reverse-exit-active {\n        transform: translateX(-100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .upload_hx\n      {\n        position: fixed;\n        right: 25px;\n        bottom: 60%;\n      }\n    "]);return P=function(){return n},n}function Z(){var n=Y(["\n      padding: 0px 10px;\n      height: 100%;\n      /* .right_btns\n      {\n        position: fixed;\n        right: 25px;\n        top: 25px;\n      } */\n\n    "]);return Z=function(){return n},n}function B(){var n=Y(["\n      width: 100%;\n      height: 100%;\n    "]);return B=function(){return n},n}function _(){var n=Y(["\n      padding: 0px 40px;\n      height: calc(var(--vh, 1vh) * 100 - 170px);\n      margin-top: 16px;\n      overflow-y: scroll;\n      ::-webkit-scrollbar\n      {\n        display: none;\n      }\n      .demandLabel\n        {\n          font-weight: 600;\n          font-size: 16px;\n          color: #000;\n          margin-bottom: 8px;\n          margin-top: 20px;\n        }\n      .demandItem\n      {\n       \n        .tabRoot\n        {\n          display: flex;\n          flex-wrap: wrap;\n        }\n      }\n      .demandtab\n      {\n        display: flex;\n        width: 100px;\n        height: 32px;\n        padding: 4px 16px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n        background: #F2F3F4;\n        margin-right: 12px;\n        margin-bottom: 12px;\n      }\n      .selected\n      {\n        border-radius: 6px;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08);\n        color: #fff;\n      }\n    "]);return _=function(){return n},n}function G(){var n=Y(["\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    "]);return G=function(){return n},n}function F(){var n=Y(["\n      width: 100%;\n      height: 100%;\n    "]);return F=function(){return n},n}function R(){var n=Y(["\n      display: flex;\n      padding: 40px 40px 0px 40px;\n      justify-content: space-between;\n      align-items: center;\n\n      .title{\n        font-size: 24px;\n        font-weight: 600;\n        display: flex;\n        align-items: center;\n        .back {\n          width: 28px;\n          height: 28px;\n          border-radius: 6px;\n          background: #E9EBEB;\n          padding: 4px;\n          margin-right: 8px;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 14px;\n        }\n      }\n\n      .mySchemeButton{\n        display: flex;\n        align-items:center;\n        font-weight: 600;\n        position: fixed;\n        right: 120px;\n        font-size: 11px;\n      }\n      .myAtlasButton{\n        display: flex;\n        align-items:center;\n        font-weight: 600;\n        position: fixed;\n        right: 20px;\n        font-size: 11px;\n      }\n    "]);return R=function(){return n},n}function H(){var n=Y(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 100%;\n      height: 88px;\n      background: #fff;\n      display: flex;\n      align-items: center;\n      padding: 20px 60px;\n      justify-content: space-between;\n      .ant-btn\n      {\n        width: 160px;\n        height: 48px;\n        border-radius: 24px;\n      }\n      .rotate\n      {\n        font-size: 16px;\n        color: #5B5E60;\n      }\n    "]);return H=function(){return n},n}function W(){var n=Y(["\n      display: flex;\n      flex-wrap: wrap;\n      box-sizing: border-box;\n      margin-top: 20px;\n    "]);return W=function(){return n},n}function V(){var n=Y(["\n      width: calc(20% - 10px);\n      height: auto;\n      padding: 2px;\n      box-sizing: border-box;\n      position: relative;\n      margin-right: 10px;\n      @media (max-width: 800px) {\n        width: calc(33.33% - 10px);\n      }\n      img{\n        width: 100%;\n        aspect-ratio: 5/3;\n      }\n    "]);return V=function(){return n},n}function X(){var n=Y(["\n      padding: 0 5px;\n      "]);return X=function(){return n},n}function J(){var n=Y(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 100%;\n      margin-top: 5px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      padding: 0 10px;\n      .ant-rate\n      {\n        color: #FFAA00;\n        font-size: 16px !important;\n        .ant-rate-star:not(:last-child)\n        {\n          margin-inline-end: 3px;\n        }\n      }\n    "]);return J=function(){return n},n}function K(){var n=Y(["\n      color: #6C7175;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      display: flex;\n      margin-top: 5px;\n    "]);return K=function(){return n},n}var $=(0,Q.rU)((function(n){n.token;var e=n.css;return{enterPage:e(P()),selectHx:e(Z()),hxRoot:e(B()),selectDemand:e(_()),styleTitle:e(G()),demandRoot:e(F()),hxHeader:e(R()),bottom:e(H()),container_listInfo:e(W()),container_list:e(V()),textInfo:e(X()),container_title:e(J()),container_desc:e(K())}})),q=t(7991),nn=t(46396),en=t(76330),tn=t(53837),rn=t(34516),on=t(61307),an=t(5640),cn=t(41980),ln=t(87961),sn=t(9455);function un(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function dn(){var n=un(["\n    display: flex;\n    flex-direction: row-reverse;\n    background-color: #fff;\n    position: sticky;\n    bottom: 0;\n    align-items: center;\n    background-color: #f6f7f9;\n    padding-top: 7px;\n    "]);return dn=function(){return n},n}function gn(){var n=un(["\n    width: 100%;\n    height: 100vh;\n    border-radius: 12px;\n    overflow-y: auto;\n    display: flex;\n    flex-direction: column;\n    background-color: #f6f7f9;\n    position: fixed;\n    z-index: 999;\n    padding-top: 15px;\n    ::-webkit-scrollbar-thumb\n    {\n        display: none;\n    }\n    .back {\n        width: 28px;\n        height: 28px;\n        border-radius: 6px;\n        background: #E9EBEB;\n        padding: 4px;\n        margin-right: 8px;\n        cursor: pointer;\n        align-items: center;\n        justify-content: center;\n        font-size: 14px;\n    }\n    .atlas_header{\n        display:flex;\n        justify-content: space-between;\n\n        .segmented{\n\n        }\n\n        .back_button{\n            display:flex;\n            align-items: center;\n            margin-right: 20px;\n            height: 30px;\n            width: 74px;\n            border-radius: 8px;\n            background: #FFFFFF;\n            border: 1px solid #00000026;\n            margin-bottom: 10px;\n            cursor: pointer;\n            span{\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 14px;\n            line-height: 1.57;\n            letter-spacing: 0px;\n            text-align: left;\n            }\n        }\n        }\n    "]);return gn=function(){return n},n}function Mn(){var n=un(["\n    width:100%;\n    height: 100%;\n    padding-right: 20px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #959598;\n    "]);return Mn=function(){return n},n}function pn(){var n=un(["\n    display: grid;\n    gap: 20px;\n    flex: 1;\n    overflow-y: auto;\n\n    /* 隐藏滚动条 - Webkit浏览器 */\n    &::-webkit-scrollbar {\n        display: none;\n    }\n    \n    /* 隐藏滚动条 - Firefox */\n    scrollbar-width: none;\n    \n    /* 隐藏滚动条 - IE */\n    -ms-overflow-style: none;\n\n    // grid-template-rows: repeat(auto-fill, 200px);\n\n    @media screen and (min-width: 1400px) {\n        grid-template-columns: repeat(5, calc(20% - 20px));\n    }\n    \n    @media screen and (max-width: 1400px) and (min-width: 960px) {\n        grid-template-columns: repeat(4, calc(25% - 20px));\n    }\n    \n    @media screen and (max-width: 960px) and (min-width: 560px) {\n        grid-template-columns: repeat(3, calc(33.33% - 20px));\n    }\n    @media screen and (max-width: 560px) and (min-width: 320px) {\n        grid-template-columns: repeat(2, calc(50% - 20px));\n    }\n    @media screen and (max-width: 320px) {\n        grid-template-columns: repeat(1, 100%);\n    }\n\n    &::-webkit-scrollbar {\n        width: 6px;\n    }\n    \n    &::-webkit-scrollbar-thumb {\n        background: #00000026;\n        border-radius: 3px;\n    }\n    \n    &::-webkit-scrollbar-track {\n        background: transparent;\n    }\n    "]);return pn=function(){return n},n}function fn(){var n=un(["\n    border: 1px solid transparent;\n    // margin-bottom: 20px;\n    .main_img_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n        overflow: hidden;\n        .ant-image {\n        width: 100%;\n        height: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        }\n        .ant-image-mask{\n        opacity: 0;\n        }\n        img {\n        max-width: 100%;\n        max-height: 100%;\n        border-radius: 4px;\n        object-fit: contain;\n        position: absolute;\n        left: 50%;\n        transform: translateX(-50%);\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n    }\n\n    .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        padding: 1px;\n        background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n        mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n        mask-composite: exclude;\n        -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n        -webkit-mask-composite: xor;\n        pointer-events: none;\n        }\n        \n        span{\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        margin-top: 4px;\n        }\n    }\n\n    .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin: 8px 0;\n        padding: 0 8px;\n        \n        .name{\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: center;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        max-width: 180px;\n        \n        @media screen and (max-width: 1400px) and (min-width: 960px) {\n            max-width: 150px;\n        }\n        \n        @media screen and (max-width: 960px) and (min-width: 560px) {\n            max-width: 120px;\n        }\n        @media screen and (max-width: 560px) {\n            max-width: 80px;\n        }\n        }\n        \n        .time {\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: center;\n        white-space: nowrap;\n        overflow: hidden;\n        // text-overflow: ellipsis;\n        max-width: 80px;\n        }\n    }"]);return fn=function(){return n},n}function xn(){var n=un(["\n        padding-top: 20px;\n    "]);return xn=function(){return n},n}var hn=(0,Q.rU)((function(n){var e=n.css;return{PageContainer:e(dn()),root:e(gn()),noData:e(Mn()),content:e(pn()),item:e(fn()),display:e(xn())}})),Nn=t(72329);function mn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function jn(){var n=mn(["\n        display: flex;\n        flex-wrap: wrap;\n        height: auto;\n        margin: 0 -20px;\n    "]);return jn=function(){return n},n}function Dn(){var n=mn(["\n        margin: 10px 20px !important;\n        border-radius: 8px;\n        overflow: hidden;\n\n        @media screen and (min-width: 700px) {\n        width: calc((100% / 2) - 40px) !important;\n        }\n        @media screen and (min-width: 900px) {\n        width: calc((100% / 3) - 40px) !important;\n        }\n        @media screen and (min-width: 1150px) {\n        width: calc((100% / 4) - 40px) !important;\n        }\n        /* @media screen and (min-width: 1500px) {\n        width: calc((100% / 5) - 40px) !important;\n        }\n        @media screen and (min-width: 2000px) {\n        width: calc((100% / 6) - 40px) !important;\n        } */\n    "]);return Dn=function(){return n},n}function yn(){var n=mn(["\n        display: flex;\n        width: auto;\n        img {\n        width: 48px;\n        margin-top: -3px;\n        }\n    "]);return yn=function(){return n},n}function In(){var n=mn(["\n        margin-left: 8px;\n        justify-content: center;\n    "]);return In=function(){return n},n}function bn(){var n=mn(["\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: bold;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n    "]);return bn=function(){return n},n}function An(){var n=mn(["\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n    "]);return An=function(){return n},n}function wn(){var n=mn(["\n        border: none;\n        padding: 0 8px;\n        margin-left: 30px;\n        height: 32px;\n        background-color: #f4f5f5;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n        &:hover {\n        color: #282828 !important;\n        background-color: #f4f5f5 !important;\n        }\n    "]);return wn=function(){return n},n}function vn(){var n=mn(["\n        transform: rotate(45deg);\n    "]);return vn=function(){return n},n}var zn=(0,Q.rU)((function(n){var e=n.css;return{cardContainer:e(jn()),card:e(Dn()),content:e(yn()),right:e(In()),title:e(bn()),desc:e(An()),insertBtn:e(wn()),rotatedIcon:e(vn())}}));var Sn=t(97102),Tn=t(49104),En=t(51067),On=t(14963),Cn=t(61280),Ln=t(24534),kn=t(56425),Un={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"}}]},name:"info-circle",theme:"outlined"},Qn=t(98132),Yn=function(n,e){return u.createElement(Qn.A,(0,kn.A)({},n,{ref:e,icon:Un}))};var Pn=u.forwardRef(Yn),Zn=t(19938),Bn=t(12316),_n=t(97500),Gn=t.n(_n),Fn=t(53877),Rn=function(n){return(0,Tn.A)({},n.componentCls,{display:"inline-flex",alignItems:"center",maxWidth:"100%","&-icon":{display:"block",marginInlineStart:"4px",cursor:"pointer","&:hover":{color:n.colorPrimary}},"&-title":{display:"inline-flex",flex:"1"},"&-subtitle ":{marginInlineStart:8,color:n.colorTextSecondary,fontWeight:"normal",fontSize:n.fontSize,whiteSpace:"nowrap"},"&-title-ellipsis":{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"keep-all"}})};var Hn=d().memo((function(n){var e,t=n.label,r=n.tooltip,o=n.ellipsis,a=n.subTitle,c=(0,(0,u.useContext)(Zn.Ay.ConfigContext).getPrefixCls)("pro-core-label-tip"),l=(e=c,(0,Fn.X3)("LabelIconTip",(function(n){var t=(0,Sn.A)((0,Sn.A)({},n),{},{componentCls:".".concat(e)});return[Rn(t)]}))),s=l.wrapSSR,g=l.hashId;if(!r&&!a)return(0,i.jsx)(i.Fragment,{children:t});var M="string"==typeof r||d().isValidElement(r)?{title:r}:r,p=(null==M?void 0:M.icon)||(0,i.jsx)(Pn,{});return s((0,i.jsxs)("div",{className:Gn()(c,g),onMouseDown:function(n){return n.stopPropagation()},onMouseLeave:function(n){return n.stopPropagation()},onMouseMove:function(n){return n.stopPropagation()},children:[(0,i.jsx)("div",{className:Gn()("".concat(c,"-title"),g,(0,Tn.A)({},"".concat(c,"-title-ellipsis"),o)),children:t}),a&&(0,i.jsx)("div",{className:"".concat(c,"-subtitle ").concat(g).trim(),children:a}),r&&(0,i.jsx)(Bn.A,(0,Sn.A)((0,Sn.A)({},M),{},{children:(0,i.jsx)("span",{className:"".concat(c,"-icon ").concat(g).trim(),children:p})}))]}))})),Wn=t(63982),Vn=t(13286),Xn=t(32785),Jn=t(46505),Kn=function(n){var e=n.componentCls,t=n.antCls;return(0,Tn.A)({},"".concat(e,"-actions"),(0,Tn.A)((0,Tn.A)({marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none",display:"flex",gap:n.marginXS,background:n.colorBgContainer,borderBlockStart:"".concat(n.lineWidth,"px ").concat(n.lineType," ").concat(n.colorSplit),minHeight:42},"& > *",{alignItems:"center",justifyContent:"center",flex:1,display:"flex",cursor:"pointer",color:n.colorTextSecondary,transition:"color 0.3s","&:hover":{color:n.colorPrimaryHover}}),"& > li > div",{flex:1,width:"100%",marginBlock:n.marginSM,marginInline:0,color:n.colorTextSecondary,textAlign:"center",a:{color:n.colorTextSecondary,transition:"color 0.3s","&:hover":{color:n.colorPrimaryHover}},div:(0,Tn.A)((0,Tn.A)({position:"relative",display:"block",minWidth:32,fontSize:n.fontSize,lineHeight:n.lineHeight,cursor:"pointer","&:hover":{color:n.colorPrimaryHover,transition:"color 0.3s"}},"a:not(".concat(t,"-btn),\n            > .anticon"),{display:"inline-block",width:"100%",color:n.colorTextSecondary,lineHeight:"22px",transition:"color 0.3s","&:hover":{color:n.colorPrimaryHover}}),".anticon",{fontSize:n.cardActionIconSize,lineHeight:"22px"}),"&:not(:last-child)":{borderInlineEnd:"".concat(n.lineWidth,"px ").concat(n.lineType," ").concat(n.colorSplit)}}))};var $n=function(n){var e=n.actions,t=n.prefixCls,r=function(n){return(0,Fn.X3)("ProCardActions",(function(e){var t=(0,Sn.A)((0,Sn.A)({},e),{},{componentCls:".".concat(n),cardActionIconSize:16});return[Kn(t)]}))}(t),o=r.wrapSSR,a=r.hashId;return Array.isArray(e)&&null!=e&&e.length?o((0,i.jsx)("ul",{className:Gn()("".concat(t,"-actions"),a),children:e.map((function(n,r){return(0,i.jsx)("li",{style:{width:"".concat(100/e.length,"%"),padding:0,margin:0},className:Gn()("".concat(t,"-actions-item"),a),children:n},"action-".concat(r))}))})):o((0,i.jsx)("ul",{className:Gn()("".concat(t,"-actions"),a),children:e}))},qn=t(12867),ne=t(14069),ee=new(t(58058).Mo)("card-loading",{"0%":{backgroundPosition:"0 50%"},"50%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),te=function(n){return(0,Tn.A)({},n.componentCls,(0,Tn.A)((0,Tn.A)({"&-loading":{overflow:"hidden"},"&-loading &-body":{userSelect:"none"}},"".concat(n.componentCls,"-loading-content"),{width:"100%",p:{marginBlock:0,marginInline:0}}),"".concat(n.componentCls,"-loading-block"),{height:"14px",marginBlock:"4px",background:"linear-gradient(90deg, rgba(54, 61, 64, 0.2), rgba(54, 61, 64, 0.4), rgba(54, 61, 64, 0.2))",backgroundSize:"600% 600%",borderRadius:n.borderRadius,animationName:ee,animationDuration:"1.4s",animationTimingFunction:"ease",animationIterationCount:"infinite"}))};var ie=function(n){var e,t=n.style,r=n.prefix;return(0,(e=r||"ant-pro-card",(0,Fn.X3)("ProCardLoading",(function(n){var t=(0,Sn.A)((0,Sn.A)({},n),{},{componentCls:".".concat(e)});return[te(t)]}))).wrapSSR)((0,i.jsxs)("div",{className:"".concat(r,"-loading-content"),style:t,children:[(0,i.jsx)(qn.A,{gutter:8,children:(0,i.jsx)(ne.A,{span:22,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})})}),(0,i.jsxs)(qn.A,{gutter:8,children:[(0,i.jsx)(ne.A,{span:8,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,i.jsx)(ne.A,{span:15,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})})]}),(0,i.jsxs)(qn.A,{gutter:8,children:[(0,i.jsx)(ne.A,{span:6,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,i.jsx)(ne.A,{span:18,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})})]}),(0,i.jsxs)(qn.A,{gutter:8,children:[(0,i.jsx)(ne.A,{span:13,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,i.jsx)(ne.A,{span:9,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})})]}),(0,i.jsxs)(qn.A,{gutter:8,children:[(0,i.jsx)(ne.A,{span:4,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,i.jsx)(ne.A,{span:3,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,i.jsx)(ne.A,{span:16,children:(0,i.jsx)("div",{className:"".concat(r,"-loading-block")})})]})]}))},re=t(73709),oe=t(27054),ae=t(16862),ce=(t(68558),["tab","children"]),le=["key","tab","tabKey","disabled","destroyInactiveTabPane","children","className","style","cardProps"];var se=function(n){var e=(0,u.useContext)(Zn.Ay.ConfigContext).getPrefixCls;if(re.A.startsWith("5"))return(0,i.jsx)(i.Fragment,{});var t=n.key,r=n.tab,o=n.tabKey,a=n.disabled,c=n.destroyInactiveTabPane,l=n.children,s=n.className,d=n.style,g=n.cardProps,M=(0,Cn.A)(n,le),p=e("pro-card-tabpane"),f=Gn()(p,s);return(0,i.jsx)(Wn.A.TabPane,(0,Sn.A)((0,Sn.A)({tabKey:o,tab:r,className:f,style:d,disabled:a,destroyInactiveTabPane:c},M),{},{children:(0,i.jsx)(fe,(0,Sn.A)((0,Sn.A)({},g),{},{children:l}))}),t)},ue=function(n){return{backgroundColor:n.controlItemBgActive,borderColor:n.controlOutline}},de=function(n){var e=n.componentCls;return(0,Tn.A)((0,Tn.A)((0,Tn.A)({},e,(0,Sn.A)((0,Sn.A)({position:"relative",display:"flex",flexDirection:"column",boxSizing:"border-box",width:"100%",marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,backgroundColor:n.colorBgContainer,borderRadius:n.borderRadius,transition:"all 0.3s"},null===Fn.dF||void 0===Fn.dF?void 0:(0,Fn.dF)(n)),{},(0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)({"&-box-shadow":{boxShadow:"0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017",borderColor:"transparent"},"&-col":{width:"100%"},"&-border":{border:"".concat(n.lineWidth,"px ").concat(n.lineType," ").concat(n.colorSplit)},"&-hoverable":(0,Tn.A)({cursor:"pointer",transition:"box-shadow 0.3s, border-color 0.3s","&:hover":{borderColor:"transparent",boxShadow:"0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017"}},"&".concat(e,"-checked:hover"),{borderColor:n.controlOutline}),"&-checked":(0,Sn.A)((0,Sn.A)({},ue(n)),{},{"&::after":{visibility:"visible",position:"absolute",insetBlockStart:2,insetInlineEnd:2,opacity:1,width:0,height:0,border:"6px solid ".concat(n.colorPrimary),borderBlockEnd:"6px solid transparent",borderInlineStart:"6px solid transparent",borderStartEndRadius:2,content:'""'}}),"&:focus":(0,Sn.A)({},ue(n)),"&&-ghost":(0,Tn.A)({backgroundColor:"transparent"},"> ".concat(e),{"&-header":{paddingInlineEnd:0,paddingBlockEnd:n.padding,paddingInlineStart:0},"&-body":{paddingBlock:0,paddingInline:0,backgroundColor:"transparent"}}),"&&-split > &-body":{paddingBlock:0,paddingInline:0},"&&-contain-card > &-body":{display:"flex"}},"".concat(e,"-body-direction-column"),{flexDirection:"column"}),"".concat(e,"-body-wrap"),{flexWrap:"wrap"}),"&&-collapse",(0,Tn.A)({},"> ".concat(e),{"&-header":{paddingBlockEnd:n.padding,borderBlockEnd:0},"&-body":{display:"none"}})),"".concat(e,"-header"),{display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:n.paddingLG,paddingBlock:n.padding,paddingBlockEnd:0,"&-border":{"&":{paddingBlockEnd:n.padding},borderBlockEnd:"".concat(n.lineWidth,"px ").concat(n.lineType," ").concat(n.colorSplit)},"&-collapsible":{cursor:"pointer"}}),"".concat(e,"-title"),{color:n.colorText,fontWeight:500,fontSize:n.fontSizeLG,lineHeight:n.lineHeight}),"".concat(e,"-extra"),{color:n.colorText}),"".concat(e,"-type-inner"),(0,Tn.A)({},"".concat(e,"-header"),{backgroundColor:n.colorFillAlter})),"".concat(e,"-collapsible-icon"),{marginInlineEnd:n.marginXS,color:n.colorIconHover,":hover":{color:n.colorPrimaryHover},"& svg":{transition:"transform ".concat(n.motionDurationMid)}}),"".concat(e,"-body"),{display:"block",boxSizing:"border-box",height:"100%",paddingInline:n.paddingLG,paddingBlock:n.padding,"&-center":{display:"flex",alignItems:"center",justifyContent:"center"}}),"&&-size-small",(0,Tn.A)((0,Tn.A)({},e,{"&-header":{paddingInline:n.paddingSM,paddingBlock:n.paddingXS,paddingBlockEnd:0,"&-border":{paddingBlockEnd:n.paddingXS}},"&-title":{fontSize:n.fontSize},"&-body":{paddingInline:n.paddingSM,paddingBlock:n.paddingSM}}),"".concat(e,"-header").concat(e,"-header-collapsible"),{paddingBlock:n.paddingXS})))),"".concat(e,"-col"),(0,Tn.A)((0,Tn.A)({},"&".concat(e,"-split-vertical"),{borderInlineEnd:"".concat(n.lineWidth,"px ").concat(n.lineType," ").concat(n.colorSplit)}),"&".concat(e,"-split-horizontal"),{borderBlockEnd:"".concat(n.lineWidth,"px ").concat(n.lineType," ").concat(n.colorSplit)})),"".concat(e,"-tabs"),(0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)({},"".concat(n.antCls,"-tabs-top > ").concat(n.antCls,"-tabs-nav"),(0,Tn.A)({marginBlockEnd:0},"".concat(n.antCls,"-tabs-nav-list"),{marginBlockStart:n.marginXS,paddingInlineStart:n.padding})),"".concat(n.antCls,"-tabs-bottom > ").concat(n.antCls,"-tabs-nav"),(0,Tn.A)({marginBlockEnd:0},"".concat(n.antCls,"-tabs-nav-list"),{paddingInlineStart:n.padding})),"".concat(n.antCls,"-tabs-left"),(0,Tn.A)({},"".concat(n.antCls,"-tabs-content-holder"),(0,Tn.A)({},"".concat(n.antCls,"-tabs-content"),(0,Tn.A)({},"".concat(n.antCls,"-tabs-tabpane"),{paddingInlineStart:0})))),"".concat(n.antCls,"-tabs-left > ").concat(n.antCls,"-tabs-nav"),(0,Tn.A)({marginInlineEnd:0},"".concat(n.antCls,"-tabs-nav-list"),{paddingBlockStart:n.padding})),"".concat(n.antCls,"-tabs-right"),(0,Tn.A)({},"".concat(n.antCls,"-tabs-content-holder"),(0,Tn.A)({},"".concat(n.antCls,"-tabs-content"),(0,Tn.A)({},"".concat(n.antCls,"-tabs-tabpane"),{paddingInlineStart:0})))),"".concat(n.antCls,"-tabs-right > ").concat(n.antCls,"-tabs-nav"),(0,Tn.A)({},"".concat(n.antCls,"-tabs-nav-list"),{paddingBlockStart:n.padding})))},ge=function(n){return Array(25).fill(1).map((function(e,t){return function(n,e){var t=e.componentCls;return 0===n?(0,Tn.A)({},"".concat(t,"-col-0"),{display:"none"}):(0,Tn.A)({},"".concat(t,"-col-").concat(n),{flexShrink:0,width:"".concat(n/24*100,"%")})}(t,n)}))};var Me=["className","style","bodyStyle","headStyle","title","subTitle","extra","wrap","layout","loading","gutter","tooltip","split","headerBordered","bordered","boxShadow","children","size","actions","ghost","hoverable","direction","collapsed","collapsible","collapsibleIconRender","colStyle","defaultCollapsed","onCollapse","checked","onChecked","tabs","type"],pe=d().forwardRef((function(n,e){var t,r,o,a=n.className,c=n.style,l=n.bodyStyle,s=n.headStyle,g=n.title,M=n.subTitle,p=n.extra,f=n.wrap,x=void 0!==f&&f,h=n.layout,N=n.loading,m=n.gutter,j=void 0===m?0:m,D=n.tooltip,y=n.split,I=n.headerBordered,b=void 0!==I&&I,A=n.bordered,w=void 0!==A&&A,v=n.boxShadow,z=void 0!==v&&v,S=n.children,T=n.size,E=n.actions,O=n.ghost,C=void 0!==O&&O,L=n.hoverable,k=void 0!==L&&L,U=n.direction,Q=n.collapsed,Y=n.collapsible,P=void 0!==Y&&Y,Z=n.collapsibleIconRender,B=n.colStyle,_=n.defaultCollapsed,G=void 0!==_&&_,F=n.onCollapse,R=n.checked,H=n.onChecked,W=n.tabs,V=n.type,X=(0,Cn.A)(n,Me),J=(0,u.useContext)(Zn.Ay.ConfigContext).getPrefixCls,K=(0,Vn.A)()||{lg:!0,md:!0,sm:!0,xl:!1,xs:!1,xxl:!1},$=(0,Xn.A)(G,{value:Q,onChange:F}),q=(0,On.A)($,2),nn=q[0],en=q[1],tn=["xxl","xl","lg","md","sm","xs"],rn=function(n,e,t){return n?n.map((function(n){return(0,Sn.A)((0,Sn.A)({},n),{},{children:(0,i.jsx)(fe,(0,Sn.A)((0,Sn.A)({},null==t?void 0:t.cardProps),{},{children:n.children}))})})):((0,ae.g9)(!t,"Tabs.TabPane is deprecated. Please use `items` directly."),function(n){return n.filter((function(n){return n}))}((0,oe.A)(e).map((function(n){if(d().isValidElement(n)){var e=n.key,r=n.props||{},o=r.tab,a=r.children,c=(0,Cn.A)(r,ce);return(0,Sn.A)((0,Sn.A)({key:String(e)},c),{},{children:(0,i.jsx)(fe,(0,Sn.A)((0,Sn.A)({},null==t?void 0:t.cardProps),{},{children:a})),label:o})}return null}))))}(null==W?void 0:W.items,S,W),on=function(n,e){return n?e:{}},an=J("pro-card"),cn=function(n){return(0,Fn.X3)("ProCard",(function(e){var t=(0,Sn.A)((0,Sn.A)({},e),{},{componentCls:".".concat(n)});return[de(t),ge(t)]}))}(an),ln=cn.wrapSSR,sn=cn.hashId,un=(r=j,o=[0,0],(Array.isArray(r)?r:[r,0]).forEach((function(n,e){if("object"===(0,En.A)(n))for(var t=0;t<tn.length;t+=1){var i=tn[t];if(K[i]&&void 0!==n[i]){o[e]=n[i];break}}else o[e]=n||0})),o),dn=(0,On.A)(un,2),gn=dn[0],Mn=dn[1],pn=!1,fn=d().Children.toArray(S),xn=fn.map((function(n,e){var t;if(null!=n&&null!==(t=n.type)&&void 0!==t&&t.isProCard){pn=!0;var r=function(n){var e=n;if("object"===(0,En.A)(n))for(var t=0;t<tn.length;t+=1){var i=tn[t];if(null!=K&&K[i]&&void 0!==(null==n?void 0:n[i])){e=n[i];break}}return{span:e,colSpanStyle:on("string"==typeof e&&/\d%|\dpx/i.test(e),{width:e,flexShrink:0})}}(n.props.colSpan),o=r.span,a=r.colSpanStyle,c=Gn()(["".concat(an,"-col")],sn,(0,Tn.A)((0,Tn.A)((0,Tn.A)({},"".concat(an,"-split-vertical"),"vertical"===y&&e!==fn.length-1),"".concat(an,"-split-horizontal"),"horizontal"===y&&e!==fn.length-1),"".concat(an,"-col-").concat(o),"number"==typeof o&&o>=0&&o<=24)),l=ln((0,i.jsx)("div",{style:(0,Sn.A)((0,Sn.A)((0,Sn.A)((0,Sn.A)({},a),on(gn>0,{paddingInlineEnd:gn/2,paddingInlineStart:gn/2})),on(Mn>0,{paddingBlockStart:Mn/2,paddingBlockEnd:Mn/2})),B),className:c,children:d().cloneElement(n)}));return d().cloneElement(l,{key:"pro-card-col-".concat((null==n?void 0:n.key)||e)})}return n})),hn=Gn()("".concat(an),a,sn,(t={},(0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)((0,Tn.A)(t,"".concat(an,"-border"),w),"".concat(an,"-box-shadow"),z),"".concat(an,"-contain-card"),pn),"".concat(an,"-loading"),N),"".concat(an,"-split"),"vertical"===y||"horizontal"===y),"".concat(an,"-ghost"),C),"".concat(an,"-hoverable"),k),"".concat(an,"-size-").concat(T),T),"".concat(an,"-type-").concat(V),V),"".concat(an,"-collapse"),nn),(0,Tn.A)(t,"".concat(an,"-checked"),R))),Nn=Gn()("".concat(an,"-body"),sn,(0,Tn.A)((0,Tn.A)((0,Tn.A)({},"".concat(an,"-body-center"),"center"===h),"".concat(an,"-body-direction-column"),"horizontal"===y||"column"===U),"".concat(an,"-body-wrap"),x&&pn)),mn=l,jn=d().isValidElement(N)?N:(0,i.jsx)(ie,{prefix:an,style:0===(null==l?void 0:l.padding)||"0px"===(null==l?void 0:l.padding)?{padding:24}:void 0}),Dn=P&&void 0===Q&&(Z?Z({collapsed:nn}):(0,i.jsx)(Ln.A,{rotate:nn?void 0:90,className:"".concat(an,"-collapsible-icon ").concat(sn).trim()}));return ln((0,i.jsxs)("div",(0,Sn.A)((0,Sn.A)({className:hn,style:c,ref:e,onClick:function(n){var e;null==H||H(n),null==X||null===(e=X.onClick)||void 0===e||e.call(X,n)}},(0,Jn.A)(X,["prefixCls","colSpan"])),{},{children:[(g||p||Dn)&&(0,i.jsxs)("div",{className:Gn()("".concat(an,"-header"),sn,(0,Tn.A)((0,Tn.A)({},"".concat(an,"-header-border"),b||"inner"===V),"".concat(an,"-header-collapsible"),Dn)),style:s,onClick:function(){Dn&&en(!nn)},children:[(0,i.jsxs)("div",{className:"".concat(an,"-title ").concat(sn).trim(),children:[Dn,(0,i.jsx)(Hn,{label:g,tooltip:D,subTitle:M})]}),p&&(0,i.jsx)("div",{className:"".concat(an,"-extra ").concat(sn).trim(),onClick:function(n){return n.stopPropagation()},children:p})]}),W?(0,i.jsx)("div",{className:"".concat(an,"-tabs ").concat(sn).trim(),children:(0,i.jsx)(Wn.A,(0,Sn.A)((0,Sn.A)({onChange:W.onChange},(0,Jn.A)(W,["cardProps"])),{},{items:rn,children:N?jn:S}))}):(0,i.jsx)("div",{className:Nn,style:mn,children:N?jn:xn}),E?(0,i.jsx)($n,{actions:E,prefixCls:an}):null]})))})),fe=pe,xe=function(n){var e=n.componentCls;return(0,Tn.A)({},e,{"&-divider":{flex:"none",width:n.lineWidth,marginInline:n.marginXS,marginBlock:n.marginLG,backgroundColor:n.colorSplit,"&-horizontal":{width:"initial",height:n.lineWidth,marginInline:n.marginLG,marginBlock:n.marginXS}},"&&-size-small &-divider":{marginBlock:n.marginLG,marginInline:n.marginXS,"&-horizontal":{marginBlock:n.marginXS,marginInline:n.marginLG}}})};var he=function(n){var e=(0,(0,u.useContext)(Zn.Ay.ConfigContext).getPrefixCls)("pro-card"),t="".concat(e,"-divider"),r=function(n){return(0,Fn.X3)("ProCardDivider",(function(e){var t=(0,Sn.A)((0,Sn.A)({},e),{},{componentCls:".".concat(n)});return[xe(t)]}))}(e),o=r.wrapSSR,a=r.hashId,c=n.className,l=n.style,s=void 0===l?{}:l,d=n.type,g=Gn()(t,c,a,(0,Tn.A)({},"".concat(t,"-").concat(d),d));return o((0,i.jsx)("div",{className:g,style:s}))},Ne=fe;Ne.isProCard=!0,Ne.Divider=he,Ne.TabPane=se,Ne.Group=function(n){return(0,i.jsx)(fe,(0,Sn.A)({bodyStyle:{padding:0}},n))};var me=Ne,je=t(65640);function De(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function ye(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){De(o,i,r,a,c,"next",n)}function c(n){De(o,i,r,a,c,"throw",n)}a(void 0)}))}}function Ie(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var be=function(){var n=function(){return new Promise((function(n,e){var t=indexedDB.open("LayoutAI_DB",1);t.onupgradeneeded=function(n){var e=n.target.result;e.objectStoreNames.contains("files")||e.createObjectStore("files",{keyPath:"id"})},t.onsuccess=function(){n(t.result)},t.onerror=function(){e(t.error)}}))},e=function(){var e=ye((function(e){var t,i,r,o,a;return Ie(this,(function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"CopyingBase64",data:e,type:"png"})).onsuccess=function(){je.log("House ID saved to IndexedDB")},o.onerror=function(n){je.error("Error saving House ID to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),je.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}}))}));return function(n){return e.apply(this,arguments)}}(),t=function(){var e=ye((function(e){var t,i,r,o,a;return Ie(this,(function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"DwgBase64",data:e,type:"dwg"})).onsuccess=function(){je.log("House ID saved to IndexedDB")},o.onerror=function(n){je.error("Error saving House ID to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),je.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}}))}));return function(n){return e.apply(this,arguments)}}(),i=function(){var e=ye((function(e){var t,i,r,o,a;return Ie(this,(function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"HouseId",data:e})).onsuccess=function(){je.log("House ID saved to IndexedDB")},o.onerror=function(n){je.error("Error saving House ID to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),je.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}}))}));return function(n){return e.apply(this,arguments)}}(),r=function(){var e=ye((function(e){var t,i,r,o,a;return Ie(this,(function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"HxInfo",data:e})).onsuccess=function(){je.log("House information saved to IndexedDB"),"2"===e.source?window.location.href="".concat(N.O9,"/editor/padmobile?mode=DwgBase64&from=local"):"3"===e.source?window.location.href="".concat(N.O9,"/editor/padmobile?mode=CopyingBase64&from=local"):"1"===e.source&&(window.location.href="".concat(N.O9,"/editor/padmobile?mode=HouseId&from=local"))},o.onerror=function(n){je.error("Error saving house information to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),je.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}}))}));return function(n){return e.apply(this,arguments)}}();return{saveImageToDB:e,saveDwgToDB:t,saveHouseIdToDB:i,saveInfoToDB:r}},Ae=t(65640);function we(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function ve(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){we(o,i,r,a,c,"next",n)}function c(n){we(o,i,r,a,c,"throw",n)}a(void 0)}))}}function ze(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var Se=function(n){var e=n.toSelectHX,t=zn().styles,r=(0,p.P)(),o=be(),a=(o.saveImageToDB,o.saveDwgToDB),c=o.saveInfoToDB,l=[{key:"search",title:"搜索户型图",description:"从户型库中搜索户型",img:"data:image/svg+xml;base64,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",styles:"#E1F2FF"},{key:"upload",title:"上传临摹图",description:"通过照片草图进行AI生成",img:"data:image/svg+xml;base64,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",styles:"#FFF6DE"}],s=function(n){return new Promise((function(e,t){var i=new FileReader;i.readAsDataURL(n),i.onload=function(){return e(i.result)},i.onerror=function(n){return t(n)}}))},d=(0,u.useCallback)((function(n){var e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=function(){var e=ve((function(e){var t,i,o,a;return ze(this,(function(c){switch(c.label){case 0:return(t=e.target.files).length>0?(i=t[0],[4,s(i)]):[3,2];case 1:o=c.sent(),g.nb.DispatchEvent(g.n0.LoadImitateImageFile,o),r.homeStore.setShowEnterPage({show:!1,source:""}),n||(a={houseTypeName:"未命名",source:"3",from:"myScheme"},Ae.log(a)),c.label=2;case 2:return[2]}}))}));return function(n){return e.apply(this,arguments)}}(),e.click()}),[]),M=(0,u.useCallback)((function(n){var e=document.createElement("input");e.type="file",e.accept=".dwg",e.onchange=function(){var e=ve((function(e){var t,i,r;return ze(this,(function(o){switch(o.label){case 0:return(t=e.target.files).length>0?(i=t[0],[4,s(i)]):[3,3];case 1:return r=o.sent(),[4,a(r)];case 2:o.sent(),n||c({houseTypeName:"未命名",source:"2",from:"myScheme"}),o.label=3;case 3:return[2]}}))}));return function(n){return e.apply(this,arguments)}}(),e.click()}),[]);return(0,i.jsx)("div",{className:t.cardContainer,children:l.map((function(n){return(0,i.jsx)(me,{style:{margin:20,cursor:"pointer"},className:t.card,onClick:(r=n.key,function(){"search"===r?e():"upload"===r?d(!1):"CAD"===r&&M(!1)}),bodyStyle:{padding:"15px 20px",height:"72px",background:n.styles},children:(0,i.jsxs)("div",{className:t.content,children:[(0,i.jsx)("div",{className:"",children:(0,i.jsx)("img",{src:n.img,alt:"img"})}),(0,i.jsxs)("div",{className:t.right,children:[(0,i.jsx)("div",{className:t.title,children:n.title}),(0,i.jsx)("div",{className:t.desc,children:n.description})]})]})},n.key);var r}))})},Te=t(40879);function Ee(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Oe(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Ee(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ee(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ce=(0,a.observer)((function(n){var e=n.toSelectHX,t=n.step,r=hn().styles,o=Oe((0,u.useState)("我的方案"),2),a=o[0],c=o[1],l=(0,p.P)();return(0,i.jsx)("div",{className:r.root,children:(0,i.jsxs)(Nn.LN,{title:(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",fontSize:24},children:[(0,i.jsx)("div",{className:"back",onClick:function(){U.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=N.O9},style:{display:l.userStore.isHaiEr&&-1===t?"none":(0,N.fZ)()||0!==t?"flex":"none"},children:(0,i.jsx)(en.A,{type:"icon-line_left"})}),(0,i.jsx)("span",{children:"开始设计"})]}),children:[(0,i.jsx)("div",{style:{padding:"0 0 20px 0"},children:(0,i.jsx)(Se,{toSelectHX:e})}),(0,i.jsxs)("div",{className:"displayContent",children:[(0,i.jsx)("div",{className:"tab",children:(0,i.jsx)(q.A,{options:["我的方案","我的图册"],onChange:function(n){c(n)}})}),(0,i.jsxs)("div",{className:r.display,children:["我的方案"===a&&(0,i.jsx)(Te.A,{source:"startDesignPage"}),"我的图册"===a&&(0,i.jsx)(O.A,{setZIndexOfMobileAtlas:null})]})]})]})})}));function Le(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function ke(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function Ue(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){ke(o,i,r,a,c,"next",n)}function c(n){ke(o,i,r,a,c,"throw",n)}a(void 0)}))}}function Qe(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Ye(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){Qe(n,e,t[e])}))}return n}function Pe(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function Ze(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||_e(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Be(n){return function(n){if(Array.isArray(n))return Le(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||_e(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _e(n,e){if(n){if("string"==typeof n)return Le(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Le(n,e):void 0}}function Ge(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var Fe=(0,a.observer)((function(){var n,e,t,o=(0,u.useRef)(null),a=(0,r.B)().t,c=$().styles,d=Ze((0,u.useState)(null),2),M=d[0],x=d[1],h=Ze((0,u.useState)([]),2),m=h[0],j=h[1],D=Ze((0,u.useState)(-1),2),y=D[0],I=D[1],b=Ze((0,u.useState)([{label:"居住人口",multiple:!1,tabList:[{label:"单身独居",selected:!1},{label:"二人世界",selected:!1},{label:"三口之家",selected:!1},{label:"四口之家",selected:!1},{label:"多代同堂",selected:!1}]},{label:"房屋类型",multiple:!1,tabList:[{label:"毛坯房",selected:!1},{label:"精装修",selected:!1},{label:"旧房改造",selected:!1}]},{label:"功能需求",multiple:!0,tabList:[{label:"聚会",selected:!1},{label:"品茗",selected:!1},{label:"健身",selected:!1},{label:"绿植",selected:!1},{label:"收纳",selected:!1},{label:"梳妆",selected:!1},{label:"休闲",selected:!1},{label:"西厨",selected:!1},{label:"宠物",selected:!1},{label:"办公",selected:!1},{label:"适老",selected:!1},{label:"孩童",selected:!1}]},{label:"装修预算",multiple:!1,tabList:[{label:"10万以下",selected:!1},{label:"10-20万",selected:!1},{label:"20-50万",selected:!1},{label:"50万以上",selected:!1}]}]),2),A=b[0],w=b[1],v=(0,p.P)(),z=Ze((0,u.useState)({orderBy:"sort asc",ruleType:(null===(n=v.userStore.userInfo)||void 0===n?void 0:n.isFactory)?2:1,pageSize:50,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),S=z[0],T=z[1],E=function(){var n=Ue((function(){var n,e;return Ge(this,(function(t){switch(t.label){case 0:return[4,(0,on.Ic)(S)];case 1:return(null==(n=t.sent())?void 0:n.result)?(e=Array.isArray(m)&&m.length>0&&S.pageIndex>1?Be(m).concat(Be((null==n?void 0:n.result)||[])):(null==n?void 0:n.result)||[],j(e),1!==y||M||x(e[0])):j([]),[2]}}))}));return function(){return n.apply(this,arguments)}}(),O=function(){var n=Ue((function(){var n;return Ge(this,(function(e){switch(e.label){case 0:return[4,(0,an.L7)("image/*").catch((function(){return null}))];case 1:return(n=e.sent()).content?(v.homeStore.setImgBase64(n.content),l.A.success(a("上传户型图成功")),I(1)):l.A.warning(a("上传户型图失败")),[2]}}))}));return function(){return n.apply(this,arguments)}}();(0,u.useEffect)((function(){g.nb.on_M(f.U.xmlSchemeLoaded,"enterPage",(function(n){if("Finish"===n.mode&&M){g.nb.DispatchEvent(g.n0.SeriesSampleSelected,{series:M,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}});var e="";A.forEach((function(n){n.tabList.forEach((function(n){n.selected&&(e+=n.label+",")}))})),e=e.slice(0,-1),g.nb.instance.layout_container._funcRequire=e,x(null),v.trialStore.setHouseData(null),g.nb.off(f.U.xmlSchemeLoaded)}v.homeStore.setRoomEntites(g.nb.instance.layout_container._room_entities)}))}),[M,A]);var C=function(){0===y&&I(-1),1===y&&I(0)};(0,u.useEffect)((function(){"HouseId"===N.Zx&&I(1)}),[]),(0,u.useEffect)((function(){1===y&&E()}),[S,y]),(0,u.useEffect)((function(){var n,e;if(0===y&&v.userStore.isHaiEr)return g.nb.DispatchEvent(g.n0.PostBuildingId,{id:v.trialStore.houseData.id,name:""}),void v.homeStore.setShowEnterPage({show:!1,source:""});0===y&&(null===(e=v.trialStore)||void 0===e||null===(n=e.houseData)||void 0===n?void 0:n.id)&&(I(1),v.homeStore.setImgBase64(null))}),[null===(t=v.trialStore)||void 0===t||null===(e=t.houseData)||void 0===e?void 0:e.num,v.userStore.isHaiEr]),(0,u.useEffect)((function(){v.userStore.isHaiEr?I(-1):I(0)}),[v.userStore.isHaiEr]);return(0,i.jsxs)("div",{className:c.enterPage,children:[-1===y&&(0,i.jsx)(Ce,{toSelectHX:function(){I(0)},step:y}),(0,i.jsxs)("div",{className:c.hxHeader,children:[(0,i.jsxs)("div",{className:"title",children:[(0,i.jsx)("div",{className:"back",onClick:C,children:(0,i.jsx)(en.A,{type:"icon-line_left"})}),(0,i.jsx)(nn.If,{condition:0===y,children:(0,i.jsx)("span",{children:a("找户型")})}),(0,i.jsx)(nn.If,{condition:1===y,children:(0,i.jsx)("span",{children:a("选需求")})})]}),(0,i.jsxs)("div",{style:{display:v.userStore.isHaiEr?"none":"block"},children:[(0,i.jsxs)(s.A,{type:"primary",className:"mySchemeButton",color:"orange",variant:"filled",onClick:function(){v.homeStore.setShowMySchemeList(!0),x(null)},children:[(0,i.jsx)(cn.In,{iconClass:"iconwenjianjia",style:{fontSize:"12px"}}),(0,i.jsx)("div",{style:{color:"rgba(0, 0, 0, 0.8)"},children:"我的方案"})]}),(0,i.jsxs)(s.A,{type:"primary",className:"myAtlasButton",color:"purple",variant:"filled",onClick:function(){v.homeStore.setShowAtlas(!0)},children:[(0,i.jsx)(cn.In,{iconClass:"icontuce1",style:{fontSize:"12px"}}),(0,i.jsx)("div",{style:{color:"rgba(0, 0, 0, 0.8)"},children:"我的图册"})]})]})]}),(0,i.jsxs)("div",{className:"upload_hx",onClick:O,style:{display:0!==y||v.userStore.isHaiEr?"none":"block"},children:[(0,i.jsx)("img",{style:{marginTop:"20px",width:"50px",height:"auto"},src:"https://3vj-fe.3vjia.com/layoutai/icons/upload.svg",alt:""}),(0,i.jsx)("div",{className:"upload_title",children:"上传户型"})]}),(0,i.jsx)(rn.A,{in:0===y,timeout:300,classNames:1===y?"slide-reverse":"slide",mountOnEnter:!0,appear:!0,style:{display:0===y?"block":"none"},children:(0,i.jsx)("div",{className:c.selectHx,children:(0,i.jsx)("div",{className:c.hxRoot,children:(0,i.jsx)(tn.A,{})})})}),(0,i.jsx)(rn.A,{in:1===y,timeout:300,classNames:0===y?"slide":"slide-reverse",mountOnEnter:!0,appear:!0,style:{display:1===y?"block":"none"},children:function(){var n,e;return(0,i.jsxs)("div",{className:c.selectDemand,children:[A.map((function(n,e){return(0,i.jsxs)("div",{className:"demandItem",children:[(0,i.jsx)("div",{className:"demandLabel",children:n.label},e),(0,i.jsx)("div",{className:"tabRoot",children:n.tabList.map((function(e,t){return(0,i.jsx)("div",{onClick:function(){return t=e,i=n.label,r=A.map((function(n){return n.label===i?Pe(Ye({},n),{tabList:n.tabList.map((function(e){return n.multiple?Pe(Ye({},e),{selected:e.label===t.label?!e.selected:e.selected}):Pe(Ye({},e),{selected:e.label===t.label})}))}):n})),void w(r);var t,i,r},className:"demandtab"+(e.selected?" selected":""),children:e.label},t)}))})]})})),(0,i.jsxs)("div",{className:c.styleTitle,children:[(0,i.jsxs)("div",{className:"demandLabel",children:[a("风格偏好"),(0,i.jsxs)("span",{style:{color:"#959598",fontSize:"12px"},children:["（",a("必选"),"）"]})]}),(0,i.jsx)(q.A,{onChange:function(){var n=Ue((function(n){return Ge(this,(function(e){return T((function(e){return Pe(Ye({},e),{pageIndex:1,ruleType:"平台"===n?1:2})})),x(null),[2]}))}));return function(e){return n.apply(this,arguments)}}(),defaultValue:(null===(n=v.userStore.userInfo)||void 0===n?void 0:n.isFactory)?"企业":"平台",options:sn.x.instance.hasPermission(ln.J.SERIES.SHOW_PLATFORM_SERIES)?["平台","企业"]:["企业"]})]}),(0,i.jsx)("div",{className:"".concat(c.container_listInfo),ref:o,children:(0,i.jsx)(i.Fragment,{children:null==m||null===(e=m.map)||void 0===e?void 0:e.call(m,(function(n,e){return(0,i.jsx)("div",{className:c.container_list,onClick:function(){x(n)},children:(0,i.jsxs)("div",{style:{border:(null==M?void 0:M.ruleId)===n.ruleId?"2px solid #9242FB":"2px solid #fff",borderRadius:"8px",overflow:"hidden"},children:[(0,i.jsx)("img",{src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(0,i.jsx)("div",{className:c.container_title,title:n.seedSchemeName||n.ruleName,children:a(n.seedSchemeName)||a(n.ruleName)})]})},"series_"+e)}))})})]})}()}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(nn.If,{condition:0===y}),(0,i.jsx)(nn.If,{condition:1===y,children:(0,i.jsxs)("div",{className:c.bottom,children:[(0,i.jsx)("div",{className:"bottomLeft",children:(0,i.jsxs)("div",{className:"rotate",onClick:function(){x(null),w(A.map((function(n){return Pe(Ye({},n),{tabList:n.tabList.map((function(n){return Pe(Ye({},n),{selected:!1})}))})})))},children:[(0,i.jsx)(en.A,{type:"icon-rotate",style:{marginRight:"5px"}}),a("重置选项")]})}),(0,i.jsxs)("div",{className:"bottomRight",children:[(0,i.jsx)(s.A,{style:{marginRight:"10px"},onClick:function(){return I(0)},color:"default",variant:"filled",children:a("上一步")}),(0,i.jsx)(s.A,{style:{background:"linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%)",color:"#fff"},onClick:function(){M?(I(0),v.homeStore.setShowEnterPage({show:!1,source:""}),v.homeStore.img_base64?g.nb.DispatchEvent(g.n0.LoadImitateImageFile,v.homeStore.img_base64):v.trialStore.houseData.id&&g.nb.DispatchEvent(g.n0.PostBuildingId,{id:v.trialStore.houseData.id,name:""})):l.A.warning(a("请选择风格偏好"))},color:"default",variant:"filled",children:a("下一步")})]})]})})]})]})}));function Re(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function He(){var n=Re(["\n      background-color: #fff;\n      border-radius: 8px !important;\n      position: relative;\n      .swj-baseComponent-Containersbox-title\n      {\n        background-color: #fff !important;\n      }\n    "]);return He=function(){return n},n}function We(){var n=Re(["\n      .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell\n      {\n        background-color: #fff !important;\n      }\n    "]);return We=function(){return n},n}function Ve(){var n=Re(['\n      color: #282828;\n      font-family: "PingFang SC";\n      font-size: 16px;\n      font-style: normal;\n      font-weight: 600;\n    ']);return Ve=function(){return n},n}function Xe(){var n=Re(["\n      display: flex;\n      flex-direction: row;\n      gap: 10px;\n      margin: 16px 0px;\n      color: #282828;\n      .tabItem\n      {\n        display: flex;\n        width: 64px;\n        height: 28px;\n        padding: 0px 0px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n      }\n      .selected\n      {\n        background: #F2F3F4;\n        font-weight: 600;\n      }\n    "]);return Xe=function(){return n},n}function Je(){var n=Re(["\n      .ant-table-thead .ant-table-selection-column .ant-checkbox-wrapper\n      {\n         display: none;\n      }\n      .ant-table-thead .ant-table-cell\n      {\n        background: #F2F3F4;\n      }\n      .ant-table-container\n      {\n        border: 2px solid #F2F3F4;\n      }\n    "]);return Je=function(){return n},n}function Ke(){var n=Re(["\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      position: absolute;\n      bottom: 0;\n      width: 100%;\n      padding: 16px;\n      background-color: #fff;\n      width: 95%;\n      font-size: 16px;\n    "]);return Ke=function(){return n},n}function $e(){var n=Re(["\n      font-size: 16px;\n      font-weight: 600;\n    "]);return $e=function(){return n},n}var qe=(0,Q.rU)((function(n){var e=n.css;return{panelContainer:e(He()),content:e(We()),title:e(Ve()),tab:e(Xe()),table:e(Je()),bottom:e(Ke()),bottomRight:e($e())}})),nt=t(56980),et=t(70766);function tt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function it(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function rt(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){it(n,e,t[e])}))}return n}function ot(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function at(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return tt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return tt(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ct=(0,a.observer)((function(){var n,e,t,o,a=(0,p.P)(),c=((0,r.B)().t,qe().styles),l=at((0,u.useState)("checkbox"),2),s=l[0],d=(l[1],at((0,u.useState)([]),2)),M=d[0],f=d[1],x=at((0,u.useState)([]),2),h=x[0],N=x[1],m=at((0,u.useState)("延米"),2),j=m[0],D=m[1],y=at((0,u.useState)(0),2),I=y[0],b=y[1],A=at((0,u.useState)([{name:"定制柜",selected:!1},{name:"橱柜",selected:!1},{name:"卫阳",selected:!1}]),2),w=A[0],v=A[1],z=[{title:"类型",dataIndex:"name"},{title:"尺寸",dataIndex:"size"},{title:(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer"},onClick:function(){D("延米"===j?"投影面积":"延米")},children:[(0,i.jsx)(nn.If,{condition:null===(n=w.find((function(n){return"定制柜"===n.name})))||void 0===n?void 0:n.selected,children:(0,i.jsx)(nn.al,{children:"投影面积"})}),(0,i.jsx)(nn.If,{condition:null===(e=w.find((function(n){return"橱柜"===n.name})))||void 0===e?void 0:e.selected,children:(0,i.jsx)(nn.al,{children:"延米"})}),(0,i.jsx)(nn.If,{condition:null===(t=w.find((function(n){return"卫阳"===n.name})))||void 0===t?void 0:t.selected,children:(0,i.jsxs)(nn.al,{children:[(0,i.jsx)(en.A,{type:"icon-change_logo"}),j]})})]}),dataIndex:"meter",render:function(n,e){return(0,i.jsx)("span",{children:"延米"===j?e.meter:e.area})}},{title:"空间",dataIndex:"space",hidden:null===(o=w.find((function(n){return"橱柜"===n.name})))||void 0===o?void 0:o.selected}],S={onChange:function(n,e){f(n)},getCheckboxProps:function(n){return{disabled:"Disabled User"===n.name,name:n.name}}};return(0,u.useEffect)((function(){var n,e=w.find((function(n){return n.selected})),t=g.nb.instance.layout_container;if(N([]),0!=(null==t||null===(n=t._room_entities)||void 0===n?void 0:n.length)){if("橱柜"===(null==e?void 0:e.name)){var i,r;D("延米");var o=null===(i=t._room_entities.find((function(n){return"厨房"===n.name})))||void 0===i?void 0:i._room;if(o&&(null===(r=o._furniture_list)||void 0===r?void 0:r.length)>0){var a=[];o._furniture_list.forEach((function(n){if(n.category.endsWith("柜")){var e=n.matched_rect?n.matched_rect:n.rect;a.push({key:n.uuid,name:n.category,size:"".concat(e._w,"*").concat(e._h,"*").concat(e.rect_center_3d.z||n.height),meter:"".concat((e._w/1e3).toFixed(2),"m"),space:o.name,area:"".concat((e._w*(e.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})}})),N(a)}}if("卫阳"===(null==e?void 0:e.name)){var c=t._room_entities.filter((function(n){if(n.name.includes("卫生间")||n.name.includes("阳台"))return n}));if(c&&c.length>0){var l=[];c.forEach((function(n){var e;(null===(e=n._room._furniture_list)||void 0===e?void 0:e.length)>0&&n._room._furniture_list.forEach((function(n){if(n.category.endsWith("柜")){var e=n.matched_rect?n.matched_rect:n.rect;l.push({key:n.uuid,name:n.category,size:"".concat(Math.round(e._w),"*").concat(Math.round(e._h),"*").concat(Math.round(e.rect_center_3d.z||n.height)),meter:"".concat((e._w/1e3).toFixed(2),"m"),space:n._room.name,area:"".concat((e._w*Math.round(e.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})}}))})),N(l)}}if("定制柜"===(null==e?void 0:e.name)){D("投影面积");var s=t._room_entities.filter((function(n){if(!n.name.includes("卫生间")&&!n.name.includes("阳台")&&!n.name.includes("厨房"))return n}));if(s&&s.length>0){var u=[];s.forEach((function(n){var e;(null===(e=n._room._furniture_list)||void 0===e?void 0:e.length)>0&&n._room._furniture_list.forEach((function(n){n.category.endsWith("柜")&&u.push({key:n.uuid,name:n.category,size:"".concat(Math.round(n.rect._w),"*").concat(Math.round(n.rect._h),"*").concat(Math.round(n.rect.rect_center_3d.z||n.height)),meter:"".concat((n.rect._w/1e3).toFixed(2),"m"),space:n._room.name,area:"".concat((n.rect._w*Math.round(n.rect.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})}))})),N(u)}}f([]),b(I+1)}}),[w]),(0,u.useEffect)((function(){a.homeStore.showCabinetCompute&&v(w.map((function(n,e){return ot(rt({},n),{selected:0===e})})))}),[a.homeStore.showCabinetCompute]),(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(nn.If,{condition:a.homeStore.showCabinetCompute,children:(0,i.jsx)(cn._w,{center:!0,height:446,width:600,showHeader:!0,showCloseInContainerbox:!1,title:"基本算量",className:c.panelContainer,onClose:function(){a.homeStore.setShowCabinetCompute(!1)},draggable:!0,children:(0,i.jsxs)("div",{style:{padding:"0 20px 20px 20px"},className:c.content,children:[(0,i.jsx)("div",{className:c.tab,children:w.map((function(n,e){return(0,i.jsx)("div",{onClick:function(){v(w.map((function(n,t){return ot(rt({},n),{selected:t===e})})))},className:"tabItem"+(n.selected?" selected":""),children:n.name},e)}))}),(0,i.jsx)(nt.A,{rowSelection:rt({type:s,columnTitle:"",selectedRowKeys:M},S),columns:z,dataSource:h,pagination:!1,scroll:{y:240},className:c.table},I),(0,i.jsxs)("div",{className:c.bottom,children:[(0,i.jsx)("div",{children:(0,i.jsx)(et.A,{checked:M.length===h.length,indeterminate:M.length>0&&M.length<h.length,onChange:function(n){n.target.checked?f(h.map((function(n){return n.key}))):f([])},children:"全选"})}),(0,i.jsx)("div",{className:c.bottomRight,children:(0,i.jsxs)(nn.If,{condition:"延米"===j,children:[(0,i.jsxs)(nn.al,{children:["共 ",h.filter((function(n){return M.includes(n.key)})).reduce((function(n,e){return n+parseFloat(e.meter)}),0).toFixed(2),"m"]}),(0,i.jsxs)(nn._I,{children:["共 ",h.filter((function(n){return M.includes(n.key)})).reduce((function(n,e){return n+parseFloat(e.area)}),0).toFixed(2),"m²"]})]})})]})]})})})})})),lt=t(67869);function st(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ut(){var n=st(["\n      position: fixed;\n      background: #fff;\n      z-index: 10;\n      transition: all 0.3s ease-in-out;\n      .closeBtn {\n        display: none;\n        position: absolute;\n        right: 6px;\n        top: 6px;\n        font-size: 20px;\n        width: 60px;\n        height: 24px;\n        text-align: right;\n      }\n      &.panel_hide {\n        box-shadow: 0px 0px 0px 0px #00000000;\n      }\n      @media screen and (orientation: landscape) {\n        position: fixed;\n        left: 12px;\n        top: 52px;\n        bottom: 12px;\n        right: auto;\n        height: auto;\n        padding-left: 0 !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        max-width: 224px;\n        width: 224px;\n        border-radius: 8px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateX(calc(-100% - 12px));\n        }\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        left: 0;\n        bottom: 0px;\n        right: 0;\n        width: auto;\n        height: 340px;\n        max-width: auto;\n        max-height: 340px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 8px 8px 0px 0px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateY(100%);\n        }\n        .closeBtn {\n          display: block;\n        }\n      }\n    "]);return ut=function(){return n},n}function dt(){var n=st(["\n      height: 60px;\n      width: 100%;\n      @media screen and (orientation: landscape) {\n        height: 55px;\n        width: 100%;\n        color: #000;\n        font-weight: 700;\n        padding: 12px 13px 0;\n      }\n      @media screen and (orientation: portrait) {\n        width: 30%;\n        margin: 10px;\n        height: auto;\n      }\n    "]);return dt=function(){return n},n}function gt(){var n=st(["\n      display: none;\n      width: 20px;\n      height: 48px;\n      line-height: 48px;\n      text-align: center;\n      background-color: #fff;\n      border-radius: 0px 6px 6px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      cursor: pointer;\n      transition: all 0.3s ease-in-out;\n\n      @media screen and (orientation: landscape) {\n        display: block;\n        position: fixed;\n        left: 235px;\n        top: calc(50% - 48px);\n        z-index: 9;\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        bottom: 120px;\n        left: 0px;\n        z-index: 999;\n      }\n      &.panel_hide {\n        left: 0px;\n        display: block;\n      }\n    "]);return gt=function(){return n},n}function Mt(){var n=st(["\n      padding: 0 12px;\n      height: calc(100% - 60px);\n      overflow: hidden;\n    "]);return Mt=function(){return n},n}function pt(){var n=st(["\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 12px;\n      padding: 4px 0;\n      overflow-y: auto;\n      scrollbar-width: none;  /* Firefox */\n      -ms-overflow-style: none;  /* IE and Edge */\n      &::-webkit-scrollbar {\n        display: none;  /* Chrome, Safari and Opera */\n      }\n    "]);return pt=function(){return n},n}function ft(){var n=st(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 6px 4px;\n      cursor: pointer;\n      transition: all 0.3s;\n      user-select: none;\n    "]);return ft=function(){return n},n}function xt(){var n=st(["\n      font-size: 50px;\n      height: 50px;\n      width: 70px;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #666;\n      background: #F5F5F5;\n      -webkit-user-drag: none;\n      -khtml-user-drag: none;\n      -moz-user-drag: none;\n      -o-user-drag: none;\n      user-drag: none;\n      \n      img {\n        max-width: 50px;\n        max-height: 40px;\n        object-fit: contain;\n        -webkit-user-drag: none;\n        -khtml-user-drag: none;\n        -moz-user-drag: none;\n        -o-user-drag: none;\n        user-drag: none;\n        pointer-events: none;\n      }\n    "]);return xt=function(){return n},n}function ht(){var n=st(["\n      font-size: 14px;\n      color: #333;\n      height: 30px;\n      line-height: 30px;\n      text-align: center;\n    "]);return ht=function(){return n},n}var Nt=(0,Q.rU)((function(n){var e=n.css;return{leftPanelRoot:e(ut()),tabBar:e(dt()),collapseBtn:e(gt()),popupContainer:e(Mt()),itemGrid:e(pt()),gridItem:e(ft()),itemIcon:e(xt()),itemLabel:e(ht())}})),mt=t(17830),jt=t(48402);function Dt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function yt(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Dt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Dt(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var It=(0,a.observer)((function(n){var e=n.isCollapse,t=n.onCollapseChange,o=(0,r.B)().t,a=Nt().styles,c=yt((0,u.useState)("doors"),2),l=c[0],s=c[1],d=bt(),M=d.doorWindowItems,p=d.structureItems,f="https://3vj-fe.3vjia.com/layoutai/figures_imgs/";(0,u.useEffect)((function(){var n=g.nb.instance;n&&n.addHouseLeftPanelEvent()}),[]);var x=[{value:"doors",label:o("门窗")},{value:"structure",label:o("结构件")}],h=function(n){g.nb.DispatchEvent(g.n0.SelectedFurniture,n)},N=!e;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{id:"pad_house_left_panel",className:a.leftPanelRoot+" leftPanelRoot "+(N?"":"panel_hide"),children:[N&&(0,i.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return t(!0)}}),N&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:a.tabBar,children:(0,i.jsx)("div",{className:"tab-content",children:(0,i.jsx)(q.A,{value:l,onChange:function(n){return s(n.toString())},block:!0,options:x})})}),(0,i.jsx)("div",{className:a.popupContainer,children:(0,i.jsx)("div",{className:a.itemGrid,children:"doors"===l?M.map((function(n,e){return(0,i.jsxs)("div",{className:a.gridItem,children:[(0,i.jsx)("div",{className:a.itemIcon,onPointerDown:function(e){h(n.label)},children:(0,i.jsx)(mt.A,{src:"".concat(f).concat(n.image),preview:!1,title:n.label,alt:n.label})}),(0,i.jsx)("div",{className:a.itemLabel,children:o(n.label)})]},e)})):p.map((function(n,e){return(0,i.jsxs)("div",{className:a.gridItem,children:[(0,i.jsx)("div",{className:a.itemIcon,onPointerDown:function(e){h(n.label)},onPointerUp:function(e){g.nb.DispatchEvent(g.n0.mobileAddFurniture,n.label)},children:(0,i.jsx)(mt.A,{src:"".concat(f).concat(n.image),preview:!1,title:n.label,alt:n.label})}),(0,i.jsx)("div",{className:a.itemLabel,children:o(n.label)})]},e)}))})})]})]}),(0,i.jsx)("div",{className:a.collapseBtn+(N?" iconfont iconfill_left":" panel_hide iconfont iconfill_right"),onClick:function(){return t(!e)}})]})})),bt=function(){return(0,u.useMemo)((function(){var n=jt.V.find((function(n){return"户型"===n.label}));if(!n||!n.child)return{doorWindowItems:[],structureItems:[]};var e=n.child.find((function(n){return"结构件"===n.label}));if(!e||!e.figureList)return{doorWindowItems:[],structureItems:[]};var t=[],i=[];return e.figureList.forEach((function(n){var e,r={icon:n.icon||(null===(e=n.image)||void 0===e?void 0:e.split(".")[0])||"",label:n.title,type:At(n.label)?"Door":"StructureEntity",realType:n.label,image:n.image};At(n.label)?t.push(r):i.push(r)})),{doorWindowItems:t,structureItems:i}}),[])},At=function(n){return["Door","Window","door","window","Railing"].some((function(e){return n.includes(e)}))};function wt(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function vt(){var n=wt(["\n      position: fixed;\n      background: #fff;\n      z-index: 10;\n      transition: all 0.3s ease-in-out;\n      .closeBtn {\n        display: none;\n        position: absolute;\n        right: 6px;\n        top: 6px;\n        font-size: 20px;\n        width: 60px;\n        height: 24px;\n        text-align: right;\n      }\n      &.panel_hide {\n        box-shadow: 0px 0px 0px 0px #00000000;\n      }\n      @media screen and (orientation: landscape) {\n        position: fixed;\n        left: 12px;\n        top: 52px;\n        bottom: 12px;\n        right: auto;\n        height: auto;\n        padding-left: 0 !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        max-width: 224px;\n        width: 224px;\n        border-radius: 8px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateX(calc(-100% - 12px));\n        }\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        left: 0;\n        bottom: 0px;\n        right: 0;\n        width: auto;\n        height: 340px;\n        max-width: auto;\n        max-height: 340px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 8px 8px 0px 0px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateY(100%);\n        }\n        .closeBtn {\n          display: block;\n        }\n      }\n    "]);return vt=function(){return n},n}function zt(){var n=wt(["\n      display: none;\n      width: 20px;\n      height: 48px;\n      line-height: 48px;\n      text-align: center;\n      background-color: #fff;\n      border-radius: 0px 6px 6px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      cursor: pointer;\n      transition: all 0.3s ease-in-out;\n\n      @media screen and (orientation: landscape) {\n        display: block;\n        position: fixed;\n        left: 235px;\n        top: calc(50% - 48px);\n        z-index: 9;\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        bottom: 120px;\n        left: 0px;\n        z-index: 999;\n      }\n      &.panel_hide {\n        left: 0px;\n        display: block;\n      }\n    "]);return zt=function(){return n},n}function St(){var n=wt(["\n      padding: 12px;\n      height: calc(100% - 40px);\n      overflow-y: auto;\n      &::-webkit-scrollbar {\n        width: 4px;\n      }\n      &::-webkit-scrollbar-track {\n        background: #f1f1f1;\n      }\n      &::-webkit-scrollbar-thumb {\n        background: #888;\n        border-radius: 2px;\n      }\n    "]);return St=function(){return n},n}function Tt(){var n=wt(["\n      font-size: 16px;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 16px;\n      padding-bottom: 8px;\n      border-bottom: 1px solid #e8e8e8;\n    "]);return Tt=function(){return n},n}var Et=(0,Q.rU)((function(n){var e=n.css;return{leftPanelRoot:e(vt()),collapseBtn:e(zt()),propertyContainer:e(St()),propertyTitle:e(Tt())}})),Ot=t(26769),Ct=t(32184),Lt=(0,a.observer)((function(n){var e=n.isCollapse,t=n.onCollapseChange,r=(0,p.P)(),o=Et().styles,a=!e,c=r.homeStore.selectEntity;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:o.leftPanelRoot+" leftPanelRoot "+(a?"":"panel_hide"),children:a&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return t(!0)}}),(0,i.jsx)("div",{className:o.propertyContainer,children:c&&function(n){if(!n)return!1;var e=n.type;return e===Ct.Fz.Door||e===Ct.Fz.Window||e===Ct.Fz.StructureEntity||e===Ct.Fz.Wall||e===Ct.Fz.RoomArea}(c)&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:o.propertyTitle,children:function(n){if(!n)return"";var e=n.type;return e===Ct.Fz.Door?"门信息":e===Ct.Fz.Window?"窗信息":e===Ct.Fz.StructureEntity?"结构件信息":e===Ct.Fz.Wall?"墙体信息":e===Ct.Fz.RoomArea?"空间信息":""}(c)}),(0,i.jsx)(Ot.A,{Entity:c})]})})]})}),(0,i.jsx)("div",{className:o.collapseBtn+(a?" iconfont iconfill_left":" panel_hide iconfont iconfill_right"),onClick:function(){return t(!e)}})]})}));function kt(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Ut(){var n=kt(["\n        "]);return Ut=function(){return n},n}function Qt(){var n=kt(["  \n        position:fixed;\n        right:0;\n        z-index:9;\n        top:0px;\n        transition: all 0.2s ease;\n    "]);return Qt=function(){return n},n}function Yt(){var n=kt(["\n        position:fixed;\n        top:0;\n        z-index:9;\n        display:flex;\n        justify-content:center;\n        align-items:center;\n        height:34px;\n        background:#fff;\n        width: 140px;\n        left: 50%;\n        transform: translateX(-50%);\n        border-radius: 6px;\n        border: 2px solid #E5E5E5;\n        font-size: 14px;\n    "]);return Yt=function(){return n},n}function Pt(){var n=kt(["\n        position:fixed;\n        bottom:13px;\n        z-index:9;\n        display:flex;\n        justify-content:center;\n        align-items:center;\n        left: 50%;\n        transform: translate(-50%, 0);\n        transition: all 0.5s ease;\n        .btn {\n            border-radius: 50px;\n            background: #FFFFFF;\n            box-shadow: 0px 6px 20px 0px #00000014;\n            width : 140px;\n            border: none;\n            height : 48px;\n            color: #282828;\n            font-size: 14px;\n            line-height: 48px;\n            letter-spacing: 0px;\n            text-align: center;\n            margin-left:12px;\n            margin-right:12px;\n        }\n\n        @media screen and (orientation: landscape){\n            display:flex;\n            justify-content:center;\n            align-items:center;\n            left: 50%;\n            transform: translate(-50%, 0);\n        }\n        "]);return Pt=function(){return n},n}var Zt=(0,Q.rU)((function(n){var e=n.css;return{padHousePanelRoot:e(Ut()),sideToolbarContainer:e(Qt()),topBar:e(Yt()),bottomButtons:e(Pt())}})),Bt=t(23184),_t=t(6934),Gt=t(34577);function Ft(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Rt(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Ft(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ft(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ht=(0,a.observer)((function(){var n="PadHousePanel",e=(0,p.P)(),t=Rt((0,u.useState)(!1),2),r=t[0],o=t[1],a=Zt().styles,c=Rt((0,u.useState)("menu"),2),l=c[0],s=c[1];return(0,u.useEffect)((function(){return g.nb.on_M(Gt.$.showPopup,n,(function(n){s("attribute"===n?"attribute":"menu")})),g.nb.on_M(f.U.SelectingTarget,n,(function(){s("menu")})),function(){g.nb.off_M(Gt.$.showPopup,n),g.nb.off_M(f.U.SelectingTarget,n)}}),[]),(0,i.jsxs)("div",{className:a.padHousePanelRoot,children:[(0,i.jsx)(nn.If,{condition:"menu"===l,children:(0,i.jsx)(It,{isCollapse:r,onCollapseChange:o})}),(0,i.jsx)(nn.If,{condition:"attribute"===l,children:(0,i.jsx)(Lt,{isCollapse:r,onCollapseChange:o})}),(0,i.jsxs)("div",{className:a.sideToolbarContainer,children:[(0,i.jsx)(Bt.A,{}),(0,i.jsx)(_t.A,{})]}),(0,i.jsx)("div",{className:a.topBar,children:"户型编辑模式"}),(0,i.jsx)("div",{className:a.bottomButtons,children:(0,i.jsx)("div",{className:"btn",onClick:function(){g.nb.instance._current_handler_mode=M.f.AiCadMode,g.nb.RunCommand(M.f.AiCadMode),e.homeStore.setDesignMode(M.f.AiCadMode)},children:"进入布局"})})]})})),Wt=t(65640);function Vt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Xt(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function Jt(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){Xt(o,i,r,a,c,"next",n)}function c(n){Xt(o,i,r,a,c,"throw",n)}a(void 0)}))}}function Kt(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Vt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Vt(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $t(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var qt=(0,a.observer)((function(){var n=(0,r.B)().t,e=(0,o.A)().styles,t=Kt((0,u.useState)(""),2),a=t[0],d=t[1],Q=Kt((0,u.useState)(-2),2),Y=Q[0],P=Q[1],Z=(0,p.P)(),B=c.A.confirm,_="SaveSchemeProgress",G=Kt(l.A.useMessage(),2),F=G[0],R=G[1],H=Kt((0,u.useState)(!1),2),W=H[0],V=(H[1],Kt((0,u.useState)(!1),2)),X=V[0],J=V[1],K=Kt((0,u.useState)(null),2),$=K[0],q=K[1],en=Kt((0,u.useState)(""),2),tn=en[0],rn=en[1],on=Kt((0,u.useState)(-2),2),an=(on[0],on[1]),ln=Kt((0,u.useState)(0),2),sn=ln[0],un=(ln[1],(0,u.useRef)()),dn=Kt((0,u.useState)(null),2),gn=dn[0],Mn=dn[1];g.nb.UseApp(M.e.AppName),g.nb.instance&&(g.nb.t=n);var pn=function(){g.nb.instance&&(g.nb.instance.bindCanvas(document.getElementById("cad_canvas")),g.nb.instance.update()),fn()},fn=function(){g.nb.instance&&(g.nb.instance._is_landscape=window.innerWidth>window.innerHeight);Z.homeStore.IsLandscape;Z.homeStore.setIsLandscape(window.innerWidth>window.innerHeight),document.documentElement.style.setProperty("--vh","".concat(.01*window.innerHeight,"px"))},xn=function(){var n=Jt((function(){var n,e,t;return $t(this,(function(i){switch(i.label){case 0:return N.uN?(n={isDelete:0,pageIndex:1,pageSize:9,keyword:N.uN},[4,b.D.getLayoutSchemeList(n)]):[2];case 1:return e=i.sent(),t=e.layoutSchemeDataList,e.total,t&&(g.nb.DispatchEvent(g.n0.OpenMyLayoutSchemeData,t[0]),g.nb.emit(f.U.OpenHouseSearching,!1)),[2]}}))}));return function(){return n.apply(this,arguments)}}(),hn=function(){var n=Jt((function(){var n,e,t,i;return $t(this,(function(r){switch(r.label){case 0:return"HouseId"!==N.Zx?[3,1]:(Jt((function(){var n,e,t;return $t(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,(0,A.ZN)("HouseId")];case 1:return n=i.sent(),Wt.log(n),e=n.data,(0,N.fZ)()?Z.trialStore.houseData.id=e:g.nb.DispatchEvent(g.n0.PostBuildingId,{id:e,name:""}),[3,3];case 2:return t=i.sent(),Wt.error("Error loading file:",t),[3,3];case 3:return[2]}}))}))(),[3,9]);case 1:return"DwgBase64"!==N.Zx?[3,2]:(Jt((function(){return $t(this,(function(n){try{g.nb.RunCommand(g._I.OpenDwgFilefromWork)}catch(n){Wt.error("Error loading file:",n)}return[2]}))}))(),[3,9]);case 2:return"CopyingBase64"!==N.Zx?[3,3]:(Jt((function(){var n,e,t;return $t(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,(0,A.ZN)("CopyingBase64")];case 1:return n=i.sent(),e=n.data,Z.homeStore.setImgBase64(e),(0,N.fZ)()?Wt.log("fileData",e):g.nb.DispatchEvent(g.n0.LoadImitateImageFile,e),[3,3];case 2:return t=i.sent(),Wt.error("Error loading file:",t),[3,3];case 3:return[2]}}))}))(),[3,9]);case 3:return"hxcreate"!==N.Zx?[3,6]:N.fW?[4,(0,w.ON)({id:N.fW})]:[3,5];case 4:(n=r.sent()).result.contentUrl=n.result.dataUrl,g.nb.DispatchEvent(g.n0.OpenMyLayoutSchemeData,n.result),g.nb.DispatchEvent(g.n0.autoSave,null),r.label=5;case 5:return[3,9];case 6:return"hxedit"!==N.Zx?[3,9]:N.vu?[4,(0,w.ON)({id:N.vu})]:[3,8];case 7:(e=r.sent()).success&&e.result&&e.result.dataUrl&&(e.result.contentUrl=e.result.dataUrl,g.nb.DispatchEvent(g.n0.OpenMyLayoutSchemeData,e.result)),r.label=8;case 8:g.nb.instance&&"SingleRoom"==(null===(i=g.nb.instance)||void 0===i||null===(t=i.layout_container)||void 0===t?void 0:t._drawing_layer_mode)&&g.nb.DispatchEvent(g.n0.leaveSingleRoomLayout,{}),g.nb.instance._current_handler_mode=M.f.HouseDesignMode,g.nb.RunCommand(M.f.HouseDesignMode),Z.homeStore.setDesignMode(M.f.HouseDesignMode),r.label=9;case 9:return[2]}}))}));return function(){return n.apply(this,arguments)}}();return(0,u.useEffect)((function(){if(g.nb.instance&&(g.nb.instance._is_website_debug=N.iG),window.addEventListener("resize",pn),pn(),g.nb.instance){var e;if(g.nb.instance.initialized||(!(0,N.fZ)()||"HouseId"!==N.Zx&&"CopyingBase64"!==N.Zx||g.nb.emit(f.U.Initializing,{initializing:!0}),g.nb.instance.init(),g.nb.RunCommand(M.f.AiCadMode),g.nb.instance.prepare().then((function(){xn(),hn(),g.nb.emit(f.U.Initializing,{initializing:!1});var n=g.nb.instance.scene3D;n&&n.stopRender()})),g.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(e=window)||void 0===e?void 0:e.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var i="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(i)),g.nb.instance._debug_mode=i)}}g.nb.instance.update()}g.nb.on_M("showLight3DViewer","PadMobile",(function(n){n?Y<0&&(P(2),Z.homeStore.setZIndexOf3DViewer(2),g.nb.emit(L.r.UpdateScene3D,!0)):P(-1)})),g.nb.on(f.U.ShowDreamerPopup,(function(n){Z.homeStore.setShowDreamerPopup(n)})),g.nb.on(f.U.LayoutSchemeOpened,(function(n){d(n.name),g.nb.emit(h.$T,h.Kw.Default)})),g.nb.on(f.U.ClearLayout,(function(){B({title:n("清空布局"),content:n("确定清空单空间布局？"),okText:n("确定"),cancelText:n("取消"),onOk:function(){g.nb.DispatchEvent(g.n0.ClearLayout,this)},onCancel:function(){}})})),g.nb.on(f.U.OpenMySchemeList,(function(){Z.homeStore.setShowMySchemeList(!0)})),g.nb.on_M(f.U.RoomList,"room_list",(function(n){Z.homeStore.setRoomInfos(n)})),g.nb.on(f.U.Room2SeriesSampleRoom,(function(n){Z.homeStore.setRoom2SeriesSampleArray(n)})),g.nb.on_M(f.U.SelectingTarget,"LeftPanelValue",(function(n,e,t){J(!1)})),g.nb.on(f.U.showCustomKeyboard,(function(n){(null==n?void 0:n.visible)||!1?setTimeout((function(){J(!0)}),50):setTimeout((function(){J(!1)}),10),n.input&&(rn(n.input.value),q(n.input),n.onValueChange&&Mn((function(){return n.onValueChange})))})),g.nb.on_M(f.U.updateAllMaterialScene3D,"padMobile",function(){var n=Jt((function(n){var e,t,i,r,o,a;return $t(this,(function(c){switch(c.label){case 0:return e=g.nb.instance.layout_container,t=g.nb.instance.scene3D,n&&"3D_FirstPerson"===Z.homeStore.viewMode?(g.nb.emit(f.U.ApplySeriesSample,{seriesOpening:!0,title:"更新视角中..."}),i=[],e._room_entities.forEach((function(n){n._view_cameras.forEach((function(n){i.push(n)}))})),r=i.map(function(){var n=Jt((function(n){return $t(this,(function(i){switch(i.label){case 0:return n._perspective_img.src?[3,2]:(t.active_controls.bindViewEntity(n),t.update(),[4,n.updatePerspectiveViewImg(e.painter)]);case 1:i.sent(),i.label=2;case 2:return[2]}}))}));return function(e){return n.apply(this,arguments)}}()),[4,Promise.allSettled(r)]):[3,2];case 1:c.sent(),(o=e._room_entities.reduce((function(n,e){return n?e._area>n._area?e:n:e}),null))?(t.active_controls.bindViewEntity(o._view_cameras[0]),Z.homeStore.setCurrentViewCameras(o._view_cameras)):t.setCenter((null==o||null===(a=o._main_rect)||void 0===a?void 0:a.rect_center)||new lt.Pq0(0,0,0)),g.nb.emit(f.U.ApplySeriesSample,{seriesOpening:!1,title:""}),c.label=2;case 2:return[2]}}))}));return function(e){return n.apply(this,arguments)}}()),g.nb.on(f.U.SaveProgress,(function(e){"success"===e.progress?(F.open({key:_,type:"success",content:n("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}),"autoExit"===Z.homeStore.isAutoExit&&(U.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=N.O9),Z.homeStore.setIsAutoExit("")):"fail"===e.progress?F.open({key:_,type:"error",content:n("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"ongoing"===e.progress&&F.open({key:_,type:"loading",content:n("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})})),g.nb.on_M(f.U.xmlSchemeLoaded,"padMobile",(function(n){k.f.updateAliasName(),Z.homeStore.setRoomEntites(g.nb.instance.layout_container._room_entities)}))}),[Z.homeStore.isAutoExit]),(0,u.useEffect)((function(){4===Z.homeStore.zIndexOf3DViewer?P(2):P(Z.homeStore.zIndexOf3DViewer)}),[Z.homeStore.zIndexOf3DViewer]),(0,i.jsxs)("div",{className:e.root,children:[Z.homeStore.designMode===M.f.HouseDesignMode?(0,i.jsx)(Ht,{}):(0,i.jsx)(C.A,{updateKey:sn}),(0,i.jsxs)("div",{id:"Canvascontent",className:e.content,children:[(0,i.jsx)("div",{className:"3d_container "+e.canvas3d,style:{zIndex:Y},children:(0,i.jsx)(x.A,{defaultViewMode:4})}),(0,i.jsxs)("div",{id:"body_container",className:e.canvas_pannel+" left_panel_layout",children:[(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){Z.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){Z.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t);Z.homeStore.setInitialDistance(i/Z.homeStore.scale)}},onTouchMove:function(n){if(n.stopPropagation(),2!=n.touches[n.touches.length-1].identifier&&2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t)/Z.homeStore.initialDistance;i>5?i=5:i<.001&&(i=.001),Z.homeStore.setScale(i),g.nb.DispatchEvent(g.n0.scale,i)}},onTouchEnd:function(n){n.touches.length>0&&g.nb.DispatchEvent(g.n0.updateLast_pos,n),Z.homeStore.setInitialDistance(null)}}),Z.homeStore.designMode===M.f.MeasurScaleMode&&(0,i.jsxs)("div",{className:"canvas_btns",style:{zIndex:999999,marginBottom:"10vh",gap:"20px"},children:[(0,i.jsx)(s.A,{className:"btn",type:"primary",onClick:function(){g.nb.instance&&(g.nb.RunCommand(M.f.AiCadMode),Z.homeStore.setDesignMode(M.f.AiCadMode))},children:n("取消")}),(0,i.jsx)(s.A,{className:"btn",type:"primary",onClick:function(){g.nb.instance&&(g.nb.DispatchEvent(g.n0.ConfirmScale,{img_base64:Z.homeStore.img_base64}),Z.homeStore.setDesignMode(M.f.AiCadMode))},children:n("确定")})]})]})]}),(0,i.jsx)(nn.If,{condition:Z.homeStore.showEnterPage.show,children:(0,i.jsx)(Fe,{})}),(0,i.jsx)(ct,{}),(0,i.jsx)(j.A,{}),(0,i.jsx)(m.ti,{}),(0,i.jsx)(S.A,{}),Z.homeStore.showDreamerPopup&&(0,i.jsx)(I.A,{}),"3D_FirstPerson"!==Z.homeStore.viewMode&&"3D"!==Z.homeStore.viewMode&&Z.homeStore.isSingleRoom&&Z.homeStore.designMode!=M.f.HouseDesignMode&&(0,i.jsx)("div",{className:e.RoomAreaBtns,children:(0,i.jsx)(z.A,{mode:1})}),Z.homeStore.showSaveLayoutSchemeDialog.show&&(0,i.jsx)("div",{className:e.overlay,children:(0,i.jsx)(y.A,{schemeName:a||"",closeCb:function(){Z.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:""})},isSaveAs:W})}),(0,i.jsx)(D.A,{schemeNameCb:function(n){d(n)}}),(0,i.jsx)(v.A,{onKeyPress:function(n){$&&($.value=$.value+n,rn($.value))},onDelete:function(){$&&($.value=$.value.slice(0,-1),rn($.value))},onConfirm:function(){if($){var n=parseFloat(tn);if(!isNaN(n)){if(gn)gn(n);else{$.value=tn;var e=new Event("change",{bubbles:!0});$.dispatchEvent(e)}J(!1)}}},onClose:function(){J(!1),rn("")},inputValue:tn,isVisible:X}),Z.homeStore.showDreamerPopup&&(0,i.jsx)(I.A,{}),(0,i.jsx)(T.A,{ref:un}),(0,i.jsx)(E.A,{}),Z.homeStore.showAtlas&&(0,i.jsx)("div",{className:e.mobile_atlas_container,style:{zIndex:999},children:(0,i.jsx)(O.A,{setZIndexOfMobileAtlas:an})}),Z.homeStore.showStartPage.show&&(0,i.jsx)("div",{className:e.pad_startpage_container,style:{zIndex:999},children:(0,i.jsx)(Ce,{toSelectHX:function(){},step:0})}),(0,i.jsx)(cn.cq,{channelCode:"Helptips-006"}),R]})}))}}]);