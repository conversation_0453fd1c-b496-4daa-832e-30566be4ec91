import { G<PERSON> } from "lil-gui";
import { PerspectiveCamera, Vector3 } from "three";
import { EventName } from "@/Apps/EventSystem";
import { OutlinePostProcess } from "@/Apps/LayoutAI/Scene3D/builder/OutlinePostProcess";
import { FigureViewControls } from "@/Apps/LayoutAI/Scene3D/controls/FigureViewControls";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";

export class CameraTest {
    private _gui: GUI;

    constructor(gui: GUI) {
        this._gui = gui;
    }

    public addCameraCtr() {
        const scene3D = LayoutAI_App.instance.scene3D;
        const camera = scene3D.active_controls.camera;

        const onProjectionUpdate = () => {
            if (camera instanceof PerspectiveCamera) {
                camera.updateProjectionMatrix();
                let outline = scene3D.outlinePostProcessing as OutlinePostProcess;
                if (outline.isEnabled) {
                    outline.makeOutlineDirty();
                }
                LayoutAI_App.emit_M(EventName.Scene3DCameraChanged, null);
            }
        };

        // 相机控制
        let cameraFolder = this._gui.addFolder('相机参数');
        cameraFolder.close();
        if (camera instanceof PerspectiveCamera) {
            cameraFolder.add(camera, 'fov', 1, 180).name('视场角').onChange(onProjectionUpdate);

            cameraFolder.add(camera, 'near', 0.1, 10000).name('近裁面').onChange((value: number) => {
                if (scene3D.active_controls instanceof FigureViewControls) {
                    scene3D.active_controls.setCameraNear(value);
                } else {
                    camera.near = value;
                }
                onProjectionUpdate();
            });

            cameraFolder.add(camera, 'far', 1, 10000).name('远裁面').onChange(onProjectionUpdate);

            // 控制相机宽高比
            cameraFolder.add(camera, 'aspect', 0.1, 10).name('宽高比').onChange(onProjectionUpdate);
        }

        // 控制相机位置
        let cameraPositionFolder = cameraFolder.addFolder('位置');
        cameraPositionFolder.add(camera.position, 'x', -10000, 10000).name('x').onChange((value: number) => {
            scene3D.active_controls.setCenterAndUpdate(new Vector3(value, camera.position.y, camera.position.z));
        });
        cameraPositionFolder.add(camera.position, 'y', -10000, 10000).name('y').onChange((value: number) => {
            scene3D.active_controls.setCenterAndUpdate(new Vector3(camera.position.x, value, camera.position.z));
        });
        cameraPositionFolder.add(camera.position, 'z', 0, 5000).name('z').onChange((value: number) => {
            if (scene3D.active_controls instanceof FigureViewControls) {
                let rect = scene3D.active_controls._rect;
                rect.zval = value;
                scene3D.active_controls.setCenterAndUpdate(new Vector3(camera.position.x, camera.position.y, value));
            }
        });

        // 控制相机旋转
        let rect = (scene3D.active_controls as FigureViewControls)._rect;
        if (!rect) {
            return;
        }
        let cameraRotationFolder = cameraFolder.addFolder('旋转');
        cameraRotationFolder.add({ x: 180 * rect.rotation_x / Math.PI }, 'x', -180, 180).name('俯仰角').onChange((value: number) => {
            if (scene3D.active_controls instanceof FigureViewControls) {
                rect.rotation_x = Math.PI * value / 180;
            }
        });

        cameraRotationFolder.add({ z: 180 * rect.rotation_z / Math.PI }, 'z', -180, 180).name('偏航角').onChange((value: number) => {
            if (scene3D.active_controls instanceof FigureViewControls) {
                rect.rotation_z = Math.PI * value / 180;
            }
        });

        // 监听相机变化以更新GUI
        LayoutAI_App.on_M(EventName.Scene3DCameraChanged, '', () => {
            // 相机位置 
            cameraPositionFolder.controllers.forEach(controller => { controller.updateDisplay() });
            // 相机旋转
            cameraRotationFolder.controllers.forEach(controller => {
                if (controller._name === '俯仰角') {
                    controller.setValue(180 * rect.rotation_x / Math.PI);
                } else if (controller._name === '偏航角') {
                    controller.setValue(180 * rect.rotation_z / Math.PI);
                }
                controller.updateDisplay()
            });
        });
    }
}
