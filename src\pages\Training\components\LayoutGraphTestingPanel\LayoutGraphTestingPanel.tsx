import { useEffect, useState } from "react";

import { EventName } from "@/Apps/EventSystem";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { LayoutGraphTestingHander } from "@/Apps/Training/Handlers/LayoutGraphTestingHandler";
import LayoutScoreDialog from "@/pages/Home/LayoutScoreDialog/layoutScoreDialog";
import { Checkbox } from "@svg/antd";

import useCommonStyles from "../../common_style/index";
import { SolverMethods, SolverMethodsDict } from "@/Apps/LayoutAI/Layout/TAppSolvers/TSwjLayoutGraphSolver";


const LayoutGraphTestingPanel: React.FC = () => {

    const common_styles = useCommonStyles().styles;
    // const [isDisplay, setIsDisplay] = useState<boolean>(false);
    const [layoutGraphTestingRightPanelProps, setLayoutGraphTestingRightPanelProps] = useState<any>(null);
    const [updatedCount, setUpdatedCount] = useState<number>(0);

    // const object_id = "LayoutGraphTestingPanel";
    const register_events = () => {
        LayoutAI_App.on(EventName.LayoutGraphTestingRightPanel, (props: any) => {
            setLayoutGraphTestingRightPanelProps(props);
        });
    }
    const showLayoutScoreDialog = () => {
        LayoutAI_App.emit(EventName.ShowLayoutScoreDialog, true);
    }
    useEffect(() => {
        register_events();

    }, []);

    return (
        <div key={"layout_graph_" + updatedCount} className={common_styles.rightPanel} style={{ zIndex: layoutGraphTestingRightPanelProps ? 5 : -2 }}>
            <LayoutScoreDialog></LayoutScoreDialog>
            <div className={"row_div"}>
                <span>显示控制</span>
                <select id="select_visible_range_type" onChange={layoutGraphTestingRightPanelProps?.onSelectVisibleChange}>
                    <option value="7">所有元素</option>
                    <option value="1">地面元素</option>
                    <option value="2">悬挂元素</option>
                    <option value="4">顶部元素</option>
                </select>
            </div>
            <div className={"row_div"}>
                <span>更新布局结果</span>
                <button onClick={layoutGraphTestingRightPanelProps?.onUpdateAiLayoutBtnClick}>更新</button>
            </div>
            <div className={"row_div"}>
                <span>微调</span>
                <button onClick={layoutGraphTestingRightPanelProps?.onFinetuning}>微调</button>
            </div>
            <div className={"row_div"}>
                <span>显示评分器</span>
                <button onClick={showLayoutScoreDialog}>显示&更新</button>
            </div>
            <div className={"row_container"} style={{ height: 300 }}>
                {Object.keys(LayoutGraphTestingHander._layer_visible).map((key, index) => <div className={"row_div"} key={"item" + index}>
                    <span>{(LayoutGraphTestingHander._layer_visible as any)[key].name}</span>
                    <Checkbox style={{ marginLeft: 10 }} defaultChecked={(LayoutGraphTestingHander._layer_visible as any)[key].visible} onChange={(ev) => {
                        (LayoutGraphTestingHander._layer_visible as any)[key].visible = ev.target.checked;
                        setUpdatedCount(updatedCount + 1);
                        LayoutAI_App.instance.update();
                    }}></Checkbox>
                </div>)}
            </div>

        </div>

    )
}

export default LayoutGraphTestingPanel;