import { I_SwjExtDrawingData } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { TExtDrawingEntity } from "./TExtDrawingEntity";
import { TDrawingLineEntity } from "./TDrawingLineEntity";



export class TExtDrawingParser
{

    private static _instance : TExtDrawingParser = null;
    constructor()
    {

    }

    public static get instance()
    {
        if(!TExtDrawingParser._instance)
        {
            TExtDrawingParser._instance = new TExtDrawingParser();
        }
        return TExtDrawingParser._instance;
    }

    public parse(data:I_SwjExtDrawingData)
    {
        let entity: TExtDrawingEntity = null;
        if(data.realType == "MovingLine")
        {
            entity = new TDrawingLineEntity();
            entity.importData(data);
        }
        else{

        }
        return entity;

    }

    static Parse(data:I_SwjExtDrawingData)
    {
        return TExtDrawingParser.instance.parse(data)
    }
}