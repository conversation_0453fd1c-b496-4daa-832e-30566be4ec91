import React, { useState, useRef } from 'react';
import { Button, message, Tooltip } from '@svg/antd';
import { PlusCircleOutlined, MinusCircleOutlined, RotateRightOutlined, DownloadOutlined, DeleteOutlined, CloseOutlined, LeftOutlined, RightOutlined } from "@ant-design/icons";
import useStyles from './style';
import { checkIsMobile } from '@/config';

// 定义按钮类型枚举
type ButtonType = 'zoomIn' | 'zoomOut' | 'rotate' | 'reset' | 'download' | 'delete' | 'close';

// 定义按钮配置接口
interface ButtonConfig {
    title: string;
    icon?: React.ReactNode;
    text?: string;
    onClick: (e: React.MouseEvent) => void;
    type: ButtonType;
}

// 定义组件属性接口（使用泛型实现类型解耦）
interface Props<T> {
    onClose: () => void;
    imageAlt?: string;
    openIndex: number;
    items: T[]; // 泛型数组，支持任意类型
    onDelete?: (item: T) => void; // 删除回调也使用泛型
    
    // 配置函数：从任意类型的item中提取图片URL
    getImageUrl: (item: T) => string;
    
    // 可选配置：从item中提取文件名（用于下载）
    getFileName?: (item: T) => string;
    
    // 按钮显示配置
    btnConfig?: Partial<Record<ButtonType, boolean>>;
}

const downloadImage = (url: string, filename?: string) => {
    fetch(url)
        .then(response => {
            if (!response.ok) throw new Error('网络响应不正常');
            return response.blob();
        })
        .then(blob => {
            const blobUrl = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = blobUrl;
            a.download = filename || 'image';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(blobUrl);
        })
        .catch(error => console.error('下载图片失败:', error));
};

// 使用泛型组件实现与父组件的解耦
const CustomImagePreview = <T,>({
    onClose,
    imageAlt,
    openIndex,
    items,
    onDelete,
    getImageUrl, // 从item获取图片URL的配置函数
    getFileName, // 从item获取文件名的配置函数（可选）
    btnConfig = {
        zoomIn: true,
        zoomOut: true,
        rotate: true,
        reset: true,
        download: true,
        delete: true,
        close: true
    }
}: Props<T>) => {
    const { styles } = useStyles();
    const [scale, setScale] = useState(1);
    const [rotate, setRotate] = useState(0);
    const [showedImageIndex, setShowedImageIndex] = useState<number>(openIndex);
    const [position, setPosition] = useState({ x: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const startX = useRef(0);
    const imageContainer = useRef<HTMLDivElement>(null);
    const [opacity, setOpacity] = useState(1)

    /**
     * 图片缩放函数
     * @param zoomIn 传入true放大，传入false缩小
     */
    const handleZoom = (zoomIn: boolean) => {
        setScale(prev => Math.max(0.5, Math.min(zoomIn ? prev * 1.2 : prev / 1.2, 5)));
    };

    const handleRotate = () => setRotate(prev => (prev + 90) % 360);
    
    const handleReset = () => { 
        setScale(1); 
        setRotate(0); 
        setPosition({ x: 0 });
    };

    // 获取当前图片的文件名（优先使用配置函数，其次自动解析）
    const getCurrentFileName = (): string => {
        // 如果提供了getFileName配置函数，直接使用
        if (getFileName) {
            return getFileName(items[showedImageIndex]) || imageAlt || 'image';
        }
        
        // 否则从URL中解析（兼容旧逻辑）
        const url = getImageUrl(items[showedImageIndex]);
        const urlParts = url.split('/');
        const urlFileName = urlParts[urlParts.length - 1]?.split('?')[0];
        return urlFileName || imageAlt || 'image';
    };

    const showNext = () => {
        if (showedImageIndex < items.length - 1) {
            setShowedImageIndex(prev => prev + 1);
            // 切换图片时重置缩放和旋转
            handleReset();
        } else {
            message.destroy();
            message.info('已经是最后一张啦');
        }
    };

    const showPrev = () => {
        if (showedImageIndex > 0) {
            setShowedImageIndex(prev => prev - 1);
            // 切换图片时重置缩放和旋转
            handleReset();
        } else {
            message.destroy();
            message.info('已经是第一张啦');
        }
    };

    // 拖动开始
    const handleDragStart = (clientX: number) => {
        setIsDragging(true);
        startX.current = clientX - position.x;
    };

    // 拖动中
    const handleDragMove = (clientX: number) => {
        if (!isDragging) return;
        setPosition({
            x: clientX - startX.current
        });
        const dis = Math.abs(clientX - startX.current)
        if(dis/1000 < 0.5){
            setOpacity(1- dis/1000)
        }
    };

    // 拖动结束
    const handleDragEnd = () => {
        if (!isDragging) return;
        
        // 计算容器宽度（用于确定拖动阈值）
        const containerWidth = imageContainer.current?.clientWidth || 300;
        const threshold = containerWidth * 0.1; // 拖动距离超过容器宽度的指定比例的距离则切换图片
        
        // 根据拖动距离判断是否切换图片
        if (position.x > threshold) {
            showPrev();
        } else if (position.x < -threshold) {
            showNext();
        }
        setPosition({ x: 0 });
        setIsDragging(false);
        setOpacity(1)
    };

    // 基础按钮配置数组
    const baseButtonConfigs: ButtonConfig[] = [
        {
            type: 'zoomIn',
            title: "放大",
            icon: <PlusCircleOutlined />,
            onClick: (e) => {
                e.stopPropagation();
                handleZoom(true);
            }
        },
        {
            type: 'zoomOut',
            title: "缩小",
            icon: <MinusCircleOutlined />,
            onClick: (e) => {
                e.stopPropagation();
                handleZoom(false);
            }
        },
        {
            type: 'rotate',
            title: "旋转",
            icon: <RotateRightOutlined />,
            onClick: (e) => {
                e.stopPropagation();
                handleRotate();
            }
        },
        {
            type: 'reset',
            title: "重置",
            text: "重置",
            onClick: (e) => {
                e.stopPropagation();
                handleReset();
            }
        },
        {
            type: 'download',
            title: "下载图片",
            icon: <DownloadOutlined />,
            onClick: (e) => {
                e.stopPropagation();
                const currentItem = items[showedImageIndex];
                downloadImage(getImageUrl(currentItem), getCurrentFileName());
            }
        },
        {
            type: 'delete',
            title: "删除",
            icon: <DeleteOutlined />,
            onClick: (e) => {
                e.stopPropagation();
                onDelete?.(items[showedImageIndex]);
            }
        },
        {
            type: 'close',
            title: "关闭",
            icon: <CloseOutlined />,
            onClick: (e) => {
                e.stopPropagation();
                onClose();
            }
        }
    ];

    // 根据props配置过滤需要显示的按钮
    const visibleButtons = baseButtonConfigs.filter(
        config => btnConfig[config.type] !== false
    );

    // 获取当前图片的URL
    const currentImageUrl = getImageUrl(items[showedImageIndex]);

    return (
        <div
            onClick={onClose}
            className={styles.previewContainer}
        >
            <div 
                className={styles.changeBtn} 
                style={{left: '3%'}} 
                onClick={e => {e.stopPropagation(); showPrev()}}
            >
                <LeftOutlined style={{color: 'white', opacity: '0.5'}}/>
            </div>
            <div 
                className={styles.changeBtn} 
                style={{right: '3%'}} 
                onClick={e => {e.stopPropagation(); showNext()}}
            >
                <RightOutlined style={{color: 'white', opacity: '0.5'}}/>
            </div>

            <div onClick={onClose} className={styles.previewMask} />
            <div className={styles.previewWrap}>
                <div 
                    ref={imageContainer}
                    className={styles.previewBody}
                    // 鼠标事件
                    onMouseDown={(e) => {
                        e.stopPropagation();
                        handleDragStart(e.clientX);
                    }}
                    onMouseMove={(e) => {
                        e.stopPropagation();
                        handleDragMove(e.clientX);
                    }}
                    onMouseUp={(e) => {
                        e.stopPropagation();
                        handleDragEnd();
                    }}
                    onMouseLeave={(e) => {
                        e.stopPropagation();
                        handleDragEnd();
                    }}
                    // 触摸事件（支持移动设备）
                    onTouchStart={(e) => {
                        e.stopPropagation();
                        handleDragStart(e.touches[0].clientX);
                    }}
                    onTouchMove={(e) => {
                        e.stopPropagation();
                        handleDragMove(e.touches[0].clientX);
                    }}
                    onTouchEnd={(e) => {
                        e.stopPropagation();
                        handleDragEnd();
                    }}
                >
                    <img
                        src={currentImageUrl}
                        alt={imageAlt}
                        draggable={false}
                        style={{
                            display: 'block',
                            transform: `scale(${scale}) rotate(${rotate}deg)`,
                            position: 'relative',
                            margin: '0 auto',
                            transition: isDragging ? 'none' : 'transform 0.3s ease',
                            opacity: `${opacity}`,
                            height: 'auto',
                            width: '80vw'
                        }}
                        onClick={e => e.stopPropagation()}
                    />
                </div>

                <div
                    className={styles.operationsBar}
                    onClick={e => e.stopPropagation()}
                >
                    {
                    !checkIsMobile() 
                    ?  visibleButtons.map((config) => (
                        <Tooltip key={config.type} title={config.title}>
                            <Button
                                icon={config.icon}
                                size="small"
                                onClick={config.onClick}
                                style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}
                            >
                                {config.text}
                            </Button>
                        </Tooltip>
                    ))
                    :  visibleButtons.map((config) => (
                        <Button
                            icon={config.icon}
                            size="small"
                            onClick={config.onClick}
                            style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}
                        >
                            {config.text}
                        </Button>
                    ))
                    }
                </div>
            </div>
        </div>
    );
};

export default CustomImagePreview;