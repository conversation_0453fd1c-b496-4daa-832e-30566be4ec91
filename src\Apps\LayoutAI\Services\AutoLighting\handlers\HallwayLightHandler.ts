import { <PERSON><PERSON><PERSON>Hand<PERSON> } from "./BaseLightHandler";
import { LightingRuler, AutoLightingRulerType } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { RoomSpaceAreaType } from "../../../Layout/IRoomInterface";
import { ZRect } from "@layoutai/z_polygon";

export class HallwayLightHandler extends BaseLightHandler {

    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        let entities: TFillLightEntity[] = [];
        let layout_container = this.getLayoutContainer();
        layout_container._room_entities.forEach(roomEntity => {
            roomEntity._sub_room_areas.forEach(subRoomArea => {
                if (subRoomArea.space_area_type === RoomSpaceAreaType.HallwayArea) {
                    if (this.checkCondition(ruler, roomEntity)) {
                        let rect = subRoomArea.rect;
                        if(this.isSkipHallway(ruler, rect)){
                            return;
                        }
                        let lightEntity = this._createFillLightEntity(
                            ruler.lighting.intensity,
                            ruler.lighting.color,
                            { x: rect.rect_center_3d.x, y: rect.rect_center_3d.y, z: ruler.pose.z },
                            this.getSize(ruler.lighting.length, rect.h),
                            this.getSize(ruler.lighting.width, rect.w),
                            0, 0, rect.rotation_z,
                            ruler.name,
                        );
                        entities.push(lightEntity);
                    }
                }
            });
        });
        return entities;
    }

    private isSkipHallway(ruler: LightingRuler, rect: ZRect): boolean {
        if(ruler.typeId === AutoLightingRulerType.CorridorDayLight){
            return rect.h * rect.w <= 5e6;
        }
    }
}