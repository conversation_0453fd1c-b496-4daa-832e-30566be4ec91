const express = require("express");
const bodyParser = require('body-parser');
const http = require("http");
const { resolve } = require("path");
const fs = require("fs")
const Ws = require("ws").Server;

const app = express();
app.use(bodyParser.json()); // 解析JSON数据
app.use(bodyParser.urlencoded({ extended: true })); // 解析URL编码数据
const server = http.createServer(app);

let port = 37661;
server.listen(port);


let wsServer = new Ws({server});

const SocketList = [];
const QueryQueue = [];

function dataURItoBlob(base64Data) {
    var byteString;

    if(base64Data.split(',')[0].indexOf('base64') >= 0)
        byteString = atob(base64Data.split(',')[1]);//base64 解码
    else{
        byteString = unescape(base64Data.split(',')[1]);
    }
    var mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0];//mime类型

    // var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
    // var ia = new Uint8Array(arrayBuffer);//创建视图
    var ia = new Uint8Array(byteString.length);//创建视图
    for(var i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
    }
    var blob = new Blob([ia], {
        type: mimeString
    });
    return blob;
}  
function dataURItoBuffer(base64Data) {

    let dataBuffer = Buffer.from(base64Data.split(',')[1], 'base64');
    return dataBuffer;
}  
function addQuery(query)
{
    QueryQueue.push(query);
}
function removeQuery(query)
{
    let id = QueryQueue.indexOf(query);
    QueryQueue.splice(id,1);
}
function infillQuery(query_res)
{
    let query = QueryQueue.find((query)=>query.queryId === query_res.queryId);
    if(query)
    {
        query["queryResult"] = query_res['queryResult'];
    }

}
function getValidSocket()
{
    return SocketList.find((socket)=>{
        return socket.readyState === 1;
    })
}
async function queryModelById(id)
{

    if(!id) return null;
    let validSocket = getValidSocket();
    if(!validSocket)
    {
        console.log("未找到活跃的socket!");
        return null;
    }
    console.log("找到活跃的socket, 当前请求数量", QueryQueue.length);
    let query = {queryId:(new Date().getTime()),queryData:{method:"queryModelById", modelId:id},queryResult:null};
    addQuery(query);

    validSocket.send(JSON.stringify(query)); // 传输到客户端


    let res = await new Promise((resolve,reject)=>{
        let count = 0;
        try {
            const checkQuery = ()=>{

                if(query.queryResult)
                {
                    resolve(true);
                    return;
                }
                if(count++<10)
                {
                    setTimeout(() => {
                        checkQuery(); 
                     }, 1000);
                }
                else{
                    resolve(false);
                }
            }
            checkQuery();
        } catch (error) {
            console.log(error);
            resolve(false);
        }

    })

    console.log("请求计算的结果",res);
    let query_result = query.queryResult;
    if(query_result.gltf)
    {
        let buffer = dataURItoBuffer(query_result.gltf);
        console.log(buffer.byteLength,query_result.gltf.length);
        fs.writeFile("./test.glb",buffer,(err)=>{})
    }
    removeQuery(query);
    return query_result;


}

function filterSocketList()
{
    let list =  SocketList.filter((socket)=>socket.readyState===1);
    SocketList.length = 0;
    SocketList.push(...list);
}


wsServer.on("connection",function(socket){
    filterSocketList();
    SocketList.push(socket);
    console.log("连接成功",socket.protocol,"当前连接数",SocketList.length);

    socket.on("message",msg=>{
        let query =  JSON.parse(msg.toString());
        if(query && query.queryId)
        {
            infillQuery(query);
        }
    });


    socket.on("close",(code,reason)=>{
        let id = SocketList.indexOf(socket);
        if(id >=0) SocketList.splice(id,1);
    })
})
// app.use(express.static('www'));
app.get('/', (req, res) => {
    res.send('hello world!');
  });
app.get('/queryCabinetModelById',async (req, res) => {
    const id = req.query['model_id'] || null; // 获取POST请求的数据
    // console.log(data);
    // 处理数据逻辑
    let res_data =await queryModelById(id);
    res.send(JSON.stringify(res_data));
  });
console.log("listen ",port);