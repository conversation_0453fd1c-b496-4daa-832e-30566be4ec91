export interface metaImage {
    createDate: string;
    createUser: string;
    enable: number;
    id: string;
    isDelete: number;
    layer: number;
    metaCateId: string;
    metaCode: string;
    metaData: string;
    metaDesc: string;
    metaHeight: number;
    metaImage: string;
    metaLabel: string;
    metaLength: number;
    metaName: string;
    metaType: string;
    metaWidth: number;
    modelloc: string;
    roomName: string;
    sort: number;
    subCateId: string;
    tenantId: string;
    updateDate: string;
    updateUser: string;
}

export interface subCategoryList {
    createDate: string;
    createUser: string;
    id: string;
    isDelete: number;
    metaCateId: string;
    metaImageList: metaImage[];
    subCateCode: string;
    subCateName: string;
    updateDate: string;
    updateUser: string;
}
export interface CombMenuObjData {
    cateCode: string;
    cateIcon: string;
    cateName: string;
    createDate: string;
    createUser: string;
    enable: number;
    id: string;
    indust: string;
    isDelete: number;
    subCategoryList: subCategoryList[];
    tenantId: string;
    updateDate: string;
    updateUser: string;
}