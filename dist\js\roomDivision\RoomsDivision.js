var key,RoomsDivisionModule=void 0!==RoomsDivisionModule?RoomsDivisionModule:{},moduleOverrides={};for(key in RoomsDivisionModule)RoomsDivisionModule.hasOwnProperty(key)&&(moduleOverrides[key]=RoomsDivisionModule[key]);var arguments_=[],thisProgram="./this.program",quit_=function(e,o){throw o},ENVIRONMENT_IS_WEB="object"==typeof window,ENVIRONMENT_IS_WORKER="function"==typeof importScripts,ENVIRONMENT_IS_NODE="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(RoomsDivisionModule.ENVIRONMENT)throw new Error("RoomsDivisionModule.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var read_,readAsync,readBinary,setWindowTitle,nodeFS,nodePath,scriptDirectory="";function locateFile(e){return RoomsDivisionModule.locateFile?RoomsDivisionModule.locateFile(e,scriptDirectory):scriptDirectory+e}function logExceptionOnExit(e){if(!(e instanceof ExitStatus)){var o=e;e&&"object"==typeof e&&e.stack&&(o=[e,e.stack]),err("exiting due to exception: "+o)}}if(ENVIRONMENT_IS_NODE){if("object"!=typeof process||"function"!=typeof require)throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");scriptDirectory=ENVIRONMENT_IS_WORKER?require("path").dirname(scriptDirectory)+"/":__dirname+"/",read_=function(e,o){return nodeFS||(nodeFS=require("fs")),nodePath||(nodePath=require("path")),e=nodePath.normalize(e),nodeFS.readFileSync(e,o?null:"utf8")},readBinary=function(e){var o=read_(e,!0);return o.buffer||(o=new Uint8Array(o)),assert(o.buffer),o},readAsync=function(e,o,t){nodeFS||(nodeFS=require("fs")),nodePath||(nodePath=require("path")),e=nodePath.normalize(e),nodeFS.readFile(e,(function(e,r){e?t(e):o(r.buffer)}))},process.argv.length>1&&(thisProgram=process.argv[1].replace(/\\/g,"/")),arguments_=process.argv.slice(2),"undefined"!=typeof module&&(module.exports=RoomsDivisionModule),process.on("uncaughtException",(function(e){if(!(e instanceof ExitStatus))throw e})),process.on("unhandledRejection",(function(e){throw e})),quit_=function(e,o){if(keepRuntimeAlive())throw process.exitCode=e,o;logExceptionOnExit(o),process.exit(e)},RoomsDivisionModule.inspect=function(){return"[Emscripten RoomsDivisionModule object]"}}else if(ENVIRONMENT_IS_SHELL){if("object"==typeof process&&"function"==typeof require||"object"==typeof window||"function"==typeof importScripts)throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");"undefined"!=typeof read&&(read_=function(e){return read(e)}),readBinary=function(e){var o;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(assert("object"==typeof(o=read(e,"binary"))),o)},readAsync=function(e,o,t){setTimeout((function(){o(readBinary(e))}),0)},"undefined"!=typeof scriptArgs?arguments_=scriptArgs:"undefined"!=typeof arguments&&(arguments_=arguments),"function"==typeof quit&&(quit_=function(e,o){logExceptionOnExit(o),quit(e)}),"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)}else{if(!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER)throw new Error("environment detection error");if(ENVIRONMENT_IS_WORKER?scriptDirectory=self.location.href:"undefined"!=typeof document&&document.currentScript&&(scriptDirectory=document.currentScript.src),scriptDirectory=0!==scriptDirectory.indexOf("blob:")?scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1):"","object"!=typeof window&&"function"!=typeof importScripts)throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");read_=function(e){var o=new XMLHttpRequest;return o.open("GET",e,!1),o.send(null),o.responseText},ENVIRONMENT_IS_WORKER&&(readBinary=function(e){var o=new XMLHttpRequest;return o.open("GET",e,!1),o.responseType="arraybuffer",o.send(null),new Uint8Array(o.response)}),readAsync=function(e,o,t){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=function(){200==r.status||0==r.status&&r.response?o(r.response):t()},r.onerror=t,r.send(null)},setWindowTitle=function(e){document.title=e}}var out=RoomsDivisionModule.print||console.log.bind(console),err=RoomsDivisionModule.printErr||console.warn.bind(console);for(key in moduleOverrides)moduleOverrides.hasOwnProperty(key)&&(RoomsDivisionModule[key]=moduleOverrides[key]);moduleOverrides=null,RoomsDivisionModule.arguments&&(arguments_=RoomsDivisionModule.arguments),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"arguments")||Object.defineProperty(RoomsDivisionModule,"arguments",{configurable:!0,get:function(){abort("RoomsDivisionModule.arguments has been replaced with plain arguments_ (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),RoomsDivisionModule.thisProgram&&(thisProgram=RoomsDivisionModule.thisProgram),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"thisProgram")||Object.defineProperty(RoomsDivisionModule,"thisProgram",{configurable:!0,get:function(){abort("RoomsDivisionModule.thisProgram has been replaced with plain thisProgram (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),RoomsDivisionModule.quit&&(quit_=RoomsDivisionModule.quit),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"quit")||Object.defineProperty(RoomsDivisionModule,"quit",{configurable:!0,get:function(){abort("RoomsDivisionModule.quit has been replaced with plain quit_ (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),assert(void 0===RoomsDivisionModule.memoryInitializerPrefixURL,"RoomsDivisionModule.memoryInitializerPrefixURL option was removed, use RoomsDivisionModule.locateFile instead"),assert(void 0===RoomsDivisionModule.pthreadMainPrefixURL,"RoomsDivisionModule.pthreadMainPrefixURL option was removed, use RoomsDivisionModule.locateFile instead"),assert(void 0===RoomsDivisionModule.cdInitializerPrefixURL,"RoomsDivisionModule.cdInitializerPrefixURL option was removed, use RoomsDivisionModule.locateFile instead"),assert(void 0===RoomsDivisionModule.filePackagePrefixURL,"RoomsDivisionModule.filePackagePrefixURL option was removed, use RoomsDivisionModule.locateFile instead"),assert(void 0===RoomsDivisionModule.read,"RoomsDivisionModule.read option was removed (modify read_ in JS)"),assert(void 0===RoomsDivisionModule.readAsync,"RoomsDivisionModule.readAsync option was removed (modify readAsync in JS)"),assert(void 0===RoomsDivisionModule.readBinary,"RoomsDivisionModule.readBinary option was removed (modify readBinary in JS)"),assert(void 0===RoomsDivisionModule.setWindowTitle,"RoomsDivisionModule.setWindowTitle option was removed (modify setWindowTitle in JS)"),assert(void 0===RoomsDivisionModule.TOTAL_MEMORY,"RoomsDivisionModule.TOTAL_MEMORY has been renamed RoomsDivisionModule.INITIAL_MEMORY"),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"read")||Object.defineProperty(RoomsDivisionModule,"read",{configurable:!0,get:function(){abort("RoomsDivisionModule.read has been replaced with plain read_ (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readAsync")||Object.defineProperty(RoomsDivisionModule,"readAsync",{configurable:!0,get:function(){abort("RoomsDivisionModule.readAsync has been replaced with plain readAsync (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readBinary")||Object.defineProperty(RoomsDivisionModule,"readBinary",{configurable:!0,get:function(){abort("RoomsDivisionModule.readBinary has been replaced with plain readBinary (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setWindowTitle")||Object.defineProperty(RoomsDivisionModule,"setWindowTitle",{configurable:!0,get:function(){abort("RoomsDivisionModule.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}});var IDBFS="IDBFS is no longer included by default; build with -lidbfs.js",PROXYFS="PROXYFS is no longer included by default; build with -lproxyfs.js",WORKERFS="WORKERFS is no longer included by default; build with -lworkerfs.js",NODEFS="NODEFS is no longer included by default; build with -lnodefs.js";assert(!ENVIRONMENT_IS_SHELL,"shell environment detected but not enabled at build time.  Add 'shell' to `-s ENVIRONMENT` to enable.");var STACK_ALIGN=16,POINTER_SIZE=4;function getNativeTypeSize(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":case"float":return 4;case"i64":case"double":return 8;default:if("*"===e[e.length-1])return POINTER_SIZE;if("i"===e[0]){var o=Number(e.substr(1));return assert(o%8==0,"getNativeTypeSize invalid bits "+o+", type "+e),o/8}return 0}}function warnOnce(e){warnOnce.shown||(warnOnce.shown={}),warnOnce.shown[e]||(warnOnce.shown[e]=1,err(e))}function convertJsFunctionToWasm(e,o){if("function"==typeof WebAssembly.Function){for(var t={i:"i32",j:"i64",f:"f32",d:"f64"},r={parameters:[],results:"v"==o[0]?[]:[t[o[0]]]},n=1;n<o.length;++n)r.parameters.push(t[o[n]]);return new WebAssembly.Function(r,e)}var i=[1,0,1,96],s=o.slice(0,1),a=o.slice(1),d={i:127,j:126,f:125,d:124};i.push(a.length);for(n=0;n<a.length;++n)i.push(d[a[n]]);"v"==s?i.push(0):i=i.concat([1,d[s]]),i[1]=i.length-2;var u=new Uint8Array([0,97,115,109,1,0,0,0].concat(i,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0])),l=new WebAssembly.RoomsDivisionModule(u);return new WebAssembly.Instance(l,{e:{f:e}}).exports.f}var functionsInTableMap,freeTableIndexes=[];function getEmptyTableSlot(){if(freeTableIndexes.length)return freeTableIndexes.pop();try{wasmTable.grow(1)}catch(e){if(!(e instanceof RangeError))throw e;throw"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH."}return wasmTable.length-1}function updateTableMap(e,o){for(var t=e;t<e+o;t++){var r=getWasmTableEntry(t);r&&functionsInTableMap.set(r,t)}}function addFunction(e,o){if(assert(void 0!==e),functionsInTableMap||(functionsInTableMap=new WeakMap,updateTableMap(0,wasmTable.length)),functionsInTableMap.has(e))return functionsInTableMap.get(e);var t=getEmptyTableSlot();try{setWasmTableEntry(t,e)}catch(r){if(!(r instanceof TypeError))throw r;assert(void 0!==o,"Missing signature argument to addFunction: "+e),setWasmTableEntry(t,convertJsFunctionToWasm(e,o))}return functionsInTableMap.set(e,t),t}function removeFunction(e){functionsInTableMap.delete(getWasmTableEntry(e)),freeTableIndexes.push(e)}var wasmBinary,tempRet0=0,setTempRet0=function(e){tempRet0=e},getTempRet0=function(){return tempRet0};RoomsDivisionModule.wasmBinary&&(wasmBinary=RoomsDivisionModule.wasmBinary),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"wasmBinary")||Object.defineProperty(RoomsDivisionModule,"wasmBinary",{configurable:!0,get:function(){abort("RoomsDivisionModule.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}});var wasmMemory,noExitRuntime=RoomsDivisionModule.noExitRuntime||!0;function setValue(e,o,t,r){switch("*"===(t=t||"i8").charAt(t.length-1)&&(t="i32"),t){case"i1":case"i8":HEAP8[e|0]=o;break;case"i16":HEAP16[e>>1]=o;break;case"i32":HEAP32[e>>2]=o;break;case"i64":tempI64=[o>>>0,(tempDouble=o,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[e>>2]=tempI64[0],HEAP32[e+4>>2]=tempI64[1];break;case"float":HEAPF32[e>>2]=o;break;case"double":HEAPF64[e>>3]=o;break;default:abort("invalid type for setValue: "+t)}}function getValue(e,o,t){switch("*"===(o=o||"i8").charAt(o.length-1)&&(o="i32"),o){case"i1":case"i8":return HEAP8[e|0];case"i16":return HEAP16[e>>1];case"i32":case"i64":return HEAP32[e>>2];case"float":return HEAPF32[e>>2];case"double":return Number(HEAPF64[e>>3]);default:abort("invalid type for getValue: "+o)}return null}Object.getOwnPropertyDescriptor(RoomsDivisionModule,"noExitRuntime")||Object.defineProperty(RoomsDivisionModule,"noExitRuntime",{configurable:!0,get:function(){abort("RoomsDivisionModule.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),"object"!=typeof WebAssembly&&abort("no native wasm support detected");var EXITSTATUS,ABORT=!1;function assert(e,o){e||abort("Assertion failed: "+o)}function getCFunc(e){var o=RoomsDivisionModule["_"+e];return assert(o,"Cannot call unknown function "+e+", make sure it is exported"),o}function ccall(e,o,t,r,n){var i={string:function(e){var o=0;if(null!=e&&0!==e){var t=1+(e.length<<2);stringToUTF8(e,o=stackAlloc(t),t)}return o},array:function(e){var o=stackAlloc(e.length);return writeArrayToMemory(e,o),o}};var s=getCFunc(e),a=[],d=0;if(assert("array"!==o,'Return type should not be "array".'),r)for(var u=0;u<r.length;u++){var l=i[t[u]];l?(0===d&&(d=stackSave()),a[u]=l(r[u])):a[u]=r[u]}var c=s.apply(null,a);return c=function(e){return 0!==d&&stackRestore(d),function(e){return"string"===o?UTF8ToString(e):"boolean"===o?Boolean(e):e}(e)}(c)}function cwrap(e,o,t,r){return function(){return ccall(e,o,t,arguments,r)}}var ALLOC_NORMAL=0,ALLOC_STACK=1;function allocate(e,o){var t;return assert("number"==typeof o,"allocate no longer takes a type argument"),assert("number"!=typeof e,"allocate no longer takes a number as arg0"),t=o==ALLOC_STACK?stackAlloc(e.length):_malloc(e.length),e.subarray||e.slice?HEAPU8.set(e,t):HEAPU8.set(new Uint8Array(e),t),t}var UTF8Decoder="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function UTF8ArrayToString(e,o,t){for(var r=o+t,n=o;e[n]&&!(n>=r);)++n;if(n-o>16&&e.subarray&&UTF8Decoder)return UTF8Decoder.decode(e.subarray(o,n));for(var i="";o<n;){var s=e[o++];if(128&s){var a=63&e[o++];if(192!=(224&s)){var d=63&e[o++];if(224==(240&s)?s=(15&s)<<12|a<<6|d:(240!=(248&s)&&warnOnce("Invalid UTF-8 leading byte 0x"+s.toString(16)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!"),s=(7&s)<<18|a<<12|d<<6|63&e[o++]),s<65536)i+=String.fromCharCode(s);else{var u=s-65536;i+=String.fromCharCode(55296|u>>10,56320|1023&u)}}else i+=String.fromCharCode((31&s)<<6|a)}else i+=String.fromCharCode(s)}return i}function UTF8ToString(e,o){return e?UTF8ArrayToString(HEAPU8,e,o):""}function stringToUTF8Array(e,o,t,r){if(!(r>0))return 0;for(var n=t,i=t+r-1,s=0;s<e.length;++s){var a=e.charCodeAt(s);if(a>=55296&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++s);if(a<=127){if(t>=i)break;o[t++]=a}else if(a<=2047){if(t+1>=i)break;o[t++]=192|a>>6,o[t++]=128|63&a}else if(a<=65535){if(t+2>=i)break;o[t++]=224|a>>12,o[t++]=128|a>>6&63,o[t++]=128|63&a}else{if(t+3>=i)break;a>1114111&&warnOnce("Invalid Unicode code point 0x"+a.toString(16)+" encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF)."),o[t++]=240|a>>18,o[t++]=128|a>>12&63,o[t++]=128|a>>6&63,o[t++]=128|63&a}}return o[t]=0,t-n}function stringToUTF8(e,o,t){return assert("number"==typeof t,"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),stringToUTF8Array(e,HEAPU8,o,t)}function lengthBytesUTF8(e){for(var o=0,t=0;t<e.length;++t){var r=e.charCodeAt(t);r>=55296&&r<=57343&&(r=65536+((1023&r)<<10)|1023&e.charCodeAt(++t)),r<=127?++o:o+=r<=2047?2:r<=65535?3:4}return o}function AsciiToString(e){for(var o="";;){var t=HEAPU8[e++|0];if(!t)return o;o+=String.fromCharCode(t)}}function stringToAscii(e,o){return writeAsciiToMemory(e,o,!1)}var HEAP,buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64,UTF16Decoder="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function UTF16ToString(e,o){assert(e%2==0,"Pointer passed to UTF16ToString must be aligned to two bytes!");for(var t=e,r=t>>1,n=r+o/2;!(r>=n)&&HEAPU16[r];)++r;if((t=r<<1)-e>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(e,t));for(var i="",s=0;!(s>=o/2);++s){var a=HEAP16[e+2*s>>1];if(0==a)break;i+=String.fromCharCode(a)}return i}function stringToUTF16(e,o,t){if(assert(o%2==0,"Pointer passed to stringToUTF16 must be aligned to two bytes!"),assert("number"==typeof t,"stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),void 0===t&&(t=2147483647),t<2)return 0;for(var r=o,n=(t-=2)<2*e.length?t/2:e.length,i=0;i<n;++i){var s=e.charCodeAt(i);HEAP16[o>>1]=s,o+=2}return HEAP16[o>>1]=0,o-r}function lengthBytesUTF16(e){return 2*e.length}function UTF32ToString(e,o){assert(e%4==0,"Pointer passed to UTF32ToString must be aligned to four bytes!");for(var t=0,r="";!(t>=o/4);){var n=HEAP32[e+4*t>>2];if(0==n)break;if(++t,n>=65536){var i=n-65536;r+=String.fromCharCode(55296|i>>10,56320|1023&i)}else r+=String.fromCharCode(n)}return r}function stringToUTF32(e,o,t){if(assert(o%4==0,"Pointer passed to stringToUTF32 must be aligned to four bytes!"),assert("number"==typeof t,"stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),void 0===t&&(t=2147483647),t<4)return 0;for(var r=o,n=r+t-4,i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++i);if(HEAP32[o>>2]=s,(o+=4)+4>n)break}return HEAP32[o>>2]=0,o-r}function lengthBytesUTF32(e){for(var o=0,t=0;t<e.length;++t){var r=e.charCodeAt(t);r>=55296&&r<=57343&&++t,o+=4}return o}function allocateUTF8(e){var o=lengthBytesUTF8(e)+1,t=_malloc(o);return t&&stringToUTF8Array(e,HEAP8,t,o),t}function allocateUTF8OnStack(e){var o=lengthBytesUTF8(e)+1,t=stackAlloc(o);return stringToUTF8Array(e,HEAP8,t,o),t}function writeStringToMemory(e,o,t){var r,n;warnOnce("writeStringToMemory is deprecated and should not be called! Use stringToUTF8() instead!"),t&&(n=o+lengthBytesUTF8(e),r=HEAP8[n]),stringToUTF8(e,o,1/0),t&&(HEAP8[n]=r)}function writeArrayToMemory(e,o){assert(e.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),HEAP8.set(e,o)}function writeAsciiToMemory(e,o,t){for(var r=0;r<e.length;++r)assert(e.charCodeAt(r)===(255&e.charCodeAt(r))),HEAP8[o++|0]=e.charCodeAt(r);t||(HEAP8[o|0]=0)}function alignUp(e,o){return e%o>0&&(e+=o-e%o),e}function updateGlobalBufferAndViews(e){buffer=e,RoomsDivisionModule.HEAP8=HEAP8=new Int8Array(e),RoomsDivisionModule.HEAP16=HEAP16=new Int16Array(e),RoomsDivisionModule.HEAP32=HEAP32=new Int32Array(e),RoomsDivisionModule.HEAPU8=HEAPU8=new Uint8Array(e),RoomsDivisionModule.HEAPU16=HEAPU16=new Uint16Array(e),RoomsDivisionModule.HEAPU32=HEAPU32=new Uint32Array(e),RoomsDivisionModule.HEAPF32=HEAPF32=new Float32Array(e),RoomsDivisionModule.HEAPF64=HEAPF64=new Float64Array(e)}var TOTAL_STACK=5242880;RoomsDivisionModule.TOTAL_STACK&&assert(TOTAL_STACK===RoomsDivisionModule.TOTAL_STACK,"the stack size can no longer be determined at runtime");var wasmTable,INITIAL_MEMORY=RoomsDivisionModule.INITIAL_MEMORY||16777216;function writeStackCookie(){var e=_emscripten_stack_get_end();assert(!(3&e)),HEAP32[e+4>>2]=34821223,HEAP32[e+8>>2]=2310721022,HEAP32[0]=1668509029}function checkStackCookie(){if(!ABORT){var e=_emscripten_stack_get_end(),o=HEAPU32[e+4>>2],t=HEAPU32[e+8>>2];34821223==o&&2310721022==t||abort("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x"+t.toString(16)+" 0x"+o.toString(16)),1668509029!==HEAP32[0]&&abort("Runtime error: The application has corrupted its heap memory area (address zero)!")}}Object.getOwnPropertyDescriptor(RoomsDivisionModule,"INITIAL_MEMORY")||Object.defineProperty(RoomsDivisionModule,"INITIAL_MEMORY",{configurable:!0,get:function(){abort("RoomsDivisionModule.INITIAL_MEMORY has been replaced with plain INITIAL_MEMORY (the initial value can be provided on RoomsDivisionModule, but after startup the value is only looked for on a local variable of that name)")}}),assert(INITIAL_MEMORY>=TOTAL_STACK,"INITIAL_MEMORY should be larger than TOTAL_STACK, was "+INITIAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")"),assert("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),assert(!RoomsDivisionModule.wasmMemory,"Use of `wasmMemory` detected.  Use -s IMPORTED_MEMORY to define wasmMemory externally"),assert(16777216==INITIAL_MEMORY,"Detected runtime INITIAL_MEMORY setting.  Use -s IMPORTED_MEMORY to define wasmMemory dynamically"),function(){var e=new Int16Array(1),o=new Int8Array(e.buffer);if(e[0]=25459,115!==o[0]||99!==o[1])throw"Runtime error: expected the system to be little-endian! (Run with -s SUPPORT_BIG_ENDIAN=1 to bypass)"}();var __ATPRERUN__=[],__ATINIT__=[],__ATEXIT__=[],__ATPOSTRUN__=[],runtimeInitialized=!1,runtimeExited=!1,runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(RoomsDivisionModule.preRun)for("function"==typeof RoomsDivisionModule.preRun&&(RoomsDivisionModule.preRun=[RoomsDivisionModule.preRun]);RoomsDivisionModule.preRun.length;)addOnPreRun(RoomsDivisionModule.preRun.shift());callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){checkStackCookie(),assert(!runtimeInitialized),runtimeInitialized=!0,RoomsDivisionModule.noFSInit||FS.init.initialized||FS.init(),FS.ignorePermissions=!1,TTY.init(),callRuntimeCallbacks(__ATINIT__)}function exitRuntime(){checkStackCookie(),runtimeExited=!0}function postRun(){if(checkStackCookie(),RoomsDivisionModule.postRun)for("function"==typeof RoomsDivisionModule.postRun&&(RoomsDivisionModule.postRun=[RoomsDivisionModule.postRun]);RoomsDivisionModule.postRun.length;)addOnPostRun(RoomsDivisionModule.postRun.shift());callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(e){__ATPRERUN__.unshift(e)}function addOnInit(e){__ATINIT__.unshift(e)}function addOnExit(e){}function addOnPostRun(e){__ATPOSTRUN__.unshift(e)}assert(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),assert(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),assert(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),assert(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var runDependencies=0,runDependencyWatcher=null,dependenciesFulfilled=null,runDependencyTracking={};function getUniqueRunDependency(e){for(var o=e;;){if(!runDependencyTracking[e])return e;e=o+Math.random()}}function addRunDependency(e){runDependencies++,RoomsDivisionModule.monitorRunDependencies&&RoomsDivisionModule.monitorRunDependencies(runDependencies),e?(assert(!runDependencyTracking[e]),runDependencyTracking[e]=1,null===runDependencyWatcher&&"undefined"!=typeof setInterval&&(runDependencyWatcher=setInterval((function(){if(ABORT)return clearInterval(runDependencyWatcher),void(runDependencyWatcher=null);var e=!1;for(var o in runDependencyTracking)e||(e=!0,err("still waiting on run dependencies:")),err("dependency: "+o);e&&err("(end of list)")}),1e4))):err("warning: run dependency added without ID")}function removeRunDependency(e){if(runDependencies--,RoomsDivisionModule.monitorRunDependencies&&RoomsDivisionModule.monitorRunDependencies(runDependencies),e?(assert(runDependencyTracking[e]),delete runDependencyTracking[e]):err("warning: run dependency removed without ID"),0==runDependencies&&(null!==runDependencyWatcher&&(clearInterval(runDependencyWatcher),runDependencyWatcher=null),dependenciesFulfilled)){var o=dependenciesFulfilled;dependenciesFulfilled=null,o()}}function abort(e){throw RoomsDivisionModule.onAbort&&RoomsDivisionModule.onAbort(e),err(e="Aborted("+e+")"),ABORT=!0,EXITSTATUS=1,new WebAssembly.RuntimeError(e)}RoomsDivisionModule.preloadedImages={},RoomsDivisionModule.preloadedAudios={};var wasmBinaryFile,tempDouble,tempI64,dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(e){return e.startsWith(dataURIPrefix)}function isFileURI(e){return e.startsWith("file://")}function createExportWrapper(e,o){return function(){var t=e,r=o;return o||(r=RoomsDivisionModule.asm),assert(runtimeInitialized,"native function `"+t+"` called before runtime initialization"),assert(!runtimeExited,"native function `"+t+"` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),r[e]||assert(r[e],"exported native function `"+t+"` not found"),r[e].apply(null,arguments)}}function getBinary(e){try{if(e==wasmBinaryFile&&wasmBinary)return new Uint8Array(wasmBinary);if(readBinary)return readBinary(e);throw"both async and sync fetching of the wasm failed"}catch(e){abort(e)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if("function"==typeof fetch&&!isFileURI(wasmBinaryFile))return fetch(wasmBinaryFile,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+wasmBinaryFile+"'";return e.arrayBuffer()})).catch((function(){return getBinary(wasmBinaryFile)}));if(readAsync)return new Promise((function(e,o){readAsync(wasmBinaryFile,(function(o){e(new Uint8Array(o))}),o)}))}return Promise.resolve().then((function(){return getBinary(wasmBinaryFile)}))}function createWasm(){var e={env:asmLibraryArg,wasi_snapshot_preview1:asmLibraryArg};function o(e,o){var t=e.exports;RoomsDivisionModule.asm=t,assert(wasmMemory=RoomsDivisionModule.asm.memory,"memory not found in wasm exports"),updateGlobalBufferAndViews(wasmMemory.buffer),assert(wasmTable=RoomsDivisionModule.asm.__indirect_function_table,"table not found in wasm exports"),addOnInit(RoomsDivisionModule.asm.__wasm_call_ctors),removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");var t=RoomsDivisionModule;function r(e){assert(RoomsDivisionModule===t,"the RoomsDivisionModule object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),t=null,o(e.instance)}function n(o){return getBinaryPromise().then((function(o){return WebAssembly.instantiate(o,e)})).then((function(e){return e})).then(o,(function(e){err("failed to asynchronously prepare wasm: "+e),isFileURI(wasmBinaryFile)&&err("warning: Loading from a file URI ("+wasmBinaryFile+") is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing"),abort(e)}))}if(RoomsDivisionModule.instantiateWasm)try{return RoomsDivisionModule.instantiateWasm(e,o)}catch(e){return err("RoomsDivisionModule.instantiateWasm callback failed with error: "+e),!1}return wasmBinary||"function"!=typeof WebAssembly.instantiateStreaming||isDataURI(wasmBinaryFile)||isFileURI(wasmBinaryFile)||"function"!=typeof fetch?n(r):fetch(wasmBinaryFile,{credentials:"same-origin"}).then((function(o){return WebAssembly.instantiateStreaming(o,e).then(r,(function(e){return err("wasm streaming compile failed: "+e),err("falling back to ArrayBuffer instantiation"),n(r)}))})),{}}isDataURI(wasmBinaryFile="RoomsDivision.wasm")||(wasmBinaryFile=locateFile(wasmBinaryFile));var ASM_CONSTS={};function callRuntimeCallbacks(e){for(;e.length>0;){var o=e.shift();if("function"!=typeof o){var t=o.func;"number"==typeof t?void 0===o.arg?getWasmTableEntry(t)():getWasmTableEntry(t)(o.arg):t(void 0===o.arg?null:o.arg)}else o(RoomsDivisionModule)}}function withStackSave(e){var o=stackSave(),t=e();return stackRestore(o),t}function demangle(e){return warnOnce("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),e}function demangleAll(e){return e.replace(/\b_Z[\w\d_]+/g,(function(e){var o=demangle(e);return e===o?e:o+" ["+e+"]"}))}var wasmTableMirror=[];function getWasmTableEntry(e){var o=wasmTableMirror[e];return o||(e>=wasmTableMirror.length&&(wasmTableMirror.length=e+1),wasmTableMirror[e]=o=wasmTable.get(e)),assert(wasmTable.get(e)==o,"JavaScript-side Wasm function table mirror is out of date!"),o}function handleException(e){if(e instanceof ExitStatus||"unwind"==e)return EXITSTATUS;quit_(1,e)}function jsStackTrace(){var e=new Error;if(!e.stack){try{throw new Error}catch(o){e=o}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}function setWasmTableEntry(e,o){wasmTable.set(e,o),wasmTableMirror[e]=o}function stackTrace(){var e=jsStackTrace();return RoomsDivisionModule.extraStackTrace&&(e+="\n"+RoomsDivisionModule.extraStackTrace()),demangleAll(e)}function ___cxa_allocate_exception(e){return _malloc(e+16)+16}function _atexit(e,o){}function ___cxa_atexit(e,o){return _atexit(e,o)}function ExceptionInfo(e){this.excPtr=e,this.ptr=e-16,this.set_type=function(e){HEAP32[this.ptr+4>>2]=e},this.get_type=function(){return HEAP32[this.ptr+4>>2]},this.set_destructor=function(e){HEAP32[this.ptr+8>>2]=e},this.get_destructor=function(){return HEAP32[this.ptr+8>>2]},this.set_refcount=function(e){HEAP32[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,HEAP8[this.ptr+12|0]=e},this.get_caught=function(){return 0!=HEAP8[this.ptr+12|0]},this.set_rethrown=function(e){e=e?1:0,HEAP8[this.ptr+13|0]=e},this.get_rethrown=function(){return 0!=HEAP8[this.ptr+13|0]},this.init=function(e,o){this.set_type(e),this.set_destructor(o),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=e+1},this.release_ref=function(){var e=HEAP32[this.ptr>>2];return HEAP32[this.ptr>>2]=e-1,assert(e>0),1===e}}var exceptionLast=0,uncaughtExceptionCount=0;function ___cxa_throw(e,o,t){throw new ExceptionInfo(e).init(o,t),exceptionLast=e,uncaughtExceptionCount++,e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch."}function __embind_register_bigint(e,o,t,r,n){}function getShiftFromSize(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}function embind_init_charCodes(){for(var e=new Array(256),o=0;o<256;++o)e[o]=String.fromCharCode(o);embind_charCodes=e}var embind_charCodes=void 0;function readLatin1String(e){for(var o="",t=e;HEAPU8[t];)o+=embind_charCodes[HEAPU8[t++]];return o}var awaitingDependencies={},registeredTypes={},typeDependencies={},char_0=48,char_9=57;function makeLegalFunctionName(e){if(void 0===e)return"_unknown";var o=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return o>=char_0&&o<=char_9?"_"+e:e}function createNamedFunction(e,o){return e=makeLegalFunctionName(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(o)}function extendError(e,o){var t=createNamedFunction(o,(function(e){this.name=o,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var BindingError=void 0;function throwBindingError(e){throw new BindingError(e)}var InternalError=void 0;function throwInternalError(e){throw new InternalError(e)}function whenDependentTypesAreResolved(e,o,t){function r(o){var r=t(o);r.length!==e.length&&throwInternalError("Mismatched type converter count");for(var n=0;n<e.length;++n)registerType(e[n],r[n])}e.forEach((function(e){typeDependencies[e]=o}));var n=new Array(o.length),i=[],s=0;o.forEach((function(e,o){registeredTypes.hasOwnProperty(e)?n[o]=registeredTypes[e]:(i.push(e),awaitingDependencies.hasOwnProperty(e)||(awaitingDependencies[e]=[]),awaitingDependencies[e].push((function(){n[o]=registeredTypes[e],++s===i.length&&r(n)})))})),0===i.length&&r(n)}function registerType(e,o,t){if(t=t||{},!("argPackAdvance"in o))throw new TypeError("registerType registeredInstance requires argPackAdvance");var r=o.name;if(e||throwBindingError('type "'+r+'" must have a positive integer typeid pointer'),registeredTypes.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;throwBindingError("Cannot register type '"+r+"' twice")}if(registeredTypes[e]=o,delete typeDependencies[e],awaitingDependencies.hasOwnProperty(e)){var n=awaitingDependencies[e];delete awaitingDependencies[e],n.forEach((function(e){e()}))}}function __embind_register_bool(e,o,t,r,n){var i=getShiftFromSize(t);registerType(e,{name:o=readLatin1String(o),fromWireType:function(e){return!!e},toWireType:function(e,o){return o?r:n},argPackAdvance:8,readValueFromPointer:function(e){var r;if(1===t)r=HEAP8;else if(2===t)r=HEAP16;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+o);r=HEAP32}return this.fromWireType(r[e>>i])},destructorFunction:null})}var emval_free_list=[],emval_handle_array=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function __emval_decref(e){e>4&&0==--emval_handle_array[e].refcount&&(emval_handle_array[e]=void 0,emval_free_list.push(e))}function count_emval_handles(){for(var e=0,o=5;o<emval_handle_array.length;++o)void 0!==emval_handle_array[o]&&++e;return e}function get_first_emval(){for(var e=5;e<emval_handle_array.length;++e)if(void 0!==emval_handle_array[e])return emval_handle_array[e];return null}function init_emval(){RoomsDivisionModule.count_emval_handles=count_emval_handles,RoomsDivisionModule.get_first_emval=get_first_emval}var Emval={toValue:function(e){return e||throwBindingError("Cannot use deleted val. handle = "+e),emval_handle_array[e].value},toHandle:function(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var o=emval_free_list.length?emval_free_list.pop():emval_handle_array.length;return emval_handle_array[o]={refcount:1,value:e},o}}};function simpleReadValueFromPointer(e){return this.fromWireType(HEAPU32[e>>2])}function __embind_register_emval(e,o){registerType(e,{name:o=readLatin1String(o),fromWireType:function(e){var o=Emval.toValue(e);return __emval_decref(e),o},toWireType:function(e,o){return Emval.toHandle(o)},argPackAdvance:8,readValueFromPointer:simpleReadValueFromPointer,destructorFunction:null})}function _embind_repr(e){if(null===e)return"null";var o=typeof e;return"object"===o||"array"===o||"function"===o?e.toString():""+e}function floatReadValueFromPointer(e,o){switch(o){case 2:return function(e){return this.fromWireType(HEAPF32[e>>2])};case 3:return function(e){return this.fromWireType(HEAPF64[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function __embind_register_float(e,o,t){var r=getShiftFromSize(t);registerType(e,{name:o=readLatin1String(o),fromWireType:function(e){return e},toWireType:function(e,o){if("number"!=typeof o&&"boolean"!=typeof o)throw new TypeError('Cannot convert "'+_embind_repr(o)+'" to '+this.name);return o},argPackAdvance:8,readValueFromPointer:floatReadValueFromPointer(o,r),destructorFunction:null})}function new_(e,o){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=createNamedFunction(e.name||"unknownFunctionName",(function(){}));t.prototype=e.prototype;var r=new t,n=e.apply(r,o);return n instanceof Object?n:r}function runDestructors(e){for(;e.length;){var o=e.pop();e.pop()(o)}}function craftInvokerFunction(e,o,t,r,n){var i=o.length;i<2&&throwBindingError("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==o[1]&&null!==t,a=!1,d=1;d<o.length;++d)if(null!==o[d]&&void 0===o[d].destructorFunction){a=!0;break}var u="void"!==o[0].name,l="",c="";for(d=0;d<i-2;++d)l+=(0!==d?", ":"")+"arg"+d,c+=(0!==d?", ":"")+"arg"+d+"Wired";var p="return function "+makeLegalFunctionName(e)+"("+l+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";a&&(p+="var destructors = [];\n");var E=a?"destructors":"null",m=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],D=[throwBindingError,r,n,runDestructors,o[0],o[1]];s&&(p+="var thisWired = classParam.toWireType("+E+", this);\n");for(d=0;d<i-2;++d)p+="var arg"+d+"Wired = argType"+d+".toWireType("+E+", arg"+d+"); // "+o[d+2].name+"\n",m.push("argType"+d),D.push(o[d+2]);if(s&&(c="thisWired"+(c.length>0?", ":"")+c),p+=(u?"var rv = ":"")+"invoker(fn"+(c.length>0?", ":"")+c+");\n",a)p+="runDestructors(destructors);\n";else for(d=s?1:2;d<o.length;++d){var f=1===d?"thisWired":"arg"+(d-2)+"Wired";null!==o[d].destructorFunction&&(p+=f+"_dtor("+f+"); // "+o[d].name+"\n",m.push(f+"_dtor"),D.push(o[d].destructorFunction))}return u&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",m.push(p),new_(Function,m).apply(null,D)}function ensureOverloadTable(e,o,t){if(void 0===e[o].overloadTable){var r=e[o];e[o]=function(){return e[o].overloadTable.hasOwnProperty(arguments.length)||throwBindingError("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[o].overloadTable+")!"),e[o].overloadTable[arguments.length].apply(this,arguments)},e[o].overloadTable=[],e[o].overloadTable[r.argCount]=r}}function exposePublicSymbol(e,o,t){RoomsDivisionModule.hasOwnProperty(e)?((void 0===t||void 0!==RoomsDivisionModule[e].overloadTable&&void 0!==RoomsDivisionModule[e].overloadTable[t])&&throwBindingError("Cannot register public name '"+e+"' twice"),ensureOverloadTable(RoomsDivisionModule,e,e),RoomsDivisionModule.hasOwnProperty(t)&&throwBindingError("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),RoomsDivisionModule[e].overloadTable[t]=o):(RoomsDivisionModule[e]=o,void 0!==t&&(RoomsDivisionModule[e].numArguments=t))}function heap32VectorToArray(e,o){for(var t=[],r=0;r<e;r++)t.push(HEAP32[(o>>2)+r]);return t}function replacePublicSymbol(e,o,t){RoomsDivisionModule.hasOwnProperty(e)||throwInternalError("Replacing nonexistant public symbol"),void 0!==RoomsDivisionModule[e].overloadTable&&void 0!==t?RoomsDivisionModule[e].overloadTable[t]=o:(RoomsDivisionModule[e]=o,RoomsDivisionModule[e].argCount=t)}function dynCallLegacy(e,o,t){assert("dynCall_"+e in RoomsDivisionModule,"bad function pointer type - no table for sig '"+e+"'"),t&&t.length?assert(t.length===e.substring(1).replace(/j/g,"--").length):assert(1==e.length);var r=RoomsDivisionModule["dynCall_"+e];return t&&t.length?r.apply(null,[o].concat(t)):r.call(null,o)}function dynCall(e,o,t){return e.includes("j")?dynCallLegacy(e,o,t):(assert(getWasmTableEntry(o),"missing table entry in dynCall: "+o),getWasmTableEntry(o).apply(null,t))}function getDynCaller(e,o){assert(e.includes("j"),"getDynCaller should only be called with i64 sigs");var t=[];return function(){t.length=arguments.length;for(var r=0;r<arguments.length;r++)t[r]=arguments[r];return dynCall(e,o,t)}}function embind__requireFunction(e,o){var t=(e=readLatin1String(e)).includes("j")?getDynCaller(e,o):getWasmTableEntry(o);return"function"!=typeof t&&throwBindingError("unknown function pointer with signature "+e+": "+o),t}var UnboundTypeError=void 0;function getTypeName(e){var o=___getTypeName(e),t=readLatin1String(o);return _free(o),t}function throwUnboundTypeError(e,o){var t=[],r={};throw o.forEach((function e(o){r[o]||registeredTypes[o]||(typeDependencies[o]?typeDependencies[o].forEach(e):(t.push(o),r[o]=!0))})),new UnboundTypeError(e+": "+t.map(getTypeName).join([", "]))}function __embind_register_function(e,o,t,r,n,i){var s=heap32VectorToArray(o,t);e=readLatin1String(e),n=embind__requireFunction(r,n),exposePublicSymbol(e,(function(){throwUnboundTypeError("Cannot call "+e+" due to unbound types",s)}),o-1),whenDependentTypesAreResolved([],s,(function(t){var r=[t[0],null].concat(t.slice(1));return replacePublicSymbol(e,craftInvokerFunction(e,r,null,n,i),o-1),[]}))}function integerReadValueFromPointer(e,o,t){switch(o){case 0:return t?function(e){return HEAP8[e]}:function(e){return HEAPU8[e]};case 1:return t?function(e){return HEAP16[e>>1]}:function(e){return HEAPU16[e>>1]};case 2:return t?function(e){return HEAP32[e>>2]}:function(e){return HEAPU32[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function __embind_register_integer(e,o,t,r,n){o=readLatin1String(o),-1===n&&(n=4294967295);var i=getShiftFromSize(t),s=function(e){return e};if(0===r){var a=32-8*t;s=function(e){return e<<a>>>a}}var d=o.includes("unsigned");registerType(e,{name:o,fromWireType:s,toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+_embind_repr(t)+'" to '+this.name);if(t<r||t>n)throw new TypeError('Passing a number "'+_embind_repr(t)+'" from JS side to C/C++ side to an argument of type "'+o+'", which is outside the valid range ['+r+", "+n+"]!");return d?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:integerReadValueFromPointer(o,i,0!==r),destructorFunction:null})}function __embind_register_memory_view(e,o,t){var r=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][o];function n(e){var o=HEAPU32,t=o[e>>=2],n=o[e+1];return new r(buffer,n,t)}registerType(e,{name:t=readLatin1String(t),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ignoreDuplicateRegistrations:!0})}function __embind_register_std_string(e,o){var t="std::string"===(o=readLatin1String(o));registerType(e,{name:o,fromWireType:function(e){var o,r=HEAPU32[e>>2];if(t)for(var n=e+4,i=0;i<=r;++i){var s=e+4+i;if(i==r||0==HEAPU8[s]){var a=UTF8ToString(n,s-n);void 0===o?o=a:(o+=String.fromCharCode(0),o+=a),n=s+1}}else{var d=new Array(r);for(i=0;i<r;++i)d[i]=String.fromCharCode(HEAPU8[e+4+i]);o=d.join("")}return _free(e),o},toWireType:function(e,o){o instanceof ArrayBuffer&&(o=new Uint8Array(o));var r="string"==typeof o;r||o instanceof Uint8Array||o instanceof Uint8ClampedArray||o instanceof Int8Array||throwBindingError("Cannot pass non-string to std::string");var n=(t&&r?function(){return lengthBytesUTF8(o)}:function(){return o.length})(),i=_malloc(4+n+1);if(HEAPU32[i>>2]=n,t&&r)stringToUTF8(o,i+4,n+1);else if(r)for(var s=0;s<n;++s){var a=o.charCodeAt(s);a>255&&(_free(i),throwBindingError("String has UTF-16 code units that do not fit in 8 bits")),HEAPU8[i+4+s]=a}else for(s=0;s<n;++s)HEAPU8[i+4+s]=o[s];return null!==e&&e.push(_free,i),i},argPackAdvance:8,readValueFromPointer:simpleReadValueFromPointer,destructorFunction:function(e){_free(e)}})}function __embind_register_std_wstring(e,o,t){var r,n,i,s,a;t=readLatin1String(t),2===o?(r=UTF16ToString,n=stringToUTF16,s=lengthBytesUTF16,i=function(){return HEAPU16},a=1):4===o&&(r=UTF32ToString,n=stringToUTF32,s=lengthBytesUTF32,i=function(){return HEAPU32},a=2),registerType(e,{name:t,fromWireType:function(e){for(var t,n=HEAPU32[e>>2],s=i(),d=e+4,u=0;u<=n;++u){var l=e+4+u*o;if(u==n||0==s[l>>a]){var c=r(d,l-d);void 0===t?t=c:(t+=String.fromCharCode(0),t+=c),d=l+o}}return _free(e),t},toWireType:function(e,r){"string"!=typeof r&&throwBindingError("Cannot pass non-string to C++ string type "+t);var i=s(r),d=_malloc(4+i+o);return HEAPU32[d>>2]=i>>a,n(r,d+4,i+o),null!==e&&e.push(_free,d),d},argPackAdvance:8,readValueFromPointer:simpleReadValueFromPointer,destructorFunction:function(e){_free(e)}})}function __embind_register_void(e,o){registerType(e,{isVoid:!0,name:o=readLatin1String(o),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,o){}})}function _abort(){abort("native code called abort()")}function _emscripten_memcpy_big(e,o,t){HEAPU8.copyWithin(e,o,o+t)}function abortOnCannotGrowMemory(e){abort("Cannot enlarge memory arrays to size "+e+" bytes (OOM). Either (1) compile with  -s INITIAL_MEMORY=X  with X higher than the current value "+HEAP8.length+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function _emscripten_resize_heap(e){HEAPU8.length;abortOnCannotGrowMemory(e>>>=0)}var ENV={};function getExecutableName(){return thisProgram||"./this.program"}function getEnvStrings(){if(!getEnvStrings.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:getExecutableName()};for(var o in ENV)void 0===ENV[o]?delete e[o]:e[o]=ENV[o];var t=[];for(var o in e)t.push(o+"="+e[o]);getEnvStrings.strings=t}return getEnvStrings.strings}var PATH={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,o){for(var t=0,r=e.length-1;r>=0;r--){var n=e[r];"."===n?e.splice(r,1):".."===n?(e.splice(r,1),t++):t&&(e.splice(r,1),t--)}if(o)for(;t;t--)e.unshift("..");return e},normalize:function(e){var o="/"===e.charAt(0),t="/"===e.substr(-1);return(e=PATH.normalizeArray(e.split("/").filter((function(e){return!!e})),!o).join("/"))||o||(e="."),e&&t&&(e+="/"),(o?"/":"")+e},dirname:function(e){var o=PATH.splitPath(e),t=o[0],r=o[1];return t||r?(r&&(r=r.substr(0,r.length-1)),t+r):"."},basename:function(e){if("/"===e)return"/";var o=(e=(e=PATH.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===o?e:e.substr(o+1)},extname:function(e){return PATH.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return PATH.normalize(e.join("/"))},join2:function(e,o){return PATH.normalize(e+"/"+o)}};function getRandomDevice(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return function(){return crypto.getRandomValues(e),e[0]}}if(ENVIRONMENT_IS_NODE)try{var o=require("crypto");return function(){return o.randomBytes(1)[0]}}catch(e){}return function(){abort("no cryptographic support found for randomDevice. consider polyfilling it if you want to use something insecure like Math.random(), e.g. put this in a --pre-js: var crypto = { getRandomValues: function(array) { for (var i = 0; i < array.length; i++) array[i] = (Math.random()*256)|0 } };")}}var PATH_FS={resolve:function(){for(var e="",o=!1,t=arguments.length-1;t>=-1&&!o;t--){var r=t>=0?arguments[t]:FS.cwd();if("string"!=typeof r)throw new TypeError("Arguments to path.resolve must be strings");if(!r)return"";e=r+"/"+e,o="/"===r.charAt(0)}return(o?"/":"")+(e=PATH.normalizeArray(e.split("/").filter((function(e){return!!e})),!o).join("/"))||"."},relative:function(e,o){function t(e){for(var o=0;o<e.length&&""===e[o];o++);for(var t=e.length-1;t>=0&&""===e[t];t--);return o>t?[]:e.slice(o,t-o+1)}e=PATH_FS.resolve(e).substr(1),o=PATH_FS.resolve(o).substr(1);for(var r=t(e.split("/")),n=t(o.split("/")),i=Math.min(r.length,n.length),s=i,a=0;a<i;a++)if(r[a]!==n[a]){s=a;break}var d=[];for(a=s;a<r.length;a++)d.push("..");return(d=d.concat(n.slice(s))).join("/")}},TTY={ttys:[],init:function(){},shutdown:function(){},register:function(e,o){TTY.ttys[e]={input:[],output:[],ops:o},FS.registerDevice(e,TTY.stream_ops)},stream_ops:{open:function(e){var o=TTY.ttys[e.node.rdev];if(!o)throw new FS.ErrnoError(43);e.tty=o,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,o,t,r,n){if(!e.tty||!e.tty.ops.get_char)throw new FS.ErrnoError(60);for(var i=0,s=0;s<r;s++){var a;try{a=e.tty.ops.get_char(e.tty)}catch(e){throw new FS.ErrnoError(29)}if(void 0===a&&0===i)throw new FS.ErrnoError(6);if(null==a)break;i++,o[t+s]=a}return i&&(e.node.timestamp=Date.now()),i},write:function(e,o,t,r,n){if(!e.tty||!e.tty.ops.put_char)throw new FS.ErrnoError(60);try{for(var i=0;i<r;i++)e.tty.ops.put_char(e.tty,o[t+i])}catch(e){throw new FS.ErrnoError(29)}return r&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){if(!e.input.length){var o=null;if(ENVIRONMENT_IS_NODE){var t=Buffer.alloc(256),r=0;try{r=nodeFS.readSync(process.stdin.fd,t,0,256,null)}catch(e){if(!e.toString().includes("EOF"))throw e;r=0}o=r>0?t.slice(0,r).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(o=window.prompt("Input: "))&&(o+="\n"):"function"==typeof readline&&null!==(o=readline())&&(o+="\n");if(!o)return null;e.input=intArrayFromString(o,!0)}return e.input.shift()},put_char:function(e,o){null===o||10===o?(out(UTF8ArrayToString(e.output,0)),e.output=[]):0!=o&&e.output.push(o)},flush:function(e){e.output&&e.output.length>0&&(out(UTF8ArrayToString(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,o){null===o||10===o?(err(UTF8ArrayToString(e.output,0)),e.output=[]):0!=o&&e.output.push(o)},flush:function(e){e.output&&e.output.length>0&&(err(UTF8ArrayToString(e.output,0)),e.output=[])}}};function zeroMemory(e,o){HEAPU8.fill(0,e,e+o)}function alignMemory(e,o){return assert(o,"alignment argument is required"),Math.ceil(e/o)*o}function mmapAlloc(e){abort("internal error: mmapAlloc called but `memalign` native symbol not exported")}var MEMFS={ops_table:null,mount:function(e){return MEMFS.createNode(null,"/",16895,0)},createNode:function(e,o,t,r){if(FS.isBlkdev(t)||FS.isFIFO(t))throw new FS.ErrnoError(63);MEMFS.ops_table||(MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}});var n=FS.createNode(e,o,t,r);return FS.isDir(n.mode)?(n.node_ops=MEMFS.ops_table.dir.node,n.stream_ops=MEMFS.ops_table.dir.stream,n.contents={}):FS.isFile(n.mode)?(n.node_ops=MEMFS.ops_table.file.node,n.stream_ops=MEMFS.ops_table.file.stream,n.usedBytes=0,n.contents=null):FS.isLink(n.mode)?(n.node_ops=MEMFS.ops_table.link.node,n.stream_ops=MEMFS.ops_table.link.stream):FS.isChrdev(n.mode)&&(n.node_ops=MEMFS.ops_table.chrdev.node,n.stream_ops=MEMFS.ops_table.chrdev.stream),n.timestamp=Date.now(),e&&(e.contents[o]=n,e.timestamp=n.timestamp),n},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,o){var t=e.contents?e.contents.length:0;if(!(t>=o)){o=Math.max(o,t*(t<1048576?2:1.125)>>>0),0!=t&&(o=Math.max(o,256));var r=e.contents;e.contents=new Uint8Array(o),e.usedBytes>0&&e.contents.set(r.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,o){if(e.usedBytes!=o)if(0==o)e.contents=null,e.usedBytes=0;else{var t=e.contents;e.contents=new Uint8Array(o),t&&e.contents.set(t.subarray(0,Math.min(o,e.usedBytes))),e.usedBytes=o}},node_ops:{getattr:function(e){var o={};return o.dev=FS.isChrdev(e.mode)?e.id:1,o.ino=e.id,o.mode=e.mode,o.nlink=1,o.uid=0,o.gid=0,o.rdev=e.rdev,FS.isDir(e.mode)?o.size=4096:FS.isFile(e.mode)?o.size=e.usedBytes:FS.isLink(e.mode)?o.size=e.link.length:o.size=0,o.atime=new Date(e.timestamp),o.mtime=new Date(e.timestamp),o.ctime=new Date(e.timestamp),o.blksize=4096,o.blocks=Math.ceil(o.size/o.blksize),o},setattr:function(e,o){void 0!==o.mode&&(e.mode=o.mode),void 0!==o.timestamp&&(e.timestamp=o.timestamp),void 0!==o.size&&MEMFS.resizeFileStorage(e,o.size)},lookup:function(e,o){throw FS.genericErrors[44]},mknod:function(e,o,t,r){return MEMFS.createNode(e,o,t,r)},rename:function(e,o,t){if(FS.isDir(e.mode)){var r;try{r=FS.lookupNode(o,t)}catch(e){}if(r)for(var n in r.contents)throw new FS.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=t,o.contents[t]=e,o.timestamp=e.parent.timestamp,e.parent=o},unlink:function(e,o){delete e.contents[o],e.timestamp=Date.now()},rmdir:function(e,o){var t=FS.lookupNode(e,o);for(var r in t.contents)throw new FS.ErrnoError(55);delete e.contents[o],e.timestamp=Date.now()},readdir:function(e){var o=[".",".."];for(var t in e.contents)e.contents.hasOwnProperty(t)&&o.push(t);return o},symlink:function(e,o,t){var r=MEMFS.createNode(e,o,41471,0);return r.link=t,r},readlink:function(e){if(!FS.isLink(e.mode))throw new FS.ErrnoError(28);return e.link}},stream_ops:{read:function(e,o,t,r,n){var i=e.node.contents;if(n>=e.node.usedBytes)return 0;var s=Math.min(e.node.usedBytes-n,r);if(assert(s>=0),s>8&&i.subarray)o.set(i.subarray(n,n+s),t);else for(var a=0;a<s;a++)o[t+a]=i[n+a];return s},write:function(e,o,t,r,n,i){if(assert(!(o instanceof ArrayBuffer)),!r)return 0;var s=e.node;if(s.timestamp=Date.now(),o.subarray&&(!s.contents||s.contents.subarray)){if(i)return assert(0===n,"canOwn must imply no weird position inside the file"),s.contents=o.subarray(t,t+r),s.usedBytes=r,r;if(0===s.usedBytes&&0===n)return s.contents=o.slice(t,t+r),s.usedBytes=r,r;if(n+r<=s.usedBytes)return s.contents.set(o.subarray(t,t+r),n),r}if(MEMFS.expandFileStorage(s,n+r),s.contents.subarray&&o.subarray)s.contents.set(o.subarray(t,t+r),n);else for(var a=0;a<r;a++)s.contents[n+a]=o[t+a];return s.usedBytes=Math.max(s.usedBytes,n+r),r},llseek:function(e,o,t){var r=o;if(1===t?r+=e.position:2===t&&FS.isFile(e.node.mode)&&(r+=e.node.usedBytes),r<0)throw new FS.ErrnoError(28);return r},allocate:function(e,o,t){MEMFS.expandFileStorage(e.node,o+t),e.node.usedBytes=Math.max(e.node.usedBytes,o+t)},mmap:function(e,o,t,r,n,i){if(0!==o)throw new FS.ErrnoError(28);if(!FS.isFile(e.node.mode))throw new FS.ErrnoError(43);var s,a,d=e.node.contents;if(2&i||d.buffer!==buffer){if((r>0||r+t<d.length)&&(d=d.subarray?d.subarray(r,r+t):Array.prototype.slice.call(d,r,r+t)),a=!0,!(s=mmapAlloc(t)))throw new FS.ErrnoError(48);HEAP8.set(d,s)}else a=!1,s=d.byteOffset;return{ptr:s,allocated:a}},msync:function(e,o,t,r,n){if(!FS.isFile(e.node.mode))throw new FS.ErrnoError(43);if(2&n)return 0;MEMFS.stream_ops.write(e,o,0,r,t,!1);return 0}}};function asyncLoad(e,o,t,r){var n=r?"":getUniqueRunDependency("al "+e);readAsync(e,(function(t){assert(t,'Loading data file "'+e+'" failed (no arrayBuffer).'),o(new Uint8Array(t)),n&&removeRunDependency(n)}),(function(o){if(!t)throw'Loading data file "'+e+'" failed.';t()})),n&&addRunDependency(n)}var ERRNO_MESSAGES={0:"Success",1:"Arg list too long",2:"Permission denied",3:"Address already in use",4:"Address not available",5:"Address family not supported by protocol family",6:"No more processes",7:"Socket already connected",8:"Bad file number",9:"Trying to read unreadable message",10:"Mount device busy",11:"Operation canceled",12:"No children",13:"Connection aborted",14:"Connection refused",15:"Connection reset by peer",16:"File locking deadlock error",17:"Destination address required",18:"Math arg out of domain of func",19:"Quota exceeded",20:"File exists",21:"Bad address",22:"File too large",23:"Host is unreachable",24:"Identifier removed",25:"Illegal byte sequence",26:"Connection already in progress",27:"Interrupted system call",28:"Invalid argument",29:"I/O error",30:"Socket is already connected",31:"Is a directory",32:"Too many symbolic links",33:"Too many open files",34:"Too many links",35:"Message too long",36:"Multihop attempted",37:"File or path name too long",38:"Network interface is not configured",39:"Connection reset by network",40:"Network is unreachable",41:"Too many open files in system",42:"No buffer space available",43:"No such device",44:"No such file or directory",45:"Exec format error",46:"No record locks available",47:"The link has been severed",48:"Not enough core",49:"No message of desired type",50:"Protocol not available",51:"No space left on device",52:"Function not implemented",53:"Socket is not connected",54:"Not a directory",55:"Directory not empty",56:"State not recoverable",57:"Socket operation on non-socket",59:"Not a typewriter",60:"No such device or address",61:"Value too large for defined data type",62:"Previous owner died",63:"Not super-user",64:"Broken pipe",65:"Protocol error",66:"Unknown protocol",67:"Protocol wrong type for socket",68:"Math result not representable",69:"Read only file system",70:"Illegal seek",71:"No such process",72:"Stale file handle",73:"Connection timed out",74:"Text file busy",75:"Cross-device link",100:"Device not a stream",101:"Bad font file fmt",102:"Invalid slot",103:"Invalid request code",104:"No anode",105:"Block device required",106:"Channel number out of range",107:"Level 3 halted",108:"Level 3 reset",109:"Link number out of range",110:"Protocol driver not attached",111:"No CSI structure available",112:"Level 2 halted",113:"Invalid exchange",114:"Invalid request descriptor",115:"Exchange full",116:"No data (for no delay io)",117:"Timer expired",118:"Out of streams resources",119:"Machine is not on the network",120:"Package not installed",121:"The object is remote",122:"Advertise error",123:"Srmount error",124:"Communication error on send",125:"Cross mount point (not really error)",126:"Given log. name not unique",127:"f.d. invalid for this operation",128:"Remote address changed",129:"Can   access a needed shared lib",130:"Accessing a corrupted shared lib",131:".lib section in a.out corrupted",132:"Attempting to link in too many libs",133:"Attempting to exec a shared library",135:"Streams pipe error",136:"Too many users",137:"Socket type not supported",138:"Not supported",139:"Protocol family not supported",140:"Can't send after socket shutdown",141:"Too many references",142:"Host is down",148:"No medium (in tape drive)",156:"Level 2 not synchronized"},ERRNO_CODES={},FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e,o){if(o=o||{},!(e=PATH_FS.resolve(FS.cwd(),e)))return{path:"",node:null};var t={follow_mount:!0,recurse_count:0};for(var r in t)void 0===o[r]&&(o[r]=t[r]);if(o.recurse_count>8)throw new FS.ErrnoError(32);for(var n=PATH.normalizeArray(e.split("/").filter((function(e){return!!e})),!1),i=FS.root,s="/",a=0;a<n.length;a++){var d=a===n.length-1;if(d&&o.parent)break;if(i=FS.lookupNode(i,n[a]),s=PATH.join2(s,n[a]),FS.isMountpoint(i)&&(!d||d&&o.follow_mount)&&(i=i.mounted.root),!d||o.follow)for(var u=0;FS.isLink(i.mode);){var l=FS.readlink(s);if(s=PATH_FS.resolve(PATH.dirname(s),l),i=FS.lookupPath(s,{recurse_count:o.recurse_count}).node,u++>40)throw new FS.ErrnoError(32)}}return{path:s,node:i}},getPath:function(e){for(var o;;){if(FS.isRoot(e)){var t=e.mount.mountpoint;return o?"/"!==t[t.length-1]?t+"/"+o:t+o:t}o=o?e.name+"/"+o:e.name,e=e.parent}},hashName:function(e,o){for(var t=0,r=0;r<o.length;r++)t=(t<<5)-t+o.charCodeAt(r)|0;return(e+t>>>0)%FS.nameTable.length},hashAddNode:function(e){var o=FS.hashName(e.parent.id,e.name);e.name_next=FS.nameTable[o],FS.nameTable[o]=e},hashRemoveNode:function(e){var o=FS.hashName(e.parent.id,e.name);if(FS.nameTable[o]===e)FS.nameTable[o]=e.name_next;else for(var t=FS.nameTable[o];t;){if(t.name_next===e){t.name_next=e.name_next;break}t=t.name_next}},lookupNode:function(e,o){var t=FS.mayLookup(e);if(t)throw new FS.ErrnoError(t,e);for(var r=FS.hashName(e.id,o),n=FS.nameTable[r];n;n=n.name_next){var i=n.name;if(n.parent.id===e.id&&i===o)return n}return FS.lookup(e,o)},createNode:function(e,o,t,r){assert("object"==typeof e);var n=new FS.FSNode(e,o,t,r);return FS.hashAddNode(n),n},destroyNode:function(e){FS.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return!(49152&~e)},flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:function(e){var o=FS.flagModes[e];if(void 0===o)throw new Error("Unknown file open mode: "+e);return o},flagsToPermissionString:function(e){var o=["r","w","rw"][3&e];return 512&e&&(o+="w"),o},nodePermissions:function(e,o){return FS.ignorePermissions||(!o.includes("r")||292&e.mode)&&(!o.includes("w")||146&e.mode)&&(!o.includes("x")||73&e.mode)?0:2},mayLookup:function(e){var o=FS.nodePermissions(e,"x");return o||(e.node_ops.lookup?0:2)},mayCreate:function(e,o){try{FS.lookupNode(e,o);return 20}catch(e){}return FS.nodePermissions(e,"wx")},mayDelete:function(e,o,t){var r;try{r=FS.lookupNode(e,o)}catch(e){return e.errno}var n=FS.nodePermissions(e,"wx");if(n)return n;if(t){if(!FS.isDir(r.mode))return 54;if(FS.isRoot(r)||FS.getPath(r)===FS.cwd())return 10}else if(FS.isDir(r.mode))return 31;return 0},mayOpen:function(e,o){return e?FS.isLink(e.mode)?32:FS.isDir(e.mode)&&("r"!==FS.flagsToPermissionString(o)||512&o)?31:FS.nodePermissions(e,FS.flagsToPermissionString(o)):44},MAX_OPEN_FDS:4096,nextfd:function(e,o){e=e||0,o=o||FS.MAX_OPEN_FDS;for(var t=e;t<=o;t++)if(!FS.streams[t])return t;throw new FS.ErrnoError(33)},getStream:function(e){return FS.streams[e]},createStream:function(e,o,t){FS.FSStream||(FS.FSStream=function(){},FS.FSStream.prototype={object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return!!(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}});var r=new FS.FSStream;for(var n in e)r[n]=e[n];e=r;var i=FS.nextfd(o,t);return e.fd=i,FS.streams[i]=e,e},closeStream:function(e){FS.streams[e]=null},chrdev_stream_ops:{open:function(e){var o=FS.getDevice(e.node.rdev);e.stream_ops=o.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new FS.ErrnoError(70)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,o){return e<<8|o},registerDevice:function(e,o){FS.devices[e]={stream_ops:o}},getDevice:function(e){return FS.devices[e]},getMounts:function(e){for(var o=[],t=[e];t.length;){var r=t.pop();o.push(r),t.push.apply(t,r.mounts)}return o},syncfs:function(e,o){"function"==typeof e&&(o=e,e=!1),FS.syncFSRequests++,FS.syncFSRequests>1&&err("warning: "+FS.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var t=FS.getMounts(FS.root.mount),r=0;function n(e){return assert(FS.syncFSRequests>0),FS.syncFSRequests--,o(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,n(e));++r>=t.length&&n(null)}t.forEach((function(o){if(!o.type.syncfs)return i(null);o.type.syncfs(o,e,i)}))},mount:function(e,o,t){if("string"==typeof e)throw e;var r,n="/"===t,i=!t;if(n&&FS.root)throw new FS.ErrnoError(10);if(!n&&!i){var s=FS.lookupPath(t,{follow_mount:!1});if(t=s.path,r=s.node,FS.isMountpoint(r))throw new FS.ErrnoError(10);if(!FS.isDir(r.mode))throw new FS.ErrnoError(54)}var a={type:e,opts:o,mountpoint:t,mounts:[]},d=e.mount(a);return d.mount=a,a.root=d,n?FS.root=d:r&&(r.mounted=a,r.mount&&r.mount.mounts.push(a)),d},unmount:function(e){var o=FS.lookupPath(e,{follow_mount:!1});if(!FS.isMountpoint(o.node))throw new FS.ErrnoError(28);var t=o.node,r=t.mounted,n=FS.getMounts(r);Object.keys(FS.nameTable).forEach((function(e){for(var o=FS.nameTable[e];o;){var t=o.name_next;n.includes(o.mount)&&FS.destroyNode(o),o=t}})),t.mounted=null;var i=t.mount.mounts.indexOf(r);assert(-1!==i),t.mount.mounts.splice(i,1)},lookup:function(e,o){return e.node_ops.lookup(e,o)},mknod:function(e,o,t){var r=FS.lookupPath(e,{parent:!0}).node,n=PATH.basename(e);if(!n||"."===n||".."===n)throw new FS.ErrnoError(28);var i=FS.mayCreate(r,n);if(i)throw new FS.ErrnoError(i);if(!r.node_ops.mknod)throw new FS.ErrnoError(63);return r.node_ops.mknod(r,n,o,t)},create:function(e,o){return o=void 0!==o?o:438,o&=4095,o|=32768,FS.mknod(e,o,0)},mkdir:function(e,o){return o=void 0!==o?o:511,o&=1023,o|=16384,FS.mknod(e,o,0)},mkdirTree:function(e,o){for(var t=e.split("/"),r="",n=0;n<t.length;++n)if(t[n]){r+="/"+t[n];try{FS.mkdir(r,o)}catch(e){if(20!=e.errno)throw e}}},mkdev:function(e,o,t){return void 0===t&&(t=o,o=438),o|=8192,FS.mknod(e,o,t)},symlink:function(e,o){if(!PATH_FS.resolve(e))throw new FS.ErrnoError(44);var t=FS.lookupPath(o,{parent:!0}).node;if(!t)throw new FS.ErrnoError(44);var r=PATH.basename(o),n=FS.mayCreate(t,r);if(n)throw new FS.ErrnoError(n);if(!t.node_ops.symlink)throw new FS.ErrnoError(63);return t.node_ops.symlink(t,r,e)},rename:function(e,o){var t,r,n=PATH.dirname(e),i=PATH.dirname(o),s=PATH.basename(e),a=PATH.basename(o);if(t=FS.lookupPath(e,{parent:!0}).node,r=FS.lookupPath(o,{parent:!0}).node,!t||!r)throw new FS.ErrnoError(44);if(t.mount!==r.mount)throw new FS.ErrnoError(75);var d,u=FS.lookupNode(t,s),l=PATH_FS.relative(e,i);if("."!==l.charAt(0))throw new FS.ErrnoError(28);if("."!==(l=PATH_FS.relative(o,n)).charAt(0))throw new FS.ErrnoError(55);try{d=FS.lookupNode(r,a)}catch(e){}if(u!==d){var c=FS.isDir(u.mode),p=FS.mayDelete(t,s,c);if(p)throw new FS.ErrnoError(p);if(p=d?FS.mayDelete(r,a,c):FS.mayCreate(r,a))throw new FS.ErrnoError(p);if(!t.node_ops.rename)throw new FS.ErrnoError(63);if(FS.isMountpoint(u)||d&&FS.isMountpoint(d))throw new FS.ErrnoError(10);if(r!==t&&(p=FS.nodePermissions(t,"w")))throw new FS.ErrnoError(p);FS.hashRemoveNode(u);try{t.node_ops.rename(u,r,a)}catch(e){throw e}finally{FS.hashAddNode(u)}}},rmdir:function(e){var o=FS.lookupPath(e,{parent:!0}).node,t=PATH.basename(e),r=FS.lookupNode(o,t),n=FS.mayDelete(o,t,!0);if(n)throw new FS.ErrnoError(n);if(!o.node_ops.rmdir)throw new FS.ErrnoError(63);if(FS.isMountpoint(r))throw new FS.ErrnoError(10);o.node_ops.rmdir(o,t),FS.destroyNode(r)},readdir:function(e){var o=FS.lookupPath(e,{follow:!0}).node;if(!o.node_ops.readdir)throw new FS.ErrnoError(54);return o.node_ops.readdir(o)},unlink:function(e){var o=FS.lookupPath(e,{parent:!0}).node,t=PATH.basename(e),r=FS.lookupNode(o,t),n=FS.mayDelete(o,t,!1);if(n)throw new FS.ErrnoError(n);if(!o.node_ops.unlink)throw new FS.ErrnoError(63);if(FS.isMountpoint(r))throw new FS.ErrnoError(10);o.node_ops.unlink(o,t),FS.destroyNode(r)},readlink:function(e){var o=FS.lookupPath(e).node;if(!o)throw new FS.ErrnoError(44);if(!o.node_ops.readlink)throw new FS.ErrnoError(28);return PATH_FS.resolve(FS.getPath(o.parent),o.node_ops.readlink(o))},stat:function(e,o){var t=FS.lookupPath(e,{follow:!o}).node;if(!t)throw new FS.ErrnoError(44);if(!t.node_ops.getattr)throw new FS.ErrnoError(63);return t.node_ops.getattr(t)},lstat:function(e){return FS.stat(e,!0)},chmod:function(e,o,t){var r;"string"==typeof e?r=FS.lookupPath(e,{follow:!t}).node:r=e;if(!r.node_ops.setattr)throw new FS.ErrnoError(63);r.node_ops.setattr(r,{mode:4095&o|-4096&r.mode,timestamp:Date.now()})},lchmod:function(e,o){FS.chmod(e,o,!0)},fchmod:function(e,o){var t=FS.getStream(e);if(!t)throw new FS.ErrnoError(8);FS.chmod(t.node,o)},chown:function(e,o,t,r){var n;"string"==typeof e?n=FS.lookupPath(e,{follow:!r}).node:n=e;if(!n.node_ops.setattr)throw new FS.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:function(e,o,t){FS.chown(e,o,t,!0)},fchown:function(e,o,t){var r=FS.getStream(e);if(!r)throw new FS.ErrnoError(8);FS.chown(r.node,o,t)},truncate:function(e,o){if(o<0)throw new FS.ErrnoError(28);var t;"string"==typeof e?t=FS.lookupPath(e,{follow:!0}).node:t=e;if(!t.node_ops.setattr)throw new FS.ErrnoError(63);if(FS.isDir(t.mode))throw new FS.ErrnoError(31);if(!FS.isFile(t.mode))throw new FS.ErrnoError(28);var r=FS.nodePermissions(t,"w");if(r)throw new FS.ErrnoError(r);t.node_ops.setattr(t,{size:o,timestamp:Date.now()})},ftruncate:function(e,o){var t=FS.getStream(e);if(!t)throw new FS.ErrnoError(8);if(!(2097155&t.flags))throw new FS.ErrnoError(28);FS.truncate(t.node,o)},utime:function(e,o,t){var r=FS.lookupPath(e,{follow:!0}).node;r.node_ops.setattr(r,{timestamp:Math.max(o,t)})},open:function(e,o,t,r,n){if(""===e)throw new FS.ErrnoError(44);var i;if(t=void 0===t?438:t,t=64&(o="string"==typeof o?FS.modeStringToFlags(o):o)?4095&t|32768:0,"object"==typeof e)i=e;else{e=PATH.normalize(e);try{i=FS.lookupPath(e,{follow:!(131072&o)}).node}catch(e){}}var s=!1;if(64&o)if(i){if(128&o)throw new FS.ErrnoError(20)}else i=FS.mknod(e,t,0),s=!0;if(!i)throw new FS.ErrnoError(44);if(FS.isChrdev(i.mode)&&(o&=-513),65536&o&&!FS.isDir(i.mode))throw new FS.ErrnoError(54);if(!s){var a=FS.mayOpen(i,o);if(a)throw new FS.ErrnoError(a)}512&o&&FS.truncate(i,0),o&=-131713;var d=FS.createStream({node:i,path:FS.getPath(i),id:i.id,flags:o,mode:i.mode,seekable:!0,position:0,stream_ops:i.stream_ops,node_ops:i.node_ops,ungotten:[],error:!1},r,n);return d.stream_ops.open&&d.stream_ops.open(d),!RoomsDivisionModule.logReadFiles||1&o||(FS.readFiles||(FS.readFiles={}),e in FS.readFiles||(FS.readFiles[e]=1)),d},close:function(e){if(FS.isClosed(e))throw new FS.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{FS.closeStream(e.fd)}e.fd=null},isClosed:function(e){return null===e.fd},llseek:function(e,o,t){if(FS.isClosed(e))throw new FS.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new FS.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new FS.ErrnoError(28);return e.position=e.stream_ops.llseek(e,o,t),e.ungotten=[],e.position},read:function(e,o,t,r,n){if(r<0||n<0)throw new FS.ErrnoError(28);if(FS.isClosed(e))throw new FS.ErrnoError(8);if(1==(2097155&e.flags))throw new FS.ErrnoError(8);if(FS.isDir(e.node.mode))throw new FS.ErrnoError(31);if(!e.stream_ops.read)throw new FS.ErrnoError(28);var i=void 0!==n;if(i){if(!e.seekable)throw new FS.ErrnoError(70)}else n=e.position;var s=e.stream_ops.read(e,o,t,r,n);return i||(e.position+=s),s},write:function(e,o,t,r,n,i){if(r<0||n<0)throw new FS.ErrnoError(28);if(FS.isClosed(e))throw new FS.ErrnoError(8);if(!(2097155&e.flags))throw new FS.ErrnoError(8);if(FS.isDir(e.node.mode))throw new FS.ErrnoError(31);if(!e.stream_ops.write)throw new FS.ErrnoError(28);e.seekable&&1024&e.flags&&FS.llseek(e,0,2);var s=void 0!==n;if(s){if(!e.seekable)throw new FS.ErrnoError(70)}else n=e.position;var a=e.stream_ops.write(e,o,t,r,n,i);return s||(e.position+=a),a},allocate:function(e,o,t){if(FS.isClosed(e))throw new FS.ErrnoError(8);if(o<0||t<=0)throw new FS.ErrnoError(28);if(!(2097155&e.flags))throw new FS.ErrnoError(8);if(!FS.isFile(e.node.mode)&&!FS.isDir(e.node.mode))throw new FS.ErrnoError(43);if(!e.stream_ops.allocate)throw new FS.ErrnoError(138);e.stream_ops.allocate(e,o,t)},mmap:function(e,o,t,r,n,i){if(2&n&&!(2&i)&&2!=(2097155&e.flags))throw new FS.ErrnoError(2);if(1==(2097155&e.flags))throw new FS.ErrnoError(2);if(!e.stream_ops.mmap)throw new FS.ErrnoError(43);return e.stream_ops.mmap(e,o,t,r,n,i)},msync:function(e,o,t,r,n){return e&&e.stream_ops.msync?e.stream_ops.msync(e,o,t,r,n):0},munmap:function(e){return 0},ioctl:function(e,o,t){if(!e.stream_ops.ioctl)throw new FS.ErrnoError(59);return e.stream_ops.ioctl(e,o,t)},readFile:function(e,o){if((o=o||{}).flags=o.flags||0,o.encoding=o.encoding||"binary","utf8"!==o.encoding&&"binary"!==o.encoding)throw new Error('Invalid encoding type "'+o.encoding+'"');var t,r=FS.open(e,o.flags),n=FS.stat(e).size,i=new Uint8Array(n);return FS.read(r,i,0,n,0),"utf8"===o.encoding?t=UTF8ArrayToString(i,0):"binary"===o.encoding&&(t=i),FS.close(r),t},writeFile:function(e,o,t){(t=t||{}).flags=t.flags||577;var r=FS.open(e,t.flags,t.mode);if("string"==typeof o){var n=new Uint8Array(lengthBytesUTF8(o)+1),i=stringToUTF8Array(o,n,0,n.length);FS.write(r,n,0,i,void 0,t.canOwn)}else{if(!ArrayBuffer.isView(o))throw new Error("Unsupported data type");FS.write(r,o,0,o.byteLength,void 0,t.canOwn)}FS.close(r)},cwd:function(){return FS.currentPath},chdir:function(e){var o=FS.lookupPath(e,{follow:!0});if(null===o.node)throw new FS.ErrnoError(44);if(!FS.isDir(o.node.mode))throw new FS.ErrnoError(54);var t=FS.nodePermissions(o.node,"x");if(t)throw new FS.ErrnoError(t);FS.currentPath=o.path},createDefaultDirectories:function(){FS.mkdir("/tmp"),FS.mkdir("/home"),FS.mkdir("/home/<USER>")},createDefaultDevices:function(){FS.mkdir("/dev"),FS.registerDevice(FS.makedev(1,3),{read:function(){return 0},write:function(e,o,t,r,n){return r}}),FS.mkdev("/dev/null",FS.makedev(1,3)),TTY.register(FS.makedev(5,0),TTY.default_tty_ops),TTY.register(FS.makedev(6,0),TTY.default_tty1_ops),FS.mkdev("/dev/tty",FS.makedev(5,0)),FS.mkdev("/dev/tty1",FS.makedev(6,0));var e=getRandomDevice();FS.createDevice("/dev","random",e),FS.createDevice("/dev","urandom",e),FS.mkdir("/dev/shm"),FS.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){FS.mkdir("/proc");var e=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd"),FS.mount({mount:function(){var o=FS.createNode(e,"fd",16895,73);return o.node_ops={lookup:function(e,o){var t=+o,r=FS.getStream(t);if(!r)throw new FS.ErrnoError(8);var n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return r.path}}};return n.parent=n,n}},o}},{},"/proc/self/fd")},createStandardStreams:function(){RoomsDivisionModule.stdin?FS.createDevice("/dev","stdin",RoomsDivisionModule.stdin):FS.symlink("/dev/tty","/dev/stdin"),RoomsDivisionModule.stdout?FS.createDevice("/dev","stdout",null,RoomsDivisionModule.stdout):FS.symlink("/dev/tty","/dev/stdout"),RoomsDivisionModule.stderr?FS.createDevice("/dev","stderr",null,RoomsDivisionModule.stderr):FS.symlink("/dev/tty1","/dev/stderr");var e=FS.open("/dev/stdin",0),o=FS.open("/dev/stdout",1),t=FS.open("/dev/stderr",1);assert(0===e.fd,"invalid handle for stdin ("+e.fd+")"),assert(1===o.fd,"invalid handle for stdout ("+o.fd+")"),assert(2===t.fd,"invalid handle for stderr ("+t.fd+")")},ensureErrnoError:function(){FS.ErrnoError||(FS.ErrnoError=function(e,o){this.node=o,this.setErrno=function(e){for(var o in this.errno=e,ERRNO_CODES)if(ERRNO_CODES[o]===e){this.code=o;break}},this.setErrno(e),this.message=ERRNO_MESSAGES[e],this.stack&&(Object.defineProperty(this,"stack",{value:(new Error).stack,writable:!0}),this.stack=demangleAll(this.stack))},FS.ErrnoError.prototype=new Error,FS.ErrnoError.prototype.constructor=FS.ErrnoError,[44].forEach((function(e){FS.genericErrors[e]=new FS.ErrnoError(e),FS.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:function(){FS.ensureErrnoError(),FS.nameTable=new Array(4096),FS.mount(MEMFS,{},"/"),FS.createDefaultDirectories(),FS.createDefaultDevices(),FS.createSpecialDirectories(),FS.filesystems={MEMFS:MEMFS}},init:function(e,o,t){assert(!FS.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),FS.init.initialized=!0,FS.ensureErrnoError(),RoomsDivisionModule.stdin=e||RoomsDivisionModule.stdin,RoomsDivisionModule.stdout=o||RoomsDivisionModule.stdout,RoomsDivisionModule.stderr=t||RoomsDivisionModule.stderr,FS.createStandardStreams()},quit:function(){FS.init.initialized=!1;var e=RoomsDivisionModule._fflush;e&&e(0);for(var o=0;o<FS.streams.length;o++){var t=FS.streams[o];t&&FS.close(t)}},getMode:function(e,o){var t=0;return e&&(t|=365),o&&(t|=146),t},findObject:function(e,o){var t=FS.analyzePath(e,o);return t.exists?t.object:null},analyzePath:function(e,o){try{e=(r=FS.lookupPath(e,{follow:!o})).path}catch(e){}var t={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var r=FS.lookupPath(e,{parent:!0});t.parentExists=!0,t.parentPath=r.path,t.parentObject=r.node,t.name=PATH.basename(e),r=FS.lookupPath(e,{follow:!o}),t.exists=!0,t.path=r.path,t.object=r.node,t.name=r.node.name,t.isRoot="/"===r.path}catch(e){t.error=e.errno}return t},createPath:function(e,o,t,r){e="string"==typeof e?e:FS.getPath(e);for(var n=o.split("/").reverse();n.length;){var i=n.pop();if(i){var s=PATH.join2(e,i);try{FS.mkdir(s)}catch(e){}e=s}}return s},createFile:function(e,o,t,r,n){var i=PATH.join2("string"==typeof e?e:FS.getPath(e),o),s=FS.getMode(r,n);return FS.create(i,s)},createDataFile:function(e,o,t,r,n,i){var s=o?PATH.join2("string"==typeof e?e:FS.getPath(e),o):e,a=FS.getMode(r,n),d=FS.create(s,a);if(t){if("string"==typeof t){for(var u=new Array(t.length),l=0,c=t.length;l<c;++l)u[l]=t.charCodeAt(l);t=u}FS.chmod(d,146|a);var p=FS.open(d,577);FS.write(p,t,0,t.length,0,i),FS.close(p),FS.chmod(d,a)}return d},createDevice:function(e,o,t,r){var n=PATH.join2("string"==typeof e?e:FS.getPath(e),o),i=FS.getMode(!!t,!!r);FS.createDevice.major||(FS.createDevice.major=64);var s=FS.makedev(FS.createDevice.major++,0);return FS.registerDevice(s,{open:function(e){e.seekable=!1},close:function(e){r&&r.buffer&&r.buffer.length&&r(10)},read:function(e,o,r,n,i){for(var s=0,a=0;a<n;a++){var d;try{d=t()}catch(e){throw new FS.ErrnoError(29)}if(void 0===d&&0===s)throw new FS.ErrnoError(6);if(null==d)break;s++,o[r+a]=d}return s&&(e.node.timestamp=Date.now()),s},write:function(e,o,t,n,i){for(var s=0;s<n;s++)try{r(o[t+s])}catch(e){throw new FS.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),s}}),FS.mkdev(n,i,s)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!read_)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=intArrayFromString(read_(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new FS.ErrnoError(29)}},createLazyFile:function(e,o,t,r,n){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var o=e%this.chunkSize,t=e/this.chunkSize|0;return this.getter(t)[o]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",t,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+t+". Status: "+e.status);var o,r=Number(e.getResponseHeader("Content-length")),n=(o=e.getResponseHeader("Accept-Ranges"))&&"bytes"===o,i=(o=e.getResponseHeader("Content-Encoding"))&&"gzip"===o,s=1048576;n||(s=r);var a=this;a.setDataGetter((function(e){var o=e*s,n=(e+1)*s-1;if(n=Math.min(n,r-1),void 0===a.chunks[e]&&(a.chunks[e]=function(e,o){if(e>o)throw new Error("invalid range ("+e+", "+o+") or no bytes requested!");if(o>r-1)throw new Error("only "+r+" bytes available! programmer error!");var n=new XMLHttpRequest;if(n.open("GET",t,!1),r!==s&&n.setRequestHeader("Range","bytes="+e+"-"+o),"undefined"!=typeof Uint8Array&&(n.responseType="arraybuffer"),n.overrideMimeType&&n.overrideMimeType("text/plain; charset=x-user-defined"),n.send(null),!(n.status>=200&&n.status<300||304===n.status))throw new Error("Couldn't load "+t+". Status: "+n.status);return void 0!==n.response?new Uint8Array(n.response||[]):intArrayFromString(n.responseText||"",!0)}(o,n)),void 0===a.chunks[e])throw new Error("doXHR failed!");return a.chunks[e]})),!i&&r||(s=r=1,r=this.getter(0).length,s=r,out("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=r,this._chunkSize=s,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var s=new i;Object.defineProperties(s,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var a={isDevice:!1,contents:s}}else a={isDevice:!1,url:t};var d=FS.createFile(e,o,a,r,n);a.contents?d.contents=a.contents:a.url&&(d.contents=null,d.url=a.url),Object.defineProperties(d,{usedBytes:{get:function(){return this.contents.length}}});var u={};return Object.keys(d.stream_ops).forEach((function(e){var o=d.stream_ops[e];u[e]=function(){return FS.forceLoadFile(d),o.apply(null,arguments)}})),u.read=function(e,o,t,r,n){FS.forceLoadFile(d);var i=e.node.contents;if(n>=i.length)return 0;var s=Math.min(i.length-n,r);if(assert(s>=0),i.slice)for(var a=0;a<s;a++)o[t+a]=i[n+a];else for(a=0;a<s;a++)o[t+a]=i.get(n+a);return s},d.stream_ops=u,d},createPreloadedFile:function(e,o,t,r,n,i,s,a,d,u){Browser.init();var l=o?PATH_FS.resolve(PATH.join2(e,o)):e,c=getUniqueRunDependency("cp "+l);function p(t){function p(t){u&&u(),a||FS.createDataFile(e,o,t,r,n,d),i&&i(),removeRunDependency(c)}var E=!1;RoomsDivisionModule.preloadPlugins.forEach((function(e){E||e.canHandle(l)&&(e.handle(t,l,p,(function(){s&&s(),removeRunDependency(c)})),E=!0)})),E||p(t)}addRunDependency(c),"string"==typeof t?asyncLoad(t,(function(e){p(e)}),s):p(t)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(e,o,t){o=o||function(){},t=t||function(){};var r=FS.indexedDB();try{var n=r.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return t(e)}n.onupgradeneeded=function(){out("creating db"),n.result.createObjectStore(FS.DB_STORE_NAME)},n.onsuccess=function(){var r=n.result.transaction([FS.DB_STORE_NAME],"readwrite"),i=r.objectStore(FS.DB_STORE_NAME),s=0,a=0,d=e.length;function u(){0==a?o():t()}e.forEach((function(e){var o=i.put(FS.analyzePath(e).object.contents,e);o.onsuccess=function(){++s+a==d&&u()},o.onerror=function(){a++,s+a==d&&u()}})),r.onerror=t},n.onerror=t},loadFilesFromDB:function(e,o,t){o=o||function(){},t=t||function(){};var r=FS.indexedDB();try{var n=r.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return t(e)}n.onupgradeneeded=t,n.onsuccess=function(){var r=n.result;try{var i=r.transaction([FS.DB_STORE_NAME],"readonly")}catch(e){return void t(e)}var s=i.objectStore(FS.DB_STORE_NAME),a=0,d=0,u=e.length;function l(){0==d?o():t()}e.forEach((function(e){var o=s.get(e);o.onsuccess=function(){FS.analyzePath(e).exists&&FS.unlink(e),FS.createDataFile(PATH.dirname(e),PATH.basename(e),o.result,!0,!0,!0),++a+d==u&&l()},o.onerror=function(){d++,a+d==u&&l()}})),i.onerror=t},n.onerror=t},absolutePath:function(){abort("FS.absolutePath has been removed; use PATH_FS.resolve instead")},createFolder:function(){abort("FS.createFolder has been removed; use FS.mkdir instead")},createLink:function(){abort("FS.createLink has been removed; use FS.symlink instead")},joinPath:function(){abort("FS.joinPath has been removed; use PATH.join instead")},mmapAlloc:function(){abort("FS.mmapAlloc has been replaced by the top level function mmapAlloc")},standardizePath:function(){abort("FS.standardizePath has been removed; use PATH.normalize instead")}},SYSCALLS={mappings:{},DEFAULT_POLLMASK:5,calculateAt:function(e,o,t){if("/"===o[0])return o;var r;if(-100===e)r=FS.cwd();else{var n=FS.getStream(e);if(!n)throw new FS.ErrnoError(8);r=n.path}if(0==o.length){if(!t)throw new FS.ErrnoError(44);return r}return PATH.join2(r,o)},doStat:function(e,o,t){try{var r=e(o)}catch(e){if(e&&e.node&&PATH.normalize(o)!==PATH.normalize(FS.getPath(e.node)))return-54;throw e}return HEAP32[t>>2]=r.dev,HEAP32[t+4>>2]=0,HEAP32[t+8>>2]=r.ino,HEAP32[t+12>>2]=r.mode,HEAP32[t+16>>2]=r.nlink,HEAP32[t+20>>2]=r.uid,HEAP32[t+24>>2]=r.gid,HEAP32[t+28>>2]=r.rdev,HEAP32[t+32>>2]=0,tempI64=[r.size>>>0,(tempDouble=r.size,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[t+40>>2]=tempI64[0],HEAP32[t+44>>2]=tempI64[1],HEAP32[t+48>>2]=4096,HEAP32[t+52>>2]=r.blocks,HEAP32[t+56>>2]=r.atime.getTime()/1e3|0,HEAP32[t+60>>2]=0,HEAP32[t+64>>2]=r.mtime.getTime()/1e3|0,HEAP32[t+68>>2]=0,HEAP32[t+72>>2]=r.ctime.getTime()/1e3|0,HEAP32[t+76>>2]=0,tempI64=[r.ino>>>0,(tempDouble=r.ino,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[t+80>>2]=tempI64[0],HEAP32[t+84>>2]=tempI64[1],0},doMsync:function(e,o,t,r,n){var i=HEAPU8.slice(e,e+t);FS.msync(o,i,n,t,r)},doMkdir:function(e,o){return"/"===(e=PATH.normalize(e))[e.length-1]&&(e=e.substr(0,e.length-1)),FS.mkdir(e,o,0),0},doMknod:function(e,o,t){switch(61440&o){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}return FS.mknod(e,o,t),0},doReadlink:function(e,o,t){if(t<=0)return-28;var r=FS.readlink(e),n=Math.min(t,lengthBytesUTF8(r)),i=HEAP8[o+n];return stringToUTF8(r,o,t+1),HEAP8[o+n]=i,n},doAccess:function(e,o){if(-8&o)return-28;var t=FS.lookupPath(e,{follow:!0}).node;if(!t)return-44;var r="";return 4&o&&(r+="r"),2&o&&(r+="w"),1&o&&(r+="x"),r&&FS.nodePermissions(t,r)?-2:0},doDup:function(e,o,t){var r=FS.getStream(t);return r&&FS.close(r),FS.open(e,o,0,t,t).fd},doReadv:function(e,o,t,r){for(var n=0,i=0;i<t;i++){var s=HEAP32[o+8*i>>2],a=HEAP32[o+(8*i+4)>>2],d=FS.read(e,HEAP8,s,a,r);if(d<0)return-1;if(n+=d,d<a)break}return n},doWritev:function(e,o,t,r){for(var n=0,i=0;i<t;i++){var s=HEAP32[o+8*i>>2],a=HEAP32[o+(8*i+4)>>2],d=FS.write(e,HEAP8,s,a,r);if(d<0)return-1;n+=d}return n},varargs:void 0,get:function(){return assert(null!=SYSCALLS.varargs),SYSCALLS.varargs+=4,HEAP32[SYSCALLS.varargs-4>>2]},getStr:function(e){return UTF8ToString(e)},getStreamFromFD:function(e){var o=FS.getStream(e);if(!o)throw new FS.ErrnoError(8);return o},get64:function(e,o){return assert(e>=0?0===o:-1===o),e}};function _environ_get(e,o){var t=0;return getEnvStrings().forEach((function(r,n){var i=o+t;HEAP32[e+4*n>>2]=i,writeAsciiToMemory(r,i),t+=r.length+1})),0}function _environ_sizes_get(e,o){var t=getEnvStrings();HEAP32[e>>2]=t.length;var r=0;return t.forEach((function(e){r+=e.length+1})),HEAP32[o>>2]=r,0}function _fd_close(e){try{var o=SYSCALLS.getStreamFromFD(e);return FS.close(o),0}catch(e){if(void 0===FS||!(e instanceof FS.ErrnoError))throw e;return e.errno}}function _fd_read(e,o,t,r){try{var n=SYSCALLS.getStreamFromFD(e),i=SYSCALLS.doReadv(n,o,t);return HEAP32[r>>2]=i,0}catch(e){if(void 0===FS||!(e instanceof FS.ErrnoError))throw e;return e.errno}}function _fd_seek(e,o,t,r,n){try{var i=SYSCALLS.getStreamFromFD(e),s=4294967296*t+(o>>>0),a=9007199254740992;return s<=-a||s>=a?-61:(FS.llseek(i,s,r),tempI64=[i.position>>>0,(tempDouble=i.position,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[n>>2]=tempI64[0],HEAP32[n+4>>2]=tempI64[1],i.getdents&&0===s&&0===r&&(i.getdents=null),0)}catch(e){if(void 0===FS||!(e instanceof FS.ErrnoError))throw e;return e.errno}}function _fd_write(e,o,t,r){try{var n=SYSCALLS.getStreamFromFD(e),i=SYSCALLS.doWritev(n,o,t);return HEAP32[r>>2]=i,0}catch(e){if(void 0===FS||!(e instanceof FS.ErrnoError))throw e;return e.errno}}function _getentropy(e,o){_getentropy.randomDevice||(_getentropy.randomDevice=getRandomDevice());for(var t=0;t<o;t++)HEAP8[e+t|0]=_getentropy.randomDevice();return 0}function _setTempRet0(e){setTempRet0(e)}function __isLeapYear(e){return e%4==0&&(e%100!=0||e%400==0)}function __arraySum(e,o){for(var t=0,r=0;r<=o;t+=e[r++]);return t}var __MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31],__MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];function __addDays(e,o){for(var t=new Date(e.getTime());o>0;){var r=__isLeapYear(t.getFullYear()),n=t.getMonth(),i=(r?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR)[n];if(!(o>i-t.getDate()))return t.setDate(t.getDate()+o),t;o-=i-t.getDate()+1,t.setDate(1),n<11?t.setMonth(n+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return t}function _strftime(e,o,t,r){var n=HEAP32[r+40>>2],i={tm_sec:HEAP32[r>>2],tm_min:HEAP32[r+4>>2],tm_hour:HEAP32[r+8>>2],tm_mday:HEAP32[r+12>>2],tm_mon:HEAP32[r+16>>2],tm_year:HEAP32[r+20>>2],tm_wday:HEAP32[r+24>>2],tm_yday:HEAP32[r+28>>2],tm_isdst:HEAP32[r+32>>2],tm_gmtoff:HEAP32[r+36>>2],tm_zone:n?UTF8ToString(n):""},s=UTF8ToString(t),a={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var d in a)s=s.replace(new RegExp(d,"g"),a[d]);var u=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],l=["January","February","March","April","May","June","July","August","September","October","November","December"];function c(e,o,t){for(var r="number"==typeof e?e.toString():e||"";r.length<o;)r=t[0]+r;return r}function p(e,o){return c(e,o,"0")}function E(e,o){function t(e){return e<0?-1:e>0?1:0}var r;return 0===(r=t(e.getFullYear()-o.getFullYear()))&&0===(r=t(e.getMonth()-o.getMonth()))&&(r=t(e.getDate()-o.getDate())),r}function m(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function D(e){var o=__addDays(new Date(e.tm_year+1900,0,1),e.tm_yday),t=new Date(o.getFullYear(),0,4),r=new Date(o.getFullYear()+1,0,4),n=m(t),i=m(r);return E(n,o)<=0?E(i,o)<=0?o.getFullYear()+1:o.getFullYear():o.getFullYear()-1}var f={"%a":function(e){return u[e.tm_wday].substring(0,3)},"%A":function(e){return u[e.tm_wday]},"%b":function(e){return l[e.tm_mon].substring(0,3)},"%B":function(e){return l[e.tm_mon]},"%C":function(e){return p((e.tm_year+1900)/100|0,2)},"%d":function(e){return p(e.tm_mday,2)},"%e":function(e){return c(e.tm_mday,2," ")},"%g":function(e){return D(e).toString().substring(2)},"%G":function(e){return D(e)},"%H":function(e){return p(e.tm_hour,2)},"%I":function(e){var o=e.tm_hour;return 0==o?o=12:o>12&&(o-=12),p(o,2)},"%j":function(e){return p(e.tm_mday+__arraySum(__isLeapYear(e.tm_year+1900)?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,e.tm_mon-1),3)},"%m":function(e){return p(e.tm_mon+1,2)},"%M":function(e){return p(e.tm_min,2)},"%n":function(){return"\n"},"%p":function(e){return e.tm_hour>=0&&e.tm_hour<12?"AM":"PM"},"%S":function(e){return p(e.tm_sec,2)},"%t":function(){return"\t"},"%u":function(e){return e.tm_wday||7},"%U":function(e){var o=new Date(e.tm_year+1900,0,1),t=0===o.getDay()?o:__addDays(o,7-o.getDay()),r=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(E(t,r)<0){var n=__arraySum(__isLeapYear(r.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,r.getMonth()-1)-31,i=31-t.getDate()+n+r.getDate();return p(Math.ceil(i/7),2)}return 0===E(t,o)?"01":"00"},"%V":function(e){var o,t=new Date(e.tm_year+1900,0,4),r=new Date(e.tm_year+1901,0,4),n=m(t),i=m(r),s=__addDays(new Date(e.tm_year+1900,0,1),e.tm_yday);return E(s,n)<0?"53":E(i,s)<=0?"01":(o=n.getFullYear()<e.tm_year+1900?e.tm_yday+32-n.getDate():e.tm_yday+1-n.getDate(),p(Math.ceil(o/7),2))},"%w":function(e){return e.tm_wday},"%W":function(e){var o=new Date(e.tm_year,0,1),t=1===o.getDay()?o:__addDays(o,0===o.getDay()?1:7-o.getDay()+1),r=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(E(t,r)<0){var n=__arraySum(__isLeapYear(r.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,r.getMonth()-1)-31,i=31-t.getDate()+n+r.getDate();return p(Math.ceil(i/7),2)}return 0===E(t,o)?"01":"00"},"%y":function(e){return(e.tm_year+1900).toString().substring(2)},"%Y":function(e){return e.tm_year+1900},"%z":function(e){var o=e.tm_gmtoff,t=o>=0;return o=(o=Math.abs(o)/60)/60*100+o%60,(t?"+":"-")+String("0000"+o).slice(-4)},"%Z":function(e){return e.tm_zone},"%%":function(){return"%"}};for(var d in f)s.includes(d)&&(s=s.replace(new RegExp(d,"g"),f[d](i)));var R=intArrayFromString(s,!1);return R.length>o?0:(writeArrayToMemory(R,e),R.length-1)}function _strftime_l(e,o,t,r){return _strftime(e,o,t,r)}embind_init_charCodes(),BindingError=RoomsDivisionModule.BindingError=extendError(Error,"BindingError"),InternalError=RoomsDivisionModule.InternalError=extendError(Error,"InternalError"),init_emval(),UnboundTypeError=RoomsDivisionModule.UnboundTypeError=extendError(Error,"UnboundTypeError");var FSNode=function(e,o,t,r){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=FS.nextInode++,this.name=o,this.mode=t,this.node_ops={},this.stream_ops={},this.rdev=r},readMode=365,writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(e){e?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(e){e?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}}),FS.FSNode=FSNode,FS.staticInit(),ERRNO_CODES={EPERM:63,ENOENT:44,ESRCH:71,EINTR:27,EIO:29,ENXIO:60,E2BIG:1,ENOEXEC:45,EBADF:8,ECHILD:12,EAGAIN:6,EWOULDBLOCK:6,ENOMEM:48,EACCES:2,EFAULT:21,ENOTBLK:105,EBUSY:10,EEXIST:20,EXDEV:75,ENODEV:43,ENOTDIR:54,EISDIR:31,EINVAL:28,ENFILE:41,EMFILE:33,ENOTTY:59,ETXTBSY:74,EFBIG:22,ENOSPC:51,ESPIPE:70,EROFS:69,EMLINK:34,EPIPE:64,EDOM:18,ERANGE:68,ENOMSG:49,EIDRM:24,ECHRNG:106,EL2NSYNC:156,EL3HLT:107,EL3RST:108,ELNRNG:109,EUNATCH:110,ENOCSI:111,EL2HLT:112,EDEADLK:16,ENOLCK:46,EBADE:113,EBADR:114,EXFULL:115,ENOANO:104,EBADRQC:103,EBADSLT:102,EDEADLOCK:16,EBFONT:101,ENOSTR:100,ENODATA:116,ETIME:117,ENOSR:118,ENONET:119,ENOPKG:120,EREMOTE:121,ENOLINK:47,EADV:122,ESRMNT:123,ECOMM:124,EPROTO:65,EMULTIHOP:36,EDOTDOT:125,EBADMSG:9,ENOTUNIQ:126,EBADFD:127,EREMCHG:128,ELIBACC:129,ELIBBAD:130,ELIBSCN:131,ELIBMAX:132,ELIBEXEC:133,ENOSYS:52,ENOTEMPTY:55,ENAMETOOLONG:37,ELOOP:32,EOPNOTSUPP:138,EPFNOSUPPORT:139,ECONNRESET:15,ENOBUFS:42,EAFNOSUPPORT:5,EPROTOTYPE:67,ENOTSOCK:57,ENOPROTOOPT:50,ESHUTDOWN:140,ECONNREFUSED:14,EADDRINUSE:3,ECONNABORTED:13,ENETUNREACH:40,ENETDOWN:38,ETIMEDOUT:73,EHOSTDOWN:142,EHOSTUNREACH:23,EINPROGRESS:26,EALREADY:7,EDESTADDRREQ:17,EMSGSIZE:35,EPROTONOSUPPORT:66,ESOCKTNOSUPPORT:137,EADDRNOTAVAIL:4,ENETRESET:39,EISCONN:30,ENOTCONN:53,ETOOMANYREFS:141,EUSERS:136,EDQUOT:19,ESTALE:72,ENOTSUP:138,ENOMEDIUM:148,EILSEQ:25,EOVERFLOW:61,ECANCELED:11,ENOTRECOVERABLE:56,EOWNERDEAD:62,ESTRPIPE:135};var ASSERTIONS=!0;function intArrayFromString(e,o,t){var r=t>0?t:lengthBytesUTF8(e)+1,n=new Array(r),i=stringToUTF8Array(e,n,0,n.length);return o&&(n.length=i),n}function intArrayToString(e){for(var o=[],t=0;t<e.length;t++){var r=e[t];r>255&&(ASSERTIONS&&assert(!1,"Character code "+r+" ("+String.fromCharCode(r)+")  at offset "+t+" not in 0x00-0xFF."),r&=255),o.push(String.fromCharCode(r))}return o.join("")}var calledRun,asmLibraryArg={__cxa_allocate_exception:___cxa_allocate_exception,__cxa_atexit:___cxa_atexit,__cxa_throw:___cxa_throw,_embind_register_bigint:__embind_register_bigint,_embind_register_bool:__embind_register_bool,_embind_register_emval:__embind_register_emval,_embind_register_float:__embind_register_float,_embind_register_function:__embind_register_function,_embind_register_integer:__embind_register_integer,_embind_register_memory_view:__embind_register_memory_view,_embind_register_std_string:__embind_register_std_string,_embind_register_std_wstring:__embind_register_std_wstring,_embind_register_void:__embind_register_void,abort:_abort,emscripten_memcpy_big:_emscripten_memcpy_big,emscripten_resize_heap:_emscripten_resize_heap,environ_get:_environ_get,environ_sizes_get:_environ_sizes_get,fd_close:_fd_close,fd_read:_fd_read,fd_seek:_fd_seek,fd_write:_fd_write,getentropy:_getentropy,setTempRet0:_setTempRet0,strftime_l:_strftime_l},asm=createWasm(),___wasm_call_ctors=RoomsDivisionModule.___wasm_call_ctors=createExportWrapper("__wasm_call_ctors"),_free=RoomsDivisionModule._free=createExportWrapper("free"),_malloc=RoomsDivisionModule._malloc=createExportWrapper("malloc"),___errno_location=RoomsDivisionModule.___errno_location=createExportWrapper("__errno_location"),_fflush=RoomsDivisionModule._fflush=createExportWrapper("fflush"),___getTypeName=RoomsDivisionModule.___getTypeName=createExportWrapper("__getTypeName"),___embind_register_native_and_builtin_types=RoomsDivisionModule.___embind_register_native_and_builtin_types=createExportWrapper("__embind_register_native_and_builtin_types"),stackSave=RoomsDivisionModule.stackSave=createExportWrapper("stackSave"),stackRestore=RoomsDivisionModule.stackRestore=createExportWrapper("stackRestore"),stackAlloc=RoomsDivisionModule.stackAlloc=createExportWrapper("stackAlloc"),_emscripten_stack_init=RoomsDivisionModule._emscripten_stack_init=function(){return(_emscripten_stack_init=RoomsDivisionModule._emscripten_stack_init=RoomsDivisionModule.asm.emscripten_stack_init).apply(null,arguments)},_emscripten_stack_get_free=RoomsDivisionModule._emscripten_stack_get_free=function(){return(_emscripten_stack_get_free=RoomsDivisionModule._emscripten_stack_get_free=RoomsDivisionModule.asm.emscripten_stack_get_free).apply(null,arguments)},_emscripten_stack_get_end=RoomsDivisionModule._emscripten_stack_get_end=function(){return(_emscripten_stack_get_end=RoomsDivisionModule._emscripten_stack_get_end=RoomsDivisionModule.asm.emscripten_stack_get_end).apply(null,arguments)},dynCall_iij=RoomsDivisionModule.dynCall_iij=createExportWrapper("dynCall_iij"),dynCall_jiji=RoomsDivisionModule.dynCall_jiji=createExportWrapper("dynCall_jiji"),dynCall_viijii=RoomsDivisionModule.dynCall_viijii=createExportWrapper("dynCall_viijii"),dynCall_iiiiij=RoomsDivisionModule.dynCall_iiiiij=createExportWrapper("dynCall_iiiiij"),dynCall_iiiiijj=RoomsDivisionModule.dynCall_iiiiijj=createExportWrapper("dynCall_iiiiijj"),dynCall_iiiiiijj=RoomsDivisionModule.dynCall_iiiiiijj=createExportWrapper("dynCall_iiiiiijj");function ExitStatus(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}Object.getOwnPropertyDescriptor(RoomsDivisionModule,"intArrayFromString")||(RoomsDivisionModule.intArrayFromString=function(){abort("'intArrayFromString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"intArrayToString")||(RoomsDivisionModule.intArrayToString=function(){abort("'intArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ccall")||(RoomsDivisionModule.ccall=function(){abort("'ccall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"cwrap")||(RoomsDivisionModule.cwrap=function(){abort("'cwrap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setValue")||(RoomsDivisionModule.setValue=function(){abort("'setValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getValue")||(RoomsDivisionModule.getValue=function(){abort("'getValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"allocate")||(RoomsDivisionModule.allocate=function(){abort("'allocate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"UTF8ArrayToString")||(RoomsDivisionModule.UTF8ArrayToString=function(){abort("'UTF8ArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"UTF8ToString")||(RoomsDivisionModule.UTF8ToString=function(){abort("'UTF8ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stringToUTF8Array")||(RoomsDivisionModule.stringToUTF8Array=function(){abort("'stringToUTF8Array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stringToUTF8")||(RoomsDivisionModule.stringToUTF8=function(){abort("'stringToUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"lengthBytesUTF8")||(RoomsDivisionModule.lengthBytesUTF8=function(){abort("'lengthBytesUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stackTrace")||(RoomsDivisionModule.stackTrace=function(){abort("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"addOnPreRun")||(RoomsDivisionModule.addOnPreRun=function(){abort("'addOnPreRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"addOnInit")||(RoomsDivisionModule.addOnInit=function(){abort("'addOnInit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"addOnPreMain")||(RoomsDivisionModule.addOnPreMain=function(){abort("'addOnPreMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"addOnExit")||(RoomsDivisionModule.addOnExit=function(){abort("'addOnExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"addOnPostRun")||(RoomsDivisionModule.addOnPostRun=function(){abort("'addOnPostRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeStringToMemory")||(RoomsDivisionModule.writeStringToMemory=function(){abort("'writeStringToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeArrayToMemory")||(RoomsDivisionModule.writeArrayToMemory=function(){abort("'writeArrayToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeAsciiToMemory")||(RoomsDivisionModule.writeAsciiToMemory=function(){abort("'writeAsciiToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"addRunDependency")||(RoomsDivisionModule.addRunDependency=function(){abort("'addRunDependency' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"removeRunDependency")||(RoomsDivisionModule.removeRunDependency=function(){abort("'removeRunDependency' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_createFolder")||(RoomsDivisionModule.FS_createFolder=function(){abort("'FS_createFolder' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_createPath")||(RoomsDivisionModule.FS_createPath=function(){abort("'FS_createPath' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_createDataFile")||(RoomsDivisionModule.FS_createDataFile=function(){abort("'FS_createDataFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_createPreloadedFile")||(RoomsDivisionModule.FS_createPreloadedFile=function(){abort("'FS_createPreloadedFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_createLazyFile")||(RoomsDivisionModule.FS_createLazyFile=function(){abort("'FS_createLazyFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_createLink")||(RoomsDivisionModule.FS_createLink=function(){abort("'FS_createLink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_createDevice")||(RoomsDivisionModule.FS_createDevice=function(){abort("'FS_createDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS_unlink")||(RoomsDivisionModule.FS_unlink=function(){abort("'FS_unlink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getLEB")||(RoomsDivisionModule.getLEB=function(){abort("'getLEB' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getFunctionTables")||(RoomsDivisionModule.getFunctionTables=function(){abort("'getFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"alignFunctionTables")||(RoomsDivisionModule.alignFunctionTables=function(){abort("'alignFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerFunctions")||(RoomsDivisionModule.registerFunctions=function(){abort("'registerFunctions' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"addFunction")||(RoomsDivisionModule.addFunction=function(){abort("'addFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"removeFunction")||(RoomsDivisionModule.removeFunction=function(){abort("'removeFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getFuncWrapper")||(RoomsDivisionModule.getFuncWrapper=function(){abort("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"prettyPrint")||(RoomsDivisionModule.prettyPrint=function(){abort("'prettyPrint' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"dynCall")||(RoomsDivisionModule.dynCall=function(){abort("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getCompilerSetting")||(RoomsDivisionModule.getCompilerSetting=function(){abort("'getCompilerSetting' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"print")||(RoomsDivisionModule.print=function(){abort("'print' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"printErr")||(RoomsDivisionModule.printErr=function(){abort("'printErr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getTempRet0")||(RoomsDivisionModule.getTempRet0=function(){abort("'getTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setTempRet0")||(RoomsDivisionModule.setTempRet0=function(){abort("'setTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"callMain")||(RoomsDivisionModule.callMain=function(){abort("'callMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"abort")||(RoomsDivisionModule.abort=function(){abort("'abort' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"keepRuntimeAlive")||(RoomsDivisionModule.keepRuntimeAlive=function(){abort("'keepRuntimeAlive' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"zeroMemory")||(RoomsDivisionModule.zeroMemory=function(){abort("'zeroMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stringToNewUTF8")||(RoomsDivisionModule.stringToNewUTF8=function(){abort("'stringToNewUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setFileTime")||(RoomsDivisionModule.setFileTime=function(){abort("'setFileTime' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"abortOnCannotGrowMemory")||(RoomsDivisionModule.abortOnCannotGrowMemory=function(){abort("'abortOnCannotGrowMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emscripten_realloc_buffer")||(RoomsDivisionModule.emscripten_realloc_buffer=function(){abort("'emscripten_realloc_buffer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ENV")||(RoomsDivisionModule.ENV=function(){abort("'ENV' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"withStackSave")||(RoomsDivisionModule.withStackSave=function(){abort("'withStackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ERRNO_CODES")||(RoomsDivisionModule.ERRNO_CODES=function(){abort("'ERRNO_CODES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ERRNO_MESSAGES")||(RoomsDivisionModule.ERRNO_MESSAGES=function(){abort("'ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setErrNo")||(RoomsDivisionModule.setErrNo=function(){abort("'setErrNo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"inetPton4")||(RoomsDivisionModule.inetPton4=function(){abort("'inetPton4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"inetNtop4")||(RoomsDivisionModule.inetNtop4=function(){abort("'inetNtop4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"inetPton6")||(RoomsDivisionModule.inetPton6=function(){abort("'inetPton6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"inetNtop6")||(RoomsDivisionModule.inetNtop6=function(){abort("'inetNtop6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readSockaddr")||(RoomsDivisionModule.readSockaddr=function(){abort("'readSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeSockaddr")||(RoomsDivisionModule.writeSockaddr=function(){abort("'writeSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"DNS")||(RoomsDivisionModule.DNS=function(){abort("'DNS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getHostByName")||(RoomsDivisionModule.getHostByName=function(){abort("'getHostByName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"GAI_ERRNO_MESSAGES")||(RoomsDivisionModule.GAI_ERRNO_MESSAGES=function(){abort("'GAI_ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"Protocols")||(RoomsDivisionModule.Protocols=function(){abort("'Protocols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"Sockets")||(RoomsDivisionModule.Sockets=function(){abort("'Sockets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getRandomDevice")||(RoomsDivisionModule.getRandomDevice=function(){abort("'getRandomDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"traverseStack")||(RoomsDivisionModule.traverseStack=function(){abort("'traverseStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"UNWIND_CACHE")||(RoomsDivisionModule.UNWIND_CACHE=function(){abort("'UNWIND_CACHE' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readAsmConstArgsArray")||(RoomsDivisionModule.readAsmConstArgsArray=function(){abort("'readAsmConstArgsArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readAsmConstArgs")||(RoomsDivisionModule.readAsmConstArgs=function(){abort("'readAsmConstArgs' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"mainThreadEM_ASM")||(RoomsDivisionModule.mainThreadEM_ASM=function(){abort("'mainThreadEM_ASM' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"jstoi_q")||(RoomsDivisionModule.jstoi_q=function(){abort("'jstoi_q' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"jstoi_s")||(RoomsDivisionModule.jstoi_s=function(){abort("'jstoi_s' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getExecutableName")||(RoomsDivisionModule.getExecutableName=function(){abort("'getExecutableName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"listenOnce")||(RoomsDivisionModule.listenOnce=function(){abort("'listenOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"autoResumeAudioContext")||(RoomsDivisionModule.autoResumeAudioContext=function(){abort("'autoResumeAudioContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"dynCallLegacy")||(RoomsDivisionModule.dynCallLegacy=function(){abort("'dynCallLegacy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getDynCaller")||(RoomsDivisionModule.getDynCaller=function(){abort("'getDynCaller' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"dynCall")||(RoomsDivisionModule.dynCall=function(){abort("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"callRuntimeCallbacks")||(RoomsDivisionModule.callRuntimeCallbacks=function(){abort("'callRuntimeCallbacks' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"wasmTableMirror")||(RoomsDivisionModule.wasmTableMirror=function(){abort("'wasmTableMirror' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setWasmTableEntry")||(RoomsDivisionModule.setWasmTableEntry=function(){abort("'setWasmTableEntry' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getWasmTableEntry")||(RoomsDivisionModule.getWasmTableEntry=function(){abort("'getWasmTableEntry' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"handleException")||(RoomsDivisionModule.handleException=function(){abort("'handleException' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"runtimeKeepalivePush")||(RoomsDivisionModule.runtimeKeepalivePush=function(){abort("'runtimeKeepalivePush' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"runtimeKeepalivePop")||(RoomsDivisionModule.runtimeKeepalivePop=function(){abort("'runtimeKeepalivePop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"callUserCallback")||(RoomsDivisionModule.callUserCallback=function(){abort("'callUserCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"maybeExit")||(RoomsDivisionModule.maybeExit=function(){abort("'maybeExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"safeSetTimeout")||(RoomsDivisionModule.safeSetTimeout=function(){abort("'safeSetTimeout' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"asmjsMangle")||(RoomsDivisionModule.asmjsMangle=function(){abort("'asmjsMangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"asyncLoad")||(RoomsDivisionModule.asyncLoad=function(){abort("'asyncLoad' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"alignMemory")||(RoomsDivisionModule.alignMemory=function(){abort("'alignMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"mmapAlloc")||(RoomsDivisionModule.mmapAlloc=function(){abort("'mmapAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"reallyNegative")||(RoomsDivisionModule.reallyNegative=function(){abort("'reallyNegative' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"unSign")||(RoomsDivisionModule.unSign=function(){abort("'unSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"reSign")||(RoomsDivisionModule.reSign=function(){abort("'reSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"formatString")||(RoomsDivisionModule.formatString=function(){abort("'formatString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"PATH")||(RoomsDivisionModule.PATH=function(){abort("'PATH' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"PATH_FS")||(RoomsDivisionModule.PATH_FS=function(){abort("'PATH_FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"SYSCALLS")||(RoomsDivisionModule.SYSCALLS=function(){abort("'SYSCALLS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"syscallMmap2")||(RoomsDivisionModule.syscallMmap2=function(){abort("'syscallMmap2' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"syscallMunmap")||(RoomsDivisionModule.syscallMunmap=function(){abort("'syscallMunmap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getSocketFromFD")||(RoomsDivisionModule.getSocketFromFD=function(){abort("'getSocketFromFD' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getSocketAddress")||(RoomsDivisionModule.getSocketAddress=function(){abort("'getSocketAddress' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"JSEvents")||(RoomsDivisionModule.JSEvents=function(){abort("'JSEvents' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerKeyEventCallback")||(RoomsDivisionModule.registerKeyEventCallback=function(){abort("'registerKeyEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"specialHTMLTargets")||(RoomsDivisionModule.specialHTMLTargets=function(){abort("'specialHTMLTargets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"maybeCStringToJsString")||(RoomsDivisionModule.maybeCStringToJsString=function(){abort("'maybeCStringToJsString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"findEventTarget")||(RoomsDivisionModule.findEventTarget=function(){abort("'findEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"findCanvasEventTarget")||(RoomsDivisionModule.findCanvasEventTarget=function(){abort("'findCanvasEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getBoundingClientRect")||(RoomsDivisionModule.getBoundingClientRect=function(){abort("'getBoundingClientRect' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillMouseEventData")||(RoomsDivisionModule.fillMouseEventData=function(){abort("'fillMouseEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerMouseEventCallback")||(RoomsDivisionModule.registerMouseEventCallback=function(){abort("'registerMouseEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerWheelEventCallback")||(RoomsDivisionModule.registerWheelEventCallback=function(){abort("'registerWheelEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerUiEventCallback")||(RoomsDivisionModule.registerUiEventCallback=function(){abort("'registerUiEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerFocusEventCallback")||(RoomsDivisionModule.registerFocusEventCallback=function(){abort("'registerFocusEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillDeviceOrientationEventData")||(RoomsDivisionModule.fillDeviceOrientationEventData=function(){abort("'fillDeviceOrientationEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerDeviceOrientationEventCallback")||(RoomsDivisionModule.registerDeviceOrientationEventCallback=function(){abort("'registerDeviceOrientationEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillDeviceMotionEventData")||(RoomsDivisionModule.fillDeviceMotionEventData=function(){abort("'fillDeviceMotionEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerDeviceMotionEventCallback")||(RoomsDivisionModule.registerDeviceMotionEventCallback=function(){abort("'registerDeviceMotionEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"screenOrientation")||(RoomsDivisionModule.screenOrientation=function(){abort("'screenOrientation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillOrientationChangeEventData")||(RoomsDivisionModule.fillOrientationChangeEventData=function(){abort("'fillOrientationChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerOrientationChangeEventCallback")||(RoomsDivisionModule.registerOrientationChangeEventCallback=function(){abort("'registerOrientationChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillFullscreenChangeEventData")||(RoomsDivisionModule.fillFullscreenChangeEventData=function(){abort("'fillFullscreenChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerFullscreenChangeEventCallback")||(RoomsDivisionModule.registerFullscreenChangeEventCallback=function(){abort("'registerFullscreenChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerRestoreOldStyle")||(RoomsDivisionModule.registerRestoreOldStyle=function(){abort("'registerRestoreOldStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"hideEverythingExceptGivenElement")||(RoomsDivisionModule.hideEverythingExceptGivenElement=function(){abort("'hideEverythingExceptGivenElement' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"restoreHiddenElements")||(RoomsDivisionModule.restoreHiddenElements=function(){abort("'restoreHiddenElements' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setLetterbox")||(RoomsDivisionModule.setLetterbox=function(){abort("'setLetterbox' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"currentFullscreenStrategy")||(RoomsDivisionModule.currentFullscreenStrategy=function(){abort("'currentFullscreenStrategy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"restoreOldWindowedStyle")||(RoomsDivisionModule.restoreOldWindowedStyle=function(){abort("'restoreOldWindowedStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"softFullscreenResizeWebGLRenderTarget")||(RoomsDivisionModule.softFullscreenResizeWebGLRenderTarget=function(){abort("'softFullscreenResizeWebGLRenderTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"doRequestFullscreen")||(RoomsDivisionModule.doRequestFullscreen=function(){abort("'doRequestFullscreen' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillPointerlockChangeEventData")||(RoomsDivisionModule.fillPointerlockChangeEventData=function(){abort("'fillPointerlockChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerPointerlockChangeEventCallback")||(RoomsDivisionModule.registerPointerlockChangeEventCallback=function(){abort("'registerPointerlockChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerPointerlockErrorEventCallback")||(RoomsDivisionModule.registerPointerlockErrorEventCallback=function(){abort("'registerPointerlockErrorEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"requestPointerLock")||(RoomsDivisionModule.requestPointerLock=function(){abort("'requestPointerLock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillVisibilityChangeEventData")||(RoomsDivisionModule.fillVisibilityChangeEventData=function(){abort("'fillVisibilityChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerVisibilityChangeEventCallback")||(RoomsDivisionModule.registerVisibilityChangeEventCallback=function(){abort("'registerVisibilityChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerTouchEventCallback")||(RoomsDivisionModule.registerTouchEventCallback=function(){abort("'registerTouchEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillGamepadEventData")||(RoomsDivisionModule.fillGamepadEventData=function(){abort("'fillGamepadEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerGamepadEventCallback")||(RoomsDivisionModule.registerGamepadEventCallback=function(){abort("'registerGamepadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerBeforeUnloadEventCallback")||(RoomsDivisionModule.registerBeforeUnloadEventCallback=function(){abort("'registerBeforeUnloadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"fillBatteryEventData")||(RoomsDivisionModule.fillBatteryEventData=function(){abort("'fillBatteryEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"battery")||(RoomsDivisionModule.battery=function(){abort("'battery' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerBatteryEventCallback")||(RoomsDivisionModule.registerBatteryEventCallback=function(){abort("'registerBatteryEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setCanvasElementSize")||(RoomsDivisionModule.setCanvasElementSize=function(){abort("'setCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getCanvasElementSize")||(RoomsDivisionModule.getCanvasElementSize=function(){abort("'getCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"demangle")||(RoomsDivisionModule.demangle=function(){abort("'demangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"demangleAll")||(RoomsDivisionModule.demangleAll=function(){abort("'demangleAll' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"jsStackTrace")||(RoomsDivisionModule.jsStackTrace=function(){abort("'jsStackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stackTrace")||(RoomsDivisionModule.stackTrace=function(){abort("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getEnvStrings")||(RoomsDivisionModule.getEnvStrings=function(){abort("'getEnvStrings' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"checkWasiClock")||(RoomsDivisionModule.checkWasiClock=function(){abort("'checkWasiClock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeI53ToI64")||(RoomsDivisionModule.writeI53ToI64=function(){abort("'writeI53ToI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeI53ToI64Clamped")||(RoomsDivisionModule.writeI53ToI64Clamped=function(){abort("'writeI53ToI64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeI53ToI64Signaling")||(RoomsDivisionModule.writeI53ToI64Signaling=function(){abort("'writeI53ToI64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeI53ToU64Clamped")||(RoomsDivisionModule.writeI53ToU64Clamped=function(){abort("'writeI53ToU64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeI53ToU64Signaling")||(RoomsDivisionModule.writeI53ToU64Signaling=function(){abort("'writeI53ToU64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readI53FromI64")||(RoomsDivisionModule.readI53FromI64=function(){abort("'readI53FromI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readI53FromU64")||(RoomsDivisionModule.readI53FromU64=function(){abort("'readI53FromU64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"convertI32PairToI53")||(RoomsDivisionModule.convertI32PairToI53=function(){abort("'convertI32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"convertU32PairToI53")||(RoomsDivisionModule.convertU32PairToI53=function(){abort("'convertU32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setImmediateWrapped")||(RoomsDivisionModule.setImmediateWrapped=function(){abort("'setImmediateWrapped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"clearImmediateWrapped")||(RoomsDivisionModule.clearImmediateWrapped=function(){abort("'clearImmediateWrapped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"polyfillSetImmediate")||(RoomsDivisionModule.polyfillSetImmediate=function(){abort("'polyfillSetImmediate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"uncaughtExceptionCount")||(RoomsDivisionModule.uncaughtExceptionCount=function(){abort("'uncaughtExceptionCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"exceptionLast")||(RoomsDivisionModule.exceptionLast=function(){abort("'exceptionLast' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"exceptionCaught")||(RoomsDivisionModule.exceptionCaught=function(){abort("'exceptionCaught' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ExceptionInfo")||(RoomsDivisionModule.ExceptionInfo=function(){abort("'ExceptionInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"CatchInfo")||(RoomsDivisionModule.CatchInfo=function(){abort("'CatchInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"exception_addRef")||(RoomsDivisionModule.exception_addRef=function(){abort("'exception_addRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"exception_decRef")||(RoomsDivisionModule.exception_decRef=function(){abort("'exception_decRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"Browser")||(RoomsDivisionModule.Browser=function(){abort("'Browser' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"funcWrappers")||(RoomsDivisionModule.funcWrappers=function(){abort("'funcWrappers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getFuncWrapper")||(RoomsDivisionModule.getFuncWrapper=function(){abort("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setMainLoop")||(RoomsDivisionModule.setMainLoop=function(){abort("'setMainLoop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"wget")||(RoomsDivisionModule.wget=function(){abort("'wget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"FS")||(RoomsDivisionModule.FS=function(){abort("'FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"MEMFS")||(RoomsDivisionModule.MEMFS=function(){abort("'MEMFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"TTY")||(RoomsDivisionModule.TTY=function(){abort("'TTY' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"PIPEFS")||(RoomsDivisionModule.PIPEFS=function(){abort("'PIPEFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"SOCKFS")||(RoomsDivisionModule.SOCKFS=function(){abort("'SOCKFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"_setNetworkCallback")||(RoomsDivisionModule._setNetworkCallback=function(){abort("'_setNetworkCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"tempFixedLengthArray")||(RoomsDivisionModule.tempFixedLengthArray=function(){abort("'tempFixedLengthArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"miniTempWebGLFloatBuffers")||(RoomsDivisionModule.miniTempWebGLFloatBuffers=function(){abort("'miniTempWebGLFloatBuffers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"heapObjectForWebGLType")||(RoomsDivisionModule.heapObjectForWebGLType=function(){abort("'heapObjectForWebGLType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"heapAccessShiftForWebGLHeap")||(RoomsDivisionModule.heapAccessShiftForWebGLHeap=function(){abort("'heapAccessShiftForWebGLHeap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"GL")||(RoomsDivisionModule.GL=function(){abort("'GL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emscriptenWebGLGet")||(RoomsDivisionModule.emscriptenWebGLGet=function(){abort("'emscriptenWebGLGet' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"computeUnpackAlignedImageSize")||(RoomsDivisionModule.computeUnpackAlignedImageSize=function(){abort("'computeUnpackAlignedImageSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emscriptenWebGLGetTexPixelData")||(RoomsDivisionModule.emscriptenWebGLGetTexPixelData=function(){abort("'emscriptenWebGLGetTexPixelData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emscriptenWebGLGetUniform")||(RoomsDivisionModule.emscriptenWebGLGetUniform=function(){abort("'emscriptenWebGLGetUniform' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"webglGetUniformLocation")||(RoomsDivisionModule.webglGetUniformLocation=function(){abort("'webglGetUniformLocation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"webglPrepareUniformLocationsBeforeFirstUse")||(RoomsDivisionModule.webglPrepareUniformLocationsBeforeFirstUse=function(){abort("'webglPrepareUniformLocationsBeforeFirstUse' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"webglGetLeftBracePos")||(RoomsDivisionModule.webglGetLeftBracePos=function(){abort("'webglGetLeftBracePos' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emscriptenWebGLGetVertexAttrib")||(RoomsDivisionModule.emscriptenWebGLGetVertexAttrib=function(){abort("'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"writeGLArray")||(RoomsDivisionModule.writeGLArray=function(){abort("'writeGLArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"AL")||(RoomsDivisionModule.AL=function(){abort("'AL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"SDL_unicode")||(RoomsDivisionModule.SDL_unicode=function(){abort("'SDL_unicode' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"SDL_ttfContext")||(RoomsDivisionModule.SDL_ttfContext=function(){abort("'SDL_ttfContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"SDL_audio")||(RoomsDivisionModule.SDL_audio=function(){abort("'SDL_audio' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"SDL")||(RoomsDivisionModule.SDL=function(){abort("'SDL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"SDL_gfx")||(RoomsDivisionModule.SDL_gfx=function(){abort("'SDL_gfx' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"GLUT")||(RoomsDivisionModule.GLUT=function(){abort("'GLUT' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"EGL")||(RoomsDivisionModule.EGL=function(){abort("'EGL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"GLFW_Window")||(RoomsDivisionModule.GLFW_Window=function(){abort("'GLFW_Window' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"GLFW")||(RoomsDivisionModule.GLFW=function(){abort("'GLFW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"GLEW")||(RoomsDivisionModule.GLEW=function(){abort("'GLEW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"IDBStore")||(RoomsDivisionModule.IDBStore=function(){abort("'IDBStore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"runAndAbortIfError")||(RoomsDivisionModule.runAndAbortIfError=function(){abort("'runAndAbortIfError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emval_handle_array")||(RoomsDivisionModule.emval_handle_array=function(){abort("'emval_handle_array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emval_free_list")||(RoomsDivisionModule.emval_free_list=function(){abort("'emval_free_list' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emval_symbols")||(RoomsDivisionModule.emval_symbols=function(){abort("'emval_symbols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"init_emval")||(RoomsDivisionModule.init_emval=function(){abort("'init_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"count_emval_handles")||(RoomsDivisionModule.count_emval_handles=function(){abort("'count_emval_handles' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"get_first_emval")||(RoomsDivisionModule.get_first_emval=function(){abort("'get_first_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getStringOrSymbol")||(RoomsDivisionModule.getStringOrSymbol=function(){abort("'getStringOrSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"Emval")||(RoomsDivisionModule.Emval=function(){abort("'Emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emval_newers")||(RoomsDivisionModule.emval_newers=function(){abort("'emval_newers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"craftEmvalAllocator")||(RoomsDivisionModule.craftEmvalAllocator=function(){abort("'craftEmvalAllocator' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emval_get_global")||(RoomsDivisionModule.emval_get_global=function(){abort("'emval_get_global' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emval_methodCallers")||(RoomsDivisionModule.emval_methodCallers=function(){abort("'emval_methodCallers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"emval_registeredMethods")||(RoomsDivisionModule.emval_registeredMethods=function(){abort("'emval_registeredMethods' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"InternalError")||(RoomsDivisionModule.InternalError=function(){abort("'InternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"BindingError")||(RoomsDivisionModule.BindingError=function(){abort("'BindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"UnboundTypeError")||(RoomsDivisionModule.UnboundTypeError=function(){abort("'UnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"PureVirtualError")||(RoomsDivisionModule.PureVirtualError=function(){abort("'PureVirtualError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"init_embind")||(RoomsDivisionModule.init_embind=function(){abort("'init_embind' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"throwInternalError")||(RoomsDivisionModule.throwInternalError=function(){abort("'throwInternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"throwBindingError")||(RoomsDivisionModule.throwBindingError=function(){abort("'throwBindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"throwUnboundTypeError")||(RoomsDivisionModule.throwUnboundTypeError=function(){abort("'throwUnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ensureOverloadTable")||(RoomsDivisionModule.ensureOverloadTable=function(){abort("'ensureOverloadTable' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"exposePublicSymbol")||(RoomsDivisionModule.exposePublicSymbol=function(){abort("'exposePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"replacePublicSymbol")||(RoomsDivisionModule.replacePublicSymbol=function(){abort("'replacePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"extendError")||(RoomsDivisionModule.extendError=function(){abort("'extendError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"createNamedFunction")||(RoomsDivisionModule.createNamedFunction=function(){abort("'createNamedFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registeredInstances")||(RoomsDivisionModule.registeredInstances=function(){abort("'registeredInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getBasestPointer")||(RoomsDivisionModule.getBasestPointer=function(){abort("'getBasestPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerInheritedInstance")||(RoomsDivisionModule.registerInheritedInstance=function(){abort("'registerInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"unregisterInheritedInstance")||(RoomsDivisionModule.unregisterInheritedInstance=function(){abort("'unregisterInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getInheritedInstance")||(RoomsDivisionModule.getInheritedInstance=function(){abort("'getInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getInheritedInstanceCount")||(RoomsDivisionModule.getInheritedInstanceCount=function(){abort("'getInheritedInstanceCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getLiveInheritedInstances")||(RoomsDivisionModule.getLiveInheritedInstances=function(){abort("'getLiveInheritedInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registeredTypes")||(RoomsDivisionModule.registeredTypes=function(){abort("'registeredTypes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"awaitingDependencies")||(RoomsDivisionModule.awaitingDependencies=function(){abort("'awaitingDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"typeDependencies")||(RoomsDivisionModule.typeDependencies=function(){abort("'typeDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registeredPointers")||(RoomsDivisionModule.registeredPointers=function(){abort("'registeredPointers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"registerType")||(RoomsDivisionModule.registerType=function(){abort("'registerType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"whenDependentTypesAreResolved")||(RoomsDivisionModule.whenDependentTypesAreResolved=function(){abort("'whenDependentTypesAreResolved' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"embind_charCodes")||(RoomsDivisionModule.embind_charCodes=function(){abort("'embind_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"embind_init_charCodes")||(RoomsDivisionModule.embind_init_charCodes=function(){abort("'embind_init_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"readLatin1String")||(RoomsDivisionModule.readLatin1String=function(){abort("'readLatin1String' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getTypeName")||(RoomsDivisionModule.getTypeName=function(){abort("'getTypeName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"heap32VectorToArray")||(RoomsDivisionModule.heap32VectorToArray=function(){abort("'heap32VectorToArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"requireRegisteredType")||(RoomsDivisionModule.requireRegisteredType=function(){abort("'requireRegisteredType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"getShiftFromSize")||(RoomsDivisionModule.getShiftFromSize=function(){abort("'getShiftFromSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"integerReadValueFromPointer")||(RoomsDivisionModule.integerReadValueFromPointer=function(){abort("'integerReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"enumReadValueFromPointer")||(RoomsDivisionModule.enumReadValueFromPointer=function(){abort("'enumReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"floatReadValueFromPointer")||(RoomsDivisionModule.floatReadValueFromPointer=function(){abort("'floatReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"simpleReadValueFromPointer")||(RoomsDivisionModule.simpleReadValueFromPointer=function(){abort("'simpleReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"runDestructors")||(RoomsDivisionModule.runDestructors=function(){abort("'runDestructors' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"new_")||(RoomsDivisionModule.new_=function(){abort("'new_' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"craftInvokerFunction")||(RoomsDivisionModule.craftInvokerFunction=function(){abort("'craftInvokerFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"embind__requireFunction")||(RoomsDivisionModule.embind__requireFunction=function(){abort("'embind__requireFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"tupleRegistrations")||(RoomsDivisionModule.tupleRegistrations=function(){abort("'tupleRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"structRegistrations")||(RoomsDivisionModule.structRegistrations=function(){abort("'structRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"genericPointerToWireType")||(RoomsDivisionModule.genericPointerToWireType=function(){abort("'genericPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"constNoSmartPtrRawPointerToWireType")||(RoomsDivisionModule.constNoSmartPtrRawPointerToWireType=function(){abort("'constNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"nonConstNoSmartPtrRawPointerToWireType")||(RoomsDivisionModule.nonConstNoSmartPtrRawPointerToWireType=function(){abort("'nonConstNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"init_RegisteredPointer")||(RoomsDivisionModule.init_RegisteredPointer=function(){abort("'init_RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"RegisteredPointer")||(RoomsDivisionModule.RegisteredPointer=function(){abort("'RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"RegisteredPointer_getPointee")||(RoomsDivisionModule.RegisteredPointer_getPointee=function(){abort("'RegisteredPointer_getPointee' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"RegisteredPointer_destructor")||(RoomsDivisionModule.RegisteredPointer_destructor=function(){abort("'RegisteredPointer_destructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"RegisteredPointer_deleteObject")||(RoomsDivisionModule.RegisteredPointer_deleteObject=function(){abort("'RegisteredPointer_deleteObject' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"RegisteredPointer_fromWireType")||(RoomsDivisionModule.RegisteredPointer_fromWireType=function(){abort("'RegisteredPointer_fromWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"runDestructor")||(RoomsDivisionModule.runDestructor=function(){abort("'runDestructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"releaseClassHandle")||(RoomsDivisionModule.releaseClassHandle=function(){abort("'releaseClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"finalizationGroup")||(RoomsDivisionModule.finalizationGroup=function(){abort("'finalizationGroup' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"detachFinalizer_deps")||(RoomsDivisionModule.detachFinalizer_deps=function(){abort("'detachFinalizer_deps' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"detachFinalizer")||(RoomsDivisionModule.detachFinalizer=function(){abort("'detachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"attachFinalizer")||(RoomsDivisionModule.attachFinalizer=function(){abort("'attachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"makeClassHandle")||(RoomsDivisionModule.makeClassHandle=function(){abort("'makeClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"init_ClassHandle")||(RoomsDivisionModule.init_ClassHandle=function(){abort("'init_ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ClassHandle")||(RoomsDivisionModule.ClassHandle=function(){abort("'ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ClassHandle_isAliasOf")||(RoomsDivisionModule.ClassHandle_isAliasOf=function(){abort("'ClassHandle_isAliasOf' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"throwInstanceAlreadyDeleted")||(RoomsDivisionModule.throwInstanceAlreadyDeleted=function(){abort("'throwInstanceAlreadyDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ClassHandle_clone")||(RoomsDivisionModule.ClassHandle_clone=function(){abort("'ClassHandle_clone' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ClassHandle_delete")||(RoomsDivisionModule.ClassHandle_delete=function(){abort("'ClassHandle_delete' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"deletionQueue")||(RoomsDivisionModule.deletionQueue=function(){abort("'deletionQueue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ClassHandle_isDeleted")||(RoomsDivisionModule.ClassHandle_isDeleted=function(){abort("'ClassHandle_isDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ClassHandle_deleteLater")||(RoomsDivisionModule.ClassHandle_deleteLater=function(){abort("'ClassHandle_deleteLater' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"flushPendingDeletes")||(RoomsDivisionModule.flushPendingDeletes=function(){abort("'flushPendingDeletes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"delayFunction")||(RoomsDivisionModule.delayFunction=function(){abort("'delayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"setDelayFunction")||(RoomsDivisionModule.setDelayFunction=function(){abort("'setDelayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"RegisteredClass")||(RoomsDivisionModule.RegisteredClass=function(){abort("'RegisteredClass' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"shallowCopyInternalPointer")||(RoomsDivisionModule.shallowCopyInternalPointer=function(){abort("'shallowCopyInternalPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"downcastPointer")||(RoomsDivisionModule.downcastPointer=function(){abort("'downcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"upcastPointer")||(RoomsDivisionModule.upcastPointer=function(){abort("'upcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"validateThis")||(RoomsDivisionModule.validateThis=function(){abort("'validateThis' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"char_0")||(RoomsDivisionModule.char_0=function(){abort("'char_0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"char_9")||(RoomsDivisionModule.char_9=function(){abort("'char_9' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"makeLegalFunctionName")||(RoomsDivisionModule.makeLegalFunctionName=function(){abort("'makeLegalFunctionName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"warnOnce")||(RoomsDivisionModule.warnOnce=function(){abort("'warnOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stackSave")||(RoomsDivisionModule.stackSave=function(){abort("'stackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stackRestore")||(RoomsDivisionModule.stackRestore=function(){abort("'stackRestore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stackAlloc")||(RoomsDivisionModule.stackAlloc=function(){abort("'stackAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"AsciiToString")||(RoomsDivisionModule.AsciiToString=function(){abort("'AsciiToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stringToAscii")||(RoomsDivisionModule.stringToAscii=function(){abort("'stringToAscii' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"UTF16ToString")||(RoomsDivisionModule.UTF16ToString=function(){abort("'UTF16ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stringToUTF16")||(RoomsDivisionModule.stringToUTF16=function(){abort("'stringToUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"lengthBytesUTF16")||(RoomsDivisionModule.lengthBytesUTF16=function(){abort("'lengthBytesUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"UTF32ToString")||(RoomsDivisionModule.UTF32ToString=function(){abort("'UTF32ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"stringToUTF32")||(RoomsDivisionModule.stringToUTF32=function(){abort("'stringToUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"lengthBytesUTF32")||(RoomsDivisionModule.lengthBytesUTF32=function(){abort("'lengthBytesUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"allocateUTF8")||(RoomsDivisionModule.allocateUTF8=function(){abort("'allocateUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"allocateUTF8OnStack")||(RoomsDivisionModule.allocateUTF8OnStack=function(){abort("'allocateUTF8OnStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),RoomsDivisionModule.writeStackCookie=writeStackCookie,RoomsDivisionModule.checkStackCookie=checkStackCookie,Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ALLOC_NORMAL")||Object.defineProperty(RoomsDivisionModule,"ALLOC_NORMAL",{configurable:!0,get:function(){abort("'ALLOC_NORMAL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),Object.getOwnPropertyDescriptor(RoomsDivisionModule,"ALLOC_STACK")||Object.defineProperty(RoomsDivisionModule,"ALLOC_STACK",{configurable:!0,get:function(){abort("'ALLOC_STACK' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}});var calledMain=!1;function stackCheckInit(){_emscripten_stack_init(),writeStackCookie()}function run(e){function o(){calledRun||(calledRun=!0,RoomsDivisionModule.calledRun=!0,ABORT||(initRuntime(),RoomsDivisionModule.onRuntimeInitialized&&RoomsDivisionModule.onRuntimeInitialized(),assert(!RoomsDivisionModule._main,'compiled without a main, but one is present. if you added it from JS, use RoomsDivisionModule["onRuntimeInitialized"]'),postRun()))}e=e||arguments_,runDependencies>0||(stackCheckInit(),preRun(),runDependencies>0||(RoomsDivisionModule.setStatus?(RoomsDivisionModule.setStatus("Running..."),setTimeout((function(){setTimeout((function(){RoomsDivisionModule.setStatus("")}),1),o()}),1)):o(),checkStackCookie()))}function checkUnflushedContent(){var e=out,o=err,t=!1;out=err=function(e){t=!0};try{var r=RoomsDivisionModule._fflush;r&&r(0),["stdout","stderr"].forEach((function(e){var o=FS.analyzePath("/dev/"+e);if(o){var r=o.object.rdev,n=TTY.ttys[r];n&&n.output&&n.output.length&&(t=!0)}}))}catch(e){}out=e,err=o,t&&warnOnce("stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the FAQ), or make sure to emit a newline when you printf etc.")}function exit(e,o){(EXITSTATUS=e,checkUnflushedContent(),keepRuntimeAlive())?o||err("program exited (with status: "+e+"), but EXIT_RUNTIME is not set, so halting execution but not exiting the runtime or preventing further async execution (build with EXIT_RUNTIME=1, if you want a true shutdown)"):exitRuntime();procExit(e)}function procExit(e){EXITSTATUS=e,keepRuntimeAlive()||(RoomsDivisionModule.onExit&&RoomsDivisionModule.onExit(e),ABORT=!0),quit_(e,new ExitStatus(e))}if(dependenciesFulfilled=function e(){calledRun||run(),calledRun||(dependenciesFulfilled=e)},RoomsDivisionModule.run=run,RoomsDivisionModule.preInit)for("function"==typeof RoomsDivisionModule.preInit&&(RoomsDivisionModule.preInit=[RoomsDivisionModule.preInit]);RoomsDivisionModule.preInit.length>0;)RoomsDivisionModule.preInit.pop()();run();