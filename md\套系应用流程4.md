# TSeriesFurnisher.onSeriesSampleSelected方法解析

## 方法概述

`onSeriesSampleSelected`是LayoutAI应用中`TSeriesFurnisher`类的核心方法，负责在用户选择套系样本后，将其应用到选定的房间中。该方法处理了从套系选择到材质匹配、多样化处理、3D场景更新以及自动微调的整个过程。这是套系应用流程在本工程内核层的起点。

## 方法定义及参数

```typescript
async onSeriesSampleSelected(
    scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean }, 
    series: TSeriesSample,
    targetRooms: TRoom[] = null,
    options: { 
        needsDiversifyMatched?: boolean, 
        updateTopViewBy3d?: boolean,
        needAutoFinetune?: boolean
    } = { 
        needsDiversifyMatched: true, 
        updateTopViewBy3d: true, 
        needAutoFinetune: true 
    }
)
```

### 参数说明：

- **scope**: 应用范围，包括软装（soft）、定制柜（cabinet）、硬装（hard）和补全（remaining）
- **series**: 用户选择的套系样本
- **targetRooms**: 需要应用套系的目标房间列表，如果为null则使用当前选中的房间列表
- **options**: 套系应用的选项
  - needsDiversifyMatched: 是否需要多样化匹配
  - updateTopViewBy3d: 是否需要更新3D视图
  - needAutoFinetune: 是否需要自动微调

## 执行流程详解

### 1. 初始化处理

首先，方法取消当前被选中图元的选中状态，并确定目标房间列表：

```typescript
// 取消被选中图元的选中状态
LayoutAI_App.emit_M(EventName.FigureElementSelected, null);

// 如果未指定目标房间列表，则使用当前选中的房间列表
if (!targetRooms) targetRooms = this.current_rooms;
```

### 2. 遍历目标房间列表，进行套系应用

方法使用`Promise.all`和`map`方法，对每个房间进行并行的套系应用处理：

```typescript
let needFurnishRooms: TRoom[] = []; // 用于记录需要应用套系的房间列表

let allPromises = targetRooms.map(async (roomItem: TRoom) => {
    // 如果房间被锁定，则此房间跳过套系应用流程
    if (roomItem.locked) return;
    
    // 将需要应用套系的房间添加到needFurnishRooms中
    needFurnishRooms.push(roomItem);
    
    // 创建一个新的套系实例
    let newSeries = new TSeriesSample(series);
    let promise_list = null;
    
    // 根据scope.remaining决定套系应用策略
    if (scope.remaining == false) {
        // 常规套系应用流程
        roomItem.addApplyScope(scope, newSeries);
        roomItem.setCurrentApplyScope(scope);
        roomItem.clearMatchedMaterialsInCurrentScope();
        this.room2SeriesSampleMap.set(roomItem, newSeries);
        promise_list = this.tryToFurnishRooms([roomItem]);
    } else {
        // 补全套系应用流程
        roomItem.setCurrentApplyScope({ soft: false, cabinet: false, hard: false, remaining: true });
        promise_list = this.tryToFurnishRoomRemainings([roomItem], newSeries);
    }
    
    // 等待所有套系应用完成
    return Promise.allSettled(promise_list).then(() => {
        // 更新UI层：更新选中房间列表、房间套系的映射关系
        this.emitSeriesSamplesWithOrdering();
        roomItem.kgId = (series as any)["kgId"];
    });
});

// 等待所有房间的套系应用完成
await Promise.allSettled(allPromises);
```

### 3. 多样化处理（可选）

如果options.needsDiversifyMatched为true，则进行多样化处理：

```typescript
if (options.needsDiversifyMatched) {
    let logger = this._logger;
    // 根据房间数量和类型进行多样化匹配
    // 例如，确保相邻房间的某些元素使用不同的材质，增加空间变化感
}
```

### 4. 3D场景更新和报价数据生成

```typescript
// 如果3D场景有效，则通知更新3D场景
if (LayoutAI_App.instance.scene3D?.isValid()) {
    LayoutAI_App.emit(Scene3DEvents.UpdateScene3D, true);
} else if (LayoutAI_App.instance.scene3D) {
    if (options.updateTopViewBy3d) {
        await Scene3DEventsProcessor.UpdateEntitiesNeedsTopViewImage();
    }
}

// 应用套系后，通知UI层去根据套系生成报价数据
LayoutAI_App.emit(EventName.quoteDataSeries, series);
this._current_series = series; // 记录当前套系

// 更新UI层，重新绘制canvas
this.update();
```

### 5. 自动微调（可选）

如果options.needAutoFinetune为true，则进行自动微调：

```typescript
if (options.needAutoFinetune) {
    await this.autoFineTuning();
}
```

### 6. 检查空房间情况

```typescript
// 如果当前没有房间被选中，则通知UI层弹出提示，提示用户先选择空间
if (this.current_rooms == null || this.current_rooms.length == 0) {
    LayoutAI_App.emit(EventName.PerformFurnishResult, { 
        progress: "info", 
        message: LayoutAI_App.t("请先选择空间") 
    });
}
```

## 关键调用流程剖析

### tryToFurnishRooms 方法

`tryToFurnishRooms`是执行套系匹配的核心方法，它为指定的房间列表应用套系、匹配套系素材：

```typescript
tryToFurnishRooms(targetRooms: TRoom[]): Promise<any>[] {
    let logger = this._logger;
    let promises: Promise<any>[] = [];
    
    for (let roomItem of targetRooms) {
        // 先将furniture绑定room
        roomItem._furniture_list.forEach(ele => ele._room = roomItem);
        
        // 更新房间已应用套系的范围
        roomItem.updateFurnishedApplyScope();
        
        // 如果房间已应用套系，并且房间中有图元，则匹配套系素材
        if (this.room2SeriesSampleMap.has(roomItem) && (roomItem._furniture_list.length > 0)) {
            // 所有匹配到的素材列表
            let allMatchedMaterials: I_MaterialMatchingItem[] = [];
            
            // 从room2SeriesSampleMap中获取房间当前所对应的套系
            // 然后进入房间匹配套系素材的流程
            promises.push(TMaterialMatcher.instance.furnishRoom(
                roomItem, 
                this.room2SeriesSampleMap.get(roomItem), 
                logger, 
                allMatchedMaterials
            ));
        }
    }
    
    return promises;
}
```

### TMaterialMatcher.furnishRoom 方法

`furnishRoom`方法负责为特定房间匹配套系素材，是套系应用的核心环节：

```typescript
async furnishRoom(
    roomItem: TRoom, 
    seriesSampleItem: TSeriesSample, 
    logger: Logger, 
    matchedMaterials: I_MaterialMatchingItem[]
) {
    // 如果房间被锁定，则直接返回，不匹配套系素材
    if (roomItem.locked) return;
    
    // 更新房间当前应用的套系
    roomItem._series_sample_info = seriesSampleItem;
    
    // 记录房间的应用范围所对应的套系
    roomItem.updateCurrentScopeSeries(seriesSampleItem);
    
    // 从_furniture_list中删除房间中的电器装饰图元
    TPostDecoratesLayout.post_clean_electricity_decorations(roomItem, roomItem._furniture_list);
    
    // 获取需要匹配套系素材的图元列表
    let toBeMatchedFigureElements: TFigureElement[] = [];
    
    // 判断当前房间是否正在走补全的套系应用流程
    if (!roomItem.isCurrentApplyRemainingCategory()) {
        // 如果不是，则获取房间中需要匹配套系素材的图元列表
        toBeMatchedFigureElements = roomItem._furniture_list.filter((fe) => {
            if (compareNames([fe.modelLoc], TMaterialMatchingConfigs._ignoreCategories)) return false;
            return true;
        });
        // 当前是常规套系应用流程，所以要清空此图元列表
        roomItem._remaining_figure_list = [];
    } else {
        // 当前是补全套系应用流程，所以先获取房间中未匹配套系素材的图元列表
        toBeMatchedFigureElements.push(...roomItem._furniture_list.filter((fe) => !fe.haveMatchedMaterial()));
    }
    
    // 硬装处理逻辑
    let waitingHardElements: TFigureElement[] = [];
    if (roomItem.isCurrentApplyHardCategory()) {
        if (!roomItem.haveGeneratedHardElements) {
            // 生成当前房间的硬装图元
            waitingHardElements = this.makeHardFurnishingElements(roomItem);
        } else {
            // 获取当前房间的硬装图元
            waitingHardElements = roomItem.getHardFurnishingElements();
        }
        // 将硬装图元添加到需要匹配套系素材的图元列表中
        toBeMatchedFigureElements.push(...waitingHardElements);
    }
    
    // 过滤图元
    toBeMatchedFigureElements = toBeMatchedFigureElements.filter((fe) => {
        // 判断当前图元是否在当前房间的应用范围内，并且没有被锁定
        let shouldFilter: boolean = roomItem.isFigureElementInCurrentScope(fe) && !fe.locked;
        if (shouldFilter) {
            // 清空图元的匹配素材
            if (roomItem.isCurrentApplyRemainingCategory()) {
                fe.clearMatchedMaterials();
            } else {
                fe.clearAllMatchedMaterials();
            }
        } else if (!roomItem.isCurrentApplyRemainingCategory() && !fe.haveMatchedMaterial()) {
            // 如果当前图元被排除在当前常规套系应用流程中，并且没有匹配到素材，则将此图元添加到_remaining_figure_list
            roomItem._remaining_figure_list.push(fe);
        }
        return shouldFilter;
    });
    
    // 从服务器获取匹配的素材
    await MaterialService.matchMaterials(
        toBeMatchedFigureElements,
        seriesSampleItem.seriesKgId as number,
        seriesSampleItem.seriesName,
        seriesSampleItem.seedSchemeId,
        // 其他参数...
    );
    
    // 后处理逻辑
    // 遍历toBeMatchedFigureElements的所有图元，检查哪些没有匹配到素材
    toBeMatchedFigureElements.forEach((fe) => {
        let haveMatched: boolean = false;
        // 判断逻辑...
        if (!haveMatched) {
            // 如果当前图元没有匹配到素材，则根据当前是否属于补全套系应用流程，将当前图元添加到相应列表
            if (!roomItem.isCurrentApplyRemainingCategory()) {
                roomItem._remaining_figure_list.push(fe);
            } else {
                roomItem._unmatched_remaining_figure_list.push(fe);
            }
        }
    });
    
    // 如果图元没有匹配到素材，则使用图元默认素材
    this.checkAndUpdateDefaultMaterialIds(toBeMatchedFigureElements, matchedMaterials);
    // 其他默认素材处理...
    
    // 素材和房间关联，将素材的roomUid设置为当前房间的uid
    let allMatchedMaterials = matchedMaterials.concat(allMemberMaterials);
    allMatchedMaterials.forEach((material) => {
        if (!material.roomUid) {
            material.roomUid = roomItem.uid;
        }
    });
    
    // 生成组合家具实体
    for (let fe of groupElements) {
        if (!fe.haveMatchedMaterial()) continue;
        await this.generateGroupMemberMatchedEntities(fe);
    }
    
    // 匹配素材的后处理流程
    MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements(roomItem, allMatchedMaterials);
    MatchingPostProcesser.adjust_backgroundwall_related(roomItem);
    // 其他后处理逻辑...
}
```

### MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements 方法

这个方法负责调整匹配的素材和图元，确保它们的位置、大小和方向正确：

```typescript
public static adjustMatchedMaterialsAndFigureElements(room: TRoom, allMatchedMaterial: I_MaterialMatchingItem[]) {
    if (!allMatchedMaterial) return;
    
    for (let material of allMatchedMaterial) {
        // 将图元和素材进行关联
        let feIndex = material["index"];
        let figure_element = material.figureElement;
        if (figure_element == null || !figure_element) {
            console.error("Error: invalid index value " + feIndex)
            continue;
        }
        figure_element._matched_material = material;
        
        if (!material.modelId) continue;
        
        figure_element.params.materialId = material.modelId;
        
        // 修改素材的目标布置位置和尺寸
        MatchingPostProcesser.modifyMaterialPositionSize(figure_element, material);
        
        if (compareNames([material?.figureElement?.category], ["吊顶"])) {
            // 吊顶特殊处理
            material.figureElement.matched_rect = material.figureElement.rect.clone();
        } else {
            // 将素材的位置尺寸信息同步到图元的matchedRect中
            MatchingPostProcesser.syncMatchedRectAndModifyMaterialPositionZ(material, room);
        }
    }
}
```

### autoFineTuning 方法

`autoFineTuning`是套系应用后的自动微调过程，用于优化家具的位置和方向：

```typescript
private async autoFineTuning() {
    TLayoutFineTuningManagerToolUtil.instance.postMatchAutoFineTuning(this.room_list);
    this.update();
}
```

### TLayoutFineTuningManagerToolUtil.postMatchAutoFineTuning 方法

这个方法负责对房间中的图元进行自动微调，改善布局：

```typescript
public postMatchAutoFineTuning(rooms: TRoom[]) {
    for (let room of rooms) {
        this.fineTuningRoom(room);
    }
}

public fineTuningRoom(room: TRoom) {
    let sourcefigures: TFigureElement[] = room._furniture_list;
    sourcefigures = TBaseRoomToolUtil.instance.getAllSingleFigureFromGroup(sourcefigures);
    let figures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.copyFigures(sourcefigures);
    
    // 预处理：让适合靠墙的家具先靠墙
    this.preStickWallForFineTuningFigures(room, figures);
    
    // 迭代微调，直到达到稳定状态
    while (true) {
        let lastTrimInfo: any = getFineTuningInfoByLayoutScore(room, figures);
        this.fineTuningFigures(room, figures, TLayoutFineTuningManagerToolUtil._fineTuningMoveStep, TLayoutFineTuningManagerToolUtil._fineTuningScaleStep);
        let fineTuningInfo: any = getFineTuningInfoByLayoutScore(room, figures);
        
        // 如果前后两次的评分差异很小，则认为已经达到稳定状态
        if (Math.abs(lastTrimInfo.fineTuningValue - fineTuningInfo.fineTuningValue) < 0.1) {
            break;
        }
    }
    
    // 将微调后的图元信息应用到房间
    TLayoutFineTuningOperationToolUtil.instance.setRoomFurnitures(room, figures);
}
```

## 套系应用流程总结

1. **初始化** - 确定目标房间并取消当前图元选中状态
2. **应用套系** - 对每个房间应用选择的套系
   - 常规应用 - 根据scope进行全新的素材匹配
   - 补全应用 - 仅对未匹配素材的图元进行匹配
3. **套系匹配** - 对每个图元找到合适的套系素材
   - 过滤需要匹配的图元
   - 从服务器获取匹配素材
   - 处理未匹配到素材的图元
4. **素材后处理** - 调整素材和图元的位置、大小与方向
5. **多样化处理** - 确保不同房间的素材有足够变化（可选）
6. **3D场景更新** - 更新3D视图以反映套系应用
7. **自动微调** - 优化家具布局位置（可选）

## 重要概念解释

- **套系（series）**: 一组风格一致的家居素材集合，用于统一房间风格
- **应用范围（scope）**: 决定套系应用的类别范围
  - soft: 软装（如沙发、床等常规家具）
  - cabinet: 定制柜（如衣柜、鞋柜等定制家具）
  - hard: 硬装（如地板、墙面等固定装修）
  - remaining: 补全（对未匹配到材质的图元进行匹配）
- **图元（FigureElement）**: 布局中的基本元素，如家具、装饰物等
- **素材（Material）**: 图元的具体表现形式，包含3D模型、材质等信息
- **微调（FineTuning）**: 对家具位置和方向的细微调整，使布局更合理

## 调用层级关系

```
onSeriesSampleSelected
├── tryToFurnishRooms / tryToFurnishRoomRemainings
│   └── TMaterialMatcher.furnishRoom
│       ├── MaterialService.matchMaterials (远程API调用)
│       ├── TPostDecoratesLayout.post_clean_electricity_decorations
│       ├── makeHardFurnishingElements (硬装图元生成)
│       ├── generateGroupMemberMatchedEntities (组合家具处理)
│       └── MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements
│           ├── modifyMaterialPositionSize
│           └── syncMatchedRectAndModifyMaterialPositionZ
├── Scene3DEventsProcessor.UpdateEntitiesNeedsTopViewImage (3D场景更新)
└── autoFineTuning 
    └── TLayoutFineTuningManagerToolUtil.postMatchAutoFineTuning
        └── fineTuningRoom
            ├── preStickWallForFineTuningFigures (家具靠墙处理)
            ├── getFineTuningInfoByLayoutScore (布局评分)
            └── fineTuningFigures (图元位置微调)
```

此文档提供了对`TSeriesFurnisher.onSeriesSampleSelected`方法的全面解析，展示了LayoutAI应用中套系应用的完整流程，从用户选择套系到最终将套系应用到房间布局中的每个步骤。 