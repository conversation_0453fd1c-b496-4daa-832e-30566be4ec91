import { TAppManagerBase } from '../../AppManagerBase';
import { I_PainterTransform, ZPainter } from "@layoutai/z_polygon";
import { TPainter } from './TPainter';
import { AI_CadData } from '../AICadData/AI_CadData';
import { Matrix3, Vector2 } from 'three';
import { LayoutAiCadDataParser } from '../Layout/TLayoutEntities/loader/LayoutAiCadDataParser';

export enum CadDrawingLayerType {
  BlankDrawing = 'BlankDrawing', // 空图层, 应该是对应毛坯图层---保留，暂时没用

  CadEzdxfDrawing = 'CadEzdxfDrawing', // EzdxfCad数据层, 原始数据层
  CadRoomStrucure = 'CadRoomStrucure', // Cad房型数据层
  CadFurniture = 'CadFurniture', // Cad家具图元数据层
  CadCabinet = 'CadCabinet', // Cad定制柜图元数据层
  CadOutLine = 'CadOutLine', // Cad内轮廓数据层
  CadRoomName = 'CadRoomName', // 房间名称层
  CadDecorates = 'CadDecorates', // Cad饰品层
  CadSubRoomAreaDrawing = 'CadSubRoomAreaDrawing', // 子分区
  CadLighting = 'CadLighting', // Cad灯光层
  CadElectricity = 'CadElectricity',
  CadCeiling = 'CadCeiling', // Cad吊顶层
  AILayoutDrawing = 'AILayoutDrawing', // AI布局结果层--- 用于智能布置
  AIMatchingDrawing = 'AIMatchingDrawing', // AI布局结果层--- 用于智能布置
  CadFloorDrawing = 'CadFloorDrawing', // Cad地板数据层
  ExtDrawingDrawing = 'ExtDrawingDrawing',
  ExportCadDrawing = 'ExportCadDrawing',
  CadDimensionWallElement = 'CadDimensionWallElements', // 墙线标注层
  CadDimensionOutterWallElement = 'CadDimensionOutterWallElements', // 外墙标注层
  CadCopyImageDrawing = 'CadCopyImageDrawing', //临摹图
  RulerDrawing = 'RulerDrawing', // 标尺层
  Remodeling = 'Remodeling', // 拆改图层
  Scene3DViewImageLayer = 'Scene3DViewImageLayer',
  BaseSpaceDrawingLayer = 'BaseSpaceDrawingLayer',
  CadCamera = 'CadCamera' // 相机层
}

export enum CadBatchDrawingLayerType {
  AICadDefaultBatch = 'AICadDefaultBatch', // Cad房型数据层
  CadFurnitureBatch = 'CadFurnitureBatch', // Cad家具图元数据层
  ExtDrawingBatch = 'ExtDrawingBatch' // 扩展数据图元层
}

export interface I_LayerCanvasInfo {
  transform_matrix: Matrix3;
  left?: number;
  top?: number;
  p_sc?: number;
}
/**
 *  对应一张Cad绘制图层
 */
export class TDrawingLayer {
  cad_drawing_type: CadDrawingLayerType | string;

  private _visible: boolean;
  protected _dirty: boolean;

  protected _manager: TAppManagerBase;

  protected _layer_canvas: HTMLCanvasElement;
  protected _layer_canvas_drawing_info: I_LayerCanvasInfo;
  protected _z_index: number;

  protected _layer_scale = 2;

  /**
   *  非脏绘制次数
   */
  protected _cleaned_drawing_count = 0;

  constructor(
    layer_type: string = CadDrawingLayerType.BlankDrawing,
    manager: TAppManagerBase = null
  ) {
    this.cad_drawing_type = layer_type;
    this._manager = manager;

    this._visible = true;
    this._dirty = false;
    this._layer_canvas = null;

    this._z_index = 0;
    this._layer_canvas_drawing_info = {
      transform_matrix: null
    };
  }

  get layerCanvas() {
    return this._layer_canvas;
  }
  set z_index(t: number) {
    this._z_index = t;
  }

  get z_index() {
    return this._z_index;
  }

  get cleaned_drawing_count() {
    return this._cleaned_drawing_count;
  }

  get layout_container() {
    return this._manager.layout_container;
  }

  get ai_cad_data() {
    return this._manager.layout_container._ai_cad_data;
  }

  set ai_cad_data(data: AI_CadData) {
    LayoutAiCadDataParser.Container = this.layout_container;
    LayoutAiCadDataParser.fromAiCadData(data);
  }

  get painter() {
    return this._manager.painter;
  }

  get visible() {
    return this._visible;
  }

  set visible(t: boolean) {
    this._visible = t;
    if (this._layer_canvas) {
      this._layer_canvas.style.display = this._visible ? 'block' : 'none';
    }
  }

  public switch() {
    this.visible = !this.visible;
  }

  _updateLayerContent() {
    this._dirty = false;
  }

  makeDirty() {
    this._dirty = true;
  }
  onDraw() {} // 启动绘制


  disposeLayerCanvas()
  {
    if(this._layer_canvas)
    {
      if(this._layer_canvas.parentElement)
      {
        this._layer_canvas.parentElement.removeChild(this._layer_canvas);
      }
      delete this._layer_canvas;
      this._layer_canvas = null;
    }
  }
  checkDrawingLayerCanvas(dirty: boolean) {
    if (this._manager?.canvas) {
      if (!this._layer_canvas) {
        this._layer_canvas = document.createElement('canvas');
      }
      let parent = this._manager.canvas.parentElement as HTMLDivElement;
      if (parent) {
        if (!parent.contains(this._layer_canvas)) {
          parent.insertBefore(this._layer_canvas, this._manager.canvas);
        }
      }
      if (dirty) {
        this._layer_canvas.className = this._manager.canvas.className;
        this._layer_canvas.width = this._manager.canvas.width;
        this._layer_canvas.height = this._manager.canvas.height;
        this._layer_canvas.style.width = this._manager.canvas.style.width;
        this._layer_canvas.style.height = this._manager.canvas.style.height;
        this._layer_canvas.style.left = this._manager.canvas.style.left;
        this._layer_canvas.style.top = this._manager.canvas.style.top;
      }
    }
  }

  /**
   *  绘制图层: 有可能绘制在canvas上
   */
  drawLayer(dirty: boolean, is_moving_element: boolean = false) {
    this.checkDrawingLayerCanvas(dirty);

    if (!dirty) {
      if (!this._layer_canvas_drawing_info || !this.painter) {
        dirty = true;
      } else {
        let df_pc = Math.abs(this._layer_canvas_drawing_info.p_sc - this.painter._p_sc) * 100;

        if (df_pc > 0.01) {
          dirty = true;
        }
      }
    }
    if (dirty) {
      this.painter.bindCanvas(this._layer_canvas);
      this.painter.clean();
      this.painter.enter_drawpoly();
      this.onDraw();
      this.painter.leave_drawpoly();
      this.painter.bindCanvas(this._manager.canvas);
      this._layer_canvas_drawing_info.transform_matrix = this.painter.getCanvasProject2D_Matrix3();
      this._layer_canvas_drawing_info.p_sc = this.painter._p_sc;
      this._layer_canvas.style.transform = '';
      this.painter._context.resetTransform();
      this.painter.enter_drawpoly();
      this._cleaned_drawing_count = 0;
    } else {
      this.updateLayerCanvasTransform();
      this._cleaned_drawing_count++;
    }
  }

  updateLayerCanvasTransform() {
    if (!this._layer_canvas || !this._layer_canvas_drawing_info.transform_matrix) return;
    if (!this.painter) return;

    let current_matrix = this.painter.getCanvasProject2D_Matrix3();
    let last_matrix = this._layer_canvas_drawing_info.transform_matrix;

    let inverse_matrix = last_matrix.clone().invert();
    let result_matrix = current_matrix.clone().multiply(inverse_matrix);

    let arr = result_matrix.toArray();
    let a = arr[3 * 0 + 0];
    let b = arr[3 * 0 + 1];
    let c = arr[3 * 1 + 0];
    let d = arr[3 * 1 + 1];
    let e = arr[3 * 2 + 0];
    let f = arr[3 * 2 + 1];

    let domMatrix = new DOMMatrix([a, b, c, d, e, f]);
    this._layer_canvas.style.transform = domMatrix.toString();
  }
}
