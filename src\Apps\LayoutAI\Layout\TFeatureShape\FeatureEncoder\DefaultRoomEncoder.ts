
import { ZEdge } from "@layoutai/z_polygon";
import { I_Window } from "../../IRoomInterface";

import { TRoomShape } from "../../TRoomShape";
import { TFeatureShape } from "../TFeatureShape";
import { WPolygon } from "../WPolygon";
import { FeatureEncoder } from "./FeatureEncoder";

export class DefaultRoomEncoder extends FeatureEncoder
{
    constructor(){
        super();
        // this.code_len_level = CodedLevelLen / 2;
    }
    encode(feature_shape: TFeatureShape): string[] {
        let shape_code = "";
        
        feature_shape.level_shape_codes = [];
        if(!feature_shape._contours)
        {
            return [];
        }
        let poly = feature_shape._contours._polygons[0];


        let target_edges : ZEdge[] = [];
        for(let edge of poly.edges)
        {
            target_edges.push(edge);
        }

        let main_door :I_Window = null;

        for(let edge of poly.edges)
        {
            let windows = TRoomShape.getWindowsOfEdge(edge);
            if(!windows) continue;
            
            for(let win of windows)
            {
                if(win.type === "Door")
                {
                    if(!main_door || win.length > main_door.length)
                    {
                        main_door = win;
                    }

                }
            }
        }
        let edge_weight = function(edge:ZEdge){

            let ww = 0;


            let windows = TRoomShape.getWindowsOfEdge(edge);

            if(!windows) return ww;


            return ww;
        }
        target_edges.sort((a,b)=>edge_weight(b)-edge_weight(a));



        let ti = 0;
        let target_edge : ZEdge = target_edges[0];

        if(!target_edge)
        {
            console.log("EncodeError: Can't find target_edge");
            return;
        }
        ti = poly.edges.indexOf(target_edge);


        let ww = target_edge.length;
        let hh = Math.min(target_edge.next_edge.length,target_edge.prev_edge.length);






        feature_shape.level_shape_codes.push(shape_code);

        let code1 = "";
        let code2 = "";

        let wp_code = "";
        let code_dict :{[key:string]:number}= {};
        code_dict["Wall"] = 0;
        code_dict["Door"] = 1;
        code_dict["Window"] = 2;
        code_dict["Hallway"] = 1;
        if(feature_shape._w_poly)
        {

            code1 = "";
            let edge0 = feature_shape._w_poly.edges[0];
            let nor0 = edge0.nor;
            for(let edge of feature_shape._w_poly.edges)
            {
                let win = WPolygon.getWindowOnEdge(edge)?.type||"Wall";
 
                let val = code_dict[win];
                if(val && edge.length > 1200)
                {
                    val = 1;
                }
                let t_val = Math.abs(edge.nor.dot(nor0))>0.1?0:1;
                code1 += ""+(val + t_val * 5);
                
            }

            
            for(let i=code1.length; i >=1; i--)
            {
                code2 += code1[i % code1.length];
            }

            wp_code += code1.localeCompare(code2)>0?code1:code2;

            if(code1.localeCompare(code2)<0)
            {
                let tmp = code1; code1 = code2; code2 = tmp;
            }
            
            feature_shape.level_shape_codes.length = 0;
            feature_shape.level_shape_codes.push(code1);


        
            feature_shape.level_shape_codes.push(code2);

            
        }
        else{
            feature_shape.level_shape_codes.push("");

        }

        // TsAI_app.log(feature_shape.level_shape_codes,feature_shape);

        return feature_shape.level_shape_codes;
    }
}