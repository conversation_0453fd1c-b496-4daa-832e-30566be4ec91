import { EventName } from "@/Apps/EventSystem";
import { I_SwjXmlScheme } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { GenDateUUid } from "@layoutai/z_polygon";
import { Logger } from "@/Apps/LayoutAI/Utils/logger";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import SunvegaAPI from "@api/clouddesign";
import { spaceArrange } from "../Services";


/**
 *  跟3D云设计的数据通信中心
 */
export class Sunvega3DApiCenter
{
    constructor()
    {

    }

    // /**
    //  * 从3D云设计获取3D方案数据
    //  * @returns  
    //  */
    // static async load3DSchemeData() : Promise<I_SwjXmlScheme>
    // {
    //     let houseStrutureResponse = await SunvegaAPI.BasicBiz.HouseApi.getHouseStructure();
    
    //     if(!houseStrutureResponse?.data?.house) return null;
    
    //     let houseStruture:I_SwjXmlScheme = (houseStrutureResponse.data as SunvegaAPI.BasicBiz.HouseApi.GetHouseStructureOutput).house as any as I_SwjXmlScheme;

    //     return houseStruture;
    // }



}