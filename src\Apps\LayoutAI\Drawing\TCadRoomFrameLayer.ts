import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { IRoomEntityRealType, IRoomEntityType } from "../Layout/IRoomInterface";
import { TAppManagerBase } from "../../AppManagerBase";
import { TRoomEntity } from "../Layout/TLayoutEntities/TRoomEntity";
import { TWall } from "../Layout/TLayoutEntities/TWall";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";

export class TCadRoomStrucureLayer extends TDrawingLayer
{
    


    _target_poly : ZPolygon;

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadRoomStrucure,manager);
        this._target_poly = null;
        this._dirty = true;
    
    }


     _updateLayerContent(): void {
        if(!this._dirty) return;
        if(!this.ai_cad_data) {
            super._updateLayerContent();
            return;
        }
    }
    
    onDraw(): void {
        if(this._dirty) // 如果画布脏了(需要更新), 就先更新画布内容 
        {
            this._updateLayerContent();

        }

        // 单空间模式
        if(this.layout_container._drawing_layer_mode === "SingleRoom" && this.layout_container._selected_room) {

            let room = this.layout_container._selected_room;
            let room_entity = room._room_entity;
            if(room_entity)
            {
                room._painter = this.painter;
                room_entity.drawRoomWithWalls(this.painter,120,false);
                return;
            }

        }

        //  这里是设置canvas一些属性
        this.painter.strokeStyle = "#000";
        this.painter.fillStyle = "#777"; 

        let wall_rects = this.layout_container.getCandidateRects(["Wall"]);
        let window_rects = this.layout_container.getCandidateRects(["Window","Door"]);
        let room_polys = this.layout_container.getCandidateRects(["RoomArea"]);

        if(this.layout_container._outter_border_polygons && wall_rects.length > 1)
        {
            this.painter.strokeStyle = "#2b2b2b";
            this.painter.strokePolygons(this.layout_container._outter_border_polygons);
        }
        this.painter.strokeStyle= "#a42";
        this.painter.fillStyle = "#fff";

        
        if(wall_rects)
        {
            let target_rects : ZRect[] = [];
            for(let rect of wall_rects)
            {
                if(TBaseEntity.is_deleted(rect)) continue;
                
     
                if(rect.orientation_z_nor.z < 0)
                {
                    rect = rect.clone();
                    rect.invertOrder();
                }
                 target_rects.push(rect);     
            }
            for(let rect of window_rects)
            {
                let t_rect = rect.clone();
                if(t_rect.orientation_z_nor.z > 0)
                {
                    t_rect.invertOrder();
                }
                target_rects.push(t_rect);

            }
            // this.painter.fillPolygons(target_rects);  

            const type_order : IRoomEntityRealType[] =["InnerWall", "Wall","LoadBearingWall","OutterWall"]
            let wall_order = (rect:ZRect)=>{
                let entity = TBaseEntity.getEntityOfRect(rect) as TWall;
                if(entity)
                {
                    let id = type_order.indexOf(entity.realType);

                    let t_id = id * 1000 + (entity.uidN||0);
                    return t_id;
                }
                else{
                    return 0;
                }
   
            }

            wall_rects.sort((a,b)=>wall_order(a)-wall_order(b));
        }

        

        if(this.layout_container._outter_border_polygons && wall_rects.length > 1)
        {
            this.painter.strokeStyle = "#2b2b2b";
            this.painter.strokePolygons(this.layout_container._outter_border_polygons);
        }
        
        let structure_rects = this.layout_container.getCandidateRects(["StructureEntity"],{ignore_realtypes:["Lighting"]});

        let rects : ZRect[] =[...wall_rects,...window_rects,...structure_rects];
        for(let rect of rects)
        {

            if(TBaseEntity.is_deleted(rect)) continue;
            let entity = TBaseEntity.getEntityOfRect(rect);
            if(entity)
            {
                if(entity.is_selected)
                {
                    continue;
                }

                // 如果是临摹图，设置透明度为0.5
                let globalAlpha = this._manager.layer_CadCopyImageLayer.visible ? 0.5 : 1;
                entity.drawEntity(this.painter,{is_draw_figure:true, globalAlpha: globalAlpha});
                
            }
        }

        for(let poly of room_polys)
        {
            let entity = TRoomEntity.getOrMakeEntityOfCadRect(poly) as TRoomEntity;
            if(entity)
            {
                this.painter.strokeStyle = "#2b2b2b";
                this.painter.strokePolygons([entity._room_poly]);
            }
        }

    }
}