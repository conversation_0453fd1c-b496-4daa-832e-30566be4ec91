import { BaseViewHandler } from "./BaseViewHandler";
import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { RoomSpaceAreaType, IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { Vector3 } from "three";
import { TBaseRoomToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";

/**
 * 根据房间中某个图元生成视角
 * 位置：分区的边中点
 */
export class EdgeTargetViewHandler extends BaseViewHandler {
    handle(
        ruler: ViewCameraRuler,
        roomEntity: TRoomEntity,
        options: IViewCameraGenerateOptions
    ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            const targetFurnitureList = ruler.targets;
            const furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
            // 找客餐厅分区
            const areas: TSubSpaceAreaEntity[] = roomEntity._sub_room_areas;
            let LivingArea: TSubSpaceAreaEntity = null; // 客厅
            let DiningArea: TSubSpaceAreaEntity = null; // 餐厅
            areas.forEach(area => {
                if (area.space_area_type === RoomSpaceAreaType.LivingArea) {
                    LivingArea = area;
                } else if (area.space_area_type === RoomSpaceAreaType.DiningArea) {
                    DiningArea = area;
                }
            });
            if (ruler.condition?.spaceArea) {
                let targetArea: TSubSpaceAreaEntity = null;
                let nearArea: TSubSpaceAreaEntity = null;
                if (ruler.condition.spaceArea === IType2UITypeDict[RoomSpaceAreaType.LivingArea]) {
                    targetArea = LivingArea;
                    nearArea = DiningArea;
                } else {
                    targetArea = DiningArea;
                    nearArea = LivingArea;
                }
                if (!targetArea || !nearArea) return [];

                // 目标图元有效性
                let validTargetFurniture: TFurnitureEntity[] = [];
                targetFurnitureList.forEach(targetFurniture => {
                    let matchedFurniture = furnitureList.find(furniture => furniture.category.endsWith(targetFurniture as string));
                    if (matchedFurniture && this.isFurnitureInArea(matchedFurniture, targetArea)) {
                        validTargetFurniture.push(matchedFurniture);
                    }
                });

                // 一个目标图元：利用最小距离确定edge
                if (validTargetFurniture.length == 1) {
                    let targetEdge: ZEdge = null;
                    let minDistance = Infinity;
                    targetArea.rect.edges.forEach(edge => {
                        const distance = edge.center.distanceTo(nearArea.rect.rect_center);
                        if (distance < minDistance) {
                            minDistance = distance;
                            targetEdge = edge;
                        }
                    });
                    if (targetEdge) {
                        let furniture = validTargetFurniture[0];
                        let center = (furniture.matched_rect || furniture.rect).rect_center.clone();
                        const {pos, direction} = this.getViewByIntersection(center, targetEdge);
                        const target = [furniture.category];
                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }

                // 两个目标图元：找到与两图元连线垂直且不靠墙的最佳边作为视角位置
                if (validTargetFurniture.length == 2) {
                    const [furniture1, furniture2] = validTargetFurniture;
                    const center1 = (furniture1.matched_rect || furniture1.rect).rect_center;
                    const center2 = (furniture2.matched_rect || furniture2.rect).rect_center;
                    const midPoint = center1.clone().add(center2).multiplyScalar(0.5);

                    // 计算两图元连线方向
                    const lineDirection = center2.clone().sub(center1).normalize();

                    // 简化逻辑：直接过滤符合条件的边
                    const validEdges: ZEdge[] = [];
                    const debugInfo: Array<{
                        edge: ZEdge,
                        perpDot: number,
                        isWall: boolean,
                        wallChecks?: any,
                        isValid: boolean
                    }> = [];

                    for (let edge of targetArea.rect.edges) {
                        const edgeDirection = edge.dv.clone().normalize();
                        // 计算边与连线方向的点积：接近0表示垂直，接近1表示平行
                        const parallelDot = Math.abs(edgeDirection.dot(lineDirection));

                        // 多种方法检查边是否靠墙
                        const wallCheck1 = TBaseRoomToolUtil.instance.edgeOnPolygon(edge, roomEntity._room_poly) !== null;
                        const wallCheck2 = TBaseRoomToolUtil.instance.edgeOnPolygon(edge, roomEntity._room_poly, 10, 0.5) !== null;
                        const wallCheck3 = TBaseRoomToolUtil.instance.edgeOnPolygon(edge, roomEntity._room_poly, 20, 0.3) !== null;

                        // 尝试更严格的靠墙检测：检查边的中心点到房间边界的距离
                        let minDistanceToWall = Infinity;
                        for (let roomEdge of roomEntity._room_poly.edges) {
                            const projectedPoint = roomEdge.projectEdge2d(edge.center);
                            const distance = Math.abs(projectedPoint.y);
                            if (projectedPoint.x >= 0 && projectedPoint.x <= roomEdge.length) {
                                minDistanceToWall = Math.min(minDistanceToWall, distance);
                            }
                        }
                        const isNearWall = minDistanceToWall < 50; // 距离墙体50mm以内认为靠墙

                        // 过滤条件：
                        // 1. 必须与连线方向垂直（点积 < 0.3，约等于17度以内）
                        // 2. 必须不靠墙
                        const isPerpendicular = parallelDot < 0.3; // 垂直判断阈值
                        const isAgainstWall = wallCheck1 || wallCheck2 || wallCheck3 || isNearWall;
                        const isNotAgainstWall = !isAgainstWall;
                        const isValid = isPerpendicular && isNotAgainstWall;

                        debugInfo.push({
                            edge,
                            perpDot: parallelDot,
                            isWall: isAgainstWall,
                            wallChecks: {
                                method1: wallCheck1,
                                method2: wallCheck2,
                                method3: wallCheck3,
                                nearWall: isNearWall,
                                minDist: minDistanceToWall
                            },
                            isValid
                        });

                        if (isValid) {
                            validEdges.push(edge);
                        }
                    }

                    // 调试信息
                    console.log('Edge filtering for two furniture:', {
                        lineDirection: lineDirection,
                        totalEdges: targetArea.rect.edges.length,
                        validEdges: validEdges.length,
                        debugInfo: debugInfo.map(item => ({
                            perpDot: item.perpDot.toFixed(3),
                            isWall: item.isWall,
                            wallChecks: item.wallChecks,
                            isValid: item.isValid
                        }))
                    });

                    // 选择策略：优先选择不靠墙的垂直边，否则选择最垂直的边
                    let bestEdge: ZEdge = null;
                    if (validEdges.length > 0) {
                        bestEdge = validEdges[0]; // 取第一个符合条件的边
                        console.log('Found ideal edge (perpendicular + not against wall)');
                    } else {
                        // 降级策略1：只要求垂直，允许靠墙
                        console.log('No ideal edge found, trying perpendicular edges (allow wall)');
                        let perpendicularEdges: {edge: ZEdge, perpDot: number}[] = [];
                        for (let edge of targetArea.rect.edges) {
                            const edgeDirection = edge.dv.clone().normalize();
                            const parallelDot = Math.abs(edgeDirection.dot(lineDirection));
                            if (parallelDot < 0.5) { // 放宽垂直要求
                                perpendicularEdges.push({edge, perpDot: parallelDot});
                            }
                        }

                        if (perpendicularEdges.length > 0) {
                            // 选择最垂直的边
                            perpendicularEdges.sort((a, b) => a.perpDot - b.perpDot);
                            bestEdge = perpendicularEdges[0].edge;
                            console.log('Selected most perpendicular edge with dot product:', perpendicularEdges[0].perpDot.toFixed(3));
                        } else {
                            // 降级策略2：如果没有垂直的边，选择距离两图元中点最近的边
                            console.log('No perpendicular edges found, selecting edge closest to midpoint');
                            let minDistance = Infinity;
                            for (let edge of targetArea.rect.edges) {
                                const distance = edge.center.distanceTo(midPoint);
                                if (distance < minDistance) {
                                    minDistance = distance;
                                    bestEdge = edge;
                                }
                            }
                        }
                    }
                    if (bestEdge) {
                        // 利用投影获取交点
                        const {pos, direction} = this.getViewByIntersection(midPoint, bestEdge);
                        const target = validTargetFurniture.map(furniture => furniture.category);
                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }
            }
        }
        return entities;
    }

    private createEdgeTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        pos: Vector3,
        direction: TFurnitureEntity | Vector3,
        target: string[],
        roomEntity: TRoomEntity
    ): TViewCameraEntity {
        let rect = new ZRect(500, 500);
        // 方向
        if (direction instanceof TFurnitureEntity) {
            let ToCenter = (direction.matched_rect || direction.rect).rect_center.clone().sub(roomEntity._main_rect.rect_center);
            let ToFurniture = (direction.matched_rect || direction.rect).rect_center.clone().sub(pos);
            if (ToCenter.dot(ToFurniture) >= 0) {
                rect.nor = ToCenter.normalize();
            }
        } else {
            rect.nor = direction.clone();
        }
        // 位置
        rect.rect_center_3d = this.getViewCameraPosByPoint(ruler, pos, rect.nor);
        let name = ruler.name + "-侧方";
        let viewEntity = this.createViewCameraEntity(
            ruler,
            options,
            rect,
            target,
            roomEntity,
            name
        );
        return viewEntity;
    }

    private isFurnitureInArea(entity: TFurnitureEntity, area: TSubSpaceAreaEntity): boolean {
        let rect = entity.rect;
        if (rect.intersect(area.rect).length > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据投影点获取位置和方向
     */
    private getViewByIntersection(point: Vector3, edge: ZEdge): {pos: Vector3, direction: Vector3} {
        const projectedPoint = edge.projectEdge2d(point);
        const clampedX = Math.max(0, Math.min(edge.length, projectedPoint.x));
        const pos = edge.unprojectEdge2d({ x: clampedX, y: 0 });
        const direction = point.clone().sub(pos).normalize();
        return {pos, direction};
}
}
