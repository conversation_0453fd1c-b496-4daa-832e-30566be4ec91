import { BaseViewHandler } from "./BaseViewHandler";
import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { RoomSpaceAreaType, IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { Vector3 } from "three";
import { TBaseRoomToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";

/**
 * 根据房间中某个图元生成视角
 * 位置：分区的边中点
 */
export class EdgeTargetViewHandler extends BaseViewHandler {
    handle(
        ruler: ViewCameraRuler,
        roomEntity: TRoomEntity,
        options: IViewCameraGenerateOptions
    ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            const targetFurnitureList = ruler.targets;
            const furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
            // 找客餐厅分区
            const areas: TSubSpaceAreaEntity[] = roomEntity._sub_room_areas;
            let LivingArea: TSubSpaceAreaEntity = null; // 客厅
            let DiningArea: TSubSpaceAreaEntity = null; // 餐厅
            areas.forEach(area => {
                if (area.space_area_type === RoomSpaceAreaType.LivingArea) {
                    LivingArea = area;
                } else if (area.space_area_type === RoomSpaceAreaType.DiningArea) {
                    DiningArea = area;
                }
            });
            if (ruler.condition?.spaceArea) {
                let targetArea: TSubSpaceAreaEntity = null;
                let nearArea: TSubSpaceAreaEntity = null;
                if (ruler.condition.spaceArea === IType2UITypeDict[RoomSpaceAreaType.LivingArea]) {
                    targetArea = LivingArea;
                    nearArea = DiningArea;
                } else {
                    targetArea = DiningArea;
                    nearArea = LivingArea;
                }
                if (!targetArea || !nearArea) return [];

                // 目标图元有效性
                let validTargetFurniture: TFurnitureEntity[] = [];
                targetFurnitureList.forEach(targetFurniture => {
                    let matchedFurniture = furnitureList.find(furniture => furniture.category.endsWith(targetFurniture as string));
                    if (matchedFurniture && this.isFurnitureInArea(matchedFurniture, targetArea)) {
                        validTargetFurniture.push(matchedFurniture);
                    }
                });

                // 一个目标图元：利用最小距离确定edge
                if (validTargetFurniture.length == 1) {
                    let targetEdge: ZEdge = null;
                    let minDistance = Infinity;
                    targetArea.rect.edges.forEach(edge => {
                        const distance = edge.center.distanceTo(nearArea.rect.rect_center);
                        if (distance < minDistance) {
                            minDistance = distance;
                            targetEdge = edge;
                        }
                    });
                    if (targetEdge) {
                        let furniture = validTargetFurniture[0];
                        let center = (furniture.matched_rect || furniture.rect).rect_center.clone();
                        const {pos, direction} = this.getViewByIntersection(center, targetEdge);
                        const target = [furniture.category];
                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }

                // 两个目标图元：找到与两图元连线垂直且不靠墙的最佳边作为视角位置
                if (validTargetFurniture.length == 2) {
                    const [furniture1, furniture2] = validTargetFurniture;
                    const center1 = (furniture1.matched_rect || furniture1.rect).rect_center;
                    const center2 = (furniture2.matched_rect || furniture2.rect).rect_center;
                    const midPoint = center1.clone().add(center2).multiplyScalar(0.5);

                    // 计算两图元连线方向
                    const lineDirection = center2.clone().sub(center1).normalize();

                    // 找到最佳边：与连线方向垂直且不靠墙
                    let bestEdge: ZEdge = null;
                    let bestScore = -1;
                    let edgeScores: Array<{edge: ZEdge, score: number, perpScore: number, isWall: boolean}> = [];

                    for (let edge of targetArea.rect.edges) {
                        const edgeDirection = edge.dv.clone().normalize();
                        // 计算边与连线方向的垂直程度：接近0表示垂直，接近1表示平行
                        const parallelDot = Math.abs(edgeDirection.dot(lineDirection));
                        const perpendicularScore = 1.0 - parallelDot; // 垂直度评分：越垂直分数越高

                        // 检查边是否靠墙
                        const isAgainstWall = TBaseRoomToolUtil.instance.edgeOnPolygon(edge, roomEntity._room_poly) !== null;

                        // 评分策略：
                        // 1. 优先选择垂直度高的边（perpendicularScore > 0.7）
                        // 2. 在垂直度相近的情况下，优先选择不靠墙的边
                        let score = perpendicularScore;
                        if (perpendicularScore > 0.7) { // 足够垂直的边
                            score += isAgainstWall ? 0 : 0.5; // 不靠墙的边额外加分
                        } else {
                            score -= 0.3; // 不够垂直的边减分
                        }

                        edgeScores.push({edge, score, perpScore: perpendicularScore, isWall: isAgainstWall});

                        if (score > bestScore) {
                            bestScore = score;
                            bestEdge = edge;
                        }
                    }

                    // 调试信息：输出边的评分情况
                    console.log('Edge selection for two furniture:', {
                        lineDirection: lineDirection,
                        edgeScores: edgeScores.map(item => ({
                            perpScore: item.perpScore.toFixed(3),
                            isWall: item.isWall,
                            finalScore: item.score.toFixed(3)
                        })),
                        bestScore: bestScore.toFixed(3)
                    });
                    if (bestEdge) {
                        // 利用投影获取交点
                        const {pos, direction} = this.getViewByIntersection(midPoint, bestEdge);
                        const target = validTargetFurniture.map(furniture => furniture.category);
                        const viewEntity = this.createEdgeTargetView(ruler, options, pos, direction, target, roomEntity);
                        entities.push(viewEntity);
                    }
                }
            }
        }
        return entities;
    }

    private createEdgeTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        pos: Vector3,
        direction: TFurnitureEntity | Vector3,
        target: string[],
        roomEntity: TRoomEntity
    ): TViewCameraEntity {
        let rect = new ZRect(500, 500);
        // 方向
        if (direction instanceof TFurnitureEntity) {
            let ToCenter = (direction.matched_rect || direction.rect).rect_center.clone().sub(roomEntity._main_rect.rect_center);
            let ToFurniture = (direction.matched_rect || direction.rect).rect_center.clone().sub(pos);
            if (ToCenter.dot(ToFurniture) >= 0) {
                rect.nor = ToCenter.normalize();
            }
        } else {
            rect.nor = direction.clone();
        }
        // 位置
        rect.rect_center_3d = this.getViewCameraPosByPoint(ruler, pos, rect.nor);
        let name = ruler.name + "-侧方";
        let viewEntity = this.createViewCameraEntity(
            ruler,
            options,
            rect,
            target,
            roomEntity,
            name
        );
        return viewEntity;
    }

    private isFurnitureInArea(entity: TFurnitureEntity, area: TSubSpaceAreaEntity): boolean {
        let rect = entity.rect;
        if (rect.intersect(area.rect).length > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据投影点获取位置和方向
     */
    private getViewByIntersection(point: Vector3, edge: ZEdge): {pos: Vector3, direction: Vector3} {
        const projectedPoint = edge.projectEdge2d(point);
        const clampedX = Math.max(0, Math.min(edge.length, projectedPoint.x));
        const pos = edge.unprojectEdge2d({ x: clampedX, y: 0 });
        const direction = point.clone().sub(pos).normalize();
        return {pos, direction};
}
}
