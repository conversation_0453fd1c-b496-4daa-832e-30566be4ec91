import { ViewCameraRuler, ViewCameraRulerType, ViewCameraType } from "../ViewCameraRuler"
import { CategoryName } from "@/Apps/LayoutAI/Scene3D/NodeName"

// 全景视角规则配置
export const PanoramaViewCameraConfigs: ViewCameraRuler[] = [
    {
        name: '客厅',
        typeId: ViewCameraRulerType.SofaView,
        target: CategoryName.Sofa,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'sofa_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '客厅',
        typeId: ViewCameraRulerType.LivingSideView,
        targets: [CategoryName.Sofa, CategoryName.TVCabinet],
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'livingroom_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '餐厅',
        typeId: ViewCameraRulerType.DiningSideView,
        targets: [CategoryName.Table],
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'dining_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            spaceArea: '餐厅区',
        },
    },
    {
        name: '餐厅',
        typeId: ViewCameraRulerType.DiningCabinetView,
        target: CategoryName.DiningCabinet,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'dining_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            spaceArea: '餐厅区',
        },
    },
    {
        name: '卧室',
        typeId: ViewCameraRulerType.BedView,
        target: CategoryName.Bed,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'bed_view_camera',
            fov: 60,
        },
        pose: {
            posObj: CategoryName.Bed,
            z: 1200,
            norOffset: 200,
        },
        condition: {
            roomName: '卧室|主卧|次卧|客卧',
        },
    },
    {
        name: '厨房',
        typeId: ViewCameraRulerType.KitchenPanoView,
        target: `${CategoryName.Stove}|${CategoryName.Sink}`,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'kitchen_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            roomName: '厨房',
        },
    },
    {
        name: '卫生间',
        typeId: ViewCameraRulerType.BathroomPanoView,
        target: `${CategoryName.BathCabinet}|${CategoryName.Toilet}|${CategoryName.Shower}`,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'bathroom_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            roomName: '卫生间',
        },
    },
    {
        name: '书房',
        typeId: ViewCameraRulerType.StudyroomPanoView,
        target: `${CategoryName.Desk}|${CategoryName.Bookshelf}`,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'studyroom_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            roomName: '书房',
        },
    },
    {
        name: '茶室',
        typeId: ViewCameraRulerType.TearoomPanoView,
        target: `${CategoryName.TeaTable}|${CategoryName.Bookshelf}`,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'tearoom_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            roomName: '茶室',
        },
    },
]