import { T_AI2DesignOperationInfo } from "./T_AI2DesignOperationInfo";
import { OperationManager } from "../OperationManager";
import { ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TBaseGroupEntity } from "../../Layout/TLayoutEntities/TBaseGroupEntity";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TRoomEntity } from "../../Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "../../Layout/TLayoutEntities/TSubSpaceAreaEntity";


export class T_DeleteFurnitureOperationInfo extends T_AI2DesignOperationInfo
{
    _target_rects : ZRect[];
    _targetFurnitureEntities : TFurnitureEntity[];
    selected_group_entity : TBaseGroupEntity;

    private _entityRecords : {[key:string]:{furniture?:TFurnitureEntity, roomEntity?:TRoomEntity, areaEntity?:TSubSpaceAreaEntity}} = {};
    constructor(manager : TAppManagerBase)
    {
        super(manager);
        this._entityRecords = {};
    }

    set target_rect(rect:ZRect)
    {
        if(rect)
        {
            this._target_rects = [rect];
        }
        else{
            this._target_rects = [];
        }
    }

    redo(operation_manager?: OperationManager): boolean {

        if(this._targetFurnitureEntities && this._targetFurnitureEntities.length > 0)
        {
            let entities = this.manager.layout_container._furniture_entities;

            this._targetFurnitureEntities.forEach((entity)=>{

                if(!this._entityRecords[entity._uuid])
                {
                    this._entityRecords[entity._uuid] = {
                        furniture : entity,roomEntity:entity.roomEntity,areaEntity:entity.area_entity
                    }
                }

                if(this.selected_group_entity && this.selected_group_entity !== entity)
                {
                    for(let sub_entity of this.selected_group_entity._combination_entitys)
                    {  
                        if(entity == sub_entity)
                        {
                            this.selected_group_entity._combination_entitys.splice(this.selected_group_entity._combination_entitys.indexOf(entity),1);
                            (LayoutAI_App.instance as TAppManagerBase).layout_container.entity_combiner.combination_target._all_sub_entitys = this.selected_group_entity._combination_entitys;
                        }
                    }
                }
                else{
                    let id = entities.indexOf(entity);
                    if(id >=0) { 
                        entities.splice(id,1);
                        if(entity.roomEntity)
                        {
                            let roomEntity = entity.roomEntity;
                            entity.roomEntity = null;
                            roomEntity.updateFurnitureListFromEntities(roomEntity.furniture_entities);

                        }
                        entity.roomEntity = null;
                        entity.area_entity = null;
                    }
                    console.log(entity,id);
                }
            })
        }

        // // 删除后更新TRoom里面的家具
        // let room_entities = this.manager.layout_container._room_entities;
        // for(let room_entity of room_entities)
        // {
        //     room_entity.updateFurnitureListFromEntities(this.manager.layout_container._furniture_entities);
        // }
        return true;
    }

    undo(operation_manager?: OperationManager): boolean {

        
        if(this._targetFurnitureEntities && this._targetFurnitureEntities.length > 0)
        {
            let entities = this.manager.layout_container._furniture_entities;

            this._targetFurnitureEntities.forEach((entity)=>{

                if(this._entityRecords[entity._uuid])
                {
                    let res = this._entityRecords[entity._uuid];
                    if(res.roomEntity && this.manager.layout_container._room_entities.includes(res.roomEntity))
                    {
                        entity.roomEntity = res.roomEntity || null;
                        if(entity.roomEntity)
                        {
                            entity.roomEntity.updateFurnitureListFromEntities(entity.roomEntity.furniture_entities);
                        }
                    }
                    if(res.areaEntity && this.manager.layout_container._sub_area_entities.includes(res.areaEntity))
                    {
                        entity.area_entity = res.areaEntity || null;
                    }
                }

                if(this.selected_group_entity && this.selected_group_entity !== entity)
                {
                    let id = this.selected_group_entity._combination_entitys.indexOf(entity);
                    if(id < 0)
                    {
                        this.selected_group_entity._combination_entitys.push(entity);
                        this.selected_group_entity.updateSize();
                    }
                } else 
                {
                    let id = entities.indexOf(entity);
                    if(id <0){
                        entities.push(entity);
                    }
                }
            })
        }

        // 删除后更新TRoom里面的家具
        // let room_entities = this.manager.layout_container._room_entities;
        // for(let room_entity of room_entities)
        // {
        //     room_entity.updateFurnitureListFromEntities(this.manager.layout_container._furniture_entities);
        // }
        return true;
    }

    push(operation_manager?: OperationManager): void {
        this.manager.appendOperationInfo(this);
    }
    pop(operation_manager?: OperationManager): void {
        this.target_rect = null;   
        this._entityRecords = null;
        this._targetFurnitureEntities = null;
        this.selected_group_entity = null;
    }
    information(): string {
        return "DeleteFurniture";
    }
}