import { Box3, Vector3, <PERSON>ector<PERSON><PERSON><PERSON> } from "three";
import { I_PoygonDrawingState, PolygonDrawingHelper } from "./PolygonDrawingHelper";
import { ZPolyline } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { <PERSON><PERSON>ain<PERSON> } from "@layoutai/z_polygon";

export type I_RectDrawingType = "2Points"|"3Points"|"FreePoly"
export class RectDrawing<PERSON>elper extends PolygonDrawingHelper
{

    protected _method :I_RectDrawingType;

    protected _rect : ZRect;
    constructor()
    {
        super();
        this._method = "2Points";
    }
    isDrawPolyClosed(): boolean {
        if(this._method == "2Points")
        {
            if(this._drawing_polyline.vertices.length == 2)
            {
                return true;
            }
        }
        else if(this._method ==="3Points")
        {
            if(this._drawing_polyline.vertices.length == 3)
            {
                return true;
            }
        }
        return super.isDrawPolyClosed();
    }
 

    protected _prepareAddingVertex(p:Vector3)
    {
        if(!this._drawing_polyline)
        {
            this._drawing_polyline = new ZPolyline();
        }
        this._drawing_polyline.vertices.push({pos:p});
        this._updatePolyline();

    }
    protected _addVertex(p: Vector3): I_PoygonDrawingState{
        return super._addVertex(p);
    }


    updateResultPoly()
    {
        this._updatePolyline();
        return this._rect?.clone() || null;
    }
    
    protected _updatePolyline(): void {
        if(this._method == "2Points")
        {
            if(this._drawing_polyline)
            {
                let box3 = new Box3();
                box3.setFromPoints([...this._drawing_polyline.positions,this._current_pos].filter(t=>t));
                if(!this._rect)
                {
                    this._rect = new ZRect(1,1);
                }
                this._rect.copy(ZRect.fromBox3(box3));
            }
            else{
                this._rect = null;
            }

        }
        else if(this._method === "3Points")
        {
            if(this._drawing_polyline)
            {
                if(!this._rect)
                {
                    this._rect = new ZRect(1,1);
                }
                this._rect.copy(ZRect.fromPoints(this._drawing_polyline.positions, this._drawing_polyline.edges[0].nor.clone().negate()));
            }
            else{
                this._rect = null;
            }
        }
        else{
            super._updatePolyline();
            if(this._drawing_polyline)
            {
                if(!this._rect)
                {
                    this._rect = new ZRect(1,1);
                }
            }
            else{

            }
        }
    }

    _drawCanvas_DrawingPoly(painter: ZPainter): void {

        let lw = painter._context.lineWidth;

        painter.strokeStyle = this.poly_stroke_style;
        painter._context.lineWidth = 2;
        if(this._rect)
        {
            painter.strokePolygons([this._rect]);
        }

        painter._context.lineWidth = lw;

    }
}