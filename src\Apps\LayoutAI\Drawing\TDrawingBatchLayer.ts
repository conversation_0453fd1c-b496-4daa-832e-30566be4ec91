import { TAppManagerBase } from "../../AppManagerBase";
import { TDrawingLayer } from "./TDrawingLayer";

/**
 *  图层合批绘制
 */
export class TDrawingBatchLayer extends TDrawingLayer
{
    _drawing_layers : TDrawingLayer[];
    constructor(layer_type:string = "BatchLayer", layers:TDrawingLayer[]=[],manager : TAppManagerBase = null)
    {
        super(layer_type,manager);
        this._drawing_layers = layers;
    }

    get drawing_layers()
    {
        return this._drawing_layers;
    }
    onDraw(): void {
        for(let layer of this.drawing_layers)
        {
            if(!layer.visible) continue;        
            layer.onDraw();
        }        
    }
    


}