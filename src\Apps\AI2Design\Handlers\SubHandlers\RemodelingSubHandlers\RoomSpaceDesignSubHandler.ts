import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { T_AddOrDeleteEntityOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_AddOrDeleteEntityOperationInfo";
import { I_SwjFlueData, I_SwjPillarData, I_SwjPipeData, I_SwjPlatformData, I_SwjRemodelingRequestData, I_SwjWindow } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { TRoomSpaceMarkEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomSpaceMarkEntity";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Vector3 } from "three";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { RmodelingUtils } from "./RmodelingUtils";
import { TRoomExporter } from "@/Apps/LayoutAI/Layout/TLayoutEntities/loader/TRoomExporter";
// 房间空间设计子模式
export class RoomSpaceDesignSubHandler extends CadBaseSubHandler {
    private _selected_mark: TRoomSpaceMarkEntity | null = null;
    private _is_dragging: boolean = false;
    private _last_mouse_pos: Vector3 | null = null;
    private _hovered_mark: TRoomSpaceMarkEntity | null = null;
    private _user_id: string = "";
    private _outer_polygons: ZPolygon[];
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = "RoomSpaceDesignSubHandler";
        this._outer_polygons = [];
    }


    async enter(state?: number): Promise<void> {
        super.enter(state);
        this.manager.layer_RemodelingLayer.visible = true;
        this.manager.layer_CadRoomNameLayer.visible = false;
        this.manager.layer_CadFurnitureLayer.visible = false;
        this.manager.layer_AILayoutLayer.visible = false;
        this.manager.layer_AIMatchingLayer.visible = false;
        if (this.manager.layout_container.remodeiling_data) {
            for (let room_info of this.manager.layout_container.remodeiling_data.roomInfos) {
                this.addRoomSpaceMark(room_info.name, room_info.pos);
            }
        }
        this._outer_polygons = RmodelingUtils.getRoomOutlinePolygons(this.manager.layout_container);
        this.manager.resetUndoRedos();

    }

    leave(): void {
        // 生成拆改请求数据
        this._setRemodelingRequestData();
        super.leave();
        this.manager.layout_container.room_space_marks = [];
        this.manager.resetUndoRedos();
    }


    private async _setRemodelingRequestData(): Promise<void> {
        let roomInfos: { name: string, pos: Vector3 }[] = [];
        let marks = this.manager.layout_container.room_space_marks;
        for (let roomMark of marks) {
            let room_info = {
                name: roomMark.name,
                pos: roomMark.position
            }
            roomInfos.push(room_info);
        }

        if (this.manager.layout_container.remodeiling_data) {
            // 如果已经存在拆改请求数据，则只更新房间信息
            this.manager.layout_container.remodeiling_data.roomInfos = roomInfos;
            return;
        }
        
        let wall_entities = this.manager.layout_container._wall_entities;
        let outerWalls = [];
        let innerWalls = [];
        let outWindows: I_SwjWindow[] = [];
        let ruhuDoors: I_SwjWindow[] = [];
        for (let wall of wall_entities) {
            if (RmodelingUtils.checkWallIsOuter(wall, this._outer_polygons)) {
                outerWalls.push(wall.exportData());
                // 外墙上的门 为入户门 窗户为外窗
                for(let win_door_rect of wall._win_rects){
                    if(win_door_rect.ex_prop['poly_target_type'] === "Window"){
                        let window = win_door_rect._attached_elements["window"];
                        if(window){
                            let window_data = TRoomExporter.exportEntity3Ddata(window,true);
                            outWindows.push(window_data);
                        }
                    }else if(win_door_rect.ex_prop['poly_target_type'] === "Door"){
                        let door = win_door_rect._attached_elements["window"];
                        if(door){
                            let door_data = TRoomExporter.exportEntity3Ddata(door,true);
                            ruhuDoors.push(door_data);
                        }
                    }
                }
            } else {
                innerWalls.push(wall.exportData());
            }
        }

        let pillarList: I_SwjPillarData[] = [];
        let flueList: I_SwjFlueData[] = [];
        let pipeList: I_SwjPipeData[] = [];
        let platformList: I_SwjPlatformData[] = [];
        for (let room of this.manager.layout_container._rooms) {
            let room_data = room.exportSwjRoomData();
            for (let pillar of room_data.pillar_list) {
                if (!pillarList.find(p => (p as any)?.id === (pillar as any)?.id)) {
                    pillarList.push(pillar as I_SwjPillarData);
                }
            }
            for (let flue of room_data.flue_list) {
                if (!flueList.find(f => (f as any)?.id === (flue as any)?.id)) {
                    flueList.push(flue as I_SwjFlueData);
                }
            }
            for (let pipe of room_data.pipe_list) {
                if (!pipeList.find(p => (p as any)?.id === (pipe as any)?.id)) {
                    pipeList.push(pipe as I_SwjPipeData );
                }
            }
            for (let platform of room_data.platform_list) {
                if (!platformList.find(p => (p as any)?.id === (platform as any)?.id)) {
                    platformList.push(platform as I_SwjPlatformData);
                }
            }
        }

        const traceId = `${this.manager.layout_container._scheme_id}_${Date.now()}`;
        const userId = this._user_id;

        this._user_id = "";
        const data: I_SwjRemodelingRequestData = {
            traceId: traceId,
            userId: userId,
            outerWalls: outerWalls,
            innerWalls: innerWalls,
            outWindows: outWindows,
            ruhuDoors: ruhuDoors,
            roomInfos: roomInfos,
            pillarList: pillarList,
            flueList: flueList,
            pipeList: pipeList,
            platformList: platformList
        };

        this.manager.layout_container.remodeiling_data = data;
    }


    onmousedown(ev: I_MouseEvent): void {
        if (ev.button !== 0) return;

        const worldPos = new Vector3(ev.posX, ev.posY, 0);
        let marks = this.manager.layout_container.room_space_marks;
        this._selected_mark = marks.find(mark =>
            mark.containsPoint(worldPos)
        ) || null;

        if (this._selected_mark) {
            this._is_dragging = true;
            this._last_mouse_pos = worldPos;
            this._cad_mode_handler._is_moving_element = true;
        }
    }

    onmousemove(ev: I_MouseEvent): void {
        // ev.buttons == 0时，表示没有按下任何鼠标键
        // ev.buttons == 1时，表示按下了鼠标左键
        // ev.buttons == 2时，表示按下了鼠标右键
        if (ev.buttons != 1) {
            return;
        }
        const worldPos = new Vector3(ev.posX, ev.posY, 0);

        if (this._is_dragging && this._selected_mark && this._last_mouse_pos) {
            this._cad_mode_handler._is_moving_element = true;
            const offset = worldPos.clone().sub(this._last_mouse_pos);
            this._selected_mark.movePosition(offset);
            this._last_mouse_pos = worldPos;
            this.update();
            return;
        }
        let marks = this.manager.layout_container.room_space_marks;
        const hoveredMark = marks.find(mark =>
            mark.containsPoint(worldPos)
        );

        if (this._hovered_mark !== hoveredMark) {
            if (this._hovered_mark) {
                this._hovered_mark.setHovered(false);
            }
            if (hoveredMark) {
                hoveredMark.setHovered(true);
            }
            this._hovered_mark = hoveredMark;
            this.update();
        }
    }

    onmouseup(ev: I_MouseEvent): void {
        if (ev.button === 2) { // 右键
            const worldPos = new Vector3(ev.posX, ev.posY, 0);
            let marks = this.manager.layout_container.room_space_marks;
            const markToDelete = marks.find(mark =>
                mark.containsPoint(worldPos)
            );

            if (markToDelete) {
                const index = marks.indexOf(markToDelete);
                if (index > -1) {
                    this.deleteRoomSpaceMark(markToDelete);
                    this.update();
                }
            }
        } else
            // 处理拖拽结束
            if (this._is_dragging) {
                this._is_dragging = false;
                this._last_mouse_pos = null;
                this._cad_mode_handler._is_moving_element = false;
            }


    }

    onwheel(ev: WheelEvent): void {
        this.update();
    }

    drawCanvas(): void {
        let painter = this.manager.layer_RemodelingLayer.painter;
        painter.clean();
        // 绘制所有房间标记
        let marks = this.manager.layout_container.room_space_marks;
        marks.forEach(mark => {
            mark.drawEntity(painter, {
                is_selected: mark === this._selected_mark
            });
        });
    }

    handleEvent(evt_name: string, evt_param: any): void {
        if (evt_name === LayoutAI_Events.AddRoomSpaceMark) {
            if (evt_param) {
                let pos = this._transformToCanvasPos(evt_param.pageX, evt_param.pageY);
                this.addRoomSpaceMark(evt_param.name, pos);
                this.update();
            }
        }
    }

    private addRoomSpaceMark(name: string, pos: Vector3) {
        let mark = new TRoomSpaceMarkEntity(name, pos);
        let operation = new T_AddOrDeleteEntityOperationInfo("Add", mark, this.manager);
        operation._entity = mark;
        operation.redo();
        this.manager.appendOperationInfo(operation);
    }

    private deleteRoomSpaceMark(mark: TRoomSpaceMarkEntity) {
        let operation = new T_AddOrDeleteEntityOperationInfo("Delete", mark, this.manager);
        operation._entity = mark;
        operation.redo();
        this.manager.appendOperationInfo(operation);
    }

    private _transformToCanvasPos = (pageX: number, pageY: number) => {
        let c_offset = this.manager._container_offset;
        let ox = pageX - c_offset.x;
        let oy = pageY - c_offset.y;
        if (LayoutAI_App.instance._is_landscape) {
            ox = pageY - c_offset.x;
            oy = window.innerWidth - pageX - c_offset.y;
        }
        let pos = this.manager.painter.canvas2world({ x: ox, y: oy });
        return new Vector3(pos.x, pos.y, 0);
    }
} 
