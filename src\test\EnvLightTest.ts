import { <PERSON>, G<PERSON> } from "lil-gui";
import { Object3D, AmbientLight, DirectionalLight, Color } from "three";
import { RoomSpaceAreaType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { AutoLightingService } from "@/Apps/LayoutAI/Services/AutoLighting/AutoLightService";
import { TRoomType } from "@/Apps/LayoutAI/Layout/RoomType";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LightManager } from "@/Apps/LayoutAI/Scene3D/LightManager";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { LightMode } from "@/pages/SdkFrame/MsgCenter/IMsgType";

// 环境光配置规则
export interface EnvLightRuler {
    // 环境光
    ambient: AmbientLight
    // 阳光
    sunlight: DirectionalLight
    // 辅光
    subSunLight: DirectionalLight
}

export class EnvLightTest {
    private _gui: GUI;
    private _envLightFolder: GUI;
    
    private _sunHelper: Object3D;
    private _subSunHelper: Object3D;

    private _lightMode: LightMode;
    private _envLightConfigs: EnvLightRuler;

    constructor(gui: GUI) {
        this._gui = gui;

        // 要与RoomBasicTest中默认值一致
        this._envLightConfigs = {
            ambient: LightManager.getDayAmbientLight(),  // 默认夜间灯光前端不显示
            sunlight: LightManager.sunlight,
            subSunLight: LightManager.subSunLight
        };

        this._sunHelper = LightManager.addLightHelper(LightManager.sunlight, LayoutAI_App.instance.scene3D.dayLightsGroup, 400, 0xff0000);
        this._sunHelper.visible = false;
        this._subSunHelper = LightManager.addLightHelper(LightManager.subSunLight, LayoutAI_App.instance.scene3D.dayLightsGroup, 200, 0xee0000);
        this._subSunHelper.visible = false;
    }

    public get config() {
        return {
            envLightConfigs: this._envLightConfigs,
        }
    }

    public get lightMode() {
        return this._lightMode;        
    }

    public set lightMode(value: LightMode) {
        this._lightMode = value;
    }

    public setEnvLightConfigs(config: Record<keyof EnvLightRuler, { intensity: number, color: number }>){
        let ambient = LightManager.getNightAmbientLight();
        if (this._lightMode === LightMode.Day) {
            ambient =LightManager.getDayAmbientLight();
        } else  if (this._lightMode === LightMode.Night) {
            ambient = LightManager.getNightAmbientLight();
        }
        this._envLightConfigs.ambient = ambient;
        Object.entries(config).forEach(([lightType, lightData]) => {
            const lightKey = lightType as keyof typeof config;
            if (this._envLightConfigs[lightKey]) {
                this._envLightConfigs[lightKey].intensity = lightData.intensity;
                this._envLightConfigs[lightKey].color.setHex(lightData.color);
            }
        });
    }

    public addEnvLightCtr(folder: GUI) {
        this._envLightFolder = folder
        let envLightFolder = this._envLightFolder;

        envLightFolder.add(this._envLightConfigs.ambient, 'intensity', 0, 10, 0.01)
            .name('环境光强度')
            .onChange((value: number) => {
                this._envLightConfigs.ambient.intensity = value;
            });

        envLightFolder.addColor(this._envLightConfigs.ambient, 'color')
            .name('颜色')
            .onChange((value: Color) => {
                this._envLightConfigs.ambient.color = value;
            });

        envLightFolder.add(this._envLightConfigs.sunlight, 'intensity', 0, 10, 0.01)
            .name('阳光强度')
            .onChange((value: number) => {
                this._envLightConfigs.sunlight.intensity = value;
            });

        envLightFolder.addColor(this._envLightConfigs.sunlight, 'color')
            .name('颜色')
            .onChange((value: Color) => {
                this._envLightConfigs.sunlight.color = value;
            });

        envLightFolder.add({ showHelper: this._sunHelper.visible }, 'showHelper')
            .name('外显')
            .onChange((value: boolean) => {
                this._sunHelper.visible = value;
            });

        let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        layout_container._room_entities.forEach((roomEntity) => {
            if (roomEntity.room_type === TRoomType.livingRoom) {
                envLightFolder.add({
                    autoRoomSunLighting: () => {
                        AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.LivingArea);
                        // (sunHelper as DirectionalLightHelper).update();
                    }
                }, 'autoRoomSunLighting').name(`照客厅`);
                envLightFolder.add({
                    autoRoomSunLighting: () => {
                        AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.DiningArea);
                        // (sunHelper as DirectionalLightHelper).update();
                    }
                }, 'autoRoomSunLighting').name(`照餐厅`);
            } else {
                envLightFolder.add({
                    autoRoomSunLighting: () => {
                        AutoLightingService.instance.autoRoomSunLighting(roomEntity);
                        // (sunHelper as DirectionalLightHelper).update();
                    }
                }, 'autoRoomSunLighting').name(`照${roomEntity.name}`);
            }
        });

        let sunRotationControllerX: Controller;
        let sunRotationControllerY: Controller;
        let sunRotationControllerZ: Controller;
        let syncSunRotation = function () {
            if (sunRotationControllerX) {
                sunRotationControllerX.setValue(LightManager.sunlight.rotation.x);
            }
            if (sunRotationControllerY) {
                sunRotationControllerY.setValue(LightManager.sunlight.rotation.y);
            }
            if (sunRotationControllerZ) {
                sunRotationControllerZ.setValue(LightManager.sunlight.rotation.z);
            }
        }
        envLightFolder.add(LightManager.sunlight.position, 'x').name('位置 x').onChange(syncSunRotation);
        envLightFolder.add(LightManager.sunlight.position, 'y').name('位置 y').onChange(syncSunRotation);
        envLightFolder.add(LightManager.sunlight.position, 'z').name('位置 z').onChange(syncSunRotation);

        sunRotationControllerX = envLightFolder.add({ x: LightManager.sunlight.rotation.x * 180 / Math.PI }, 'x').name('方向 x').onChange((value: number) => {
            LightManager.sunlight.rotation.x = value * Math.PI / 180;
        })
        sunRotationControllerY = envLightFolder.add({ y: LightManager.sunlight.rotation.y * 180 / Math.PI }, 'y').name('方向 y').onChange((value: number) => {
            LightManager.sunlight.rotation.y = value * Math.PI / 180;
        })
        sunRotationControllerZ = envLightFolder.add({ z: LightManager.sunlight.rotation.z * 180 / Math.PI }, 'z').name('方向 z').onChange((value: number) => {
            LightManager.sunlight.rotation.z = value * Math.PI / 180;
        })

        envLightFolder.add(this._envLightConfigs.subSunLight, 'intensity', 0, 10, 0.01)
            .name('辅光强度')
            .onChange((value: number) => {
                this._envLightConfigs.subSunLight.intensity = value;
            });

        envLightFolder.addColor(this._envLightConfigs.subSunLight, 'color')
            .name('颜色')
            .onChange((value: Color) => {
                this._envLightConfigs.subSunLight.color = value;
            });

        envLightFolder.add({ showHelper: this._subSunHelper.visible }, 'showHelper')
            .name('外显')
            .onChange((value: boolean) => {
                this._subSunHelper.visible = value;
            });

        let subSunRotationControllerX: Controller;
        let subSunRotationControllerY: Controller;
        let subSunRotationControllerZ: Controller;
        let syncSubSunRotation = function () {
            if (subSunRotationControllerX) {
                subSunRotationControllerX.setValue(LightManager.subSunLight.rotation.x);
            }
            if (subSunRotationControllerY) {
                subSunRotationControllerY.setValue(LightManager.subSunLight.rotation.y);
            }
            if (subSunRotationControllerZ) {
                subSunRotationControllerZ.setValue(LightManager.subSunLight.rotation.z);
            }
        }
        envLightFolder.add(LightManager.subSunLight.position, 'x').name('位置 x').onChange(syncSubSunRotation);
        envLightFolder.add(LightManager.subSunLight.position, 'y').name('位置 y').onChange(syncSubSunRotation);
        envLightFolder.add(LightManager.subSunLight.position, 'z').name('位置 z').onChange(syncSubSunRotation);

        subSunRotationControllerX = envLightFolder.add({ x: LightManager.subSunLight.rotation.x * 180 / Math.PI }, 'x').name('方向 x').onChange((value: number) => {
            LightManager.subSunLight.rotation.x = value * Math.PI / 180;
        })
        subSunRotationControllerY = envLightFolder.add({ y: LightManager.subSunLight.rotation.y * 180 / Math.PI }, 'y').name('方向 y').onChange((value: number) => {
            LightManager.subSunLight.rotation.y = value * Math.PI / 180;
        })
        subSunRotationControllerZ = envLightFolder.add({ z: LightManager.subSunLight.rotation.z * 180 / Math.PI }, 'z').name('方向 z').onChange((value: number) => {
            LightManager.subSunLight.rotation.z = value * Math.PI / 180;
        })
    }

    public changeEnvLightCtr(lightMode: LightMode) {
        this.lightMode = lightMode;
        let ambient = LightManager.getNightAmbientLight();
        // console.log(this._lightMode, ambient);
        if (this._lightMode === LightMode.Day) {
            ambient =LightManager.getDayAmbientLight();
        } else  if (this._lightMode === LightMode.Night) {
            ambient = LightManager.getNightAmbientLight();
        }
        this._envLightConfigs.ambient = ambient;
        this.clearFolder(this._envLightFolder);
        this.addEnvLightCtr(this._envLightFolder);
    }


    public removeEnvLight() {
        this._sunHelper.removeFromParent();
        this._subSunHelper.removeFromParent();
    }

    public clearFolder(folder: GUI) {
        folder.folders.forEach(subFolder => {
            this.clearFolder(subFolder);
            subFolder.destroy();
        });
        while (folder.controllers.length > 0) {
            folder.controllers[0].destroy();
        }
    }
}