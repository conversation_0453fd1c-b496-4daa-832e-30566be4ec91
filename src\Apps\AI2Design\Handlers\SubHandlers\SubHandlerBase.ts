import { T_Handlerbase } from "../../../LayoutAI/Handlers/HandlerBase";
import { AI2DesignManager } from "../../AI2DesignManager";
import { TCadRoomStrucureLayer} from "../../../LayoutAI/Drawing/TCadRoomFrameLayer";
import { ZRect } from "@layoutai/z_polygon";
import { T_TransformElement } from "../../../LayoutAI/Layout/TransformElements/T_TransformElement";
import { OperationInfo } from "@/Apps/LayoutAI/OperationInfos/OperationInfo";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TBaseGroupEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity";

export class SubHandlerBase extends T_Handlerbase
{
  

    manager : AI2DesignManager;
    constructor(manager:AI2DesignManager, name:string="")
    {
        super(name);
        this.manager = manager;
    }   

    get layer_AiCadDrawingLayer()
    {
        return this.manager.layer_CadRoomFrameLayer as TCadRoomStrucureLayer;
    }
    get ai_cad_data()
    {
        return this.layer_AiCadDrawingLayer ?.ai_cad_data;
    }
    get EventSystem()
    {
        return this.manager.EventSystem;
    }
    get painter()
    {
        return this.manager.painter;
    }

    updateTransformInfo() : OperationInfo
    {
        return null;
    }

    cleanState()
    {
        
    }

    update()
    {
        this.manager.update();
    }

    updateSidePanel()
    {

    }
    rotate()
    {
        
    }
    flip()
    {
        
    }

    deleteElement()
    {

    }
    updateSize(params?:any)
    {

    }
    toAiCadMode()
    {

    }
    cleanData()
    {
        
    }

    copySelectedTarget()
    {
        
    }

    updateEditNum()
    {
        
    }

    updateCandidateRects()
    {

    }

    cleanSelection()
    {
        
    }
}