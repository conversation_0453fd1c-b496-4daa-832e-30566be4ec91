import { AI2<PERSON><PERSON><PERSON>odeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { HotelDesignSubHandler } from "./HotelDesignBaseHandler";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { EventName } from "@/Apps/EventSystem";

import { message } from '@svg/antd';
import { I_TopBarItem } from "../../UiTemplates/I_TopBar/I_TopBarItem";
import { PolygonDrawingHelper } from "../../../../LayoutAI/PolyDrawingHelper/PolygonDrawingHelper";
import { TBaseSpaceEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSpaceEntities/TBaseSpaceEntity";
import { T_AddOrDeleteEntityOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_AddOrDeleteEntityOperationInfo";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { RectDrawingHelper } from "@/Apps/LayoutAI/PolyDrawingHelper/RectDrawingHelper";
import { BaseSpaceType } from "@/Apps/LayoutAI/Layout/IRoomInterface";

export class DrawBuildingSpaceSubHandler extends HotelDesignSubHandler
{
    protected _poly_drawing_helper : RectDrawingHelper;
    protected _mouseState : number;

    protected _space_type : string;
    constructor(mode_handler:AI2BaseModeHandler)
    {
        super(mode_handler);
        this._poly_drawing_helper = new RectDrawingHelper();
        this._mouseState = 0;
        this._space_type = BaseSpaceType.RoomGroupSpace;
    }

    protected _initTopBarItems()
    {
        let topBarItems : I_TopBarItem[] = [
            {
                id : "adsorbTol",
                title : LayoutAI_App.t("吸附距离"),
                type :"select",
                valueType:"number",
                options : [
                    {
                        label:"100mm",
                        value:'100'
                    },
                    {
                        label:"200mm",
                        value:'200'
                    }
                ],
                defaultValue :''+ this._poly_drawing_helper.adsorb_tol,
                onChange:(val:string)=>{
                    this._poly_drawing_helper.adsorb_tol = parseInt(val) || 100;
                }
            },{
                id : "hotelAreaTypes",
                title : LayoutAI_App.t("区域类别"),
                type :'select',
                options : [
                    {
                        label:LayoutAI_App.t(HotelDesignSubHandler.HotelSpaceNameDict[BaseSpaceType.RoomGroupSpace]),
                        value: BaseSpaceType.RoomGroupSpace
                    },
                    {
                        label:LayoutAI_App.t(HotelDesignSubHandler.HotelSpaceNameDict[BaseSpaceType.PublicAreaSpace]),
                        value: BaseSpaceType.PublicAreaSpace
                    },
                    {
                        label:LayoutAI_App.t(HotelDesignSubHandler.HotelSpaceNameDict[BaseSpaceType.OtherAreaSpace]),
                        value: BaseSpaceType.OtherAreaSpace
                    },
                ],
                valueType:"string",
                defaultValue :''+ this._space_type,
                onChange:(val:string)=>{
                    console.log(val);
                    this._space_type = val;
                }
            }
        ]
        return topBarItems;
    }

    protected doneDrawing()
    {
        this._poly_drawing_helper.doneDrawing();

        let result_poly = this._poly_drawing_helper.updateResultPoly();
        if(result_poly && result_poly.vertices.length > 2)
        {
            let space_entity = new TBaseSpaceEntity(result_poly, HotelDesignSubHandler.HotelSpaceNameDict[this._space_type]||"", this._space_type);

            let info = new T_AddOrDeleteEntityOperationInfo("Add",space_entity,this.manager);
            info.redo();
            this.manager.appendOperationInfo(info);
        }

        this._poly_drawing_helper.reInit();

        this.update();

        LayoutAI_App.RunCommand(LayoutAI_Commands.AcceptLeaveSubHandler);

    }

    updateCandidatePoints()
    {
        let entities : TBaseEntity[] = [...this.container._wall_entities,...this.container._room_entities,...this.container._structure_entities];
        this._poly_drawing_helper.initExsorbPoints({fromPolygons:entities.map((entity)=>entity.polygon || entity.rect)});
    }
    enter(state?: number): void {
        super.enter(state);
        this._poly_drawing_helper.reInit();
        LayoutAI_App.emit(EventName.TopBarItemChanged,{visible:true,topBarItems:this._initTopBarItems()})

        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit_M(EventName.SelectingTarget, null);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, true);
        if(this._cad_mode_handler.manager)
        {
            this._cad_mode_handler.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);
        }
        this.updateCandidatePoints();
        // message.info(LayoutAI_App.t("左键添加,右键回退"));

    }
    leave(state?: number): void {
        super.leave(state);
        LayoutAI_App.emit(EventName.TopBarItemChanged,{visible:false,topBarItems:[]})
        LayoutAI_App.emit(EventName.ShowWallTopMenu, false);
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, false);

    
        this.updateCandidateRects();
        this._poly_drawing_helper.reInit();

        if(this._cad_mode_handler.manager)
        {
            this._cad_mode_handler.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        }
    }
    onmousedown(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        this._mouseState = ev.buttons;
        if(ev.buttons == 1)
        {
            this._cad_mode_handler._is_moving_element = true;
            this._poly_drawing_helper.updatePoint(pos,"Adding",!ev.ctrlKey);

            this.update();
        }
        else if(ev.buttons == 2)
        {
            this._poly_drawing_helper.updatePoint(pos,"PopBack",!ev.ctrlKey);
            this._cad_mode_handler._is_moving_element = true;
            this.update();
        }

    }

    onmousemove(ev: I_MouseEvent): void {
    
        if(ev.buttons==0 || ev.buttons==1 || ev.buttons == 2)
        {
            this._cad_mode_handler._is_moving_element = true;
        }
        else{
            this._cad_mode_handler._is_moving_element = false;
        }
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        this._poly_drawing_helper.updatePoint(pos,"Editing",!ev.ctrlKey);

        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        // 检查是否是长按移动画布的情况
        if(this._mouseState == 1)
        {
            this._cad_mode_handler._is_moving_element = false;
            this._poly_drawing_helper.updatePoint(pos,"Added",!ev.ctrlKey);

            console.log(this._poly_drawing_helper.drawing_state);
            if(this._poly_drawing_helper.drawing_state==="Done")
            {
                this.doneDrawing();
            }
            this.update();
        }
        this._cad_mode_handler._is_moving_element = false;
        this._mouseState = 0;

        this.update();
    }

    drawCanvas(): void {
        this._poly_drawing_helper.drawCanvas(this.painter);
    }
}