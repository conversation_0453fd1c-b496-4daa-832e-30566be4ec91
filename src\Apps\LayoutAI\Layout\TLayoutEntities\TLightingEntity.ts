import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TFurnitureEntity } from "./TFurnitureEntity";

/**
 *  照明图元
 */
export class TLightingEntity extends TFurnitureEntity
{
    constructor(figure_element:TFigureElement)
    {
        super(figure_element);
        // this.type = "Decoration";
        this.realType = "Lighting";

    }

    setRectProperties(): void {
        super.setRectProperties();
    }
}