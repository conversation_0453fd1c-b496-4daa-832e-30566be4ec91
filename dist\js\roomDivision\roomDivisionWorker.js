function sendReady(e){self.postMessage({msgType:"Ready",data:e})}function DivideHouse(e,i){return RoomsDivisionModule.DivideHouse(e,i)}importScripts("RoomsDivision.js"),RoomsDivisionModule.onRuntimeInitialized=()=>{console.log("RoomsDivisionModule initialized"),sendReady("RoomsDivisionModule initialized")},self.onmessage=function(e){let i=e.data,s=i.method,o=i.msgId;if("DivideHouse"===s){let e=DivideHouse(i.houseJson,i.ruleJson);self.postMessage({msgId:o,msgType:"Result",result:e})}};