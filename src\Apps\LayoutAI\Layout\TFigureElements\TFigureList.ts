import { TPainter } from "../../../LayoutAI/Drawing/TPainter";
import { I_FigureElement, TFigureElement } from "./TFigureElement";



export interface I_FigureList
{
    target_room_names ?: string;
    figure_elements ?: I_FigureElement[];

}
/**
 *  一组图元列表, 可以认为定义了 一个布局里面的图元内容
 */
export class TFigureList
{
    target_room_names : string; // 适配的空间名称, 用分号隔开
    figure_elements ?: TFigureElement[];


    _src_data : I_FigureList;


    constructor(data:I_FigureList={})
    {
        this.loadData(data);
    }

    clone()
    {
        let group = new TFigureList(this);

        for(let i=0; i < group.figure_elements.length; i++)
        {
            group.figure_elements[i].rect.copy(this.figure_elements[i].rect);
        }
        return group;
    }

    clear()
    {
        this.figure_elements = [];
    }

    removeElement(figure_element:TFigureElement)
    {
        let id = this.figure_elements.indexOf(figure_element);
        if(id >= 0)
        {
            this.figure_elements.splice(id,1);
        }
    }
    loadData(data:I_FigureList){
        // this._src_data = data;
        this.target_room_names = data.target_room_names || "";

        this.figure_elements = [];
        if(data.figure_elements)
        {
            for(let figure of data.figure_elements)
            {
                let figure_element = new TFigureElement(figure);
                this.figure_elements.push(figure_element);
            }
        }

    }

    exportData() : I_FigureList
    {
        let res : I_FigureList = {
            target_room_names : this.target_room_names,
            figure_elements : []
        };
        for(let ele of this.figure_elements)
        {
            res.figure_elements.push(ele.exportJson())
        }

        return res;
    }

    drawFigures(painter : TPainter)
    {
        let figure_group = this;
        let drawing_figures : TFigureElement[] = [];
        for(let figure of figure_group.figure_elements)
        {
            drawing_figures.push(figure);

        }
        drawing_figures.sort((a,b)=>a.z_level-b.z_level);

        for(let figure of drawing_figures)
        {
            figure.drawFigure(painter);
        }

    }
}


