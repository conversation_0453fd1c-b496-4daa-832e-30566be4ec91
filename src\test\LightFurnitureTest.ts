import { <PERSON>, G<PERSON> } from "lil-gui";
import { Color } from "three";
import { IFrameTestConfig } from "./IFrameTestConfig";
import { DefaultLights } from "@/pages/SdkFrame/MsgCenter/DefaultLights";
import { I_SimpleFigureElement, I_RoomSubAreaSimpleData } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { I_ZRectData } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ZRectShapeType } from "@layoutai/z_polygon";

export class LightingFurnitureTest {
    private _gui: GUI;

    private _addDownLights: boolean = IFrameTestConfig.defaultAddDownLights;
    private _addTrack: boolean = IFrameTestConfig.defaultAddTrack;
    private _lightCategory: string = IFrameTestConfig.defaultLightCategory;
    private _light_product_type_mapping_table = IFrameTestConfig.lightProductTypeMappingTable;
    private _lightConfigs: { [key: string]: I_SimpleFigureElement } = {};
    private _lightMethod: string = IFrameTestConfig.defaultLightMethod;
    private _lightMethods: { [key: string]: number } = IFrameTestConfig.lightMethods;
    

    constructor(gui: GUI) {
        this._gui = gui;
        this._lightConfigs = JSON.parse(JSON.stringify(DefaultLights));
    }

    public get config(){
        return {
            addDownLights: this._addDownLights,
            addTrack: this._addTrack,
            lightCategory: this._lightCategory,
            lightMethod: this._lightMethod,
            lightMethods: this._lightMethods,
            lightConfigs: this._lightConfigs,
        }
    }

    public addLightFurnitureCtr() {
        let lightFurnitureFolder = this._gui.addFolder('灯具');
        lightFurnitureFolder.close();
        let topOffsetController: Controller;
        let materialIdController: Controller;
        let brightnessController: Controller;
        let colorController: Controller;
        let trackHeightController: Controller;


        lightFurnitureFolder.add({ addDownLights: this._addDownLights }, 'addDownLights')
            .name('是否添加灯具')
            .onChange((value: boolean) => {
                this._addDownLights = value;
            });
        lightFurnitureFolder.add({ addTrack: this._addTrack }, 'addTrack')
        .name('是否添加轨道')
        .onChange((value: boolean) => {
            this._addTrack = value;
            // 当选择添加轨道时，显示轨道高度控制器 隐藏灯具排列控制器
            if (trackHeightController) {
                trackHeightController.domElement.style.display = value ? '' : "none";
                lightArrangeController.domElement.style.display = value ? 'none' : '';

            }
        });

        lightFurnitureFolder.add({ lightType: this._lightCategory }, 'lightType', Object.keys(this._light_product_type_mapping_table))
            .name('灯具类型')
            .onChange((value: string) => {
                this._lightCategory = value;
                let topOffset: number = this._lightConfigs[this._lightCategory].topOffset;
                if (topOffsetController) {
                    topOffsetController.setValue(topOffset);
                }
                let materialId: string = this._lightConfigs[this._lightCategory].materialId;
                if (materialIdController) {
                    materialIdController.setValue(materialId);
                }
                let brightness: number = this._lightConfigs[this._lightCategory].brightness;
                if (brightnessController) {
                    brightnessController.setValue(brightness);
                }
                let color: number = this._lightConfigs[this._lightCategory].color;
                if (colorController) {
                    let lightColorObj = new Color(color);
                    colorController.setValue([lightColorObj.r, lightColorObj.g, lightColorObj.b]);
                }
            });

        let materialId: string = this._lightConfigs[this._lightCategory].materialId;
        materialIdController = lightFurnitureFolder.add({ materialId: materialId }, 'materialId')
            .name('材质ID')
            .onChange((value: string) => {
                this._lightConfigs[this._lightCategory].materialId = value;
            });
        let topOffset: number = this._lightConfigs[this._lightCategory].topOffset;
        topOffsetController = lightFurnitureFolder.add({ topOffset: topOffset }, 'topOffset')
            .name('灯具高度')
            .onChange((value: number) => {
                this._lightConfigs[this._lightCategory].topOffset = value;
            });
        
        // 添加轨道高度控制器，直接控制轨道配置的topOffset
        let trackTopOffset: number = this._lightConfigs["轨道"].topOffset;
        trackHeightController = lightFurnitureFolder.add({ trackTopOffset: trackTopOffset }, 'trackTopOffset')
            .name('轨道高度')
            .onChange((value: number) => {
                if (this._lightConfigs.hasOwnProperty("轨道")) {
                    this._lightConfigs["轨道"].topOffset = value;
                }
            });
        // 初始时隐藏轨道高度控制器
        trackHeightController.domElement.style.display = this._addTrack ? '' : 'none';
        
        let brightness: number = this._lightConfigs[this._lightCategory].brightness;
        brightnessController = lightFurnitureFolder.add({ brightness: brightness }, 'brightness')
            .name('灯具亮度')
            .onChange((value: number) => {
                this._lightConfigs[this._lightCategory].brightness = value;
            });

        let lightColor: number = this._lightConfigs[this._lightCategory].color;
        let lightColorObj = new Color(lightColor);
        colorController = lightFurnitureFolder.addColor({ color: [lightColorObj.r, lightColorObj.g, lightColorObj.b] }, 'color')
            .name('灯具颜色')
            .onChange((value: number[]) => {
                lightColorObj.setRGB(value[0], value[1], value[2]);
                this._lightConfigs[this._lightCategory].color = lightColorObj.getHex();
            });


        let lightArrangeController = lightFurnitureFolder.add({ lightMethod: this._lightMethod }, 'lightMethod', Object.keys(this._lightMethods))
            .name('灯具排列')
            .onChange((value: string) => {
                this._lightMethod = value;
            });
        // 如果勾选了轨道，则隐藏灯具排列控制器
        lightArrangeController.domElement.style.display = this._addTrack ? 'none' : '';
    }

    /**
     * @param sub_area : 子区域
     * @param method 0: 中间排列 1: 四周排布
     * @param options padding: 边缘留白  lightGap: 两灯距离
     * @param addTrack 是否添加轨道
     */
    public addDownLightsSubArea(
        sub_area: I_RoomSubAreaSimpleData,
        method: number = 0,
        options: {
            lightCategory?: string; // 灯具类型
            lightNum?: number;
            lightGap?: number;
            padding?: number;
        } = {},
        addTrack: boolean = false,
    ): I_SimpleFigureElement[] {
        let lights: I_SimpleFigureElement[] = [];
        
        if (method == 0) {
            // 中间排列
            lights = this.makeDownLightsInRowWithRectData(
                sub_area.area_rect,
                { ...options }
            );
        } else if (method == 1) {
            // 四周排布
            lights = this.makeDownLightsInRowWithRectData(
                sub_area.area_rect,
                {
                    ...options,
                    nor: { x: 1, y: 0, z: 0 },
                    offsetFrom: "Bottom",
                    offsetY: 100,
                }
            );
            lights.push(
                ...this.makeDownLightsInRowWithRectData(
                    sub_area.area_rect,
                    {
                        ...options,
                        nor: { x: -1, y: 0, z: 0 },
                        offsetFrom: "Bottom",
                        offsetY: 200,
                    }
                )
            );
            lights.push(
                ...this.makeDownLightsInRowWithRectData(
                    sub_area.area_rect,
                    {
                        ...options,
                        nor: { x: 0, y: 1, z: 0 },
                        offsetFrom: "Bottom",
                        offsetY: 200,
                    }
                )
            );
            lights.push(
                ...this.makeDownLightsInRowWithRectData(
                    sub_area.area_rect,
                    {
                        ...options,
                        nor: { x: 0, y: -1, z: 0 },
                        offsetFrom: "Bottom",
                        offsetY: 200,
                    }
                )
            );
        }

        // 如果勾选了轨道，添加轨道并将灯具放在轨道上
        if ((addTrack || this._addTrack) && lights.length > 0) {
            let trackLights = this._addTrackForLights(lights, sub_area.area_rect, method, options);
            return trackLights;
        }

        return lights;
    }


    /**
   * 为灯具添加轨道
   * @param lights 灯具列表
   * @param rect_data 区域矩形数据
   * @param method 排列方法
   * @param options 配置选项
   */
    private _addTrackForLights(
        lights: I_SimpleFigureElement[],
        rect_data: I_ZRectData,
        method: number,
        options: {
            lightCategory?: string;
            lightNum?: number;
            lightGap?: number;
            padding?: number;
        }
    ): I_SimpleFigureElement[] {
        let result: I_SimpleFigureElement[] = [];

        // 创建轨道配置，使用用户设置的轨道高度
        let trackData: I_SimpleFigureElement = DefaultLights["轨道"];

        // 先创建轨道
        if (method == 0) {
            // 中间排列 - 创建一条轨道
            let track = this._createTrackForCenterLayout(lights, rect_data, trackData, options);
            result.push(track);
        } else if (method == 1) {
            // 四周排布  不支持
            console.warn("四周排布 不支持轨道+灯具");
        }

        // 然后调整灯具位置，将其放在轨道上，并设置为明装不开洞
        lights.forEach(light => {
            // 设置为明装，不开洞
            light.installType = 1;
            // 调整灯具的Z坐标，使其与轨道高度一致
            light.pos = { x: light.pos.x, y: light.pos.y, z: trackData.topOffset };
            console.log('设置灯具为明装:', light.category, 'installType:', light.installType, 'z:', light.pos.z);
        });

        // 最后添加灯具
        result.push(...lights);
        return result;
    }


    /**
     * 为中间排列创建轨道
     */
    private _createTrackForCenterLayout(
        lights: I_SimpleFigureElement[],
        rect_data: I_ZRectData,
        trackData: I_SimpleFigureElement,
        options: any
    ): I_SimpleFigureElement {
        let rect = new ZRect();
        rect.importRectData(rect_data);

        // 如果设置了法向, 则矩形方向长宽调整（与makeDownLightsInRowWithRectData保持一致）
        if (options?.nor) {
            rect = ZRect.fromPoints(rect.positions, options.nor);
        }

        let trackNor = rect.nor;
        // 轨道长度有2种计算方式
        // 1.使用配置的长度
        // let trackLength = trackData.length;

        // 2.使用下方灯具的跨度去计算实际长度 
        // 计算轨道中心点位置
        let lightsInfo = this._getLightsDimensions(lights);
        // 使用计算出的整体中心点和边界信息
        let { overallCenter, bounds } = lightsInfo;

        // 根据轨道方向判断使用X坐标跨度还是Y坐标跨度
        let trackLength: number;
        if (Math.abs(trackNor.x) < Math.abs(trackNor.y)) {
            // 轨道主要沿X方向，使用X坐标跨度
            trackLength = bounds.maxX - bounds.minX;
        } else {
            // 轨道主要沿Y方向，使用Y坐标跨度
            trackLength = bounds.maxY - bounds.minY;
        }

        let centerX: number = overallCenter.x;
        let centerY: number = overallCenter.y;
        let nor = { x: trackNor.x, y: trackNor.y, z: trackNor.z };

        // 创建轨道，使用轨道配置里的topOffset作为Z坐标
        let track = {
            ...trackData,
            pos: { x: centerX, y: centerY, z: trackData.topOffset },
            length: trackLength,
            depth: trackData.depth,
            nor: nor
        };

        return track;
    }

    private makeDownLightsInRowWithRectData(
        rect_data: I_ZRectData,
        options: {
            nor?: { x: number; y: number; z: number };
            offsetFrom?: "Center" | "Bottom";
            offsetY?: number;
            lightCategory?: string;
            lightNum?: number;
            lightGap?: number;
            padding?: number;
        } = {}
    ): I_SimpleFigureElement[] {
        let lightCategory = options?.lightCategory || "筒灯";
        let lightData = this._lightConfigs[lightCategory] || this._lightConfigs["筒灯"];
        lightCategory = lightData.category;

        let lightGap = options?.lightGap || 0;
        let lightAreaLength = lightGap + lightData.length;
        let lightNum = 1;
        let padding = options?.padding || 400;
        let rect = new ZRect();
        rect.importRectData(rect_data);

        // 如果设置了法向, 则矩形方向长宽调整
        if (options?.nor) {
            rect = ZRect.fromPoints(rect.positions, options.nor);
        }
        if (lightGap) {
            lightGap = options.lightGap;
            lightAreaLength = lightGap + lightData.length;
            lightNum = Math.floor((rect.w - padding) / lightAreaLength + 1e-3);
        }
        if (options.lightNum) {
            lightNum = Math.min(
                Math.floor((rect.w - padding) / lightAreaLength + 1e-3),
                options.lightNum
            );
        }
        let yVal = 0;
        let offsetFrom = options?.offsetFrom;
        let offsetY = options?.offsetY || 0;

        if (offsetFrom == "Bottom") {
            if (offsetY < lightData.depth / 2) {
                offsetY = lightData.depth / 2;
            }
            yVal = -rect._h / 2 + offsetY;
        }

        let total_LightsLength = lightAreaLength * lightNum;
        let start_x = -total_LightsLength / 2;

        let result_lights: I_SimpleFigureElement[] = [];
        for (let i = 0; i < lightNum; i++) {
            let xx = start_x + lightAreaLength * i + lightAreaLength / 2;
            let pos = rect.unproject({ x: xx, y: yVal });
            // 使用rect.nor 作为灯具方向
            result_lights.push(this.createLight(lightCategory, pos, rect.nor));
        }

        return result_lights;
    }
    
    /**
     * @param lightCategory 灯光类型 
     * @param pos 不用考虑z值, 给0即可
     */
    private createLight(
        lightCategory: string,
        pos: { x: number; y: number; z: number },
        nor: { x: number; y: number; z: number } = { x: 1, y: 0, z: 0 }
    ): I_SimpleFigureElement {
        if (this._lightConfigs[lightCategory]) {
            return {
                ...this._lightConfigs[lightCategory],
                pos: { x: pos.x, y: pos.y, z: pos.z },
                nor: { x: nor.x, y: nor.y, z: nor.z },
            };
        }
        return null;
    }

    /**
     * 获取一堆灯具的长宽信息
     * @param lights 灯具列表
     * @returns 包含所有灯具整体边界与中心点信息
     */
    _getLightsDimensions(lights: I_SimpleFigureElement[]): {
        overallCenter: { x: number; y: number; z: number };
        bounds: { minX: number; maxX: number; minY: number; maxY: number };
    } {
        let lightDimensions = lights.map(light => {
            let center = light.pos;
            let width = light.depth;
            let length = light.length;

            // 计算矩形的四个顶点
            let vertices = [
                { x: center.x - width / 2, y: center.y - length / 2 }, // 左下
                { x: center.x + width / 2, y: center.y - length / 2 }, // 右下
                { x: center.x + width / 2, y: center.y + length / 2 }, // 右上
                { x: center.x - width / 2, y: center.y + length / 2 }  // 左上
            ];

            return { center, width: width, length: length, vertices };
        });

        // 计算整体边界
        let allX: number[] = [];
        let allY: number[] = [];
        let allZ: number[] = [];

        lightDimensions.forEach(light => {
            light.vertices.forEach(vertex => {
                allX.push(vertex.x);
                allY.push(vertex.y);
            });
            allZ.push(light.center.z);
        });

        let minX = Math.min(...allX);
        let maxX = Math.max(...allX);
        let minY = Math.min(...allY);
        let maxY = Math.max(...allY);
        let avgZ = allZ.reduce((sum, z) => sum + z, 0) / allZ.length;

        // 计算整体中心点
        let overallCenter = {
            x: (minX + maxX) / 2,
            y: (minY + maxY) / 2,
            z: avgZ
        };

        return {
            overallCenter,
            bounds: { minX, maxX, minY, maxY }
        };
    }
}