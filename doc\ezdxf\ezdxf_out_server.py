from flask  import Flask,request,Response
from flask_cors import CORS
from ezdxf_exporter import EzdxfExporter
app = Flask(__name__)
CORS(app, supports_credentials=True)

@app.route("/")
def hello_world():
    return 'hello world'
@app.route("/test")
def test():
    return 'hello well done!\n'

@app.route("/get_dwg", methods=['POST','GET'])
def get_dwg():
    if request.method == 'POST':
        data = request.json
        exporter = EzdxfExporter()
        ezdxf_data = data['data']
        filename = data['filename']
        dwgtxt = exporter.json2dwg(ezdxf_data,filename=filename)
    return {
        'data' : dwgtxt
    }
    
if __name__ == '__main__':
    app.run("0.0.0.0",8384)
