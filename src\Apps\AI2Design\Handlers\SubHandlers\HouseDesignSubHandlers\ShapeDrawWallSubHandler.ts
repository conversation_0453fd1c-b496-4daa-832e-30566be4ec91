import { EventName } from "@/Apps/EventSystem";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { Vector3 } from "three";
import { HouseDesignSubHandler } from "./HouseDesignBaseHandler";
import { T_AddWallOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_AddWallOperationInfo";
import { AIHouseDesignModeHandler } from "../../AIHouseDesignModeHandler";
import { checkIsMobile } from "@/config";

interface ShapeData {
    name: string;
    points: {x: number, y: number}[];
}

export const ShapeConfig: {[key: string]: ShapeData} = {
    // 横墙
    horizontalWall: {
        name: "横墙",
        points: [
            { x: -1000, y: 0 },      // 左下
            { x: 1000, y: 0 },   // 右下
        ]
    },
    // 竖墙
    verticalWall: {
        name: "竖墙",
        points: [
            { x: 0, y: -1000 },      // 左下
            { x: 0, y: 1000 },   // 右下
        ]
    },
    // 正方形 3420x3420
    square: {
        name: "正方形",
        points: [
            { x: 0, y: 0 },      // 左下
            { x: 3420, y: 0 },   // 右下
            { x: 3420, y: 3420 },// 右上
            { x: 0, y: 3420 },   // 左上
            { x: 0, y: 0 }       // 回到起点(闭合)
        ]
    },
    // 长方形 4000x3000
    rectangle: {
        name: "长方形",
        points: [
            { x: 0, y: 0 },      // 左下
            { x: 4000, y: 0 },   // 右下
            { x: 4000, y: 3000 },// 右上
            { x: 0, y: 3000 },   // 左上
            { x: 0, y: 0 }       // 回到起点(闭合)
        ]
    },
    // L形 
    LShape: {
        name: "L形",
        points: [
            { x: 0, y: 0 },       // 左下
            { x: 6120, y: 0 },    // 右下
            { x: 6120, y: 3120 }, // 右中
            { x: 2520, y: 3120 }, // 中1
            { x: 2520, y: 6120 }, // 中上
            { x: 0, y: 6120 },    // 左上
            { x: 0, y: 0 }        // 回到起点(闭合)
        ]
    },
    // 凸形
    convex: {
        name: "凸形",
        points: [
            { x: 0, y: 0 },       // 左下
            { x: 3120, y: 0 },    // 右下
            { x: 3120, y: 3000 }, // 右上
            { x: 7320, y: 3000 }, // 右中上
            { x: 7320, y: 4120 }, // 右中
            { x: 3120, y: 4120 }, // 左中
            { x: 3120, y: 7320 }, // 左中上
            { x: 0, y: 7320 },    // 左上
            { x: 0, y: 0 }        // 回到起点(闭合)
        ]
    },
    // 凹形
    concave: {
        name: "凹形",
        points: [
            { x: 0, y: 0 },       // 左下
            { x: 6720, y: 0 },    // 右下
            { x: 6720, y: 3120 }, // 右上
            { x: 4620, y: 3120 }, // 右中上
            { x: 4620, y: 1620 }, // 右中
            { x: 2100, y: 1620 }, // 左中
            { x: 2100, y: 3120 }, // 左中上
            { x: 0, y: 3120 },    // 左上
            { x: 0, y: 0 }        // 回到起点(闭合)
        ]
    },
    // 多边形（六边形示例）
    polygon: {
        name: "多边形",
        points: [
            { x: 1500, y: 0 },           // 左下
            { x: 6120, y: 0 },        // 右下
            { x: 6120, y: 2820 },     // 右中下
            { x: 4320, y: 2820 },     // 中间右
            { x: 4320, y: 6420 },     // 右上
            { x: 0, y: 6420 },        // 左上
            { x: 0, y: 2460 },        // 左中下
            { x: 1500, y: 2460 },        // 左中下
            { x: 1500, y: 0 },        // 左下
        ]
    }
};

/**
 * 形状画墙体
 */

export class ShapeDrawWallSubHandler extends HouseDesignSubHandler {
    // 常量
    private readonly MIN_WALL_SIZE = 60;
    _thickness: number = 240;
    // 形状起始点和偏移
    private _shape_start_point: Vector3 = null;
    
    // 墙体绘制相关
    drawing_wall_rects: ZRect[] = [];
    _candidate_point: Vector3 = null;
    _candidate_point_type: string = "";
    
    private _mouseDownType: number = -1;
    

    private _cadModeHandler: AIHouseDesignModeHandler;
    private _shape_draw_type: string;

    // 添加防抖计时器属性
    private _wheelDebounceTimer: ReturnType<typeof setTimeout> | null = null;

    // 保存最后一次滚轮事件的鼠标位置
    private _lastWheelEventPoint: {x: number, y: number} | null = null;



    constructor(cad_mode_handler: AIHouseDesignModeHandler) {
        super(cad_mode_handler);
        this._cadModeHandler = cad_mode_handler;
        this._candidate_point = new Vector3();
    }

    get wall_rects(): ZRect[] {
        return this.container.getCandidateRects(["Wall"]);
    }
    
    enter(state?: number): void {
        console.log("ShapeDrawWallSubHandler enter");
        super.enter(state);
        this._shape_draw_type = this._cadModeHandler.shape_draw_type || "square"; // 默认正方形
        this._shape_start_point = null;
        
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit_M(EventName.SelectingTarget, null);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, true);
        
        this.cleanSelection();
        this.manager.layout_container.cleanDimension();
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Moving);
        this.update();
    }
    
    leave(state?: number): void {
        console.log("ShapeDrawWallSubHandler leave");
        
        // 清除防抖计时器
        if (this._wheelDebounceTimer !== null) {
            clearTimeout(this._wheelDebounceTimer);
            this._wheelDebounceTimer = null;
        }
        
        super.leave(state);
        this.updateCandidateRects();
        
        LayoutAI_App.emit(EventName.ShowWallTopMenu, false);
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, false);

        this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        this.reset();
    }

    // 重置状态
    private reset(): void {
        // 清除防抖计时器
        if (this._wheelDebounceTimer !== null) {
            clearTimeout(this._wheelDebounceTimer);
            this._wheelDebounceTimer = null;
        }
        
        this.drawing_wall_rects = [];
        this._shape_start_point = null;
        this._shape_draw_type = null;
    }
    
    // 鼠标事件处理
    onmousedown(ev: I_MouseEvent): void {
        this._mouseDownType = ev.button;
        if(ev.button == 1) {
            this._cadModeHandler._is_moving_element = false;
            this.update();
            return;
        }
    }

    onmousemove(ev: I_MouseEvent): void {
        const pos = { x: ev.posX, y: ev.posY, z: 0 };
        if(this._mouseDownType == 1) {
            this._cadModeHandler._is_moving_element = false;
            this.update();
            return;
        }

        this._cadModeHandler._is_moving_element = true;
        // 直接更新候选点，不需要吸附
        this._candidate_point.copy(pos);
        // 更新预览形状
        this.updateDrawingRects();
        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        if(this._mouseDownType == 1 || !this._candidate_point) {
            this._mouseDownType = -1;

            return;
        }
        if(checkIsMobile())
            {
            let elementId = "#pad_house_left_panel";

                let scrollContainer = document.querySelector(elementId);
                const containerRect = scrollContainer.getBoundingClientRect();
                // 横屏下 鼠标松开的时候不超过侧边栏的右侧 则离开状态
                if((LayoutAI_App.instance._is_landscape))
                {
                    if(ev._ev.x <= containerRect.right && ev._ev.y > containerRect.top && ev._ev.y < containerRect.bottom)
                    {
                        this._leaveCurrentHandler();
                        return;
                    }
                } else 
                {
                    // 竖屏下 鼠标松开的时候不超过侧边栏顶部 则离开状态
                    if(ev._ev.y > containerRect.top)
                    {
                        this._leaveCurrentHandler();
                        return;
                    }
                }
        } else 
        {
            if(ev._ev.x < 340)
            {
                this._leaveCurrentHandler();
                return;
            }
        }

        this._mouseDownType = -1;
        
        this._cadModeHandler._is_moving_element = false;
        
        // 右键处理 - 直接退出模式
        if (ev.button === 2) {
            this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
            this.update();
            return;
        }

        // 直接使用当前候选点作为起点创建墙体
        this._shape_start_point = this._candidate_point.clone();
        
        // 更新绘制预览
        this.updateDrawingRects();
        
        // 验证墙体合法性
        if (this.validateWalls()) {
            // 创建墙体实体
            this.createWallEntities();
            
            // 清空绘制数据
            this.drawing_wall_rects = [];
            this._shape_start_point = null;
            // 直接退出模式
            this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
        }
        
        this.update();
    }

    private _leaveCurrentHandler()
    {
        this.cleanSelection();
        this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
    }

    // 修改onwheel方法，保存鼠标位置
    onwheel(ev: WheelEvent): void {
        // 保存当前鼠标位置
        this._lastWheelEventPoint = { x: ev.offsetX, y: ev.offsetY };
        
        // 调用父类方法进行缩放
        super.onwheel(ev);
        
        // 清除现有的定时器
        if (this._wheelDebounceTimer !== null) {
            clearTimeout(this._wheelDebounceTimer);
        }
        
        // 设置新的定时器，等待滚轮事件结束后再更新
        this._wheelDebounceTimer = setTimeout(() => {
            // 如果鼠标指针下有一个候选点，保持其位置相对于鼠标不变
            if (this._candidate_point && this._lastWheelEventPoint) {
                // 将鼠标位置转换为世界坐标
                const worldPoint = this.painter.canvas2world(this._lastWheelEventPoint);
                
                // 直接更新候选点到鼠标位置
                this._candidate_point.x = worldPoint.x;
                this._candidate_point.y = worldPoint.y;
                this._candidate_point.z = 0;
            }
            
            // 更新预览形状
            this.updateDrawingRects();
            this.update();
            
            this._wheelDebounceTimer = null;
        }, 50); // 50毫秒的延迟，可以根据需要调整
    }

    // 验证墙体合法性
    private validateWalls(): boolean {
        
        // 检查每个墙体是否足够长
        if (this.drawing_wall_rects.some(rect => rect._w < this.MIN_WALL_SIZE)) {
            console.warn("墙体太小，无法创建");
            return false;
        }
        
        return true;
    }
    
    // 创建墙体实体
    private createWallEntities(): void {
        for (const rect of this.drawing_wall_rects) {
            const info = new T_AddWallOperationInfo(this.manager);
            const entity = TWall.getOrMakeEntityOfCadRect(rect);
            info._entity = entity as TWall;
            info.redo(this.manager);
            this.manager.appendOperationInfo(info);
        }
    }

    // 更新绘制的形状墙体
    updateDrawingRects(): void {
        this.drawing_wall_rects = [];

        if (!this._candidate_point) return;

        // 获取选择的形状配置
        const shapeData = ShapeConfig[this._shape_draw_type] || ShapeConfig.square;
        
        // 特殊处理横墙和竖墙的厚度
        const thickness = (this._shape_draw_type === 'horizontalWall' || this._shape_draw_type === 'verticalWall') 
            ? 120  // 横墙和竖墙使用120的厚度
            : (this._thickness || 240);  // 其他形状使用默认厚度

        // 如果没有起点，候选点就是起点
        const startPoint = this._shape_start_point || this._candidate_point.clone();
        const points = shapeData.points;
        
        // 创建墙体
        for (let i = 0; i < points.length - 1; i++) {
            const p1 = points[i];
            const p2 = points[i + 1];
            
            // 起点 + 形状点位的偏移
            const pos1 = new Vector3(
                startPoint.x + p1.x,
                startPoint.y + p1.y,
                0
            );
            
            const pos2 = new Vector3(
                startPoint.x + p2.x,
                startPoint.y + p2.y,
                0
            );
            
            // 传入offsetType=1，使墙体沿着直线方向（起点指向终点）偏移半个厚度
            this.createWallRect(pos1, pos2, thickness, 1);
        }
    }

    // 创建单个墙体矩形
    createWallRect(start_pos: Vector3, end_pos: Vector3, thickness: number, offsetType: number = 0): void {
        // 计算方向向量和法向量
        const direction = new Vector3().subVectors(end_pos, start_pos).normalize();
        const normal = new Vector3(-direction.y, direction.x, 0);
        
        // 计算原始中线点和长度
        const originalCenter = new Vector3().addVectors(start_pos, end_pos).multiplyScalar(0.5);
        const length = start_pos.distanceTo(end_pos);
        
        // 对中线点进行偏移(沿着直线方向，而不是法线方向)
        const halfThickness = thickness / 2;
        // 使用direction而不是normal进行偏移
        const offset = direction.clone().multiplyScalar(offsetType * halfThickness);
        const center = originalCenter.clone().add(offset);
        
        // 创建墙体矩形
        const rect = new ZRect(length, thickness);
        rect.nor = normal; // 墙体的方向仍然保持不变
        rect.rect_center = center;
        
        // 设置墙体属性
        TBaseEntity.set_polygon_type(rect, 'Wall');
        this.drawing_wall_rects.push(rect);
    }

    // 绘制画布
    drawCanvas(): void {
        if (this.drawing_wall_rects.length > 0) {
            // 绘制墙体
        this.painter.fillStyle = "#3D9EFF";
        this.painter.strokeStyle = "#2b2b2b";
        this.painter.fillPolygons(this.drawing_wall_rects);
        this.painter.strokePolygons(this.drawing_wall_rects);

            // 如果没有绘制墙体，但有候选点，显示一个标记点
            const radius = 6 / this.painter._p_sc;
            this.painter.strokeStyle = "#f23dd1";
            this.painter.drawPointCircle(this._candidate_point, radius, 4);
        }
    }
    
}
