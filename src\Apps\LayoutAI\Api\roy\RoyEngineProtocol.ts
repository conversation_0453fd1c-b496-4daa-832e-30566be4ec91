import { CapinetQueryResp } from "./CapinetQueryResp";

/**
* @description 处理和实验室应用之间的消息定义
* <AUTHOR>
* @date 2025-04-11
* @lastEditTime 2025-04-11 14:34:34
* @lastEditors xuld
*/

export enum IframeBusinessType {
    SvgRoyScene = 'SvgRoyScene', // royscene场景
    ParamAICabinet = 'ParamAICabinet', // 柜子场景
}

export enum IframeMessageType {
    Error = 'Error',
    Message = 'Message',

    SceneStatus = 'SceneStatus',

    RoySceneDataInit = 'RoySceneDataInit',
    RoySceneDataUpdate = 'RoySceneDataUpdate',

    ClearScene = 'ClearScene',

    CameraDataSet = 'CameraDataSet',
    CameraDataGet = 'CameraDataGet',

    ChooseObject = 'ChooseObject',

    DebugData = 'DebugData',

    ParamAICabinetGen = 'ParamAICabinetGen',
    ParamAICabinetLib = 'ParamAICabinetLib',

    AICabCtrl = 'AICabCtrl',

    ExportGLB = 'ExportGLB',
}

export enum IframeSceneStatusType {
    EngineInit = 'EngineInit',
    SceneInit = 'SceneInit',
    ClearScene = 'ClearScene',
}

export enum IframeCameraEffectMode {
    Color = 'Color',
    Draft = 'Draft',
    DraftColor = 'DraftColor',
}

export interface IframeMessageSvgRoySceneDataInit {
    id?: string;
    schemeId?: string;
    version?: string;
    content?: ArrayBuffer | Uint8Array | any;
}

export interface IframeMessageSvgRoySceneDataUpdate {
    updateObjects?: any[];
    deleteObjects?: any[];
}

export interface IframeMessageCameraData {
    orbit?: boolean;// true 鸟瞰 flase 漫游
    fov?: number;// rad
    near?: number;// mm
    far?: number; // mm
    position?: number[]; // mm
    target?: number[]; // mm target 和 rotation 二选一，优先使用 target
    effectMode?: IframeCameraEffectMode;
}

export interface IframeSceneStatus {
    status: IframeSceneStatusType;
    msg: string;
    success: boolean;
}

export interface IframeDebugData {
    erudaEnable?: boolean;
    fpsCounterEnable?: boolean;
    dumpEngineInfo?: boolean;
}

export enum IframeAICabType {
    wardrobe = 'wardrobe',
    dining_cabinet = 'dining_cabinet',
    shoe_cabinet = 'shoe_cabinet',
}

export interface IframeAICabCtrlData {
    openDoor?: boolean;
    openDrawer?: boolean;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////

// prettier-ignore
export type IframeHandleTypeMapDataType<B extends IframeBusinessType, H extends IframeMessageType> = (
    B extends IframeBusinessType.SvgRoyScene ? (
        H extends IframeMessageType.CameraDataSet ? IframeMessageCameraData :
        H extends IframeMessageType.CameraDataGet ? void :
        H extends IframeMessageType.RoySceneDataInit ? IframeMessageSvgRoySceneDataInit :
        H extends IframeMessageType.RoySceneDataUpdate ? IframeMessageSvgRoySceneDataUpdate :
        H extends IframeMessageType.ClearScene ? void :
        H extends IframeMessageType.Message ? string :
        H extends IframeMessageType.Error ? string :
        H extends IframeMessageType.DebugData ? IframeDebugData :
        H extends IframeMessageType.SceneStatus ? IframeSceneStatus :
        unknown
    ) :
    B extends IframeBusinessType.ParamAICabinet ? (
        H extends IframeMessageType.CameraDataSet ? IframeMessageCameraData :
        H extends IframeMessageType.CameraDataGet ? void :
        H extends IframeMessageType.ClearScene ? void :
        H extends IframeMessageType.Message ? string :
        H extends IframeMessageType.Error ? string :
        H extends IframeMessageType.DebugData ? IframeDebugData :
        H extends IframeMessageType.SceneStatus ? IframeSceneStatus :
        H extends IframeMessageType.ParamAICabinetGen ? any[] :
        H extends IframeMessageType.ParamAICabinetLib ? any :
        H extends IframeMessageType.AICabCtrl ? any :
        H extends IframeMessageType.ExportGLB ? CapinetQueryResp :
        unknown
    ) :
    void
);

export interface IframeMessage {
    businessType: IframeBusinessType;
    handleType: IframeMessageType;
    data: IframeHandleTypeMapDataType<IframeBusinessType, IframeMessageType>;
}