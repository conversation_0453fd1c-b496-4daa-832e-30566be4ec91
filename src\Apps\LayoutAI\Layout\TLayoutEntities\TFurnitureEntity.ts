import { TAppManagerBase } from "../../../AppManagerBase";
import { Group, Vector3, Vector3Like } from "three";
import { LayoutAI_App, LayoutAI_Events } from "../../../LayoutAI_App";
import { g_FigureImagePaths } from "../../Drawing/FigureImagePaths";
import { TPainter } from "../../Drawing/TPainter";
import { FurnitureObject3D, MeshName, UserDataKey } from "../../Scene3D/NodeName";
import { compareNames, compareWordFittingCount } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { FigureCategoryManager } from "../TFigureElements/FigureCategoryManager";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TGraphBasicConfigs } from "../TLayoutGraph/TGraphBasicConfigs";
import { TSerialSizeRangeDB } from "../TLayoutGraph/TGroupTemplate/TSeriesSizeRangeDB";
import { TMaterialMatchingConfigs } from "../../Services/MaterialMatching/TMaterialMatchingConfigs";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TRoomEntity } from "./TRoomEntity";
import { BaseModeHandler } from "../../Handlers/BaseModeHandler";
import { DrawingFigureMode, IRoomEntityType, KeyEntity } from "../IRoomInterface";
import { TSubSpaceAreaEntity } from "./TSubSpaceAreaEntity";
/**
 *   家具集合的interface
 *   包含了addFurnitureEntity和removeFurnitureEntity方法
 */

/**
 *  绑定了一个TFigureElement
 */
export class TFurnitureEntity extends TBaseEntity {
    _figure_element: TFigureElement;
    _drawing_order: number;
    _matched_visible: boolean;

    private _room_entity: TRoomEntity;
    private _area_entity: TSubSpaceAreaEntity;

    private _group_parent : TFurnitureEntity;
    
    /**
     *  是否是单个家具
     */
    protected _is_single_furniture: boolean;

    static readonly EntityType:IRoomEntityType  = "Furniture";

    constructor(figure_element: TFigureElement) {
        super();
        this.type = TFurnitureEntity.EntityType;
        this.title = LayoutAI_App.t("产品信息");
        this._is_single_furniture = true;
        this._figure_element = figure_element || new TFigureElement();
        this._rect = this._figure_element.rect;

        this._figure_element._entity_uuid = this._uuid;
        this._drawing_order = 0;
        this._default_priority_for_selection = this._priority_for_selection = 10;
        this.rect._attached_elements["Entity"] = this;
        this._figure_element.furnitureEntity = this;

        this.updateRealType();

        if (this.figure_element.category === "地毯") {
            this._priority_for_selection = 9;
        }
        if (this.figure_element.category === "花洒") {
            this._priority_for_selection = 11;
        }
        this._name = this.figure_element.category;

        this._matched_visible = true;
    }
    public get area_entity(): TSubSpaceAreaEntity {
        return this._area_entity;
    }
    public set area_entity(value: TSubSpaceAreaEntity) {
        if(this._area_entity)
        {
            this._area_entity.removeFurnitureEntity(this);
        }
        this._area_entity = value;
        if(this._area_entity)
        {
            if(!this.group_parent)
            {
                this._area_entity.addFurnitureEntity(this);
            }
        }
    }
    set roomEntity(re: TRoomEntity) {

        if(this._room_entity)
        {
            this._room_entity.removeFurnitureEntity(this);
        }
        this._room_entity = re;

        if(this._room_entity)
        {
            if(!this.group_parent)
            {
                this._room_entity.addFurnitureEntity(this);
            }
            this.figure_elements.forEach((fe) => {
                fe._room = this._room_entity ? this._room_entity._room : null;
            });
            this.onRoomFloorThicknessChanged();

        }
    }

    get roomEntity(): TRoomEntity {
        return this._room_entity;
    }

    get group_parent()  : TFurnitureEntity
    {
        return this._group_parent;
    }

    set group_parent(t:TFurnitureEntity)
    {
        this._group_parent = t;
    }
    dispose(): void {
        super.dispose();
        this._figure_element.dispose3d();
        if(this._room_entity)
        {
            this._room_entity.removeFurnitureEntity(this);
        }
        if(this._area_entity)
        {
            this._area_entity.removeFurnitureEntity(this);
        }
    }


    static getOrMakeEntityOfCadRect(rect:ZRect):TFurnitureEntity
    {
        let entity: TFurnitureEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            let figure_element = TFigureElement.getOrMakeFigureElementOfRect(rect);
            entity = new TFurnitureEntity(figure_element);
            entity._rect = rect;
            entity.update();
            rect._attached_elements[KeyEntity] = entity;
            
        }
        return entity;
    }
    static RegisterGenerators()
    {
        TBaseEntity.RegisterPolyEntityGenerator(this.EntityType,this.getOrMakeEntityOfCadRect)
    } 
    setMatchedVisible(visible: boolean) {
        this._matched_visible = visible;
    }

    isMatchedVisible(): boolean {
        return this._matched_visible;
    }

    
    get is_single_furniture() {
        return this._is_single_furniture;
    }

    clone() {
        let _candidate_materials = this._figure_element._candidate_materials;
        let ele = new TFigureElement(this._figure_element.exportJson());
        let entity = new TFurnitureEntity(ele);
        entity.bindFigureEntities();
        if (this.matched_rect) {
            entity.matched_rect = this.matched_rect.clone();
        }
        ele._candidate_materials = _candidate_materials;
        return entity;
    }
    updateRealType() {
        // if(!this._figure_element) return;

        let figure_elements = this.figure_elements;
        let figure_element = figure_elements[figure_elements.length - 1];
        if (!figure_element) return;
        if (compareNames([figure_element.sub_category], ["饰品", "墙饰", "五金电器"])) {
            figure_element._is_decoration = true;
            this.realType = "Decoration";
        }
        if (compareNames([figure_element.sub_category], ["相机", "人物"])) {
            // figure_element._is_decoration = true;
            figure_element._decoration_type = "ViewCamera";
            this.realType = "ViewCamera";
        }
        if (figure_element.sub_category === "Default") {
            if (this.rect.max_hh < 300) {
                this._figure_element._is_decoration = true;
            }
        }

        if (figure_element._is_decoration) {
            if (figure_element._decoration_type === "Electricity") {
                this.realType = "Electricity"; // 电器模块
            }
            else {
                this.realType = "Decoration";

            }
        }
        else if (compareNames([figure_element.category, figure_element.sub_category], ["主灯", "筒灯", "射灯"])) {
            this.realType = "Lighting";
        }
        else if (compareNames([figure_element.sub_category, figure_element.public_category, figure_element.category], ["柜"])
            && compareNames([figure_element.category], ["床头柜"]) == 0) {
            this.realType = "Cabinet";
        }
        else {
            this.realType = "SoftFurniture";
        }
    }

    updateOnMovement(rect: ZRect, movement: Vector3): void {
        let last_rect = this._rect.clone();
        if (rect != null && this.matched_rect === rect) {
            this._rect.back_center = this.matched_rect.back_center.clone();
            this._rect.updateRect();
        }
        if (this._rect != rect) {
            if (rect != null) {
                this.alignRectToCenter(rect);
            } else {
                this.alignRectToCenterWithMovement(movement);
            }
        }
        if (this.matched_rect && this.matched_rect != rect) {
            if (rect != null) {
                this.alignMatchedRectToCenter(rect);
            } else {
                this.alignMatchedRectToCenterWithMovement(movement);
            }
            // this.figure_element.updateMesh3D();
            // console.info("T_MoveElement.applyTransformByMovement()  Entity-uidN:" + t_entity.uidN + " " + t_entity.figure_element.modelLoc
            //             + " movement:(" + movement.x + "," + movement.y + ")"
            //             + " layout-pos:(" + t_entity.rect.rect_center.x +","+t_entity.rect.rect_center.y + ")"
            //             + " matched-pos:(" + t_entity.matched_rect.rect_center.x +","+t_entity.matched_rect.rect_center.y + ")");
        }
        let decorationElements: TFigureElement[] = this.figure_element.decorationElements;
        if (decorationElements != null) {
            let currentOffset = this._rect.back_center.clone().sub(last_rect.back_center);
            decorationElements.forEach((decoration) => {
                let decoration_origin_center = this.getOriginRectData(decoration.rect)?.rect_center || null;
                if (decoration_origin_center != null) {
                    decoration.rect.rect_center = decoration_origin_center.clone().add(movement);
                } else {
                    decoration.rect.rect_center = decoration.rect.rect_center.clone().add(currentOffset);
                }
                decoration.rect.updateRect();
                if (decoration.matched_rect != decoration.rect && decoration.matched_rect != null) {
                    if (decoration_origin_center != null) {
                        decoration.matched_rect.rect_center = decoration_origin_center.clone().add(movement);
                    } else {
                        decoration.matched_rect.rect_center = decoration.matched_rect.rect_center.clone().add(currentOffset);
                    }
                    decoration.matched_rect.updateRect();
                }
            });
        }
    }

    public shouldAlignToBackCenter(): boolean {
        return compareNames([this.figure_element.category, this.figure_element.sub_category, this.figure_element.modelLoc], TMaterialMatchingConfigs._backto_wall_modelLoc_list) == 1;
    }

    public alignRectToCenter(another_rect: ZRect) {
        if (this.shouldAlignToBackCenter()) {
            this.alignRectToBackCenter(another_rect);
        } else {
            this.alignRectToRectCenter(another_rect);
        }
    }

    public alignRectToBackCenter(another_rect: ZRect) {
        this._rect.back_center = another_rect.back_center.clone();
        this._rect.updateRect();
    }

    public alignRectToRectCenter(another_rect: ZRect) {
        this._rect.rect_center = another_rect.rect_center.clone();
        this._rect.updateRect();
    }

    public alignRectToCenterWithMovement(movement: Vector3) {

        // 移动的时候不需要用backCenter对齐

        // if (this.shouldAlignToBackCenter()) {
        //     this.alignRectToBackCenterWithMovement(movement);
        // } else {
        //     this.alignRectToRectCenterWithMovement(movement);
        // }
        this.alignRectToRectCenterWithMovement(movement);
    }

    public alignRectToBackCenterWithMovement(movement: Vector3) {
        let origin_center = this.getOriginRectData(this._rect)?.back_center || null;
        this._rect.back_center = origin_center.clone().add(movement);
        this._rect.updateRect();
    }

    public alignRectToRectCenterWithMovement(movement: Vector3) {
        let origin_center = this.getOriginRectData(this._rect)?.rect_center || null;
        if (origin_center != null) {
            this._rect.rect_center = origin_center.clone().add(movement);
            this._rect.updateRect();
        }
    }

    public alignMatchedRectToCenter(another_rect: ZRect) {
        if (this.matched_rect == null) return;
        if (this.shouldAlignToBackCenter()) {
            this.alignMatchedRectToBackCenter(another_rect);
        } else {
            this.alignMatchedRectToRectCenter(another_rect);
        }
    }

    public alignMatchedRectToBackCenter(another_rect: ZRect) {
        this.matched_rect.back_center = another_rect.back_center.clone();
        this.matched_rect.updateRect();
    }

    public alignMatchedRectToRectCenter(another_rect: ZRect) {
        this.matched_rect.rect_center = another_rect.rect_center.clone();
        this.matched_rect.updateRect();
    }

    public alignMatchedRectToCenterWithMovement(movement: Vector3) {
        // 移动的时候不需要用backCenter对齐

        // if (this.shouldAlignToBackCenter()) {
        //     this.alignMatchedRectToBackCenterWithMovement(movement);
        // } else {
        //     this.alignMatchedRectToRectCenterWithMovement(movement);
        // }
        this.alignMatchedRectToRectCenterWithMovement(movement);
    }

    private getOriginRectData(rect: ZRect) {
        return rect._attached_elements['_previous_rect'] as ZRect || null;
    }

    public alignMatchedRectToBackCenterWithMovement(movement: Vector3) {
        let origin_center = this.getOriginRectData(this.matched_rect)?.back_center || null;
        this.matched_rect.back_center = origin_center.clone().add(movement);
        this.matched_rect.updateRect();
    }

    public alignMatchedRectToRectCenterWithMovement(movement: Vector3) {
        let origin_center = this.getOriginRectData(this.matched_rect)?.rect_center || null;
        this.matched_rect.rect_center = origin_center.clone().add(movement);
        this.matched_rect.updateRect();
    }



    updateMesh3D() {
        if (compareNames([this.category], TMaterialMatchingConfigs._ignoreCategories)) return null;
        if (!this._mesh3d) {
            this._mesh3d = new FurnitureObject3D();
            this._mesh3d.name = MeshName.Furniture;
        }
        for (let child of this._mesh3d.children) {
            this._mesh3d.remove(child);
        }
        this.figure_element.updateMesh3D();
        // this._mesh3d.add(this.figure_element._solid_mesh3D || this.figure_element._simple_mesh3D);
        // this._mesh3d.add(this.figure_element._simple_mesh3D);

        if (this.figure_element._solid_mesh3D) {
            this._mesh3d.add(this.figure_element._solid_mesh3D);
        }
        else if (this.figure_element._simple_mesh3D) {
            this._mesh3d.add(this.figure_element._simple_mesh3D);
        }

        this._mesh3d.userData[UserDataKey.EntityOfMesh] = this;
        this._mesh3d.userData._entityType = this.type;

        if(this.figure_element.decorationElements && this.figure_element.decorationElements.length > 0)
        {
            this.figure_element.decorationElements.forEach((decoration_ele)=>{
                decoration_ele.updateMesh3D();
                if(decoration_ele._solid_mesh3D)
                {
                    this._mesh3d.add(decoration_ele._solid_mesh3D);
                }
                else if(decoration_ele._simple_mesh3D)
                {
                    this._mesh3d.add(decoration_ele._simple_mesh3D);
                }
            })
        }

        return this._mesh3d;
    }
    bindEntity(): void {
        super.bindEntity();
    }

    bindFigureEntities() {
        this.bindEntity();
        this.update();
        this.initProperties();
    }
    setRectProperties(): void {
        super.setRectProperties();
        this._rect.ex_prop.label = this.category;
        this.rect._attached_elements[TFigureElement.EntityName] = this._figure_element;
    }
    update(): void {
        super.update();

        let figure_elements = this.figure_elements;
        let fig_element = figure_elements[figure_elements.length - 1];

        this._drawing_order = fig_element?.default_drawing_order || 0;

        this.updateRealType();
    }

    get figure_element() {
        return this._figure_element;
    }

    get figure_elements() {
        return [this._figure_element];
    }
    get disassembled_figure_elements(): TFigureElement[] {
        return [this.figure_element];
    }
    get displayed_figure_elements(): TFigureElement[] {
        return [this.figure_element];
    }
    get category() {
        return this._figure_element.category;
    }

    set category(str: string) {
        this._figure_element.category = str;
        this._rect.ex_prop.label = str;
    }
    /**
     *  对应sub_category
     */
    get label() {
        return this._figure_element.sub_category;
    }

    set label(str: string) {
        this._figure_element.sub_category = str;
        let data = g_FigureImagePaths[str];
        if (data) {
            this._figure_element.category = data.modelLoc || str;
            this._figure_element.public_category = data.public_category || data.modelLoc;
        }
        else {
            this._figure_element.category = str;
            this._figure_element.public_category = str;
        }
        this._rect.ex_prop.label = str;
        this.updateRealType();
    }

    get option_length() {
        return this.length;
    }

    set option_length(h: number) {
        this.length = h;
    }
    get public_category() {
        return this._figure_element.public_category || this._figure_element.sub_category;
    }

    set public_category(str: string) {
        this._figure_element.public_category = str;
        this._figure_element.sub_category = str;
    }
    get closeDirection() {
        return this._figure_element.params.close_direction;
    }

    set closeDirection(value: string) {
        this._figure_element.params.close_direction = value as any;
    }

    get offLandRule() {
        return this._figure_element.params.off_land_rule;
    }

    set offLandRule(value: string) {
        this._figure_element.params.off_land_rule = value;
    }

    get size() {
        return `${Math.round(this._figure_element.params.depth)} * ${Math.round(this._figure_element.params.length)} * ${Math.round(this._figure_element.params.height)}`
    }

    get material_name() {
        return LayoutAI_App.t(this._figure_element.material_name) || LayoutAI_App.t(this._figure_element.sub_category);
    }
    get imageUrl() {
        return this._figure_element.image_path;
    }

    set material_name(s: string) {
        this._figure_element.material_name = s;
    }

    get height() {
        return this._figure_element.height || 10;
    }

    get maxHeight(): number {
        if (this.roomEntity != null) {
            if (this._figure_element != null && FigureCategoryManager.isCustomCabinet(this._figure_element)) {
                if (this.roomEntity.is_auto_ceiling) {
                    return this.roomEntity.storey_height - this.roomEntity.floor_thickness;
                } else {
                    return this.roomEntity.storey_height - this.roomEntity.ceiling_height - this.roomEntity.floor_thickness;
                }
            } else {
                return this.roomEntity.storey_height;
            }
        } else {
            return super.maxHeight;
        }
    }

    get area() {
        return (0.000001 * this.length * this.height).toFixed(2);
    }

    private _isConfirming: boolean = false;
    public isNeedConfirming(h: number): boolean {
        if (this._room_entity && (h + this._room_entity.ceiling_height + this._room_entity.floor_thickness) > this._room_entity.storey_height
            && !this._room_entity.is_auto_ceiling) {
            if (!this._isConfirming && this._figure_element && FigureCategoryManager.isCustomCabinet(this._figure_element)) {
                return true;
            }
        }
        return false;
    }

    public setIsConfirming(value: boolean) {
        this._isConfirming = value;
    }

    set height(h: number) {
        if (this._room_entity && (h + this._room_entity.ceiling_height + this._room_entity.floor_thickness) > this._room_entity.storey_height
            && !this._room_entity.is_auto_ceiling) {
            this._figure_element.height = this.maxHeight;
            if (this._figure_element && FigureCategoryManager.isCustomCabinet(this._figure_element)) {
                this.initProperties();
                this.forcePanelUpdate(true);
            }
        } else {
            this._figure_element.height = h;
        }
    }

    get angle() {

        let vector = this._rect.nor;
        let angleInRadians = Math.atan2(vector.y, vector.x);
        let angleInDegrees = angleInRadians * (180 / Math.PI);
        if (angleInDegrees < 0) {
            angleInDegrees = (angleInDegrees + 360) % 360;
        }
        return angleInDegrees;
    }
    set angle(n: number) {
        let angleInRadians = n * (Math.PI / 180);
        let x = 1 * Math.cos(angleInRadians);
        let y = 1 * Math.sin(angleInRadians);
        let r_center = this._rect.rect_center;
        this._rect.nor = new Vector3(x, y, 0);
        this._rect.rect_center = r_center;
    }

    get pitch() {
        return this._rect.rotation_x * (180 / Math.PI);
    }
    set pitch(t: number) {
        let angleInRadians = t * (Math.PI / 180);
        this._rect.rotation_x = angleInRadians;
    }

    get fov() {
        return this._rect.fov * (180 / Math.PI);
    }

    set fov(f: number) {
        let angleInRadians = f * (Math.PI / 180);
        this._rect.fov = angleInRadians;
    }

    get corner_depth() {
        return Math.round(this._rect.cornerDepth);
    }
    set corner_depth(cd: number) {
        this._rect.cornerDepth = cd;
    }

    get corner_width() {
        return Math.round(this._rect.cornerWidth);
    }
    set corner_width(cw: number) {
        this._rect.cornerWidth = cw;
    }

    get matched_rect() {
        let rect = this._figure_element.matched_rect;
        if (rect) {
            if (!rect._attached_elements["Entity"]) {
                TBaseEntity.bindEntityOfPolygon(rect, this);
                TBaseEntity.set_polygon_type(rect, this.type);
                rect.ex_prop.label = this.category;
                rect._attached_elements[TFigureElement.EntityName] = this._figure_element;
            }
        }

        return this._figure_element.matched_rect;
    }

    set matched_rect(rect: ZRect) {
        if (!this._figure_element.matched_rect) {
            this._figure_element.matched_rect = this.rect.clone();
        }
        this._figure_element.matched_rect.copy(rect);

        rect = this._figure_element.matched_rect;
        if (rect) {
            TBaseEntity.bindEntityOfPolygon(rect, this);
            TBaseEntity.set_polygon_type(rect, this.type);
            rect.ex_prop.label = this.category;
            rect._attached_elements[TFigureElement.EntityName] = this._figure_element;
        }
    }

    get locked(): boolean {
        return this._figure_element?.locked;
    }

    set locked(value: boolean) {
        this._figure_element.locked = value;
    }

    getCategoryOptions(): { label: string; value: string }[] {
        let ans: { label: string; value: string }[] = [];
        for (let key in g_FigureImagePaths) {
            if (g_FigureImagePaths[key].isFurniture === false) continue;
            let label = key;
            let value = key;
            ans.push({ label: LayoutAI_App.t(label), value: value });
        }
        return ans;
    }
    getDistanceForMousePosSelection(point: Vector3Like, tol?: number): number {
        // return this.getPointdistanceToBoundary(point);
        let dist = super.getDistanceForMousePosSelection(point, tol);

        let order = this._drawing_order;

        dist = dist / (1.0 + (order || 0) / 1000.0)

        return dist;
    }
    getFilterCategoryOptions() {
        let options: { label: string, value: string, [key: string]: string }[] = [];

        if (compareNames([this.label], ["地柜", "高柜", "吊柜"])) {
            options.push(...TGraphBasicConfigs.MainCabinetsCategories.filter((val) => val.endsWith("柜")).map(val => { return { label: LayoutAI_App.t(val), value: val } }));
        }
        else {
            let target_options = this.getCategoryOptions();
            target_options.sort((a, b) => compareWordFittingCount(this.label, b.value) - compareWordFittingCount(this.label, a.value));


            // target_options.length = Math.min(target_options.length, 10);

            options = target_options.filter((data) => data.value !== this.label);

        }


        return options;


    }

    initProperties(): void {

        super.initProperties();
        this._ui_properties["goods"] = {
            name: "信息组件",
            widget: "infoItem",
            value: {
                name: LayoutAI_App.t(this.material_name),
                size: this.size,
                hideId: false,
                id: 'null',
                imageUrl: this.imageUrl,
                type: 'Furniture',
            },
        }
        this._ui_properties["line"] = {
            name: "分割线",
            widget: "LineItem",
            props: {
                space: 20,
                fullScreen: true
            }
        }

        this._ui_properties["label"] = {
            name: LayoutAI_App.t("图元分类"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.label,
            editable: true,
            props: {
                type: "select",
                options: [{ label: LayoutAI_App.t(this.label), value: this.label }, ...this.getFilterCategoryOptions()],
                optionWidth: 100
            }
        }
        this._ui_properties["material_name"] = {
            name: LayoutAI_App.t("素材名称"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + LayoutAI_App.t(this.material_name),
            editable: false,
            props: {
                type: "input",
                optionWidth: 100
            }
        }
        this._ui_properties["length"] = {
            name: LayoutAI_App.t("宽度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 6000,
            editable: true,
            defaultValue: '' + Math.round(this.length),
            props: {
                type: "number",
                suffix: "mm"
            }
        }
        this._ui_properties["width"] = {
            name: LayoutAI_App.t("深度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 6000,
            editable: true,
            defaultValue: '' + Math.round(this.width),
            props: {
                type: "number",
                suffix: "mm"
            }
        }
        this._ui_properties["angle"] = {
            name: LayoutAI_App.t("角度"),
            widget: "SlideItem",
            type: "number",
            min: 0,
            max: 360,
            editable: true,
            defaultValue: '' + Math.round(this.angle),
            props: {
                type: "number",
                suffix: "°"
            }
        }
        this._ui_properties["area"] = {
            name: LayoutAI_App.t("投影面积"),
            widget: "LabelItem",
            type: "number",
            editable: false,
            disabled: true,
            defaultValue: '' + this.area,
            props: {
                type: "input",
                suffix: "m²"
            }
        }

        this._ui_props_keys = ["goods", "line", "ui_type", "ui_realType", "label", "material_name", "length", "width", "height", "pos_z", "angle"];
        if (this._figure_element.modelLoc === "相机" || this._figure_element.modelLoc === "人物") {
            this._ui_properties["pos_z"] = {
                name: "视角离地高",
                widget: "SlideItem",
                type: "number",
                min: this.roomEntity ? this.roomEntity.floor_thickness : 0,
                max: 2800,
                editable: true,
                defaultValue: this.pos_z,
                props: {
                    type: "number",
                    suffix: "mm"
                }
            };
            this._ui_properties["pitch"] = {
                name: "俯仰角",
                widget: "SlideItem",
                type: "number",
                min: -180,
                max: 0,
                editable: true,
                defaultValue: this.pitch,
                props: {
                    type: "number",
                    suffix: "°"
                }
            };
            this._ui_properties["fov"] = {
                name: "视场角",
                widget: "SlideItem",
                type: "number",
                min: 20,
                max: 120,
                editable: true,
                defaultValue: this.fov,
                props: {
                    type: "number",
                    suffix: "°"
                }
            };
            this._ui_props_keys.push("pitch");
            this._ui_props_keys.push("fov");
        }

        if (this._figure_element.modelLoc.indexOf("L型") > -1) {
            this._ui_properties["corner_depth"] = {
                name: "转角深",
                widget: "SlideItem",
                type: "number",
                min: 0,
                max: 1600,
                editable: true,
                defaultValue: this.corner_depth,
                props: {
                    type: "number",
                    suffix: "mm"
                }
            };
            this._ui_properties["corner_width"] = {
                name: "转角宽",
                widget: "SlideItem",
                type: "number",
                min: 0,
                max: 1600,
                editable: true,
                defaultValue: this.corner_width,
                props: {
                    type: "number",
                    suffix: "mm"
                }
            };
            this._ui_props_keys.push("corner_width");
            this._ui_props_keys.push("corner_depth");
        }

        if (this._figure_element) {
            if (compareNames([this._figure_element.category], ["地柜", "吊柜"])) {
                let prop = this._ui_properties["length"];

                let size_ranges = TSerialSizeRangeDB.GetSizeRangeList(this._figure_element.sub_category);

                // console.log(size_ranges);

                let lengths: number[] = [];

                if (size_ranges && size_ranges.length > 0) {
                    lengths = size_ranges.map((v) => v.size_range.max.x);


                    prop.max = lengths[0] + 600;
                    prop.min = lengths[lengths.length - 1];

                    prop.step = 50;
                }
                this._ui_properties["option_length"] = {
                    name: LayoutAI_App.t("标准宽度"),
                    widget: "LabelItem",
                    type: "number",
                    editable: true,
                    defaultValue: "600",
                    props: {
                        type: "select",
                        options: lengths.map((val) => { return { value: '' + val, label: '' + val } }),
                        optionWidth: 100
                    }
                }
                this._ui_props_keys = ["goods", "line", "ui_type", "ui_realType", "label", "material_name", "length", "option_length", "width", "height", "pos_z", "angle"];
            }

            if (compareNames([this._figure_element.category], ["衣柜", "电视柜", "餐边柜", "玄关柜", "书柜"])) {
                this._ui_props_keys.push("area");
            }
        }

        this._ui_properties["locked"] = {
            name: LayoutAI_App.t("锁定"),
            widget: "LabelItem",
            type: "boolean",
            defaultValue: this.locked
        };
        this._ui_props_keys.push("locked");

        if (LayoutAI_App.IsDebug) {
            this._ui_properties["closeDirection"] = {
                name: "收口侧(仅仿真)",
                widget: "LabelItem",
                type: "string",
                defaultValue: "左右见光",
                props: {
                    type: "select",
                    options: [{ label: "左右见光", value: "左右见光" }, { label: "左收口", value: "左收口" }, { label: "右收口", value: "右收口" }, { label: "左右收口", value: "左右收口" }],
                    optionWidth: 100
                }
            }
            this._ui_properties["offLandRule"] = {
                name: "放置规则(仅仿真)",
                widget: "LabelItem",
                type: "string",
                defaultValue: "落地",
                props: {
                    type: "select",
                    options: [{ label: "落地", value: "落地" }, { label: "离地", value: "离地" }],
                    optionWidth: 100
                }
            }
            this._ui_props_keys.push("closeDirection");
            this._ui_props_keys.push("offLandRule");
        }



        this._bindPropertiesOnChange();
    }

    protected _bindPropertiesOnChange(): void {
        let scope = this;
        for (let key in this._ui_properties) {
            let property = this._ui_properties[key];
            if (property.editable === false) continue;
            property.onChange = (value) => {
                if (key == "label") {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceEntity, { title: value });
                    return;
                }
                let oldValue = (scope as any)[property._key || key];
                (scope as any)[property._key || key] = (property.type == "number") ? (~~value) : value;
                scope.update();
                scope.onEntityPropertyChanged(key, oldValue, value);
            }
        }
    }

    protected onEntityPropertyChanged(propertyName: string, oldValue: any, newValue: any): void {
        if (this.onPanelUpdate) {
            const isCamera: boolean = this.rect.ex_prop.label == "相机" || this.rect.ex_prop.label == "人物";
            if (isCamera && this.onPanelUpdate) {
                this.onPanelUpdate("cameraZ", newValue);
            } else {
                this.onPanelUpdate(propertyName, newValue);
            }
        }
    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {

        if (TBaseEntity.is_deleted(this.rect)) return;

        // 根据自己的业务，显示图元还是显示素材的rect
        if(options.is_selected && options.is_moving)
        {
            // 针对地毯特殊处理
            if(this.category==="地毯")
            {
                return;
            }
        }
        let rect = (LayoutAI_App.instance as TAppManagerBase).layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D ? this.rect : this.matched_rect || this.rect;
        let white_style = painter._style_mapping['white'];
        let black_style = painter._style_mapping['black'];
        painter._context.lineWidth = 2;
        if (options.is_draw_texture) {
            let handler = (LayoutAI_App.instance as TAppManagerBase)._current_handler as BaseModeHandler;
            if (this.is_selected && this._figure_element.default_drawing_order < 1 && handler._is_moving_element) {
                painter._context.globalAlpha = 0.6;
            } else {
                painter._context.globalAlpha = 1;
            }
            // isMaterialMarkAsInvisible控制素材可不可见
            if (!this._figure_element.isMaterialMarkAsInvisible()) {
                this._figure_element.drawPreviewFigure(painter, false);
                // console.info("  TFurnitureEntity.drawEntity() drawPreviewFigure " + this._figure_element.modelLoc);
                if (this._figure_element.decorationElements != null && options.draw_decoration) {
                    this._figure_element.decorationElements.forEach((df) => {
                        if (df.haveMatchedMaterial() && options.is_draw_texture) { 
                            df.drawPreviewFigure(painter, false);
                        }
                    });
                }
            }
        } 
        else if (options.is_draw_outline) {
            this._figure_element.drawOutline(painter, false);
        }
        else if (options.is_draw_figure) {
            if (this.is_selected && this._figure_element.default_drawing_order < 2) {
                painter._context.globalAlpha = 0.8;
            }
            this._figure_element.drawFigure(painter, false);
        } 
        painter._style_mapping['white'] = white_style;
        painter._style_mapping['black'] = black_style;

        if (options.is_selected) {
            painter.strokeStyle = "#147FFA";
            painter.strokePolygons([rect]);

            painter._context.lineWidth = 1;

        }
        if (options.is_hovered && !LayoutAI_App.instance.isMoblie) {
            painter.strokeStyle = "#147FFA";
            painter.strokePolygons([rect]);
            painter._context.lineWidth = 1;
        }
        if (rect._attached_elements.FigureElement.isAbnormalFigure) {
            painter.strokeStyle = "#de7802";
            painter.strokePolygons([rect]);
        }
    }

    public onRoomFloorThicknessChanged() {
        this.figure_elements.forEach((fe) => {
            fe.onRoomFloorThicknessChanged();
        });
    }

    public clearMatchedMaterials() {
        this.figure_elements.forEach((ele) => {
            if (ele.haveMatchedMaterial()) {
                ele.clearMatchedMaterials();
            }
        });
    }

    public cleanDecoration(): void {
        if (this._figure_element == null) {
            return;
        }

        if(this._figure_element.decorationElements) {
            this._figure_element.decorationElements.forEach((ele)=>{
                ele.dispose3d();
            });
            this._figure_element.decorationElements =null;
        }
    }
}


