
import { Z<PERSON>dge, ZPolygon } from "@layoutai/z_polygon";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, TRoomEdgeProp } from "../IRoomInterface";
import {I_TRoom, TRoomShape } from "../TRoomShape";
import { TFeatureShape } from "./TFeatureShape";
import { ZRect } from "@layoutai/z_polygon";

export class T_R_Shape extends TFeatureShape
{
    rect : ZRect;
    constructor()
    {
        super();
        this.shape_code = "R";
        this.rect = new ZRect(1,1);
        this._order = 1;

    }

    get width()
    {
        return this.rect._w;
    }

    get height()
    {
        return this.rect._h;
    }    


    findFeatureShape(room: I_TRoom): number {

       
        let target_poly : ZPolygon = null;
        let target_shape : TRoomShape = null;
        this._poly = null;
        this._room_shapes = [];
        this._room = room;
        for(let shape of room.shape_list)
        {
            if(shape._children.length>0) continue;
            // if(shape._poly.vertices.length != 4) continue;

            if(!target_shape || shape._area > target_shape._area)
            {
                target_poly = shape._poly;
                target_shape = shape;
            }
        }

        if(target_poly)
        {
            this._poly = target_poly;
            this._room_shapes.push(target_shape);
            return this.getFitAreaScore();
        }
        return 0;
    }
    _resortPolyEdges()
    {
        if(!this._contours) return;
        for(let poly of this._contours._polygons)
        {
            let t_edges : ZEdge[] = [];

            for(let edge of poly.edges)
            {
                t_edges.push(edge);
            }
    
            t_edges.sort((a,b)=>{
                if(Math.abs(a.length - b.length) < AlignWallDist / 2.)
                {
                    let diff = b.getProperty(TRoomEdgeProp.WindowWeight) - a.getProperty(TRoomEdgeProp.WindowWeight);
                    if(Math.abs(diff) > 0.1)
                    {
                        return diff;
                    }
                }
                return a.length - b.length;
     
            });            
            // TsAI_app.log(t_edges);
            let target_edge = t_edges[0];
    
            if(target_edge.next_edge.getProperty(TRoomEdgeProp.WindowWeight) > target_edge.prev_edge.getProperty(TRoomEdgeProp.WindowWeight) - 0.01)
            {
                let next_edge = target_edge.next_edge;
                let id = poly.edges.indexOf(next_edge);
    
                let p_size = poly.edges.length;
    
                let res_edges : ZEdge[] = [];
                for(let i=0; i < p_size; i++)
                {
                    res_edges.push(poly.edges[(id+i)%p_size]);
                }
    
                poly.edges.length = 0;  
                for(let edge of res_edges)
                {
                    poly.edges.push(edge);
                }
                poly._updateVerticesByEdgesList();
                poly.computeZNor();
            }
            else{
                let next_edge = target_edge.prev_edge;
                let id = poly.edges.indexOf(next_edge);
    
                let p_size = poly.edges.length;
    
                let res_edges : ZEdge[] = [];
                for(let i=0; i < p_size; i++)
                {
                    res_edges.push(poly.edges[(id-i+p_size)%p_size]);
                }
    
                poly.edges.length = 0;  
                for(let edge of res_edges)
                {
                    let tmp = edge.v0;
                    edge.v0 = edge.v1;
                    edge.v1 = tmp;

                    poly.edges.push(edge);
                }
                poly._updateEdgeNeighbors();
                poly._updateVerticesByEdgesList();
                poly.computeZNor();
            }




            this.rect = null;
            
        }


    }
    _updateShapeEdgeProp(): void {
        super._updateShapeEdgeProp();


        this._resortPolyEdges();

    }
}