import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { T_RotateElement } from "./T_RotateElement";
import { I_SelectedTarget } from "@/Apps/LayoutAI/Layout/TEntitySelector/TEntitySelector";
import { checkIsMobile } from '@/config';
import { ZRect } from "@layoutai/z_polygon";
import { Vector3, Vector3Like } from "three";

/**
 * 旋转箭头控制器
 */
export class T_ArrowRotateElement extends T_RotateElement {
    private _offset: number = 210;
    checkEditRect(pos: Vector3Like, _p_sc: number): boolean {
        if (!this._rotate_rect) return false;
        if (this.isTargetRectTypeValid()) {
            return this._rotate_rect.containsPoint(new Vector3().copy(pos));
        } else {
            return false;
        }
    }  
    get visible() {
        return this._target_rect && this._visible ;
    }

    drawCanvas(painter: TPainter): void {
        if (!this._selected_target) return;
        if (!this._selected_target.selected_rect) return;
        if (!this.isTargetRectTypeValid()) return;
        
        const arrowRect = new ZRect(1, 1);
        arrowRect._w = painter._p_sc > 0.4 ? 400 : 150 / painter._p_sc;
        arrowRect._h = painter._p_sc > 0.4 ? 125 : 60 / painter._p_sc;

        arrowRect._w *= 0.6;
        arrowRect._h *= 0.6;
        // arrowRect._w = 110 / painter._p_sc;
        // arrowRect._h =  40 / painter._p_sc;
        let t_edge = null;
        for (const edge of this._selected_target.selected_rect.edges) {
            if (edge._nor.dot(this._selected_target.selected_rect.nor) < 0.1) continue;
            t_edge = edge;
        }
        if (!t_edge) return;
        arrowRect.back_center = this._target_rect.unproject({x:this._dir_val * (this._target_rect._w/2+this._offset),y:this._nor_val * (this._target_rect.h/2+this._offset)});
        arrowRect.nor = arrowRect.back_center.clone().sub(this._target_rect.rect_center);
        this._rotate_rect = arrowRect.clone();
        painter.fillStyle = "#fe0000";
        painter.strokeStyle = "#fe0000";
        // painter.fillPolygon(this._rotate_rect)
        painter.drawFigureRect(arrowRect, '箭头', ['箭头']);
        this._rotate_rect.updateRect();
        if (this._selected_target.hover_transform_element && this._selected_target.hover_transform_element.element_name === this._element_name) {
            painter.drawFigureRect(this._rotate_rect, '箭头hover', ['箭头hover']);
        }
    }
}