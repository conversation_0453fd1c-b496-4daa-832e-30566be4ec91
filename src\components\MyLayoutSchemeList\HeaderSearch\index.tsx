import { Dropdown, Space } from '@svg/antd';
import type { MenuProps } from '@svg/antd';
import React, { useState } from 'react';
import IconFont from '@/components/IconFont/iconFont';
import useStyles from "./style"

const timeItems: MenuProps['items'] = [
  {
    key: 'all',
    label: `全部`,
  },
  {
    key: 'range1',
    label: `近一个月`,
  },
  {
    key: 'range3',
    label: `近三个月`,
  },
  {
    key: 'range6',
    label: `近半年`,
  },
];
const typeItems: MenuProps['items'] = [
  {
    key: 'update_date desc',
    label: `修改时间`,
  },
  {
    key: 'create_date desc',
    label: `创建时间`,
  },
];

export type HeaderSearchProps = {
  updateData: (data: string) => void;
  selectedSort: (selectedType: string) => void;
  selectedTime: (selectedTime: string) => void;
};

const HeaderSearch: React.FC<HeaderSearchProps> = (props) => {
  const { styles } = useStyles();
  const [showType, setShowType] = useState<'card' | 'list'>('card');
  const [selectedTimeLabel, setSelectedTimeLabel] = useState('全部');
  const [selectedTypeLabel, setSelectedTypeLabel] = useState('修改时间');

  // const { initialState } = useModel('@@initialState');

  // if (!initialState || !initialState.settings) {
  //   return null;
  // }

  // const { currentUser } = initialState;

  const timeClick: MenuProps['onClick'] = ({ key, domEvent }) => {
    // if (currentUser && currentUser.username) {
      const label = (domEvent.target as HTMLElement).innerText;
      setSelectedTimeLabel(label);
      props.selectedTime(key);
    // } else {
    //   if (checkIsMobile()) {
    //     toLogin();
    //   } else {
    //     getLoginPop()
    //   }

    // }
  };
  const typeClick: MenuProps['onClick'] = ({ key, domEvent }) => {
    // if (currentUser && currentUser.username) {
      const label = (domEvent.target as HTMLElement).innerText;
      setSelectedTypeLabel(label);
      props.selectedSort(key);
    // } else {
    //   if (checkIsMobile()) {
    //     toLogin();
    //   } else {
    //     getLoginPop()
    //   }
    // }
  };

  const setCardView = () => {
    // if (currentUser && currentUser.username) {
      setShowType('card');
      props.updateData('card');
    // } else {
    //   if (checkIsMobile()) {
    //     toLogin();
    //   } else {
    //     getLoginPop()
    //   }
    // }
  };

  const setListView = () => {
    // if (currentUser && currentUser.username) {
      setShowType('list');
      props.updateData('list');
    // } else {
    //   if (checkIsMobile()) {
    //     toLogin();
    //   } else {
    //     getLoginPop()
    //   }
    // }
  };

  return (
    <div className={styles.headerSearch}>
      <Dropdown 
        menu={{ items: timeItems }} 
        className={styles.selectTime}
        trigger={['click']}
        overlayStyle={{zIndex: 10000}}
      >
        <a onClick={(e) => e.preventDefault()}>
          <Space size={4}>
            {selectedTimeLabel}
            <IconFont type="icon-a-fangxiangxia"/>
          </Space>
        </a>
      </Dropdown>
      <Dropdown 
        menu={{ items: typeItems, onClick: typeClick }} 
        className={styles.selectType}
        trigger={['click']}
        overlayStyle={{zIndex: 10000}}
      >
        <a onClick={(e) => e.preventDefault()}>
          <Space size={4}>
            {selectedTypeLabel}
            <IconFont type="icon-a-fangxiangxia"/>
          </Space>
        </a>
      </Dropdown>
      <div style={{ borderLeft: '1px solid #ccc', height: '20px', margin: '0 12px' }}></div>
      <div className={styles.showSelect}>
        <div
          onClick={setCardView}
          style={{
            cursor: 'pointer',
            backgroundColor: showType === 'card' ? '#FFFFFF' : 'transparent',
            padding: '4px 12px',
            borderRadius: '4px',
          }}
        >
          <IconFont type="icon-kapianmoshi"/>
        </div>
        <div
          onClick={setListView}
          style={{
            cursor: 'pointer',
            backgroundColor: showType === 'list' ? '#FFFFFF' : 'transparent',
            padding: '4px 12px',
            borderRadius: '4px',
          }}
        >
          <IconFont type="icon-UnorderedListOutlined" />
        </div>
      </div>
    </div>
  );
};

export default HeaderSearch;
