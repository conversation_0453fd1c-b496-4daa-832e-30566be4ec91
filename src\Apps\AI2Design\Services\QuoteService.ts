import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom"
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter"
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample"
import { compareNames } from "@layoutai/z_polygon"
import { magiccubeRequest } from "@/utils"
import { deflate_to_base64_str } from "@/Apps/LayoutAI/Utils/xml_utils";

export interface I_QuoteCabinetData
{
  type: "收纳柜"|"地柜"|"吊柜",
  name: string,
  /**
   *  柜子UUID
   */
  uid: string,
  /**
   *  素材ID
   */
  materialId: string,
  width: number,
  depth: number,
  height: number

}
export interface I_QuoteRoomData
{
    roomName: string,
    /**
     *  房间UUID
     */
    roomUID: string,
    /**
     *  层高
     */
    storeyLevel: number,
    /**
     *  周长
     */
    perimeter: number,
    area: number,
    CH: string,
    cabinetList: I_QuoteCabinetData[],

    

  }
export interface I_QuoteContent
{
    /**
     *  整屋面积
     */
    wholeArea: number,
    roomList: I_QuoteRoomData[],
    schemeInfo: {
  
      /**
       *  方案名称
       */
      schemeName: string,
  
      /**
       *  客户名称
       */
      userName: string,
  
      /**
       *  客户电话
       */
      userPhone: string,
  
      /**
       *  所在城市
       */
      cityName: string,
      modelName: string,
      buildName: string,
      designerName: string,
      designerPhone: string
  
    },
  
    shopingInfo: {
  
      /**
       *  门店名称
       */
      shopName: string,
  
      shopTel: string,
  
      shopUser: string,
  
      shopMobie: string,
  
      shopProvince: string,
  
      shopCity: string,
  
      shopDistrict: string,
  
      shopAddress: string
  
    }
  
  }

export const QuoteUrl = "https://magiccube-gateway.3vjia.com/quoteweb-wrap/web/calculation/series/evaluate";
export interface I_QuoteQueryData
{
    schemeId : string;
    seriesId : string;
    seriesName : string;
    sourceContent : I_QuoteContent;
    /**
     *  1-excel; 0-报价结果json
     */
    outputType : number;
}
export class QuoteService
{
    private static _instance : QuoteService = null;
    constructor()
    {

    }

    static  get instance()
    {
        if(!QuoteService._instance)
        {
            QuoteService._instance = new QuoteService();
        }
        return QuoteService._instance;
    }

    private initQuoteContent() : I_QuoteContent
    {
        return {
            wholeArea: 0,
            roomList: [],
            schemeInfo: {
          
              "schemeName": "",
              "userName": "",
          
              "userPhone": "",
          
              "cityName": "",
          
              "modelName": "",
          
              "buildName": "",
          
              "designerName": "李星亮",
          
              "designerPhone": "18188971095"
          
            },
            shopingInfo: {
          
              "shopName": "三维家微官网261",
          
              "shopTel": "",
          
              "shopUser": "",
          
              "shopMobie": "",
          
              "shopProvince": "全国",
          
              "shopCity": "",
          
              "shopDistrict": "",
          
              "shopAddress": ""
          
            }
          
          }
    }
    private initQuoteQueryData() :I_QuoteQueryData
    {
        return {
            schemeId : "",
            seriesId : "",
            seriesName : "",
            sourceContent : this.initQuoteContent(),
            outputType : 0
        }
    }
    makeQuoteData(container:TLayoutEntityContainer, TSeriesSample: TSeriesSample)
    {
        let quote_data = this.initQuoteQueryData();

        let quote_content = quote_data.sourceContent;


        let area_num = 0;

        let rooms = [...container._rooms];

        rooms.sort((a,b)=>{
          return b.room_shape._area - a.room_shape._area;          
        });
        
        if(!rooms[0]) return null;
        let serial_info = rooms[0]._series_sample_info || TSeriesSample;
        quote_data.seriesId = ''+serial_info?.seriesKgId;
        quote_data.seriesName = serial_info.seriesName;
        quote_data.schemeId = container._layout_scheme_id;


        
        for(let room of rooms)
        {
            area_num += room.room_shape._area;
            quote_content.roomList.push(this.makeQuoteDataOfRoom(room))

        }

        quote_content.wholeArea = area_num;


        return quote_content;

    }


    makeQuoteDataOfRoom(room:TRoom):I_QuoteRoomData
    {
       let room_data : I_QuoteRoomData = {
          roomName : room.roomname,
          roomUID : room.uuid,
          storeyLevel: 2800,
          perimeter: 0,
          area: room.room_shape._area,
          CH:'',
          cabinetList:[]
       };

       let len = 0;       
       room.room_shape._poly.edges.forEach((edge)=>len+=edge.length);
       room_data.perimeter = Math.round(len / 1000 *100)/100;

       for(let ele of room._furniture_list)
       {
          let matched_material = ele._matched_material;
          if(!matched_material) continue;
          
          let cabinet_data : I_QuoteCabinetData = {
              uid : ele._entity_uuid,
              type:"收纳柜",
              materialId : matched_material.modelId,
              name : matched_material.name,
              width : matched_material.targetSize?.length,
              depth : matched_material.targetSize?.width,
              height : matched_material.targetSize?.height
          }
          if(compareNames([ele.category],["地柜"]))
          {
            cabinet_data.type = "地柜";
          }
          else if(compareNames([ele.category],["吊柜"]))
          {
            cabinet_data.type = "吊柜";
          }
          else if(ele.category.endsWith("柜"))
          {
            cabinet_data.type = "收纳柜";
          }
          else{
           continue;
          }
          room_data.cabinetList.push(cabinet_data);
       }

       return room_data;
    }
    
    public async saveCabinetModelJson(schemeId: string, cabinetModelJsonMap: Map<string, string>) {
      const cabinetIdJsonArray = Array.from(cabinetModelJsonMap.entries()).map(([cabinetId, layoutJsonData]) => ({ id : cabinetId, content: deflate_to_base64_str(layoutJsonData)}));
      const bodyData = {
        schemeId: schemeId,
        cabinetXmlContents: cabinetIdJsonArray,
      }
      const res = await magiccubeRequest({
          method: 'post',
          url: `/quoteweb-wrap/web/layout/quote_xml/save`,
          data: bodyData,
          timeout: 30000,
      });
      return res?.data || null;
    }
}