"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[3076],{25617:function(n,e,t){t.d(e,{A:function(){return ln}});var r,i=t(13274),o=t(41594),a=t.n(o),l=t(33100),s=t(32293),c=t(97500),u=t.n(c),d=t(79874);var f=(0,d.rU)((function(n){var e,t,i=n.token,o=n.css,a=n.prefixCls,l=n.cx,s="".concat(a,"-menu");return{normal:l(s,o(r||(e=["\n        &."," {\n          .ant-menu-submenu-selected {\n            & > .ant-menu-submenu-title {\n              color: ",";\n            }\n          }\n        }\n      "],t||(t=e.slice(0)),r=Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))),s,i.colorPrimary))}}));function p(n){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},p(n)}var h=["className"];function m(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,r)}return t}function x(n,e,t){var r;return r=function(n,e){if("object"!=p(n)||!n)return n;var t=n[Symbol.toPrimitive];if(void 0!==t){var r=t.call(n,e||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(n)}(e,"string"),(e="symbol"==p(r)?r:String(r))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function y(n,e){if(null==n)return{};var t,r,i=function(n,e){if(null==n)return{};var t,r,i={},o=Object.keys(n);for(r=0;r<o.length;r++)t=o[r],e.indexOf(t)>=0||(i[t]=n[t]);return i}(n,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)t=o[r],e.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(n,t)&&(i[t]=n[t])}return i}var b=function(n){var e=n.className,t=y(n,h),r=f().styles;return(0,i.jsx)(s.A,function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?m(Object(t),!0).forEach((function(e){x(n,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):m(Object(t)).forEach((function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))}))}return n}({className:u()(r.normal,e)},t))},v=b,g=t(71364),j=t(36134);function w(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function S(){var n=w(["\n      display: flex;\n      flex-direction: column;\n      height: 100%;   \n      z-index: 999;\n      /* position: fixed; */\n      background: #fff;\n      top: 0;\n      width: 100%;\n      padding: 10px;\n      overflow: hidden;\n      .ant-menu\n      {\n        border-inline-end: none !important;\n      }\n      .ant-menu-item-selected\n      {\n        background: #f6f7f9;\n        color: #000;\n      }\n      .ant-menu-item-group-title\n      {\n        padding: 8px 28px;\n      }\n    "]);return S=function(){return n},n}function A(){var n=w(["\n      height: 55px;\n      display: flex;\n      padding-bottom: 10px;\n      justify-content: space-between;\n      align-items: center;\n      border-bottom: 1px solid var(--background-color-divider-regular, rgba(0, 0, 0, .08));\n      .leftMenu\n      {\n        img{\n          width: 150px;\n          height: 40px;\n        }\n      }\n      .rightMenu\n      {\n        display: flex;\n        button{\n          margin-right: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n      }\n    "]);return A=function(){return n},n}function _(){var n=w(["\n      display: flex;\n    "]);return _=function(){return n},n}function k(){var n=w(["\n      width: 14%;\n      position: relative;\n      @media screen and (max-height: 768px){\n        width: 18%;\n      }\n    "]);return k=function(){return n},n}function C(){var n=w(["\n      width: 86%;\n      padding: 0 20px;\n      height: 680px;\n      @media screen and (max-height: 924px){\n        height: 698px;\n        min-height: 698px;\n      }\n      @media screen and (max-height: 868px){\n        height: 632px;\n        min-height: 632px;\n      }\n      @media screen and (max-height: 768px){\n        height: 520px;\n        min-height: 520px;\n      }\n      @media screen and (max-height: 635px){\n        height: 490px;\n        min-height: 490px;\n      }\n      .title\n      {\n        font-size: 20px;\n        width: 100%;\n        font-weight: 600;\n        padding:0 20px ;\n        margin: 20px 0px;\n      }\n      @media screen and (max-height: 768px){\n        .title{\n          font-size: 16px;\n          margin: 10px 0px;\n        }\n      }\n      .mobile\n      {\n        .video{\n          width: 45% !important; \n          height: 130px !important;\n          object-fit: cover;\n          border-radius: 8px;\n        }\n      }\n      .advertise\n      {\n        width: 100%;\n        /* height: 200px; */\n        /* padding: 0 100px; */\n        display: flex;\n        justify-content: space-between;\n        border-radius: 8px;\n        margin: 20px 0;\n        .desc\n        {\n          font-size: 20px;\n          color: #666;\n          font-weight: 600;\n        }\n        .video{\n          object-fit: cover;\n          width: 100%;\n          /* margin-right: 40px; */\n          max-height: 380px !important;\n        }\n        img{\n          object-fit: cover;\n          width: 100%;\n        }\n      }\n      .designList\n      {\n        display: flex;\n        margin-top: 10px;\n        margin-bottom: 20px !important;\n        .item\n        {\n          text-align: center;\n          width: 25%;\n          position: relative;\n          display: flex;\n          /* flex-direction: column; */\n          justify-content: center;\n          align-items: center;\n          height: 110px;\n          border-radius: var(--border-radius-medium, 6px);\n          transition: cubic-bezier(.2,0,0,1) .3s;\n          border-radius: 8px;\n          margin: 0 1%;\n          padding: 10px 0px;\n          background-color: #f6f7f9;\n        }\n        @media screen and (max-height: 768px){\n          .item{\n              height: 80px;\n              .icon\n              {\n                height: 40px;\n              }\n            }\n          }\n        .item:hover\n        {\n          color: currentcolor;\n          background: #e9e9ea;\n          transform: translateY(-2px);\n        }\n          cursor: pointer;\n          .icon\n          {\n            width: 100%;\n            height: 60px;\n          }\n          flex: 1;\n          .iconTitle\n          {\n            font-size: 16px;\n            width: 100%;\n            font-weight: 600;\n            margin: 20px 0px;\n          }\n        }\n      }\n      .hxList\n      {\n        display: flex;\n        justify-content: space-between;\n      }\n      .mobile\n      {\n        .hx{\n          .item\n          {\n            width: 150px !important;\n            height: 150px !important;\n          }\n        }\n      }\n      .hx\n      {\n        display: flex;\n        .item\n        {\n          transition: all .3s;\n          width: 50%;\n          position: relative;\n          margin-right: 20px;\n          img\n          {\n            width: 100%;\n            height: 100%;\n          }\n          .button\n          {\n            position: absolute;\n            bottom: 30px;\n            left: 50%;\n            transform: translateX(-50%);\n            display: none;\n          }\n        }\n        .item:hover\n        {\n          .button\n          {\n            display: block;\n          }\n        }\n\n      }\n    "]);return C=function(){return n},n}function I(){var n=w(["\n      background-color: #fff; \n      width: 200px;\n      height: 100%; \n      padding: 10px; \n      text-align: center; \n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    "]);return I=function(){return n},n}function O(){var n=w(["\n      display: flex;\n      align-items: center;\n      img{\n          margin-right: 10px;\n      }\n    "]);return O=function(){return n},n}function N(){var n=w(["\n      height: 1px;\n      width: 100%;\n      background-color: #e6e6e6;\n      margin: 10px 0;\n    "]);return N=function(){return n},n}function P(){var n=w(["\n      position: relative;\n      height: 32px;\n      padding: 0 12px;\n      display: -webkit-box;\n      display: -webkit-flex;\n      display: -ms-flexbox;\n      display: flex;\n      -webkit-align-items: center;\n      -webkit-box-align: center;\n      -ms-flex-align: center;\n      align-items: center;\n      cursor: pointer;\n      margin: 2px 0;  \n      :hover{\n        background-color: #f2f3f5;\n      }\n      img{\n        width: 30px;\n        height: 30px;\n        margin-right: 10px;\n      }\n    "]);return P=function(){return n},n}function D(){var n=w(["\n      margin-top: 10px;\n    "]);return D=function(){return n},n}function E(){var n=w(["\n      position: absolute;\n      bottom: 5%;\n      width: 100%;\n      .ant-progress-status-success .ant-progress-bg\n      {\n        background-color: #f85050 !important;\n      }\n    "]);return E=function(){return n},n}var T=(0,d.rU)((function(n){var e=n.css;return{container:e(S()),top:e(A()),middle:e(_()),left:e(k()),center:e(C()),loginContainer:e(I()),topLogin:e(O()),line:e(N()),item:e(P()),menu:e(D()),progress:e(E())}})),z=t(9003),B=t(27347),L=t(98612),U=t(15696),F=t(85783),M=t(27164),R=t(5711),W=t(93720),H=t(46909),V=t(66192),K=t(41980),$=t(84872),G=t(23825),Z=t(88934),Q=t(50316);function X(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Y(n,e,t,r,i,o,a){try{var l=n[o](a),s=l.value}catch(n){return void t(n)}l.done?e(s):Promise.resolve(s).then(r,i)}function q(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||nn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(n){return function(n){if(Array.isArray(n))return X(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||nn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nn(n,e){if(n){if("string"==typeof n)return X(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?X(n,e):void 0}}function en(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var tn=(0,U.observer)((function(n){var e,t=n.height,r=(0,F.B)().t,a=(0,z.P)(),s=q((0,o.useState)([]),2),c=s[0],u=s[1],d=q((0,o.useState)(0),2),f=d[0],p=d[1],h=function(n){var e,t=G.PV;(B.nb.instance.isOverSea||"aihouse"===(null===(e=a.userStore.userInfo)||void 0===e?void 0:e.regSource))&&(t=t.replace("3d","aihouse"));var r=t+(t.indexOf("?")>-1?"&":"?")+"AiLayoutSchemeId="+n;window.open(r,"_blank"),Q.s.trackApplyTo3DEvent("mySchemeToNew3d")},m=function(n){var e=document.createElement("textarea");e.textContent=n.id,e.style.position="fixed",document.body.appendChild(e),e.select();try{document.execCommand("copy")?l.A.success(r("复制成功！")):l.A.error(r("复制失败！"))}catch(n){l.A.error(r("复制失败！"))}finally{document.body.removeChild(e)}},x=function(){var n,e=(n=function(n,e){var t,r,i,o;return en(this,(function(a){switch(a.label){case 0:return t={isDelete:0,pageIndex:n,pageSize:9,keyword:e},[4,$.D.getLayoutSchemeList(t)];case 1:return r=a.sent(),i=r.layoutSchemeDataList,o=r.total,i&&(u(i),p(o)),[2]}}))},function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Y(o,r,i,a,l,"next",n)}function l(n){Y(o,r,i,a,l,"throw",n)}a(void 0)}))});return function(n,t){return e.apply(this,arguments)}}();return(0,o.useEffect)((function(){x(1,null)}),[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:W.A.tools_box,children:[(0,i.jsx)("div",{className:W.A.tools_box_search,children:(0,i.jsx)(K.vj,{placeholder:r("请输入"),onSearch:function(n){x(1,n)}})}),(0,i.jsx)("div",{className:W.A.tools_box_screen}),(0,i.jsx)("div",{})]}),(0,i.jsx)("div",{className:W.A.schemeListContainer,style:{height:t},children:c&&c.map((function(n,t){return(0,i.jsxs)("div",{className:W.A.schemeItem,children:[(0,i.jsxs)("div",{className:W.A.schemeListItemImage,children:[(0,i.jsx)("div",{className:W.A.itemOvelay}),(0,i.jsx)(j.A,{className:W.A.itemOpenButton,onClick:function(){var n;n=c[t],B.nb.DispatchEvent(B.n0.OpenMyLayoutSchemeData,n),a.homeStore.setShowMySchemeList(!1),B.nb.emit(Z.U.OpenHouseSearching,!1),a.homeStore.setShowWelcomePage(!1),a.homeStore.setShowEnterPage({show:!1,source:""})},children:r("打开")}),c[t].dreamerScheme&&(0,i.jsx)(H.A,{placement:"bottom",title:r("打开梦想家方案"),children:(0,i.jsx)(M.A,{className:W.A.itemDreamerButton,onClick:function(){var n,e;n=c[t],e=n.dreamerScheme,window.open(e,"_blank"),Q.s.trackApplyTo3DEvent("mySchemeToDreamer")},iconClass:"icona-uploadmode"})}),(0,i.jsx)(H.A,{placement:"bottom",title:r("浏览3D方案"),children:(0,i.jsx)(M.A,{className:W.A.itemOpen3dButton,onClick:function(){var n;n=c[t],G.Bj?h(n.id):(B.nb.DispatchEvent(B.n0.OpenMyLayoutSchemeIn3D,n),Q.s.trackApplyTo3DEvent("mySchemeToNew3d"))},iconClass:"icona-viewdatasource"})}),(0,i.jsx)(H.A,{placement:"bottom",title:r("删除"),children:(0,i.jsx)(M.A,{className:W.A.itemDeleteButton,onClick:function(){var n,e;n=c[t],e=n.id,$.D.deleteLayoutScheme(e).then((function(n){if(n){var t=J(c);t=t.filter((function(n){return n.id!==e})),u(t)}}))},iconClass:"icondelete"})}),(0,i.jsx)("img",{src:n.coverImage,alt:""})]}),(0,i.jsx)("div",{className:W.A.schemeListItemName,children:(0,i.jsx)("span",{title:n.layoutSchemeName,children:n.layoutSchemeName})}),(0,i.jsxs)("div",{className:W.A.schemeListItemText,children:[(0,i.jsxs)("div",{style:{width:(0,G.fZ)()?"100%":"50%"},onDoubleClick:function(){return m(n)},onTouchStart:function(){return e=setTimeout((function(){return m(n)}),1e3)},onTouchEnd:function(){return clearTimeout(e)},children:["ID:",n.id]}),!(0,G.fZ)()&&(0,i.jsx)("div",{style:{width:"50%"},children:n.updateDate})]})]},t)}))}),(0,i.jsx)("div",{className:W.A.paginatio_box,children:(0,i.jsx)(V.A,{simple:!0,defaultCurrent:1,total:f,showSizeChanger:!1,onChange:function(n){x(n,null)}})})]})})),rn=t(72026);function on(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function an(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return on(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return on(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ln=(0,U.observer)((function(n){var e=function(n,e,t,r,i){return{key:e,icon:t,children:r,label:n,type:i}},t=n.isFixed,r=T().styles,s=(0,F.B)().t,c=an(a().useState("sub1"),2),u=c[0],d=c[1],f=(0,z.P)(),p=f.userStore.aihouse?[e(s("个人空间"),"sub2",null,[e(s("我的方案"),"我的方案",(0,i.jsx)(M.A,{iconClass:"iconbuzhisucai"}))],"group")]:[e(s("首页"),"sub1",(0,i.jsx)(M.A,{iconClass:"iconsmarttemplate"})),e(s("个人空间"),"sub2",null,[e(s("我的方案"),"我的方案",(0,i.jsx)(M.A,{iconClass:"iconbuzhisucai"}))],"group"),e(s("快捷访问"),"sub3",null,[e(s("AI工作台"),"AI工作台",(0,i.jsx)(M.A,{iconClass:"iconFreecabinetManager"})),"C00002170"===f.userStore.userInfo.tenantId?null:e(s("AI搭柜"),"AI搭柜",(0,i.jsx)(M.A,{iconClass:"iconbuzhisucai"}))],"group")],h=[{icon:(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",children:(0,i.jsx)("use",{xlinkHref:"#iconfollrplanliabrary"})}),title:s("户型库"),id:"openSwjFileByBuildingId"},{icon:(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",children:(0,i.jsx)("use",{xlinkHref:"#iconuploadfloorplan"})}),title:s("上传临摹图"),id:"OpenImitateImage"},{icon:(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",children:(0,i.jsx)("use",{xlinkHref:"#iconimportCAD"})}),title:s("导入CAD"),id:"CadEzdxfDrawing"}];B.nb.IsDebug||(h=h.filter((function(n){return"CadEzdxfDrawing"!==n.id})));(0,o.useEffect)((function(){"我的方案"===f.homeStore.menu_Key&&d("我的方案")}),[f.homeStore.menu_Key]);return(0,i.jsx)("div",{className:r.container,style:{position:t?"fixed":"static"},children:(0,i.jsxs)("div",{className:r.middle,children:[(0,i.jsxs)("div",{className:r.left,children:[(0,i.jsx)(v,{onClick:function(n){"AI搭柜"!==n.key?"AI布局"!==n.key?"打开本地"!==n.key&&("AI工作台"!==n.key?"帮助文档"!==n.key?d(n.key):window.open("http://3vj-fe.3vjia.com/layoutai/%E6%96%B0%E6%89%8B%E5%BC%95%E5%AF%BC-PC%E7%AB%AF0820.pdf"):window.open(G.tn)):window.open():window.open(G.Kv)},style:{width:"100%"},defaultSelectedKeys:["1"],defaultOpenKeys:["sub1"],mode:"inline",items:p}),!f.userStore.hasAuth&&(0,i.jsxs)("div",{className:r.progress,children:[(0,i.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,i.jsx)("div",{children:s("浏览3D数")}),(0,i.jsxs)("div",{children:[f.userStore.checkUse," / ",f.userStore.totalUse]})]}),(0,i.jsx)(g.A,{showInfo:!1,percent:f.userStore.checkUse/5*100}),(0,i.jsx)(j.A,{style:{width:"100%",marginTop:"5px"},type:"primary",onClick:function(){window.open("https://mall.3vjia.com/")},children:s("立即升级")})]})]}),"sub1"===u&&(0,i.jsxs)("div",{className:r.center,children:[(0,i.jsx)("div",{className:"title",children:s("首页")}),(0,i.jsx)("div",{className:"advertise ".concat((0,G.fZ)()?"mobile":""),children:(0,i.jsx)("video",{src:"https://3vj-fe.3vjia.com/layoutai/11_banner.mp4",autoPlay:!0,loop:!0,muted:!0,playsInline:!0,className:"video"})}),(0,i.jsx)("div",{className:"title",children:s("快速设计")}),(0,i.jsx)("div",{className:"designList",style:{marginBottom:rn.Bj?"40px":"0px"},children:h.map((function(n,e){return(0,i.jsxs)("div",{className:"item",onClick:function(){!function(n){switch(n){case"drawWall":B.nb.RunCommand(L.f.HouseDesignMode),f.homeStore.setDesignMode(L.f.HouseDesignMode),f.homeStore.setShowWelcomePage(!1);break;case"CadEzdxfDrawing":B.nb.RunCommand(B._I.OpenDwgFile),f.homeStore.setShowWelcomePage(!1);break;case"OpenImitateImage":B.nb.RunCommand(B._I.OpenImitateImage),f.homeStore.setShowWelcomePage(!1);break;case"openSwjFileByBuildingId":(0,R.aL)(),f.homeStore.setShowWelcomePage(!1);break;case"modelRoom":l.A.info(s("暂未开放"))}}(n.id)},children:[n.icon,(0,i.jsx)("div",{className:"iconTitle",children:n.title})]},e)}))})]}),"我的方案"===u&&(0,i.jsx)("div",{className:r.center,children:(0,i.jsx)(tn,{height:"83%"})})]})})}))},66742:function(n,e,t){t.d(e,{A:function(){return S}});var r=t(13274),i=t(41594);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n            position:fixed;\n            left : 0;\n            top : 0;\n            right : 0;\n            bottom : 0;\n            z-index : 10001; \n        "]);return a=function(){return n},n}function l(){var n=o(["\n            position : absolute;\n            width : 520px;\n            height : 520px;\n            left : calc(50% - 260px);\n            top : 80px;\n            background :#fff;\n            border-radius : 8px;\n            .mask {\n                position:absolute;\n                left : 0;\n                top : 0;\n                width : 100%;\n                height : 100%;\n                z-index : 10;\n                background :rgba(127,127,127,0.1);\n                line-height : 520px;\n                text-align : center;\n\n            }\n            .title {\n                position:absolute;\n                width : 100%;\n                left : 0;\n                top : 10px;\n                color : #2b2b2b;\n                font-size:15px;\n                font-weight:500;\n                text-align:center;\n            }\n            .closeBtn {\n                position:absolute;\n                right : 12px;\n                top : 10px;\n            }\n            .Inputs {\n                position: absolute;\n                top: 440px;\n                width: 100%;\n            }\n            .SaveTemplateCanvas {\n                width : 400px;\n                height : 400px;\n                position : absolute;\n                left : calc( 50% - 200px );\n                top : 30px;\n                cursor : pointer;\n            }\n            .input_row {\n                width: 100%;\n                text-align: center;\n                height: 20px;\n                line-height: 15px;\n                margin-bottom: 15px;\n            }\n        "]);return l=function(){return n},n}function s(){var n=o(["\n            position:absolute;\n            top : 40px;\n            left : 40px;\n            width : 160px;\n            z-index: 3;\n            input {\n            position:relative;\n            top:2px;\n            }\n            .checkbox_div {\n                float:left;\n                margin-right:10px;\n                cursor:pointer;\n            }\n\n        "]);return s=function(){return n},n}var c=(0,t(79874).rU)((function(n){var e=n.css;return{PopUpContainer:e(a()),PopUpDialog:e(l()),CheckboxContainer:e(s())}})),u=t(27347),d=t(36134),f=t(65833),p=t(1870),h=t(17365),m=t(9003),x=t(36983),y=t(15696);function b(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function v(n,e,t,r,i,o,a){try{var l=n[o](a),s=l.value}catch(n){return void t(n)}l.done?e(s):Promise.resolve(s).then(r,i)}function g(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return b(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return b(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var w={isDrawingLayout:!1},S=(0,y.observer)((function(){var n=c().styles,e=u.nb.t,t=(0,m.P)(),o=g((0,i.useState)(!1),2),a=o[0],l=o[1],s=(0,i.useRef)(null),y=(0,i.useRef)(null),b=u.nb.instance.layout_container,S=g((0,i.useState)(""),2),A=S[0],_=S[1],k=g((0,i.useState)(!0),2),C=k[0],I=k[1],O=g((0,i.useState)(w.isDrawingLayout),2),N=O[0],P=O[1],D=g((0,i.useState)(!1),2),E=D[0],T=D[1],z=function(n){if(b){b.updateWholeBox();var t=h.f.BeginDrawOnCanvas(n,b,{fixed_scale:.9});w.transformData=t.transformData;var r=b.painter;if(w.isDrawingLayout){var i=u.nb.instance;[i.layer_CadFurnitureLayer,i.layer_CadCabinetLayer].filter((function(n){return n})).forEach((function(n){n.onDraw()}))}b._room_entities.forEach((function(n){var t,i,o=n._room_poly,a=3==w.valid_room_ids[n.room_id];1==w.valid_room_ids[n.room_id]?(r.fillStyle="#c9e2ff",r.strokeStyle="#c9e2ff"):3==w.valid_room_ids[n.room_id]?(r.fillStyle="#ffc9e2",r.strokeStyle="#ffc9e2"):(r.fillStyle="#ebebeb",r.strokeStyle="#ebebeb"),r.fillPolygons([o],.6),r.strokePolygons([o]),r.fillStyle="#2b2b2b";var l=(null==n||null===(t=n._main_rect)||void 0===t?void 0:t.rect_center)||(null===(i=n.rect)||void 0===i?void 0:i.rect_center),s=e(n.roomname)+(a?"✔":"");r.drawText(e(s),l,0,60,10,!0,!0)})),h.f.EndDrawOnCanvas(n,b)}},B=function(){var n,e=(n=function(){var n,e,t,r,i,o,a,l,c,u,d,f,h,m=arguments;return j(this,(function(v){switch(v.label){case 0:n=m.length>0&&void 0!==m[0]&&m[0],e=b._scheme_id||b._layout_scheme_id||(0,p.AU)({format_type:3}),n&&(e=(0,p.AU)({format_type:3})),T(!0),w.resultMap={},t=w.resultMap,r=!0,i=!1,o=void 0,v.label=1;case 1:v.trys.push([1,6,7,8]),a=b._room_entities[Symbol.iterator](),v.label=2;case 2:return(r=(l=a.next()).done)?[3,5]:(c=l.value,1!=w.valid_room_ids[c.room_id]?[3,4]:(f=(null==y||null===(u=y.current)||void 0===u?void 0:u.value)||A,b._layout_scheme_id,[4,x.h.saveRoomEntityTemplate(c,t,{schemeId:e,layoutSchemeId:b._layout_scheme_id||"",houseTypeId:b.hxId||"",nickName:f})]));case 3:v.sent(),t[(null==c||null===(d=c._room)||void 0===d?void 0:d.uid)||c._uuid]&&(w.valid_room_ids[c.room_id]=3,s.current&&z(s.current)),v.label=4;case 4:return r=!0,[3,2];case 5:return[3,8];case 6:return h=v.sent(),i=!0,o=h,[3,8];case 7:try{r||null==a.return||a.return()}finally{if(i)throw o}return[7];case 8:return T(!1),[2]}}))},function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){v(o,r,i,a,l,"next",n)}function l(n){v(o,r,i,a,l,"throw",n)}a(void 0)}))});return function(){return e.apply(this,arguments)}}();(0,i.useEffect)((function(){return u.nb.on(f.Q.SaveRoomTemplatesPopUp,(function(n){l(n)})),function(){u.nb.off(f.Q.SaveRoomTemplatesPopUp)}}),[]);var L=function(n){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];b&&(h.f.updateRoomEntityRoomIds(b),!e&&w.valid_room_ids||(w.valid_room_ids={}),b._room_entities.forEach((function(e){var t;(null==e||null===(t=e._room)||void 0===t?void 0:t.furnitureList)&&(null==e?void 0:e._room.furnitureList.length)>0&&3!==w.valid_room_ids[e.room_id]&&(w.valid_room_ids[e.room_id]=n?1:2)}))),z(s.current)},U=function(n){y.current&&(y.current.value=n),w.templateName=n,_(n)};return(0,i.useEffect)((function(){var n;a&&(w.isDrawingLayout=N,s.current&&L(C,!0),w.schemeId===b._scheme_id&&w.templateName?U(w.templateName):(w.schemeId=b._scheme_id,U((null===(n=t.userStore.userInfo)||void 0===n?void 0:n.username)+" "+((null==b?void 0:b._scheme_name)||""))))}),[a]),a?(0,r.jsx)("div",{className:n.PopUpContainer,onClick:function(){l(!1)},children:(0,r.jsxs)("div",{className:n.PopUpDialog,onClick:function(n){n.stopPropagation()},children:[(0,r.jsx)("div",{className:"title",children:e("保存布局模板")}),(0,r.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return l(!1)}}),E&&(0,r.jsx)("div",{className:"mask",children:e("保存模板中...")}),(0,r.jsxs)("div",{className:n.CheckboxContainer,children:[(0,r.jsxs)("div",{className:"checkbox_div",onClick:function(){I(!C),L(!C)},children:[(0,r.jsx)("input",{name:"selectAllCheckbox",type:"checkbox",checked:C,onChange:function(n){}}),e("全选")]}),(0,r.jsxs)("div",{className:"checkbox_div",onClick:function(){P(!w.isDrawingLayout),w.isDrawingLayout=!w.isDrawingLayout,s.current&&z(s.current)},children:[(0,r.jsx)("input",{name:"showLayout",type:"checkbox",checked:N,onChange:function(n){}}),e("显示布局")]})]}),(0,r.jsx)("canvas",{className:"SaveTemplateCanvas",width:600,height:600,ref:s,onClick:function(n){!function(n){var e=n.target.getBoundingClientRect(),t=n.clientX-e.left,r=n.clientY-e.top,i=h.f.CanvasToWorldPos(n.target,{x:t,y:r},w.transformData);if(b){var o=b._room_entities.find((function(n){return n._room_poly.containsPoint(i)}));if(o&&w.valid_room_ids[o.room_id]){var a=w.valid_room_ids[o.room_id];2==a?w.valid_room_ids[o.room_id]=1:1===a&&(w.valid_room_ids[o.room_id]=2),z(n.target)}}}(n)}}),(0,r.jsxs)("div",{className:"Inputs",children:[(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsxs)("span",{children:[e("模板名称"),": "]}),(0,r.jsx)("input",{ref:y,type:"text",defaultValue:A,style:{width:"300px"},onChange:function(n){U(n.target.value)}})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)(d.A,{onClick:function(){return B(!1)},children:e("保存")}),"    ",(0,r.jsx)(d.A,{onClick:function(){return B(!0)},children:e("另存为")})]})]})]})}):(0,r.jsx)(r.Fragment,{})}))},75887:function(n,e,t){t.d(e,{A:function(){return y}});var r=t(13274),i=t(27347),o=t(11164),a=t(25076),l=t(41594),s=t(85783);function c(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function u(){var n=c(["\n        position: fixed;\n        top: 48px;\n        right: 50%;\n        transform: translate(50%);\n        height: 40px;\n        background: #fff;\n        box-shadow: 0 2px 8px 0 rgba(0,0,0,.16);\n        border-radius: 2px;\n        font-size: 12px;\n        font-weight: 400;\n        line-height: 40px;\n        z-index: 1;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        text-align: center;\n        padding: 0 12px;\n        box-sizing: border-box;\n    "]);return u=function(){return n},n}function d(){var n=c(["\n      .ant-select-selection-item\n        {\n            display: none;\n        }\n    "]);return d=function(){return n},n}function f(){var n=c(["\n        margin: 0px 10px;\n    "]);return f=function(){return n},n}function p(){var n=c(["\n        width: 100px !important;\n    "]);return p=function(){return n},n}var h=(0,t(79874).rU)((function(n){var e=n.css;return{container:e(u()),wallInput:e(d()),label:e(f()),popupClass:e(p())}}));function m(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function x(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return m(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return m(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var y=function(){var n=(0,s.B)().t,e=h().styles,t=o.A.Option,c=x((0,l.useState)("240"),2),u=c[0],d=c[1],f=x((0,l.useState)(n("中心线")),2),p=f[0],m=f[1];(0,l.useEffect)((function(){i.nb.DispatchEvent(i.n0.setTopWallMenuProps,{width:u,lineValue:p})}),[u,p]);var y=(0,r.jsxs)(o.A,{popupClassName:e.popupClass,placement:"bottomRight",defaultValue:"",value:"",onChange:function(n){d(n)},children:[(0,r.jsx)(t,{value:"60",children:"60"}),(0,r.jsx)(t,{value:"100",children:"100"}),(0,r.jsx)(t,{value:"120",children:"120"}),(0,r.jsx)(t,{value:"240",children:"240"}),(0,r.jsx)(t,{value:"300",children:"300"}),(0,r.jsx)(t,{value:"400",children:"400"})]});return(0,l.useEffect)((function(){}),[]),(0,r.jsxs)("div",{className:e.container,children:[(0,r.jsx)("span",{className:e.label,children:n("定位线")}),(0,r.jsx)(o.A,{style:{width:80},size:"small",value:p,onChange:function(n){m(n)},options:[{value:n("外线"),label:n("外线")},{value:n("中心线"),label:n("中心线")},{value:n("内线"),label:n("内线")}]}),(0,r.jsx)("span",{className:e.label,children:n("墙厚")}),(0,r.jsx)(a.A,{value:u,size:"small",style:{width:90},addonAfter:y,className:e.wallInput,onChange:function(n){d(n.target.value)},defaultValue:""})]})}},84545:function(n,e,t){t.d(e,{A:function(){return k}});var r=t(13274),i=t(41594),o=t(535),a=t(33100),l=t(77320),s=t(36134),c=t(25076),u=t(11164);function d(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function f(){var n=d(["\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: bold;\n            font-size: 14px;\n            line-height: 1.57;\n            letter-spacing: 0px;\n            text-align: left;\n            margin-bottom: 12px;\n        "]);return f=function(){return n},n}function p(){var n=d(["\n            width: 100px;\n            height: 40px;\n           background: #147ffa; /* 按钮背景色 */\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            text-align: center;\n            transition: background 0.3s; /* 背景色过渡效果 */\n            font-family: PingFang SC;\n            font-weight: bold;\n            font-size: 14px;\n            line-height: 1.5;\n        \n            &:hover {\n            background: linear-gradient(90deg, #d07bff 0%, #7a5bff 100%) !important; /* 悬停时背景色变亮 */\n            }\n        "]);return p=function(){return n},n}function h(){var n=d(["\n            width: 100px;\n            height: 40px;\n            background: #f4f5f5 !important; /* 按钮背景色 */\n            color: #282828 !important;\n            font-family: PingFang SC;\n            font-weight: bold;\n            font-size: 14px;\n            line-height: 1.5;\n            letter-spacing: 0px;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            text-align: center;\n            transition: background 0.3s; /* 背景色过渡效果 */\n\n            &:hover {\n                background:rgb(188, 195, 203) \n            }\n        "]);return h=function(){return n},n}var m=(0,t(79874).rU)((function(n){var e=n.css;return{desc:e(f()),customSubmitButton:e(p()),customCancelButton:e(h())}})),x=t(9003),y=t(27347),b=t(51764),v=t(46335),g=t(65640);function j(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function w(n,e,t,r,i,o,a){try{var l=n[o](a),s=l.value}catch(n){return void t(n)}l.done?e(s):Promise.resolve(s).then(r,i)}function S(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){w(o,r,i,a,l,"next",n)}function l(n){w(o,r,i,a,l,"throw",n)}a(void 0)}))}}function A(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return j(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return j(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var k=function(){var n=m().styles,e=(0,x.P)(),t=A(o.A.useForm(),1)[0];(0,i.useEffect)((function(){var n=y.nb.instance.layout_container,e=0;n._rooms.forEach((function(n){e+=n.area})),t.setFieldsValue({area:e.toFixed(2)})}),[]);var d=function(){var n=S((function(n){var t,r;return _(this,(function(i){switch(i.label){case 0:return t={houseSchemeId:"",houseTypeName:n.houseTypeName,province:h[0],city:h[1],district:h[2],towards:n.towards,area:n.area,room:n.room,hall:n.hall,kitchen:n.kitchen,bathroom:n.bathroom,buildingName:"",dataUrl:"",dwgUrl:"",imageUrl:"",platLayoutId:"",source:""},[4,b.r.createHouseScheme(t)];case 1:return(r=i.sent())?a.A.success((0,v.t)("已保存到个人工作台-我的户型")):a.A.error((0,v.t)("保存失败")),e.homeStore.setShowHouseSchemeAddForm(!1),[2,r]}}))}));return function(e){return n.apply(this,arguments)}}(),f=function(){var n=S((function(){return _(this,(function(n){return t.validateFields().then(function(){var n=S((function(n){return _(this,(function(e){switch(e.label){case 0:return[4,d(n)];case 1:return e.sent(),[2]}}))}));return function(e){return n.apply(this,arguments)}}()).catch((function(n){g.warn(n),a.A.error(n.errorFields[0].errors[0])})),[2]}))}));return function(){return n.apply(this,arguments)}}(),p=A((0,i.useState)([]),2),h=p[0];p[1];return(0,i.useEffect)((function(){}),[]),(0,r.jsx)(l.A,{title:"户型信息",open:!0,destroyOnClose:!0,onCancel:function(){e.homeStore.setShowHouseSchemeAddForm(!1)},footer:[(0,r.jsx)(s.A,{onClick:function(){},className:n.customCancelButton,children:"取消"},"back"),(0,r.jsx)(s.A,{type:"primary",onClick:f,className:n.customSubmitButton,children:"确定"},"submit")],children:(0,r.jsxs)(o.A,{form:t,initialValues:{houseTypeName:"",room:"",hall:"",bathroom:"",kitchen:"",towards:"",area:""},labelCol:{span:24},wrapperCol:{span:24},children:[(0,r.jsx)("div",{style:{display:"flex",alignItems:"center"},children:(0,r.jsx)(o.A.Item,{name:"houseTypeName",label:(0,r.jsx)("span",{style:{fontWeight:"bold"},children:"户型名称"}),style:{flex:1},rules:[{required:!0,message:"户型名称不能为空"},{max:30,message:"最多支持输入30个字"}],children:(0,r.jsx)(c.A,{placeholder:"请输入名称"})})}),(0,r.jsx)("div",{className:n.desc,children:"户型"}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:20},children:[(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",flex:1},children:[(0,r.jsx)(o.A.Item,{name:"room",style:{flex:1,marginBottom:0},children:(0,r.jsx)(c.A,{placeholder:"请输入"})}),(0,r.jsx)("span",{style:{margin:"0 20px 0 5px"},children:"室"})]}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",flex:1},children:[(0,r.jsx)(o.A.Item,{name:"hall",style:{flex:1,marginBottom:0},children:(0,r.jsx)(c.A,{placeholder:"请输入"})}),(0,r.jsx)("span",{style:{margin:"0 20px 0 5px"},children:"厅"})]}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",flex:1},children:[(0,r.jsx)(o.A.Item,{name:"bathroom",style:{flex:1,marginBottom:0},children:(0,r.jsx)(c.A,{placeholder:"请输入"})}),(0,r.jsx)("span",{style:{margin:"0 20px 0 5px"},children:"卫"})]}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",flex:1},children:[(0,r.jsx)(o.A.Item,{name:"kitchen",style:{flex:1,marginBottom:0},children:(0,r.jsx)(c.A,{placeholder:"请输入"})}),(0,r.jsx)("span",{style:{margin:"0 20px 0 5px"},children:"厨"})]})]}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)(o.A.Item,{name:"towards",label:(0,r.jsx)("span",{style:{fontWeight:"bold"},children:"朝向"}),style:{marginRight:"16px",flex:1},children:(0,r.jsxs)(u.A,{placeholder:"请选择",onChange:function(n,e){},children:[(0,r.jsx)(u.A.Option,{value:"东",children:"东"}),(0,r.jsx)(u.A.Option,{value:"南",children:"南"}),(0,r.jsx)(u.A.Option,{value:"西",children:"西"}),(0,r.jsx)(u.A.Option,{value:"北",children:"北"}),(0,r.jsx)(u.A.Option,{value:"东南",children:"东南"}),(0,r.jsx)(u.A.Option,{value:"西南",children:"西南"}),(0,r.jsx)(u.A.Option,{value:"东北",children:"东北"}),(0,r.jsx)(u.A.Option,{value:"西北",children:"西北"})]})}),(0,r.jsx)(o.A.Item,{name:"area",label:(0,r.jsx)("span",{style:{fontWeight:"bold"},children:"建筑面积"}),style:{flex:1},children:(0,r.jsx)(c.A,{placeholder:"请输入面积",suffix:"m²"})})]})]})})}}}]);