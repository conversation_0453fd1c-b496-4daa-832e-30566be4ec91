import { Object3D, Object3DEventMap } from "three";

import { TPainter } from "../../Drawing/TPainter";
import { SceneMaterialMode } from "../../Scene3D/MaterialManager";
import { compareNames } from "@layoutai/z_polygon";
import { DefaultCeilingLayerTemplate, DefaultCeilingType } from "../IRoomInterface";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { DefaultSpaceCeilingRules } from "../TLayoutGraph/TGraphConfigs/DefaultCeilingConfigs";
import { CeilingMeshBuilder } from "../../Scene3D/builder/CeilingMeshBuilder";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TCeilingLayerEntity } from "./TCeilingLayerEntity";
import { TRoomEntity } from "./TRoomEntity";
import { CeilingConfigReader } from "../TLayoutGraph/TGraphConfigs/CeilingConfigReader";


/**
 *   房间吊顶实体
 *   整个空间的吊顶 --- 它关联一个roomEntity 和 多个TCeilingLayerEntity
 *     --- 暂定一个空间唯一绑定一个TRoomCeilingEntity --- 便于各种生成逻辑集中处理
 */
export class TRoomCeilingEntity extends TBaseEntity {


    protected _roomEntity: TRoomEntity;
    private _ceiling_layer_entities: TCeilingLayerEntity[];

    constructor(room_entity: TRoomEntity) {
        super();
        this._roomEntity = room_entity;
        this._ceiling_layer_entities = [];
    }
    get roomEntity() {
        return this._roomEntity;
    }
    get ceiling_layer_entities(): TCeilingLayerEntity[] {
        return this._ceiling_layer_entities;
    }

    getAllDownLights() {
        let down_lights: TFigureElement[] = [];
        this._ceiling_layer_entities.forEach((entity) => {
            down_lights.push(...entity.getAllDownLights());
        })
        return down_lights;
    }
    update(): void {
        if (this._ceiling_layer_entities) {
            this._ceiling_layer_entities.forEach((entity) => entity.update());
        }
    }
    updateMesh3D(mode?: SceneMaterialMode): Object3D<Object3DEventMap> {
        CeilingMeshBuilder.buildMesh(this);
        return this._mesh3d;
    }

    static getDefaultLayerType(ceilingEntity: TRoomCeilingEntity, ceiling_figure_element: TFigureElement = null): DefaultCeilingType {
        let space_names: string[] = [ceilingEntity.name, ceilingEntity.roomEntity?.roomname, ceiling_figure_element?.sub_category || "未命名"];
        let target_rule = DefaultSpaceCeilingRules.find(rule => compareNames(rule.space_names, space_names));
        if (target_rule) {
            return target_rule.layerType;
        }
        return DefaultCeilingType.Flattop;
    }

    updateCeiling(force: boolean = false) {
        let room = this._roomEntity._room;
        let ceiling_list = room?._ceilling_list;

        if (!ceiling_list) return;

        let needs_update = true;
        if (ceiling_list.length == this._ceiling_layer_entities.length) {
            let has_diffrent = false;
            for (let i = 0; i < ceiling_list.length; i++) {
                if (ceiling_list[i] !== this._ceiling_layer_entities[i].ceiling_figure_element) {
                    has_diffrent = true;
                }
            }
            if (!has_diffrent) {
                needs_update = false;
            }
        }
        if (needs_update || force) {
            if (this._ceiling_layer_entities.length === ceiling_list.length) {

                ceiling_list.forEach((ceiling_figure, index) => {

                    let layerEntity = this.ceiling_layer_entities[index];
                    layerEntity.roomEntity = this._roomEntity;
                    layerEntity.ceiling_figure_element = ceiling_figure;
                    return layerEntity;
                });
            }
            else {

                let prev_ceiling_layer_entities = [...this.ceiling_layer_entities];

                this._ceiling_layer_entities = ceiling_list.map((ceiling_figure, index) => {

                    let prev_layer_entity = prev_ceiling_layer_entities[index];
                    let layerEntity = prev_layer_entity || new TCeilingLayerEntity(null);
                    layerEntity.roomEntity = this._roomEntity;
                    layerEntity.ceiling_figure_element = ceiling_figure;

                    if (!prev_layer_entity) {
                        let tp = TRoomCeilingEntity.getDefaultLayerType(this, ceiling_figure);
                        let templateData = CeilingConfigReader.readConfig(tp);
                        layerEntity.loadCeilingLayerData(templateData);
                    }
                    return layerEntity;
                });
            }

        }

        this.update();

    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (options.is_draw_figure) {
            this._ceiling_layer_entities.forEach((layer) => {
                layer.drawEntity(painter, options);
            })
        }
    }


    bindDownLights(down_lights: TFigureElement[], options: { add_figures_to_room?: boolean } = {}) {
        down_lights = down_lights.filter((ele) => compareNames([ele.category, ele.sub_category], ["筒灯", "射灯", "格栅灯", "轨道", "灯带"]));
        down_lights.forEach((down_light) => {
            let target_layer: TCeilingLayerEntity = null;
            this.ceiling_layer_entities.forEach((layer) => {
                let res = layer.bindDownLight(down_light);
                if (res) {
                    target_layer = res;
                }
            });
            if (target_layer) {
                target_layer.down_lights.push(down_light);

                if (options.add_figures_to_room) {
                    if (this.roomEntity._room) {
                        let id = this.roomEntity._room._furniture_list.indexOf(down_light);
                        if (id < 0) {
                            this._roomEntity._room._furniture_list.push(down_light);
                        }
                    }
                }

            }
        });
    }

    exportCeilingData(): object[] {
        return this._ceiling_layer_entities.map((layer) => layer.exportCeilingData());
    }
}

