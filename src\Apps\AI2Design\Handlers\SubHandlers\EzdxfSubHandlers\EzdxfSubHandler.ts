import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { EventName } from "@/Apps/EventSystem";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { I_EzdxfEntity } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZPolyline } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Box3, Vector3, Vector3Like } from "three";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";

export enum EzdxfEventName {
}
export enum EzdxfEvents {
    Ezdxf_SelectModeChanged = "Ezdxf_SelectModeChanged"
}

export enum EzdxfSelectMode {
    SelectSingleEntity = "SelectSingleEntity",
    SelectInfillArea = "SelectInfillArea"
}
export class EzdxfSubHandler extends CadBaseSubHandler {
    _candidate_polygons: ZPolygon[];
    _target_poly: ZPolygon;

    _selecting_area_v0: Vector3;
    _selecting_area_v1: Vector3;

    _select_mode: EzdxfSelectMode; // 0:选择
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = "EzdxfSubHandler";
        this._selecting_area_v0 = null;
        this._selecting_area_v1 = null;
        this._select_mode = EzdxfSelectMode.SelectSingleEntity;
    }
    enter(state?: number): void {
        super.enter(state);
        this._candidate_polygons = [];
        this._select_mode = EzdxfSelectMode.SelectSingleEntity;
        this.updateEzdxfAreaPolygons();
    }
    leave(state?: number): void {
        super.leave(state);
        if (this._candidate_polygons) {
            delete this._candidate_polygons;
        }
    }

    findSelectedEzdxfEntity(pos: Vector3Like) {
        let container = this.container;
        if (container.ezdxf_cad_data) {
            let ans_entity: I_EzdxfEntity = null;
            let min_dist: number = 300;
            container.ezdxf_cad_data.modelspace.entities.forEach((entity) => {
                if (entity._entity_points) {
                    let t_min_dist = -1;
                    for (let i = 0; i < entity._entity_points.length; i += 2) {
                        let p0 = entity._entity_points[i];
                        let p1 = entity._entity_points[i + 1];

                        let dist0 = p0.distanceTo(pos);

                        // if(entity.type!="INSERT")
                        {
                            if (p1) {
                                let edge = new ZEdge({ pos: p0 }, { pos: p1 });
                                if (edge.length > 1.) {
                                    edge.computeNormal();


                                    let pp = edge.projectEdge2d(pos);
                                    if (pp.x >= 0 && pp.x <= edge.length) {
                                        dist0 = Math.min(dist0, Math.abs(pp.y));
                                    }
                                }

                            }
                        }

                        t_min_dist = t_min_dist < 0 ? dist0 : Math.min(t_min_dist, dist0);
                    }
                    if (t_min_dist > -0.1 && t_min_dist < min_dist) {
                        min_dist = t_min_dist;
                        ans_entity = entity;
                    }
                }
            });

            return ans_entity;
        }
        return null;
    }
    findSelectedCandidatePolygons(pos: Vector3Like, is_add_mode: number = 0) {
        if (this._candidate_polygons) {
            let ans_poly: ZPolygon = null;
            let min_dist = -1;
            this._candidate_polygons.forEach((poly) => {
                if (poly.orientation_z_nor.z > 0) return;
                let dist = poly.distanceToPoint(pos);
                if (dist < 0) {
                    if (!ans_poly || dist > min_dist) {
                        min_dist = dist;
                        ans_poly = poly;
                    }
                }

            });
            this._target_poly = ans_poly;

            if (this._target_poly) {
                let container = this.container;
                if (container.ezdxf_cad_data) {
                    container.ezdxf_cad_data.modelspace.entities.forEach((entity) => {
                        if (entity.type === "LINE") {
                            let pos0 = entity.attribs.start_v3.clone();
                            let pos1 = entity.attribs.end_v3.clone();

                            let center = pos0.add(pos1).multiplyScalar(0.5);
                            let dist = this._target_poly.distanceToPoint(center);

                            let t_edge: ZEdge = new ZEdge({ pos: pos0 }, { pos: pos1 });
                            t_edge.computeNormal();
                            if (dist < 5) {
                                if (is_add_mode == 1) {
                                    entity.attribs.is_entity_selected = true;
                                }
                                else if (is_add_mode == 2) {
                                    if (entity.attribs.is_entity_selected) {
                                        delete entity.attribs.is_entity_selected;
                                    }
                                }
                                else {
                                    if (entity.attribs.is_entity_selected) {
                                        delete entity.attribs.is_entity_selected;
                                    }
                                    else {
                                        entity.attribs.is_entity_selected = true;
                                    }
                                }
                            }


                        }
                        if (entity.type === "LWPOLYLINE") {
                            let sum_length = 0;
                            let total_length = 0;
                            for (let i = 0; i < entity._entity_points.length; i += 2) {
                                let p0 = entity._entity_points[i];
                                let p1 = entity._entity_points[i + 1];
                                if (!p1) break;

                                let center = p0.clone().add(p1).multiplyScalar(0.5);
                                let dist = this._target_poly.distanceToPoint(center);
                                if (Math.abs(dist) < 5) {
                                    sum_length += p0.distanceTo(p1);
                                }
                                total_length += p0.distanceTo(p1);
                            }

                            if (sum_length > total_length * 0.5) {
                                if (is_add_mode == 1) {
                                    entity.attribs.is_entity_selected = true;
                                }
                                else if (is_add_mode == 2) {
                                    if (entity.attribs.is_entity_selected) {
                                        delete entity.attribs.is_entity_selected;
                                    }
                                }
                                else {
                                    if (entity.attribs.is_entity_selected) {
                                        delete entity.attribs.is_entity_selected;
                                    }
                                    else {
                                        entity.attribs.is_entity_selected = true;
                                    }
                                }
                            }

                        }
                    })
                }
                LayoutAI_App.emit_M(EventName.EzdxfEntitiesSelected, null);

            }
        }

    }
    cleanSelection(): void {
        let container = this.container;
        if (container.ezdxf_cad_data) {
            container.ezdxf_cad_data.modelspace.entities.forEach(entity => {
                if (entity.attribs.is_entity_selected) {
                    delete entity.attribs.is_entity_selected;
                }
            })
        }
    }

    onmousedown(ev: I_MouseEvent): void {

        if (ev.ctrlKey) {
            this._selecting_area_v0 = new Vector3(ev.posX, ev.posY, 0);
            this._selecting_area_v1 = new Vector3(ev.posX, ev.posY, 0);

            this._cad_mode_handler._is_moving_element = true;
            return;
        }
        this._selecting_area_v0 = null;
        this._selecting_area_v1 = null;
        let is_add_mode = ev.ctrlKey ? 1 : 0;
        if (this._select_mode === EzdxfSelectMode.SelectInfillArea) {
            this.findSelectedCandidatePolygons({ x: ev.posX, y: ev.posY, z: 0 }, is_add_mode);
        }
        else {
            let selected_entity = this.findSelectedEzdxfEntity({ x: ev.posX, y: ev.posY, z: 0 });
            if (selected_entity) {
                if (is_add_mode == 1) {
                    selected_entity.attribs.is_entity_selected = true;
                }
                else if (is_add_mode == 2) {
                    if (selected_entity.attribs.is_entity_selected) {
                        delete selected_entity.attribs.is_entity_selected;
                    }
                }
                else {
                    if (selected_entity.attribs.is_entity_selected) {
                        delete selected_entity.attribs.is_entity_selected;
                    }
                    else {
                        selected_entity.attribs.is_entity_selected = true;
                    }
                }
                LayoutAI_App.emit_M(EventName.EzdxfEntitiesSelected, null);
            }
            else {

            }

        }




        this.update();

    }
    onmousemove(ev: I_MouseEvent): void {
        if (this._selecting_area_v1) {
            this._selecting_area_v1.copy({ x: ev.posX, y: ev.posY, z: 0 });
            this.update();
        }
    }
    onmouseup(ev: I_MouseEvent): void {

        if (this._selecting_area_v0 && this._selecting_area_v1) {
            this.selectEntitiesBySelectingRect(ev.button == 0);
        }
        this._cad_mode_handler._is_moving_element = false;

        this._selecting_area_v0 = null;
        this._selecting_area_v1 = null;
        this.update();
    }

    selectEntitiesBySelectingRect(is_add_entity: boolean = true) {
        let bbox = new Box3();
        bbox.expandByPoint(this._selecting_area_v0);
        bbox.expandByPoint(this._selecting_area_v1);
        if (this.ezdxf_data) {

            this.ezdxf_data.modelspace.entities.forEach((entity) => {
                if (entity._bbox3) {
                    if (bbox.containsBox(entity._bbox3)) {
                        if (is_add_entity) {
                            entity.attribs.is_entity_selected = true;
                        }
                        else {
                            if (entity.attribs.is_entity_selected) {
                                delete entity.attribs.is_entity_selected;
                            }
                        }
                    }
                }

            });


            LayoutAI_App.emit_M(EventName.EzdxfEntitiesSelected, null);


        }

    }

    updateCandidateRects(): void {

    }

    get ezdxf_data() {
        return this.container.ezdxf_cad_data;
    }

    updateEzdxfAreaPolygons() {
        let ezdxf_cad_data = this.container.ezdxf_cad_data;
        if (!ezdxf_cad_data) return;
        let wall_lines: ZEdge[] = [];
        ezdxf_cad_data.modelspace.entities.forEach(entity => {
            if (entity.type === "LINE" || entity.type == "LWPOLYLINE") {
                if (entity.type === "LWPOLYLINE") {
                    let points = entity.lwpoints_v3;
                    // if(points.length == 4)
                    {
                        let poly = new ZPolyline();
                        if (entity.attribs.flags) {
                            poly = new ZPolygon();
                        }
                        let tpoints: Vector3[] = [];
                        poly.initByVertices(points);
                        poly.computeZNor();
                        wall_lines.push(...poly.edges);
                    }
                }
                else if (entity.type === "LINE") {
                    let pos0 = entity.attribs.start_v3.clone();
                    let pos1 = entity.attribs.end_v3.clone();
                    let edge = new ZEdge({ pos: pos0 }, { pos: pos1 });
                    edge.computeNormal();
                    wall_lines.push(edge);
                }
            }

        });
        let extend_len = 10;
        let rects: ZRect[] = wall_lines.map((line) => {
            let rect = new ZRect(line.length + extend_len, extend_len);
            rect.nor = line.nor;
            rect.rect_center = line.center;
            rect.reOrderByOrientation();
            return rect;
        });

        if (!rects[0]) return;
        let ans_polygons = rects[0].union_polygons(rects);
        // console.log("Union Polygons", ans_polygons.length);
        // this._candidate_polygons = ans_polygons.filter(poly=>poly.orientation_z_nor.z < 0);
        this._candidate_polygons = ans_polygons.filter(poly => poly.expandPolygon(extend_len / 2 * -poly.orientation_z_nor.z));

    }
    drawCanvas(): void {

        // this.painter.strokeStyle = "#0f0";

        // this.painter.strokePolygons(this._candidate_polygons);
        if (this._target_poly) {
            this.painter.strokeStyle = "#0ff";
            this.painter.strokePolygons([this._target_poly]);

        }
        if (this._selecting_area_v0 && this._selecting_area_v1) {
            let bbox = new Box3();
            bbox.expandByPoint(this._selecting_area_v0);
            bbox.expandByPoint(this._selecting_area_v1);
            let rect = ZRect.fromBox3(bbox);
            let painter = this.painter;
            painter.fillStyle = "#d5e4fb";
            painter.strokeStyle = "#147FFA";
            painter._context.lineWidth = 1;
            this.painter.fillPolygons([rect], 0.5);
            this.painter.strokePolygons([rect]);
        }
        if (this._ex_ondraw_func) {
            this._ex_ondraw_func(this.painter);
        }
    }

    handleEvent(evt_name: string, evt_param: any): void {
        if (evt_name === LayoutAI_Events.SetExDrawingFunc) {
            this._ex_ondraw_func = evt_param;
        }
        if (evt_name === EzdxfEvents.Ezdxf_SelectModeChanged) {
            this._select_mode = evt_param;
        }
    }

    onkeydown(ev: KeyboardEvent): boolean {
        if (ev.key == "Escape") {
            this.cleanSelection();
            this.update();
        }
        return true;
    }
}