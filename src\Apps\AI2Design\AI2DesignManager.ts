import { TAppManagerBase } from '../AppManagerBase';
import { FigureDataList, g_FigureImagePaths } from '../LayoutAI/Drawing/FigureImagePaths';
import { FloorImagePaths } from '../LayoutAI/Drawing/FloorImagePath';
import { TAILayoutDrawingLayer } from '../LayoutAI/Drawing/TAILayoutDrawingLayer';
import { TCadCabinetLayer } from '../LayoutAI/Drawing/TCadCabinetLayer';
import { TCadFloorDrawingLayer } from '../LayoutAI/Drawing/TCadFloorDrawingLayer';
import { TCadFurnitureLayer } from '../LayoutAI/Drawing/TCadFurnitureLayer';
import { TCadRoomStrucureLayer } from '../LayoutAI/Drawing/TCadRoomFrameLayer';
import {
  CadBatchDrawingLayerType,
  CadDrawingLayerType,
  TDrawingLayer
} from '../LayoutAI/Drawing/TDrawingLayer';
import { TSeriesFigureGroupDB } from '../LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TSeriesFigureGroupDB';
import { TSerialSizeRangeDB } from '../LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TSeriesSizeRangeDB';
import { loadImage } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands } from '../LayoutAI_App';
import { Permissions } from '../LayoutAI/setting/Permissions';

import { AICadEditModeHandler } from './Handlers/AICadEditModeHandler';
import { AIMatchingModeHandler } from './Handlers/AIMatchingModeHandler';

import { cad_file } from '@/config';
import { EventName } from '../EventSystem';
import { ModelLocPublicCategoryMap } from '../LayoutAI/AICadData/ModelLocPubliccategoryMap';
import { DefaultFigureXml } from '../LayoutAI/Drawing/DefaultFigureXml';
import { TAIMatchingDrawingLayer } from '../LayoutAI/Drawing/TAIMatchingDrawingLayer';
import { TCadCeilingLayer } from '../LayoutAI/Drawing/TCadCeilingDrawingLayer';
import { TCadCopyImageLayer } from '../LayoutAI/Drawing/TCadCopyImageLayer';
import { TCadElectricityDrawingLayer } from '../LayoutAI/Drawing/TCadElectricityDrawingLayer';
import { TEzdxfDataDrawingLayer } from '../LayoutAI/Drawing/TCadEzdxfDrawingLayer';
import { TCadRoomLightingLayer } from '../LayoutAI/Drawing/TCadLightingDrawingLayer';
import { TCadOutLineLayer } from '../LayoutAI/Drawing/TCadOutLineLayer';
import { TCadRoomDecoratesLayer } from '../LayoutAI/Drawing/TCadRoomDecoratesLayer';
import { TCadRoomNameLayer } from '../LayoutAI/Drawing/TCadRoomNameLayer';
import { TCadSubRoomAreaDrawingLayer } from '../LayoutAI/Drawing/TCadSubRoomAreaDrawingLayer';
import { TDimensionWallElementLayer } from '../LayoutAI/Layout/TransformElements/TDimensionWallElementLayer';
import { TDimensionOutterWallElementLayer } from '../LayoutAI/Layout/TransformElements/TDimensionOutterWallElementLayer';
import { TDrawingBatchLayer } from '../LayoutAI/Drawing/TDrawingBatchLayer';
import { TExportCadDrawingLayer } from '../LayoutAI/Drawing/TExportCadLayer';
import { TExtDrawingDrawingLayer } from '../LayoutAI/Drawing/TExtDrawingDrawingLayer';
import { TRulerLayer } from '../LayoutAI/Drawing/TRulerLayer';
import { TGroupTemplate } from '../LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TGroupTemplate';
import { TLayoutEntityContainer } from '../LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter';
import { AIHouseDesignModeHandler } from './Handlers/AIHouseDesignModeHandler';
import { AIViewEditModeHandler } from './Handlers/AIViewEditModeHandler';
import { ExDrawingModeHandler } from './Handlers/ExDrawingModeHandler';
import { EzDxfEditModeHandler } from './Handlers/EzDxfEditModeHandler';
import { HouseCorrectionModeHandler } from './Handlers/HouseCorrectionModeHandler';
import { MeasurScaleModeHandler } from './Handlers/MeasurScaleModeHandler';
import { RemodelingModeHandler } from './Handlers/RemodelingModeHandler';
import { TrialModelHandler } from './Handlers/TrialModelHandler';
import { HotelDesignSubHandler } from './Handlers/HotelHandlers/HotelDesignSubHander/HotelDesignBaseHandler';
import { AIHotelLayoutModeHandler } from './Handlers/HotelHandlers/AIHotelLayoutModeHandler';
import { IndexedDBService } from '../LayoutAI/Services/IndexedDB/IndexedDBService';
import { TCadCameraLayer } from '../LayoutAI/Drawing/TCadCameraLayer';

/**
 *  AI2Design最基本的模式
 */
export enum AI2DesignBasicModes {
  AiCadMode = 'AiCadMode',
  LayoutMode = 'LayoutMode',
  SwjLayoutMode = 'SwjLayoutMode',
  DesignMode = 'DesignMode',
  HouseDesignMode = 'HouseDesignMode',
  HotelLayoutMode = "HotelLayoutMode",
  EzDxfEditMode = 'EzDxfEditMode',
  ExDrawingMode = 'ExDrawingMode',
  MeasurScaleMode = 'MeasurScaleMode',
  RulerMode = 'RulerMode',
  RemodelingMode = 'RemodelingMode',
  HouseCorrectionMode = 'HouseCorrectionMode',
  AIViewEditMode = 'AIViewEditMode',
  TrialModelMode = 'TrialModelMode'
}

export class AI2DesignManager extends TAppManagerBase {
  _visible_layers: TDrawingLayer[];

  _show_background_grids: boolean;

  static AppName: string = 'AI2Design';

  constructor(canvas: HTMLCanvasElement = null) {
    super(AI2DesignManager.AppName);

    this._layout_container = new TLayoutEntityContainer(this);

    this._visible_layers = [];

    this._show_background_grids = false;
  }

  public init(): void {
    super.init();

    this.initLayers();
  }

  initElements(canvas: HTMLCanvasElement = null) {
    super.initElements(canvas);
    this.drawing_layers = {};
  }

  /**
   *  初始化图层
   */
  initLayers() {
    // 目前只初始化两个图层

    this.drawing_layers = {};
    this.drawing_layers[CadDrawingLayerType.CadFloorDrawing] = new TCadFloorDrawingLayer(this);
    this.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing] = new TEzdxfDataDrawingLayer(this); // ezdxf json数据层

    this.drawing_layers[CadDrawingLayerType.CadFurniture] = new TCadFurnitureLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadCamera] = new TCadCameraLayer(this); //初始化相机图层
    this.drawing_layers[CadDrawingLayerType.CadCabinet] = new TCadCabinetLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadRoomStrucure] = new TCadRoomStrucureLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadDecorates] = new TCadRoomDecoratesLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadElectricity] = new TCadElectricityDrawingLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadLighting] = new TCadRoomLightingLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadCeiling] = new TCadCeilingLayer(this); //

    this.drawing_layers[CadDrawingLayerType.CadOutLine] = new TCadOutLineLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadRoomName] = new TCadRoomNameLayer(this); //
    this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing] =
      new TCadSubRoomAreaDrawingLayer(this);

    this.drawing_layers[CadDrawingLayerType.CadDimensionWallElement] =
      new TDimensionWallElementLayer(this);
    this.drawing_layers[CadDrawingLayerType.CadDimensionWallElement].visible = false;
    this.drawing_layers[CadDrawingLayerType.CadDimensionOutterWallElement] =
      new TDimensionOutterWallElementLayer(this);
    this.drawing_layers[CadDrawingLayerType.CadDimensionOutterWallElement].visible = false;

    this.drawing_layers[CadDrawingLayerType.AILayoutDrawing] = new TAILayoutDrawingLayer(this);
    this.drawing_layers[CadDrawingLayerType.AIMatchingDrawing] = new TAIMatchingDrawingLayer(this);
    this.drawing_layers[CadDrawingLayerType.ExtDrawingDrawing] = new TExtDrawingDrawingLayer(this);
    this.drawing_layers[CadDrawingLayerType.CadCopyImageDrawing] = new TCadCopyImageLayer(this);
    this.drawing_layers[CadDrawingLayerType.RulerDrawing] = new TRulerLayer(this);
    this.drawing_layers[CadDrawingLayerType.AIMatchingDrawing].visible = false;
    this.drawing_layers[CadDrawingLayerType.AILayoutDrawing].visible = false;
    this.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing].visible = false;
    this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing].visible = false;
    this.drawing_layers[CadDrawingLayerType.CadLighting].visible = false;
    this.drawing_layers[CadDrawingLayerType.CadDecorates].visible = false;

    this.drawing_layers[CadDrawingLayerType.ExportCadDrawing] = new TExportCadDrawingLayer(this);
    this.drawing_layers[CadDrawingLayerType.ExportCadDrawing].visible = false;
    // 拆改图层
    this.drawing_layers[CadDrawingLayerType.Remodeling] = new TDrawingLayer(
      CadDrawingLayerType.Remodeling,
      this
    );

    this._batch_drawing_layers = {};
    this._batch_drawing_layers[CadBatchDrawingLayerType.AICadDefaultBatch] = new TDrawingBatchLayer(
      CadBatchDrawingLayerType.AICadDefaultBatch,
      [
        this.drawing_layers[CadDrawingLayerType.CadCopyImageDrawing],
        this.drawing_layers[CadDrawingLayerType.CadFloorDrawing],
        this.drawing_layers[CadDrawingLayerType.CadFurniture],
        this.drawing_layers[CadDrawingLayerType.CadCabinet],
        this.drawing_layers[CadDrawingLayerType.CadRoomStrucure],
        this.drawing_layers[CadDrawingLayerType.CadDecorates],
        this.drawing_layers[CadDrawingLayerType.CadElectricity],
        this.drawing_layers[CadDrawingLayerType.CadLighting],
        this.drawing_layers[CadDrawingLayerType.CadCeiling],
        this.drawing_layers[CadDrawingLayerType.CadOutLine],
        this.drawing_layers[CadDrawingLayerType.CadRoomName],
        this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing],
        this.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing],
        this.drawing_layers[CadDrawingLayerType.RulerDrawing],
        this.drawing_layers[CadDrawingLayerType.CadCamera],
        // this.drawing_layers[CadDrawingLayerType.CadDimensionWallElement]
        this.drawing_layers[CadDrawingLayerType.CadDimensionOutterWallElement]
      ],
      this
    );

    this._batch_drawing_layers[CadBatchDrawingLayerType.ExtDrawingBatch] = new TDrawingBatchLayer(
      CadBatchDrawingLayerType.ExtDrawingBatch,
      [
        this.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing],
        this.drawing_layers[CadDrawingLayerType.ExtDrawingDrawing]
      ],
      this
    );

    let t_count = Object.keys(this.drawing_layers).length;
    let t_i = 0;
    for (let key in this.drawing_layers) {
      this.drawing_layers[key].z_index = -t_count + t_i;
      t_i++;
    }

    this.updateVisibleLayers();
  }

  initHandlers() {
    this.handlers = {};

    this.handlers[AI2DesignBasicModes.AiCadMode] = new AICadEditModeHandler(this);

    this.handlers[AI2DesignBasicModes.HouseDesignMode] = new AIHouseDesignModeHandler(this);

    this.handlers[AI2DesignBasicModes.DesignMode] = new AIMatchingModeHandler(this);
    this.handlers[AI2DesignBasicModes.EzDxfEditMode] = new EzDxfEditModeHandler(this);

    this.handlers[AI2DesignBasicModes.ExDrawingMode] = new ExDrawingModeHandler(this);
    this.handlers[AI2DesignBasicModes.MeasurScaleMode] = new MeasurScaleModeHandler(this);

    this.handlers[AI2DesignBasicModes.RemodelingMode] = new RemodelingModeHandler(this);
    this.handlers[AI2DesignBasicModes.HouseCorrectionMode] = new HouseCorrectionModeHandler(this);
    this.handlers[AI2DesignBasicModes.AIViewEditMode] = new AIViewEditModeHandler(this);
    this.handlers[AI2DesignBasicModes.TrialModelMode] = new TrialModelHandler(this);
    this.handlers[AI2DesignBasicModes.HotelLayoutMode] = new AIHotelLayoutModeHandler(this);
    for (let key in this.handlers) {
      this.handlers[key].name = key;
    }
  }

  updateVisibleLayers() {
    this._visible_layers = [];
    for (let key in this.drawing_layers) {
      if (this.drawing_layers[key].visible) {
        this._visible_layers.push(this.drawing_layers[key]);
      }
    }
  }

  protected async preparePatterns() {
    await this.painter.prepareDefaultBlocks();

    let pattern_img_paths = g_FigureImagePaths;
    this.painter._svg_pattern_dict = {};
    let figure_xml: string = DefaultFigureXml;
    if (figure_xml) {
      let xml_document = new DOMParser().parseFromString(figure_xml, 'application/xml');

      let svg_elements = xml_document.getElementsByTagName('svg');

      for (let svg_element of svg_elements) {
        let figure_name = svg_element.getAttribute('figure_name');
        if (figure_name) {
          let elements = this.painter.computeSvgDrawingElement(svg_element);
          this.painter._svg_pattern_dict[figure_name] = {
            drawing_elements: elements
          };
        }
      }
    }
    for (let key in pattern_img_paths) {
      let data = pattern_img_paths[key];
      if (!data.img_path || data.img_path.length == 0) continue;
      if (this.painter._svg_pattern_dict[key] || this.painter._svg_pattern_dict[data.modelLoc])
        continue;

      let img = await loadImage(data.img_path);

      if (this.painter != null && img) {
        this.painter.setPattern(key, img, 'no-repeat');
      }
    }
    let pattern_floorimg_paths: { [key: string]: { img_path?: string } } = FloorImagePaths;

    for (let key in pattern_floorimg_paths) {
      let data = pattern_floorimg_paths[key];
      if (!data.img_path || data.img_path.length == 0) continue;

      let img = await loadImage(data.img_path);

      if (this.painter != null && img) {
        this.painter.setPattern(key, img, 'repeat');
      }
    }
    this.update();
  }

  onLayerVisibilityChanged(): void {
    let state: { [key: string]: boolean } = {};

    for (let layer_name in this.drawing_layers) {
      let layer = this.drawing_layers[layer_name];
      state[layer_name] = layer.visible;
    }

    this.EventSystem.emit_M(EventName.SwitchDrawingLayer, state);
  }

  /**
   *  数据准备
   */
  async prepare(): Promise<void> {
    let model_loc_map = new ModelLocPublicCategoryMap();
    await model_loc_map.prepare_fromAiDesk();
    await TSerialSizeRangeDB.LoadSeries();

    await TSeriesFigureGroupDB.LoadSeries();



    await IndexedDBService.instance.openDB();
    await this.preparePatterns().then(() => {
      // 绘制组合模板的图片

      let group_template_data_list: any = [];

      let visit_data_list = (
        s_data_list: { label: string; child: []; figureList: { group_code?: string }[] }[]
      ) => {
        if (!s_data_list) return;
        for (let data of s_data_list) {
          if (!data) {
            console.log(s_data_list, data);
            continue;
          }
          if (data.figureList) {
            for (let figure of data.figureList) {
              if (figure.group_code) {
                group_template_data_list.push(figure);
              }
            }
          }
          visit_data_list(data.child);
        }
      };
      visit_data_list(FigureDataList as any);
      this._updateGroupTemplateImages(group_template_data_list);
    });

    let hasInputCadData: boolean = cad_file != null && cad_file.length > 0;
    if (this._current_handler) {
      await this._current_handler.prepare(!hasInputCadData);
    }
    await super.prepare();

      // 获取权限配置参数
      await Permissions.instance.loadPermissions();
  }

  /**
   *  绘制函数
   */
  onDraw() {
    if (!this.painter) return;
    this.painter.clean();

    this.painter.enter_drawpoly();
    if (this._current_handler) {
      this._current_handler.drawCanvas();
    }

    this.painter.leave_drawpoly();
  }

  /**
   *   更新
   */
  update() {
    // 重点位置
    this.onDraw();
  }

  public setMode(mode: string): void {
    super.setMode(mode);
    LayoutAI_App.emit_M(EventName.AIDesignModeChanged, mode);
  }

  _runCommand(cmd_str: string) {
    if (cmd_str === LayoutAI_Commands.ShowBackgroundGrids) {
      this._show_background_grids = true;
    } else if (cmd_str === LayoutAI_Commands.HideBackgroundGrids) {
      this._show_background_grids = false;
    }
    if (this.handlers[cmd_str] !== undefined) {
      this.setMode(cmd_str);
      return;
    }

    if (this._current_handler) {
      this._current_handler.runCommand(cmd_str);
    }
  }

  _updateGroupTemplateImages(
    data_list: {
      image?: string;
      group_code?: string;
      title?: string;
      length?: number;
      depth?: number;
    }[]
  ) {
    if (!this.painter) return;
    let ts = this.painter.exportTransformData();
    let main_canvas = this.painter._canvas;
    let canvas: HTMLCanvasElement = document.createElement('canvas');

    this.painter.bindCanvas(canvas);
    for (let data of data_list) {
      if (data.group_code && !data.image) {
        let res = TGroupTemplate.getGroupTemplateImageByGroupCode(
          data.group_code,
          this.painter,
          canvas,
          data.length,
          data.depth
        );
        data.image = res.img_path;

        TGroupTemplate.GroupCodeUiInfo[data.group_code] = {
          image: data.image,
          title: data.title,
          default_length: data.length,
          default_depth: data.depth
        };
      }
    }

    this.painter.bindCanvas(main_canvas);
    this.painter.importTransformData(ts);
  }

  _dispatchEvent(event_name: string, event_param: any) {
    if (event_name === EventName.UpdateGroupTemplateImages) {
      this._updateGroupTemplateImages(event_param);
    }
    if (this._current_handler) {
      this._current_handler.handleEvent(event_name, event_param);
    }
  }

  makeLayersDirty() {
    for (let layer_name in this.drawing_layers) {
      this.drawing_layers[layer_name].makeDirty();
    }
  }

  undo(): void {
    super.undo();
    if (this._current_handler) {
      this._current_handler.undo();
    }
  }

  redo(): void {
    super.redo();
    if (this._current_handler) {
      this._current_handler.redo();
    }
  }

  onkeydown(ev: KeyboardEvent): void {
    super.onkeydown(ev);
  }

  // 重写父类方法
  public getPainterGlobalAlpha(): number {
    return this._current_handler_mode === AI2DesignBasicModes.MeasurScaleMode ? 1 : 0.5;
  }
}
