"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[9586],{29586:function(e,a,t){t.r(a),t.d(a,{default:function(){return d}});var i={items_per_page:"na stronę",jump_to:"Id<PERSON> do",jump_to_confirm:"potwierdź",page:"",prev_page:"Poprzednia strona",next_page:"Następna strona",prev_5:"Poprzednie 5 stron",next_5:"Następne 5 stron",prev_3:"Poprzednie 3 strony",next_3:"Następne 3 strony",page_size:"rozmiar strony"},r=t(74866),o=t(50799),n=(0,r.A)((0,r.A)({},o.I),{},{locale:"pl_PL",today:"Dzisiaj",now:"Teraz",backToToday:"Ustaw dzisiaj",ok:"OK",clear:"W<PERSON><PERSON><PERSON><PERSON><PERSON>",week:"Tydzie<PERSON>",month:"Miesiąc",year:"Rok",timeSelect:"Ustaw czas",dateSelect:"Ustaw datę",monthSelect:"Wybierz miesiąc",yearSelect:"Wybierz rok",decadeSelect:"Wybierz dekadę",dateFormat:"D/M/YYYY",dateTimeFormat:"D/M/YYYY HH:mm:ss",previousMonth:"Poprzedni miesiąc (PageUp)",nextMonth:"Następny miesiąc (PageDown)",previousYear:"Ostatni rok (Ctrl + left)",nextYear:"Następny rok (Ctrl + right)",previousDecade:"Ostatnia dekada",nextDecade:"Następna dekada",previousCentury:"Ostatni wiek",nextCentury:"Następny wiek"});var l={placeholder:"Wybierz godzinę"};const s={lang:Object.assign({placeholder:"Wybierz datę",rangePlaceholder:["Data początkowa","Data końcowa"],yearFormat:"YYYY",dateFormat:"M/D/YYYY",dayFormat:"D",dateTimeFormat:"M/D/YYYY HH:mm:ss",monthFormat:"MMMM",monthBeforeYear:!0,shortWeekDays:["Niedz","Pon","Wt","Śr","Czw","Pt","Sob"],shortMonths:["Sty","Lut","Mar","Kwi","Maj","Cze","Lip","Sie","Wrz","Paź","Lis","Gru"]},n),timePickerLocale:Object.assign({},l)};const m="${label} nie posiada poprawnej wartości dla typu ${type}";var d={locale:"pl",Pagination:i,DatePicker:s,TimePicker:l,Calendar:s,global:{placeholder:"Wybierz"},Table:{filterTitle:"Menu filtra",filterConfirm:"OK",filterReset:"Usuń filtry",filterEmptyText:"Brak filtrów",filterCheckAll:"Wybierz wszystkie elementy",filterSearchPlaceholder:"Szukaj w filtrach",emptyText:"Brak danych",selectAll:"Zaznacz bieżącą stronę",selectInvert:"Odwróć zaznaczenie",selectNone:"Wyczyść",selectionAll:"Wybierz wszystkie",sortTitle:"Sortowanie",expand:"Rozwiń wiersz",collapse:"Zwiń wiersz",triggerDesc:"Sortuj malejąco",triggerAsc:"Sortuj rosnąco",cancelSort:"Usuń sortowanie"},Tour:{Next:"Dalej",Previous:"Wróć",Finish:"Zakończ"},Modal:{okText:"OK",cancelText:"Anuluj",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Anuluj"},Transfer:{titles:["",""],searchPlaceholder:"Szukaj",itemUnit:"obiekt",itemsUnit:"obiekty",remove:"Usuń",selectCurrent:"Wybierz aktualną stronę",removeCurrent:"Usuń aktualną stronę",selectAll:"Wybierz wszystkie",removeAll:"Usuń wszystkie",selectInvert:"Odwróć wybór"},Upload:{uploading:"Wysyłanie...",removeFile:"Usuń plik",uploadError:"Błąd wysyłania",previewFile:"Podejrzyj plik",downloadFile:"Pobieranie pliku"},Empty:{description:"Brak danych"},Icon:{icon:"Ikona"},Text:{edit:"Edytuj",copy:"Kopiuj",copied:"Skopiowany",expand:"Rozwiń"},Form:{optional:"(opcjonalne)",defaultValidateMessages:{default:"Błąd walidacji dla pola ${label}",required:"Pole ${label} jest wymagane",enum:"Pole ${label} musi posiadać wartość z listy: [${enum}]",whitespace:"Pole ${label} nie może być puste",date:{format:"${label} posiada zły format daty",parse:"${label} nie może zostać zinterpretowane jako data",invalid:"${label} jest niepoprawną datą"},types:{string:m,method:m,array:m,object:m,number:m,date:m,boolean:m,integer:m,float:m,regexp:m,email:m,url:m,hex:m},string:{len:"${label} musi posiadać ${len} znaków",min:"${label} musi posiadać co namniej ${min} znaków",max:"${label} musi posiadać maksymalnie ${max} znaków",range:"${label} musi posiadać między ${min} a ${max} znaków"},number:{len:"${label} musi mieć wartość o długości ${len}",min:"${label} musi mieć wartość większą lub równą ${min}",max:"${label} musi mieć wartość mniejszą lub równą ${max}",range:"${label} musi mieć wartość pomiędzy ${min} a ${max}"},array:{len:"${label} musi posiadać ${len} elementów",min:"${label} musi posiadać co najmniej ${min} elementów",max:"${label} musi posiadać maksymalnie ${max} elementów",range:"${label} musi posiadać między ${min} a ${max} elementów"},pattern:{mismatch:"${label} nie posiada wartości zgodnej ze wzorem ${pattern}"}}},Image:{preview:"Podgląd"}}}}]);