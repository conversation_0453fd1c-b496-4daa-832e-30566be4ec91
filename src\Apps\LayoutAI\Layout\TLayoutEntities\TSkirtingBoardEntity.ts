import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>anager, SceneMaterialMode } from "@layoutai/model3d_api";
import { ZEdge, ZPolygon, ZPolyline, ZRect } from "@layoutai/z_polygon";
import { <PERSON>uffer<PERSON>ttribute, DoubleSide, Float32BufferAttribute, Mesh, MeshStandardMaterial, Object3D, Vector3 } from "three";
import { MeshName, SkirtBoardObject3D } from "../../Scene3D/NodeName";
import { GeometryBuilder } from "../../Scene3D/builder/GeometryBuilder";
import { TBaseEntity } from "./TBaseEntity";
import { TRoomEntity } from "./TRoomEntity";

/**
 *  橱柜的踢脚板类
 */
export class TSkirtingBoardEntity extends TBaseEntity
{
    protected _roomEntity : TRoomEntity;

    protected _boardThickness : number;

    private _skirtPolyline: ZPolyline;

    private _firstEdgeNor : Vector3;

    protected _skirtboard_mesh : Mesh;
    constructor(poly:ZPolyline)
    {
        super(null,-1);
        this.type = "SkirtingBoard";
        this.height = 100;
        this._boardThickness = 18;
        this.skirtPolyline = poly;

        this.bindEntity();
    }

    get skirtPolyline(): ZPolyline {
        return this._skirtPolyline;
    }
    set skirtPolyline(value: ZPolyline) {
        this._skirtPolyline = value;
        if(this._skirtPolyline && this._skirtPolyline.edges.length > 0)
        {
            this._firstEdgeNor = this._skirtPolyline.edges[0].nor.clone();
        }
    }
    

    updateMesh3D(mode?: SceneMaterialMode): Object3D {
        if(!this._mesh3d)
        {
            this._mesh3d = new SkirtBoardObject3D();
            this._mesh3d.name = MeshName.SkirtBoard;
        }
        if(!this._skirtPolyline)
        {
            return this._mesh3d;
        }
        if(!this._skirtboard_mesh)
        {
            this._skirtboard_mesh = new Mesh();
        }

        this._mesh3d.add(this._skirtboard_mesh);

        if(!this._skirtboard_mesh.material)
        {
            this._skirtboard_mesh.material = new MeshStandardMaterial({side:DoubleSide,polygonOffset:true, roughness: 1.,
                metalness: 1.,
                transparent: false,
                polygonOffsetUnits:1,
                polygonOffsetFactor:1});
            MaterialManager.bindMeshMaterials(this._skirtboard_mesh);
        }

        if(this._skirtboard_mesh.geometry)
        {
            this._skirtboard_mesh.geometry.dispose();
            this._skirtboard_mesh.geometry = null;
        }

        let cliperRect = new ZRect(1,1);
        cliperRect._u_dv_flag = -cliperRect.u_dv_flag;
        cliperRect.rect_center = new Vector3(0.5,0.5);
        
        this._skirtboard_mesh.geometry = GeometryBuilder.buildSweepGeometry(this._skirtPolyline,cliperRect,0,{direction:-1,extrude_length:this._boardThickness, extrude_height:this.height});
        
        const positionAttribute =  this._skirtboard_mesh.geometry.getAttribute('position');
        const normalAttriibute = this._skirtboard_mesh.geometry.getAttribute('normal');
        // positionAttribute.count 是顶点数量
        const uvArray = new Float32Array(positionAttribute.count * 2);

        for (let i = 0; i < positionAttribute.count; i++) {
            let x = positionAttribute.getX(i);
            let y = positionAttribute.getY(i);
            let z = positionAttribute.getZ(i);

            const nx = normalAttriibute.getX(i);
            const ny = normalAttriibute.getY(i);
            const nz = normalAttriibute.getZ(i);

            let v = new Vector3(x,y,z);
            let nor = new Vector3(nx,ny,nz);

            if(Math.abs(nor.z) < 0.5)
            {
                let dx = nor.clone().cross({x:0,y:0,z:1});
                let dy = dx.clone().cross(nor).normalize();
                dx = dy.clone().cross(nor).normalize();
                y = v.dot(dx);
                x = v.dot(dy);
            }  

            // 假设墙体主要沿着X轴和Y轴展开
            // 这里的UV计算是一个简单的平面映射
            uvArray[i * 2] = x /1000.; // U坐标
            uvArray[i * 2 + 1] = y / 1000.; // V坐标
        }
        this._skirtboard_mesh.geometry.setAttribute("uv",new Float32BufferAttribute(uvArray,2));
        let indexList: number[] = [];
        for (let i = 0;i < positionAttribute.count; i++) {
            indexList.push(i);
        }
        let indices = new Uint16Array(indexList);
        let attr = new BufferAttribute(indices, 1);
        this._skirtboard_mesh.geometry.setIndex(attr)
        EdgesBuilder.makeEdgesOfObject(this._skirtboard_mesh);

        
        return this._mesh3d;
    }
    /**
     *  根据橱柜生成踢脚线
     *  
     */
    static makeSkirtingBoardsFromCabinetRects(floorCabinetRects:ZRect[], highCabinetRects:ZRect[], roomPoly:ZPolygon, boardThickness:number = 18,extendLen:number=100)
    {
        if(floorCabinetRects.length == 0) return;

        const tolLen = 5;
        let rects = floorCabinetRects.map((cabinet_rect) => {
            let rect = cabinet_rect.clone();
            rect.updateRect();
            let left_extend = 0;
            let right_extend = 0;
            let back_extend = 100;

            const computeExtendLength = (edge:ZEdge)=>{
                let result_len = tolLen;
                floorCabinetRects.forEach((other_rect)=>{
                    if(other_rect === cabinet_rect) return;
                    let res = other_rect.getRayIntersection(edge.center,edge.nor);
                    if(res && res.point)
                    {
                        let len = res.point.distanceTo(edge.center);
                        if(len <= extendLen)
                        {
                            result_len = Math.max(len,result_len);
                        }
                    }
                });
                return result_len;
            }
            left_extend = computeExtendLength(rect.leftEdge);
            right_extend = computeExtendLength(rect.rightEdge);


            let pos = rect.unproject({ x: (right_extend - left_extend) / 2, y: -back_extend / 2 });
            rect._w += (left_extend + right_extend);
            rect._h += back_extend;
            rect.rect_center = pos;
            rect.reOrderByOrientation(true);
            return rect;

        });

        if (rects.length == 0) {
            return [];
        }
        let polys = rects[0].union_polygons(rects);
        let t_polys: ZPolygon[] = [];
        let room_poly = roomPoly.clone();
        room_poly.reOrderByOrientation(true);
        polys.forEach(poly => {
            let inside_polys = poly.intersect(room_poly);
            t_polys.push(...inside_polys);
        });
        let resultSkirtBoards : TSkirtingBoardEntity[] = [];
        
        t_polys.forEach(poly=>{
            // 找到所有前沿的Edge
            let frontEdges = poly.edges.filter((edge)=>{
                let isInfrontCabinet = floorCabinetRects.find((rect)=>{
                    if(edge.islayOn(rect.frontEdge,boardThickness,0.5))
                    {
                        return true;
                    }
                    return false;
                });
                return isInfrontCabinet;
            });

            // 找到连续的Edge

            let edges_groups : ZEdge[][] = [];
            const isInGroup = "isInGroup";
            frontEdges.forEach((edge)=>{
                if(edge._ex_props[isInGroup]) return;
                let next_edges_group : ZEdge[] = [];
                let prev_edges_group : ZEdge[] = [];
                let startEdge = edge;
                while(startEdge)
                {
                    if(!frontEdges.includes(startEdge)) break;
                    if(startEdge._ex_props[isInGroup]) break;
                    next_edges_group.push(startEdge);
                    startEdge._ex_props[isInGroup] = 1;
                    startEdge = startEdge.next_edge;                    
                }
                startEdge = edge.prev_edge;
                while(startEdge)
                {
                    if(!frontEdges.includes(startEdge)) break;
                    if(startEdge._ex_props[isInGroup]) break;
                    prev_edges_group.push(startEdge);
                    startEdge._ex_props[isInGroup] = 1;
                    startEdge = startEdge.prev_edge;                    
                }
                prev_edges_group = prev_edges_group.reverse();
                edges_groups.push([...prev_edges_group,...next_edges_group]);
            });

            edges_groups.forEach((edges)=>{
                if(edges.length == 0) return;
                let points = [edges[0].v0.pos];
                points.push(...edges.map((edge)=>edge.v1.pos));

                let polyline = new ZPolyline();
                polyline.initByVertices(points);
                polyline.orientation_z_nor.copy(poly.orientation_z_nor.clone());
                polyline.edges.forEach((edge)=>{
                    edge.computeNormal(polyline.orientation_z_nor);
                });
                
                if(polyline.edges[0])
                {
                    let startEdge = polyline.edges[0];
                    startEdge.v0.pos.add(startEdge.dv.multiplyScalar(tolLen));
                }
                if(polyline.edges[polyline.edges.length-1])
                {
                    let endEdge = polyline.edges[polyline.edges.length-1]
                    endEdge.v0.pos.add(endEdge.dv.multiplyScalar(-tolLen));
                }
                let entity = new TSkirtingBoardEntity(polyline);
                resultSkirtBoards.push(entity);
            })

        })
        return resultSkirtBoards;
    }    

}