import GUI, { Controller } from "lil-gui";
import { <PERSON> } from "three";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutRoom } from "@/Apps/LayoutAI/Layout/TAppSolvers/LayoutRoom";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import {
    KitchenAreaData,
    KitchenAreaTypeNames,
    KitchenLayoutType,
    KitchenSubService
} from "@layoutai/layout_service";
import { ZEdge, ZPolyline } from "@layoutai/z_polygon";

const layoutTypes = [
    KitchenLayoutType.I_TYPE,
    KitchenLayoutType.II_TYPE,
    KitchenLayoutType.L_TYPE,
    KitchenLayoutType.U_TYPE
];

/**
 * @description 自动布局测试 - 支持两种显示模式
 * mode 0: 跟随2D画布 - 测试canvas与painter保持完全一致的变换
 * mode 1: 屏幕居中 - 在测试canvas中居中显示房间布局
 * <AUTHOR>
 * @date 2025-07-22
 * @lastEditTime 2025-07-22 14:10:36
 * @lastEditors xuld
 */
export class AutoLayoutKitchenTest {
    private static _isInit: boolean = false;
    private static _gui: GUI;
    private static _canvas: HTMLCanvasElement;

    // 0: 跟2D画布一致 1: 屏幕中间点
    private static _mode: number = 0;

    // 厨房布局子服务
    private static _kitchenSubService = new KitchenSubService();

    // 当前选中的房间
    private static selRoom: LayoutRoom = null;

    // 门禁区
    private static doorEdges: ZEdge[] = [];

    // 布局方案
    private static areaData: KitchenAreaData = null;

    // 显示房间轮廓
    private static showPoly: boolean = true;
    // 显示门禁区
    private static showDoorEdge: boolean = true;
    // 显示布局类型区域
    private static showLayoutArea: boolean = true;
    // 显示布局
    private static showLayout: boolean = true;
    // 当前选中的布局类型索引
    private static selLayoutTypeIndex: number = 0;
    // 当前选中的布局方案索引
    private static selLayoutIndex: number = 0;
    private static selLayoutController: Controller = null;

    public static get gui(): GUI {
        if (!this._gui) {
            this._gui = new GUI();
            this._canvas = this.createCanvas();
            // 根据模式选择添加位置
            if (this._mode === 0) {
                // mode 0: 添加到 painter canvas 的父容器中
                const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
                if (painter && painter._canvas && painter._canvas.parentElement) {
                    painter._canvas.parentElement.appendChild(this._canvas);
                } else {
                    document.body.appendChild(this._canvas);
                }
            } else {
                // mode 1: 添加到 body
                document.body.appendChild(this._canvas);
            }
        }
        return this._gui;
    }

    public static get kitchenSubService(): KitchenSubService {
        return this._kitchenSubService;
    }

    public static updateData(): boolean {
        // 获取所有房间名称
        const layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const roomEntities = layout_container._room_entities;
        if (roomEntities.length === 0) {
            return false;
        }

        // 添加房间下拉列表
        let kitchen = roomEntities.find(roomEntity => roomEntity.name === "厨房");
        if (!kitchen) {
            kitchen = roomEntities[0];
        }
        this.selRoom = new LayoutRoom(kitchen._room);

        this.doorEdges = this.kitchenSubService.getDoorEdges(this.selRoom);
        const typeDataMap: Map<KitchenLayoutType, KitchenAreaData> =
            this.kitchenSubService.getLayoutTypeAreas(this.doorEdges[0], this.selRoom, [
                layoutTypes[this.selLayoutTypeIndex]
            ]);
        this.areaData = typeDataMap.get(layoutTypes[this.selLayoutTypeIndex]);

        return true;
    }

    public static showPanel() {
        if (this._isInit) {
            return;
        }

        let res = this.updateData();
        if (!res) {
            console.error("数据更新失败");
            return;
        }
        this._isInit = true;

        // 添加模式切换
        this.gui
            .add({ mode: this._mode }, "mode", { 跟随2D画布: 0, 屏幕居中: 1 })
            .name("显示模式")
            .onChange((value: number) => {
                this._mode = value;
                // 重新创建 canvas 以应用新的尺寸设置
                if (this._canvas) {
                    this._canvas.remove();
                    this._canvas = this.createCanvas();
                    // 根据模式选择添加位置
                    if (this._mode === 0) {
                        // mode 0: 添加到 painter canvas 的父容器中
                        const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
                        if (painter && painter._canvas && painter._canvas.parentElement) {
                            painter._canvas.parentElement.appendChild(this._canvas);
                        } else {
                            document.body.appendChild(this._canvas);
                        }
                    } else {
                        // mode 1: 添加到 body
                        document.body.appendChild(this._canvas);
                    }
                }
                this.update();
            });

        // 获取所有房间名称
        const layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const roomEntities = layout_container._room_entities;
        const roomOptions: { [key: string]: TRoomEntity } = {};
        roomEntities.forEach((roomEntity, index) => {
            roomOptions[index + "_" + roomEntity.name] = roomEntity;
        });
        if (roomEntities.length === 0) {
            return;
        }

        // 添加房间下拉列表
        this.gui
            .add({ room: "0_" + this.selRoom.name }, "room", Object.keys(roomOptions))
            .name("房间")
            .onChange((value: string) => {
                this.selRoom = new LayoutRoom(roomOptions[value]._room);
                this.selLayoutTypeIndex = 0;
                this.selLayoutIndex = 0;
                this.update();
            });

        this.gui
            .add({ showPoly: this.showPoly }, "showPoly")
            .name("显示房间轮廓")
            .onChange((value: boolean) => {
                this.showPoly = value;
                this.update();
            });

        this.gui
            .add({ showDoorArea: this.showDoorEdge }, "showDoorArea")
            .name("显示门禁区")
            .onChange((value: boolean) => {
                this.showDoorEdge = value;
                this.update();
            });

        this.gui
            .add({ showLayoutArea: this.showLayoutArea }, "showLayoutArea")
            .name("布局区域")
            .onChange((value: boolean) => {
                this.showLayoutArea = value;
                this.update();
            });

        this.gui
            .add({ showLayout: this.showLayout }, "showLayout")
            .name("显示布局")
            .onChange((value: boolean) => {
                this.showLayout = value;
                this.update();
            });

        const selType = layoutTypes[this.selLayoutTypeIndex];
        this.gui
            .add({ selType: selType }, "selType", layoutTypes)
            .name("选择布局类型")
            .onChange((value: KitchenLayoutType) => {
                this.selLayoutTypeIndex = layoutTypes.indexOf(value as any);
                this.selLayoutIndex = 0;
                this.update();
            });

        this.updateUI();

        this.gui
            .add(
                {
                    refresh: () => {
                        this.update();
                    }
                },
                "refresh"
            )
            .name("刷新");

        this.gui
            .add(
                {
                    closePanel: () => {
                        this.closePanel();
                    }
                },
                "closePanel"
            )
            .name("关闭面板");

        this.updateScene();
    }

    public static closePanel() {
        if (this.selLayoutController) {
            this.selLayoutController.destroy();
            this.selLayoutController = null;
        }
        if (this._gui) {
            this._gui.destroy();
            this._gui = null;
            this._isInit = false;
        }
        if (this._canvas) {
            this.clean();
            this._canvas.remove();
            this._canvas = null;
        }
    }

    private static clean() {
        const ctx = this._canvas.getContext("2d");
        if (ctx) {
            ctx.clearRect(0, 0, this._canvas.width, this._canvas.height);
        }
    }

    private static syncCanvasSize() {
        if (!this._canvas || this._mode !== 0) return;

        const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
        if (painter && painter._canvas) {
            // 同步尺寸
            if (
                this._canvas.width !== painter._canvas.width ||
                this._canvas.height !== painter._canvas.height
            ) {
                this._canvas.width = painter._canvas.width;
                this._canvas.height = painter._canvas.height;
            }

            // 同步样式尺寸
            const painterStyleWidth = painter._canvas.style.width;
            const painterStyleHeight = painter._canvas.style.height;
            if (painterStyleWidth && this._canvas.style.width !== painterStyleWidth) {
                this._canvas.style.width = painterStyleWidth;
            }
            if (painterStyleHeight && this._canvas.style.height !== painterStyleHeight) {
                this._canvas.style.height = painterStyleHeight;
            }

            // 同步样式属性
            const painterCanvas = painter._canvas;
            if (
                painterCanvas.style.position &&
                this._canvas.style.position !== painterCanvas.style.position
            ) {
                this._canvas.style.position = painterCanvas.style.position;
            }
            if (painterCanvas.style.left && this._canvas.style.left !== painterCanvas.style.left) {
                this._canvas.style.left = painterCanvas.style.left;
            }
            if (painterCanvas.style.top && this._canvas.style.top !== painterCanvas.style.top) {
                this._canvas.style.top = painterCanvas.style.top;
            }
        }
    }

    public static update() {
        this.updateData();
        this.updateUI();
        this.updateScene();
    }

    private static updateUI() {
        const options: number[] = [];
        for (let i = 0; i < this.areaData.layouts.length; i++) {
            options.push(i);
        }
        if (this.selLayoutController) {
            this.selLayoutController.options(options);
            this.selLayoutController.updateDisplay();
        } else {
            this.selLayoutController = this.gui
                .add({ selectedLayoutIndex: this.selLayoutIndex }, "selectedLayoutIndex", options)
                .name("选择布局")
                .onChange((value: number) => {
                    this.selLayoutIndex = value;
                    this.update();
                });
        }
    }

    private static updateScene() {
        this.syncCanvasSize();
        this.clean();

        if (this.showPoly) {
            this.drawPolys([this.selRoom.featurePoly], "#000000");
        }

        if (this.showDoorEdge) {
            if (this.doorEdges && this.doorEdges.length > 0) {
                this.drawEdges(this.doorEdges, "#ff0000");
            }
        }

        if (this.showLayoutArea) {
            for (let edgeInfo of this.areaData.edgeInfos) {
                this.drawPolys([edgeInfo.rect], "#00ff00", true);
                this.drawEdges([edgeInfo.edge], "#ff0000");
            }
        }

        if (this.showLayout) {
            if (
                this.areaData.layouts.length > 0 &&
                this.selLayoutIndex < this.areaData.layouts.length
            ) {
                const layout = this.areaData.layouts[this.selLayoutIndex];
                for (let edgeLayout of layout.edgeLayouts) {
                    for (let areaTypeRect of edgeLayout.areaTypeRects) {
                        this.drawPolys([areaTypeRect.area], "#0000ff");
                        this.drawText(
                            KitchenAreaTypeNames[areaTypeRect.areaType],
                            areaTypeRect.area.rect_center,
                            "#0000ff"
                        );
                    }
                }
            }
        }
    }

    private static createCanvas() {
        const canvas = document.createElement("canvas");
        canvas.style.position = "absolute";
        canvas.style.pointerEvents = "none";
        canvas.style.zIndex = "9999"; // 设置最高层级，避免被其他DOM元素遮挡

        // 根据模式设置 canvas 尺寸和位置
        if (this._mode === 0) {
            // mode 0: 与 painter 保持一致
            const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
            if (painter && painter._canvas) {
                canvas.width = painter._canvas.width;
                canvas.height = painter._canvas.height;
                canvas.style.width = painter._canvas.style.width || painter._canvas.width + "px";
                canvas.style.height = painter._canvas.style.height || painter._canvas.height + "px";

                // 尝试将测试 canvas 添加到 painter canvas 的父容器中，这样可以保持相对位置
                const painterCanvas = painter._canvas;
                const parentContainer = painterCanvas.parentElement;
                if (parentContainer) {
                    // 使用与 painter canvas 相同的定位方式
                    canvas.style.position = painterCanvas.style.position || "absolute";
                    canvas.style.left = painterCanvas.style.left || "0px";
                    canvas.style.top = painterCanvas.style.top || "0px";
                    canvas.style.right = painterCanvas.style.right || "auto";
                    canvas.style.bottom = painterCanvas.style.bottom || "auto";
                } else {
                    // 如果找不到父容器，使用绝对定位
                    canvas.style.left = "0";
                    canvas.style.top = "0";
                }
            } else {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                canvas.style.left = "0";
                canvas.style.top = "0";
            }
        } else {
            // mode 1: 屏幕居中模式
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            canvas.style.left = "0";
            canvas.style.top = "0";
        }

        return canvas;
    }

    private static getMidScreenPos(pos: { x: number; y: number }): { x: number; y: number } {
        const points = this.selRoom.featurePoly.toPoints();
        // 计算坐标范围
        const minX = Math.min(...points.map(p => p.x));
        const maxX = Math.max(...points.map(p => p.x));
        const minY = Math.min(...points.map(p => p.y));
        const maxY = Math.max(...points.map(p => p.y));

        // 计算缩放比例，确保图形能完整显示在canvas中
        const padding = 200; // 留出边距
        const scaleX = (this._canvas.width - padding * 2) / (maxX - minX);
        const scaleY = (this._canvas.height - padding * 2) / (maxY - minY);
        const scale = Math.min(scaleX, scaleY, 1); // 不要放大，只缩小

        // 计算偏移量，使图形居中，Y轴翻转
        const offsetX = (this._canvas.width - (maxX - minX) * scale) / 2 - minX * scale;
        const offsetY = (this._canvas.height - (maxY - minY) * scale) / 2 + maxY * scale;

        return {
            x: pos.x * scale + offsetX,
            y: pos.y * -scale + offsetY
        };
    }

    private static getPainterPos(pos: { x: number; y: number }): { x: number; y: number } {
        const painter = (LayoutAI_App.instance as TAppManagerBase).painter;

        // 使用 project2D 方法，然后转换为相对于 painter canvas 的坐标
        const projected = painter.project2D(pos);

        // 将投影坐标转换为相对于 painter canvas 的坐标
        const canvasX = projected.x + painter._canvas.width / 2;
        const canvasY = projected.y + painter._canvas.height / 2;

        return { x: canvasX, y: canvasY };
    }

    private static drawPolys(polys: ZPolyline[], color: string, fill: boolean = false) {
        const ctx = this._canvas.getContext("2d");

        ctx.strokeStyle = color;
        ctx.lineWidth = 4;

        for (let i = 0; i < polys.length; i++) {
            ctx.beginPath();
            const poly = polys[i];
            const points = poly.toPoints();
            if (points && points.length > 0) {
                const pos0 = this.getLocalPos(points[0]);
                ctx.moveTo(pos0.x, pos0.y);
                for (let j = 1; j < points.length; j++) {
                    const pos = this.getLocalPos(points[j]);
                    ctx.lineTo(pos.x, pos.y);
                }
                ctx.closePath();
            }
            ctx.stroke();
            if (fill) {
                ctx.fillStyle = color;
                ctx.fill();
            }
        }
    }

    /**
     * @description 获取本地坐标, mode 0: 跟2D画布一致 1: 屏幕中间点
     * @param pos 屏幕坐标
     * @return 本地坐标
     */
    private static getLocalPos(pos: { x: number; y: number }): { x: number; y: number } {
        if (this._mode === 1) {
            return this.getMidScreenPos(pos);
        } else {
            return this.getPainterPos(pos);
        }
    }

    private static drawEdges(edges: ZEdge[], color: string) {
        const ctx = this._canvas.getContext("2d");

        ctx.strokeStyle = color;
        ctx.lineWidth = 4;

        for (let i = 0; i < edges.length; i++) {
            ctx.beginPath();
            const edge = edges[i];
            const points = [edge.start_pos, edge.end_pos];
            const pos0 = this.getLocalPos(points[0]);
            ctx.moveTo(pos0.x, pos0.y);

            for (let j = 1; j < points.length; j++) {
                const pos = this.getLocalPos(points[j]);
                ctx.lineTo(pos.x, pos.y);
            }
            ctx.closePath();
            ctx.stroke();
        }
    }

    private static drawRays(dirs: Ray[], color: string, len: number) {
        const ctx = this._canvas.getContext("2d");

        ctx.strokeStyle = color;
        ctx.fillStyle = color;
        ctx.lineWidth = 4;

        for (let i = 0; i < dirs.length; i++) {
            const dir = dirs[i];
            const startPoint = dir.origin;
            const endPoint = dir.origin.clone().add(dir.direction.clone().multiplyScalar(len));

            // 获取本地坐标
            const startPos = this.getLocalPos(startPoint);
            const endPos = this.getLocalPos(endPoint);

            // 绘制射线主体
            ctx.beginPath();
            ctx.moveTo(startPos.x, startPos.y);
            ctx.lineTo(endPos.x, endPos.y);
            ctx.stroke();

            // 计算箭头参数
            const arrowLength = 15; // 箭头长度
            const arrowAngle = Math.PI / 6; // 箭头角度 (30度)

            // 计算箭头方向
            const dx = endPos.x - startPos.x;
            const dy = endPos.y - startPos.y;
            const angle = Math.atan2(dy, dx);

            // 绘制箭头
            ctx.beginPath();
            ctx.moveTo(endPos.x, endPos.y);
            ctx.lineTo(
                endPos.x - arrowLength * Math.cos(angle - arrowAngle),
                endPos.y - arrowLength * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(endPos.x, endPos.y);
            ctx.lineTo(
                endPos.x - arrowLength * Math.cos(angle + arrowAngle),
                endPos.y - arrowLength * Math.sin(angle + arrowAngle)
            );
            ctx.stroke();
        }
    }

    private static drawText(text: string, pos: { x: number; y: number }, color: string) {
        const ctx = this._canvas.getContext("2d");

        // 确保获取到有效的上下文
        if (!ctx) {
            console.warn("无法获取Canvas上下文");
            return;
        }

        // 通过坐标转换获取正确的本地坐标
        const localPos = this.getLocalPos(pos);

        // 设置文本样式
        ctx.fillStyle = color;
        ctx.font = "30px Arial, sans-serif"; // 增大字体，添加备用字体
        ctx.textAlign = "center"; // 居中对齐
        ctx.textBaseline = "middle"; // 垂直居中

        // 绘制文本
        ctx.fillText(text, localPos.x, localPos.y);

        // 可选：添加文本边框以提高可见性
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.strokeText(text, localPos.x, localPos.y);
    }
}
