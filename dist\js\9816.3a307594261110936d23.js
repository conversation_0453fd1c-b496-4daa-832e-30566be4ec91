"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[9816],{13457:function(e,n,t){t.d(n,{A:function(){return r}});var r=t(24033).A},49816:function(e,n,t){t.d(n,{A:function(){return q}});var r=t(13274),o=t(84872),i=t(88934),c=t(27347),a=t(27164),u=t(88863),s=t(33100),l=t(61214),f=t(17830),h=t(46909),p=t(13457),d=t(41140),m=t(41594),v=t(64186),y=t(65640);function g(e,n,t,r,o,i,c){try{var a=e[i](c),u=a.value}catch(e){return void t(e)}a.done?n(u):Promise.resolve(u).then(r,o)}function b(e){return function(){var n=this,t=arguments;return new Promise((function(r,o){var i=e.apply(n,t);function c(e){g(i,r,o,c,a,"next",e)}function a(e){g(i,r,o,c,a,"throw",e)}c(void 0)}))}}function x(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function S(e,n){var t,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},c=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return c.next=a(0),c.throw=a(1),c.return=a(2),"function"==typeof Symbol&&(c[Symbol.iterator]=function(){return this}),c;function a(a){return function(u){return function(a){if(t)throw new TypeError("Generator is already executing.");for(;c&&(c=0,a[0]&&(i=0)),i;)try{if(t=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=n.call(e,i)}catch(e){a=[6,e],r=0}finally{t=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}var w=new function e(){!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e);var n=this;x(this,"_GetSchemeListByPagePath","/api/njvr/layoutSchemeMult/listByPage"),x(this,"_InsertSchemePath","/api/njvr/layoutSchemeMult/insert"),x(this,"_DeleteSchemePath","/api/njvr/layoutSchemeMult/delete"),x(this,"_GetLayoutSchemeMultDetailPath","/api/njvr/layoutSchemeMult/get"),x(this,"getLayoutSchemeMultDetail",function(){var e=b((function(e){var t;return S(this,(function(r){switch(r.label){case 0:return[4,(0,v.Ap)({method:"post",url:n._GetLayoutSchemeMultDetailPath,data:{id:e}})];case 1:return(t=r.sent())&&t.success?(y.log(t),[2,t.result]):(y.warn("getLayoutSchemeMultDetail response is null"),[2,null])}}))}));return function(n){return e.apply(this,arguments)}}()),x(this,"loadSchemeList",function(){var e=b((function(e){var t,r,o;return S(this,(function(i){switch(i.label){case 0:return[4,(0,v.Ap)({method:"post",url:n._GetSchemeListByPagePath,data:{originSchemeId:e,page:1,pageSize:10,orderBy:"create_date desc"},timeout:6e4}).catch((function(e){y.error(e)}))];case 1:return(t=i.sent())&&t.success?(null==t||null===(o=t.result)||void 0===o||null===(r=o.result)||void 0===r?void 0:r.length)>0?[2,t.result.result]:[2,[]]:(y.warn("getSchemeListByPage response is null"),[2,[]])}}))}));return function(n){return e.apply(this,arguments)}}()),x(this,"insertScheme",function(){var e=b((function(e,t,r){var o;return S(this,(function(i){switch(i.label){case 0:return[4,(0,v.Ap)({method:"post",url:n._InsertSchemePath,data:{originSchemeId:e,refSchemeId:t,refSchemeName:r}})];case 1:return(o=i.sent())&&o.success?[2,!0]:[2,!1]}}))}));return function(n,t,r){return e.apply(this,arguments)}}()),x(this,"deleteScheme",function(){var e=b((function(e){var t;return S(this,(function(r){switch(r.label){case 0:return[4,(0,v.Ap)({method:"post",url:n._DeleteSchemePath,data:{id:e}})];case 1:return(t=r.sent())&&t.success?[2,!0]:[2,!1]}}))}));return function(n){return e.apply(this,arguments)}}())},j=t(23825);function I(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}function _(){var e=I(["\n            position: absolute;\n            top: ",";\n            right: ",";\n            bottom: ",";\n            width: 218px;\n            background: ",";\n            overflow: hidden;\n            ",";\n            padding: 16px;\n            display: flex;\n            flex-direction: column;\n        "]);return _=function(){return e},e}function A(){var e=I(["\n            font-size: 16px;\n            font-weight: 600;\n            margin-bottom: 16px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            background: ",";\n        "]);return A=function(){return e},e}function P(){var e=I(["\n            font-size: 18px;\n            color: ",";\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            line-height: 1;\n            height: 18px;\n            &:hover {\n                color: ",";\n            }\n        "]);return P=function(){return e},e}function k(){var e=I(["\n            display: flex;\n            flex-direction: column;\n            overflow-y: auto;\n            overflow-x: hidden;\n            flex: 1;\n            width: 100%;\n            align-items: center;\n        "]);return k=function(){return e},e}function L(){var e=I(["\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            width: 100%;\n            cursor: pointer;\n        "]);return L=function(){return e},e}function T(){var e=I(["\n            width: 180px;\n            height: 135px;\n            background-color: #38295710;\n            border-radius: 8px;\n            border: 1px solid ",";\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        "]);return T=function(){return e},e}function N(){var e=I(["\n            width: 180px;\n            height: 135px;\n            background-color: #3829570A;;\n            border-radius: 8px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        "]);return N=function(){return e},e}function E(){var e=I(["\n            width: 180px;\n            height: 135px;\n            background-color: #3829570A;\n            border-radius: 8px;\n            border: 1px dashed ",";\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            cursor: pointer;\n            flex-direction: column;\n            justify-content: center;\n            text-align: center; \n        "]);return E=function(){return e},e}function U(){var e=I(["\n            font-size: 20px;\n        "]);return U=function(){return e},e}function C(){var e=I(["\n            font-size: 12px;\n            margin-top: 8px;\n            display: block;\n            text-align: center;\n            color: gray;\n        "]);return C=function(){return e},e}function B(){var e=I(["\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            width: 180px;\n            height: 36px;\n            padding: 0 6px;\n        "]);return B=function(){return e},e}function M(){var e=I(["\n            font-size: 14px;\n            font-weight: 600;\n            color: ",";\n            padding-left: 6px;\n        "]);return M=function(){return e},e}function O(){var e=I(["\n            color: ",";\n            cursor: pointer;\n            font-size: 14px;\n            display: flex;\n            align-items: center;\n            padding-right: 6px;\n            &:hover {\n                color: ",";\n            }\n        "]);return O=function(){return e},e}function D(){var e=I(["\n            position: absolute;\n            left: 0;\n            right: 0;\n            top: 0;\n            bottom: 0;\n            z-index: 1;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            background: rgba(255, 255, 255, 0.6);\n            .ant-spin {\n                pointer-events: auto;\n            }\n        "]);return D=function(){return e},e}var Z=(0,t(79874).rU)((function(e){var n=e.token,t=e.css;return{schemeListContainer:t(_(),(null===j.fZ||void 0===j.fZ?void 0:(0,j.fZ)())?"64px":"1px",(null===j.fZ||void 0===j.fZ?void 0:(0,j.fZ)())?"68px":"244px",(null===j.fZ||void 0===j.fZ?void 0:(0,j.fZ)())?"12px":"0px",n.colorBgContainer,(null===j.fZ||void 0===j.fZ?void 0:(0,j.fZ)())&&"border-radius: 12px;"),title:t(A(),n.colorBgContainer),closeIcon:t(P(),n.colorTextSecondary,n.colorText),schemeList:t(k()),schemeItem:t(L()),selectedSchemeImage:t(T(),n.colorPrimary),schemeImage:t(N()),addSchemeImage:t(E(),n.colorBorder),addIcon:t(U()),addSchemeText:t(C()),schemeBottom:t(B()),schemeName:t(M(),n.colorText),deleteIcon:t(O(),n.colorTextSecondary,n.colorError),spinContainer:t(D()),textEllipsis:{maxWidth:"140px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",display:"inline-block"}}})),z=t(76330),G=t(9003),W=t(85783);function R(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function F(e,n,t,r,o,i,c){try{var a=e[i](c),u=a.value}catch(e){return void t(e)}a.done?n(u):Promise.resolve(u).then(r,o)}function J(e){return function(){var n=this,t=arguments;return new Promise((function(r,o){var i=e.apply(n,t);function c(e){F(i,r,o,c,a,"next",e)}function a(e){F(i,r,o,c,a,"throw",e)}c(void 0)}))}}function V(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,i=[],c=!0,a=!1;try{for(t=t.call(e);!(c=(r=t.next()).done)&&(i.push(r.value),!n||i.length!==n);c=!0);}catch(e){a=!0,o=e}finally{try{c||null==t.return||t.return()}finally{if(a)throw o}}return i}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return R(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return R(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e,n){var t,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},c=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return c.next=a(0),c.throw=a(1),c.return=a(2),"function"==typeof Symbol&&(c[Symbol.iterator]=function(){return this}),c;function a(a){return function(u){return function(a){if(t)throw new TypeError("Generator is already executing.");for(;c&&(c=0,a[0]&&(i=0)),i;)try{if(t=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=n.call(e,i)}catch(e){a=[6,e],r=0}finally{t=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}var q=(0,d.observer)((function(){var e=(0,G.P)(),n=(0,W.B)().t,t=Z(),d=t.styles,v=t.cx,y=V((0,m.useState)(!1),2),g=y[0],b=y[1],x=V((0,m.useState)(!1),2),S=x[0],j=x[1],I=V((0,m.useState)(""),2),_=I[0],A=I[1],P=V((0,m.useState)(""),2),k=P[0],L=P[1],T=V((0,m.useState)(""),2),N=T[0],E=T[1],U=c.nb.instance.layout_container,C=V((0,m.useState)([]),2),B=C[0],M=C[1];(0,m.useEffect)((function(){var e=function(){var e=J((function(e){var n;return $(this,(function(t){return null==e||e==g||(b(!!e),n=U._layout_scheme_id,A(n)),[2]}))}));return function(n){return e.apply(this,arguments)}}();c.nb.on(i.U.setMultiSchemeListVisible,e);var n=function(){var e=J((function(){var e;return $(this,(function(n){return(e=U._layout_scheme_id)?(e!=_&&A(e),[2]):(b(!1),[2])}))}));return function(){return e.apply(this,arguments)}}();return c.nb.on(i.U.RoomList,n),function(){c.nb.off(i.U.setMultiSchemeListVisible),c.nb.off(i.U.RoomList)}})),(0,m.useEffect)((function(){g?O(_):(M([]),L(""),E(""),A(""))}),[_,g]),(0,m.useEffect)((function(){e.homeStore.showWelcomePage&&b(!1)}),[e.homeStore.showWelcomePage]);var O=function(){var e=J((function(e){var n;return $(this,(function(t){switch(t.label){case 0:return[4,o.D.getLayoutSchemeById(e)];case 1:return(null==(n=t.sent())?void 0:n.projectId)?(L(n.projectId),[4,D(n.projectId)]):[3,3];case 2:return t.sent(),[3,4];case 3:M([]),t.label=4;case 4:return[2]}}))}));return function(n){return e.apply(this,arguments)}}(),D=function(){var e=J((function(e){var n,t,r;return $(this,(function(i){switch(i.label){case 0:j(!0),i.label=1;case 1:return i.trys.push([1,,4,5]),[4,w.loadSchemeList(e)];case 2:return n=i.sent(),[4,o.D.getLayoutSchemeById(e)];case 3:return(t=i.sent())&&(r={id:t.id,originSchemeId:t.projectId,refSchemeId:t.projectId,refSchemeName:t.layoutSchemeName,imageUrl:new URL(t.coverImage).pathname.replace(/^\/+/,"")},E(t.layoutSchemeName),n.push(r)),M(n),[3,5];case 4:return j(!1),[7];case 5:return[2]}}))}));return function(n){return e.apply(this,arguments)}}(),R=function(){var e=J((function(e){var t;return $(this,(function(r){switch(r.label){case 0:return[4,w.deleteScheme(e)];case 1:return r.sent()?(s.A.success({content:n("删除成功").toString(),style:{marginTop:"4vh"}}),t=U._layout_scheme_id,O(t)):s.A.error({content:n("删除失败").toString(),style:{marginTop:"4vh"}}),[2]}}))}));return function(n){return e.apply(this,arguments)}}(),F=function(){var e=J((function(){var e,t,r;return $(this,(function(o){switch(o.label){case 0:return B.length>=6?(s.A.warning({content:n("最多只能关联5个方案").toString(),style:{marginTop:"4vh"}}),[2]):U._layout_scheme_id?0==U._room_entities.length?(s.A.warning({content:n("当前方案为空，无法保存！").toString(),style:{marginTop:"4vh"}}),[2]):(e=k||U._layout_scheme_id,t=q(),c.nb.on(i.U.SaveProgress,function(){var t=J((function(t){var r;return $(this,(function(o){switch(o.label){case 0:return"ongoing"==(null==t?void 0:t.progress)?[2]:"fail"==(null==t?void 0:t.progress)?(s.A.error({content:n("方案保存失败").toString(),style:{marginTop:"4vh"}}),[2]):[4,w.insertScheme(e,t.id,t.name)];case 1:return o.sent()?s.A.success({content:n("方案关联成功").toString(),style:{marginTop:"4vh"}}):s.A.error({content:n("方案关联失败").toString(),style:{marginTop:"4vh"}}),r=U._layout_scheme_id,A(r),[2]}}))}));return function(e){return t.apply(this,arguments)}}()),r={schemename:t,username:"",telephone:"",address:""},[4,U.saveSchemeLayout2JsonAs(r)]):(s.A.warning({content:n("请先保存方案").toString(),style:{marginTop:"4vh"}}),[2]);case 1:return o.sent(),c.nb.off(i.U.SaveProgress),[2]}}))}));return function(){return e.apply(this,arguments)}}(),q=function(){var e=N||U._layout_scheme_name,t=B.length>0?B.length:1;return n("副本")+t+"-"+e},H=function(){var e=J((function(e){return $(this,(function(t){switch(t.label){case 0:return c.nb.on(i.U.SaveProgress,function(){var t=J((function(t){var r;return $(this,(function(a){switch(a.label){case 0:return"ongoing"==(null==t?void 0:t.progress)?[2]:"fail"==(null==t?void 0:t.progress)?(s.A.error({content:n("方案保存失败").toString(),style:{marginTop:"4vh"}}),c.nb.off(i.U.SaveProgress),[2]):(s.A.success({content:n("方案自动保存成功").toString(),style:{marginTop:"4vh"},duration:1}),[4,o.D.getLayoutSchemeById(e.refSchemeId)]);case 1:return r=a.sent(),c.nb.DispatchEvent(c.n0.OpenMyLayoutSchemeData,r),c.nb.off(i.U.SaveProgress),[2]}}))}));return function(e){return t.apply(this,arguments)}}()),[4,U.saveSchemeLayout2Json()];case 1:return t.sent(),[2]}}))}));return function(n){return e.apply(this,arguments)}}();return g?(0,r.jsxs)("div",{className:d.schemeListContainer,children:[(0,r.jsxs)("div",{className:d.title,children:[n("布局方案"),(0,r.jsx)(a.A,{iconClass:"iconclose1",onClick:function(){b(!1)},className:d.closeIcon})]}),(0,r.jsx)("div",{className:d.schemeList,children:(0,r.jsxs)(l.A,{spinning:S,size:"large",children:[(0,r.jsx)("div",{className:d.schemeItem,children:(0,r.jsxs)("div",{className:d.addSchemeImage,onClick:F,children:[(0,r.jsx)(z.A,{className:d.addIcon,type:"icon-tianjiasucai"}),(0,r.jsx)("span",{className:d.addSchemeText,children:n("新增方案")})]})}),B.length>0?B.map((function(e){return(0,r.jsxs)("div",{className:d.schemeItem,children:[(0,r.jsx)("div",{className:v(d.schemeImage,(t={},o=d.selectedSchemeImage,i=e.refSchemeId===U._layout_scheme_id,o in t?Object.defineProperty(t,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[o]=i,t)),onClick:function(){return H(e)},style:{cursor:"pointer"},children:e.imageUrl?(0,r.jsx)(f.A,{preview:!1,src:new URL(e.imageUrl,u.hl).href,width:180,height:140,style:{objectFit:"contain",padding:"10px",userSelect:"none",pointerEvents:"none",WebkitUserSelect:"none",WebkitTouchCallout:"none"}}):(0,r.jsx)(f.A,{width:180,height:140,src:"error",style:{objectFit:"contain",padding:"10px",userSelect:"none",pointerEvents:"none",WebkitUserSelect:"none",WebkitTouchCallout:"none"}})}),(0,r.jsxs)("div",{className:d.schemeBottom,children:[(0,r.jsx)(h.A,{title:e.refSchemeName,children:(0,r.jsx)("span",{className:"".concat(d.schemeName," ").concat(d.textEllipsis),children:e.refSchemeId==k?n("原始方案"):e.refSchemeName})}),e.refSchemeId!=k&&(0,r.jsx)(a.A,{iconClass:"icondelete",onClick:function(n){n.stopPropagation(),R(e.id)},className:d.deleteIcon})]})]},e.id);var t,o,i})):(0,r.jsx)(p.A,{image:p.A.PRESENTED_IMAGE_SIMPLE,description:n("暂无关联方案")})]})})]}):null}))}}]);