import { Vector3 } from "three";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>gon, ZRectShapeType } from "@layoutai/z_polygon";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ist, TRoomEdgeProp, TsAI_app } from "../IRoomInterface";
import { TRoom } from "../TRoom";
import { I_TRoom , TRoomShape } from "../TRoomShape";
import { TFeatureShape } from "./TFeatureShape";



export class T_8V_Shape extends TFeatureShape
{
    constructor()
    {
        super();
    }
    checkPolyFeature(poly : ZPolygon)
    {
        return true;
    }
    
    getFitVals()
    {
        return [
                [[1,0],[0,1],[1,0],[0,-1],[-1,0],[0,-1],[-1,0],[0,1]],
                [[1,0],[0,1],[1,0],[0,1],[-1,0],[0,-1],[-1,0],[0,-1]]
            ];   
    }
    fitPolygon(poly:ZPolygon)
    {
        if(poly.vertices.length != 8 || poly.edges.length != 8)
        {
            return null;
        }

        let bbox = poly.computeBBox();

        let size = bbox.getSize(new Vector3());

        let nor = new Vector3(1,0,0);
        if(size.y > size.x)
        {
            nor.set(0,1,0);
        }

        let back_edge :ZEdge = null;
        for(let edge of poly.edges)
        {
            if(Math.abs(edge.dv.dot(nor)) <0.1)
            {
                if(!back_edge || edge.length > back_edge.length)
                {
                    back_edge = edge;
                }
            }
        }

        if(!back_edge)
        {
            console.log("Error back edge not found!",this._room._t_id);
            
            return null;
        }
        let s_shape = new ZPolygon();
        
        let id = poly.edges.indexOf(back_edge);

        try {
            if(back_edge.next_edge.length < back_edge.prev_edge.length)
            {
                let vlist :Vector3[] = [];
                for(let i=0; i < 8; i++)
                {
                    let t_id = (id + i) % 8;
                    let t_edge = poly.edges[t_id];
                    vlist.push(t_edge.v1.pos.clone());
    
                }
                s_shape.initByVertices(vlist);
                s_shape.computeBBox();
            }
            else {
                let vlist :Vector3[] = [];
                for(let i=0; i < 8; i++)
                {
                    let t_id = (id + 8 - i) % 8;
                    let t_edge = poly.edges[t_id];
                    vlist.push(t_edge.v0.pos.clone());
    
                }
                s_shape.initByVertices(vlist);
                s_shape.computeBBox(); 
            }
      
        } catch (error) {
            console.log(error);
            console.log(poly.edges.length,id);
        }

        let dv = s_shape.edges[0].dv;
        let dn = s_shape.edges[1].dv;

        let check_vals = this.getFitVals();
        let tvals  = [];
        for(let i=0; i < s_shape.edges.length; i++)
        {
            let edge = s_shape.edges[i];
            let tval0 = Math.round(edge.dv.dot(dv));
            let tval1 = Math.round(edge.dv.dot(dn));

            tvals.push([tval0,tval1]);
           
        }
        let flag = false;

        // TsAI_app.log(JSON.stringify(tvals),JSON.stringify(check_vals),this.shape_code);
        for(let c_vals of check_vals)
        {
            let t_flag = true;
            for(let i=0; i < c_vals.length; i++)
            {
                let t_dist0 = Math.abs(tvals[i][0]-c_vals[i][0]);
                let t_dist1 = Math.abs(tvals[i][1]-c_vals[i][1]);
                let t_err = Math.max(t_dist0,t_dist1);
                if(t_err > 0.5)
                {
                    t_flag = false;
                }
            }
            if(t_flag)
            {
                flag = true;
            }
        }

        // TsAI_app.log(flag);
        if(!flag){
            return null;
        }
        if(!this.checkPolyFeature(s_shape))
        {
            return null;
        }



        return s_shape;
    }
    findFeatureShape(room:I_TRoom): number {

        let target_poly : ZPolygon = null;
        this._poly = null;
        let target_shape : TRoomShape = null;
        this._room_shapes = [];
        this._room = room;
        if(room.shape_list.length==0) return 0;
        for(let shape of room.shape_list)
        {
            let ans = this.fitPolygon(shape._poly);
            if(ans)
            {
                // let sub_shapes : TRoomShape[] = [];
                // shape.visitContainedShapes(sub_shapes);
                // let flag = true;
                // for(let sub_shape of sub_shapes)
                // {
                //     if(sub_shape._area_type === TRoomAreaType.Hallway)
                //     {
                //         flag = false;
                //     }
                // }
                // if(!flag) continue;
                if(!target_poly || (target_poly.edges[6].length < ans.edges[6].length))
                {
                    target_poly = ans;
                    target_shape = shape;
                }
            }
        }

        if(target_poly)
        {
            this._poly = target_poly;
            this._room_shapes.push(target_shape);
            return this.getFitAreaScore();
        }
        return 0;
    }

    _updateShapeEdgeProp(): void {
        super._updateShapeEdgeProp();


        let target_edge : ZEdge = null;
        let back_edge = this._poly.backEdge;
        let front_edge = this._poly.edges[3];

        // console.log(front_edge,back_edge);
        if(front_edge.dv.dot(back_edge.dv) > -0.8) return;

        
        if(front_edge.getProperty(TRoomEdgeProp.WindowWeight) > back_edge.getProperty(TRoomEdgeProp.WindowWeight) + 0.1)
        {
            target_edge = front_edge;
        }
        if(!target_edge) return;

        if(target_edge.getProperty(TRoomEdgeProp.WindowWeight) > 1.3)
        {
            let next_edge = target_edge.next_edge;
            let id = this._poly.edges.indexOf(next_edge);

            let p_size = this._poly.edges.length;

            let res_edges : ZEdge[] = [];
            for(let i=0; i < p_size; i++)
            {
                res_edges.push(this._poly.edges[(id+i)%p_size]);
            }

            this._poly.edges.length = 0;  
            for(let edge of res_edges)
            {
                this._poly.edges.push(edge);
            }
            this._poly._updateVerticesByEdgesList();
            this._poly.computeZNor();
        }

    }
}