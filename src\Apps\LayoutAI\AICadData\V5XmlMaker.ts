import { TLayoutEntityContainer } from "../Layout/TLayoutEntities/TLayoutEntityContainter";
import { LayoutContainerUtils } from "../Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { ZRect } from "@layoutai/z_polygon";
import { V5XmlTemplate } from "./V5XmlTemplate";

export class V5XmlMaker {
    _xml_doc: XMLDocument;

    _scheme_root: Element;
    _wallInfo_Ele: Element;

    _last_uid: number;
    constructor()
    {

    }
    protected reInitXml()
    {
        this._last_uid = 15;
        this._xml_doc = (new DOMParser()).parseFromString(V5XmlTemplate, 'application/xml');
        this._wallInfo_Ele = this._xml_doc.getElementsByTagName("WallInfo")[0];
        this._wallInfo_Ele.innerHTML = '';
    }
    private makeXmlMWallElement(rect: ZRect, uidN: number, storeyHeight: number) {
        let mWall = this._xml_doc.createElement("WallData");
        let wall_thickness = rect._h;
        let start_pos = rect.unproject({x:-rect._w/2,y:0});
        let end_pos = rect.unproject({x:rect._w/2,y:0});
        let attributes:any = {
            uidS : uidN,
            StartXS : Math.round(start_pos.x),
            StartYS : Math.round(start_pos.y),
            EndXS : Math.round(end_pos.x),
            EndYS : Math.round(end_pos.y),
            ThicknessS :wall_thickness,
            HeightS : storeyHeight,
            IsBearS : "false",
            wallUIDS : uidN,
            IsLockedS : "false",
            isHalfWallS : "false",
            bulgeS : 0
        }
        for(let key in attributes)
        {
            mWall.setAttribute(key,''+attributes[key]);
        }

        return mWall;
    }
    public makeByEntities(container: TLayoutEntityContainer) {
        this.reInitXml();
        this._last_uid = LayoutContainerUtils.getMaximalEntityUid(container);
        this._last_uid = LayoutContainerUtils.rectifyEntityUid(this._last_uid);

        for (let entity of container._wall_entities) {
            let rect = entity.rect;
            let mwall = this.makeXmlMWallElement(rect, entity.uidN, container._storey_height);
            this._wallInfo_Ele.appendChild(mwall);
            rect.ex_prop['uidN'] = '' + entity.uidN;
        }


        // for (let entity of container._structure_entities) {
        //     let rect = entity.rect;
        //     let uidN = entity.uidN;
        //     if (entity.realType == "Flue") {
        //         let ele = this.makeXmlFlueElement(rect, uidN);

        //         this._bim_elements.appendChild(ele);
        //     }
        //     else if (entity.realType == "Pillar" || entity.realType == "Platform" || entity.realType == "Envelope_Pipe") {
        //         let ele = this.makeXmlStructureElement(rect, entity.height, uidN, entity.realType.toLocaleLowerCase());

        //         this._bim_elements.appendChild(ele);
        //     }
        //     else if (entity.realType == "Beam") {
        //         let ele = this.makeXmlBeamElement(rect, entity.height, uidN);
        //         this._bim_elements.appendChild(ele);
        //     }
        // }

        // for (let entity of container._room_entities) {
        //     if (entity._area < 0.01) continue;
        //     if (entity._room_poly.orientation_z_nor.z > 0) continue;
        //     let room_name = entity.name;
        //     let room_poly = entity._room_poly;

        //     let center = entity._main_rect.rect_center || room_poly.computeBBox().getCenter(new Vector3());
        //     let room_ele = this.makeXmlRoomElement(entity.uidN, center, entity._hasFloorFootLineB ? "T" : "F", entity._hasCeilingFootLineB ? "T" : "F");
        //     this._bim_elements.appendChild(room_ele);
        // }

        // for (let entity of container._room_entities) {
        //     if (entity._area < 0.01) continue;
        //     if (entity._room_poly.orientation_z_nor.z > 0) continue;

        //     let room_name = entity.name;

        //     let room_size = entity._area;
        //     let center = entity._main_rect.rect_center;

        //     let room_area_ele = this.makeRoomAreaElement(room_name, `${center.x},${center.y}`, room_size, entity._name_uid, entity.uidN);
        //     this._bim_elements.appendChild(room_area_ele);

        //     let room_extra = this.makeRoomCurvesElement(entity.uidN, entity._main_rect);
        //     this._bim_extra.appendChild(room_extra);
        // }
        // let window_door_entities = this._xml_doc.createElement("Entitys")
        // this._furniture_door_window.appendChild(window_door_entities);
        // for (let win of container._window_entities) {
        //     let wall_rect = win._wall_rect;
        //     if (!wall_rect) continue;
        //     let wall_entity = TWall.getOrMakeEntityOfCadRect(wall_rect);
        //     if (!wall_entity) continue;
        //     let rect = win.rect;

        //     let mirror = win.mirror;
        //     let rotate_z = win.rotate_z;
        //     let t_center = rect.rect_center;

        //     if (win.realType === "BayWindow") // 飘窗要特殊处理一下
        //     {
        //         if (wall_entity) {
        //             let pp = wall_entity.rect.project(t_center);
        //             t_center = wall_entity.rect.unproject({ x: pp.x, y: 0 });
        //         }
        //         rotate_z += Math.PI;

        //     }

        //     let ele = this.makeXmlWindoorElement(win.uidN, wall_entity.uidN, {
        //         line_center: t_center.toArray(),
        //         length: win.length, width: win.depth, mirror: mirror, angle: rotate_z, label: win.realType, type: win.type
        //     });
        //     window_door_entities.appendChild(ele);
        // }
        return this._xml_doc;
    }
}