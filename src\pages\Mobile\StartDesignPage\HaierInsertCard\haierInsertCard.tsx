import React, { useEffect } from 'react';
import useStyles from './style';
import search from '../img/search.svg';
import img from '../img/img.svg';
import CAD from '../img/CAD.svg';
import canvas from '../img/canvas.png';
import { ProCard } from '@ant-design/pro-components';
import { useCallback } from 'react';
import type { RcFile } from '@svg/antd/es/upload';
import { message, type MenuProps } from '@svg/antd';
import { checkIsMobile } from '@/config';
import useUploader from '../hooks/useUploader';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import { useStore } from '@/models';
import { SdkService } from '@/services/SdkService';
import { AI2DesignBasicModes } from '@/Apps/AI2Design/AI2DesignManager';
enum CardItem {
  Search = 'search',
  Upload = 'upload',
  CAD = 'CAD',
  Draw = 'draw',
}

interface HaiErInsertCard {
  toSelectHX: () => void;
}
const HaiErInsertCard: React.FC<HaiErInsertCard> = ({ toSelectHX }) => {
  const { styles } = useStyles();
  const { saveImageToDB, saveDwgToDB, saveInfoToDB } = useUploader();
  const store = useStore();
  // const [HxFormStatus, setHxFormStatus] = useState<0 | 1 | 2 | 3>(0);

  const List = [
    {
      key: CardItem.Search,
      title: '搜索户型图',
      description: '从户型库中搜索户型',
      img: search,
      styles: '#E1F2FF',
    },
    {
      key: CardItem.Upload,
      title: '上传临摹图',
      description: '通过照片草图进行AI生成',
      img: img,
      styles: '#FFF6DE',
    },
    {
      key: CardItem.CAD,
      title: '导入CAD图纸',
      description: '识别图纸生成准确户型',
      img: CAD,
      styles: '#FFEFEB',
    },
    {
      key: CardItem.Draw,
      title: '自由绘制',
      description: '新建画布进行设计',
      img: canvas,
      styles: '#E6E2FF',
    },
  ];

  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
  });

  const handleUploadImg = (hasForm?: boolean) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (event: any) => {
      const files = event.target.files;
      if (files.length > 0) {
        const file = files[0];
        const preview = await getBase64(file);
        if(preview)
        {
          message.success('上传成功');
          SdkService.clickTag('点击上传临摹图', 2, preview);
        }
      }
    };
    input.click();
  };

  const handleUploadDwg = useCallback((hasForm?: boolean) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.dwg';
    input.onchange = async (event: any) => {
      const files = event.target.files;
      if (files.length > 0) {
        const file = files[0];
        const preview = await getBase64(file);
        if(preview)
        {
          message.success('上传成功');
          SdkService.clickTag('点击上传临摹图', 3, preview);
        }
      }
    };
    input.click();
    
  }, []);


  const handleClick = (key: CardItem) => () => {
    if (key === CardItem.Search) {
      /**
       * 发送消息给海尔
       * mode: 1 搜索户型图
       * mode: 2 上传临摹图
       */
      SdkService.clickTag('点击搜索户型图', 1, null);
    } else if (key === CardItem.Upload) {
      handleUploadImg(false);
    } else if (key === CardItem.CAD) {
      handleUploadDwg(false);
    } else if (key === CardItem.Draw) {
      SdkService.clickTag('点击自由绘制', 4, null);
    }
    
  };


  useEffect(() => {
    LayoutAI_App.on(EventName.openHxSearch, (event) => {
      toSelectHX();
    });
    LayoutAI_App.on(EventName.openCopyImage, (event) => {
      handleUploadImg(false);
    });

  }, []);

  return (
    <div className={styles.cardContainer}>
      {List.map((item) => (
        <ProCard
          style={{ margin: 20, cursor: 'pointer' }}
          key={item.key}
          // hoverable
          className={styles.card}
          onClick={handleClick(item.key)}
          bodyStyle={{ padding: '15px 20px', height: '72px', background: item.styles }}
        >
          <div className={styles.content}>
            <div className=''>
              <img src={item.img} alt="img" />
            </div>
            <div className={styles.right}>
              <div className={styles.title}>{item.title}</div>
              <div className={styles.desc}>{item.description}</div>
            </div>
          </div>
        </ProCard>
      ))}
    </div>
  );
};

export default HaiErInsertCard;
