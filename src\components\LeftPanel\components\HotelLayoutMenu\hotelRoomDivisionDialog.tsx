import React, { useEffect, useState } from "react";
import useStyles from './style/index';
import { Button } from "@svg/antd";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { EventName } from "@/Apps/EventSystem";
import { LayoutSchemeXmlJsonParser } from "@/Apps/LayoutAI/Layout/TLayoutEntities/loader/LayoutSchemeXmlJsonParser";
import { HotelRoomDivisionService } from "@/Apps/LayoutAI/Services/Hotel/HotelRoomDivisionService";
import RoomDivisionConfigEditor from "./hotelRoomConfig";
import HotelRoomConfig from "./hotelRoomConfig";
const initialConfig = {
    roomSize: [
      {
        type: "标单/标双动态尺寸链",
        minArea: 29.5,
        maxArea: 33.5,
        minW: 3.8,
        maxW: 5.0,
        minL: 6.0,
        maxL: 7.9,
        minW_L: 0,
        maxW_L: 0.5,
        rate: 0.6,
        character: "窄开间长条形"
      },
      {
        type: "标单/标双动态尺寸链",
        minArea: 32.5,
        maxArea: 36.5,
        minW: 4.5,
        maxW: 6.0,
        minL: 5.5,
        maxL: 7.3,
        minW_L: 0.667,
        maxW_L: 0.833,
        character: "均衡矩形"
      },
      {
        type: "套房/亲子房动态尺寸链",
        minArea: 49.5,
        maxArea: 53.5,
        minW: 5.8,
        maxW: 7.8,
        minL: 6.4,
        maxL: 9.1
      }
    ],
    wallThick: 120
  };

export const HotelRoomDivisionDialog :React.FC = ()=>{
    const { styles } = useStyles();
    const [isVisible,setIsVisible] = useState<boolean>(false);
    const [currentConfig, setCurrentConfig] = useState(initialConfig);

    const onConfirm = async ()=>{
        let jsonScheme = LayoutSchemeXmlJsonParser.toXmlSchemeData();
        let text = JSON.stringify(jsonScheme);
        setIsVisible(false);

        // let res = await HotelRoomDivisionService.instance.divisionRoom(text);

        // console.log(res);


        // if(res)
        // {
        //     let t = LayoutAI_App.instance.Configs.saving_localstorage_layout_scheme;
        //     LayoutAI_App.instance.Configs.saving_localstorage_layout_scheme = false;
        //     LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(res,{auto_layout:false});
        //     LayoutAI_App.instance.Configs.saving_localstorage_layout_scheme = t;
        // }

    }
    const handleConfigConfirm = (config:any) => {
        console.log('配置数据:', config);
        // 处理配置数据
        onConfirm();
      };
    
    useEffect(()=>{
        LayoutAI_App.on(EventName.ShowHotelDivisionDialog, (t:boolean)=>{


            
            setIsVisible(t||false);


        })
    },[])
    if(!isVisible)
    {
        return <></>
    }
    return <div className={styles.dialog_container}>
        <HotelRoomConfig onConfirm={handleConfigConfirm} onClose={()=>setIsVisible(false)}></HotelRoomConfig>
    </div>
}

export default HotelRoomDivisionDialog;