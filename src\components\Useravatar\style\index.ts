import { createStyles } from '@svg/antd/es/theme/utils';
export default createStyles(({ css }) => {
  return {
    container: css`
        background-color: #fff; 
        width: 200px;
        height: 110px; 
        padding: 10px; 
        text-align: center; 
    `,
    top: css`
        display: flex;
        align-items: center;
        img{
            margin-right: 10px;
        }
    `,
    line:css`
        height: 1px;
        width: 100%;
        background-color: #e6e6e6;
        margin: 10px 0;
    `,
    item: css`
        position: relative;
        height: 32px;
        padding: 0 12px;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        cursor: pointer;
        margin: 2px 0;  
        :hover{
            background-color: #f2f3f5;
        }
    `,
  }
});
