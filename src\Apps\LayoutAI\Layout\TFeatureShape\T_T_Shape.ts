import { ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { <PERSON><PERSON>idth, MinHallwayWidth, TRoomEdgeProp } from "../IRoomInterface";
import { T_8V_Shape } from "./T_8V_Shape";

export class T_T_Shape extends T_8V_Shape
{
    constructor()
    {
        super();
        this.shape_code = "T";
        this._order = 1;

    }

    getFitVals() {
        return [
                [[1,0],[0,1],[1,0],[0,-1],[1,0],[0,-1],[-1,0],[0,1]],
                [[1,0],[0,1],[1,0],[0,1],[-1,0],[0,1],[-1,0],[0,-1]]
            ];
    }

    checkPolyFeature(poly : ZPolygon)
    {

        for(let edge of poly.edges)
        {
            if(edge.length < MinHallwayWidth * 1.5)
            {
                return false;
            }
        }
        if(poly.backEdge.length > HallwayWidth * 2.)
        {
            for(let edge of poly.edges)
            {
                if(edge.length < HallwayWidth)
                {
                    return false;
                }
            }
        }


        return true;
    }

    _resortPolyEdges(): void {
        if(!this._contours) return;
        let poly = this._poly;
    
        let t_edges : ZEdge[] = [];

        for(let edge of poly.edges)
        {
            t_edges.push(edge);
        }

        t_edges.sort((a,b)=>b.getProperty(TRoomEdgeProp.WindowWeight) - a.getProperty(TRoomEdgeProp.WindowWeight));

        let target_edge = t_edges[0];

        if(target_edge.getProperty(TRoomEdgeProp.WindowWeight) > 1.3)
        {
            let t_id = poly.edges.indexOf(target_edge);
            // TsAI_app.log(t_id,t_edges);
            if(t_id == 5)
            {
                let next_edge = target_edge.prev_edge;
                let id = poly.edges.indexOf(next_edge);

                let p_size = poly.edges.length;

                let res_edges : ZEdge[] = [];
                for(let i=0; i < p_size; i++)
                {
                    res_edges.push(poly.edges[(id-i+p_size)%p_size]);
                }

                poly.edges.length = 0;  
                for(let edge of res_edges)
                {
                    let tmp = edge.v0;
                    edge.v0 = edge.v1;
                    edge.v1 = tmp;

                    poly.edges.push(edge);
                }
                poly._updateEdgeNeighbors();
                poly._updateVerticesByEdgesList();
                poly.computeZNor();

                // TsAI_app.log(poly);

                
            }

        }
        
    }
    _updateShapeEdgeProp(): void {
        super._updateShapeEdgeProp();


        this._resortPolyEdges();

        

    }
}