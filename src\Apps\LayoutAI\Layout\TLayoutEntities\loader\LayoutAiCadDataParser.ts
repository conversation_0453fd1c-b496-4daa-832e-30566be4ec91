import { I_SwjBaseGroup, I_SwjCabinetData, I_SwjEntityBase, I_SwjFurnitureData, I_SwjFurnitureGroup, I_SwjLineEdge, I_SwjRemodelingRequestData, I_SwjRoom, I_SwjWall, I_SwjWindow, I_SwjXmlScheme, SchemeSourceType }
 from "../../../AICadData/SwjLayoutData";
import { AI_CadData } from "@/Apps/LayoutAI/AICadData/AI_CadData";

import { TLayoutEntityContainer } from "../TLayoutEntityContainter";
import { LayoutAI_App, LayoutAI_Version } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TWall } from "../TWall";
import { Box3, Mesh, Vector3 } from "three";
import { TFurnitureEntity } from "../TFurnitureEntity";
import { TRoomEntity } from "../TRoomEntity";
import { TStructureEntity } from "../TStructureEntity";
import { TWindowDoorEntity } from "../TWinDoorEntity";
import { TRoom } from "../../TRoom";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { LayoutContainerUtils } from "../utils/LayoutContainerUtils";
import { ZRect } from "@layoutai/z_polygon";
import { get_window_of_win_rect } from "@/Apps/LayoutAI/AICadData/SwjDataBasicFuncs";



export class LayoutAiCadDataParser
{
    static Container : TLayoutEntityContainer = null;
    static fromAiCadData(data: AI_CadData) {
        if(!LayoutAiCadDataParser.Container)
        {
            LayoutAiCadDataParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let scope = LayoutAiCadDataParser.Container;
        scope._ai_cad_data = data;
        scope._layout_scheme_id = null;
        scope._scheme_id = "";
        scope._scheme_name = "";
        scope._outter_border_polygons = null;
        LayoutAiCadDataParser._updateEntitiesByAiCadData();
    }
    /**
     *  这个函数会减少使用, 生成rooms要通过Entities
     */
    static  makeRoomsFromAiCadData() {
        if(!LayoutAiCadDataParser.Container)
        {
            LayoutAiCadDataParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let scope = LayoutAiCadDataParser.Container;
        scope._rooms = [];
        scope._ai_cad_data.updateRoomWindowDoorAndStructures();

        let ai_cad_data = scope._ai_cad_data;


        for (let room_poly of ai_cad_data._room_polys) {

            if (room_poly.orientation_z_nor.z > 0) continue;
            let room_name = AI_CadData.get_room_poly_name(room_poly);
            let points: number[][] = [];
            for (let v of room_poly.vertices) {
                points.push([v.pos.x, v.pos.y, v.pos.z]);
            }
            let room = new TRoom({
                schemaName: "",
                roomname: room_name,
                points: points
            });
            let entity = TRoomEntity.getOrMakeEntityOfCadRect(room_poly as ZRect) as TRoomEntity;

            room.name = entity.name;
            room.roomname = entity.roomname;
            room.room_size = '' + (entity as TRoomEntity)._area.toFixed(2);
            room._t_id = scope._rooms.length;
            room.id = room._t_id;
            room.room_id = room.id;


            for (let win of entity._win_rects) {
                let window = get_window_of_win_rect(win);
                if (window) {
                    room.windows.push(window);

                }
            }

            scope._rooms.push(room);
            room.updateFeatures();

            for (let figure_rect of scope._ai_cad_data._figure_rects) {
                if (AI_CadData.is_deleted(figure_rect)) continue;
                if (room_poly.containsPoint(figure_rect.rect_center)) {
                    let entity = TFurnitureEntity.getOrMakeEntityOfCadRect(figure_rect) as TFurnitureEntity;
                    room.addFurnitureElement(entity.figure_element);
                }
            }
        }

        scope._furniture_entities = [];
        scope._ai_cad_data._figure_rects = [];
    }

    static fromRoomsToAiCadData(rooms: TRoom[]) {
        if(!LayoutAiCadDataParser.Container)
        {
            LayoutAiCadDataParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let scope = LayoutAiCadDataParser.Container;
        // 如果不存在AI_CAD_DATA, 就要从房间信息来生成数据
        if (!scope._ai_cad_data) {
            scope._ai_cad_data = new AI_CadData();

        }

        scope._furniture_entities = []; //清空当前的图元信息

        for (let room of rooms) {
            let figure_elements = room._furniture_list;
            let group_figure_elements: TFigureElement[] = [];


            for (let figure_ele of figure_elements) {

                let rect = figure_ele.rect;

                rect._attached_elements[TFigureElement.EntityName] = figure_ele;

                let entity = AI_CadData.get_furniture_entity_of_rect(rect);

                scope._furniture_entities.push(entity);
            }

        }

        LayoutAiCadDataParser.toAiCadData();


    }

    /**
     * 更新AI_CadData的数据
     * @param data  
     */
    static toAiCadData(data: AI_CadData = null) {
        if(!LayoutAiCadDataParser.Container)
        {
            LayoutAiCadDataParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let scope = LayoutAiCadDataParser.Container;
        if (!data) {
            if (!scope._ai_cad_data) {
                scope._ai_cad_data = new AI_CadData();
            }
            data = scope._ai_cad_data;
        }

        // 用length = 0 清空, 保持原始指针不变
        data._room_polys.length = 0;
        data._wall_rects.length = 0;
        data._window_rects.length = 0;
        data._strucutre_rects.length = 0;
        data._figure_rects.length = 0;
        for (let room_poly of scope._room_entities) {
            data._room_polys.push(room_poly._room_poly);
        }

        for (let wall of scope._wall_entities) {
            data._wall_rects.push(wall._rect);
        }

        for (let win of scope._window_entities) {
            data._window_rects.push(win.rect);
        }

        for (let entity of scope._structure_entities) {
            data._strucutre_rects.push(entity.rect);
        }

        for (let figure of scope._furniture_entities) {
            data._figure_rects.push(figure.rect);
        }

        data.updateWindowsInWall();

        // 同步一下timestamp
        data._room_structure_update_timestamp = ++AI_CadData._timestamp;
        scope._current_room_structure_update_timestamp = data._room_structure_update_timestamp;



    }


    static _updateEntitiesByAiCadData() {
        if(!LayoutAiCadDataParser.Container)
        {
            LayoutAiCadDataParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let scope = LayoutAiCadDataParser.Container;
        let data = scope._ai_cad_data;
        if (!data) return;

        scope.init();
        for (let room_poly of data._room_polys) {
            if (AI_CadData.is_deleted(room_poly)) continue;
            if (room_poly.orientation_z_nor.z > 0) continue;

            scope._room_entities.push(TRoomEntity.getOrMakeEntityOfCadRect(room_poly as ZRect) as TRoomEntity);
        }

        for (let wall of data._wall_rects) {
            if (AI_CadData.is_deleted(wall)) continue;
            scope._wall_entities.push(TWall.getOrMakeEntityOfCadRect(wall) as TWall);
        }

        for (let win of data._window_rects) {
            if (AI_CadData.is_deleted(win)) continue;
            scope._window_entities.push(TWindowDoorEntity.getOrMakeEntityOfCadRect(win) as TWindowDoorEntity)
        }

        for (let rect of data._strucutre_rects) {
            if (AI_CadData.is_deleted(rect)) continue;
            scope._structure_entities.push(TStructureEntity.getOrMakeEntityOfCadRect(rect) as TStructureEntity);
        }

        for (let rect of data._figure_rects) {
            if (AI_CadData.is_deleted(rect)) continue;
            scope._furniture_entities.push(TFurnitureEntity.getOrMakeEntityOfCadRect(rect) as TFurnitureEntity);
        }
        scope._ai_cad_data = data;

        scope.updateEntityRelations();

        LayoutContainerUtils.updateEntityUids(true);

        scope.updateRoomsFromEntities();
    }

}