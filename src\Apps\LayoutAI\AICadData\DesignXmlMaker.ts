import { Vector3, Vector3<PERSON>ike } from "three";
import { TRoom } from "../Layout/TRoom";
import { DesignXmlTemplate } from "./DesignXmlTemplate";
import { ZRect } from "@layoutai/z_polygon";
import { AI_CadData } from "./AI_CadData";
import { TRoomEntity } from "../Layout/TLayoutEntities/TRoomEntity";
import { IRoomEntityRealType } from "../Layout/IRoomInterface";
import { TLayoutEntityContainer } from "../Layout/TLayoutEntities/TLayoutEntityContainter";
import { ZPolygon } from "@layoutai/z_polygon";
import { TWindowDoorEntity } from "../Layout/TLayoutEntities/TWinDoorEntity";
import { LayoutContainerUtils } from "../Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { TWall } from "../Layout/TLayoutEntities/TWall";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";
import { TStructureEntity } from "../Layout/TLayoutEntities/TStructureEntity";

export const XmlMaterialTemplate = {
    // 矩形窗
    window_MaterialID: '142378863',// 素材ID
    window_HighFloor: '900',// 离地高度
    window_Height: '1300',// 高度
    window_Nmae: '矩形窗'
    ,// 飘窗
    bay_window_MaterialID: '24661567',// 素材ID
    bay_window_HighFloor: '500',// 离地高度
    bay_window_Height: '1600',// 高度
    bay_window_Name: '飘窗'
    ,// 单开门
    door_MaterialID: '135723478',// 素材ID
    door_HighFloor: '0',// 离地高度
    door_Height: '2150',// 高度
    door_Name: '单开门'
    ,// 推拉门
    slide_door_MaterialID: '135714326',// 素材ID
    slide_door_HighFloor: '0',// 离地高度
    slide_door_Height: '2150',// 高度
    slide_door_Nmae: '推拉门',
    // 门洞
    doorHole_MaterialID: '103426591',// 素材ID
    doorHole_HighFloor: '0',// 离地高度
    doorHole_Height: '2200',// 高度
    doorHole_Nmae: '门洞',
    // 双开门
    double_door_MaterialID: '253344988',// 素材ID
    double_door_HighFloor: '0',// 离地高度
    double_door_Height: '2200',// 高度
    double_door_Nmae: '双开门',
    //字母门
    safetyDoor_MaterialID: '281904412',// 素材ID
    safetyDoor_HighFloor: '0',// 离地高度
    safetyDoor_Height: '2200',// 高度
    safetyDoor_Nmae: '字母门',
    // 栏杆
    railing_MaterialID: '358450026',// 素材ID
    railing_HighFloor: '0',// 离地高度
    railing_Height: '2400',// 高度
    railing_Nmae: '阳台护栏',
    // 垭口
    passDoor_MaterialID: '39858273',// 素材ID
    passDoor_HighFloor: '0',// 离地高度
    passDoor_Height: '2200',// 高度
    passDoor_Nmae: '垭口'


}

export class DesignXmlMaker {
    _xml_doc: XMLDocument;

    _scheme_entity_5: Element;
    _scheme_root: Element;
    _bim: Element;
    _bim_elements: Element;
    _bim_extra: Element;
    _furniture_door_window: Element;

    _last_uid: number;
    _room_id_uidN: { [key: string]: number };

    constructor() {
        this._xml_doc = (new DOMParser()).parseFromString(DesignXmlTemplate, 'application/xml');
        this._last_uid = 15;
    }

    private get newUid() {
        return ++this._last_uid;
    }

    private makeXmlRoomElement(uidN: number, pos: Vector3Like, hasFloorFootLineB: string = "T", hasCeilingFootLineB: string = "T") {
        let room_ele = this._xml_doc.createElement("room");
        room_ele.setAttribute("uidN", '' + uidN);
        room_ele.setAttribute('storeyUIDN', '4');
        room_ele.setAttribute('anchorPosU', `${pos.x},${pos.y}`);
        room_ele.setAttribute('hasFloorFootLineB', hasFloorFootLineB);
        room_ele.setAttribute('hasCeilingFootLineB', hasCeilingFootLineB);

        return room_ele;
    }

    private makeXmlMwallLocationElement(pos0: Vector3Like, pos1: Vector3Like) {
        let location = this._xml_doc.createElement('location');
        location.setAttribute('typeS', "Line");

        let endpoint0 = this._xml_doc.createElement('endpoint0');
        endpoint0.setAttribute('xN', '' + (pos0.x));
        endpoint0.setAttribute('yN', '' + (pos0.y));

        let endpoint1 = this._xml_doc.createElement('endpoint1');
        endpoint1.setAttribute('xN', '' + (pos1.x));
        endpoint1.setAttribute('yN', '' + (pos1.y));
        location.appendChild(endpoint0);
        location.appendChild(endpoint1);

        return location;
    }

    private makeXmlMWallProfileElement(rect: ZRect) {
        let profile = this._xml_doc.createElement('profile')
        let boundary = this._xml_doc.createElement('boundary')
        for (let edge of rect.edges) {
            let pos0 = edge.v0.pos.clone();
            let pos1 = edge.v1.pos.clone();
            let curve = this.makeXmlMwallLocationElement(pos0, pos1);
            boundary.appendChild(curve)
        }
        profile.appendChild(boundary)
        return profile
    }

    private makeXmlMWallElement(rect: ZRect, uidN: number, storeyHeight: number) {
        let mWall = this._xml_doc.createElement("mWall");
        let wall_thickness = rect._h;
        mWall.setAttribute("uidN", '' + uidN);
        mWall.setAttribute('wallTypeN', "1");
        mWall.setAttribute('thicknessN', '' + (wall_thickness));
        mWall.setAttribute('isLockedB', "F");
        mWall.setAttribute('storeyUIDN', "4");
        mWall.setAttribute('topLevelUIDN', "3");
        mWall.setAttribute('bottomLevelUIDN', "2");
        mWall.setAttribute('heightN', storeyHeight.toString());
        mWall.setAttribute('isCustomProfileB', "F");
        let pos0 = rect.unproject({ x: -rect._w / 2, y: 0 });
        let pos1 = rect.unproject({ x: rect._w / 2, y: 0 });
        let location = this.makeXmlMwallLocationElement(pos0, pos1);
        location.setAttribute('locationTypeS', "line");
        let profile = this.makeXmlMWallProfileElement(rect);
        mWall.appendChild(location);
        mWall.appendChild(profile);
        return mWall;
    }

    private makeRectPointLocationType(rect: ZRect) {
        let location = this._xml_doc.createElement('location');

        let origin = this._xml_doc.createElement('origin');
        let pos = rect.rect_center;
        origin.setAttribute("xN", '' + pos.x);
        origin.setAttribute("yN", '' + pos.y);

        let xAxis = this._xml_doc.createElement('xAxis');
        xAxis.setAttribute("xN", '' + rect.dv.x);
        xAxis.setAttribute("yN", '' + rect.dv.y);
        let yAxis = this._xml_doc.createElement('yAxis');
        yAxis.setAttribute("xN", '' + rect.nor.x);
        yAxis.setAttribute("yN", '' + rect.nor.y);
        location.appendChild(origin);
        location.appendChild(xAxis);
        location.appendChild(yAxis);

        return location;
    }

    private makeRectLineLocationType(rect: ZRect) {
        let location = this._xml_doc.createElement('location');
        location.setAttribute('typeS', "Line");
        location.setAttribute('locationTypeS', "line");

        let endpoint0 = this._xml_doc.createElement('endpoint0');
        let endpoint1 = this._xml_doc.createElement('endpoint1');

        let pos0 = rect.unproject({ x: -rect.w / 2, y: 0 });
        let pos1 = rect.unproject({ x: rect.w / 2, y: 0 });
        endpoint0.setAttribute("xN", '' + pos0.x);
        endpoint0.setAttribute("yN", '' + pos0.y);

        endpoint1.setAttribute("xN", '' + pos1.x);
        endpoint1.setAttribute("yN", '' + pos1.y);

        location.appendChild(endpoint0);
        location.appendChild(endpoint1);
        return location;
    }

    private makeXmlStructureElement(rect: ZRect, height: number, uidN: number, struct_name: string = "platform") {
        let platform = this._xml_doc.createElement(struct_name);
        platform.setAttribute("uidN", '' + uidN);
        platform.setAttribute('lengthN', '' + rect.w);
        platform.setAttribute('widthN', '' + rect.h);
        platform.setAttribute('heightN', '' + height);

        platform.setAttribute('isLockedB', "F");
        platform.setAttribute('storeyUIDN', "4");
        platform.setAttribute('topLevelUIDN', "3");
        platform.setAttribute('bottomLevelUIDN', "2");
        platform.setAttribute('bottomOffsetN', "0");
        platform.setAttribute('isLockedB', "F");
        platform.setAttribute('topOffsetN', "200");

        platform.setAttribute(struct_name + "TypeN", '1')
        let location = this.makeRectPointLocationType(rect);
        location.setAttribute('locationTypeS', "point");
        platform.appendChild(location);
        return platform;
    }

    private makeXmlBeamElement(rect: ZRect, height: number, uidN: number) {
        let beam = this._xml_doc.createElement("beam");
        beam.setAttribute("uidN", '' + uidN);
        beam.setAttribute('lengthN', '' + rect.w);
        beam.setAttribute('widthN', '' + rect.h);
        beam.setAttribute('heightN', '' + height);

        let rect_center = rect.rect_center;
        beam.setAttribute("positionU", `${rect_center.x},${rect_center.y}`);
        beam.setAttribute("directionU", `${rect.nor.x},${rect.nor.y}`);

        beam.setAttribute('isLockedB', "F");
        beam.setAttribute('storeyUIDN', "4");
        beam.setAttribute('topLevelUIDN', "3");
        beam.setAttribute('bottomLevelUIDN', "2");
        beam.setAttribute('bottomOffsetN', "0");
        beam.setAttribute('topOffsetN', "200");

        let location = this.makeRectLineLocationType(rect);
        beam.appendChild(location);
        return beam;
    }

    private makeXmlFlueElement(rect: ZRect, uidN: number) {
        let flue = this._xml_doc.createElement("flue");
        flue.setAttribute("uidN", '' + uidN);
        flue.setAttribute('lengthN', '' + rect.w);
        flue.setAttribute('widthN', '' + rect.h);
        flue.setAttribute('heightN', '' + 2800);

        flue.setAttribute('thicknessN', '50');
        flue.setAttribute('isLockedB', "F");
        flue.setAttribute('storeyUIDN', "4");
        flue.setAttribute('topLevelUIDN', "3");
        flue.setAttribute('bottomLevelUIDN', "2");
        flue.setAttribute('bottomOffsetN', "0");
        flue.setAttribute('topOffsetN', "200");
        flue.setAttribute('flueTypeN', "1");

        let location = this.makeRectPointLocationType(rect);
        location.setAttribute('locationTypeS', "point");
        flue.appendChild(location);
        return flue;
    }

    private makeRoomAreaElement(name: string, positionU: string, size: number, uidN: number, roomUIDN: number) {
        let room_area = this._xml_doc.createElement('room_area')
        room_area.setAttribute('uidN', '' + (uidN))
        room_area.setAttribute('storeyUIDN', "4")
        room_area.setAttribute('roomUIDN', '' + (roomUIDN))
        room_area.setAttribute('nameS', '' + (name))
        room_area.setAttribute('categoryIdS', "10742")
        room_area.setAttribute('positionU', '' + (positionU))
        room_area.setAttribute('areaN', '' + (size))
        room_area.setAttribute('defaultAreaB', "T")
        room_area.setAttribute('indexN', "0")
        room_area.setAttribute('displayStatusN', "3")
        return room_area
    }

    private makeAttributesFromTemplate(template: string): { [key: string]: string } {
        let pairs = template.split(" ");
        let ans: { [key: string]: string } = {};
        for (let pair of pairs) {
            let t_data = pair.split("=");
            if (t_data.length == 2) {
                let key = t_data[0];
                let value = t_data[1].replace(/\"/g, "");
                ans[key] = value;
            }
        }
        return ans;

    }

    private makeElementByTemplate(tagName: string, template: string, attributes: { [key: string]: string }) {
        let element = this._xml_doc.createElement(tagName);
        let attributes_template = template;
        let attributes_data = this.makeAttributesFromTemplate(attributes_template);

        for (let key in attributes) {
            attributes_data[key] = attributes[key];
        }
        for (let key in attributes_data) {
            element.setAttribute(key, attributes_data[key]);
        }
        return element;
    }

    private makeRoomCurvesElement(uidN: number, poly: ZPolygon) {
        let room = this._xml_doc.createElement('room')
        room.setAttribute('versionN', "0")
        room.setAttribute('uidN', '' + (uidN))
        for (let edge of poly.edges) {
            let startU = (edge.v0.pos.x) + "," + (edge.v0.pos.y) + ",0";
            let endU = (edge.v1.pos.x) + "," + (edge.v1.pos.y) + ",0";
            let curve3 = this._xml_doc.createElement('curve3');
            curve3.setAttribute('typeN', "1");
            curve3.setAttribute('startU', (startU));
            curve3.setAttribute('endU', (endU))
            curve3.setAttribute('isBoundB', "T");
            room.append(curve3)
        }

        return room
    }

    private makeXmlWindoorElement(uidN: number, wallUIDN: number, data: {
        line_center: number[],
        length: number, width: number, angle: number, mirror: number, label: IRoomEntityRealType
    ,type: string}) {
        // console.log(data);
        let line_center = data["line_center"];
        let xN = line_center[0];
        let yN = line_center[1];

        let lengthN = data["length"];
        let widthN = (data["width"]);
        let rotationZN = data["angle"];
        let isMirrorN = data["mirror"];
        let label = data["label"];
        let materialIdS = "";
        let heightFromFloorN = '1000';
        let heightN = '1000';
        let nameS = "";
        let doorTypeS = "";


        if (label == "SingleDoor" || label.toLocaleLowerCase() == "singledoor") {
            materialIdS = XmlMaterialTemplate.door_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.door_HighFloor;
            heightN = XmlMaterialTemplate.door_Height;
            nameS = XmlMaterialTemplate.door_Name;
            doorTypeS = "SingleDoor";
        }
        else if (label == "DoubleDoor" || label.toLocaleLowerCase() == "doubledoor") {
            materialIdS = XmlMaterialTemplate.double_door_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.double_door_HighFloor;
            heightN = XmlMaterialTemplate.double_door_Height;
            nameS = XmlMaterialTemplate.double_door_Nmae;
            doorTypeS = "DoubleDoor";
        }
        else if (label == "SafetyDoor" || label.toLocaleLowerCase() == "safetydoor") {
            materialIdS = XmlMaterialTemplate.safetyDoor_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.safetyDoor_HighFloor;
            heightN = XmlMaterialTemplate.safetyDoor_Height;
            nameS = XmlMaterialTemplate.safetyDoor_Nmae;
            doorTypeS = "SafetyDoor";
        }
        else if (label == "SlidingDoor" || label as string == "slidedoor") {
            materialIdS = XmlMaterialTemplate.slide_door_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.slide_door_HighFloor;
            heightN = XmlMaterialTemplate.slide_door_Height;
            nameS = XmlMaterialTemplate.slide_door_Nmae;
            doorTypeS = "SlidingDoor";
        }
        else if (label == "OneWindow" || label.toLocaleLowerCase() == "window") {
            materialIdS = XmlMaterialTemplate.window_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.window_HighFloor;
            heightN = XmlMaterialTemplate.window_Height;
            nameS = XmlMaterialTemplate.window_Nmae;
        }
        else if (label == "BayWindow" || label.toLocaleLowerCase() == "baywindow") {
            materialIdS = XmlMaterialTemplate.bay_window_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.bay_window_HighFloor;
            heightN = XmlMaterialTemplate.bay_window_Height;
            nameS = XmlMaterialTemplate.bay_window_Name;
        }
        else if(label == "DoorHole" || label.toLocaleLowerCase() == "doorhole"){
            materialIdS = XmlMaterialTemplate.doorHole_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.doorHole_HighFloor;
            heightN = XmlMaterialTemplate.doorHole_Height;
            nameS = XmlMaterialTemplate.doorHole_Nmae;
            doorTypeS = "DoorHole";
        }
        else if (label == "Railing" || label.toLocaleLowerCase() == "railing") {
            materialIdS = XmlMaterialTemplate.railing_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.railing_HighFloor;
            heightN = XmlMaterialTemplate.railing_Height;
            nameS = XmlMaterialTemplate.railing_Nmae;
        }
        else if (label == "PassDoor" || label.toLocaleLowerCase() == "passdoor") {
            materialIdS = XmlMaterialTemplate.passDoor_MaterialID;
            heightFromFloorN = XmlMaterialTemplate.passDoor_HighFloor;
            heightN = XmlMaterialTemplate.passDoor_Height;
            nameS = XmlMaterialTemplate.passDoor_Nmae;
            doorTypeS = "DoorHole";
        }

        let EmbeddingFurnitureDoorEntity: Element = null;
        if (data.type == 'Window') {
            EmbeddingFurnitureDoorEntity = this._xml_doc.createElement('EmbeddingFurnitureWindowEntity');

        }
        else {
            EmbeddingFurnitureDoorEntity = this._xml_doc.createElement('EmbeddingFurnitureDoorEntity');
        }
        EmbeddingFurnitureDoorEntity.setAttribute('nameS', (nameS));
        EmbeddingFurnitureDoorEntity.setAttribute('uidN', '' + (uidN));
        EmbeddingFurnitureDoorEntity.setAttribute('visibleB', "T");
        EmbeddingFurnitureDoorEntity.setAttribute('materialIdS', (materialIdS));
        EmbeddingFurnitureDoorEntity.setAttribute('xN', '' + (xN));
        EmbeddingFurnitureDoorEntity.setAttribute('yN', '' + (yN));
        EmbeddingFurnitureDoorEntity.setAttribute('zN', (heightFromFloorN));
        EmbeddingFurnitureDoorEntity.setAttribute('rotationZN', '' + (rotationZN));
        EmbeddingFurnitureDoorEntity.setAttribute('lengthN', '' + (lengthN));
        EmbeddingFurnitureDoorEntity.setAttribute('widthN', '' + (widthN));
        EmbeddingFurnitureDoorEntity.setAttribute('heightN', (heightN));
        EmbeddingFurnitureDoorEntity.setAttribute('isMirrorN', '' + (isMirrorN));
        EmbeddingFurnitureDoorEntity.setAttribute('thicknessN', "0");
        EmbeddingFurnitureDoorEntity.setAttribute('offsetN', "0");
        EmbeddingFurnitureDoorEntity.setAttribute('heightFromFloorN', (heightFromFloorN));
        EmbeddingFurnitureDoorEntity.setAttribute('mirrorTypeS', "");
        EmbeddingFurnitureDoorEntity.setAttribute('wallUIDN', '' + (wallUIDN));
        EmbeddingFurnitureDoorEntity.setAttribute('pillarUIDN', "0");
        if (doorTypeS) {
            EmbeddingFurnitureDoorEntity.setAttribute('doorTypeS', doorTypeS);
        }
        return EmbeddingFurnitureDoorEntity;
    }

    public makeByAiCadData(ai_cad_data: AI_CadData) {
        this.reInitXml();
        this._room_id_uidN = {};
        let walls: ZRect[] = [];
        // if(!ai_cad_data._simple_wall_rects || ai_cad_data._simple_wall_rects.length == 0)
        // {
        //     ai_cad_data.updateSimpleWallRects();
        // }
        if (ai_cad_data._wall_rects) {
            for (let rect of ai_cad_data._wall_rects) {
                if (TBaseEntity.is_deleted(rect)) continue;

                if (rect.w < 10 || rect.h < 10) continue;
                // if(rect.ex_prop['label'] != "wall") continue;

                let uidN = this.newUid;
                let mwall = this.makeXmlMWallElement(rect, uidN, 2800);
                this._bim_elements.appendChild(mwall);

                rect.ex_prop['uidN'] = '' + uidN;
                let entity = TWall.getOrMakeEntityOfCadRect(rect);
                entity.uidN = uidN;

                walls.push(rect);
            }
        }

        let structure_rects: ZRect[] = [...ai_cad_data._strucutre_rects];
        for (let rect of structure_rects) {
            let entity = TStructureEntity.getOrMakeEntityOfCadRect(rect);
            if (entity.realType == "Flue") {
                let ele = this.makeXmlFlueElement(rect, this.newUid);

                this._bim_elements.appendChild(ele);
            }
            else if (entity.realType == "Pillar" || entity.realType == "Platform" || entity.realType == "Envelope_Pipe") {
                let ele = this.makeXmlStructureElement(rect, entity.height, this.newUid, entity.realType.toLocaleLowerCase());

                this._bim_elements.appendChild(ele);
            }
            else if (entity.realType == "Beam") {
                let ele = this.makeXmlBeamElement(rect, entity.height, this.newUid);

                this._bim_elements.appendChild(ele);
            }
        }
        // this._uidN += 1;
        // 然后添加name pos

        if (ai_cad_data._room_polys) {
            for (let id in ai_cad_data._room_polys) {
                let room_poly = ai_cad_data._room_polys[id];

                if (room_poly.orientation_z_nor.z > 0.5) continue;

                let entity: TRoomEntity = TRoomEntity.getOrMakeEntityOfCadRect(room_poly as ZRect) as TRoomEntity;
                if (entity._area < 0.01) continue;
                let room_name = entity.name;

                let center = entity._main_rect.rect_center || room_poly.computeBBox().getCenter(new Vector3());
                let uidN = this.newUid;
                let room_ele = this.makeXmlRoomElement(uidN, center, entity._hasFloorFootLineB ? "T" : "F", entity._hasCeilingFootLineB ? "T" : "F");
                this._bim_elements.appendChild(room_ele);
                this._room_id_uidN[id] = uidN;
                entity.uidN = uidN;
            }

            for (let id in ai_cad_data._room_polys) {
                let room_poly = ai_cad_data._room_polys[id];
                if (room_poly.orientation_z_nor.z > 0.5) continue;

                let entity: TRoomEntity = TRoomEntity.getOrMakeEntityOfCadRect(room_poly as ZRect) as TRoomEntity;
                if (entity._area < 0.01) continue;

                let room_name = entity.name;

                let room_size = entity._area;
                let roomUIDN = this._room_id_uidN[id];
                entity.computeMainRect();
                let center = entity._main_rect.rect_center;

                let room_area_ele = this.makeRoomAreaElement(room_name, `${center.x},${center.y}`, room_size, this.newUid, roomUIDN);
                this._bim_elements.appendChild(room_area_ele);
            }
        }

        // this._uidN += 10;   // uidN设置间隔

        let window_door_enetitys = this._xml_doc.createElement("Entitys")
        this._furniture_door_window.appendChild(window_door_enetitys);

        for (let rect of walls) {
            let wall_uidN = ~~rect.ex_prop['uidN']
            let windows = AI_CadData.get_window_rects_of_wall_rect(rect) as ZRect[];

            // console.log(wall_uidN,rect);
            for (let win of windows) {
                // console.log("windows",win);
                let entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(win) as TWindowDoorEntity;
                let label = win.ex_prop.label || "";
                label = label.toLocaleLowerCase();
                let ele = this.makeXmlWindoorElement(this.newUid, wall_uidN, {
                        line_center: win.rect_center.toArray(),
                        length: win._w, width: win._h, mirror: win.u_dv_flag > 0 ? 0 : 1, angle: win.rotation_z, label: entity.realType, type: entity.type
                });
                window_door_enetitys.appendChild(ele);
            }
        }

        return this._xml_doc;
    }

    public makeByEntities(container: TLayoutEntityContainer) {
        this.reInitXml();
        container.updateEntityRelations();
        this._last_uid = LayoutContainerUtils.getMaximalEntityUid(container);
        this._last_uid = LayoutContainerUtils.rectifyEntityUid(this._last_uid);

        for (let entity of container._wall_entities) {
            let rect = entity.rect;
            let mwall = this.makeXmlMWallElement(rect, entity.uidN, container._storey_height);
            this._bim_elements.appendChild(mwall);
            rect.ex_prop['uidN'] = '' + entity.uidN;
        }


        for (let entity of container._structure_entities) {
            let rect = entity.rect;
            let uidN = entity.uidN;
            if (entity.realType == "Flue") {
                let ele = this.makeXmlFlueElement(rect, uidN);

                this._bim_elements.appendChild(ele);
            }
            else if (entity.realType == "Pillar" || entity.realType == "Platform" || entity.realType == "Envelope_Pipe") {
                let ele = this.makeXmlStructureElement(rect, entity.height, uidN, entity.realType.toLocaleLowerCase());

                this._bim_elements.appendChild(ele);
            }
            else if (entity.realType == "Beam") {
                let ele = this.makeXmlBeamElement(rect, entity.height, uidN);
                this._bim_elements.appendChild(ele);
            }
        }

        for (let entity of container._room_entities) {
            if (entity._area < 0.01) continue;
            if (entity._room_poly.orientation_z_nor.z > 0) continue;
            let room_name = entity.name;
            let room_poly = entity._room_poly;

            let center = entity._main_rect.rect_center || room_poly.computeBBox().getCenter(new Vector3());
            let room_ele = this.makeXmlRoomElement(entity.uidN, center, entity._hasFloorFootLineB ? "T" : "F", entity._hasCeilingFootLineB ? "T" : "F");
            this._bim_elements.appendChild(room_ele);
        }

        for (let entity of container._room_entities) {
            if (entity._area < 0.01) continue;
            if (entity._room_poly.orientation_z_nor.z > 0) continue;

            let room_name = entity.name;

            let room_size = entity._area;
            let center = entity._main_rect.rect_center;

            let room_area_ele = this.makeRoomAreaElement(room_name, `${center.x},${center.y}`, room_size, entity._name_uid, entity.uidN);
            this._bim_elements.appendChild(room_area_ele);

            let room_extra = this.makeRoomCurvesElement(entity.uidN, entity._main_rect);
            this._bim_extra.appendChild(room_extra);
        }
        let window_door_entities = this._xml_doc.createElement("Entitys")
        this._furniture_door_window.appendChild(window_door_entities);
        for (let win of container._window_entities) {
            let wall_rect = win._wall_rect;
            if (!wall_rect) continue;
            let wall_entity = TWall.getOrMakeEntityOfCadRect(wall_rect);
            if (!wall_entity) continue;
            let rect = win.rect;

            let mirror = win.mirror;
            let rotate_z = win.rotate_z;
            let t_center = rect.rect_center;

            if (win.realType === "BayWindow") // 飘窗要特殊处理一下
            {
                if (wall_entity) {
                    let pp = wall_entity.rect.project(t_center);
                    t_center = wall_entity.rect.unproject({ x: pp.x, y: 0 });
                }
                rotate_z += Math.PI;

            }

            let ele = this.makeXmlWindoorElement(win.uidN, wall_entity.uidN, {
                line_center: t_center.toArray(),
                length: win.length, width: win.depth, mirror: mirror, angle: rotate_z, label: win.realType, type: win.type
            });
            window_door_entities.appendChild(ele);
        }
        return this._xml_doc;
    }

    public makeByTRooms(rooms: TRoom[]) {
        this.reInitXml();

        this._room_id_uidN = {};

        // 先添加墙体信息

        let walls: ZRect[] = [];
        let test_thickness = 240;

        for (let room of rooms) {
            let poly = room.room_shape._poly;
            poly.computeZNor();
            for (let edge of poly.edges) {
                let rect = new ZRect(edge.length, test_thickness);

                rect._back_center.copy(edge.center);
                rect._nor.copy(edge.nor);
                rect.u_dv = edge.dv;
                rect.updateRect();

                walls.push(rect);
            }

        }
        for (let rect of walls) {
            let uidN = this.newUid;
            let mwall = this.makeXmlMWallElement(rect, uidN, 2800);
            this._bim_elements.appendChild(mwall);
            rect.ex_prop['uidN'] = '' + uidN;
        }

        // this._uidN += 10;
        // 然后添加name pos

        for (let id in rooms) {
            let room = rooms[id];
            let center = room.room_center;
            let uidN = this.newUid;
            let room_ele = this.makeXmlRoomElement(uidN, center);
            this._bim_elements.appendChild(room_ele);
            this._room_id_uidN[id] = uidN;
        }
        for (let id in rooms) {
            let room = rooms[id];
            let room_name = room.roomname;
            let room_size = room.room_shape._area;
            let roomUIDN = this._room_id_uidN[id];
            let center = room.room_center;

            let room_area_ele = this.makeRoomAreaElement(room_name, `${center.x},${center.y}`, room_size, this.newUid, roomUIDN);
            this._bim_elements.appendChild(room_area_ele);
        }

        // for(let id in rooms)
        // {
        //     let room = rooms[id];
        //     let roomUIDN = this._room_id_uidN[id];
        //     let bim_room = this.makeRoomCurvesElement(roomUIDN,room.room_shape._poly);
        //     // this._bim_extra.appendChild(bim_room);
        // }

        return this._xml_doc;
    }

    public reInitXml() {
        this._last_uid = 15;
        this._xml_doc = (new DOMParser()).parseFromString(DesignXmlTemplate, 'application/xml');
        this._scheme_entity_5 = this._xml_doc.getElementsByTagName("swjia_scheme_5_0")[0];
        this._scheme_root = this._scheme_entity_5.getElementsByTagName("SchemeRoot")[0];
        this._bim = this._scheme_root.getElementsByTagName("bim")[0];
        this._bim_elements = this._bim.getElementsByTagName("elements")[0];
        this._bim_extra = this._bim.getElementsByTagName("bim_extra")[0];
        this._furniture_door_window = this._scheme_root.getElementsByTagName("FurnitureDoorWindow")[0];

        this.cleanBimElementsData();
        this.cleanBimRoomsData();
        this.cleanDoorWindowData();

        // console.log(new XMLSerializer().serializeToString(this._xml_doc));
    }

    private cleanBimRoomsData() {
        // let rooms = this._bim_extra.getElementsByTagName("room");
        // if(rooms)
        // {
        //     let t_rooms = [];
        //     for(let room of rooms)
        //     {
        //         t_rooms.push(room);
        //     }

        //     for(let room of t_rooms)
        //     {
        //         this._bim_extra.removeChild(room);
        //     }
        // }
        this._bim_extra.innerHTML = "";
    }

    private cleanBimElementsData() {
        // let children_to_removed = [];
        // for(let child of this._bim_elements.children)
        // {
        //     if(["mWall","room","room_area"].indexOf(child.tagName)>=0)
        //     {
        //         children_to_removed.push(child);
        //     }
        // }

        // for(let child of children_to_removed)
        // {
        //     this._bim_elements.removeChild(child);
        // }

        this._bim_elements.innerHTML = `
        <building uidN="1" nameS="bud"/>
        <level uidN="2" buildingUIDN="1" heightN="0" indexN="0"/>
        <level uidN="3" buildingUIDN="1" heightN="3000" indexN="1"/>
        <storey uidN="4" bottomLevelUIDN="2" topLevelUIDN="3" buildingUIDN="1" alphaN="1" floorSlabAlphaN="1"/>
        `;
    }

    private cleanDoorWindowData() {
        let entities = this._furniture_door_window.getElementsByTagName("Entitys");
        if (entities) {
            for (let child of entities) {
                this._furniture_door_window.removeChild(child);
            }
        }
    }
}