import { <PERSON>, G<PERSON> } from "lil-gui";
import { PerspectiveCamera } from "three";
import { DefaultCeilingType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { FigureViewControls } from "@/Apps/LayoutAI/Scene3D/controls/FigureViewControls";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { IFrameMsgServerExFuncs } from "@/pages/SdkFrame/MsgCenter/IFrameMsgServerExFuncs";
import { LightMode } from "@/pages/SdkFrame/MsgCenter/IMsgType";
import { IFrameTestConfig } from "./IFrameTestConfig";
import { AutoLightingTest } from "./AutoLightingTest";
import { EnvLightTest } from "./EnvLightTest";
import { SceneLightMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import { SwitchConfig } from "@/Apps/LayoutAI/Scene3D/NodeName";

export class RoomBasicTest {
    private _gui: GUI;

    private _ceilingOptions: { [key: string]: DefaultCeilingType } = IFrameTestConfig.ceilingOptions;
    private _lightMode = LightMode.Day;
    // private _currentCeilingInfo: I_RoomSubAreaSimpleData;
    private _currentCeilingInfo: any;
    private _autoLightingTest: AutoLightingTest;
    private __envLightTest: EnvLightTest;


    constructor(gui: GUI, autoLightingTest: AutoLightingTest, envLightTest: EnvLightTest) {
        this._gui = gui;
        this._autoLightingTest = autoLightingTest;
        this.__envLightTest = envLightTest;
    }

    public get config(){
        return {
            ceilingOptions: this._ceilingOptions,
            lightMode: this._lightMode,
            currentCeilingInfo: this._currentCeilingInfo,
        }
    }

    public async Sleep(millisecond: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, millisecond));
    }

    public async addRoomBasicCtr(){
        // 分区
        let sub_area_result = await IFrameMsgServerExFuncs.getCeilingAreasInRoom({});
        let sub_area_list: any[] = [];
        sub_area_result.forEach((result) => {
            for (let area of result.sub_areas) {
                let copy: any = { ...area };
                // 补充额外信息
                copy.room_name = result.room_name;
                copy.room_uid = result.room_uid;
                copy.room_uuid = result.room_uuid;
                sub_area_list.push(copy);
            }
        });
        console.log(sub_area_result);
        console.log(sub_area_list);
        this._currentCeilingInfo = sub_area_list[0];
        let lvRoom = sub_area_list.find((area) => area.name === '客厅');
        if (lvRoom) {
            this._currentCeilingInfo = lvRoom;
        }

        // 创建吊顶区域下拉列表
        let curCeilingArea: { [key: string]: any } = {};
        sub_area_list.forEach((area, i) => {
            curCeilingArea[i + "_" + area.room_name] = area;
        });

        // 视角下来列表
        let view_list = await IFrameMsgServerExFuncs.generateViewCameraList({ filteredRoomNames: [this._currentCeilingInfo.room_name] });
        console.log(view_list);

        if (view_list.length > 0) {
            let selectedCamera = view_list[0];

            await this.Sleep(3000);

            let cameraOptions: { [key: string]: any } = {};
            view_list.forEach((camera, index) => {
                cameraOptions[index + "_" + camera.name] = camera;
            });

            let viewCtrl: Controller;
            this._gui.add({ ceiling: 0 + "_" + this._currentCeilingInfo.room_name }, 'ceiling', Object.keys(curCeilingArea))
                .name('房间')
                .onChange(async (value: string) => {
                    this._currentCeilingInfo = curCeilingArea[value];
                    if (viewCtrl) {
                        console.log(this._currentCeilingInfo);
                        const view_list = await IFrameMsgServerExFuncs.generateViewCameraList({ filteredRoomNames: [this._currentCeilingInfo.room_name] });
                        if (view_list.length == 0) {
                            console.error("没有找到视角");
                            return;
                        }
                        selectedCamera = view_list[0];
                        cameraOptions = {};
                        view_list.forEach((camera, index) => {
                            cameraOptions[index + "_" + camera.name] = camera;
                        });
                        console.log(cameraOptions);

                        // 更新视角控制器的选项
                        viewCtrl.options(Object.keys(cameraOptions));
                        // 设置新的默认值
                        viewCtrl.setValue(0 + "_" + selectedCamera.name);
                        // 更新显示
                        viewCtrl.updateDisplay();
                    }
                });

            let scene3d = LayoutAI_App.instance.scene3D;
            let camera = scene3d.active_controls.camera as PerspectiveCamera;
            let rect = (scene3d.active_controls as FigureViewControls)._rect;
            if (!rect) {
                console.error("请在3D漫游视角下打开");
                return;
            }
            let fov = camera.fov;
            let pitch = 180 * rect.rotation_x / Math.PI;

            this._gui.add({ fov: fov }, 'fov', 30, 180)
                .name('视场角')
                .onChange((value: number) => {
                    fov = value;
                    IFrameMsgServerExFuncs.applyCurrentViewCamera({
                        uuid: selectedCamera.uuid,
                        fov: fov,
                        pitch: pitch
                    });
                });

            this._gui.add({ pitch: pitch }, 'pitch', 0, 180)
                .name('俯仰角')
                .onChange((value: number) => {
                    pitch = value;
                    IFrameMsgServerExFuncs.applyCurrentViewCamera({
                        uuid: selectedCamera.uuid,
                        fov: fov,
                        pitch: pitch
                    });
                });


            viewCtrl = this._gui.add({ camera: 0 + "_" + selectedCamera.name }, 'camera', Object.keys(cameraOptions))
                .name('视角')
                .onChange((value: string) => {
                    selectedCamera = cameraOptions[value];
                    IFrameMsgServerExFuncs.applyCurrentViewCamera({
                        uuid: selectedCamera.uuid,
                        fov: fov,
                        pitch: pitch
                    });
                });
        }
        else {
            console.error("没有找到视角");
        }

        this._currentCeilingInfo.ceiling_type = DefaultCeilingType.OverhangingEdge;
        let ceilingOptions = ["悬边吊顶", "悬浮吊顶", "平顶"];
        this._gui.add({ ceilingType: "悬边吊顶" }, 'ceilingType', ceilingOptions)
            .name('吊顶类型')
            .onChange((value: string) => {
                this._currentCeilingInfo.ceiling_type = this._ceilingOptions[value];
                console.log(this._currentCeilingInfo.ceiling_type);
                IFrameMsgServerExFuncs.editCeilingAreaInRoom(this._currentCeilingInfo);
                this.applyCeilingType({ filterRoomUuids: [this._currentCeilingInfo.room_uuid || ""] }, this._currentCeilingInfo.ceiling_type);
            });

        this._gui.add({ lightMode: this._lightMode }, 'lightMode', Object.keys(LightMode))
            .name('灯光模式')
            .onChange((value: string) => {
                this._lightMode = value as any;
                let mode = SceneLightMode.Day;
                if (this._lightMode === LightMode.Day ){
                    mode = SceneLightMode.Day;
                }else if (this._lightMode === LightMode.Night ){
                    mode = SceneLightMode.Night;
                }
                SwitchConfig.showNightMode = true;
                LayoutAI_App.instance.scene3D.setLightMode(mode);
                LayoutAI_App.instance.scene3D.setLightGroupVisible(true, true, false);
                // 切换自动灯光规则配置
                this._autoLightingTest.changeAutoLightingCtr(this._lightMode);
                // 切换环境光配置
                this.__envLightTest.changeEnvLightCtr(this._lightMode);
            });
    }

    /**
     * 改变吊顶类型
     * @param room_uuids
     * @param ceiling_type
     */
    private async applyCeilingType(
        room_filter: {
            filteredRoomUids?: string[];
            filterRoomUuids?: string[];
            filteredRoomNames?: string[];
        },
        ceiling_type: string
    ) {

        let ceiling_infos = await IFrameMsgServerExFuncs.getCeilingAreasInRoom(room_filter);

        let promises: Promise<any>[] = [];
        ceiling_infos.map((ceiling_info) => {
            ceiling_info.sub_areas.forEach((area) => {
                area.ceiling_type = ceiling_type;
                promises.push(IFrameMsgServerExFuncs.editCeilingAreaInRoom(area));
            });
        });
        return await Promise.allSettled(promises);
    }
     
}