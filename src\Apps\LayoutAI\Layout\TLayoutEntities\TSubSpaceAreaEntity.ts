import { EventName } from "@/Apps/EventSystem";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { I_SwjEntityBase, I_SwjSubSpaceData } from "../../AICadData/SwjLayoutData";
import { TPainter } from "../../Drawing/TPainter";
import { compareNames, range_substract } from "@layoutai/z_polygon";
import { ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { AI_PolyTargetType, IRoomSpaceAreaType, IType2UITypeDict, I_Window, RoomSpaceAreaType, SolverMethods } from "../IRoomInterface";
import { TSwjLayoutGraphSolver } from "../TAppSolvers/TSwjLayoutGraphSolver";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { I_SimpleAreaExtractRule } from "../TLayoutGraph/TGraphConfigureInterface";
import { TSubAreaLayoutScheme } from "../TLayoutScheme/TSubAreaLayoutScheme";
import { TRoom } from "../TRoom";
import { IPropertyUI } from "./IPropertyUI";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TFurnitureEntity } from "./TFurnitureEntity";
import { TRoomEntity } from "./TRoomEntity";
import { Vector3Like } from "three";
import { RemarkTagsExtractor } from "./algorithms/RemarkTagsExtractor";
import { TRoomTemplateSaver } from "./loader/TRoomTemplateSaver";
import { roomSubAreaService } from "@/Apps/LayoutAI/Services/Basic/RoomSubAreaService";
import { I_RoomSubAreaSimpleData } from "@layoutai/basic_data";


/**
 *  分区区域: 一般用矩形表示
 */
export class TSubSpaceAreaEntity extends TBaseEntity {
    /**
     *  功能分区分类
     */
    private _space_area_type: IRoomSpaceAreaType;

    /**
     *  所绑定的分区
     */
    private _room_entity: TRoomEntity = null;

    /**
     *  区域形状
     */
    _area_shape: ZPolygon;


    _color_style: string;

    _room_uidN?: number;

    _area_layout_scheme_list?: TSubAreaLayoutScheme[];

    _area_scheme_index?: number;


    /**
     *  以分区生成的虚拟房间对象
     */
    _space_area_room: TRoom;


    DEFAULT_COLOR_DICT: { [key: string]: string } = {
        [RoomSpaceAreaType.LivingArea]: "#A5C4D4",
        [RoomSpaceAreaType.DiningArea]: "#FFB294",
        [RoomSpaceAreaType.SleepingArea]: "#FFD166",
        [RoomSpaceAreaType.DressingArea]: "#9AE5A5",
        [RoomSpaceAreaType.WashingArea]: "#E0B2E9",
        [RoomSpaceAreaType.KitchenArea]: "#E0B2E9",
        [RoomSpaceAreaType.BalconyArea]: "#E0B2E9",
        [RoomSpaceAreaType.HallwayArea]: "#9AE5A5",
        [RoomSpaceAreaType.StudyArea]: "#FFD166",
        [RoomSpaceAreaType.EntertainmentArea]: "#A5C4D4",
        [RoomSpaceAreaType.EntranceArea]: "#9AE5A5",
        [RoomSpaceAreaType.UnknownArea]: "#D2D2D2"
    }
    static readonly EntityType = AI_PolyTargetType.RoomSubArea;
    // 分区内的家具列表
    private _furniture_entities: Map<string, TFurnitureEntity>;

    private _tags: string;

    constructor() {
        super();

        this.type = TSubSpaceAreaEntity.EntityType;

        this._space_area_type = RoomSpaceAreaType.UnknownArea;

        this._room_entity = null;

        this._color_style = "#66b8ff";

        this._default_priority_for_selection = this._priority_for_selection = 3;

        this._furniture_entities = new Map();


        this._tags = "";

        this._area_scheme_index = -1;
        this.title = LayoutAI_App.t("分区信息");
    }

    get name() {
        if (this.space_area_type) {
            return IType2UITypeDict[this.space_area_type];
        } else {
            return "无分区类型";
        }
    }
    set name(name: string) {
        this.space_area_type = name as any;
    }

    get color_style() {
        return this._color_style;
    }

    set color_style(color: string) {
        this._color_style = color;
    }
    addFurnitureEntity(entity: TFurnitureEntity) {
        if (!this._furniture_entities.has(entity._uuid)) {
            this._furniture_entities.set(entity._uuid, entity);
        }
    }
    addFurnitureEntities(entities: TFurnitureEntity[]) {
        entities.forEach((entity) => {
            if (this.rect.containsPoint(entity.rect.rect_center) && !entity.figure_element._subarea) {
                entity.figure_element._subarea = this.space_area_type;
                this.addFurnitureEntity(entity);
            }
        })
    }
    get furniture_entities() {
        return Array.from(this._furniture_entities.values());
    }

    removeFurnitureEntity(entity: TFurnitureEntity) {
        this._furniture_entities.delete(entity._uuid);
    }

    clearFurnitureEntities(clear_parent: boolean = false) {
        if (clear_parent) {
            this.furniture_entities.forEach((entity) => {
                entity.dispose();
            })
        }
        this._furniture_entities.clear();
    }

    get tags(): string {
        return this._tags;
    }
    set tags(t: string) {
        this.tags = t;
    }


    get room_entity() {
        return this._room_entity;
    }

    /**
     *  拓展这个赋值, 把在房间中自动添加和删除分区放在这里
     */
    set room_entity(entity: TRoomEntity) {
        if (this._room_entity) {
            this._room_entity.removeSubAreaEntity(this);
        }
        this._room_entity = entity;
        if (this._room_entity) {
            this._room_entity.addSubAreaEntity(this);
        }
    }


    setDefaultColor() {
        let color = this.DEFAULT_COLOR_DICT[this._space_area_type] || "#aaaaaa";
        this.color_style = color;
    }

    get space_area_type() {
        return this._space_area_type;
    }

    set space_area_type(type: IRoomSpaceAreaType) {
        if (this._space_area_type !== type) {
            this._space_area_type = type;
            if (this._space_area_room) {
                this._space_area_room.roomname = IType2UITypeDict[this.space_area_type];
                this._space_area_room._layout_scheme_list.length = 0;
            }
            this._area_layout_scheme_list = [];
        }
        this.setDefaultColor();

    }

    setSpaceAreaTypeByName(name: string) {
        let res = TSubSpaceAreaEntity.getSpaceAreaOptions();
        let ans_space_type: string = '' + RoomSpaceAreaType.UnknownArea;
        res.forEach((val) => {
            if (compareNames([val.label, val.value.toLocaleLowerCase()], [name.toLocaleLowerCase()])) {
                ans_space_type = val.value;
            }
        });
        this.space_area_type = ans_space_type as any;
    }
    static getSpaceAreaOptions() {
        let values = {
            [RoomSpaceAreaType.LivingArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.LivingArea]),
            [RoomSpaceAreaType.DiningArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.DiningArea]),
            [RoomSpaceAreaType.SleepingArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.SleepingArea]),
            [RoomSpaceAreaType.DressingArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.DressingArea]),
            [RoomSpaceAreaType.WashingArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.WashingArea]),
            [RoomSpaceAreaType.KitchenArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.KitchenArea]),
            [RoomSpaceAreaType.BalconyArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.BalconyArea]),
            [RoomSpaceAreaType.StudyArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.StudyArea]),
            [RoomSpaceAreaType.EntertainmentArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.EntertainmentArea]),
            [RoomSpaceAreaType.HallwayArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.HallwayArea]),
            [RoomSpaceAreaType.EntranceArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.EntranceArea]),
            [RoomSpaceAreaType.UnknownArea]: LayoutAI_App.t(IType2UITypeDict[RoomSpaceAreaType.UnknownArea])
        };
        return Object.keys(values).map((val) => {
            let label = (values as any)[val];

            return {
                label: label,
                value: val
            }
        });


    }
    bindRoomEntityFromList(room_entities: TRoomEntity[]) {
        let list = room_entities.filter((entity) => entity._room_poly.containsPoint(this.rect.rect_center));
        if (list.length > 0) {
            this.room_entity = list[0];

            this.updateSpaceAreaTRoom();
        }
    }

    extractTags() {
        let figure_elements: TFigureElement[] = [];
        this.furniture_entities.forEach((ele) => {
            figure_elements.push(...ele.disassembled_figure_elements);
        });
        this._tags = "";
        this._tags = (RemarkTagsExtractor.ExtractTags(figure_elements)).join(",");
    }

    exportData(): I_SwjEntityBase {
        let data = super.exportData() as I_SwjSubSpaceData;
        data.space_area_type = this.space_area_type;
        data.color_style = this.color_style;
        data.room_uidN = this._room_entity?.uidN;
        return data;
    }
    importData(data: I_SwjEntityBase): void {
        super.importData(data);
        this.space_area_type = (data as I_SwjSubSpaceData)?.space_area_type || RoomSpaceAreaType.UnknownArea;
        this.color_style = (data as I_SwjSubSpaceData)?.color_style || "#777777";
        if ((data as I_SwjSubSpaceData).room_uidN) {
            this._room_uidN = (data as I_SwjSubSpaceData).room_uidN;
        }
        this.updateSpaceAreaTRoom();
        this.bindEntity();
    }


    checkHoverCursorState(pos: Vector3Like): string {
        let dist = this.rect.distanceToPoint(pos);
        if (dist > 0) return null;
        let pp = this.rect.project(pos);

        let xx = this.rect.w / 2 - Math.abs(pp.x);
        let yy = this.rect.h / 2 - Math.abs(pp.y);

        if (Math.min(xx, yy) < 100) {
            if (xx < yy) {
                return LayoutAI_CursorState.Acrossmove;
            }
            else {
                return LayoutAI_CursorState.verticalmove;
            }

        }

        return null;
    }

    async updateSubLayoutScheme(options: { displayInUI?: boolean, auto_layout?: boolean, solver_methods?: SolverMethods[] } = { auto_layout: true, displayInUI: true }) {
        this.updateSpaceAreaTRoom();
        let scheme_list = await TSwjLayoutGraphSolver.instance.applyRoomWithSolvingMethods(this._space_area_room, "", options.solver_methods || ["SubSpaceTransfer", "SpacePartition"]);
        if (scheme_list) {
            scheme_list.sort((a, b) => {
                return (b.figure_list.figure_elements?.length || 0) - (a.figure_list.figure_elements.length || 0);
            })
        }
        this._space_area_room._layout_scheme_list = scheme_list;
        this._area_layout_scheme_list = [];
        scheme_list.forEach((scheme) => {
            let sub_scheme = new TSubAreaLayoutScheme(this);
            sub_scheme.fromRoomLayoutScheme(scheme);
            this._area_layout_scheme_list.push(sub_scheme);
        });
        // 如果推荐的没有分区布局也要刷新布局列表页面
        if (options.displayInUI) {
            LayoutAI_App.emit(EventName.SubAreaLayoutSchemeList, { schemeList: this._area_layout_scheme_list || [], index: 0, auto_layout: options.auto_layout || false });
        }
    }



    initProperties(): void {

        if (!this._ui_properties) {
            this._ui_properties = {};
        }
        this._ui_properties["ui_type"] = {
            name: LayoutAI_App.t("图层"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.ui_type,
            editable: false,
            props: {
                type: "input",
            }
        }

        this._ui_properties["space_area_type"] = {
            name: LayoutAI_App.t("分区类型"),
            widget: "LabelItem",
            type: "string",
            defaultValue: this.space_area_type,
            props: {
                type: "select",
                options: TSubSpaceAreaEntity.getSpaceAreaOptions(),
                optionWidth: 100,
                onChange: (value: string) => {
                    roomSubAreaService.updateSubAreaType(this, value as any);
                }
            }
        }

        this._ui_properties["color_style"] = {
            name: LayoutAI_App.t("颜色样式"),
            widget: "ColorWidget",
            type: "string",
            defaultValue: '' + this.color_style,
            props: {
                type: "select",
                optionWidth: 100

            }
        }

        this._ui_properties["tags"] = {
            name: LayoutAI_App.t("标签"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.tags,
            editable: false,
            props: {
                type: "input",
            }
        }
        this._ui_properties["generateSubLayout"] = {
            name: LayoutAI_App.t("分区布局推荐"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                this.updateSubLayoutScheme();
            }
        }
        this._ui_properties["saveSpaceTemplate"] = {
            name: LayoutAI_App.t("保存分区模板"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                TRoomTemplateSaver.saveSubAreaEntityTemplate(this).then(() => {
                    LayoutAI_App.emit(EventName.MessageTip, LayoutAI_App.t("保存分区模板完成!"));
                })
            }
        }
        this._ui_properties["querySpaceTemplates"] = {
            name: LayoutAI_App.t("更新分区模板"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                LayoutAI_App.RunCommand(LayoutAI_Commands.QuerySpaceTemplates);
            }
        }
        this._ui_properties["removeSubArea"] = {
            name: LayoutAI_App.t("删除分区"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteFurniture);
            }
        }

        this._ui_props_keys = ["ui_type", "space_area_type", "length", "depth", "color_style", "tags", "removeSubArea"];
        if (LayoutAI_App.IsDebug) {
            this._ui_props_keys = ["ui_type", "space_area_type", "length", "depth", "color_style", "tags", "saveSpaceTemplate", "querySpaceTemplates", "removeSubArea"];
        }
        this._bindPropertiesOnChange();
    }
    getUiProperties() {
        if (!this._ui_properties) {
            this.initProperties();
        }
        this.extractTags();

        // 先更新一下 一些列表信息
        if (this._ui_properties['ui_realType']) {
            this._ui_properties["ui_realType"].defaultValue = '' + this.ui_realType;
            this._ui_properties["ui_realType"].props.options = this.getRealTypeOptions();
        }
        if (this._ui_properties['tags']) {
            this._ui_properties["tags"].defaultValue = '' + this.tags;

        }

        let data: { [key: string]: IPropertyUI } = {};
        for (let key of this._ui_props_keys) {
            if (this._ui_properties[key]) {

                this._ui_properties[key].defaultValue = (this as any)[key];


                data[key] = this._ui_properties[key];
            }
        }
        return data;
    }

    _updateSubAreaByParentRoomEntity() {
        if (!this._room_entity) return;

        let room_poly = this._room_entity._room_poly;

        let tol = 100;
        for (let edge of this.rect.edges) {
            let t_room_edge: ZEdge = null;
            let min_dist_y: number = -1;
            let min_dist_x: number = 0;
            room_poly.edges.forEach((room_edge) => {
                if (room_edge.checkSameNormal(edge.nor, true, 0.1)) {
                    let x0 = room_edge.center.clone().sub(edge.center).dot(room_edge.dv);

                    let dist_x = Math.abs(x0) - (room_edge.length + edge.length) / 2;

                    if (dist_x < tol) {
                        let dist_y = room_edge.projectEdge2d(edge.center).y;
                        if (!t_room_edge || Math.abs(dist_y) < Math.abs(min_dist_y)) {
                            t_room_edge = room_edge;
                            min_dist_y = dist_y;
                            min_dist_x = dist_x;
                        }
                    }
                }
            });
            if (t_room_edge) {

                if ((min_dist_x < 0 && (Math.abs(min_dist_x) / edge.length > 0.5) && min_dist_y > 0) || Math.abs(min_dist_y) < tol) {
                    let offset = t_room_edge.nor.clone().multiplyScalar(-min_dist_y);
                    edge.moveEdge(offset);
                }
            }
        }
        this.rect.reParaFromVertices();


    }

    /**
     *  更新spaceArea的TRoom
     */
    updateSpaceAreaTRoom() {

        if (this._room_entity && this.rect) {
            let room_poly = this._room_entity._room_poly.clone();
            room_poly.reOrderByOrientation(true);
            let rect = this.rect.clone();
            rect.reOrderByOrientation(true);
            let intersect_polys = rect.intersect_polygons([room_poly]);

            if (intersect_polys && intersect_polys.length > 0) {
                this._area_shape = intersect_polys[0];
            }
        }


        if (this._area_shape) {
            let win_entities = this._room_entity._win_rects.filter((rect) => {
                let entity = TBaseEntity.getOrMakeEntityOfCadRect(rect);
                if (!entity) return false;
                let t_rect = rect.clone();
                let r_center = t_rect.rect_center;
                t_rect._h += 400;
                t_rect.rect_center = r_center;

                let int_polys = t_rect.intersect(this._area_shape);

                if (int_polys.length > 0) return true;
                return false;
            }).map((rect) => TBaseEntity.getOrMakeEntityOfCadRect(rect).exportSimpleData());


            for (let r_edge of this.rect.edges) {
                let onwall_pairs: number[][] = [];
                this._room_entity._room_poly.edges.forEach((edge) => {
                    if (edge.islayOn(r_edge, 300, 0.1)) {
                        let ll = r_edge.projectEdge2d(edge.v0.pos).x;
                        let rr = r_edge.projectEdge2d(edge.v1.pos).x;
                        if (ll > rr) {
                            let tmp = rr; rr = ll; ll = tmp;
                        }
                        onwall_pairs.push([ll, rr]);
                    }
                });
                let not_onwall_pairs = range_substract(r_edge.length, onwall_pairs);

                for (let pair of not_onwall_pairs) {
                    let ll = pair[0];
                    let rr = pair[1];
                    if (Math.abs(rr - ll) < 10) continue;

                    let thickness = 100;

                    let rect = new ZRect(rr - ll, thickness);
                    rect.nor = r_edge.nor;
                    rect.rect_center = r_edge.unprojectEdge2d({ x: (ll + rr) / 2, y: thickness / 2 });

                    let pos = rect.rect_center;
                    let hallway_door: I_Window = {
                        posX: pos.x,
                        posY: pos.y,
                        posZ: 0,
                        rotateZ: rect.rotation_z,
                        height: 2800,
                        length: rect.w,
                        width: rect.h,
                        rect: rect,
                        center: rect.rect_center,
                        nor: rect.nor,
                        type: this.space_area_type === RoomSpaceAreaType.KitchenArea ? "Door" : "Hallway",
                        realType: "SlidingDoor"
                    }
                    win_entities.push(hallway_door);
                }
            }


            if (!this._space_area_room) {
                this._space_area_room = new TRoom({
                    points: this._rect.vertices.map((v) => [v.pos.x, v.pos.y, 0]),
                    roomname: IType2UITypeDict[this.space_area_type],
                    windows: win_entities,
                    uid: '' + this.uidN,
                });
            }
            else {
                this._space_area_room.importRoomData({
                    points: this._rect.vertices.map((v) => [v.pos.x, v.pos.y, 0]),
                    roomname: IType2UITypeDict[this.space_area_type],
                    windows: win_entities,
                    uid: '' + this.uidN,
                });
            }
            this._space_area_room.room_id = this.uidN;
            this._space_area_room.updateFeatures();

            // this._space_area_room.updateFeatures();
            // console.log(this._space_area_room);
        }

    }
    _updateFurnitureEntities() {

        if (this.room_entity?.furniture_entities && this._space_area_room) {
            for (let entity of this.room_entity.furniture_entities) {
                if (entity.roomEntity === this.room_entity) {
                    if (this._space_area_room.room_shape._poly.containsPoint(entity.rect.rect_center)) {
                        entity.area_entity = this;
                    }
                }
            }
        }
    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (options.is_selected) {
            painter.fillStyle = this._color_style;
            painter.strokeStyle = "#147FFA";
            painter.fillPolygon(this._rect, 0.27);
            painter._context.lineWidth = 2;
            painter.strokePolygons([this._rect]);
            painter._context.lineWidth = 1;

            if (this._space_area_room) {
                this._space_area_room._painter = painter;
                // this._space_area_room.drawRoomWithWalls(100);
                // this._space_area_room.drawRoomWindows("Training");
            }
        }
        else if (options.is_hovered) {
            painter.fillStyle = this._color_style;
            painter.strokeStyle = this._color_style;
            painter.fillPolygon(this._rect, 0.25);
            painter.strokeStyle = "#147FFA";
            painter._context.lineWidth = 2;
            painter.strokePolygons([this._rect]);
            painter._context.lineWidth = 1;
        }
        if (options.is_draw_figure) {
            painter.fillStyle = this._color_style;
            painter.fillPolygon(this._rect, 0.2);
            painter.strokeStyle = this._color_style;
            painter.strokePolygons([this._rect]);

            let text = LayoutAI_App.t(IType2UITypeDict[this.space_area_type]) || LayoutAI_App.t("未命名");

            painter.drawText(text, this.rect.rect_center, 0, painter.clientScale > 1.5 ? 50 : 38, 10, true, true)
        }

    }

    static extractSubAreaRectByRule(rule: I_SimpleAreaExtractRule, room: TRoom = null, figures: TFigureElement[] = null) {
        figures = figures || room?._furniture_list;

        if (!figures) return null;
        let main_figures = figures.filter((fig) => compareNames([fig.category, fig.sub_category], [rule.main_figure]));
        main_figures.sort((a, b) => b.rect.w - a.rect.w);
        if (!main_figures[0]) return null;

        let rect = main_figures[0].rect.clone();

        let postions = [...rect.positions];
        let group_range = rule.group_range || { front_dist: 10000, side_dist: 1000, back_dist: 1000 };
        let range_rect = rect.clone();
        let t_pos = range_rect.unproject({ x: 0, y: (group_range.front_dist - group_range.back_dist) / 2 });
        range_rect._w += group_range.side_dist * 2;
        range_rect._h += (group_range.front_dist + group_range.back_dist) / 2;
        range_rect.rect_center = t_pos;
        figures.forEach(fig => {
            if (!compareNames([fig.category, fig.sub_category], rule.sub_figures)) {
                return;
            }

            let intersects = fig.rect.clone().intersect(range_rect);
            if (intersects.length == 0) return;
            postions.push(...fig.rect.positions);
        });
        let area_rect = ZRect.fromPoints(postions, rect.nor);
        if (rule.is_expand_to_front_wall && room) {
            let int_p = room.room_shape._poly.getRayIntersection(rect.rect_center, rect.nor);
            if (int_p && int_p.point) {
                let dist = area_rect.backEdge.projectEdge2d(int_p.point).y;
                if (dist > 0) {
                    area_rect._h = dist;
                    area_rect.updateRect();
                }
            }
        }

        return area_rect;
    }

    exportSubAreaSimpleData(index: number): I_RoomSubAreaSimpleData {
        let data = {
            room_uuid: this.room_entity._uuid,
            index: index,
            name: this.name,
            space_area_type: this.space_area_type,
            // 克隆，避免被修改
            area_rect: this.rect.clone(),
        }
        return data;
    }

}