import React from "react";

import { EventName } from "@/Apps/EventSystem";
import { I_TLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TSubAreaLayoutScheme";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";


interface LayoutSchemeDetailProps {
    selectedScheme: I_TLayoutScheme | null;
    onClose: () => void;
}

const LayoutSchemeDetail: React.FC<LayoutSchemeDetailProps> = ({ selectedScheme, onClose }) => {
    if (!selectedScheme) {
        return null;
    }

    // 选中scheme时更新场景
    const handleSchemeSelect = () => {
        if (selectedScheme) {
            // 检查是否有必要的属性来判断是否为有效的布局方案
            if (selectedScheme.figure_list && selectedScheme.figure_list.figure_elements) {
                // 获取当前选中的房间
                const container = (LayoutAI_App.instance as any).layout_container;
                if (container && container._selected_room) {
                    const room = container._selected_room;

                    // 重要：不要直接更新房间的布局方案列表
                    // 因为selectedScheme是I_TLayoutScheme接口，不是TRoomLayoutScheme实例
                    // 我们需要找到原始的TRoomLayoutScheme实例

                    // 查找原始的TRoomLayoutScheme实例
                    if (room._layout_scheme_list && room._layout_scheme_list.length > 0) {
                        // 找到与当前selectedScheme匹配的原始实例
                        const originalScheme = room._layout_scheme_list.find((scheme: any) =>
                            scheme._scheme_name === selectedScheme._scheme_name ||
                            scheme.figure_list === selectedScheme.figure_list
                        );

                        if (originalScheme) {
                            // 设置为选中的方案
                            room.selectIndex = room._layout_scheme_list.indexOf(originalScheme);

                            // 使用原始方案中的图元，确保方法完整
                            if (originalScheme.figure_list && originalScheme.figure_list.figure_elements) {
                                // 清空当前家具列表
                                room.furnitureList = [];

                                // 清除场景中的2D图标
                                if (room._room_entity) {
                                    // 尝试使用可用的方法来清除图元
                                    try {
                                        // 方法1: 尝试清除所有图元
                                        if (typeof room._room_entity.clearAllFigures === 'function') {
                                            room._room_entity.clearAllFigures();
                                        }
                                        // 方法2: 尝试清除图元列表
                                        else if (typeof room._room_entity.clearFigures === 'function') {
                                            room._room_entity.clearFigures();
                                        }
                                        // 方法3: 尝试重置图元状态
                                        else if (typeof room._room_entity.resetFigures === 'function') {
                                            room._room_entity.resetFigures();
                                        }
                                        // 方法4: 如果都没有，尝试手动清除
                                        else if (room._room_entity.figures) {
                                            room._room_entity.figures = [];
                                        }

                                        console.log('已清除旧的2D图标');
                                    } catch (error) {
                                        console.warn('清除2D图标时出错，继续执行:', error);
                                    }
                                }

                                // 获取原始方案中的图元元素
                                const originalFigureElements = [...originalScheme.figure_list.figure_elements];
                                originalFigureElements.sort((a, b) => {
                                    const orderA = (a as any).default_drawing_order || 0;
                                    const orderB = (b as any).default_drawing_order || 0;
                                    const differ_order = orderA - orderB;
                                    if (differ_order === 0) {
                                        const zA = (a as any).min_z || 0;
                                        const zB = (b as any).min_z || 0;
                                        return zA - zB;
                                    } else {
                                        return differ_order;
                                    }
                                });

                                // 添加家具元素到房间
                                for (const figure of originalFigureElements) {
                                    try {
                                        if (figure && typeof (figure as any).drawFigure === 'function') {
                                            room.addFurnitureElement(figure);
                                            if (room.name.indexOf("厨房") < 0) {
                                                if ((figure as any).clearMatchedMaterials) {
                                                    (figure as any).clearMatchedMaterials();
                                                }
                                            }
                                        } else {
                                            console.warn('图元缺少drawFigure方法，跳过:', figure);
                                        }
                                    } catch (error) {
                                        console.error('添加图元时出错:', error, figure);
                                    }
                                }

                                // 更新房间空间信息
                                if (room._room_entity) {
                                    room._room_entity.updateSpaceLivingInfo({ force_auto_sub_area: true });
                                }
                            }

                            // 发送事件通知场景更新
                            LayoutAI_App.emit(EventName.LayoutSchemeList, {
                                schemeList: room._layout_scheme_list,
                                index: room.selectIndex
                            });
                        }
                    }

                    // 更新场景
                    LayoutAI_App.instance.update();
                }
            }
        }
    };

    return (
        <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1000,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        }}>
            <div className="scheme_details" style={{
                backgroundColor: '#fff',
                padding: '24px',
                borderRadius: '8px',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                width: '70vw',
                maxWidth: '1200px',
                maxHeight: '70vh',
                overflow: 'auto',
                position: 'relative',
                border: '1px solid #e8e8e8'
            }}>
                {/* 关闭按钮 */}
                <button
                    onClick={onClose}
                    style={{
                        position: 'absolute',
                        top: '16px',
                        right: '20px',
                        border: 'none',
                        background: 'none',
                        cursor: 'pointer',
                        fontSize: '18px',
                        color: '#999',
                        width: '28px',
                        height: '28px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '50%',
                        transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f0f0f0';
                        e.currentTarget.style.color = '#666';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#999';
                    }}
                    title="关闭"
                >
                    ✕
                </button>

                {/* 标题和操作按钮 */}
                <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '24px',
                    paddingRight: '50px'
                }}>
                    <h3 style={{
                        margin: 0,
                        color: '#1890ff',
                        fontSize: '20px'
                    }}>
                        Scheme详情
                    </h3>

                    {/* 应用方案按钮 */}
                    <button
                        onClick={handleSchemeSelect}
                        style={{
                            padding: '8px 16px',
                            backgroundColor: '#1890ff',
                            color: '#fff',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 'bold',
                            transition: 'all 0.2s'
                        }}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#40a9ff';
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '#1890ff';
                        }}
                        title="在场景中应用此方案"
                    >
                        应用方案
                    </button>
                </div>

                {/* 显示scheme的基本信息 */}
                <div className="scheme_info" style={{
                    marginBottom: '24px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '16px'
                }}>
                    <div style={{
                        padding: '12px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '6px',
                        border: '1px solid #e9ecef'
                    }}>
                        <strong>Scheme名称:</strong> {selectedScheme._scheme_name || 'N/A'}
                    </div>
                    <div style={{
                        padding: '12px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '6px',
                        border: '1px solid #e9ecef'
                    }}>
                        <strong>图元数量:</strong> {selectedScheme.figure_list ? Object.keys(selectedScheme.figure_list).length : 'N/A'}
                    </div>
                    <div style={{
                        padding: '12px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '6px',
                        border: '1px solid #e9ecef'
                    }}>
                        <strong>评分项数量:</strong> {selectedScheme._layout_scores ? selectedScheme._layout_scores.length : 'N/A'}
                    </div>
                </div>

                {/* 显示详细的评分信息 */}
                <div className="score_details">
                    <h4 style={{ margin: '0 0 20px 0', color: '#333', fontSize: '16px' }}>评分详情:</h4>

                    {/* 没有children的评分项直接显示 */}
                    <div style={{ marginBottom: '16px' }}>
                        {selectedScheme._layout_scores && selectedScheme._layout_scores
                            .filter(scoreItem => !scoreItem.children || scoreItem.children.length === 0)
                            .map((scoreItem, scoreIndex) => (
                                <div key={`simple-${scoreIndex}`} style={{
                                    display: 'inline-block',
                                    padding: '8px 16px',
                                    backgroundColor: scoreItem.score <= -100 ? '#fff2f0' : '#f6ffed',
                                    border: `1px solid ${scoreItem.score <= -100 ? '#ffccc7' : '#b7eb8f'}`,
                                    borderRadius: '6px',
                                    margin: '4px 8px 4px 0',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                    color: scoreItem.score <= -100 ? '#cf1322' : '#389e0d'
                                }}>
                                    {scoreItem.name}: {scoreItem.score}
                                </div>
                            ))}
                    </div>

                    {/* 有children的评分项放到滚动列表中 */}
                    {selectedScheme._layout_scores && selectedScheme._layout_scores.some(scoreItem =>
                        scoreItem.children && scoreItem.children.length > 0
                    ) && (
                            <>
                                <div style={{
                                    maxHeight: '300px',
                                    overflow: 'auto',
                                    border: '1px solid #e8e8e8',
                                    borderRadius: '6px',
                                    padding: '16px',
                                    backgroundColor: '#fafafa'
                                }}>
                                    <div style={{
                                        display: 'grid',
                                        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                                        gap: '16px'
                                    }}>
                                        {selectedScheme._layout_scores
                                            .filter(scoreItem => scoreItem.children && scoreItem.children.length > 0)
                                            .map((scoreItem, scoreIndex) => (
                                                <div key={`complex-${scoreIndex}`} style={{
                                                    padding: '16px',
                                                    backgroundColor: scoreItem.score <= -100 ? '#fff2f0' : '#f6ffed',
                                                    border: `1px solid ${scoreItem.score <= -100 ? '#ffccc7' : '#b7eb8f'}`,
                                                    borderRadius: '6px',
                                                    minHeight: 'fit-content'
                                                }}>
                                                    <div style={{
                                                        fontSize: '14px',
                                                        fontWeight: 'bold',
                                                        color: scoreItem.score <= -100 ? '#cf1322' : '#389e0d',
                                                        marginBottom: '8px'
                                                    }}>
                                                        {scoreItem.name}: {scoreItem.score}
                                                    </div>
                                                    <div style={{ marginLeft: '16px' }}>
                                                        {scoreItem.children.map((child, childIndex) => (
                                                            <div key={childIndex} style={{
                                                                color: child.score <= -100 ? '#cf1322' : '#389e0d',
                                                                fontSize: '13px',
                                                                margin: '4px 0',
                                                                padding: '6px 8px',
                                                                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                                                                borderRadius: '4px',
                                                                border: `1px solid ${child.score <= -100 ? '#ffccc7' : '#b7eb8f'}`
                                                            }}>
                                                                • {child.name}: {child.score}
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </div>
                            </>
                        )}
                </div>
            </div>
        </div>
    );
};

export default LayoutSchemeDetail;
