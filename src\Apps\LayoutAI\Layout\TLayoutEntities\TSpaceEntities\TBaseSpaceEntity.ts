import { ZR<PERSON><PERSON> } from "@layoutai/z_polygon";
import { I_EntityDrawingState, TBaseEntity } from '../TBaseEntity';
import { BaseSpaceType, IBaseSpaceType, IRoomEntityType, IType2UITypeDict, I_Window } from '../../IRoomInterface';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { TPainter } from '@/Apps/LayoutAI/Drawing/TPainter';
import { TRoomShape } from '../../TRoomShape';
import { WPolygon } from '../../TFeatureShape/WPolygon';
import { I_SplitSpaceItem, I_SwjBaseSpaceData, I_SwjEntityBase, I_SwjStructureData } from '@/Apps/LayoutAI/AICadData/SwjLayoutData';
import { ZPolygon } from "@layoutai/z_polygon";
import { ZDistanceDimension, makeDimensionsOfEdge } from "@layoutai/z_polygon";
import { ZTriangulation } from "@layoutai/z_polygon";

export interface I_BaseSpaceAttachedElement
{
    inner_space_area ?: number;
}

/**
 *   抽象的基础空间实体, 其要求起码是一个封闭空间
 *
 */
export class TBaseSpaceEntity extends TBaseEntity {
    static readonly EntityType: IRoomEntityType = 'BaseSpace';
    protected _space_type: IBaseSpaceType|string;

    protected _color_style: string;

    protected _w_poly_color_style :string;

    protected _main_rect: ZRect;
    protected _area: number;

    protected _parentSpace : TBaseSpaceEntity;
    protected _childrenSpaces: TBaseSpaceEntity[];

    /**
     *   w_polygon 类似TRoom里的w_polygon, 一些门窗墙属性会绑定到edge上
     */
    protected _w_polygon : ZPolygon;



    
    protected _splitItems : I_SplitSpaceItem[];

    /**
     *  内空多边形: 去掉墙、板后产生的多边形
     */
    protected _inner_space_poly : ZPolygon;

    /**
     *  边缘板件|墙体多边形
     */
    protected _border_rects : ZRect[];

    /**
     *  绑定的其它entities
     */
    protected _attached_entities : TBaseEntity[];

    constructor(rect: ZRect, name: string = "未命名",space_type:string = BaseSpaceType.OtherAreaSpace) {
        super(rect);
        this.type = TBaseSpaceEntity.EntityType;
        this._parentSpace = null;
        this._childrenSpaces = [];
        this.title = LayoutAI_App.t('基础空间');
        this.space_type = space_type;
        this.name = name;
        this._polygon = null;
        this._w_polygon = new ZPolygon();
        this._inner_space_poly = new ZPolygon();
        this._color_style = '#66b8ff';
        this._w_poly_color_style = "#ffb866";
        this._main_rect = null;
        this._area = 0;
        this._priority_for_selection = 5;
        this._splitItems = [];
        this._border_rects = [];
        this.update();
        this.bindEntity();
    }
    bindEntity(): void {
        if (this.space_poly) {
            TBaseEntity.bindEntityOfPolygon(this.space_poly, this);
        }
        if (this._rect) {
            TBaseEntity.bindEntityOfPolygon(this.rect, this);
        }
        this.setRectProperties();
    }
    importData(data: I_SwjBaseSpaceData): void {
        super.importData(data);
        this.space_type = data.space_type || BaseSpaceType.BaseSpace;
        if(data.childrenSpaces)
        {
            this._childrenSpaces = data.childrenSpaces.map((data)=>{
                let t = new TBaseSpaceEntity(new ZRect(),"",BaseSpaceType.BaseSpace);
                t.importData(data);
                t.parent = this;
                return t;
            })
        }
        if(data.splitParams)
        {
            this._splitItems = data.splitParams.map((params)=>{
                return {
                    interval :params.interval,
                    splitRefer : params.splitRefer,
                    category : params.category
                }
            })
        }
    }
    exportData(): I_SwjBaseSpaceData {
        let data : I_SwjBaseSpaceData = super.exportData();
        data.space_type = this.space_type;
        if(this.childrenSpaces)
        {
            data.childrenSpaces = this.childrenSpaces.map((space)=>space.exportData());
        }
        if(this._splitItems)
        {
            data.splitParams = this._splitItems.map((params)=>{return {
                interval :params.interval,
                splitRefer : params.splitRefer,
                category : params.category
            }})
        }
        return data;
    }

    isLeafSpace()
    {
        return !this.childrenSpaces || this._childrenSpaces.length==0;
    }

    isRootSpace()
    {
        return !this.parent;
    }

    set parent(p:TBaseSpaceEntity)
    {
        if(this._parentSpace)
        {
            this._parentSpace.removeChild(this);
        }
        this._parentSpace = p;

        if(this._parentSpace)
        {
            this._parentSpace.addChild(this);
        }
        
    }
    get parent()
    {
        return this._parentSpace;
    }

    get attached_entities()
    {
        if(!this._attached_entities) this._attached_entities =[];
        return this._attached_entities;
    }

    protected removeChild(space:TBaseSpaceEntity)
    {
        let id = this._childrenSpaces.indexOf(space);
        if(id >= 0) this._childrenSpaces.splice(id,1);
    }
    protected addChild(space:TBaseSpaceEntity)
    {
        if(space === this) return;
        let id = this._childrenSpaces.indexOf(space);
        if(id < 0)
        {
            this._childrenSpaces.push(space);
        }
    }
    cleanChildren()
    {
        if(this._childrenSpaces)
        {
            let children = [...this.childrenSpaces];
        
            children.forEach(child=>child.parent = null);
        }
    }
    
  
    /**
     *   w_polygon 类似TRoom里的w_polygon, 一些门窗墙属性会绑定到edge上
     */
    get w_polygon()
    {
        return this._w_polygon;
    }

    get inner_space_poly()
    {
        return this._inner_space_poly;
    }
    get childrenSpaces() {
        return this._childrenSpaces;
    }

    get space_poly() {
        return this._polygon || this.rect;
    }

    get area()
    {
        return this._area;
    }
    get main_rect()
    {
        return this._main_rect;
    }

    get border_rects()
    {
        return this._border_rects;
    }
    public get space_type(): string {
        return this._space_type;
    }
    public set space_type(value: string) {
        this._space_type = value;
    }
    public get color_style(): string {
        return this._color_style;
    }
    public set color_style(value: string) {
        this._color_style = value;
    }

    public get splitItems()
    {
        return this._splitItems;
    }
    update(): void {

    }

    get attached_elements() : I_BaseSpaceAttachedElement
    {
        return this.rect._attached_elements;
    }

    get inner_space_area()
    {
        return this.attached_elements.inner_space_area || 0;
    }
    updateInnerSpaceArea()
    {
        if(!this._inner_space_poly || this.inner_space_poly.edges.length < 3) return;

        let res =  ZTriangulation.triagulate(this.inner_space_poly);

        this.attached_elements.inner_space_area = res.area;

    }
    computeMainRect() {
        if (!this.space_poly) return;

        this._main_rect = TRoomShape.computeMaxRectBySplitShape(this.space_poly);

        let polys = WPolygon.splitPolyIntoRects(this.space_poly, "SplitRoom");

        let area = 0;
        for (let poly of polys) {
            if (poly.edges.length != 4) {
                // console.log("TRoomArea区域可能存在非四边形的子区域");
                continue;
            }
            let rect = ZRect.computeMainRect(poly);
            let t_area = (rect.w / 1000 * rect.h / 1000);
            area += t_area;
            if (!this._main_rect || rect.min_hh > this._main_rect.min_hh) {
                this._main_rect = rect;
            }
        }
        if (polys.length == 0) {
            this._main_rect = this._rect.clone();
            area = (this._main_rect.w * this._main_rect.h) / (1000 * 1000);
        }


        this._area = area;
    }
    getBaseSpaceTypeOptions()
    {
        let values = {
            [BaseSpaceType.BaseSpace]: LayoutAI_App.t(IType2UITypeDict[BaseSpaceType.BaseSpace]),
            [BaseSpaceType.RoomGroupSpace]: LayoutAI_App.t(IType2UITypeDict[BaseSpaceType.RoomGroupSpace]),
            [BaseSpaceType.PublicAreaSpace]: LayoutAI_App.t(IType2UITypeDict[BaseSpaceType.PublicAreaSpace]),
            [BaseSpaceType.OtherAreaSpace]: LayoutAI_App.t(IType2UITypeDict[BaseSpaceType.OtherAreaSpace]),

        };

        return Object.keys(values).map((val) => {
            let label = (values as any)[val];

            return {
                label: label,
                value: val
            }
        });
    }
    initProperties(): void {

        if (!this._ui_properties) {
            this._ui_properties = {};
        }
        this._ui_properties["ui_type"] = {
            name: LayoutAI_App.t("图层"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.ui_type,
            editable: false,
            props: {
                type: "input",
            }
        }
        this._ui_properties["name"] = {
            name : LayoutAI_App.t("名称"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.name,
            editable: false,
            props: {
                type: "input",
            }
        }

        this._ui_properties["space_type"] = {
            name: LayoutAI_App.t("分区类型"),
            widget: "LabelItem",
            type: "string",
            defaultValue: this.space_type,
            props: {
                type: "select",
                options: this.getBaseSpaceTypeOptions(),
                optionWidth: 100,
            }
        }

        this._ui_properties["color_style"] = {
            name: LayoutAI_App.t("颜色样式"),
            widget: "ColorWidget",
            type: "string",
            defaultValue: '' + this.color_style,
            props: {
                type: "select",
                optionWidth: 100

            }
        }
        this._ui_properties["length"] = {
            name: LayoutAI_App.t("宽度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 200000,
            editable: true,
            defaultValue: '' + Math.round(this.length),
            props: {
                type: "number",
                // suffix :"mm"
            }
        }
        this._ui_properties["width"] = {
            name: LayoutAI_App.t("深度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 200000,
            editable: false,
            defaultValue: '' + Math.round(this.width),
            props: {
                type: "number",
                // suffix :"mm"
            }
        }
        this._ui_properties["splitSpaces"] = {
            name: LayoutAI_App.t("分割空间"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.SplitBuildingSpace, this);
            }
        }
        this._ui_props_keys = ["ui_type", "name", "space_type","color_style","length","width","splitSpaces"];

        this._bindPropertiesOnChange();
    }
    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (options.is_selected) {
            painter.fillStyle = this._color_style;
            painter.strokeStyle = '#147FFA';
            painter.fillPolygon(this.space_poly, 0.05);
            painter._context.lineWidth = 2;
            painter.strokePolygons([this.space_poly]);
            painter._context.lineWidth = 1;
            
            painter.drawPointRect(this.rect.unproject({x:1000,y:0}),200);
        } else if (options.is_hovered) {
            painter.fillStyle = this._color_style;
            painter.strokeStyle = this._color_style;
            painter.fillPolygon(this.space_poly, 0.25);
            painter.strokeStyle = '#147FFA';
            painter._context.lineWidth = 2;
            painter.strokePolygons([this.space_poly]);
            painter._context.lineWidth = 1;
        }
        if (options.is_draw_figure) {

            painter.strokeStyle = this._color_style;

            if(this.childrenSpaces && this.childrenSpaces.length > 0)
            {
                this.childrenSpaces.forEach((space)=>space.drawEntity(painter,options));
            }
            else{
                painter.fillStyle = this._color_style;
                
                if(this.w_polygon && this.w_polygon.vertices.length > 0)
                {
                    painter.strokePolygons([this.w_polygon]);

                    painter.fillPolygon(this.w_polygon, 0.2);

                    if(this.inner_space_poly)
                    {
                        painter.strokePolygons([this.inner_space_poly]);
                    }
                    if(this.border_rects)
                    {
                        painter.fillStyle = "#7b7b7b";
                        painter.fillPolygons(this.border_rects,0.8);
                    }
                    if(this._attached_entities)
                    {
                        let ignore_types :IRoomEntityType[] = ["SubArea","RoomArea"];
                        this._attached_entities.forEach((entity)=>{
                            if(ignore_types.includes(entity.type)) return;
                            entity.drawEntity(painter,options);
                        })
                    }

                }
                else{
                    painter.fillPolygon(this.space_poly, 0.2);
                    painter.strokePolygons([this.space_poly]);

                }
                painter.fillStyle = "#2b2b2b";
                let area = this.rect.area_in_miles;
                if(this.inner_space_area)
                {
                    area = this.inner_space_area / 1000 / 1000.;
                }
                let text = this.name + "\n" + area.toFixed(2) + "m²";

                painter.drawText(
                    text,
                    this.rect.rect_center,
                    0,
                    painter.clientScale > 1.5 ? 80 : 60,
                    10,
                    true,
                    true
                );
            }


        }
    }


}
