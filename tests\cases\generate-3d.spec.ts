import { test } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import {  clickToSave,searchAndOpen } from '../utils/basicProcess';
import { compareScreenshot } from '../utils/screenshotUtils';


test.describe('生成3D方案', async () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('生成3D方案', async ({ page }) => {
    // 1.选中户型
    searchAndOpen(page, designPage);

    // 2.应用布局：选择左侧布局列表中的第二个全屋方案
    const secondScheme = page.locator('#side_list_div > div:nth-child(2)');
    await secondScheme.waitFor({ state: 'visible' });
    await secondScheme.click();

    const nextStep = await page.locator("button > span:has-text('下一步')");
    await designPage.safeClick(nextStep);
    console.info("点击'下一步'按钮");

    const platformTab = await page.locator("span:has-text('平台')");
    await designPage.safeClick(platformTab);
    console.info("点击左侧面板的平台标签页");

    // 定位并悬停在第一个系列图片上
    const firstSeriesImage = await page.locator('img[alt*="series-"]').first();
    await firstSeriesImage.hover();
    console.info("选中点击左侧面板第一个套系");

    const applyButton = await page.locator("button:has-text('全部应用')");
    await designPage.safeClick(applyButton);
    console.info("点击'全部应用'按钮");

    await page.waitForTimeout(20000);
    await page.getByRole('button', { name: '生成3D方案' }).click();

    await page.waitForTimeout(3000);
    await compareScreenshot(page, 'generate-3d', 'generate.png');

  });
});
