import { IssueService } from '@/Apps/LayoutAI/Services/Basic/IssueService';
import { EventName } from '@/Apps/EventSystem';
import { TAILayoutDrawingLayer } from '@/Apps/LayoutAI/Drawing/TAILayoutDrawingLayer';
import { TAIMatchingDrawingLayer } from '@/Apps/LayoutAI/Drawing/TAIMatchingDrawingLayer';
import { TCadFloorDrawingLayer } from '@/Apps/LayoutAI/Drawing/TCadFloorDrawingLayer';
import { TCadFurnitureLayer } from '@/Apps/LayoutAI/Drawing/TCadFurnitureLayer';
import { CadDrawingLayerType } from '@/Apps/LayoutAI/Drawing/TDrawingLayer';
import { I_MaterialMatchingItem } from '@/Apps/LayoutAI/Layout/IMaterialInterface';
import { DrawingFigureMode } from '@/Apps/LayoutAI/Layout/IRoomInterface';
import { TFigureElement } from '@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement';
import { TLayoutAllMatchFineTuningManagerToolUtil } from '@/Apps/LayoutAI/Layout/TLayoutFineTuningOperation/TLayoutAllMatchFineTuningManagerToolUtil';
import {
  TLayoutGroupFineTuningManagerToolUtil,
  TLayoutGroupFineTuningType
} from '@/Apps/LayoutAI/Layout/TLayoutFineTuningOperation/TLayoutGroupFineTuningManagerToolUtil';
import { TPostFigureElementAdjust } from '@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostFigureElementAdjust';
import { TPostDecoratesLayout } from '@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutDecorates';
import { TMaterialMatcher } from '@/Apps/LayoutAI/Services/MaterialMatching/TMaterialMatcher';
import { TSeriesFurnisher } from '@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { TBaseGroupEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity';
import { TFurnitureEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity';
import { TWindowDoorEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity';
import { TSeriesSample } from '@/Apps/LayoutAI/Layout/TSeriesSample';
import { I_MouseEvent, formatCurrentTime } from "@layoutai/z_polygon";
import { Logger } from '@/Apps/LayoutAI/Utils/logger';
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { ENV } from '@/config';
import {
  getKgSchemeListOfOrganization,
  getKgSchemeListOfPlatform
} from '@/pages/Design/components/service';
import { SensorsLogger } from '@/services/SensorsLogger';
import { AI2BaseModeHandler } from '../AI2BaseModeHandler';
import { AI2DesignManager } from '../AI2DesignManager';
import { AIMatchingSubHandler } from './SubHandlers/AIMatchingSubHandlers/AIMatchingSubHandler';
import { EditCeilingSubHandler } from './SubHandlers/AIMatchingSubHandlers/EditCeilingSubHandler';
import { CombinationHandler } from './SubHandlers/CadEditSubHandlers/CombinationHandler';
import { DimensionFurnitureHandler } from './SubHandlers/CadEditSubHandlers/DimensionFurnitureHandler';
import { MoveFurnitureSubHandler } from './SubHandlers/CadEditSubHandlers/MoveFurnitureSubHandler';
import { RotateEntityHandler } from './SubHandlers/CadEditSubHandlers/RotateEntityHandler';
import { ScaleEntitySubHandler } from './SubHandlers/CadEditSubHandlers/ScaleEntitySubHandler';
import { T_DimensionElement } from '../../LayoutAI/Layout/TransformElements/T_DimensionElement';
import { T_MoveElement } from '../../LayoutAI/Layout/TransformElements/T_MoveElement';
import { T_RotateElement } from '../../LayoutAI/Layout/TransformElements/T_RotateElement';
import { TBaseEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity';

enum SelectingMode {
  SeriesSampleMode,
  LayoutMode
}

export class AIMatchingModeHandler extends AI2BaseModeHandler {
  //当前选中的图元
  _figure_element_selected: TFigureElement;

  _selecting_mode: SelectingMode;
  _kitchen_use_default_material: boolean;

  _enable_hard_furnish: boolean;

  _layout_scheme_id: string = null;
  _debug: boolean = true;

  _enable_turing_layout: boolean = true;
  constructor(manager: AI2DesignManager) {
    super(manager, 'DesignMode');
    this._selecting_mode = SelectingMode.SeriesSampleMode;
    this._enable_hard_furnish = true;
    this._kitchen_use_default_material = true;

    this.logger.quiet = ENV == 'prod';

    new TPostDecoratesLayout();
    this._cad_default_sub_handler = new AIMatchingSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Scaling] = new ScaleEntitySubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Moving] = new MoveFurnitureSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Dimension] = new DimensionFurnitureHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Combination] = new CombinationHandler(this); //进入组合模式
    this._sub_handlers[LayoutAI_Commands.Transform_Rotate] = new RotateEntityHandler(this); //进入旋转模式
    this._sub_handlers[LayoutAI_Commands.EditCeiling] = new EditCeilingSubHandler(this); //吊顶编辑模式

    this._candidate_target_rects = [];
    this._exsorb_target_rects = [];
    this._transform_elements = [];

    this._transform_elements.push(
      new T_RotateElement(1, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(new T_DimensionElement(0, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(1, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(2, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(3, this.manager.layout_container));
    // 后面就把那四个编辑的点给加进去
    this._transform_moving_element = new T_MoveElement(
      this._selected_target,
      this.manager.layout_container
    );

    this._transform_elements.push(this._transform_moving_element);
  }

  get logger() {
    return Logger.instance;
  }
  get layer_CadFloorLayer() {
    return this.manager.drawing_layers[
      CadDrawingLayerType.CadFloorDrawing
    ] as TCadFloorDrawingLayer;
  }
  get layer_ai_layout_layer() {
    return this.manager.drawing_layers[
      CadDrawingLayerType.AILayoutDrawing
    ] as TAILayoutDrawingLayer;
  }

  get layer_ai_matching_layer() {
    return this.manager.drawing_layers[
      CadDrawingLayerType.AIMatchingDrawing
    ] as TAIMatchingDrawingLayer;
  }

  /**
   *  swj_layout_scheme_layer 的房间列表
   */
  get room_list() {
    return this.manager.layout_container._rooms;
  }

  get painter() {
    return this.manager?.painter;
  }

  getEditorFurnitureRects(): ZRect[] {
    let cadFurnitureLayer = this.manager.drawing_layers[
      CadDrawingLayerType.CadFurniture
    ] as TCadFurnitureLayer;
    return cadFurnitureLayer.furnitureRects;
  }

  async prepareTestData(): Promise<void> {
    if (this.manager.layout_container._room_entities.length == 0) {
      await this.manager._load_local_XmlSchemeData();
      this.manager.layer_CadEzdxfLayer._clean();
      this.manager.layer_CadEzdxfLayer.ezdxf_data = null;
      this.EventSystem.emit(EventName.LayoutSchemeOpened, {
        id: this.manager.layout_container._scheme_id,
        name: this.manager.layout_container._layout_scheme_name
      });

      this.manager.layout_container.focusCenter();

      this.manager.layout_container.needs_making_wall_xml = true;
      this._cad_default_sub_handler.updateCandidateRects();

      for (let room of this.room_list) {
        if (!room.room_shape?._feature_shape?._w_poly) {
          room.updateFeatures();
        }

        if (room._furniture_list && room._furniture_list.length > 0) {
          TPostFigureElementAdjust.post_remove_unvalid_onwall_elements(room);
          TPostDecoratesLayout.post_add_decorations(room);
        }
      }
      TSeriesFurnisher.instance.current_rooms = this.room_list;
      TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering();

      LayoutAI_App.emit_M(EventName.RoomList, this.room_list);

      this.update();
    }
  }

  async prepare(load_test_data: boolean = true): Promise<void> {
    if (load_test_data) {
      await this.prepareTestData();
    }
  }

  addFigureToList(figure: TFigureElement, room: TRoom) {
    room.addFurnitureElement(figure);
  }

  async prepareschemeData() {
    // 直接读取自定义布局的数据
    for (let room of this.room_list) {
      if (!room.room_shape?._feature_shape?._w_poly) {
        room.updateFeatures();
      }

      if (room._furniture_list && room._furniture_list.length > 0) {
        TPostFigureElementAdjust.post_remove_unvalid_onwall_elements(room);
        TPostDecoratesLayout.post_add_decorations(room);
      }
    }
    LayoutAI_App.emit(EventName.PrepareProgress, { progress: 'finish' });

    this.manager.layout_container.focusCenter();

    this.update();

    // 判断空间是否可选
    this.isSelectRoom();

    TSeriesFurnisher.instance.refreshRoom2SeriesSample(this.room_list);

    TSeriesFurnisher.instance.clearCurrentRooms();

    this.updateRoomSelected({
      posX: -10000,
      posY: -10000,
      ctrlKey: false
    });

    await Promise.all(TSeriesFurnisher.instance.tryToFurnishRooms(this.room_list));
    this._logger.uploadLogs();
    this.manager.layout_container.focusCenter();
    this.update();
  }

  enter(state?: number): void {
    super.enter(state);
    this.manager.layout_container.drawing_figure_mode = DrawingFigureMode.Texture;

    this.manager.layer_CadCopyImageLayer.visible = false;
    this.manager.layer_CadFloorLayer.visible = true;
    this.manager.layer_CadRoomFrameLayer.visible = true;
    this.manager.layer_CadCabinetLayer.visible = true;
    this.manager.layer_CadFurnitureLayer.visible = true;
    this.manager.layer_CadCabinetLayer.visible = true;
    this.manager.layer_OutLineLayer.visible = true;
    this.manager.layer_CadSubAreaLayer.visible = false;

    this.manager.onLayerVisibilityChanged();
    this.manager.layout_container._drawing_layer_mode = 'AIMatching';
    this.prepareschemeData();

    if (this.manager.scene3DManager) {
      this.manager.scene3DManager.bindOnSelectFigure((ele: TFigureElement) => {
        this._figure_element_selected = ele;
        this._onSelectedFigure(this._figure_element_selected);
      });

      this.manager.scene3DManager.bindOnSelectRoom((ele: TRoom) => {
        (room: TRoom) => {
          if (room && room._room_entity) {
            let center = room._room_entity._main_rect.rect_center;

            // 为了方便，模拟鼠标点击事件
            let ev: I_MouseEvent = { posX: center.x, posY: center.y, _ev: null };
            this.updateRoomSelected(ev);
          } else {
            let rooms: TRoom[] = [];
            this.room_list.forEach((roomItem: TRoom) => {
              if (
                roomItem._furniture_list &&
                roomItem._furniture_list.length > 0 &&
                roomItem.selectable
              ) {
                rooms.push(roomItem);
              }
            });
            TSeriesFurnisher.instance.current_rooms = rooms;
            LayoutAI_App.emit_M(EventName.SelectingRoom, {
              current_rooms: TSeriesFurnisher.instance.current_rooms,
              clickOnRoom: false
            });
          }
        };
      });
    }

    this.manager.layout_container._furniture_entities.forEach(entity => {
      if (entity instanceof TBaseGroupEntity && entity.figure_element.haveMatchedMaterial()) {
        (entity as TBaseGroupEntity).setMatchedVisible(true);
      }
    });
  }

  leave(state?: number | undefined): void {
    super.leave(state);
    this._figure_element_selected = null;
    TSeriesFurnisher.instance.current_rooms = [];
    this.manager.layer_AILayoutLayer.visible = false;
    this.manager.layout_container._drawing_layer_mode = 'FullHouse';

    this.manager.layout_container.cleanDimension();
    this._cad_default_sub_handler.cleanSelection();
    this.manager.layout_container.updateRoomsFromEntities(false);
  }
  saveSwjSchemeAsJsonFile() {
    // 转换数据为JSON格式

    let scheme_data = this.manager.layout_container.current_swj_layout_data;
    const dataStr = JSON.stringify(scheme_data, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const schemeId = scheme_data.scheme_id || null;

    // 弹出输入框让用户输入文件名
    let fileName = prompt(
      '当前布局匹配素材即将保存到本地JSON文件，请输入文件名：',
      'scheme_' + formatCurrentTime() + '_' + (schemeId ? schemeId : 'null') + '.json'
    );

    // 如果用户取消输入，则不进行下载
    if (fileName === null) return;

    // 创建临时的<a>标签用于下载
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName; // 使用用户输入的文件名

    document.body.appendChild(a);
    a.click();

    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  updateFigureSelected() {
    if (this._selected_target.selected_rect) {
      let entity = TBaseEntity.getEntityOfRect(
        this._selected_target.selected_rect
      ) as TFurnitureEntity;

      if (entity && entity.type === 'Furniture') {
        this._figure_element_selected = entity.figure_element;
      } else if (entity && entity.type === 'BaseGroup') {
        this._figure_element_selected = entity.figure_element;
      } else if (entity && entity.type === 'Door') {
        let win_entity = entity as any as TWindowDoorEntity;
        if (win_entity._win_figure_element) {
          this._figure_element_selected = win_entity._win_figure_element;
        }
      } else {
        this._figure_element_selected = null;
      }
    } else {
      this._figure_element_selected = null;
    }
    this._onSelectedFigure(this._figure_element_selected);
  }

  ondbclick(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      this._active_sub_handler.ondbclick(ev);
    } else if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.ondbclick(ev);
    }
  }

  onmousedown(ev: I_MouseEvent): void {
    if (ev.ctrlKey && !this._active_sub_handler) {
      this.runCommand(LayoutAI_Commands.Transform_Combination);
    }
    if (this._active_sub_handler) {
      this._active_sub_handler.onmousedown(ev);
    } else if (this._cad_default_sub_handler) {
      // 默认方法mouse_down后, 有可能触发进入新的active_handler
      this._cad_default_sub_handler.onmousedown(ev);

      if (this._active_sub_handler) {
        this._active_sub_handler.onmousedown(ev);
      }
    }
    this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

    this._is_painter_center_moving = false;
  }

  onmousemove(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmousemove(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmousemove(ev);
      }
      super.onmousemove(ev);
    }
  }

  onmouseup(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmouseup(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmouseup(ev);
      }
      super.onmouseup(ev);
    }

    this.updateFigureSelected();
    this.updateRoomSelected(ev);
  }

  onwheel(ev: WheelEvent): void {
    if (this._active_sub_handler) {
      super.onwheel(ev);
      this._active_sub_handler.onwheel(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onwheel(ev);
      }
      super.onwheel(ev);
    }
  }
  onkeydown(ev: KeyboardEvent): boolean {
    super.onkeydown(ev);
    if (ev.code == 'KeyK' && ev.ctrlKey && ev.shiftKey && ev.altKey) {
      this._kitchen_use_default_material = !this._kitchen_use_default_material;
    } else if (ev.code == 'KeyS' && ev.ctrlKey && ev.altKey && ev.shiftKey) {
      this.manager.layout_container.saveSchemeLayout2Json();
    } else if (ev.code == 'KeyC' && ev.ctrlKey && ev.shiftKey && ev.altKey) {
      LayoutAI_App.emit(EventName.BackToEditPage, null);
    } else if (ev.code == 'F12' && ev.ctrlKey && ev.shiftKey && ev.altKey) {
      LayoutAI_App.emit(EventName.SwitchToTrainingPage, null);
    } else if (ev.code == 'KeyL' && ev.ctrlKey && ev.shiftKey && ev.altKey) {
      Logger.silence = false;
    } else if (ev.code == 'KeyS' && ev.ctrlKey && ev.shiftKey) {
      this._enable_hard_furnish = false;
      console.info('disable hard furnish');
    } else if (ev.code == 'KeyH' && ev.ctrlKey && ev.shiftKey) {
      this._enable_hard_furnish = true;
      console.info('enable hard furnish');
    } else if (ev.code == 'KeyS' && ev.ctrlKey && ev.altKey) {
      console.info('save 3D scheme json');
      this.saveSwjSchemeAsJsonFile();
    }

    if (ENV != 'prod' && ev.ctrlKey && ev.shiftKey && ev.code == 'KeyF') {
      let group_element_list: TFigureElement[] = [];
      // TODO 这个需要包含单品以及组合内的素材图元
      let testGroupFigure: TFigureElement = null;
      let roomElementInfo: Map<TRoom, TFigureElement[]> = new Map<TRoom, TFigureElement[]>();
      for (let room of this.room_list) {
        if (room._furniture_list && room._furniture_list.length > 0) {
          if (!roomElementInfo.has(room)) {
            roomElementInfo.set(room, []);
          }
          let matchedFigures: TFigureElement[] = roomElementInfo.get(room);
          for (let furniture of room._furniture_list) {
            if (
              furniture.haveMatchedGroupMaterial() &&
              (furniture.modelLoc.indexOf('沙发组合') >= 0 ||
                furniture.modelLoc.indexOf('餐桌椅组合') >= 0 ||
                furniture.modelLoc.indexOf('床具组合') >= 0)
            ) {
              group_element_list.push(furniture);
            }

            // 收集所有匹配图元如果无匹配则直接用原有图元
            let groupEntity: TBaseGroupEntity = furniture.furnitureEntity as TBaseGroupEntity;
            if (groupEntity) {
              if (furniture.haveMatchedGroupMaterial()) {
                matchedFigures.push(furniture);
                testGroupFigure = furniture;
              } else {
                matchedFigures.push(...groupEntity.displayed_figure_elements);
              }
            } else {
              // 组合是组合那个板块微调，后续组合会当成那一个整体图元参与到单品微调过程中去
              if (furniture.haveMatchedMaterial2()) {
                matchedFigures.push(furniture._matched_material.figureElement);
              } else {
                matchedFigures.push(furniture);
              }
            }
          }
        }
      }

      // 打印组合位置信息
      group_element_list.forEach(group_element => {
        if (group_element != null) {
          const group_entity = group_element.furnitureEntity as TBaseGroupEntity;
          if (group_entity == null) return;

          let shouldPrintLog: boolean = true;

          if (
            group_element.modelLoc.indexOf('沙发') >= 0 ||
            group_element.modelLoc.indexOf('床') >= 0 ||
            group_element.modelLoc.indexOf('餐桌') >= 0
          ) {
            let logContent: string = null;
            let start_time: number = 0;
            let old_pos_map: Map<TFigureElement, string> = null;
            let getFigureElementPos = (fe: TFigureElement) => {
              return `(${fe.rect.rect_center.x.toFixed(2)}, ${fe.rect.rect_center.y.toFixed(
                2
              )}, ${fe.rect.rect_center.z.toFixed(2)})`;
            };
            if (shouldPrintLog) {
              logContent = '[组合微调] ' + group_element.toString();
              logContent += '\n微调前:';
              old_pos_map = new Map();
              group_entity.displayed_figure_elements.forEach(fe => {
                old_pos_map.set(fe, getFigureElementPos(fe));
                logContent +=
                  `\n  ${fe.modelLoc} ${fe.matchedMaterialId}: pos=` + getFigureElementPos(fe);
              });
              start_time = Date.now();
            }

            let groupType: TLayoutGroupFineTuningType =
              TLayoutGroupFineTuningType.k_sofaGroupFigure;
            if (group_element.modelLoc.indexOf('餐桌') >= 0) {
              groupType = TLayoutGroupFineTuningType.k_diningTableGroupFigure;
            } else if (group_element.modelLoc.indexOf('床') >= 0) {
              groupType = TLayoutGroupFineTuningType.k_bedGroupFigure;
            }

            TLayoutGroupFineTuningManagerToolUtil.instance.fineTuningGroup(
              group_entity.displayed_figure_elements,
              group_element.length,
              group_element.width,
              groupType,
              group_entity.center,
              group_entity.nor
            );

            if (shouldPrintLog) {
              let end_time = Date.now();
              logContent += `\n微调耗时: ${end_time - start_time}ms`;
              logContent += '\n微调后:';
              group_entity.displayed_figure_elements.forEach(fe => {
                let old_pos = old_pos_map.get(fe);
                let new_pos = getFigureElementPos(fe);
                if (old_pos != new_pos) {
                  logContent += `\n  ${fe.modelLoc} ${fe.matchedMaterialId}: pos=(${old_pos}) -> (${new_pos})`;
                }
              });
              console.info(logContent);
              this.update();
            }

            // TODO 组合外框也需同步进行更新 ----- 目前无接口
          }
        }
      });

      roomElementInfo.forEach((figures, room) => {
        TLayoutAllMatchFineTuningManagerToolUtil.instance.allMatchFigureFineTuning(room, figures);
      });

      if (group_element_list.length > 0 || roomElementInfo.size > 0) {
        this.update();
      }
    }
    return true;
  }

  async runCommand(cmd_name: string): Promise<void> {
    super.runCommand(cmd_name);
    switch (cmd_name) {
      case LayoutAI_Commands.OpenLayoutSchemeIn3D:
        this.openLayoutSchemeInNew3DTab();
        break;
      case LayoutAI_Commands.CreateAndOpenDreamerScheme:
        this.createAndOpenDreamerScheme();
        break;
      case LayoutAI_Commands.RenderAndOpenPanorama:
        this.renderAndOpenPanorama();
        break;
      case LayoutAI_Commands.DeleteRoomSeriesData:
        TSeriesFurnisher.instance.deleteSeriesSample();
        break;
      case LayoutAI_Commands.AllRoomClear:
        this.room_list.forEach((roomItem: TRoom) => {
          roomItem.clearMatchedMaterials();
        });
        TSeriesFurnisher.instance.current_rooms = [];
        this.update();
        LayoutAI_App.emit_M(EventName.SelectingRoom, {
          current_rooms: TSeriesFurnisher.instance.current_rooms
        });
        break;
      case LayoutAI_Commands.StartFurnishRemaining:
        TSeriesFurnisher.instance.current_rooms.forEach(room => {
          room.startFurnishRemaining();
        });
        this.update();
        break;
      case LayoutAI_Commands.FinishFurnishRemaining:
        TSeriesFurnisher.instance.current_rooms.forEach(room => {
          room.finishFurnishRemaining();
        });
        this.update();
        break;
      case LayoutAI_Commands.PostAddDownlights:
        {
          let rooms: TRoom[] = [];
          if (this.manager.layout_container._selected_room) {
            rooms = [this.manager.layout_container._selected_room];
          } else {
            rooms = this.room_list;
          }
          rooms.forEach(room => {
            TPostDecoratesLayout.post_add_lighting(room, room._furniture_list, {
              add_decoration: true,
              add_main_lights: false
            });
          });
          this.update();
        }
        break;
      default:
        break;
    }
  }

  async handleEvent(event_name: string, event_param: any) {
    super.handleEvent(event_name, event_param);

    // 选样板间触发的事件
    if (event_name === LayoutAI_Events.SeriesSampleSelected) {
      TSeriesFurnisher.instance.onSeriesSampleSelected(event_param.scope, event_param.series);
    }
    // 全屋应用触发的事件，跳过了选择的步骤
    else if (event_name === LayoutAI_Events.AllFurnishRooms) {
      this._onSelectedFigure(null);
      this.update();
    } else if (event_name === LayoutAI_Events.ClearRoom2SeriesSample) {
      const room: TRoom = event_param;
      TSeriesFurnisher.instance.clearRoom2SeriesSample(room);
      if (room == null) {
        TSeriesFurnisher.instance.clearCurrentRooms();
      }
      this.room_list.forEach(item => {
        if (item.locked) return;
        item.clearApplyScope();
        if (item == room || !room) item.clearMatchedMaterials();
      });
      TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering();
      LayoutAI_App.emit_M(EventName.SelectingRoom, {
        current_rooms: TSeriesFurnisher.instance.current_rooms
      });
      this.update();
    } else if (event_name === LayoutAI_Events.SubmitLayoutIssueReport) {
      const issueObj = event_param;
      let scheme_data = this.manager.layout_container.current_swj_layout_data;
      let houseJsonData: string = JSON.stringify(scheme_data, null, 0);
      houseJsonData = houseJsonData.replace(/"/g, "'");
      const schemeId = scheme_data?.scheme_id;
      const houseArea = scheme_data?.area;

      IssueService.submitLayoutIssueReport(
        issueObj.issueId,
        issueObj.issueList,
        schemeId,
        houseArea,
        houseJsonData
      )
        .then(res => {
          if (res && res.success) {
            LayoutAI_App.emit(EventName.IssueSubmitSuccess, null);
          } else {
            LayoutAI_App.emit(EventName.IssueSubmitFail, null);
          }
        })
        .catch(e => {
          LayoutAI_App.emit(EventName.IssueSubmitFail, null);
        });
    } else if (event_name === LayoutAI_Events.AIMatchingFigureSelected) {
      if (TSeriesFurnisher.instance.current_rooms[0]) {
        let figure_element = event_param as TFigureElement;

        let figure_ele_list = [...TSeriesFurnisher.instance.current_rooms[0]._furniture_list];
        TSeriesFurnisher.instance.current_rooms[0]._furniture_list.forEach(fe => {
          if (!fe.haveMatchedMaterial()) {
            figure_ele_list.push(...fe.getAlternativeFigureElements());
          } else {
            figure_ele_list.push(...fe.getMembersFigureElements());
          }
        });

        let check_in_room_furniture = figure_ele_list.includes(figure_element);
        if (check_in_room_furniture) {
          this._figure_element_selected = figure_element;
          if (this._figure_element_selected) {
            this._selected_target.selected_rect = this._figure_element_selected.rect;
            this.updateFigureSelected();
          }
        } else {
          let check_in_room_hard = TSeriesFurnisher.instance.current_rooms[0]
            .getHardDecorationList()
            .includes(figure_element);
          if (check_in_room_hard) {
            this._figure_element_selected = figure_element;
            this._onSelectedFigure(figure_element);
          }
        }
      }

      this.update();
    }
    // 替换素材
    else if (event_name === LayoutAI_Events.ReplaceMaterial) {
      let material_item = event_param as I_MaterialMatchingItem;
      await TMaterialMatcher.instance.replaceMaterial(
        material_item.figureElement || this._figure_element_selected,
        event_param as I_MaterialMatchingItem,
        this.room_list
      );
      this._onSelectedFigure(event_param?.figureElement);
    } else if (event_name === LayoutAI_Events.ClearSeries) {
      TSeriesFurnisher.instance.clearRoom2SeriesSample(event_param, true);
      TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering();
      this.update();
    }
    // 替换后生成预览图返回给AI搭柜
    else if (event_name === LayoutAI_Events.ReplaceMaterialPreview) {
      LayoutAI_App.emit(
        EventName.previewImg,
        this.manager.layout_container.saveLayoutSchemeImage(1200, 1200, 1, true)
      );
    } else if (event_name === LayoutAI_Events.ApplyFurnishTo3D) {
      SensorsLogger.trackApplyTo3DEvent('mySchemeToCurrent3d');
      TSeriesFurnisher.instance.applyFurnishTo3D();
    } else if (event_name === LayoutAI_Events.CheckUnfurnishedBeforePerformFurnish) {
      if (event_param.skipCheckUnfurnished || this.checkUnfurnishedRooms()) {
        await this.checkAndUpdateSchemeXml();
        await this.manager.layout_container.saveSchemeLayout2Json();
        LayoutAI_App.emit(EventName.CheckUnfurnishedResult, {
          layoutSchemeId: this.manager.layout_container._layout_scheme_id
        });
      }
    } else if (event_name === LayoutAI_Events.PrepareRestartFurnishRemaining) {
      this.room_list.forEach(room => room.prepareRestartFurnishRemaining());
    } else if (event_name === LayoutAI_Events.StartAutoFurnishRemaining) {
      if (ENV != 'prod') console.info('[Auto Furnish Remaining] Start');
      let pageIndex = 1;
      let matchingCurrentRoomList: TRoom[] = TSeriesFurnisher.instance.current_rooms;
      let unFinishedRoomList: TRoom[] = matchingCurrentRoomList.filter(
        room => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
      );
      while (unFinishedRoomList.length > 0) {
        const organizationSeries = await getKgSchemeListOfOrganization(pageIndex, 10);
        if (!organizationSeries || organizationSeries.length == 0) {
          break;
        }
        if (ENV != 'prod')
          console.info(
            '[Auto Furnish Remaining] pageIndex=' +
              pageIndex +
              ',organizationSeries.length=' +
              organizationSeries.length
          );
        for (let series of organizationSeries) {
          if (ENV != 'prod') {
            let logContent =
              '[Auto Furnish Remaining] UnFinished RoomList:\n  ' +
              unFinishedRoomList.map(room => room.toString()).join('\n  ');
            logContent += '\nRemaining use Series: ' + series.ruleName + ' ' + series.id;
            console.info(logContent);
          }
          TSeriesFurnisher.instance.current_rooms = unFinishedRoomList;
          await TSeriesFurnisher.instance.onSeriesSampleSelected(
            { soft: false, cabinet: false, hard: false, remaining: true },
            series
          );
          unFinishedRoomList = matchingCurrentRoomList.filter(
            room => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
          );
          if (unFinishedRoomList.length == 0) {
            break;
          }
          break;
        }
        pageIndex++;
        break;
      }

      pageIndex = 1;
      while (unFinishedRoomList.length > 0) {
        const platformSeries = await getKgSchemeListOfPlatform(pageIndex, 10);
        if (!platformSeries || platformSeries.length == 0) {
          break;
        }
        if (ENV != 'prod')
          console.info(
            '[Auto Furnish Remaining] pageIndex=' +
              pageIndex +
              ', platformSeries.length=' +
              platformSeries.length
          );
        for (let series of platformSeries) {
          if (ENV != 'prod') {
            let logContent =
              '[Auto Furnish Remaining] UnFinished RoomList:\n  ' +
              unFinishedRoomList.map(room => room.toString()).join('\n  ');
            logContent += '\n  Remaining use Series: ' + series.ruleName + ' ' + series.id;
            console.info(logContent);
          }
          TSeriesFurnisher.instance.current_rooms = unFinishedRoomList;
          await TSeriesFurnisher.instance.onSeriesSampleSelected(
            { soft: false, cabinet: false, hard: false, remaining: true },
            series
          );
          unFinishedRoomList = matchingCurrentRoomList.filter(
            room => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
          );
          if (unFinishedRoomList.length == 0) {
            break;
          }
          break;
        }
        pageIndex++;
        break;
      }

      TSeriesFurnisher.instance.current_rooms = matchingCurrentRoomList;
      if (ENV != 'prod') console.info('[Auto Furnish Remaining] Finished');
      LayoutAI_App.emit(EventName.FurnishRemainingFinished, null);
    } else if (event_name === LayoutAI_Events.Match3dPreviewMaterials) {
      TSeriesFurnisher.instance.tryToFurnishRoomsFor3dPreview(this.room_list);
    }
  }

  generateHouseSchemeJson(): any {
    let scheme_data = this.manager.layout_container.toXmlSchemeData();
    return scheme_data;
  }

  checkUnfurnishedRooms(): boolean {
    let hasUnfurishedRoom = TSeriesFurnisher.instance.current_rooms.some(
      (room: TRoom) => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
    );
    if (hasUnfurishedRoom) {
      setTimeout(
        () =>
          LayoutAI_App.emit(EventName.PerformFurnishResult, {
            progress: 'furnishremaining',
            message: '需要补全布置'
          }),
        10
      );
    }
    return !hasUnfurishedRoom;
  }

  // 画图元和俯视图
  drawFurniture(furniture: TFigureElement, isSelectSeries: boolean) {
    if (furniture._ex_prop['visible'] == 0 || furniture._ex_prop['is_deleted'] == '1') return;

    if (isSelectSeries && furniture.rect._attached_elements['pictureView']) {
      if (!furniture._matched_material?.modelId) return;
      if (furniture.isMaterialMarkAsInvisible()) return;
      furniture.drawPreviewFigure(this.painter);
    } else {
      furniture.drawFigure(this.painter);
    }
  }

  drawCanvas(): void {
    if (this.manager.layer_DefaultBatchLayer && this.manager.layer_DefaultBatchLayer.visible) {
      let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
      this.manager.layer_DefaultBatchLayer.drawLayer(batchDirty);
    }

    this.drawWithActiveHandler();
  }

  drawWithActiveHandler(): void {
    if (this._active_sub_handler) {
      this._active_sub_handler.drawCanvas();
    } else if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.drawCanvas();
    }

    for (let room of this.room_list) {
      room._painter = this.painter;
      TSeriesFurnisher.instance.room2SeriesSampleMap.forEach(function (
        value: TSeriesSample,
        key: TRoom
      ) {
        if (key.uid == room.uid) {
          room.isSelectSeries = true;
          room.mode = 'SeriesSampleMode';
        }
      });
      if (TSeriesFurnisher.instance.room2SeriesSampleMap.size == 0) {
        room.isSelectSeries = false;
      }
    }

    this.painter.fillStyle = '#555355';

    if (TSeriesFurnisher.instance.current_rooms != null) {
      const scope = this;

      for (let room of TSeriesFurnisher.instance.current_rooms) {
        if (room.locked === true) continue;
        let poly = room.room_shape._poly;
        this.painter.strokeStyle = '#147ffA'; // 设置描边颜色为蓝色
        this.painter._context.lineWidth = 2; // 设置描边宽度为2px
        this.painter.strokePolygons([poly]);
      }
    }
  }
  private _onSelectedFigure(ele: TFigureElement) {
    LayoutAI_App.emit_M(EventName.FigureElementSelected, ele);
    let scene3D = this.manager.scene3D;
    if (!scene3D) return;
    if (ele) {
      if (ele._solid_mesh3D) {
        scene3D.setSelectionBox(ele);
      }
    } else {
      LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
      scene3D.clearSelectionBox();
    }
  }
}
