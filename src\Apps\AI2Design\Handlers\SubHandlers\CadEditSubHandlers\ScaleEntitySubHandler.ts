import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { CadBaseSubHandler } from "./CadBaseSubHandler";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { EventName } from "@/Apps/EventSystem";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { TBaseGroupEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { Logger } from "@/Apps/LayoutAI/Utils/logger";
import { TMatchingOrdering } from "@/Apps/LayoutAI/Services/MaterialMatching/TMatchingOrdering";import { RoomSubAreaService } from "@/Apps/LayoutAI/Services/Basic/RoomSubAreaService";
import { AI_PolyTargetType, DrawingFigureMode } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TEntityTransformer } from "@/Apps/LayoutAI/Layout/TEntityTransformer/TEntityTransformer";




export class ScaleEntitySubHandler extends CadBaseSubHandler
{
    _previous_rect : ZRect;
    _logger: Logger = null;
    _last_pos : Vector3;
    constructor(cad_mode_handler: AI2BaseModeHandler)
    {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.Transform_Scaling;
        this._previous_rect = null;
        this._logger = Logger.instance;
        this._last_pos = null;
    }

    onmousedown(ev: I_MouseEvent): void {
        if(!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect)
        {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        if(this.selected_target && this.selected_target.selected_rect)
        {
            this._cad_mode_handler._is_moving_element = true;
        } 
        else 
        {
            this._cad_mode_handler._is_moving_element = false;
        }


        this._previous_rect = this.selected_target.selected_transform_element._target_rect.clone();
        this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_transform_element._target_rect);

        let entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(this.selected_target.selected_rect) as TBaseGroupEntity;
        if(entity.combination_entitys && entity.combination_entitys.length > 0)
        {
            let combinationRects: TFurnitureEntity[] = entity.recursivelyAddCombinationRects();
            this.selected_target.selected_transform_element.recordOriginCombinationRect(combinationRects);
            this.selected_target.selected_combination_entitys = combinationRects;
        }
   
        this._last_pos = new Vector3(ev.posX,ev.posY,0);
        this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);

    }
    onmousemove(ev: I_MouseEvent): void {
        if(ev.buttons != 1) return;

        let transform_element = this.selected_target.selected_transform_element;
        if(!transform_element) return;
        let current_pos = new Vector3(ev.posX,ev.posY,0);

        let movement = current_pos.clone().sub(this._last_pos);
        
        transform_element.applyTransformByMovement(movement,this.exsorb_rects);
        this.updateTransformElements();

        this.update();

    }
    onmouseup(ev: I_MouseEvent): void {
        if(this.selected_target.selected_transform_element)
        {
            this.selected_target.selected_transform_element.doneTransform();
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if(info)
            {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);

            TEntityTransformer.onDoneTransform(this.selected_target.selected_entity,this.selected_target.selected_combination_entitys);
        }



        this.updateAttributes("init");
        this._cad_mode_handler._is_moving_element = false;
        this.selected_target.selected_transform_element._align_line = false;
        // 拉伸后重新对套系内的素材进行排序

        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
        this.update();
    }

    drawCanvas(): void {
        super.drawCanvas();
        if(!this.selected_target.selected_rect) return;
        let rect = this.selected_target.selected_rect.clone();
        let minY = Math.min(...rect.vertices.map(v => v.pos.y));
        if(!rect.rect_center) 
        {
            // 选中的时候有时候会选中房间，房间的rect_center是null
            return;
        }
        let pp = {x: rect.rect_center.x, y: minY - (30 / this._p_sc), z: 0};
        this.painter.fillStyle = "#454647";
        let text =`${Math.round(rect._w)} X ${Math.round(rect._h)}`;
        this.painter.drawRectWithText(new Vector3(pp.x, pp.y,pp.z), '#fff', text, pp, 120)
    }
}