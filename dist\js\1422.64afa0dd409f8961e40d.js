"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[1422],{6934:function(e,n,t){var i=t(13274),o=t(85783),r=t(7130),a=t(15696),l=t(41594),u=t(27347),s=t(77320),c=t(33100),d=t(78154),f=t(88934),h=t(9003),p=t(32184),m=t(76330),b=t(49063),v=t(23825),y=t(5640),g=t(99940),S=t(36906),x=t(38008),j=t(98612),w=t(65640);function C(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}function A(e,n,t,i,o,r,a){try{var l=e[r](a),u=l.value}catch(e){return void t(e)}l.done?n(u):Promise.resolve(u).then(i,o)}function k(e){return function(){var n=this,t=arguments;return new Promise((function(i,o){var r=e.apply(n,t);function a(e){A(r,i,o,a,l,"next",e)}function l(e){A(r,i,o,a,l,"throw",e)}a(void 0)}))}}function N(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,r=[],a=!0,l=!1;try{for(t=t.call(e);!(a=(i=t.next()).done)&&(r.push(i.value),!n||r.length!==n);a=!0);}catch(e){l=!0,o=e}finally{try{a||null==t.return||t.return()}finally{if(l)throw o}}return r}}(e,n)||I(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(e){return function(e){if(Array.isArray(e))return C(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||I(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function I(e,n){if(e){if("string"==typeof e)return C(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?C(e,n):void 0}}function z(e,n){var t,i,o,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(u){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(r=0)),r;)try{if(t=1,i&&(o=2&l[0]?i.return:l[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,l[1])).done)return o;switch(i=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,i=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(!(o=r.trys,(o=o.length>0&&o[o.length-1])||6!==l[0]&&2!==l[0])){r=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){r.label=l[1];break}if(6===l[0]&&r.label<o[1]){r.label=o[1],o=l;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(l);break}o[2]&&r.ops.pop(),r.trys.pop();continue}l=n.call(e,r)}catch(e){l=[6,e],i=0}finally{t=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,u])}}}s.A.confirm;n.A=(0,a.observer)((function(){var e,n=(0,o.B)().t,t=(0,r.A)().styles,a=(0,h.P)(),s=N((0,l.useState)([]),2),C=(s[0],s[1]),A=(0,l.useRef)(null),I=N((0,l.useState)(!1),2),D=I[0],E=I[1],_=N((0,l.useState)("null"),2),R=_[0],F=_[1],T=(u.nb.instance.layout_container,N((0,l.useState)([]),2)),P=T[0],O=T[1],L=N((0,l.useState)(0),2),G=L[0],B=L[1],$=N((0,l.useState)(""),2),W=($[0],$[1],N((0,l.useState)(!1),2)),H=(W[0],W[1],N((0,l.useState)(!1),2)),V=H[0],U=H[1],Z=N((0,l.useState)(!1),2),K=Z[0],X=(Z[1],N((0,l.useState)(!1),2)),Y=X[0],q=X[1],J=N((0,l.useState)({designStyle:[],rooms:[]}),2),Q=(J[0],J[1]),ee=((0,b.Zp)(),D?36:45),ne=[{id:"Layout",label:n("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:n("风格"),icon:"icon-a-TypefenggeStateDefault"},"2D"===a.homeStore.viewMode?{id:"material",label:n("素材"),icon:"icon-a-TypesucaiStateDefault"}:null,{id:"attribute",label:n("属性"),icon:"icon-a-TypeshuxingStateDefault"},"3D_FirstPerson"===a.homeStore.viewMode?{id:"view",label:n("视角"),icon:"icon-smarttemplate"}:null,"2D"===a.homeStore.viewMode?{id:"aidraw",label:n("AI绘图"),icon:"icon-AIchutu"}:null,v.Ic?{id:"similar",label:n("相似匹配"),icon:"icona-Typexuanzebujian"}:null,v.Ic?{id:"create",label:n("新建"),icon:"iconfile"}:null].filter(Boolean),te=[{id:"Layout",label:n("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:n("风格"),icon:"icon-a-TypefenggeStateDefault"},{id:"submitDrawing",label:n("提交绘图"),icon:"icon-xuanranRender"},{id:"atlas",label:n("图册"),icon:"icon-tuku"}].filter(Boolean),ie=[{id:"size",icon:"icon-chicun",label:n("尺寸"),onClick:function(){a.homeStore.setSizeInfo({type:"size",visible:!0}),u.nb.emit_M(d.$.showPopup,"sizeEditor")},disabled:!1,divider:!1},{id:"rotate",icon:"icon-rotate",label:n("旋转"),disabled:!1,divider:!1},{id:"flip",icon:"icon-horizontalflip_line",label:n("左右镜像"),disabled:!1,divider:!1},{id:"copy",icon:"icon-niantie",label:n("复制"),disabled:!1,divider:!1},{id:"autoRuler",icon:"icon-chicun",label:n("开启标尺"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:n("删除"),disabled:!1,divider:!0}];ie=ie.filter(Boolean);var oe=[{id:"rotate",icon:"icon-rotate",label:n("旋转"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:n("删除"),disabled:!1,divider:!0}],re=[{id:"delete",icon:"icon-delete",label:n("删除"),disabled:!1,divider:!0}],ae=[{id:"delete",icon:"icon-delete",label:n("删除")}],le=[{id:"attribute",label:n("属性"),icon:"icon-a-TypeshuxingStateDefault",onClick:function(){u.nb.emit_M(d.$.showPopup,"attribute")}},{id:"splitWall",icon:"iconsplit",label:n("拆分墙")},{id:"delete",icon:"icon-delete",label:n("删除"),disabled:!1,divider:!0}];if(a.homeStore.designMode===j.f.HouseDesignMode){var ue={id:"attribute",label:n("属性"),icon:"icon-a-TypeshuxingStateDefault",onClick:function(){u.nb.emit_M(d.$.showPopup,"attribute")}};oe.unshift(ue),re.unshift(ue),ae.unshift(ue),v.fZ&&(le=le.filter((function(e){return"splitWall"!==e.id})))}var se=[{id:"size",icon:"icon-chicun",label:n("尺寸"),onClick:function(){a.homeStore.setSizeInfo({type:"size",visible:!0}),u.nb.emit_M(d.$.showPopup,"sizeEditor")},disabled:!1,divider:!1},{id:"rotate",icon:"icon-rotate",label:n("旋转"),disabled:!1,divider:!1},{id:"ungroupTemplate",icon:"icon-jiezu-2",label:n("解组"),disabled:!1,divider:!1},{id:"combinationStorage",icon:"icon-anzhuangInstall",label:n("组合入库"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:n("删除"),disabled:!1,divider:!0}],ce=[{id:"attribute",label:n("属性"),icon:"icon-a-TypeshuxingStateDefault",onClick:function(){u.nb.emit_M(d.$.showPopup,"attribute")}},{id:"material",label:n("清除布局"),icon:"icon-shanchubuju",EventName:"ClearLayout"},a.homeStore.isSingleRoom||"2D"!==a.homeStore.viewMode?null:{id:"focusSpace",label:n("专注空间"),icon:"icon-zhuanzhukongjian",EventName:"SingleRoomLayout",onClick:function(){u.nb.DispatchEvent(u.n0.SingleRoomLayout,a.homeStore.selectEntity),a.homeStore.setIsSingleRoom(!0)}}];ce=ce.filter((function(e){return null!==e})),(0,l.useEffect)((function(){u.nb.on_M(f.U.SelectingTarget,"PadStatusBar",(function(e){var t,i=e||null;a.homeStore.setSelectEntity(e),e||a.homeStore.setShowReplace(!1);var o=(null==i?void 0:i.type)||null,r=(null==e||null===(t=e.ex_prop)||void 0===t?void 0:t.label)||null;if("Furniture"===o){var l;l=M(ie),O(l)}else if("Group"===o){var s=[{id:"size",icon:"icon-chicun",label:n("尺寸"),onClick:function(){a.homeStore.setSizeInfo({type:"size",visible:!0}),u.nb.emit_M(d.$.showPopup,"sizeEditor")},disabled:!1,divider:!1},{id:"combination",label:n("组合"),icon:"iconcombination",tips:n("组合"),disabled:!1,divider:!1}];O(s)}else if("BaseGroup"===o){var c=window.location.pathname.includes("addGroup")?se:se.filter((function(e){return"combinationStorage"!==e.id}));O(c)}else if("Door"===o||"Window"===o){if("baywindow"===r)return void O(re);O(oe)}else"Wall"===o?O(le):"StructureEntity"===o?O(ae):o===p.Fz.RoomSubArea&&O([{id:"roomSubAreaAttribute",icon:"icon-a-TypeshuxingStateDefault",label:n("属性"),disabled:!1,divider:!1,onClick:function(){u.nb.emit_M(d.$.showPopup,"SpaceAreaAttribute")}},{id:"copyRoomSubArea",icon:"iconcopy",label:n("复制"),disabled:!1,divider:!0},{id:"deleteRoomSubArea",icon:"icon-delete",label:n("删除"),disabled:!1,divider:!0}])})),Fe()}),[]),(0,l.useEffect)((function(){"3D_FirstPerson"===a.homeStore.viewMode?C(te):C(ne)}),[a.homeStore.viewMode]),(0,l.useEffect)((function(){var e,n,t;if("3D_FirstPerson"===a.homeStore.viewMode&&"Furniture"!==(null===(e=a.homeStore.selectEntity)||void 0===e?void 0:e.type))return pe(null),void u.nb.instance.layout_container.cleanDimension();if("3D_FirstPerson"===a.homeStore.viewMode&&"Furniture"===(null===(n=a.homeStore.selectEntity)||void 0===n?void 0:n.type))return u.nb.emit_M(d.$.showPopup,"replace"),u.nb.instance.layout_container.cleanDimension(),he(!1),void pe(null);var i=(null===(t=a.homeStore.selectEntity)||void 0===t?void 0:t.type)||null;pe(i)}),[null===(e=a.homeStore.selectEntity)||void 0===e?void 0:e.type,a.homeStore.viewMode]);var de=N((0,l.useState)(!0),2),fe=de[0],he=de[1],pe=function(e){he(!1),setTimeout((function(){F(e||"null"),he(!0),"3D"===a.homeStore.viewMode&&he(!1)}),30)},me=window.innerWidth,be=(window.innerHeight,N((0,l.useState)({top:80,left:me/2}),2)),ve=be[0],ye=be[1],ge=N((0,l.useState)(!1),2),Se=ge[0],xe=ge[1],je=(0,l.useRef)(null),we=(0,l.useRef)({top:0,left:0}),Ce=(0,l.useRef)({x:0,y:0}),Ae=N((0,l.useState)(!1),2),ke=Ae[0],Ne=Ae[1],Me=function(e){xe(!0),we.current={top:ve.top,left:ve.left},Ce.current={x:e.touches[0].clientX,y:e.touches[0].clientY}},Ie=function(e){e.preventDefault()},ze=function(e){},De=function(){return{position:"fixed",top:ve.top,left:"50%",maxWidth:V||ke?ee+"px":"550px",maxHeight:V?ee+"px":ke?"550px":ee+"px",minWidth:ee+"px",minHeight:ee+"px",flexDirection:ke?"column":"row"}},Ee=function(){return(0,i.jsx)("div",{onClick:function(){return E(!D)},style:{borderTopLeftRadius:"50%",width:ke?64:24,height:ke?24:64,backgroundColor:"#fff"}})},_e=function(){return V&&(0,i.jsx)(m.A,{type:"icon-a-TypegongjuStateDefault",style:{fontSize:"31px",color:"#282828",margin:"-3px 14px 0px 17px"},onClick:Re})},Re=function(){!function(e,n,t){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];ye({top:e,left:n}),U(t),Ne(i)}(60,me/2,!1,!1)};(0,l.useEffect)((function(){var e=je.current;return e&&e.addEventListener("touchend",ze),function(){e&&e.removeEventListener("touchend",ze)}}),[Se,K]),(0,l.useEffect)((function(){Re()}),[a.homeStore.IsLandscape]),(0,l.useEffect)((function(){4===a.homeStore.zIndexOf3DViewer&&Pe()}),[a.homeStore.zIndexOf3DViewer]),(0,l.useEffect)((function(){u.nb.DispatchEvent(u.n0.AutoRuler,{AutoRuler:Y}),O(P.map((function(e){return"autoRuler"===e.id&&(e.label=n(Y?"关闭标尺":"开启标尺")),e})))}),[Y]);var Fe=function(){var e=k((function(){var e,n;return z(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,fetch("https://3vj-render.3vjia.com/config/3d/aidraw.json")];case 1:return[4,t.sent().json()];case 2:return e=t.sent(),Q(e),[3,4];case 3:return n=t.sent(),w.error("获取配置失败:",n),[3,4];case 4:return[2]}}))}));return function(){return e.apply(this,arguments)}}(),Te=function(){var e=k((function(){var e,t,i,o;return z(this,(function(r){switch(r.label){case 0:return e=a.addGroupStore.addGroupData,t=a.homeStore.selectEntity,[4,(0,y.nA)(x.x.saveEntityImage(t),"snapShot"+Math.floor(1e4*Math.random())+".png")];case 1:return i=r.sent(),{},"addsizeGroup"!==e.type?[3,3]:(o={metaData:t.exportData(),metaHeight:t.rect.rect_center_3d.z,metaLength:t.rect.w,metaWidth:t.rect.h,metaImage:i,metaName:t.name,metaImageId:e.sizeObj.id},[4,(0,S.G)(o)]);case 2:r.sent().success&&(c.A.success(n("尺寸链组合入库成功")),window.parent.postMessage({origin:"layoutai.api",type:"GroupMessage",data:{success:!0}},"*")),r.label=3;case 3:return[2]}}))}));return function(){return e.apply(this,arguments)}}(),Pe=function(){switch(R){case"null":return(0,i.jsx)(i.Fragment,{});case"Furniture":case"BaseGroup":case"Door":case"Window":case"Group":case"StructureEntity":case"Wall":return(0,i.jsxs)("div",{ref:je,onTouchStart:Me,onTouchMove:Ie,style:De(),className:"".concat(t.root," ").concat(fe?t.show:t.hide),children:[Ee(),_e(),"                    ",P.map((function(e,n){return(0,i.jsxs)("div",{className:t.btnInfo,onClick:function(){if(e.onClick)e.onClick();else switch(e.id){case"rotate":u.nb.RunCommand(u._I.RotateFurniture);break;case"flip":u.nb.RunCommand(u._I.FlipFurniture);break;case"flip_vertical":u.nb.RunCommand(u._I.FlipFurnitureVertical);break;case"delete":case"deleteRoomSubArea":u.nb.RunCommand(u._I.DeleteFurniture);break;case"deleteRuler":u.nb.RunCommand(u._I.DeleteRuler);break;case"copy":u.nb.RunCommand(u._I.CopyFurniture);break;case"combination":A.current.onModal();break;case"ungroupTemplate":u.nb.DispatchEvent(u.n0.HandleUnGroupTemplate,{}),a.homeStore.setKey(Date.now());break;case"replace":u.nb.emit_M(d.$.showPopup,e.id),he(!1);break;case"size":a.homeStore.setSizeInfo({type:"size",visible:!0});break;case"pos_z":a.homeStore.setSizeInfo({type:"pos_z",visible:!0});break;case"copyRoomSubArea":u.nb.DispatchEvent(u.n0.CopyRoomSubArea,null);break;case"combinationStorage":Te();break;case"autoRuler":q(!Y)}},children:[e.divider&&(0,i.jsx)("div",{className:"divider"}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{type:e.icon,style:{fontSize:"20px",color:"#282828"}})}),!D&&(0,i.jsx)("div",{className:"label",children:e.label})]})]},n)})),(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{type:"icon-a-tianchongFace-1",onClick:function(){u.nb.DispatchEvent(u.n0.cleanSelect,null)},style:{fontSize:"20px",color:"#BCBEC2"}})})}),Ee()]});case"RoomArea":return(0,i.jsxs)("div",{ref:je,onTouchStart:Me,onTouchMove:Ie,className:"".concat(t.root," ").concat(fe?t.show:t.hide),style:De(),children:[_e(),Ee(),(0,i.jsx)(i.Fragment,{children:ce.map((function(e,n){return(0,i.jsxs)("div",{className:t.btnInfo,style:{margin:ke?"4px 0":"0 8px"},onClick:function(){e.onClick?(e.onClick(),B(Math.floor(1e4*Math.random()))):(null==e?void 0:e.EventName)&&(B(Math.floor(1e4*Math.random())),u.nb.DispatchEvent(null==e?void 0:e.EventName,a.homeStore.selectEntity))},children:[(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{type:e.icon,style:{fontSize:"20px",color:"#282828"}})}),!D&&(0,i.jsx)("div",{className:"label",children:e.label})]},n)}))}),Ee()]});default:return null}};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{children:Pe()},G),(0,i.jsx)(g.A,{ref:A})]})}))},22681:function(e,n,t){var i=t(13274),o=t(85783),r=t(7474),a=t(15696),l=t(41594),u=t(27347),s=t(83813),c=t(25076),d=t(9003),f=t(88934),h=t(76330);function p(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}function m(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,r=[],a=!0,l=!1;try{for(t=t.call(e);!(a=(i=t.next()).done)&&(r.push(i.value),!n||r.length!==n);a=!0);}catch(e){l=!0,o=e}finally{try{a||null==t.return||t.return()}finally{if(l)throw o}}return r}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return p(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return p(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.A=(0,a.observer)((function(e){var n=(0,o.B)().t,t=(0,r.A)().styles,a=(0,d.P)(),p=m((0,l.useState)(0),2),b=p[0],v=p[1],y=m((0,l.useState)(0),2),g=y[0],S=y[1],x=m((0,l.useState)(0),2),j=x[0],w=x[1],C=m((0,l.useState)(0),2),A=C[0],k=C[1],N=m((0,l.useState)(null),2),M=N[0],I=N[1],z=m((0,l.useState)(!1),2),D=z[0],E=z[1];(0,l.useEffect)((function(){u.nb.instance&&u.nb.on(f.U.AttributeHandle,(function(e){var n,t,i,o,r,l,u,s;"init"===e.mode&&(S(Math.round(null==e||null===(t=e.properties)||void 0===t||null===(n=t.width)||void 0===n?void 0:n.defaultValue)),v(Math.round(null==e||null===(o=e.properties)||void 0===o||null===(i=o.length)||void 0===i?void 0:i.defaultValue)),w(Math.round(null==e||null===(l=e.properties)||void 0===l||null===(r=l.height)||void 0===r?void 0:r.defaultValue)),k(Math.round(null==e||null===(s=e.properties)||void 0===s||null===(u=s.pos_z)||void 0===u?void 0:u.defaultValue)),I(null==e?void 0:e.properties),a.homeStore.setAttribute(e))}))}),[]);var _=(0,l.useCallback)((function(e,n){return function(t){var i;if(e(t),null==M||null===(i=M[n])||void 0===i||i.onChange(t),D){var o,r,a=1,l=1,u=1;if("length"===n)a=t/b,S(Math.round(g*a)),w(Math.round(j*a)),null==M||null===(o=M.width)||void 0===o||o.onChange(g*a),null==M||null===(r=M.height)||void 0===r||r.onChange(j*a);else if("width"===n){var s,c;l=t/g,v(Math.round(b*l)),w(Math.round(j*l)),null==M||null===(s=M.length)||void 0===s||s.onChange(b*l),null==M||null===(c=M.height)||void 0===c||c.onChange(j*l)}else if("height"===n){var d,f;u=t/j,v(Math.round(b*u)),S(Math.round(g*u)),null==M||null===(d=M.length)||void 0===d||d.onChange(b*u),null==M||null===(f=M.width)||void 0===f||f.onChange(g*u)}}}}),[M,D,b,g,j]);return(0,i.jsx)("div",{className:t.container+" leftSizeEditor",onClick:function(e){return e.stopPropagation()},children:"size"===a.homeStore.sizeInfo.type?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.title,children:[(0,i.jsxs)("div",{children:[n("尺寸调整"),a.homeStore.IsLandscape&&(0,i.jsxs)("div",{className:t.geometricCheck,children:[(0,i.jsx)("input",{type:"checkbox",onChange:function(e){E(e.target.checked)}}),(0,i.jsxs)("span",{children:[" ",n("等比缩放")]})]})]}),!a.homeStore.IsLandscape&&(0,i.jsx)("button",{className:t.resetBtn,onClick:function(){E(!D)},children:(0,i.jsx)(h.A,{type:"icon-suoding1"})}),(0,i.jsx)("button",{className:t.resetBtn,onClick:function(){u.nb.DispatchEvent(u.n0.ResetSize,null)},children:a.homeStore.IsLandscape?n("恢复默认"):(0,i.jsx)(h.A,{type:"icon-reset"})})]}),a.homeStore.IsLandscape?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("宽度"),(0,i.jsx)(s.A,{className:t.slider,value:b,onChange:_(v,"length"),min:0,max:6e3,step:1}),(0,i.jsx)(c.A,{className:t.input,value:b,suffix:"mm",onChange:function(e){return _(v,"length")(Number(e.target.value))}})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("深度"),(0,i.jsx)(s.A,{className:t.slider,value:g,onChange:_(S,"width"),min:0,max:6e3,step:1}),(0,i.jsx)(c.A,{className:t.input,value:g,suffix:"mm",onChange:function(e){return _(S,"width")(Number(e.target.value))}})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("高度"),(0,i.jsx)(s.A,{className:t.slider,value:j,onChange:_(w,"height"),min:0,max:2800,step:1}),(0,i.jsx)(c.A,{className:t.input,value:j,suffix:"mm",onChange:function(e){return _(w,"height")(Number(e.target.value))}})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("投影面积(宽*高)"),(0,i.jsxs)("label",{className:t.input,style:{textAlign:"right"},children:[(b*j/1e3/1e3).toFixed(2),"m²"]})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("宽度"),(0,i.jsx)(c.A,{className:t.input,value:b,suffix:"mm",onChange:function(e){return _(v,"length")(Number(e.target.value))}})]}),(0,i.jsx)(s.A,{className:t.slider,value:b,onChange:_(v,"length"),min:0,max:6e3,step:1}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("深度"),(0,i.jsx)(c.A,{className:t.input,value:g,suffix:"mm",onChange:function(e){return _(S,"width")(Number(e.target.value))}})]}),(0,i.jsx)(s.A,{className:t.slider,value:g,onChange:_(S,"width"),min:0,max:6e3,step:1}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("高度"),(0,i.jsx)(c.A,{className:t.input,value:j,suffix:"mm",onChange:function(e){return _(w,"height")(Number(e.target.value))}})]}),(0,i.jsx)(s.A,{className:t.slider,value:j,onChange:_(w,"height"),min:0,max:2800,step:1}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("投影面积(宽*高)"),(0,i.jsxs)("label",{className:t.input,children:[(b*j/1e3/1e3).toFixed(2),"m²"]})]})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.title,children:[(0,i.jsx)("div",{children:n("离地")}),(0,i.jsx)("button",{className:t.resetBtn,onClick:function(){u.nb.DispatchEvent(u.n0.ResetSize,null)},children:a.homeStore.IsLandscape?n("恢复默认"):(0,i.jsx)(h.A,{type:"icon-reset"})})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("离地"),a.homeStore.IsLandscape&&(0,i.jsx)(s.A,{className:t.slider,value:A,onChange:_(k,"pos_z"),min:0,max:2800,step:1}),(0,i.jsx)(c.A,{className:t.input,value:A,suffix:"mm",onChange:function(e){return _(k,"pos_z")(Number(e.target.value))}})]}),!a.homeStore.IsLandscape&&(0,i.jsx)(s.A,{className:t.slider,value:A,onChange:_(k,"pos_z"),min:0,max:2800,step:1})]})})}))},36906:function(e,n,t){t.d(n,{G:function(){return s}});var i=t(64186);function o(e,n,t,i,o,r,a){try{var l=e[r](a),u=l.value}catch(e){return void t(e)}l.done?n(u):Promise.resolve(u).then(i,o)}function r(e){return function(){var n=this,t=arguments;return new Promise((function(i,r){var a=e.apply(n,t);function l(e){o(a,i,r,l,u,"next",e)}function u(e){o(a,i,r,l,u,"throw",e)}l(void 0)}))}}function a(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function l(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),i.forEach((function(n){a(e,n,t[n])}))}return e}function u(e,n){var t,i,o,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(u){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(r=0)),r;)try{if(t=1,i&&(o=2&l[0]?i.return:l[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,l[1])).done)return o;switch(i=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,i=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(!(o=r.trys,(o=o.length>0&&o[o.length-1])||6!==l[0]&&2!==l[0])){r=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){r.label=l[1];break}if(6===l[0]&&r.label<o[1]){r.label=o[1],o=l;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(l);break}o[2]&&r.ops.pop(),r.trys.pop();continue}l=n.call(e,r)}catch(e){l=[6,e],i=0}finally{t=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,u])}}}function s(e){return c.apply(this,arguments)}function c(){return(c=r((function(e){return u(this,(function(n){switch(n.label){case 0:return[4,(0,i.Ap)({method:"post",url:"api/njvr/layoutMetaImageSize/insert",data:l({},e),timeout:6e4}).catch((function(e){return null}))];case 1:return[2,n.sent()]}}))}))).apply(this,arguments)}}}]);