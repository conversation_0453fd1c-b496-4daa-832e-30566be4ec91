import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { LayoutAI_App } from '../../LayoutAI_App';
import { TAppManagerBase } from "../../AppManagerBase";
import { DrawingFigureMode } from "../Layout/IRoomInterface";

export class TCadCameraLayer extends TDrawingLayer
{    

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadCamera,manager);
        this._manager = manager;
    }

    onDraw()
    {
        if(this.ai_cad_data) {
            LayoutAI_App.instance._haveData = true;
        };

        this.painter.strokeStyle= "#f00";

        const entities = this.layout_container.getCandidateEntities(["ViewCamera"]);

        for(const entity of entities)
        {
            if(entity){
                entity.drawEntity(
                    this.painter,
                    {
                        is_draw_figure:true,
                        is_draw_texture:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Texture,
                        draw_decoration:true,
                        is_draw_outline:this._manager.layout_container.drawing_figure_mode===DrawingFigureMode.Outline
                    }
                );
            }
        }
    }
}
