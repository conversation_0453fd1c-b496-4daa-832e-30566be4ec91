import { ZPolygon } from '@layoutai/z_polygon';

import { TRoom } from '../TRoom';
import { ILayoutRoom } from '@layoutai/layout_service';

/**
 * @description 布局房间实现，用于桥接旧的房间实体
 * <AUTHOR>
 * @date 2025-07-28
 * @lastEditTime 2025-07-28 09:55:16
 * @lastEditors xuld
 */
export class LayoutRoom implements ILayoutRoom {
    private _room: TRoom;
    constructor(room: TRoom) {
        this._room = room;
    }

    public get featurePoly(): ZPolygon {
        return this._room.room_shape._feature_shape._w_poly;
    }

    public get name(): string {
        return this._room.name;
    }

    public get room(): TRoom {
        return this._room;
    }
}
