const deploy2PMS = require('@svg/deploy');

const {
  BUILD_USER = '',
  BUILD_ID = '0',
  versionId = '',
  OSS_CDN_DOMAIN,
} = process.env;

if (!versionId) throw Error('部署失败！');

deploy2PMS.deploy({
  // env: 'test',
  data: {
    versionId,
    module: 'web',
    submodule: 'main',
    submoduleVersion: versionId,
    author: BUILD_USER,
    submoduleEntry: `${OSS_CDN_DOMAIN}auto/layoutai/main/${versionId}/index.html?bid=${BUILD_ID}`,
  }
});
