export interface CapinetQueryResp {
    id: number;
    material_id: string;
    name?: string;
    orientation?: string;
    depth: number;
    width: number;
    height: number;
    labels?: {
        out_drawer_num: number;
        out_drawer_area: number;
        out_drawer_area_ratio: number;
        long_clothes_length: number;
        long_clothes_num: number;
        long_clothes_area: number;
        long_clothes_area_ratio: number;
        short_clothes_length: number;
        short_clothes_num: number;
        short_clothes_area: number;
        short_clothes_area_ratio: number;
        stack_area: number;
        stack_num: number;
        stack_area_ratio: number;
        desk: number;
    };
    main_img_path?: string;
    wireframes_img_path?: string;
    file_id?: null;
    min_width?: number;
    max_width?: number;
    min_height?: number;
    max_height?: number;
    min_depth?: number;
    max_depth?: number;
    zone_info?: {
        quilt_list: {
            width: number;
            height: number;
            depth: number;
            centroid: number[];
        }[];
        luggage_list: {
            width: number;
            height: number;
            depth: number;
            centroid: number[];
        }[];
        fold_list: {
            width: number;
            height: number;
            depth: number;
            centroid: number[];
        }[];
        long_clothes_list: {
            width: number;
            height: number;
            depth: number;
            centroid: number[];
        }[];
        short_clothes_list: {
            width: number;
            height: number;
            depth: number;
            centroid: number[];
        }[];
    };
    xml_path?: string;
}