import { magiccubeRequest, openApiRequest } from '@/utils';

/**
 * @description 创建视角
 */
export async function insertViewEffects(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `api/njvr/layoutSchemeViewEffects/insert`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}

/**
 * @description 分页查询布局视角效果图
 */
export async function pageViewImage(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `api/njvr/layoutSchemeViewEffects/pageViewImage`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}