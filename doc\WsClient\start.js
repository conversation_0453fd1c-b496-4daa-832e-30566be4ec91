const {app, BrowserWindow,session} = require('electron') 
const url = require('url') 
const path = require('path')  

const myWsServer = require("./myWsServer")
function createWindow() { 
   win = new BrowserWindow({width: 800, height: 600,
      webPreferences: {
        nodeIntegration: true,
        webSecurity: false
      }}) 

   //  win.loadURL("https://local3d.3vjia.com:8082/Websocket",{ extraHeaders: 'Sec-Trusted-Types-Policy: none' }); 
    win.loadURL(url.format ({ 
      pathname: path.join(__dirname, './dist/index.html'), 
      protocol: 'file:', 
      slashes : true,
      query : {
         WebSocket : true
      }
   })) 
   let target_path = "file:///"+ (path.join(__dirname, '/dist/WebSocket')).split("\\").join("/");

   console.log(target_path);
   win.webContents.session.webRequest.onBeforeRequest({
      urls: [target_path],
    }, (details, callback) => {
      //file:///D:/js_projects/layout_ai/doc/WsClient/dist/Training

      win.loadURL(url.format ({ 
         pathname: path.join(__dirname, '/dist/index.html'), 
         protocol: 'file:', 
         slashes : true,
         query : {
            WebSocket : true
         }
      })) 
    });
}  
app.commandLine.appendSwitch('ignore-certificate-errors')

app.on('ready', createWindow) 