import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { DrawingFigureMode, DrawingLayerNames } from "../Layout/IRoomInterface";


export class TBaseSpaceDrawingLayer extends TDrawingLayer
{
    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.BaseSpaceDrawingLayer,manager);
    }

    onDraw(): void {
        let space_entities = this.layout_container._base_space_entities;
        if(!space_entities || space_entities.length == 0) return;
        space_entities.forEach((entity)=>{
            entity.drawEntity(this.painter,{
                is_draw_figure:true,
                is_draw_outline:this._manager.layout_container.drawing_figure_mode === DrawingFigureMode.Outline,
                is_draw_texture:this._manager.layout_container.drawing_figure_mode === DrawingFigureMode.Texture
            });  
        })
    }
}