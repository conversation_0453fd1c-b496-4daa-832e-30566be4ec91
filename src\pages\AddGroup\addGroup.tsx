import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { <PERSON><PERSON>, Modal, message } from "@svg/antd";
import { useEffect, useState, useRef } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { AI2DesignBasicModes, AI2DesignManager } from "@/Apps/AI2Design/AI2DesignManager";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { _createHxId, _editHxId, checkIsMobile, is_debugmode_website, mode_type } from "@/config";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import useStyles from "./style";
import FigureLabelList from "../Mobile/materialList/Menu/figureLabelList";
import PadStatusbar from "../Mobile/StatusBar/padStatusbar";
import { LayoutPopEvents } from "../Mobile/layoutPopup/layoutPopup";
import LeftSizeEditor from "../Mobile/Size/leftSizeEditor";

/**
 * @description 主页
 */

const App: React.FC = () => {
  const { t } = useTranslation()
  let store = useStore();
  const { styles } = useStyles()
  LayoutAI_App.UseApp(AI2DesignManager.AppName); // 确保当前的app_id
  if (LayoutAI_App.instance) {
    LayoutAI_App.t = t;
  }
  const [popupType, setPopupType] = useState<string | null>(null);
  const object_id = "PadMobile";

  const updateCanvasSize = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
      LayoutAI_App.instance.update();
    }
    updateLandscape();
  };
  const updateIsWebSiteDebug = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_website_debug = is_debugmode_website;
    }
  }

  const updateLandscape = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_landscape = window.innerWidth > window.innerHeight;
    }
    let t_is_lanscape = store.homeStore.IsLandscape;
    store.homeStore.setIsLandscape(window.innerWidth > window.innerHeight);

    document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
  }


  useEffect(() => {
    LayoutContainerUtils.updateAliasName();
    store.homeStore.setRoomEntites((LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities);
  }, [(LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities])

  useEffect(() => {
    updateIsWebSiteDebug();
    // LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
    window.addEventListener('resize', updateCanvasSize);
    updateCanvasSize();

    if (LayoutAI_App.instance) {
      if (!LayoutAI_App.instance.initialized) {
        if(checkIsMobile() && (mode_type === 'HouseId' || mode_type === 'CopyingBase64')){
          LayoutAI_App.emit(EventName.Initializing, { initializing: true });
        }
        LayoutAI_App.instance.init();
        // 默认先进入AiCadMode
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
        LayoutAI_App.instance.prepare().then(() => {
          LayoutAI_App.emit(EventName.Initializing, { initializing: false });
          let scene3D =  (LayoutAI_App.instance).scene3D as Scene3D;
          if (scene3D) {
            scene3D.stopRender();
          }
        })
        LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
      }
      if (window?.URLSearchParams) {
        const urlParams = new URLSearchParams(window.location.search);
        const debug = urlParams.get("debug");
        if (debug !== null) { // 只有在 debug 存在时才执行赋值
          const debugValue = debug === '1' ? 1 : 0; // 根据 debug 的值设置 debugValue
          if (localStorage) {
            localStorage.setItem("LayoutAI_Debug", String(debugValue));
            LayoutAI_App.instance._debug_mode = debugValue;
          }
        }
      }

      LayoutAI_App.instance.update();
    }
  }, [store.homeStore.isAutoExit]);

  useEffect(() => {


    LayoutAI_App.on_M(LayoutPopEvents.showPopup, object_id, (popupType) => {
      setPopupType(popupType);
    });
    LayoutAI_App.on_M(EventName.SelectingTarget, object_id, (params, event, pp) => {
      if(!params){
        setPopupType(null);
      }
    });

    window.parent.postMessage({
        origin: 'layoutai.api',
        type: 'get_group',
        data: {
        }
    }, '*');
    window.addEventListener('message', (e) => {
      if(e.data.origin === 'ims2' && e.data.type === 'addCombination')
      {
        store.addGroupStore.setAddGroupData({
            data: e?.data?.data?.menuObj && JSON.parse(e?.data?.data?.menuObj),
            selected_key: e?.data?.data?.selectedKeys,
            sizeObj: e?.data?.data?.sizeObj && JSON.parse(e?.data?.data?.sizeObj),
            type: e?.data?.data?.type,
        });
      }
    });
  }, []);
  return (
    <div className={styles.root} >
        <PadStatusbar></PadStatusbar>
        <div id="pad_left_panel" className={styles.leftPanelRoot} >
            <div className={styles.listContainer}>
                <div className={styles.listContainer} style={{ display: (popupType !== "sizeEditor" ? 'block' : 'none') }}><FigureLabelList></FigureLabelList> </div>
                <div className={styles.listContainer} style={{ display: (popupType === "sizeEditor" ? 'block' : 'none') }}> <LeftSizeEditor></LeftSizeEditor> </div>
            </div>
        </div>


      <div id='Canvascontent' className={styles.content}>
        <div id="body_container" className={styles.canvas_pannel + " left_panel_layout"}>
          <canvas
            id="cad_canvas"
            className="canvas"
            onMouseEnter={() => {
              store.homeStore.setIsmoveCanvas(false);
            }}
            onMouseLeave={() => {
              store.homeStore.setIsmoveCanvas(true);
            }}
            onTouchStart={(e) => {
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy)
                store.homeStore.setInitialDistance(distance / store.homeStore.scale);
              }
            }}
            onTouchMove={(e) => {
              e.stopPropagation();
              if (e.touches[e.touches.length - 1].identifier == 2) return;
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                let newScale = distance / store.homeStore.initialDistance;
                if (newScale > 5) {
                  newScale = 5;
                } else if (newScale < 0.001) {
                  newScale = 0.001;
                }
                store.homeStore.setScale(newScale);

                LayoutAI_App.DispatchEvent(LayoutAI_Events.scale, newScale)
              }
            }}
            onTouchEnd={(e) => {
              if (e.touches.length > 0) {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.updateLast_pos, e)
              }
              store.homeStore.setInitialDistance(null);
            }}
          />
        </div>
      </div>
    
    </div>
  );
};

export default observer(App);
