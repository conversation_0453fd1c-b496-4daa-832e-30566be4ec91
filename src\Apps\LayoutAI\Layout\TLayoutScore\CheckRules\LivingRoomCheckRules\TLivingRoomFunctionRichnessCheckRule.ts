import { ZRect } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomClassfierCheckRule } from "./TLivingRoomClassfierCheckRule";
import { Vector3 } from "three";

export class TLivingRoomFunctionRichnessCheckRule extends TLivingRoomClassfierCheckRule
{
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    protected setParamConfig(): void {
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        void(room);
        void(figure_element);
        
        let sofaFigures: TFigureElement[] = this.categoryFigures.get(this.sofaName);
        let diningTables: TFigureElement[] = this.categoryFigures.get(this.diningTableName);

        let tvCabinets: TFigureElement[] = [...this.categoryFigures.get(this.TVName), ...this.categoryFigures.get(this.bookCabinetName)];
        let diningCabinets: TFigureElement[] = this.categoryFigures.get(this.diningCabinetName);
        let entranceCabinets: TFigureElement[] = this.categoryFigures.get(this.entranceCabinetName);
        let targetScore: number = 0;
        if(sofaFigures.length == 0 || diningTables.length == 0)
        {
            targetScore -= 60;
        }
        if(tvCabinets.length > 0)
        {
            targetScore += 10;
        }
        if(diningCabinets.length > 0)
        {
            targetScore += 10;
        }
        if(entranceCabinets.length > 0)
        {
            targetScore += 20;
        }
        return {score: targetScore};
    }


}
