"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[2582],{7130:function(n,e,t){var i=t(79874);function r(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function o(){var n=r(["\n      display: flex;\n      justify-content: start;\n      align-items: center;\n      border-radius: 50px;\n      background: #fff;\n      border: 1px solid #FFFFFF;\n      box-shadow: 0px 6px 20px 0px #0000001E;\n      position: fixed;\n      left: 50%;\n      z-index: 9;\n      transform: translateX(-50%);\n      overflow:hidden;\n      top: 60px; \n      left: 50%; \n       flex-direction: row; \n      .topLine{\n        width: 40px;\n        height: 2px;\n        background: #E0E1E1;\n        position: absolute;\n        top: 5px;\n        left: 50%;\n        transform: translateX(-50%);\n        border-radius: 10px;\n      }\n    "]);return o=function(){return n},n}function a(){var n=r(["\n      opacity: 1;\n    "]);return a=function(){return n},n}function l(){var n=r(["\n      opacity: 0;\n    "]);return l=function(){return n},n}function s(){var n=r(["\n      width: 56px;\n      height: 60px;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      color: #282828;\n      font-size: 12px;\n      padding: 4px 0px;\n      margin: 0 8px;\n      position: relative;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 30px !important;\n        height: 44px !important;\n        margin: 0 4px !important;\n        font-size: 10px !important;\n        .label {\n          width:20px;\n        }\n      }\n      @media screen and (orientation: landscape) {\n        width: 48px;\n        font-size: 11px;\n      }\n      div{\n        margin: 0px 0px;\n      }\n      .divider\n      {\n        position: absolute;\n        left: -5px;\n        height: 100%;\n        border-left: 1px #E0E1E1 solid;\n        width: 1px;\n      }\n    "]);return s=function(){return n},n}e.A=(0,i.rU)((function(n){var e=n.css;return{root:e(o()),show:e(a()),hide:e(l()),btnInfo:e(s())}}))},7474:function(n,e,t){var i=t(79874);function r(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function o(){var n=r(["\n      position: fixed;\n      left: 0;\n      bottom: 15px;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0); /* 透明蒙层 */\n      display: flex;\n      justify-content: center;\n      align-items: flex-end; /* 从底部对齐 */\n      transition: transform 0.3s ease;\n      transform: translateY(100%); /* 初始状态在视口外 */\n      &.show {\n          transform: translateY(0); /* 显示状态 */\n      }\n      @media screen and (orientation: landscape) {\n        top: 0px;\n        bottom: auto;\n        right: 0px;\n        left: auto;\n        max-height: calc(var(--vh, 1vh) * 100);\n        transform: translateX(100%);\n        justify-content: flex-end;\n        &.show {\n          transform: translateX(0);\n        }\n\n      }\n    "]);return o=function(){return n},n}function a(){var n=r(["\n      background-color: white;\n      width: 90%;\n      padding: 20px;\n      border-radius: 8px;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n      @media screen and (orientation: landscape) {\n        height: 100%;\n        width: 100%;\n        max-width: 225px;\n        &.leftSizeEditor {\n          position:absolute;\n          left:0;\n          top:0;\n        }\n      }\n    "]);return a=function(){return n},n}function l(){var n=r(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      line-height: 1.4;\n      font-weight: 600;\n      margin-bottom: 25px;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        font-size: 16px;\n        margin-bottom: 35px;\n\n      }\n    "]);return l=function(){return n},n}function s(){var n=r(["\n      display:none;\n      font-size:12px;\n      line-height:20px;\n      font-weight:500;\n      margin-left:30px;\n      input[type='radio'], input[type='checkbox'] {\n        box-sizing: border-box;\n        padding: 0;\n      }\n      span {\n        position:relative;\n        bottom:2px;\n      }\n      @media screen and (orientation: landscape) {\n        display:inline;\n\n\n      }\n    "]);return s=function(){return n},n}function c(){var n=r(["\n      border-radius: 12px;\n      border: 1px solid #00000026;\n      padding: 10px 20px;\n      background-color: white;\n      color: #282828;\n      width: 92px;\n      height: 24px;\n      font-size: 12px;\n      align-items: center;\n      display: flex;\n      justify-content: center;\n      @media screen and (orientation: landscape) {\n        width: 80px;\n        height: 20px;\n        font-size: 12px;\n        padding: 3px 3px;\n        position:absolute;\n        left : 122px;\n        top : 50px;\n        margin-bottom:10px;\n\n      }\n    "]);return c=function(){return n},n}function u(){var n=r(["\n      display: flex;\n      align-items: center;\n      margin-bottom: 20px;\n      justify-content: space-between;\n    "]);return u=function(){return n},n}function d(){var n=r(["\n        flex: 1;\n        margin-right: 20px;\n        margin-left: 20px;\n        @media screen and (orientation: landscape) {\n          margin-right: 0px;\n          margin-left: 0px;\n        }\n    "]);return d=function(){return n},n}function p(){var n=r(["\n        width: 124px;\n        @media screen and (orientation: landscape) {\n          width: 80px;\n          font-size : 12px;\n        }\n    "]);return p=function(){return n},n}e.A=(0,i.rU)((function(n){var e=n.css;return{root:e(o()),container:e(a()),title:e(l()),geometricCheck:e(s()),resetBtn:e(c()),sliderContainer:e(u()),slider:e(d()),input:e(p())}}))},50617:function(n,e,t){t.d(e,{A:function(){return mn}});var i=t(13274),r=t(41594),o=t(22193),a=t(98147);function l(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function s(){var n=l(["\n      background: #FFF;\n      height: 100%;\n      z-index: 999;\n      padding: 0 0 0 16px;\n      /* overflow: hidden; */\n      position: fixed;\n      top: 48px;\n      left: 0;\n    "]);return s=function(){return n},n}function c(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    "]);return c=function(){return n},n}function u(){var n=l(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      line-height: 1.4;\n      letter-spacing: 0px;\n      text-align: left;\n      font-weight: 600;\n      margin: 16px 0px;\n    "]);return u=function(){return n},n}function d(){var n=l(["\n      border-radius: 30px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      min-width: 70%;\n      height: 32px;\n      border: none;\n      margin: 16px 0 0 0px;\n      padding-left: 30px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return d=function(){return n},n}function p(){var n=l(["\n      position: absolute;\n      top: 113px;\n      left: 7px;\n    "]);return p=function(){return n},n}function f(){var n=l(["\n      position: absolute;\n      top: 97px;\n      right: 38%;\n      cursor: pointer;\n    "]);return f=function(){return n},n}function h(){var n=l(["\n      width: 24%;\n      margin: 16px 8px 0px 0;\n      display: flex;\n      justify-items: baseline;\n      align-items: center;\n      a {\n        color: #ffffff;\n        padding: 5px;\n        line-height: 23px;\n        height: 34px;\n      }\n      a:hover {\n        color: #3D9EFF;\n        border-radius: 10px;\n        background: #BFD8FF14;\n        transition: all .3s;\n      }\n    "]);return h=function(){return n},n}function m(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      padding-right: 28px;\n      margin-bottom: 16px;\n    "]);return m=function(){return n},n}function x(){var n=l(["\n      height: auto;\n      @media screen and (orientation: landscape) {\n        padding-bottom: 16px;\n      }\n    "]);return x=function(){return n},n}function g(){var n=l(["\n      @media screen and (orientation: landscape) {\n        width: 224px;\n      }\n    "]);return g=function(){return n},n}function v(){var n=l(["\n      width: 180px;\n      height: 28px;\n      display: flex;\n      justify-content: center;\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      font-weight: 600;\n      color: #959598;\n      z-index: 9;\n      align-items: center;\n      width: 100%;\n      padding: 0px 16px;\n      margin: 16px 0px;\n      position: relative;\n      div{\n        margin-right: 8px;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: auto;\n        font-size: 16px;\n        top: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        font-size: 16px;\n        justify-content: space-between;\n      }\n      @media screen and (orientation: portrait) {\n        top : 0px;\n        left: 12px;\n        width: 50%;\n        justify-content: start;\n      }\n      .checked:after\n      {\n        content: '';\n        display: block;\n        width: 20px;\n        height: 3px;\n        border-radius: 10px;\n        background-color: #282828;\n        margin-top: 5px;\n        margin-left: 23px;\n        position: absolute;\n      }\n    "]);return v=function(){return n},n}function b(){var n=l(["\n      overflow-y: hidden;\n      width:100%;\n      display:flex;\n      overflow-x: auto;\n      height: 230px;\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 140px);\n        width: 100%;\n        display: block;\n        overflow-y: auto;\n      }\n      @media screen and (orientation: portrait) {\n        margin-top: 10px;\n      }\n    "]);return b=function(){return n},n}function y(){var n=l(["\n      margin-top: 50px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 80px) !important;\n      }\n    "]);return y=function(){return n},n}function w(){var n=l(["\n      float:left;\n      @media screen and (orientation: landscape) {\n        float: none;\n      }\n    "]);return w=function(){return n},n}function j(){var n=l(["\n      width: 270px;\n      height: 170px;\n      box-sizing: border-box;\n      position: relative;\n      margin:10px;\n      @media screen and (orientation: landscape) {\n        width: 100%;\n        margin: 0;\n        padding: 0 12px;\n        height: 137px;\n        margin-bottom: 40px;\n      }\n    "]);return j=function(){return n},n}function _(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin: 10px 0 4px 0;\n      @media screen and (orientation: landscape) {\n        margin-top: 4px;\n      }\n    "]);return _=function(){return n},n}function S(){var n=l(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 78%;\n    "]);return S=function(){return n},n}function N(){var n=l(["\n      color: #5B5E60;\n      border-radius: 12px;\n      border: 1px solid #0000000F;\n      padding: 4px 8px;\n    "]);return N=function(){return n},n}function I(){var n=l(["\n      color: #5B5E60;\n      font-family: PingFang SC;\n      font-size: 12px;\n      letter-spacing: 0px;\n      text-align: left;\n      background-color: #F2F3F5;\n      width: auto;\n      border-radius: 2px;\n      padding: 2px 8px;\n      display: block;\n      white-space: nowrap;\n    "]);return I=function(){return n},n}function A(){var n=l(["\n      overflow-y: hidden !important;\n    "]);return A=function(){return n},n}function k(){var n=l(["\n      width: 100%;\n      height: 98vh;\n      overflow: auto;\n      position: absolute;\n      left: 0;\n      canvas {\n        margin-left:5px;\n        margin-top:5px;\n        cursor : pointer;\n      }\n    "]);return k=function(){return n},n}function F(){var n=l(["\n      height: 100%;\n      position: absolute;\n      right: 0;\n      top: 0;\n      width: 4px;\n      cursor: col-resize;\n      z-index: 998;\n    "]);return F=function(){return n},n}function O(){var n=l(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 100%;\n      img\n      {\n        width: 120px;\n        height: 120px;\n      }\n      .desc\n      {\n        text-align: center;\n        margin-top: 10px;\n        color: #A2A2A5;\n        font-size: 12px;\n      }\n    "]);return O=function(){return n},n}function E(){var n=l(["\n      border-radius: 4px;\n      height: 100%;\n      overflow: hidden;\n      @media screen and (orientation: landscape) {\n      }\n      img {\n        transition: all .5s;\n        width: 100%;\n        border-radius: 4px;\n      }\n    "]);return E=function(){return n},n}function z(){var n=l(["\n      border: 2px solid #9242FB !important;\n    "]);return z=function(){return n},n}function C(){var n=l(["\n      position: absolute;\n      top: 4px;\n      right: 16px;\n      border-radius: 4px;\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n    "]);return C=function(){return n},n}function P(){var n=l(["\n      \n      \n    "]);return P=function(){return n},n}function M(){var n=l(["\n      width: 560px !important;\n      @media screen and (max-width: 450px) {\n        width: 300px !important;\n      }\n      @media screen and (orientation: landscape) {\n        width: 900px !important;\n      }\n    "]);return M=function(){return n},n}function L(){var n=l(["\n      padding: 20px;\n      @media screen and (max-width: 450px) {\n        padding: 14px;\n      }\n      @media screen and (orientation: landscape) {\n        display: flex;\n        .ant-carousel {\n          max-width: 420px;\n          margin: auto 0;\n          margin-right: 40px;\n        }\n      }\n    "]);return L=function(){return n},n}function B(){var n=l(["\n      .swj-baseComponent-Containersbox-title{\n        background-color: #fff !important;\n      }\n      .swj-baseComponent-Containersbox-body\n      {\n        > div:first-child {\n          height: 760px !important; /* 只影响第一个子 div */\n          @media screen and (max-width: 450px) {\n            height: 500px !important;\n          }\n          @media screen and (orientation: landscape) {\n            height: 475px !important;\n            margin-top: -1px;\n          }\n        }\n       \n      }\n      \n    "]);return B=function(){return n},n}function D(){var n=l(["\n      display: flex;\n      flex-wrap: wrap;\n      gap: 6px;\n      height: 760px;\n      overflow-y: auto;\n      max-height: 320px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 205px;\n        gap: 12px;\n      }\n      @media screen and (orientation: landscape) {\n        gap: 18px;\n      }\n      ::-webkit-scrollbar-thumb\n      {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n    "]);return D=function(){return n},n}function T(){var n=l(["\n      width: 100%;\n      \n      height: 290px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 300px;\n        height: 160px;\n      }\n      @media screen and (orientation: landscape) {\n        border-radius: 8px;\n        height: 320px;\n        min-width: 520px;\n      }\n    "]);return T=function(){return n},n}function R(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      margin-top: 10px;\n      @media screen and (orientation: landscape) {\n        margin-top: 20px;\n      }\n      button{\n        width: 50%;\n        height: 32px;\n        margin-right: 10px;\n      }\n      .leftBtn{\n        color: #000;\n        border-radius: 10px;\n        border: none;\n        border-radius: 6px;\n        background: #EAEAEB;\n      }\n      .rightBtn{\n        border-radius: 6px;\n        border: none;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        color: #fff;\n      }\n    "]);return R=function(){return n},n}var U=(0,t(79874).rU)((function(n){var e=n.css;return{container:e(s()),titleContainer:e(c()),title:e(u()),container_input:e(d()),Icon:e(p()),IconDelete:e(f()),selectInfo:e(h()),findInfo:e(m()),roomListBar:e(x()),bottomPanel:e(g()),topSelect:e(v()),container_listInfo:e(b()),type:e(y()),container_box:e(w()),container_data:e(j()),textInfo:e(_()),container_title:e(S()),container_desc:e(N()),seriesStyle:e(I()),noScroll:e(A()),side_list:e(k()),line:e(F()),emptyInfo:e(O()),Popover_hoverInfo:e(E()),Popover_hoverInfo_type:e(z()),tag_label:e(C()),applyBtn:e(P()),panel:e(M()),panelContent:e(L()),panelContainer:e(B()),materialList:e(D()),roomImg:e(T()),applyBtnInfo:e(R())}})),V=t(88454),X=t(61307),$=t(13915),H=t(9003),W=t(27347),q=t(88934),K=t(34447),G=t(85783),Y=t(23825),J=t(15696),Z=t(41980),Q=t(62460),nn=t(46396),en=t(87961),tn=t(9455),rn=t(65640);function on(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function an(n,e,t,i,r,o,a){try{var l=n[o](a),s=l.value}catch(n){return void t(n)}l.done?e(s):Promise.resolve(s).then(i,r)}function ln(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){an(o,i,r,a,l,"next",n)}function l(n){an(o,i,r,a,l,"throw",n)}a(void 0)}))}}function sn(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function cn(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){sn(n,e,t[e])}))}return n}function un(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function dn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||fn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pn(n){return function(n){if(Array.isArray(n))return on(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||fn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fn(n,e){if(n){if("string"==typeof n)return on(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?on(n,e):void 0}}function hn(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&l[0]?i.return:l[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,l[1])).done)return r;switch(i=0,r&&(l=[2&l[0],r.value]),l[0]){case 0:case 1:r=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,i=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!r||l[1]>r[0]&&l[1]<r[3])){o.label=l[1];break}if(6===l[0]&&o.label<r[1]){o.label=r[1],r=l;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(l);break}r[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],i=0}finally{t=r=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var mn=(0,J.observer)((function(n){var e,t,l,s,c=n.type,u=(0,G.B)().t,d=(0,H.P)(),p=U().styles,f=(0,r.useContext)(K.DesignContext),h=f.waitingToFurnishRemaining,m=(f.setWaitingToFurnishRemaining,V.A,dn((0,r.useState)(!1),2)),x=(m[0],m[1]),g=dn((0,r.useState)(!1),2),v=g[0],b=g[1],y=dn((0,r.useState)(0),2),w=(y[0],y[1]),j=dn((0,r.useState)(!1),2),_=(j[0],j[1]),S=(0,r.useRef)(null),N=((0,r.useRef)(null),(0,r.useRef)(null),(0,r.useRef)(null),dn((0,r.useState)([]),2)),I=N[0],A=N[1],k=dn((0,r.useState)([]),2),F=(k[0],k[1]),O=dn((0,r.useState)([]),2),E=(O[0],O[1]),z=dn((0,r.useState)(["全案风格"]),2),C=z[0],P=z[1],M=dn((0,r.useState)((0,Y.fZ)()?260:360),2),L=(M[0],M[1],dn((0,r.useState)((null===(e=d.userStore.userInfo)||void 0===e?void 0:e.isFactory)?"2":"1"),2)),B=(L[0],L[1],dn((0,r.useState)(1),2)),D=(B[0],B[1],dn((0,r.useState)({}),2)),T=(D[0],D[1]),R=dn((0,r.useState)({}),2),J=R[0],on=R[1],an=dn((0,r.useState)(h),2),fn=(an[0],an[1]),mn=dn((0,r.useState)(!1),2),xn=mn[0],gn=mn[1],vn=dn((0,r.useState)(null),2),bn=vn[0],yn=vn[1],wn=dn((0,r.useState)([]),2),jn=wn[0],_n=wn[1],Sn=dn((0,r.useState)([]),2),Nn=Sn[0],In=Sn[1],An=dn((0,r.useState)([]),2),kn=An[0],Fn=An[1],On=dn((0,r.useState)([]),2),En=(On[0],On[1]),zn=dn((0,r.useState)("1"),2),Cn=zn[0],Pn=zn[1],Mn={"成品":["1","2","3","4","15","37","38"],"定制":["10","11","12","13","17","19","20","21","22","23","24","28"],"贴图":["8","14","26","18","25","36","41"]},Ln=dn((0,r.useState)({orderBy:"sort asc",ruleType:(null===(t=d.userStore.userInfo)||void 0===t?void 0:t.isFactory)?2:1,pageSize:100,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),Bn=Ln[0],Dn=Ln[1],Tn=function(){var n=ln((function(){var n,e,t,i;return hn(this,(function(r){switch(r.label){case 0:return b(!0),_(!0),n=Bn,[4,(0,X.Ic)(n)];case 1:return e=r.sent(),(t=null==e?void 0:e.result)&&t.forEach((function(n){var e;n.roomList=null==n||null===(e=n.ruleImageList)||void 0===e?void 0:e.map((function(n){return{imgPath:n}}))})),_(!1),b(!1),t?(i=(null==I?void 0:I.length)>0&&Bn.pageIndex>1?pn(I).concat(pn(t)):t,A(i)):A([]),x(!1),w(null==e?void 0:e.recordCount),[2]}}))}));return function(){return n.apply(this,arguments)}}(),Rn=function(){var n=ln((function(){var n,e,t;return hn(this,(function(i){switch(i.label){case 0:return[4,(0,$.kV)()];case 1:return(e=i.sent())?(t=null===(n=Object)||void 0===n?void 0:n.keys(e).map((function(n,e){return{id:e+1,screenName:n}})),E(t),[2]):[2]}}))}));return function(){return n.apply(this,arguments)}}(),Un=function(){var n=ln((function(){var n,e;return hn(this,(function(t){switch(t.label){case 0:return[4,(0,X.$f)()];case 1:return(n=t.sent())?(e=null==n?void 0:n.map((function(n){return{value:n.key,screenName:n.label}})),F(e),[2]):[2]}}))}));return function(){return n.apply(this,arguments)}}();(0,r.useEffect)((function(){Tn()}),[Bn]),(0,r.useEffect)((function(){var n=function(n){n.ctrlKey&&"q"===n.key.toLowerCase()&&P("全案风格"===C[0]?["风格套系","样板间"]:["全案风格"])};return window.addEventListener("keydown",n),function(){window.removeEventListener("keydown",n)}}),[C]),(0,r.useEffect)((function(){fn(h)}),[h]);var Vn=function(n,e,t,i){d.schemeStatusStore.layoutSchemeSaved=!1,d.schemeStatusStore.pendingOpenSchemeIn3D=!1,W.nb.DispatchEvent(W.n0.SeriesSampleSelected,{series:n,scope:{soft:e,hard:t,cabinet:i,remaining:!1}}),d.homeStore.selectData&&d.homeStore.selectData.rooms&&Xn(d.homeStore.selectData.rooms)},Xn=function(n){var e=J;e={};var t=!0,i=!1,r=void 0;try{for(var o,a=n[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var l=o.value;if(l._scope_series_map)for(var s in l._scope_series_map){var c=l._scope_series_map[s];c&&c.ruleId&&(e[c.ruleId]||(e[c.ruleId]={}),e[c.ruleId][s]=!0)}}}catch(n){i=!0,r=n}finally{try{t||null==a.return||a.return()}finally{if(i)throw r}}on(e)};(0,r.useEffect)((function(){x(!0),Rn(),Un()}),[]);var $n="SeriesCandidateList";(0,r.useEffect)((function(){return W.nb.on_M(q.U.SelectingRoom,$n,(function(n){Xn(n.current_rooms||[]),setTimeout((function(){d.homeStore.setSelectData({rooms:null==n?void 0:n.current_rooms,clickOnRoom:!0})}),20)})),function(){W.nb.off_M(q.U.SelectingRoom,$n)}}),[]);var Hn=function(n){var e=n.list;return(0,i.jsx)("div",{className:p.materialList,children:e.map((function(n,e){return(0,i.jsx)("img",{width:80,height:80,src:"".concat(n.imagePath,"?x-oss-process=image/resize,m_fixed,h_80,w_80"),alt:""},e)}))})},Wn=function(){_n([]),In([]),Fn([]),En([])},qn=[{key:"1",label:u("定制模型"),children:(0,i.jsx)(Hn,{list:jn})},{key:"2",label:u("软装模型"),children:(0,i.jsx)(Hn,{list:Nn})},{key:"3",label:u("硬装模型"),children:(0,i.jsx)(Hn,{list:kn})}];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:p.bottomPanel,children:[(0,i.jsxs)("div",{className:p.topSelect,children:[(0,i.jsx)(nn.If,{condition:tn.x.instance.hasPermission(en.J.SERIES.SHOW_PLATFORM_SERIES),children:(0,i.jsx)("div",{className:"".concat(1==Bn.ruleType?"checked":""),style:{color:"".concat(1==Bn.ruleType?"#282828":"#959598")},onClick:function(){Dn((function(n){return un(cn({},n),{ruleType:1,pageIndex:1})}))},children:u("平台套系")})}),(0,i.jsx)("div",{className:"".concat(2==Bn.ruleType?"checked":""),style:{color:"".concat(2==Bn.ruleType?"#282828":"#959598")},onClick:function(){Dn((function(n){return un(cn({},n),{ruleType:2,pageIndex:1})}))},children:u("企业套系")})]}),(0,i.jsx)("div",{className:"".concat(p.container_listInfo," ").concat(v?p.noScroll:""," ").concat(c?p.type:""),ref:S,children:I&&I.length>0?(0,i.jsx)(i.Fragment,{children:null==I||null===(l=I.map)||void 0===l?void 0:l.call(I,(function(n,e){return(0,i.jsx)("div",{id:"series_box"+e,className:p.container_box,children:(0,i.jsxs)("div",{className:p.container_data,onMouseEnter:function(){return T((function(n){return un(cn({},n),sn({},e,!0))}))},onMouseLeave:function(){return T((function(n){return un(cn({},n),sn({},e,!1))}))},children:[(0,i.jsxs)("div",{className:"".concat(p.Popover_hoverInfo," ").concat(J[n.ruleId]?p.Popover_hoverInfo_type:""),children:[(0,i.jsx)("img",{onClick:function(){!function(n,e){"软装"==n?Vn(e,!0,!1,!1):"硬装"==n?Vn(e,!1,!0,!1):"定制"==n&&Vn(e,!1,!1,!0)}(c,n),Vn(n,!0,!0,!0)},src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(0,i.jsx)(nn.If,{condition:J[n.ruleId],children:(0,i.jsx)("div",{className:p.tag_label,children:u("使用全部")})})]}),(0,i.jsxs)("div",{className:p.textInfo,children:[(0,i.jsx)("div",{className:p.container_title,title:n.seedSchemeName||n.ruleName,children:n.seedSchemeName||n.ruleName}),(0,i.jsx)("div",{className:p.container_desc,onClick:function(){return function(n){yn(n),rn.log("item",n),Q.VF.getSeriesAllMaterial(n.ruleId,(function(n){_n(n.filter((function(n){return Mn["定制"].includes(String(n.modelFlag))}))),In(n.filter((function(n){return Mn["成品"].includes(String(n.modelFlag))}))),Fn(n.filter((function(n){return Mn["贴图"].includes(String(n.modelFlag))})))}),(function(){rn.log("err")})),gn(!0),Pn("1")}(n)},children:u("详情")})]})]},e)},"record_"+e)}))}):(0,i.jsx)("div",{className:p.emptyInfo,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,i.jsx)("div",{className:"desc",children:u("暂无数据")})]})})})]}),(0,i.jsx)("div",{className:p.panelContainer,children:xn&&(0,i.jsx)(Z._w,{center:!0,className:p.panel,draggable:!0,title:(null==bn?void 0:bn.seedSchemeName)||(null==bn?void 0:bn.ruleName),onClose:function(){gn(!1),Wn()},mask:!0,children:(0,i.jsxs)("div",{className:p.panelContent,children:[(0,i.jsx)(o.A,{effect:"fade",autoplay:!0,children:null==bn||null===(s=bn.roomList)||void 0===s?void 0:s.map((function(n,e){return(0,i.jsx)("div",{children:(0,i.jsx)("img",{className:p.roomImg,src:"".concat(n.imgPath,"?x-oss-process=image/resize,m_fixed,h_290,w_520"),alt:""})},e)}))}),(0,i.jsxs)("div",{children:[(0,i.jsx)(a.A,{defaultActiveKey:"1",items:qn,onChange:function(n){En("1"==n?jn:"2"==n?Nn:kn),Pn(n)}}),(0,i.jsxs)("div",{className:p.applyBtnInfo,children:[(0,i.jsxs)("button",{className:"leftBtn",onClick:function(){"1"==Cn?Vn(bn,!1,!1,!0):"2"==Cn?Vn(bn,!0,!1,!1):"3"==Cn&&Vn(bn,!1,!0,!1),gn(!1),Wn()},children:["应用",u("1"==Cn?"定制":"2"==Cn?"软装":"硬装")]}),(0,i.jsx)("button",{className:"rightBtn",onClick:function(){Vn(bn,!0,!0,!0),gn(!1),Wn()},children:u("应用全部")})]})]})]})})})]})}))},70060:function(n,e,t){t.d(e,{A:function(){return W}});var i=t(13274),r=t(98612),o=t(88934),a=t(48402),l=t(27347),s=t(17287),c=t(9003),u=t(88454),d=t(61214),p=t(15696),f=t(41594),h=t.n(f),m=t(85783),x=t(17830),g=t(46909),v=t(79874);function b(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function y(){var n=b(["\n      display: grid;\n      max-height: 224px;\n      /* padding-top: 16px; */\n      /* transition: all .3s; */\n      overflow-y: auto;\n      overflow-x: hidden;\n      padding: 0 16px;\n      margin-bottom: 16px;\n      grid-template-columns: repeat(6, 1fr);\n      grid-template-rows: repeat(2, 1fr);\n      grid-auto-rows: 1fr;\n      gap: calc((10vw - 16px) / 6);\n      padding-bottom: 30px;\n      &::-webkit-scrollbar {\n        width: 0;\n        height: 0;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        grid-template-columns: repeat(4, 1fr);\n      }\n      @media screen and (orientation: landscape) {\n        grid-template-columns: repeat(2, 1fr);\n        max-height: calc(var(--vh, 1vh) * 100 - 280px);\n      }\n      @media screen and (orientation: landscape) and (max-width: 960px) {\n        min-height: calc(var(--vh, 1vh) * 100 - 190px);\n      }\n      .fold\n      {\n        width: 100%;\n        border-radius: 4px;\n        background: #F4F5F5;\n        height: 24px;\n        line-height: 24px;\n        font-weight: 600;\n        padding: 0 5px;\n        margin: 8px 0 4px 0;\n      }\n      // .content {\n      //   display: flex;\n      //   flex-wrap: nowrap;\n      //   transition: max-height 0.3s ease-in-out; /* 这将添加过渡动画 */\n      // }\n\n      // .collapsed {\n      //   max-height: 0;\n      // }\n      .item {\n        /* max-width: 30%; */\n        margin: 1vw 0;\n        width: 100%;\n        height: 16vw;\n        cursor: pointer;\n        transition: box-shadow 0.3s ease;\n        user-select:none;\n        @media screen and (max-width: 450px) { // 手机宽度\n          height: 28vw;\n        }\n        @media screen and (orientation: landscape) {\n          height: 9vw;\n        }\n        :nth-child(6n) {\n          margin-right: 0;\n        }\n        .image {\n          height: calc(15vw * 0.9);\n          width: 90%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 6px;\n          overflow:hidden;\n          /* transition: all .3s; */\n          background-color: #f5f5f5;\n          @media screen and (max-width: 450px) { // 手机宽度\n            height: calc(25vw * 0.9);\n          }\n          @media screen and (orientation: landscape) {\n            height: 100%;\n            width: 100%;\n          }\n          .ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n          }\n          .structure-image.ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n            height: 60px;\n            width: 60px;\n          }\n          .group_image.ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n          }\n\n        }\n\n        .title {\n          color: #000000;\n          font-size: 12px;\n          padding: 5px 0;\n          height: 40px;\n          line-height: 20px;\n          text-align: center;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n      }\n      .item:hover {\n          /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */\n        }\n    "]);return y=function(){return n},n}function w(){var n=b(["\n      /* height: calc(100vh - 370px) !important; */\n    "]);return w=function(){return n},n}var j=(0,v.rU)((function(n){var e=n.css;return{figure:e(y()),mobile:e(w())}})),_=t(41980);function S(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function N(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return S(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return S(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var I=(0,p.observer)((function(n){var e=n.data,t=n.filterName,r=(0,m.B)().t,o=j().styles,a=((0,c.P)(),N((0,f.useState)(""),2)),s=a[0],u=a[1],d=h().useRef(null),p=N((0,f.useState)(),2),v=(p[0],p[1]);(0,f.useRef)([]);(0,f.useEffect)((function(){var n=new _.Jc({isShowThumbnail:!0,container:document.getElementById("side_pannel"),log:!1});return n.bindDrag(),function(){n.unbindDrag()}}),[]),(0,f.useEffect)((function(){v(e.map((function(){return!1})))}),[e]),(0,f.useEffect)((function(){var n=d.current,e=function(){s&&(l.nb.RunCommand(l._I.LeaveSubHandler),u(""))},t=function(n){var e=d.current.getBoundingClientRect();n.touches[0].clientY<e.bottom-e.height&&d.current&&(d.current.style.overflow="hidden")},i=function(n){d.current&&(d.current.style.overflow="scroll")};return n.addEventListener("mouseup",e),n.addEventListener("touchmove",t),n.addEventListener("touchend",i),function(){n.removeEventListener("mouseup",e),n.removeEventListener("touchmove",t),n.removeEventListener("touchend",i)}}),[s]);var b=h().memo(x.A);return(0,i.jsx)("div",{className:"".concat(o.figure),ref:d,id:"scrollContainerRef",children:e.map((function(n,e){return(0,i.jsx)(h().Fragment,{children:n.figureList.map((function(n,e){return(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("div",{className:"image",onPointerDown:function(e){var t=n.title;n.group_code&&(t="GroupTemplate:"+n.group_code),n.title.includes("单开门")||n.title.includes("推拉门")||n.title.includes("一字窗")||n.title.includes("飘窗"),l.nb.DispatchEvent(l.n0.SelectedFurniture,t)},onPointerUp:function(e){var t=n.title;if(n.group_code)return t="GroupTemplate:"+n.group_code,void l.nb.DispatchEvent(l.n0.mobileAddFurniture,t);l.nb.DispatchEvent(l.n0.mobileAddFurniture,t)},children:(0,i.jsx)(b,{src:"https://3vj-fe.3vjia.com/layoutai/figures_imgs/".concat(n.png),preview:!1,alt:n.title,className:"结构件"===t?"structure-image":""})}),(0,i.jsx)(g.A,{placement:"rightBottom",title:r(n.title),children:(0,i.jsx)("div",{className:"title",children:r(n.title)})})]},e)}))},"figure_menu_key"+e)}))})}));function A(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function k(){var n=A(["\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 224px;\n      }\n    "]);return k=function(){return n},n}function F(){var n=A(["\n\n    "]);return F=function(){return n},n}function O(){var n=A(["\n      display: flex;\n      flex-wrap: nowrap;\n      margin-top: 10px;\n      overflow-x: scroll;\n      padding: 0 16px;\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      @media screen and (orientation: landscape) {\n        flex-wrap: wrap;\n        padding: 0 12px;\n      }\n      .item\n      {\n        width: 66px;\n        height: 28px;\n        padding: 2px 10px;\n        font-weight: 600;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        margin: 2px 5px 2px 0;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        @media screen and (orientation: landscape) {\n          margin: 2px 4px 2px 0px;\n          width: 61px;\n        }\n      }\n      img{\n        width: 16px;\n        height: 16px;\n      }\n      .active\n      {\n        border-radius: 4px;\n        background: #EAEAEB;\n      }\n    "]);return O=function(){return n},n}function E(){var n=A(["\n      width: 100%;\n      height: 1px;\n      background: #EAEAEB;\n      margin: 10px 0;\n    "]);return E=function(){return n},n}function z(){var n=A(["\n      display: flex;\n      margin-bottom: 4px;\n      overflow-x: scroll;\n      width: auto;\n      margin-left: 20px;\n      .item\n      {\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: regular;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        margin-right: 16px;\n        white-space: nowrap;\n        cursor: pointer;\n      }\n      .active\n      {\n        color: #147FFA;\n      }\n    "]);return z=function(){return n},n}function C(){var n=A(["\n      box-sizing: border-box;\n      padding: 12px 12px 0 0;\n      transition: all .3s;\n      min-width: 156px;\n      ul {\n        padding: 0;\n      }\n      li {\n        padding: 0;\n        margin: 0;\n        list-style: none;\n      }\n      .menu {\n        > li {\n          margin-bottom: 16px;\n          transition: all .3s;\n        }\n        li:hover{\n          color: #5B5E60;\n        }\n        &_columns {\n          display: flex;\n        }\n\n        &_item {\n          /* background: #f2f2f2; */\n          padding: 8px 0;\n\n          :first-child {\n            margin-right: 12px;\n          }\n          \n          :last-child li:first-child {\n            width: 72px;\n          }\n          li {\n            // padding: 0 16px 0 22px;\n            margin: 8px 0;\n            color: #25282D;\n            font-family: PingFang SC;\n            font-weight: regular;\n            font-size: 14px;\n            line-height: 20px;\n            height: 20px;\n            width: 60px;\n            letter-spacing: 0px;\n            text-align: left;\n            cursor: pointer;\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            user-select:none;\n          }\n        }\n      }\n      .icon {\n        color: red;\n      }\n      .label {\n        color: #25282D;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        height: 24px;\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        user-select:none;\n\n        &_name {\n          // margin-left: 5px;\n          height: 100%;\n        }\n\n        &_name::after {\n          content: '';\n          height: 8px;\n          display: block;\n          position: relative;\n          top: -3px;\n          opacity: 0.5;\n          background: linear-gradient(90deg, #66B8FF 0%, #147FFA00 100%);\n        }\n\n        &.active {\n          color: rgba(20, 127, 250, 1);\n        }\n      }\n    "]);return C=function(){return n},n}function P(){var n=A(["\n      height: 100%;\n      /* min-width: 256px; */\n      .layout_btns {\n        display: flex;\n        align-items: center;\n        margin-top:10px;\n        padding-left:16px;\n\n        .btn {\n          border-radius: 2px;\n          background: rgba(20, 127, 250, 0.1);\n          color: rgba(20,127,250,1);\n          font-weight: bold;\n          font-size: 14px;\n          margin-bottom:8px;\n          width: 100px;\n          text-align: center;\n          &:first-child{\n            margin-right: 8px;\n          }\n          &:hover{\n            background: rgba(20, 127, 250, 0.25);\n            color: rgba(20,127,250,1);\n          }\n        }\n      }\n    "]);return P=function(){return n},n}function M(){var n=A(["\n      border-radius: 4px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 32px;\n      letter-spacing: 0px;\n      text-align: left;\n      // min-width: 326px;\n      width: 100%;\n      height: 32px;\n      border: none;\n      padding-left: 36px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return M=function(){return n},n}function L(){var n=A(["\n      position: absolute;\n      top: 50%;\n      translate: 16px -50%;\n    "]);return L=function(){return n},n}function B(){var n=A(["\n      position: absolute;\n      top: 8px;\n      right: 10px;\n      cursor: pointer;\n    "]);return B=function(){return n},n}function D(){var n=A(["\n      display: flex;\n    "]);return D=function(){return n},n}function T(){var n=A(["\n      display: flex;\n      align-items: center;\n    "]);return T=function(){return n},n}function R(){var n=A(["\n      display: flex;\n      flex-grow: 1;\n      position: relative;\n    "]);return R=function(){return n},n}function U(){var n=A(["\n      color: #147FFA !important;\n    "]);return U=function(){return n},n}function V(){var n=A(["\n      position: absolute;\n      top: 32px;\n      left: 0;\n      right: 0;\n      background: #fff;\n      border-radius: 4px;\n      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);\n      z-index: 100;\n      padding: 8px 0;\n      max-height: 300px;\n      overflow: auto;\n    "]);return V=function(){return n},n}var X=(0,v.rU)((function(n){var e=n.css;return{root:e(k()),menu_container:e(F()),tab_box:e(O()),line:e(E()),filterlist:e(z()),menu_box:e(C()),figure_box:e(P()),container_input:e(M()),Icon:e(L()),deleteIcon:e(B()),searchInfo:e(D()),closeInfo:e(T()),inputInfo:e(R()),select:e(U()),searchList:e(V())}}));function $(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function H(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return $(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return $(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var W=(0,p.observer)((function(){var n=(0,c.P)(),e=(0,m.B)().t,t=X().styles,p=H((0,f.useState)([]),2),h=p[0],x=p[1],g=H((0,f.useState)(!1),2),v=g[0],b=g[1],y=H((0,f.useState)(""),2),w=y[0],j=y[1],_=H((0,f.useState)(""),2),S=_[0],N=_[1],A=H((0,f.useState)([]),2),k=(A[0],A[1]),F=H((0,f.useState)(!1),2),O=F[0],E=(F[1],H((0,f.useState)([]),2)),z=E[0],C=(E[1],l.nb.IsDebug||n.userStore.beta?a.V.filter((function(n){return"户型"!==n.label})):a.V.filter((function(n){return"视角"!==n.label&&"户型"!==n.label&&"定制"!==n.label}))),P=(0,i.jsx)(u.A,{style:{fontSize:24},spin:!0}),M=[],L=[];C.map((function(n){n.child.map((function(n){M=M.concat(n.figureList)}))})),C.filter((function(n){return"户型"!==n.label})).map((function(n,e){n.child.map((function(n){L=L.concat(n.figureList)}))}));var B=function(n){x(n.child)},D=function(){var n=C.filter((function(n){return!n.label.includes("户型")}));k(n)};return(0,f.useEffect)((function(){B(C[0]),j(C[0].label),N(C[0].child[0].label),l.nb.on_M(o.U.AIDesignModeChanged,"FiguresMenu",(function(){D()})),D()}),[]),(0,f.useEffect)((function(){b(!0),setTimeout((function(){b(!1)}),200)}),[h]),(0,f.useEffect)((function(){l.nb.instance._current_handler_mode,r.f.HouseDesignMode}),[n.homeStore.selectedRoom]),(0,i.jsx)("div",{className:t.root,children:O?(0,i.jsx)(s.xS,{data:z}):(0,i.jsxs)("div",{className:t.menu_container,style:{opacity:O?0:1},children:[(0,i.jsx)("div",{className:t.tab_box,children:C.map((function(n){return(0,i.jsxs)("div",{className:"item ".concat((t=n.label,w===t?"active":"")),onClick:function(){B(n),j(n.label),N(n.child[0].label)},children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/figures_imgs/".concat(n.png),alt:n.title}),(0,i.jsx)("span",{className:"label_name",children:e(n.label)})]},n.label);var t}))}),(0,i.jsx)("div",{className:t.figure_box,children:(0,i.jsx)(d.A,{indicator:P,spinning:v,children:(0,i.jsx)(I,{data:h,filterName:S})})})]})})}))},76135:function(n,e,t){t.d(e,{A:function(){return _}});var i=t(13274),r=t(41594);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n      height: 300px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 85);\n        width: 224px;\n        margin-top: -40px;\n        background: #fff;\n        position: absolute;\n      }\n    "]);return a=function(){return n},n}function l(){var n=o(["\n      height: 40px;\n    "]);return l=function(){return n},n}function s(){var n=o(["\n      position: fixed;\n      top: 50px;\n      right: 20px;\n      z-index: 1200\n    "]);return s=function(){return n},n}function c(){var n=o(["\n      padding: 0 20px;\n      display: flex;\n      justify-content: space-between;\n      @media screen and (orientation: landscape) {\n        flex-direction: column;\n      }\n      .svg-input-number\n      {\n        padding-right: 4px;\n        @media screen and (max-width: 450px) { // 手机宽度\n          width: 65px;\n          font-size: 12px;\n        }\n      }\n      .left\n      {\n        width: 46%;\n        .title{\n          margin: 12px 0 6px 0px;\n        }\n        .houseInfo\n        {\n          .input\n          {\n            width: 50px;\n            margin-right: 4px;\n            @media screen and (max-width: 450px) { // 手机宽度\n              width: 45px;\n              margin-top: 10px;\n            }\n          }\n\n          span{\n            margin-right: 13px;\n            font-size: 14px;\n          }\n        }\n        .leftInfo\n        {\n          display: flex;\n        }\n\n      }\n      .right{\n        width: 46%;\n        .title{\n          margin: 12px 0 6px 0px;\n        }\n        .rightInfo\n        {\n          display: flex;\n          \n        }\n      }\n      .title{\n        @media screen and (orientation: landscape) {\n          font-size: 14px;\n          margin: 16px 0 16px 0px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n      }\n      .houseInfo{\n        @media screen and (orientation: landscape) {\n          display: flex;\n          flex: 0 0 50%;\n          font-size: 14px;\n          flex-wrap: wrap;\n          align-items: center;\n          div{\n            display: flex;\n            align-items: center;\n            margin-right: 8px;\n            margin-bottom: 12px;\n            align-items: center;\n            .input{\n              width: 64px;\n            }\n            span{\n              margin-left: 4px;\n            }\n          }\n        }\n      }\n    "]);return c=function(){return n},n}var u=(0,t(79874).rU)((function(n){var e=n.css;return{root:e(a()),roomListBar:e(l()),name:e(s()),attributeInfo:e(c())}})),d=t(27347),p=t(15696),f=t(85783),h=t(9003),m=t(25076),x=t(83813),g=t(37859),v=t(11164),b=t(63286),y=t(17365);function w(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function j(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return w(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return w(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var _=(0,p.observer)((function(){var n=(0,h.P)(),e=(0,f.B)().t,t=u().styles,o=j((0,r.useState)(0),2),a=(o[0],o[1],j((0,r.useState)(-1),2)),l=(a[0],a[1],j((0,r.useState)(""),2)),s=(l[0],l[1],j((0,r.useState)({}),2)),c=(s[0],s[1],j((0,r.useState)(0),2)),p=(c[0],c[1],j((0,r.useState)(!1),2)),w=(p[0],p[1],j((0,r.useState)({}),2)),_=w[0],S=w[1],N=j((0,r.useState)(),2),I=N[0],A=N[1],k=j((0,r.useState)(),2),F=k[0],O=k[1],E=j((0,r.useState)(),2),z=E[0],C=E[1],P=j((0,r.useState)(),2),M=P[0],L=P[1],B=j((0,r.useState)(),2),D=B[0],T=B[1],R=j((0,r.useState)(0),2),U=R[0],V=R[1],X=j((0,r.useState)(0),2),$=X[0],H=X[1],W=j((0,r.useState)(0),2),q=W[0],K=W[1],G=j((0,r.useState)(0),2),Y=G[0],J=G[1],Z=j((0,r.useState)(0),2),Q=Z[0],nn=Z[1],en=j((0,r.useState)(1),2);en[0],en[1];(0,r.useEffect)((function(){var t,i,r,o,a,l,s,c,u,d,p,f,h,m,x,g,v;"空间信息"!=(null===(t=n.homeStore.attribute)||void 0===t?void 0:t.title)&&(null===(i=n.homeStore.attribute)||void 0===i?void 0:i.tile)!=e("空间信息")||(S(n.homeStore.attribute.properties),A(null===(a=n.homeStore.attribute)||void 0===a||null===(o=a.properties)||void 0===o||null===(r=o.name)||void 0===r?void 0:r.defaultValue),C(null===(c=n.homeStore.attribute)||void 0===c||null===(s=c.properties)||void 0===s||null===(l=s.storey_height)||void 0===l?void 0:l.defaultValue),O(null===(p=n.homeStore.attribute)||void 0===p||null===(d=p.properties)||void 0===d||null===(u=d.ceiling_height)||void 0===u?void 0:u.defaultValue),L(null===(m=n.homeStore.attribute)||void 0===m||null===(h=m.properties)||void 0===h||null===(f=h.floor_thickness)||void 0===f?void 0:f.defaultValue),T(null===(v=n.homeStore.attribute)||void 0===v||null===(g=v.properties)||void 0===g||null===(x=g.max_cabinet_height)||void 0===x?void 0:x.defaultValue))}),[n.homeStore.attribute]),(0,r.useEffect)((function(){if(n.homeStore.roomInfos){var e=0,t=0,i=0,r=0,o=0;n.homeStore.roomInfos.forEach((function(n){e+=n.area,(n.name.includes("室")||n.name.includes("卧"))&&t++,n.name.includes("厅")&&i++,(n.name.includes("厕所")||n.name.includes("卫生间"))&&r++,n.name.includes("厨房")&&o++})),J(parseFloat(e.toFixed(2))),V(t),H(i),K(r),nn(o),C(d.nb.instance.layout_container._storey_height)}}),[n.homeStore.roomInfos]);var tn=(0,r.useCallback)((function(e,t){return function(i){var r;e(i),null==_||null===(r=_[t])||void 0===r||r.onChange(i),"name"===t&&(y.f.updateAliasName(d.nb.instance.layout_container),n.homeStore.setRoomEntites(d.nb.instance.layout_container._room_entities))}}),[_]);return(0,i.jsx)("div",{className:t.root,children:(0,i.jsx)("div",{className:t.attributeInfo,children:n.homeStore.selectedRoom?(0,i.jsx)(i.Fragment,{children:n.homeStore.IsLandscape?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"title",children:e("空间类型")}),(0,i.jsx)("div",{children:(0,i.jsx)(v.A,{value:I,style:{width:"100%"},onChange:tn(A,"name"),options:b.H.getRoomNameOptions(),getPopupContainer:function(n){return n.parentNode}})}),(0,i.jsxs)("div",{className:"title",children:[e("当前层高"),(0,i.jsx)(g.A,{min:0,max:2800,disabled:!0,style:{width:"100px"},value:z,onChange:function(n){return tn(C,"storey_height")(Number(n))},suffix:"mm"})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"left",children:[(0,i.jsx)("div",{className:"title",children:e("空间类型")}),(0,i.jsx)("div",{children:(0,i.jsx)(v.A,{value:I,style:{width:"100%"},onChange:tn(A,"name"),options:b.H.getRoomNameOptions()})}),(0,i.jsx)("div",{className:"title",children:e("当前层高")}),(0,i.jsxs)("div",{className:"leftInfo",children:[(0,i.jsx)(x.A,{min:0,max:2800,onChange:tn(C,"storey_height"),style:{width:"100%"},value:z,disabled:!0}),(0,i.jsx)(g.A,{min:0,max:2800,disabled:!0,style:{width:"100px"},value:z,onChange:function(n){return tn(C,"storey_height")(Number(n))},suffix:"mm"})]}),(0,i.jsx)("div",{className:"title",children:e("地铺厚度")}),(0,i.jsxs)("div",{className:"leftInfo",children:[(0,i.jsx)(x.A,{min:0,max:100,onChange:tn(L,"floor_thickness"),style:{width:"100%"},value:M}),(0,i.jsx)(g.A,{min:0,max:100,style:{width:"100px"},value:M,onChange:function(n){return tn(L,"floor_thickness")(Number(n))},suffix:"mm"})]})]}),(0,i.jsxs)("div",{className:"right",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"title",children:e("最高柜顶高")}),(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{disabled:!0,style:{width:"100%"},value:D,suffix:"mm"})})]}),(0,i.jsx)("div",{className:"title",children:e("吊顶下吊")}),(0,i.jsxs)("div",{className:"rightInfo",children:[(0,i.jsx)(x.A,{min:200,max:400,onChange:tn(O,"ceiling_height"),style:{width:"100%"},value:F}),(0,i.jsx)(g.A,{min:200,max:400,style:{width:"100px"},value:F,onChange:function(n){return tn(O,"ceiling_height")(Number(n))},suffix:"mm"})]})]})]})}):(0,i.jsx)(i.Fragment,{children:n.homeStore.IsLandscape?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"title",children:e("房屋使用面积")}),(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{disabled:!0,style:{width:"100%"},value:Y,suffix:"m²"})})]}),(0,i.jsx)("div",{className:"title",children:e("户型")}),(0,i.jsxs)("div",{className:"houseInfo",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:U}),(0,i.jsx)("span",{children:e("室")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:$}),(0,i.jsx)("span",{children:e("厅")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:q})," ",(0,i.jsx)("span",{children:e("卫")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:Q})," ",(0,i.jsx)("span",{children:e("厨")})]})]}),(0,i.jsxs)("div",{className:"title",children:[e("当前层高"),(0,i.jsx)(g.A,{min:2e3,max:3500,style:{width:"100px"},value:z,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),C(n)},suffix:"mm"})]}),(0,i.jsx)("div",{className:"rightInfo",children:(0,i.jsx)(x.A,{min:2e3,max:3500,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),C(n)},style:{width:"100%"},value:z})})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"left",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"title",children:e("房屋使用面积")}),(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{disabled:!0,style:{width:"100%"},value:Y,suffix:"m²"})})]}),(0,i.jsx)("div",{className:"title",children:e("户型")}),(0,i.jsxs)("div",{className:"houseInfo",children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:U}),(0,i.jsx)("span",{children:e("室")}),(0,i.jsx)(m.A,{className:"input",disabled:!0,value:$}),(0,i.jsx)("span",{children:e("厅")}),(0,i.jsx)(m.A,{className:"input",disabled:!0,value:q}),(0,i.jsx)("span",{children:e("卫")}),(0,i.jsx)(m.A,{className:"input",disabled:!0,value:Q}),(0,i.jsx)("span",{children:e("厨")})]})]}),(0,i.jsxs)("div",{className:"right",children:[(0,i.jsx)("div",{className:"title",children:e("当前层高")}),(0,i.jsxs)("div",{className:"rightInfo",children:[(0,i.jsx)(x.A,{min:2e3,max:3500,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),C(n)},style:{width:"50%"},value:z}),(0,i.jsx)(g.A,{min:2e3,max:3500,style:{width:"100px"},value:z,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),C(n)},suffix:"mm"})]})]})]})})})})}))},78154:function(n,e,t){t.d(e,{$:function(){return S},A:function(){return N}});var i=t(13274),r=t(85783);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n      position:fixed;\n      left:0;\n      bottom:0px;\n      width:100%;\n      overflow: hidden;\n      background-color: #fff;\n      border-radius: 16px 16px 0px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      z-index: 99;\n      @media screen and (orientation: landscape) {\n        position:fixed;\n        left: -1px !important;\n        top: 52px !important;\n        bottom: 0 !important;\n        right: auto !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        width: auto;\n        border-radius: 0px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n      }\n    "]);return a=function(){return n},n}function l(){var n=o(["\n      display: flex;\n      height: 40px;\n      padding: 0 24px;\n      align-items: center;\n      font-size: 20px;\n      color: #282828;\n      font-weight: 600;\n      margin-top: 16px;\n      justify-content: space-between;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 15px;\n        font-size: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        height: 40px;\n        font-size: 14px;\n      }\n    "]);return l=function(){return n},n}function s(){var n=o(["\n      height:100%;\n      width:100%;\n    "]);return s=function(){return n},n}function c(){var n=o(["\n      position: absolute; \n      right: 10px;\n      top: 10px;\n      z-index: 9;\n    "]);return c=function(){return n},n}var u=(0,t(79874).rU)((function(n){var e=n.css;return{root:e(a()),topTitle:e(l()),listContainer:e(s()),open:e(c())}})),d=t(15696),p=t(41594),f=t(27347),h=t(45599),m=t(50617),x=t(70060),g=t(76135),v=t(76330),b=t(93491),y=t(9003),w=t(81074);function j(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function _(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return j(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return j(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var S=function(n){return n.setIsVisible="setIsVisible",n.showPopup="showPopup",n}({}),N=(0,d.observer)((function(){var n,e=(0,r.B)().t,t=u().styles,o=_((0,p.useState)(!0),2),a=(o[0],o[1],_((0,p.useState)(""),2)),l=a[0],s=a[1],c=_((0,p.useState)(!1),2),d=c[0],j=c[1],S=(0,y.P)();(0,p.useEffect)((function(){f.nb.on("showPopup",(function(n){n?(j(!0),s(n)):j(!1)}))}),[]);return(0,i.jsxs)("div",{className:t.root,style:{zIndex:10,maxHeight:S.homeStore.IsLandscape&&(d?"800px":"0px"),maxWidth:!S.homeStore.IsLandscape&&(d?"376px":"0px"),transition:"all 0.3s ease"},children:["view"!=l&&(0,i.jsxs)("div",{className:t.topTitle,children:[(0,i.jsx)("div",{children:function(n){switch(n){case"Layout":return e("布局");case"Matching":return e("");case"view":return e("视角");case"material":return e("素材");case"attribute":return e("属性");case"replace":return e("替换素材");case"searchMaterial":return e("空间素材");default:return""}}(l)}),(0,i.jsx)("div",{children:(0,i.jsx)(v.A,{style:{color:"#959598"},type:"icon-icon",onClick:function(){var n;j(!1),"3D_FirstPerson"===S.homeStore.viewMode&&"Furniture"===(null===(n=S.homeStore.selectEntity)||void 0===n?void 0:n.type)&&f.nb.DispatchEvent(f.n0.cleanSelect,null)}})})]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"Layout"===l?"block":"none"},children:[" ",(0,i.jsx)(h.A,{width:400,showSchemeName:!1,isLightMobile:!0})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"Matching"===l?"block":"none"},children:[" ",(0,i.jsx)(m.A,{})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"material"===l?"block":"none"},children:[" ",(0,i.jsx)(x.A,{})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"attribute"===l?"block":"none"},children:[" ",(0,i.jsx)(g.A,{})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"replace"===l?"block":"none"},children:[" ",(0,i.jsx)(b.A,{selectedFigureElement:null===(n=S.homeStore.selectEntity)||void 0===n?void 0:n.figure_element})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"searchMaterial"===l?"block":"none"},children:[" ",(0,i.jsx)(w.A,{})," "]})]})}))},81074:function(n,e,t){t.d(e,{A:function(){return M}});var i=t(13274),r=t(85783),o=t(15696),a=t(41594);function l(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function s(){var n=l(["\n      height: 510px;\n      @media screen and (max-width: 450px) {\n        height: 380px;\n      }\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 224px;\n      }\n    "]);return s=function(){return n},n}function c(){var n=l(["\n      display: flex;\n      padding: 0 24px;\n      margin-top: 20px;\n      justify-content: space-between;\n      @media screen and (orientation: landscape) {\n        display: block;\n      }\n    "]);return c=function(){return n},n}function u(){var n=l(["\n      width: 32%;\n      height: 80px;\n      border-radius: 8px;\n      background: #F4F5F5;\n      position: relative;\n      overflow: hidden;\n      border: 1px solid #0000000F;\n      @media screen and (orientation: landscape) {\n        width: 100%;\n        margin-bottom: 8px;\n      }\n      .add\n      {\n        left: 50%;\n        top: 50%;\n        position: absolute;\n        transform: translate(-50%, -50%);\n        color: #282828;\n        font-size: 12px;\n        width: 100%;\n        text-align: center;\n      }\n      img{\n        width: 50%;\n        height: 80px;\n        margin-right: 8px;\n        @media screen and (orientation: landscape) {\n          width: 30%;\n          height: 80px;\n        }\n      }\n      .item\n      {\n        display: flex;\n        justify-content: space-between;\n        position: relative;\n        @media screen and (orientation: landscape) {\n          justify-content: start;\n        }\n        .title\n        {\n          width: 50%;\n          position: absolute;\n          text-align: center;\n          bottom: 0px;\n          height: 20px;\n          color: #FFF;\n          border-radius: 0px 0px 4px 4px;\n          background: #00000066;\n          padding-top: 2px;\n          @media screen and (orientation: landscape) {\n            width: 30%;\n          }\n        }\n      }\n      .rightitem\n      {\n\n        width: 50%;\n        display: flex;\n        flex-direction: column;\n        padding: 8px 4px;\n        justify-content: space-between;\n        position: relative;\n        color: #282828;\n        .icon\n        {\n          position: absolute;\n          right: 10px;\n          bottom: 10px;\n          font-size: 18px;\n          color: #5B5E60;\n        }\n        .seriesStyle\n        {\n          border-radius: 4px;\n          background: #FFFFFF;\n          padding: 4px 8px;\n          font-size: 12px;\n          color: #5B5E60;\n          width: 40px;\n          text-align: center;\n          height: 24px;\n        }\n      }\n    "]);return u=function(){return n},n}function d(){var n=l(["\n     overflow-y: scroll;\n     height: calc(100% - 100px);\n     margin-top: 24px;\n     padding: 0 24px;\n     @media screen and (orientation: landscape) {\n      height: calc(var(--vh, 1vh) * 100 - 350px);\n     }\n    .itemInfo {\n        margin-bottom: 16px; /* 每个 item 之间的间距 */\n        overflow: hidden; /* 隐藏溢出内容 */\n\n        .itemList\n        {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 16px;\n          .item{\n            text-align: center;\n            overflow: hidden;\n            width: 104px;\n            position: relative;\n            .redIcon\n            {\n              position: absolute;\n              top: 5px;\n              left: 5px;\n              color: #FF4D4F;\n            }\n            @media screen and (max-width: 450px){\n              width: 82px;\n            }\n            @media screen and (max-width: 400px){\n              width: 69px;\n            }\n            @media screen and (orientation: landscape) {\n              width: 69px;\n            }\n            img{\n              aspect-ratio: 1 / 1;\n              margin-bottom: 4px;\n            }\n            div{\n              text-overflow: ellipsis;\n              white-space: nowrap;\n              overflow: hidden;\n            }\n          }\n          \n        }\n    }\n\n    .header {\n        display: flex;\n        justify-content: space-between; /* 使 label 和 icon 分开 */\n        padding: 12px 0px; /* 内边距 */\n        .title\n        {\n          font-weight: 600;\n          font-size: 14px;\n        }\n    }\n\n    .item {\n        \n    }\n\n    .item img {\n        width: 100%; /* 图片宽度占满 */\n        height: auto; /* 高度自适应 */\n        background-color: #F4F5F5;\n        border-radius: 4px;\n    }\n    &::-webkit-scrollbar {\n        display: none;\n    }\n  \n    scrollbar-width: none;\n    -ms-overflow-style: none;\n    "]);return d=function(){return n},n}function p(){var n=l(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: flex-end;\n      z-index: 10002;\n      @media screen and (orientation: landscape) {\n        justify-content: end;\n        position: fixed;\n\n      }\n    "]);return p=function(){return n},n}function f(){var n=l(["\n      background-color: white;\n      width: 100%;\n      padding: 20px;\n      height: 300px;\n      padding-top: 56px;\n      border-radius: 16px 16px 0px 0px;\n      background: #FFFFFF;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      position: relative;\n      @media screen and (orientation: landscape) {\n        position: fixed;\n        bottom: 12px;\n        right: 0;\n        top: 45px;\n        left: auto;\n        padding: 0;\n        width: 224px;\n        border-radius: 0px;\n        overflow: hidden;\n        height: auto;\n        border-radius: 6px;\n      }\n    "]);return f=function(){return n},n}function h(){var n=l(["\n      position: absolute;\n      bottom: 0;\n      right: 0;\n      width: 100%;\n      height: 586px;\n      background-color: white;\n      box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);\n      transition: transform 0.2s ease;\n      border-radius: 16px 16px 0px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      @media screen and (orientation: landscape) {\n        height: calc(100%);\n        border-radius: 0px;\n        width: 224px;\n        left : 0px;\n        right : auto;\n      }\n      .sideTopInfo\n      {\n        padding: 24px;\n        display: flex;\n        justify-content: space-between;\n        div{\n          font-size: 20px;\n          font-weight: 600;\n          color: #282828;\n          margin-left: 6px;\n        }\n      }\n    "]);return h=function(){return n},n}function m(){var n=l(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n            .size\n            {\n              color: #5B5E60;\n              margin-top: 4px;\n            }\n         } \n      "]);return m=function(){return n},n}function x(){var n=l(["\n      transform: translateX(0);\n      @media screen and (orientation: landscape) {\n        transform: translateX(0);        \n      }\n    "]);return x=function(){return n},n}function g(){var n=l(["\n      transform: translateX(100%);\n      @media screen and (orientation: landscape) {\n        transform: translateX(-100%);\n      }\n    "]);return g=function(){return n},n}function v(){var n=l(["\n      position: absolute;\n      right: 7px;\n      top: 57%;\n      font-size: 16px;\n      color: #5B5E60;\n    "]);return v=function(){return n},n}function b(){var n=l(["\n      position: absolute;\n      left: 5px;\n      top: 5px;\n      font-size: 16px;\n      color: #FFAA00;\n    "]);return b=function(){return n},n}var y=(0,t(79874).rU)((function(n){var e=n.css;return{root:e(s()),styleInfo:e(c()),styleItem:e(u()),materialInfo:e(d()),visible:e(p()),serialsInfo:e(f()),sideVisible:e(h()),topInfo:e(m()),slideIn:e(x()),slideOut:e(g()),lock_icon:e(v()),warn_icon:e(b())}})),w=t(9003),j=t(76330),_=t(27347),S=t(48402),N=t(50617),I=t(33100),A=t(93491),k=t(88934);function F(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function O(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function E(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function z(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||P(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(n){return function(n){if(Array.isArray(n))return F(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||P(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(n,e){if(n){if("string"==typeof n)return F(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?F(n,e):void 0}}var M=(0,o.observer)((function(){var n,e,t,o,l,s,c,u,d,p,f,h,m,x,g,v=(0,r.B)().t,b=y().styles,F=(0,w.P)(),P=z((0,a.useState)([]),2),M=(P[0],P[1]),L=z((0,a.useState)([]),2),B=L[0],D=L[1],T=z((0,a.useState)([]),2),R=T[0],U=T[1],V=z((0,a.useState)(""),2),X=V[0],$=V[1],H=z((0,a.useState)(!1),2),W=H[0],q=H[1],K=z((0,a.useState)(0),2),G=K[0],Y=K[1],J=z((0,a.useState)(null),2),Z=J[0],Q=J[1],nn=z((0,a.useState)({"定制素材":!0,"软装素材":!0,"硬装素材":!0}),2),en=nn[0],tn=nn[1],rn=z((0,a.useState)(!1),2),on=rn[0],an=rn[1],ln=function(n){tn((function(e){return E(function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){O(n,e,t[e])}))}return n}({},e),O({},n,!e[n]))}))},sn=[],cn=[],un=[],dn=[];return _.nb.instance&&_.nb.on(k.U.RoomMaterialsUpdated,(function(){Y(G+1)})),(0,a.useEffect)((function(){!function(){var n,e,t,i,r,o,a,l,s,c,u,d,p,f,h,m,x,g,b,y,w,j,N,I=[];if(F.homeStore.selectEntity){var A=null===(t=F.homeStore.selectedRoom)||void 0===t?void 0:t._room;if(A){var k=C(A._furniture_list);A._furniture_list.forEach((function(e){return(n=k).push.apply(n,C(e.getAlternativeFigureElements()))})),k.sort((function(n,e){return e.default_drawing_order-n.default_drawing_order})),k.forEach((function(n){var e,t,i,r,o,a;if(_.nb.IsDebug||!n._is_decoration&&!n._is_sub_board)if(n.haveMatchedMaterial()||n.haveDeletedMaterial()||n.sub_category.includes("组合")){if("Electricity"!==n._decoration_type&&(n.haveMatchedMaterial()||n.haveDeletedMaterial()||!n.sub_category.includes("组合"))){var l=(null===(e=n._matched_material)||void 0===e?void 0:e.imageUrl)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",s=(null===(t=n._matched_material)||void 0===t?void 0:t.modelId)||"无",c={image_path:l,title:v(n.sub_category)+" "+v("素材ID:")+s,centerTitle:n._matched_material.name,bottomTitle:"".concat(Math.round(null==n||null===(i=n._matched_material)||void 0===i?void 0:i.length),"*").concat(Math.round(null==n||null===(r=n._matched_material)||void 0===r?void 0:r.width),"*").concat(Math.round(null==n||null===(o=n._matched_material)||void 0===o?void 0:o.height)),figure_element:n,room:A};n.haveMatchedCustomCabinet()?dn.push(c):cn.push(c)}}else sn.push({image_path:(null===(a=S.eW[n.sub_category])||void 0===a?void 0:a.png)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",title:v(n.modelLoc)+" | "+v(n.sub_category),centerTitle:v(n.modelLoc),bottomTitle:"".concat(Math.round(n.length),"*").concat(Math.round(n.depth),"*").concat(Math.round(n.height)),figure_element:n,room:A})})),(e=cn).unshift.apply(e,C(sn)),A.getHardDecorationList().forEach((function(n){var e,t,i,r,o;if(n.haveMatchedMaterial()){var a=(null===(e=n._matched_material)||void 0===e?void 0:e.imageUrl)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",l=(null===(t=n._matched_material)||void 0===t?void 0:t.modelId)||"无";un.push({image_path:a,title:v(n.sub_category)+" "+v("素材ID:")+l,centerTitle:n._matched_material.name,bottomTitle:"".concat(Math.round(null==n||null===(i=n._matched_material)||void 0===i?void 0:i.length),"*").concat(Math.round(null==n||null===(r=n._matched_material)||void 0===r?void 0:r.width),"*").concat(Math.round(null==n||null===(o=n._matched_material)||void 0===o?void 0:o.height)),figure_element:n})}})),I.push({title1:(null===(r=A._scope_series_map)||void 0===r||null===(i=r.soft)||void 0===i?void 0:i.ruleName)||null,title2:(null===(a=A._scope_series_map)||void 0===a||null===(o=a.cabinet)||void 0===o?void 0:o.ruleName)||null,title3:(null===(s=A._scope_series_map)||void 0===s||null===(l=s.hard)||void 0===l?void 0:l.ruleName)||null,img1:(null===(u=A._scope_series_map)||void 0===u||null===(c=u.soft)||void 0===c?void 0:c.thumbnail)||null,img2:(null===(p=A._scope_series_map)||void 0===p||null===(d=p.cabinet)||void 0===d?void 0:d.thumbnail)||null,img3:(null===(h=A._scope_series_map)||void 0===h||null===(f=h.hard)||void 0===f?void 0:f.thumbnail)||null,softseriesStyle:null===(x=A._scope_series_map)||void 0===x||null===(m=x.soft)||void 0===m?void 0:m.seriesName,cabinetseriesStyle:null===(b=A._scope_series_map)||void 0===b||null===(g=b.cabinet)||void 0===g?void 0:g.seriesName,hardseriesStyle:null===(w=A._scope_series_map)||void 0===w||null===(y=w.hard)||void 0===y?void 0:y.seriesName,bottomTitle:A.roomname,area:null===(j=A.area)||void 0===j?void 0:j.toFixed(2),room:A});var O=[{label:v("风格"),figureList:I},{label:v("定制素材"),figureList:dn},{label:v("软装素材"),figureList:cn},{label:v("硬装素材"),figureList:un}];M(O),D(null===(N=O[0])||void 0===N?void 0:N.figureList),U(O.filter((function(n,e){return 0!==e})))}}}()}),[F.homeStore.selectEntity,F.homeStore.room2SeriesSampleArray,G,W]),(0,i.jsxs)("div",{className:b.root,children:[(0,i.jsxs)("div",{className:b.styleInfo,children:[(0,i.jsx)("div",{className:b.styleItem,onClick:function(){an(!0),$("软装")},children:(null===(n=B[0])||void 0===n?void 0:n.title1)&&(null===(e=B[0])||void 0===e?void 0:e.img1)?(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("img",{src:null===(t=B[0])||void 0===t?void 0:t.img1,alt:""}),(0,i.jsx)("div",{className:"title",children:v("软装")}),(0,i.jsxs)("div",{className:"rightitem",children:[(0,i.jsx)("span",{children:null===(o=B[0])||void 0===o?void 0:o.title1}),(0,i.jsx)("span",{className:"seriesStyle",children:null===(l=B[0])||void 0===l?void 0:l.softseriesStyle})]})]}):(0,i.jsxs)("span",{className:"add",children:[(0,i.jsx)(j.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),v("添加软装风格")]})}),(0,i.jsx)("div",{className:b.styleItem,onClick:function(){an(!0),$("定制")},children:(null===(s=B[0])||void 0===s?void 0:s.title2)&&(null===(c=B[0])||void 0===c?void 0:c.img2)?(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("img",{src:null===(u=B[0])||void 0===u?void 0:u.img2,alt:""}),(0,i.jsx)("div",{className:"title",children:v("定制")}),(0,i.jsxs)("div",{className:"rightitem",children:[(0,i.jsx)("span",{children:null===(d=B[0])||void 0===d?void 0:d.title2}),(0,i.jsx)("span",{className:"seriesStyle",children:null===(p=B[0])||void 0===p?void 0:p.cabinetseriesStyle})]})]}):(0,i.jsxs)("span",{className:"add",children:[(0,i.jsx)(j.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),v("添加定制风格")]})}),(0,i.jsx)("div",{className:b.styleItem,onClick:function(){an(!0),$("硬装")},children:(null===(f=B[0])||void 0===f?void 0:f.title3)&&(null===(h=B[0])||void 0===h?void 0:h.img3)?(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("img",{src:null===(m=B[0])||void 0===m?void 0:m.img3,alt:""}),(0,i.jsx)("div",{className:"title",children:v("硬装")}),(0,i.jsxs)("div",{className:"rightitem",children:[(0,i.jsx)("span",{children:null===(x=B[0])||void 0===x?void 0:x.title3}),(0,i.jsx)("span",{className:"seriesStyle",children:null===(g=B[0])||void 0===g?void 0:g.hardseriesStyle})]})]}):(0,i.jsxs)("span",{className:"add",children:[(0,i.jsx)(j.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),v("添加硬装风格")]})})]}),(0,i.jsx)("div",{className:b.materialInfo,children:R.map((function(n){return(0,i.jsxs)("div",{className:"itemInfo",children:[(0,i.jsxs)("div",{className:"header",children:[(0,i.jsxs)("span",{className:"title",onClick:function(){return ln(n.label)},children:[n.label,en[n.label]]}),en[n.label]?(0,i.jsx)(j.A,{type:"icon-a-fangxiangxia",style:{fontSize:16,color:"#959598"},onClick:function(){return ln(n.label)}}):(0,i.jsx)(j.A,{type:"icon-a-fangxiangyou",style:{fontSize:16,color:"#959598"},onClick:function(){return ln(n.label)}})]}),en[n.label]&&(0,i.jsx)("div",{className:"itemList",children:n.figureList.map((function(n,e){var t,r,o;return(0,i.jsxs)("div",{className:"item",onClick:function(){var e;q(!0),F.homeStore.setSelectEntity(n.figure_element.furnitureEntity),Q(n.figure_element),(null==n||null===(e=n.figure_element)||void 0===e?void 0:e.haveMatchedMaterial())||I.A.error(v("当前套系缺失".concat(null==n?void 0:n.figure_element.sub_category,"素材，请联系管理员补全"))),(null==n?void 0:n.figure_element.haveMatchedMaterial())&&!(null==n?void 0:n.figure_element.checkIsMatchedSizeSuitable())&&I.A.warning(v("超出目标尺寸范围"))},children:[(null==n?void 0:n.figure_element.haveMatchedMaterial())&&!(null==n?void 0:n.figure_element.checkIsMatchedSizeSuitable())&&(0,i.jsx)(j.A,{className:b.warn_icon,type:"icon-a-tianchongFace"}),!(null==n||null===(t=n.figure_element)||void 0===t?void 0:t.haveMatchedMaterial())&&(0,i.jsx)(j.A,{type:"icon-a-tianchongFace-1",className:"redIcon"}),(0,i.jsx)("img",{src:n.image_path,alt:""}),(0,i.jsx)("div",{children:n.centerTitle}),(0,i.jsx)("div",{style:{color:"#959598",marginTop:"4px"},children:n.bottomTitle}),(null==n||null===(r=n.figure_element)||void 0===r?void 0:r.haveMatchedMaterial())&&(0,i.jsx)(j.A,{className:b.lock_icon,type:(null===(o=n.figure_element)||void 0===o?void 0:o.locked)?"icon-suoding1":"icon-jiesuo1",onClick:function(e){var t;n.room&&(null===(t=n.room)||void 0===t?void 0:t.locked)||n.figure_element&&(n.figure_element.locked=!n.figure_element.locked,_.nb.emit(k.U.RoomMaterialsUpdated,!0),_.nb.instance.update(),e.stopPropagation(),Q(n.figure_element))}})]},e)}))})]},n.label)}))}),on&&(0,i.jsx)("div",{className:b.visible,onClick:function(){an(!1)},children:(0,i.jsx)("div",{className:"".concat(b.serialsInfo," ").concat(on?"show":""),onClick:function(n){return n.stopPropagation()},children:(0,i.jsx)(N.A,{type:X})})}),(0,i.jsxs)("div",{className:"".concat(b.sideVisible," ").concat(W?b.slideIn:b.slideOut),children:[(0,i.jsxs)("div",{className:"sideTopInfo",children:[(0,i.jsx)("div",{children:v("模型位信息")}),(0,i.jsx)("div",{children:(0,i.jsx)(j.A,{type:"icon-icon",style:{color:"#5B5E60"},onClick:function(){q(!1)}})})]}),(0,i.jsx)(A.A,{selectedFigureElement:Z})]})]})}))}}]);