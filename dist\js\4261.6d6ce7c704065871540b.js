"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4261],{7224:function(n,e,t){t.d(e,{z:function(){return W}});var i=t(13274),o=t(85783),r=t(23825);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function c(){var n=a(["\n      width:100%;\n      height:100vh;\n    "]);return c=function(){return n},n}function u(){var n=a(["\n      position: absolute;\n      top: 0px;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n    "]);return u=function(){return n},n}function l(){var n=a(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return l=function(){return n},n}function s(){var n=a(["\n        position: absolute;\n        left:-100px;\n        top: -100px;\n        background-color: #EAEAEB;\n        width : calc(100% + 200px);\n        height : calc(100% + 200px);\n        overflow: hidden;\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          &.canvas_drawing {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 0 0,auto;\n          }\n        }\n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return s=function(){return n},n}function d(){var n=a(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return d=function(){return n},n}function f(){var n=a(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return f=function(){return n},n}function p(){var n=a(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return p=function(){return n},n}function h(){var n=a(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return h=function(){return n},n}var m=(0,t(79874).rU)((function(n){var e=n.css;return{root:e(c()),content:e(u()),canvas3d:e(l()),canvas_pannel:e(s(),(0,r.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:e(d()),backBtn:e(f()),forwardBtn:e(p()),schemeNameSpan:e(h())}})),b=t(15696),v=t(41594),g=t(27347),x=t(98612),y=t(9003),w=t(88934),S=t(78154),C=t(78644),k=t(83657),_=t(99030),D=t(32184);function j(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function M(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,o,r=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(r.push(i.value),!e||r.length!==e);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw o}}return r}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return j(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return j(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var W=function(n){return n.showLight3DViewer="showLight3DViewer",n}({});(0,b.observer)((function(){var n=(0,o.B)().t,e=m().styles,t=M((0,v.useState)(""),2),a=(t[0],t[1]),c=M((0,v.useState)(-2),2),u=c[0],l=c[1],s=(0,y.P)();(0,v.useRef)(null);g.nb.UseApp(x.e.AppName),g.nb.instance&&(g.nb.t=n);var d=function(){g.nb.instance&&(g.nb.instance.bindCanvas(document.getElementById("cad_canvas")),g.nb.instance.update())};return(0,v.useEffect)((function(){g.nb.instance&&(g.nb.instance._is_website_debug=r.iG),window.addEventListener("resize",d),d(),g.nb.instance&&(g.nb.instance.initialized||(g.nb.instance.init(),g.nb.RunCommand(x.f.AiCadMode),g.nb.instance.layout_graph_solver._is_query_server_model_rooms=!1,g.nb.instance.layout_container.drawing_figure_mode=D.qB.Texture,g.nb.instance.prepare().then((function(){})),g.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),g.nb.instance.update()),g.nb.on_M("showLight3DViewer","LightMain",(function(n){n?(l(2),g.nb.emit(_.r.UpdateScene3D,!1)):l(-1)})),g.nb.on(w.U.LayoutSchemeOpened,(function(n){a(n.name),g.nb.emit(k.$T,k.Kw.Default)}))}),[]),(0,i.jsxs)("div",{className:e.root,children:[(0,i.jsx)(k.Ay,{}),(0,i.jsx)(S.A,{}),(0,i.jsxs)("div",{id:"Canvascontent",className:e.content,children:[(0,i.jsx)("div",{className:"3d_container "+e.canvas3d,style:{zIndex:u},children:(0,i.jsx)(C.A,{defaultViewMode:4})}),(0,i.jsx)("div",{id:"body_container",className:e.canvas_pannel,children:(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){s.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){s.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t);s.homeStore.setInitialDistance(i/s.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t)/s.homeStore.initialDistance;i>5?i=5:i<.05&&(i=.05),s.homeStore.setScale(i),g.nb.DispatchEvent(g.n0.scale,i)}},onTouchEnd:function(){s.homeStore.setInitialDistance(null)}})})]})]})}))},23184:function(n,e,t){t.d(e,{A:function(){return Q}});var i=t(13274),o=t(41594);function r(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=r(["\n      display: flex;\n      position: fixed;\n      top: calc(var(--vh, 1vh) * 50);\n      transform: translateY(-50%);\n      right: 12px;\n      flex-direction: column;\n      z-index: 11;\n      padding: 20px 0px;\n      gap: 12px;\n      background-color: #fff;\n      border-radius: 30px;\n      /* position: relative; */\n    "]);return a=function(){return n},n}function c(){var n=r(["\n      background-color: rgba(0, 0, 0, 0.40) !important;\n      backdrop-filter: blur(50px) !important;\n      color: #fff !important;\n      .iconButtonText\n      {\n        color: #fff !important;\n      }\n      .icon\n      {\n        color: #fff !important;\n      }\n    "]);return c=function(){return n},n}function u(){var n=r(["\n      display: flex;\n      position: absolute;\n      bottom: 0px;\n      right: 62px;\n      flex-direction: column;\n      z-index: 11;\n      padding: 20px 0px;\n      gap: 12px;\n      background-color: #fff;\n      border-radius: 30px;\n    "]);return u=function(){return n},n}function l(){var n=r(["\n      position : fixed;\n      top : 40px;\n      left : 40px;\n      right : 40px;\n      bottom : 40px;\n      z-index:101\n    "]);return l=function(){return n},n}function s(){var n=r(["\n      width: 48px;\n      height: auto;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 30px;\n      flex-direction: column;\n      position: relative;\n      /* margin-bottom: 12px; */\n      .iconButtonText\n      {\n        font-size: 12px;\n        color: #282828;\n        margin-top: 4px;\n        width: 24px;\n        text-align: center;\n        word-break: break-all;\n        line-height: 1.2;\n      }\n      .iconLabel {\n        font-size: 13px;\n        position: absolute;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      .divider\n      {\n        margin-top: 12px;\n        width: 100%;\n        height: 1px;\n        background-color: #E0E0E0;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n        width: 48px;\n        /* min-height: 40px; */\n      }\n      \n      @keyframes flashEffect {\n        0% {\n          background-color: rgba(255, 255, 255, 0.7); /* 原始背景色 */\n        }\n        50% {\n          background-color: rgba(200, 200, 255, 0.7); /* 高亮颜色 */\n        }\n        100% {\n          background-color: rgba(255, 255, 255, 0.7); /* 回到原始背景色 */\n        }\n      }\n    "]);return s=function(){return n},n}function d(){var n=r(["\n      font-size: 12px;\n      color: #000;\n      margin-top: 4px;\n    "]);return d=function(){return n},n}function f(){var n=r(["\n      position: fixed;\n      background: #fff;\n      z-index: 20;\n      right: 64px;\n      top: 50vh;\n      transform: translateY(-50%);\n      font-size:15px;\n      padding: 12px;\n      line-height: 28px;\n      border-radius: 8px;\n    "]);return f=function(){return n},n}function p(){var n=r(["\n      position: fixed;\n      background-color: #ffffff;\n      top: 50%;\n      right: 60px;\n      border-radius: 8px;\n      width: 210px;\n      font-size: 13px;\n      .camera_container {\n        padding: 8px;\n        .content {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n          .slider {\n            flex: 1;\n          }\n          .slider-camera {\n            flex: 1;\n            padding-bottom: 5px;\n            .camera-state {\n              display: flex;\n              justify-content: space-between;\n            }\n          }\n        }\n      }\n    "]);return p=function(){return n},n}function h(){var n=r(["\n      position: fixed;\n      background-color: #ffffff;\n      bottom: 100px;\n      right: 60px;\n      width: 300px;\n      border-radius: 10px;\n      z-index: 10;\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 5px;\n      padding: 5px;\n      .viewGrid {\n        border-radius: 10px;\n        overflow: hidden;\n      }\n    "]);return h=function(){return n},n}function m(){var n=r(["\n      background-color: #ffffff;\n      position: fixed;\n      bottom: 100px;\n      right: 60px;\n      width: 300px;\n      border-radius: 10px;\n      z-index: 11;\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 5px;\n      padding: 5px;\n      .loading-item {\n        border-radius: 10px;\n        position:relative;\n        overflow: hidden;\n        display: flex; /* 使用flex布局 */\n        flex-direction: column; /* 垂直排列 */\n        justify-content: center; /* 水平居中 */\n        align-items: center; /* 垂直居中 */\n        gap: 8px;\n        height: 106.9px; /* 保持高度 */\n        width: 142.5px; /* 保持宽度 */\n        span {\n          font-size: 12px;\n          color: #C0C0C0;\n        }\n      }\n    "]);return m=function(){return n},n}function b(){var n=r(["\n      display: flex;\n      gap: 10px;\n      position: fixed;\n      flex-direction: column;\n      top: 40%;\n      right: 65px;\n      .ratioBtn {\n        background-color:rgb(218, 218, 218);\n        height: 30px;\n        width: 45px;\n        border-radius: 5px;\n        font-size: 16px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n      }\n    "]);return b=function(){return n},n}var v=(0,t(79874).rU)((function(n){var e=n.css;return{container:e(a()),blackColor:e(c()),morebtns_container:e(u()),center_container:e(l()),iconButton:e(s()),name:e(d()),checkBoxes:e(f()),camera:e(p()),viewCamera:e(h()),loading:e(m()),ratioContainer:e(b())}})),g=t(27347),x=t(88934),y=t(15696),w=t(9003),S=t(98612),C=t(67869),k=t(23664),_=t(11180),D=t(86014),j=t(33100),M=t(68413),W=t(54282),E=t(70766);function O(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function z(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function A(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){z(n,e,t[e])}))}return n}function P(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,o,r=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(r.push(i.value),!e||r.length!==e);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw o}}return r}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return O(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return O(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var T=function(n){var e,t,r,a,c,u,l,s,d,f,p,h,m,b,y,C,k=n.isVisible,_=v().styles,D=g.nb.t,j=(0,w.P)(),M=P((0,o.useState)((y={},(null==(C=g.nb.instance)?void 0:C.drawing_layers)?(y[W.uW.CadEzdxfDrawing]=null===(e=C.drawing_layers[W.uW.CadEzdxfDrawing])||void 0===e?void 0:e.visible,y[W.uW.CadRoomStrucure]=null===(t=C.drawing_layers[W.uW.CadRoomStrucure])||void 0===t?void 0:t.visible,y[W.uW.CadFurniture]=null===(r=C.drawing_layers[W.uW.CadFurniture])||void 0===r?void 0:r.visible,y[W.uW.CadCabinet]=null===(a=C.drawing_layers[W.uW.CadCabinet])||void 0===a?void 0:a.visible,y[W.uW.CadOutLine]=null===(c=C.drawing_layers[W.uW.CadOutLine])||void 0===c?void 0:c.visible,y[W.uW.CadLighting]=null===(u=C.drawing_layers[W.uW.CadLighting])||void 0===u?void 0:u.visible,y[W.uW.CadCeiling]=null===(l=C.drawing_layers[W.uW.CadCeiling])||void 0===l?void 0:l.visible,y[W.uW.CadDecorates]=null===(s=C.drawing_layers[W.uW.CadDecorates])||void 0===s?void 0:s.visible,y[W.uW.CadSubRoomAreaDrawing]=null===(d=C.drawing_layers[W.uW.CadSubRoomAreaDrawing])||void 0===d?void 0:d.visible,y[W.uW.CadDimensionWallElement]=null===(f=C.drawing_layers[W.uW.CadDimensionWallElement])||void 0===f?void 0:f.visible,y[W.uW.CadDimensionOutterWallElement]=null===(p=C.drawing_layers[W.uW.CadDimensionOutterWallElement])||void 0===p?void 0:p.visible,y[W.uW.RulerDrawing]=null===(h=C.drawing_layers[W.uW.RulerDrawing])||void 0===h?void 0:h.visible,y[W.uW.CadRoomName]=null===(m=C.drawing_layers[W.uW.CadRoomName])||void 0===m?void 0:m.visible,y.LockEzdxf=(null===(b=C.drawing_layers.LockEzdxf)||void 0===b?void 0:b.visible)||!1,y):(y[W.uW.CadEzdxfDrawing]=!1,y[W.uW.CadRoomStrucure]=!0,y[W.uW.CadFurniture]=!0,y[W.uW.CadCabinet]=!0,y[W.uW.CadOutLine]=!0,y[W.uW.CadLighting]=!1,y[W.uW.CadCeiling]=!1,y[W.uW.CadDecorates]=!1,y[W.uW.CadSubRoomAreaDrawing]=!!g.nb.IsDebug,y[W.uW.CadDimensionWallElement]=!1,y[W.uW.CadDimensionOutterWallElement]=!1,y[W.uW.RulerDrawing]=!0,y[W.uW.CadRoomName]=!0,y.LockEzdxf=!1,y))),2),O=M[0],z=M[1],T=[];T=(T=j.homeStore.designMode===S.f.HouseDesignMode?[{id:W.uW.CadRoomName,title:D("空间名称"),titleCn:"空间名称",type:"checkbox",checked:O[W.uW.CadRoomName]},{id:W.uW.CadDimensionWallElement,title:D("内墙标注"),titleCn:"内墙标注",type:"checkbox",checked:O[W.uW.CadDimensionWallElement]},{id:W.uW.CadDimensionOutterWallElement,title:D("外墙标注"),titleCn:"外墙标注",type:"checkbox",checked:O[W.uW.CadDimensionOutterWallElement]},{id:W.uW.CadEzdxfDrawing,title:D("显示临摹图"),titleCn:"显示临摹图",type:"checkbox",checked:O[W.uW.CadEzdxfDrawing]},{id:"LockEzdxf",title:D("锁定临摹图"),titleCn:"锁定临摹图",type:"checkbox",checked:O.LockEzdxf}]:[{id:W.uW.CadRoomStrucure,title:D("墙体结构"),titleCn:"墙体结构",type:"checkbox",checked:O[W.uW.CadRoomStrucure]},{id:W.uW.CadFurniture,title:D("家具"),titleCn:"家具",type:"checkbox",checked:O[W.uW.CadFurniture]},{id:W.uW.CadCabinet,title:D("定制柜"),titleCn:"定制柜",type:"checkbox",checked:O[W.uW.CadCabinet]},{id:W.uW.CadSubRoomAreaDrawing,title:D("区域"),titleCn:"区域",type:"checkbox",checked:O[W.uW.CadSubRoomAreaDrawing]},{id:W.uW.CadCeiling,title:D("吊顶"),titleCn:"吊顶",type:"checkbox",checked:O[W.uW.CadCeiling]},{id:W.uW.CadRoomName,title:D("空间名称"),titleCn:"空间名称",type:"checkbox",checked:O[W.uW.CadRoomName]},{id:W.uW.RulerDrawing,title:D("量尺"),titleCn:"量尺",type:"checkbox",checked:O[W.uW.RulerDrawing]}]).filter((function(n){return n}));return(0,o.useEffect)((function(){return g.nb.on_M(x.U.SwitchDrawingLayer,"display-check-box",(function(n){var e=A({},O,n);z(e)})),function(){g.nb.off_M(x.U.SwitchDrawingLayer,"display-check-box")}}),[]),(0,i.jsx)("div",{className:_.checkBoxes,style:{display:k?"block":"none"},children:T.map((function(n,e){return(0,i.jsx)("div",{children:(0,i.jsx)(E.A,{checked:O[n.id],onChange:function(e){!function(n){if(void 0!==O[n.id]){var e=A({},O);e[n.id]=!e[n.id],g.nb.DispatchEvent(g.n0.HandleSwitchDrawingLayer,e)}}(n)},children:n.title})},"display_check_"+e)}))})},H=t(17365),N=t(49063),I=t(10371),R=t(21491),L=t(23825),B=t(46396),U=t(43417),F=t(57189),V=t(65640);function G(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function q(n,e,t,i,o,r,a){try{var c=n[r](a),u=c.value}catch(n){return void t(n)}c.done?e(u):Promise.resolve(u).then(i,o)}function Y(n){return function(){var e=this,t=arguments;return new Promise((function(i,o){var r=n.apply(e,t);function a(n){q(r,i,o,a,c,"next",n)}function c(n){q(r,i,o,a,c,"throw",n)}a(void 0)}))}}function $(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function X(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){$(n,e,t[e])}))}return n}function K(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function Z(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,o,r=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(r.push(i.value),!e||r.length!==e);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw o}}return r}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return G(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return G(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(n,e){var t,i,o,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(u){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(r=0)),r;)try{if(t=1,i&&(o=2&c[0]?i.return:c[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,c[1])).done)return o;switch(i=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,i=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(!(o=r.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){r=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){r.label=c[1];break}if(6===c[0]&&r.label<o[1]){r.label=o[1],o=c;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(c);break}o[2]&&r.ops.pop(),r.trys.pop();continue}c=e.call(n,r)}catch(n){c=[6,n],i=0}finally{t=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}var Q=(0,y.observer)((function(n){var e=n.setSceneMode,t=v().styles,r=(0,w.P)(),a=r.homeStore,c=a.setShowDreamerPopup,u=a.setIsdrawPicture,l=g.nb.instance.scene3D,s=g.nb.instance.layout_container,d=Z(j.A.useMessage(),2),f=d[0],p=d[1],h=g.nb.t,m=Z((0,o.useState)(null),2),b=m[0],y=m[1],W=((0,N.Zp)(),Z((0,o.useState)(!1),2)),E=W[0],O=W[1],z=[{iconType:"icon-chexiao",onClick:function(){g.nb.RunCommand(g._I.Undo)},name:h("撤销"),isHidden:"2D"!==r.homeStore.viewMode},{iconType:"icon-huifu",onClick:function(){g.nb.RunCommand(g._I.Redo)},name:h("恢复"),isHidden:"2D"!==r.homeStore.viewMode,divider:!0},{iconType:"icon-change_logo",onClick:Y((function(){return J(this,(function(n){return"3D"==r.homeStore.viewMode?e("3D_FirstPerson"):e("3D"),[2]}))})),name:"3D"==r.homeStore.viewMode?h("漫游"):h("鸟瞰"),isHidden:"2D"==r.homeStore.viewMode||r.homeStore.isdrawPicture&&!r.userStore.isHaiEr},{iconType:"icon-save",onClick:function(){0==s._room_entities.length?j.A.error(h("当前方案为空，无法保存！")):null==s._layout_scheme_id?r.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"}):g.nb.DispatchEvent(g.n0.SaveLayoutScheme,null)},name:h("保存"),isHidden:r.homeStore.isdrawPicture&&!r.userStore.isHaiEr},{iconType:"icon-tuku",onClick:Y((function(){return J(this,(function(n){switch(n.label){case 0:return[4,r.homeStore.query_genCount()];case 1:return n.sent(),0==s._room_entities.length?j.A.warning(h("请先创建方案")):r.homeStore.setShowAtlas(!0),[2]}}))})),name:h("图册"),id:"renderingTukuBtnId",isHidden:r.homeStore.designMode===S.f.HouseDesignMode},{iconType:"icon-xiangjishezhi",onClick:function(){r.homeStore.setShowSubmitInfo(!0)},name:h("相机"),isHidden:"2D"===r.homeStore.viewMode||!(r.homeStore.isdrawPicture||r.userStore.isHaiEr),isChecked:!0},{iconType:"icon-lishibanben",onClick:function(){g.nb.emit(x.U.setMultiSchemeListVisible,!0)},name:h("多方案"),isHidden:!0},{iconType:"icondisplay",onClick:function(){},name:h("显隐"),isHidden:"2D"!==r.homeStore.viewMode},{iconType:"iconhuizhong",onClick:function(){if("2D"===r.homeStore.viewMode)window.innerWidth<.8*window.innerHeight?H.f.focusCenterByWholeBox(s,.7):H.f.focusCenterByWholeBox(s,.5),g.nb.instance.update();else{s.updateWholeBox();var n=s._whole_bbox.getCenter(new C.Pq0);l.setCenter(n)}},name:h("居中"),isHidden:"2D"!==r.homeStore.viewMode},{iconType:"icon-baojia",onClick:Y((function(){var n,e,t,i,o,r,a;return J(this,(function(c){switch(c.label){case 0:return{},e=g.nb.instance,t=k.K.instance,i={seriesKgId:null,ruleId:null,seedSchemeId:null,ruleName:null,seedSchemeName:null,roomName:null,seriesName:null,status:null,thumbnail:null,roomList:null,ruleImageList:null,layoutTemplates:null},(o=t.makeQuoteData(e.layout_container,new _._(i)))?(n={data:JSON.stringify(o)},[4,(0,D.fS)(n)]):[3,2];case 1:(r=c.sent()).success&&(j.A.success(h("报价成功")),(a=document.createElement("a")).href=r.data,document.body.appendChild(a),a.click(),document.body.removeChild(a)),c.label=2;case 2:return[2]}}))})),name:h("装修"),isHidden:!0},{iconType:"icon-baojia",onClick:Y((function(){return J(this,(function(n){return r.homeStore.setShowCabinetCompute(!0),[2]}))})),name:h("算量"),isHidden:"2D"!==r.homeStore.viewMode||r.homeStore.designMode===S.f.HouseDesignMode},{iconType:"icon-search",onClick:Y((function(){return J(this,(function(n){return c(!0),[2]}))})),name:h("找相似"),isHidden:"2D"!==r.homeStore.viewMode||!L.um},{iconType:(null==l?void 0:l.outlineMaterialMode)==I.Gf.WhiteModelOutline?"iconShowmaterial_Nor":"iconShowoutline_Nor",name:(null==l?void 0:l.outlineMaterialMode)==I.Gf.WhiteModelOutline?h("材质"):h("轮廓"),onClick:function(){(null==l?void 0:l.outlineMaterialMode)==I.Gf.WhiteModelOutline?(l.outlineMaterialMode=I.Gf.MaterialOnly,G((function(n){return n.map((function(n){return n.name===g.nb.t("材质")?K(X({},n),{name:g.nb.t("轮廓"),iconType:"iconShowoutline_Nor"}):n}))}))):(l.outlineMaterialMode=I.Gf.WhiteModelOutline,G((function(n){return n.map((function(n){return n.name===g.nb.t("轮廓")?K(X({},n),{name:g.nb.t("材质"),iconType:"iconShowmaterial_Nor"}):n}))}))),f.open({type:null,content:(null==l?void 0:l.outlineMaterialMode)==I.Gf.WhiteModelOutline?h("显示轮廓"):h("显示材质"),className:"custom-class"})},isHidden:"2D"===r.homeStore.viewMode,isChecked:!0},{iconType:g.nb.instance.Configs.isClickDrawPic?"icon-chanpinzhiru":"icon-maikefeng1",onClick:function(){g.nb.instance.Configs.isClickDrawPic=!g.nb.instance.Configs.isClickDrawPic,g.nb.emit_M(x.U.FigureElementSelected,null),g.nb.instance.scene3D.setSelectionBox(null),f.open({type:null,content:g.nb.instance.Configs.isClickDrawPic?h("进入演讲模式"):h("进入换搭模式"),className:"custom-class"})},name:g.nb.instance.Configs.isClickDrawPic?h("换搭"):h("演讲"),isHidden:"3D_FirstPerson"!==r.homeStore.viewMode||r.homeStore.isdrawPicture&&!r.userStore.isHaiEr},{iconType:"icon-Frame",onClick:function(){g.nb.DispatchEvent(g.n0.autoSave,null),u(!0),g.nb.instance.Configs.isClickDrawPic=!0,l.raycasteControls.onSelectedFigure(null)},name:h("出图"),isHidden:r.userStore.isHaiEr||"3D_FirstPerson"!==r.homeStore.viewMode||r.homeStore.isdrawPicture,isChecked:!0},{iconType:"icon-fenxiang",onClick:Y((function(){return J(this,(function(n){return O(!E),[2]}))})),name:h("分享"),isHidden:r.homeStore.isdrawPicture&&!r.userStore.isHaiEr||r.homeStore.designMode===S.f.HouseDesignMode},{iconType:"iconmore",onClick:function(){},name:null,isHidden:"2D"!==r.homeStore.viewMode,isChecked:!0},{iconType:"icon-icon",onClick:function(){u(!1),g.nb.instance.Configs.isClickDrawPic=!1,g.nb.instance.renderSubmitObject={drawPictureMode:null,radioMode:0,resolution:0},l.setLightMode(I.Ei.Day),F.p.instance.cleanLighting(),g.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1),U.Y.cleanLight()},name:h("取消"),isHidden:!r.homeStore.isdrawPicture||r.userStore.isHaiEr,isChecked:!0}],A=Z((0,o.useState)(),2),P=A[0],G=A[1];(0,o.useEffect)((function(){g.nb.on_M(x.U.Scene3DUpdated,"Statusbars",(function(){g.nb.instance.layout_container;l=g.nb.instance.scene3D}))}),[]),(0,o.useEffect)((function(){var n=function(n){null!==n.target.closest("#ipad-sideToolbar")||y(null)};return document.addEventListener("mousedown",n),function(){document.removeEventListener("mousedown",n)}}),[]);var q=[{iconType:"iconfile",onClick:function(){r.homeStore.setShowEnterPage({show:!0,source:"sideToolbar"})},name:h("新建"),isHidden:r.userStore.isHaiEr},{iconType:"iconbuzhisucai",onClick:function(){r.homeStore.setShowMySchemeList(!0)},name:h("方案")},{iconType:"icona-zaoxingbianjishapeediting",onClick:function(){g.nb.instance._current_handler_mode=S.f.HouseDesignMode,g.nb.RunCommand(S.f.HouseDesignMode),r.homeStore.setDesignMode(S.f.HouseDesignMode)},name:h("户型编辑"),isHidden:r.userStore.isHaiEr||r.homeStore.designMode===S.f.HouseDesignMode&&r.homeStore.isSingleRoom}];q=q.filter((function(n){return n&&!n.isHidden})),(0,o.useEffect)((function(){G(z.filter((function(n){return null!==n&&!n.isHidden}))),V.log("store.homeStore.viewMode",r.homeStore.viewMode)}),[r.homeStore.viewMode,r.homeStore.drawPictureMode,r.homeStore.isdrawPicture,g.nb.instance.Configs.isClickDrawPic]);var $="2D"===r.homeStore.viewMode,Q=Z((0,o.useState)(0),2),nn=Q[0],en=Q[1];return(0,o.useEffect)((function(){en(r.homeStore.genCount)}),[r.homeStore.genCount]),(0,i.jsxs)("div",{id:"ipad-sideToolbar",children:[(0,i.jsxs)("div",{className:"".concat(t.container," ").concat("2D"!==r.homeStore.viewMode?t.blackColor:""),children:[P&&P.map((function(n,e){return(0,i.jsxs)("div",{className:"".concat(t.iconButton," ").concat(b===n.iconType?n.isChecked?"checked":"notChecked":""),onClick:function(){return e=n.iconType,t=n.onClick,y(b===e?null:e),void t();var e,t},id:n.id,children:["图册"===n.name?(0,i.jsx)(M.A,{count:null===nn?"?":nn,size:"small",children:(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==r.homeStore.viewMode?"#fff":"#595959",children:(0,i.jsx)("use",{xlinkHref:"#".concat(n.iconType)})})}):(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==r.homeStore.viewMode?"#fff":"#595959",children:(0,i.jsx)("use",{xlinkHref:"#".concat(n.iconType)})}),(0,i.jsx)(B.If,{condition:n.name,children:(0,i.jsx)("div",{className:"iconButtonText",children:n.name})}),(0,i.jsx)(B.If,{condition:n.divider,children:(0,i.jsx)("span",{className:"divider"})})]},e)})),"iconmore"===b&&(0,i.jsx)("div",{className:t.morebtns_container,children:q.map((function(n,e){return(0,i.jsxs)("div",{className:t.iconButton,onClick:function(){y(null),n.onClick()},id:n.id,children:[(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==r.homeStore.viewMode?"#fff":"#595959",children:(0,i.jsx)("use",{xlinkHref:"#".concat(n.iconType)})}),(0,i.jsx)("div",{className:"iconButtonText",children:n.name}),(0,i.jsx)(B.If,{condition:n.divider,children:(0,i.jsx)("span",{className:"divider"})})]},e)}))})]}),(0,i.jsx)(T,{isVisible:"icondisplay"===b&&$}),E&&(0,i.jsx)("div",{className:"",style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:"999",background:"#fff",padding:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)",borderRadius:"4px"},children:(0,i.jsx)(R.A,{onClose:function(){O(!1)}})}),p]})}))},83657:function(n,e,t){t.d(e,{$T:function(){return H},Kw:function(){return T},Ay:function(){return N}});var i=t(13274),o=t(85783),r=t(79874);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function c(){var n=a(["\n      /* position: fixed;\n      top: 0px;\n      background-color: #fff;\n      width: 100%;\n      height: 56px;\n\n      display: flex;\n      padding: 0 16px;\n      justify-content: space-between;\n      align-items: center;\n      z-index: 9;\n      max-width: 1174px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 46px;\n      } */\n    "]);return c=function(){return n},n}function u(){var n=a(["\n      position: fixed;\n      top: 12px;\n      left: 12px;\n      color: #282828;\n      font-size: 16px;\n      font-weight: 600;\n      @media screen and (max-width: 450px) { // 手机宽度\n        font-size: 12px;\n      }\n      z-index: 9;\n    "]);return u=function(){return n},n}function l(){var n=a(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:40px;\n      color:#333;\n    "]);return l=function(){return n},n}function s(){var n=a(["\n      width:100%;\n      font-size:16px;\n      line-height:40px;\n      text-align:center;\n    "]);return s=function(){return n},n}var d=(0,r.rU)((function(n){var e=n.css;return{navigation:e(c()),backBtn:e(u()),forwardBtn:e(l()),schemeNameSpan:e(s())}})),f=t(15696),p=t(41594),h=t(27347);function m(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function b(){var n=m(["\n      width: 180px;\n      position: fixed;\n      right: 12px;\n      top: 12px;\n      z-index: 9;\n      .ant-segmented\n      {\n        /* background-color: #EAEBEA; */\n        /* color: #282828 !important; */\n        @media screen and (max-width: 450px) {\n          height: 28px;\n        }\n      }\n      .ant-segmented-item-label\n      {\n        @media screen and (max-width: 450px) {\n          height: 28px;\n          line-height: 28px !important;\n          font-size: 12px !important;\n        }\n      }\n    "]);return b=function(){return n},n}function v(){var n=m(["\n      right: 50px;\n    "]);return v=function(){return n},n}function g(){var n=m(["\n      width: 50px;\n      text-align:center;\n      line-height:40px;\n      font-size:16px;\n      color : #777;\n      background-color: #fff;\n      border: 1px solid #fff;\n      &.active {\n        background: #147FFA;\n        color: #fff;\n      }\n\n    "]);return g=function(){return n},n}var x=(0,r.rU)((function(n){var e=n.css;return{root:e(b()),mobile_root:e(v()),state_btn:e(g())}})),y=t(7224),w=t(7991),S=t(9003),C=t(88934),k=t(67869),_=t(10371);function D(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function j(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,o,r=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(r.push(i.value),!e||r.length!==e);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw o}}return r}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return D(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return D(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var M=(0,f.observer)((function(){var n=(0,o.B)().t,e=x().styles,t=(0,S.P)(),r=j((0,p.useState)(t.homeStore.viewMode||"2D"),2),a=r[0],c=r[1],u=h.nb.instance.layout_container,l=h.nb.instance.scene3D;(0,p.useEffect)((function(){if(h.nb.emit_M(y.z.showLight3DViewer,!0),"2D"===a)l&&l.stopRender(),h.nb.emit_M(y.z.showLight3DViewer,!1),t.homeStore.setViewMode("2D");else if("3D"===a)h.nb.emit_M(y.z.showLight3DViewer,!0),l.setCemeraMode(_.I5.Perspective),t.homeStore.setViewMode("3D"),"SingleRoom"===u._drawing_layer_mode&&h.nb.DispatchEvent(h.n0.leaveSingleRoomLayout,{}),l&&l.startRender();else if("3D_FirstPerson"===a){l.setCemeraMode(_.I5.FirstPerson);var n,e,i,o=t.homeStore.roomEntities.reduce((function(n,e){return n?e._area>n._area?e:n:e}),null);if(o)l.setCenter((null==o||null===(n=o._main_rect)||void 0===n?void 0:n.rect_center)||new k.Pq0(0,0,0)),l.update();else l.setCenter((null===(i=t.homeStore.roomEntities[0])||void 0===i||null===(e=i._main_rect)||void 0===e?void 0:e.rect_center)||new k.Pq0(0,0,0));t.homeStore.setViewMode(a),l&&l.startRender()}h.nb.emit_M(C.U.Scene3DUpdated,!0)}),[a]);var s=[{value:"2D",label:n("2D")},{value:"3D_FirstPerson",label:n("漫游")},{value:"3D",label:n("鸟瞰")}];return(0,i.jsx)("div",{className:e.root,children:(0,i.jsx)(w.A,{value:a,onChange:function(n){c(n)},block:!0,size:"middle",options:s})})})),W=t(33100),E=t(23825),O=t(57235),z=t(76330);function A(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function P(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,o,r=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(r.push(i.value),!e||r.length!==e);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw o}}return r}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return A(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return A(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var T=function(n){return n.Default="Default",n.HouseSearch="HouseSearch",n.HuaweiDemo="HuaweiDemo",n}({}),H="NavigationEvent",N=(0,f.observer)((function(n){var e=(0,o.B)().t,t=d().styles,r=h.nb.instance.layout_container,a=P((0,p.useState)(!0),2),c=(a[0],a[1]),u=P((0,p.useState)(!0),2),l=(u[0],u[1]);return(0,p.useEffect)((function(){h.nb.instance&&(h.nb.instance.updateSlot("TopMenu_RedoableSlot",{ui_name:"RedoDisabled",target:h.nb.instance,callback:function(n){c(!n)}}),h.nb.instance.updateSlot("TopMenu_UndoableSlot",{ui_name:"UndoDisabled",target:h.nb.instance,callback:function(n){l(!n)}})),h.nb.instance.connect_obj(O.n.signalRedoable,h.nb.instance,"TopMenu_RedoableSlot"),h.nb.instance.connect_obj(O.n.signalUndoable,h.nb.instance,"TopMenu_UndoableSlot"),h.nb.on(H,(function(n){n||(n="Default")}))}),[]),(0,i.jsxs)("div",{className:t.navigation,children:[!E.Ic&&(0,i.jsx)("div",{className:t.backBtn,onClick:function(){0==r._room_entities.length?window.location.href=E.O9:(h.nb.DispatchEvent(h.n0.autoSave,null),W.A.loading(e("保存中...")),setTimeout((function(){W.A.destroy(),window.location.href=E.O9}),1500))},children:(0,i.jsx)(z.A,{type:"icon-zhuye",style:{fontSize:"18px",marginRight:"4px"}})}),(0,i.jsx)(M,{})]})}))}}]);