import { test } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import { clickToSave, matchSets } from '../utils/basicProcess';
import { takeScreenshot } from '../utils/screenshotUtils';


test.describe('分享功能测试', async () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('分享功能测试', async ({ page }) => {
    await matchSets(page, designPage);

    // 4.点击顶菜保存按钮保存方案
    await clickToSave(page, designPage);
    // 5.点击顶部分享按钮，复制分享链接

    const shareButton = page.locator('#swj-top-menu-id-shareBtn');
    await shareButton.waitFor({ state: 'visible', timeout: 5000 });
    await shareButton.click();

    // 点击“复制链接”
    // 定位策略————逐步精确，只要样式类名不改，这个定位器基本稳定
    const copyLinkButton = page.locator('button.ant-btn-variant-solid').getByText('复制链接');
    await copyLinkButton.waitFor({ state: 'visible', timeout: 5000 });
    await copyLinkButton.click();

    try {
      // 捕获复制成功的轻提示
      const copySuccessToast = page.getByText('链接复制成功，快去分享吧~');
      await copySuccessToast.waitFor({ state: 'visible', timeout: 5000 });
      await copySuccessToast.click();
    } catch (e) {
      console.info('没有捕获到链接复制成功的轻提示');
    }

    await page.waitForTimeout(3000);
    await takeScreenshot(page, 'share', 'share.png');
  });
});
