import { EzdxfEvents, EzdxfSelectMode } from '@/Apps/AI2Design/Handlers/SubHandlers/EzdxfSubHandlers/EzdxfSubHandler';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { useStore } from '@/models';
import { Button, Input, Select } from '@svg/antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from "react-router-dom";
import useStyles from './style/index';
import { I_TopBarItem } from '@/Apps/AI2Design/Handlers/UiTemplates/I_TopBar/I_TopBarItem';
import { EventName } from '@/Apps/EventSystem';

const TopBarSelectItem : React.FC<{item:I_TopBarItem}> = (props:{item:I_TopBarItem})=>{
  const { styles } = useStyles();
  const [inputValue, setInputValue] = useState(props.item.defaultValue||"");

  const inputChange = (e: any) => {
    setInputValue(e.target.value);
  }
  const selectChange = (item: string) => {
    setInputValue(item);
  }

  useEffect(()=>{
    if(props.item.onChange)
    {
      props.item.onChange(inputValue);
    }
  },[inputValue])
  const options = props.item.options || [];
  const selectAfter = (
    <Select popupClassName={styles.popupClass} placement={'bottomRight'} defaultValue={inputValue} onChange={selectChange}>
      {options.map((t_data, index) => <option key={"s_t" + index} value={t_data.value}>{t_data.label}</option>)}
    </Select>
  )
  return <>
        <span className={styles.label}>{props.item.title}</span>
        <Input
          value={inputValue}
          size={'small'}
          style={{ width: 100 }}
          addonAfter={selectAfter}
          onChange={inputChange}
          defaultValue="" />
  </>
}


/**
 * @description 墙体的顶部菜单
 */
const HotelLayoutTopBar: React.FC = () => {
    const { styles } = useStyles();
    const navigate = useNavigate();
    const { t } = useTranslation()
    const [isVisible,setIsVisible] = useState<boolean>(false);
    const [topBarItems,setTopBarItems] = useState<I_TopBarItem[]>([]);
    let store = useStore();
    const { Option } = Select;


    useEffect(() => {
        LayoutAI_App.on(EventName.TopBarItemChanged, (props:{visible:boolean, topBarItems:I_TopBarItem[]})=>{
          setIsVisible(props.visible);

          if(props.visible)
          {
              setTopBarItems(props.topBarItems);
          }
          else{
            setTopBarItems([]);
          }

        })
    }, []);
    if(!isVisible)
    {
        return <></>;
    }
    return (
      <div id="HotelTopBar" className={styles.top_container}>
        {topBarItems.map((item,index)=>{
          if(item.type === "select")
          {
            return <TopBarSelectItem item={item} key={"TopBarItem"+index}></TopBarSelectItem>
          }
          else if(item.type==="button")
          {
            return <button className={styles.button} key={"TopBarItem"+index} id={"btn"+item.id} onClick={item.onClick || null}>{item.title}</button>
          }
          else{
            return <></>
          }
        }
        )}
      </div>
    );
  };
  
  export default HotelLayoutTopBar;
  