
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
return {
    PageContainer: css`
    display: flex;
    flex-direction: row-reverse;
    background-color: #fff;
    position: sticky;
    bottom: 0;
    align-items: center;
    background-color: #f6f7f9;
    padding-top: 7px;
    `,
    root: css`
    width: 100%;
    height: 100vh;
    border-radius: 12px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: #f6f7f9;
    position: fixed;
    z-index: 999;
    padding-top: 15px;
    ::-webkit-scrollbar-thumb
    {
        display: none;
    }
    .back {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        background: #E9EBEB;
        padding: 4px;
        margin-right: 8px;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }
    .atlas_header{
        display:flex;
        justify-content: space-between;

        .segmented{

        }

        .back_button{
            display:flex;
            align-items: center;
            margin-right: 20px;
            height: 30px;
            width: 74px;
            border-radius: 8px;
            background: #FFFFFF;
            border: 1px solid #00000026;
            margin-bottom: 10px;
            cursor: pointer;
            span{
            color: #282828;
            font-family: PingFang SC;
            font-weight: normal;
            font-size: 14px;
            line-height: 1.57;
            letter-spacing: 0px;
            text-align: left;
            }
        }
        }
    `,
    noData: css`
    width:100%;
    height: 100%;
    padding-right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #959598;
    `,
    content: css`
    display: grid;
    gap: 20px;
    flex: 1;
    overflow-y: auto;

    /* 隐藏滚动条 - Webkit浏览器 */
    &::-webkit-scrollbar {
        display: none;
    }
    
    /* 隐藏滚动条 - Firefox */
    scrollbar-width: none;
    
    /* 隐藏滚动条 - IE */
    -ms-overflow-style: none;

    // grid-template-rows: repeat(auto-fill, 200px);

    @media screen and (min-width: 1400px) {
        grid-template-columns: repeat(5, calc(20% - 20px));
    }
    
    @media screen and (max-width: 1400px) and (min-width: 960px) {
        grid-template-columns: repeat(4, calc(25% - 20px));
    }
    
    @media screen and (max-width: 960px) and (min-width: 560px) {
        grid-template-columns: repeat(3, calc(33.33% - 20px));
    }
    @media screen and (max-width: 560px) and (min-width: 320px) {
        grid-template-columns: repeat(2, calc(50% - 20px));
    }
    @media screen and (max-width: 320px) {
        grid-template-columns: repeat(1, 100%);
    }

    &::-webkit-scrollbar {
        width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #00000026;
        border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
        background: transparent;
    }
    `,
    item: css`
    border: 1px solid transparent;
    // margin-bottom: 20px;
    .main_img_container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        // height: 181px;
        aspect-ratio: 4 / 3;
        border-radius: 4px;
        border: none;
        background: #F5F5F5;
        position: relative;
        overflow: hidden;
        .ant-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        }
        .ant-image-mask{
        opacity: 0;
        }
        img {
        max-width: 100%;
        max-height: 100%;
        border-radius: 4px;
        object-fit: contain;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        }

        .number_tag {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 30px;
            height: 30px;
            border-radius: 4px;
            background: #0000007F;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FFFFFF;
            font-size: 14px;
            z-index: 1;
        }
    }

    .main_loading_container{
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 100%;
        // height: 181px;
        aspect-ratio: 4 / 3;
        border-radius: 8px;
        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);
        position: relative;

        &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 8px;
        padding: 1px;
        background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);
        mask: linear-gradient(#fff 0 0) content-box, 
                linear-gradient(#fff 0 0);
        mask-composite: exclude;
        -webkit-mask: linear-gradient(#fff 0 0) content-box, 
                    linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        pointer-events: none;
        }
        
        span{
        color: #959598;
        font-family: PingFang SC;
        font-weight: normal;
        font-size: 12px;
        line-height: 1.67;
        letter-spacing: 0px;
        text-align: left;
        margin-top: 4px;
        }
    }

    .info_content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 22px;
        margin: 8px 0;
        padding: 0 8px;
        
        .name{
        color: #282828;
        font-family: PingFang SC;
        font-weight: normal;
        font-size: 14px;
        line-height: 1.57;
        letter-spacing: 0px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 180px;
        
        @media screen and (max-width: 1400px) and (min-width: 960px) {
            max-width: 150px;
        }
        
        @media screen and (max-width: 960px) and (min-width: 560px) {
            max-width: 120px;
        }
        @media screen and (max-width: 560px) {
            max-width: 80px;
        }
        }
        
        .time {
        color: #959598;
        font-family: PingFang SC;
        font-weight: normal;
        font-size: 12px;
        line-height: 1.67;
        letter-spacing: 0px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        // text-overflow: ellipsis;
        max-width: 80px;
        }
    }`,
    display: css`
        padding-top: 20px;
    `,
}

});
