import { Vector3 } from "three";
import { TAppManagerBase } from "../../AppManagerBase";
import { TRoomEntity } from "../Layout/TLayoutEntities/TRoomEntity";
import { ZPolygon } from "@layoutai/z_polygon";
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";

export class TCadOutLineLayer extends TDrawingLayer {



    _target_poly: ZPolygon;

    _name_mode: number = 0;

    constructor(manager: TAppManagerBase) {
        super(CadDrawingLayerType.CadRoomStrucure, manager);
        this._target_poly = null;
        this._dirty = true;

    }

    get room_list() {
        return this.layout_container._rooms;
    }
    _updateLayerContent(): void {

    }

    onDraw(): void {
        if (this.layout_container._drawing_layer_mode === 'SingleRoom') return;
        // 绘制房间
        for (let room of this.room_list) {
            this.painter.fillStyle = "#000"
            if (!room.selectable) {
                this.painter.fillStyle = "#ccc";
                let poly = room.room_shape._poly;
                this.painter.fillPolygon(poly, 1.0);
                this.painter.fillStyle = "#6c7175";
            }
        }

        // 绘制墙体分割线
        this.drawWallSplitLines();

        // 原有的候选矩形绘制代码
        let entities = this.layout_container.getCandidateEntities(["RoomArea"]) as TRoomEntity[];


        for (let entity of entities) {

            if (entity?.rect && TBaseEntity.is_deleted(entity?.rect)) continue;
            if (entity) {
                entity.drawEntity(this.painter, { is_draw_figure: true, is_show_room_id: this._name_mode == 1 });
                if (entity._room && entity._room.locked) {
                    if (entity._main_rect) {
                        let center = entity._main_rect.rect_center;
                        center = center.clone().add({ x: 900, y: 0, z: 0 });
                        this.painter._context.fillStyle = "#b561f0";
                        this.painter.drawText("\ue7f4", center, 0, 90, 10, true, true);

                    }
                }
                if (entity._room && entity._room.layoutLock) {
                    if (entity._main_rect) {
                        let center = entity._main_rect.rect_center;
                        center = center.clone().add({ x: 500, y: 0, z: 0 });
                        this.painter._context.fillStyle = "#b561f0";
                        this.painter.drawText("\ue7f8", center, 0, 90, 10, true, true);
                    }
                }

            }


        }
        this.painter.strokeStyle = "#a42";
        this.painter.fillStyle = "#fff";
    }

    // 绘制墙体分割线
    private drawWallSplitLines(): void {
        let walls = this.layout_container.getCandidateEntities(["Wall"]);
        for (let i = 0; i < walls.length; i++) {
            let wall1 = walls[i].rect;
            if (TBaseEntity.is_deleted(wall1)) continue;

            for (let j = i + 1; j < walls.length; j++) {
                let wall2 = walls[j].rect;
                if (TBaseEntity.is_deleted(wall2)) continue;

                // 检查两个墙体是否同向或反向
                let sameDirection = wall1.dv.dot(wall2.dv) > 0.99 || wall1.dv.dot(wall2.dv) < -0.99;

                if (sameDirection) {
                    // 检查端点是否重合
                    let points = [];

                    // 检查wall1的起点和wall2的端点
                    if (wall1.vertices[0].pos.distanceTo(wall2.vertices[0].pos) < 1) {
                        points.push(wall1.vertices[0].pos);
                    }
                    if (wall1.vertices[0].pos.distanceTo(wall2.vertices[3].pos) < 1) {
                        points.push(wall1.vertices[0].pos);
                    }

                    // 检查wall1的终点和wall2的端点
                    if (wall1.vertices[3].pos.distanceTo(wall2.vertices[0].pos) < 1) {
                        points.push(wall1.vertices[3].pos);
                    }
                    if (wall1.vertices[3].pos.distanceTo(wall2.vertices[3].pos) < 1) {
                        points.push(wall1.vertices[3].pos);
                    }

                    // 在重合点绘制分割线
                    for (let point of points) {
                        // 计算垂直于墙体的方向
                        let normal = wall1.nor.clone();
                        normal.multiplyScalar(wall1.h);
                        // 创建分割线的起点和终点（直接用数字相乘）
                        this._drawLineByPt(point, normal);
                    }
                }
            }
        }
    }
    private _drawLineByPt(middlePoint: Vector3, normal: Vector3) {
        // 计算分割线的起点和终点
        let p0 = middlePoint.clone();
        let p1 = middlePoint.clone().add(normal);

        // 转换为屏幕坐标
        let pos0 = this.painter.project2D(p0);
        let pos1 = this.painter.project2D(p1);

        // 绘制分割线
        let ctx = this.painter._context;
        ctx.beginPath();
        ctx.strokeStyle = "#ffffff";
        ctx.lineWidth = 1.5;
        ctx.setLineDash([4, 2]);
        ctx.moveTo(pos0.x, pos0.y);
        ctx.lineTo(pos1.x, pos1.y);
        ctx.stroke();
        ctx.setLineDash([]);
    }
}