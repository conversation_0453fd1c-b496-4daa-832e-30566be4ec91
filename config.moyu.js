// 读取 manifest.json ，修改后重新写入
const fs = require("fs");

const moyuUploadPath = "./moyu.upload.json";


const { 
  NODE_ENV
} = process.env;

// 开发模式无需处理
if (NODE_ENV !== "development") {
  const { 
    ENV_STAGE = "",
    OSS_REMOTE_PATH = "",
    BUILD_BRANCH = '',
    FEATURE_BRANCH = '',
    REMARK = "",
    // prod 不生效，需要去魔域发布
    AUTORELEASE = true,
    BUILD_USER=""
  } = process.env;

  const branch = FEATURE_BRANCH || BUILD_BRANCH;

  const config = {
    "bizData": {
        "env": ENV_STAGE,
        "appCode": "material-ui",
        "autoRelease": AUTORELEASE,
        "remark": branch ? `【${branch}】${REMARK}` : REMARK,
        "ossKeyDir": OSS_REMOTE_PATH
    },
    "builder":BUILD_USER,
    "bizType": "moyu_site"
  };

  fs.writeFileSync(moyuUploadPath, JSON.stringify(config), {
    flag: "w",
  });


}

