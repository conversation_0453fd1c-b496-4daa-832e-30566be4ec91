import { Vector3, <PERSON>ector3<PERSON><PERSON> } from "three";
import { ZPolyline } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { Z<PERSON>ain<PERSON> } from "@layoutai/z_polygon";
import { AdsorbingHelper, AdsorbingType, I_ExsorbPoint } from "./AdsorbingHelper";

export type I_PoygonDrawingState = "Adding"|"Added"|"Editing"|"Moving"|"Done"|"PopBack";
export class PolygonDrawingHelper
{

    protected _drawing_polyline : ZPolyline;


    protected _current_pos : Vector3 = null;

    protected _prev_adsorb_pos : Vector3 = null;

    protected _drawing_state :I_PoygonDrawingState;

    private _poly_stroke_style: string = "#66b8ff";

    private _candidate_stroke_style: string = "#fe2b2b";

    protected _result_polygon : ZPolygon;

    protected _adsorbingHelper : AdsorbingHelper;
    
    constructor()
    {   
        this._adsorbingHelper = new AdsorbingHelper();
        this.reInit();
    }
    public get candidate_stroke_style(): string {
        return this._candidate_stroke_style;
    }
    public set candidate_stroke_style(value: string) {
        this._candidate_stroke_style = value;
    }
    public get poly_stroke_style(): string {
        return this._poly_stroke_style;
    }
    public set poly_stroke_style(value: string) {
        this._poly_stroke_style = value;
    }
    public get adsorb_tol(): number {
        return  this._adsorbingHelper.adsorb_tol;
    }
    public set adsorb_tol(value: number) {
        this._adsorbingHelper.adsorb_tol = value;
    }
    public get drawing_state()
    {
        return this._drawing_state;
    }

    reInit()
    {
        this._adsorbingHelper.clean();
        this._drawing_polyline = null;
        this._result_polygon = null;
        this._current_pos = null;
        this._drawing_state = "Adding";
    }
    initExsorbPoints(input:{fromRects?:ZRect[],fromPolygons?:ZPolygon[],fromPoints?:Vector3[]})
    {
        this._adsorbingHelper.initExsorbPoints(input);
    }
    protected _updateExsorbCandidates(pos:Vector3Like)
    {
        this._adsorbingHelper.updateExsorbCandidates(pos,AdsorbingType.All,null);
    }

    isDrawPolyClosed()
    {
        if(this._drawing_polyline.vertices.length >= 3)
        {
            return this._drawing_polyline.vertices[0].pos.distanceTo(this.drawing_backVertex.pos) < 1;
        }
        else{
            return false;
        }
    }

    updateResultPoly()
    {
        this._result_polygon = new ZPolygon();
        this._result_polygon.initByVertices(this._drawing_polyline.positions);
        return this._result_polygon;
    }

    doneDrawing()
    {
        this._drawing_state = "Done";
    }


    updatePoint(pos:Vector3Like, state:I_PoygonDrawingState,needs_adsorbing:boolean=true)
    {
        if(this._drawing_state === "Done")
        {
            return;
        }
        if(state === "PopBack")
        {
            if(this._drawing_polyline && this._drawing_polyline.vertices.length >=1)
            {
                this._drawing_polyline.vertices.length = this._drawing_polyline.vertices.length - 1;
                
                this._drawing_polyline.initEdgesByVertices();                
            }
            this._updatePolyline();
            return;
        }
        if(needs_adsorbing)
        {
            this._updateExsorbCandidates(pos);
        }
        let target_pos =  this._adsorbingHelper._getExsorbPos(pos);
        if(!this._current_pos)
        {
            this._current_pos = new Vector3();
            this._drawing_polyline = new ZPolyline();
        }
        this._current_pos.copy(target_pos);
        if(state==="Adding")
        {
            this._prepareAddingVertex(target_pos);
        }
        if(state==="Added")
        {
            state = this._addVertex(target_pos);
        }
        else if(state === "Editing")
        {
            this._movingVertex(target_pos);
        }
        if(state === "Done")
        {
            this.doneDrawing();
        }
    }
    protected _updatePolyline()
    {   
        if(this._drawing_polyline)
        {
            this._drawing_polyline.computeZNor();
        }
    }
    protected _prepareAddingVertex(p:Vector3)
    {
        if(!this._drawing_polyline)
        {
            this._drawing_polyline = new ZPolyline();
        }

    }
    protected _addVertex(p:Vector3) : I_PoygonDrawingState
    {

        this._drawing_polyline.vertices.push({ pos: new Vector3().copy(p) });
        this._drawing_polyline.initEdgesByVertices();

        this._updatePolyline();


        let state :I_PoygonDrawingState = "Added";
        if(this.isDrawPolyClosed())
        {
            state = "Done";
        }

        return state;
        
    }

    protected _movingVertex(p:Vector3)
    {
        if(this._current_pos)
        {
            this._current_pos.copy(p);
            this._updatePolyline();
        }
    }

    get drawing_backEdge()
    {
        if(this._drawing_polyline)
        {
            return this._drawing_polyline.edges[this._drawing_polyline.edges.length-1];
        }
        return null;
    }

    get drawing_backVertex()
    {
        if(!this._drawing_polyline) return null;
        return this._drawing_polyline.vertices[this._drawing_polyline.vertices.length-1] || null;
    }

    protected _drawCanvas_Candidates(painter:ZPainter)
    {
        this._adsorbingHelper.drawCanvas(painter);
    }

    protected _drawCanvas_DrawingPoly(painter:ZPainter)
    {
        let lw = painter._context.lineWidth;

        painter.strokeStyle = this._poly_stroke_style;
        painter._context.lineWidth = 4;

        if(this._drawing_polyline && this._drawing_polyline.vertices.length >= 2)
        {
            painter.drawEdges(this._drawing_polyline.edges);
        }

        if(this._current_pos)
        {
            if(this.drawing_backVertex)
            {
                let t_edge = new ZEdge({pos:this.drawing_backVertex.pos},{pos:this._current_pos});
                t_edge.computeNormal(this._drawing_polyline.orientation_z_nor);

                painter.drawEdges([t_edge]);
            }

            let points = [...this._drawing_polyline.positions,this._current_pos];
            points.forEach((p)=>{
                painter.drawPointCircle(p,20,10);
            })
        }
        painter._context.lineWidth = lw;
    }
    drawCanvas(painter:ZPainter)
    {
        this._drawCanvas_Candidates(painter);
     
        this._drawCanvas_DrawingPoly(painter);

    }


}
