import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { T_SplitWallOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_SplitWallOperationInfo";
import { EventName } from "@/Apps/EventSystem";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { TWindowDoorEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { message } from '@svg/antd';
import { HouseDesignSubHandler } from "./HouseDesignBaseHandler";
import { Vector3Like } from "three";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { ZInputDimension } from "@/Apps/LayoutAI/Layout/TransformElements/ZInputDimension";

export class SplitWallSubHandler extends HouseDesignSubHandler {

    private _split_val: number = 0;
    private _inputDim: ZInputDimension | null = null;
    private _isFromLeft: boolean = true;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this._split_val = 0;
    }

    get target_wall() {
        if (!this.selected_target?.selected_rect) {
            return null;
        }
        let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
        if (entity) {
            if (entity.type === "Wall") return entity as TWall;
        }
        return null;

    }
    enter(state?: number): void {
        super.enter(state);
        // 在enter时添加事件监听，使用capture模式
        document.addEventListener('keydown', this._handleKeyDown, true);

        this.manager.setCanvasCursorState(LayoutAI_CursorState.SplitWall);
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, true);
        LayoutAI_App.emit_M(EventName.SelectingTarget, null);
        this._split_val = 0;
        this._isFromLeft = true;
        this.cleanSelection();
        this.manager.layout_container.cleanDimension();
        this.update();
    }
    leave(state?: number): void {
        // 在leave时移除事件监听，使用capture模式
        document.removeEventListener('keydown', this._handleKeyDown, true);

        super.leave(state);
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);

        if (state == 1) // accept leave
        {
            this._doneSplitWall();
        }

        if (this._inputDim) {
            this._inputDim.dispose();
            this._inputDim = null;
        }

        this.container.updateEntityRelations();
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, true);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, false);
    }


    // 添加键盘事件处理函数
    private _handleKeyDown = (ev: KeyboardEvent) => {
        if (ev.key === 'Tab' && this.target_wall) {
            ev.preventDefault(); // 阻止默认的Tab行为
            ev.stopPropagation(); // 阻止事件冒泡

            // 切换参考点
            this._isFromLeft = !this._isFromLeft;

            // 更新显示的值和标注线位置
            this.update();
        }
    }

    private _drawMiddleIndicator(wall: TWall) {
        if (!wall.rect) return;

        // 获取墙体中点的世界坐标
        const middlePoint = wall.rect.unproject({ x: 0, y: 0 });
        let p0 = wall.rect.unproject({ x: this._split_val, y: -wall.rect.h });
        let p1 = wall.rect.unproject({ x: this._split_val, y: wall.rect.h });
        let edge = new ZEdge({ pos: p0 }, { pos: p1 });
        this.painter.drawEdges([edge], 1);
        let ctx = this.painter._context;
        let pos = this.painter.project2D(middlePoint);

        // 绘制朝上的三角形
        ctx.beginPath();
        ctx.fillStyle = "rgba(242, 61, 209, 0.8)";

        // 三角形的尺寸
        const size = 9;

        // 绘制三角形的三个点
        ctx.moveTo(pos.x, pos.y - size);  // 顶点
        ctx.lineTo(pos.x - size, pos.y + size);  // 左下角
        ctx.lineTo(pos.x + size, pos.y + size);  // 右下角

        ctx.closePath();
        ctx.fill();
    }

    private _drawSplitLine(wall: TWall) {
        let p0 = wall.rect.unproject({ x: this._split_val, y: -wall.rect.h });
        let p1 = wall.rect.unproject({ x: this._split_val, y: wall.rect.h });
        let edge = new ZEdge({ pos: p0 }, { pos: p1 });
        this.painter.drawEdges([edge], 2);
    }

    private _setupInputDimension(wall: TWall) {
        if (!this._inputDim) {
            const startPoint = wall.rect.unproject({
                x: this._isFromLeft ? -wall.rect.w / 2 : wall.rect.w / 2,
                y: wall.rect.h + 100
            });
            const endPoint = wall.rect.unproject({
                x: this._split_val,
                y: wall.rect.h + 100
            });
            this._inputDim = new ZInputDimension(startPoint, endPoint);

            // 使用箭头函数来保持this的指向
            this._inputDim.setOnEnterPressed((value: number) => this.onEnterPressed(value));
        }
    }

    private onEnterPressed = (value: number) => {
        if (!this.target_wall?.rect) return
        let rect = this.target_wall.rect;
        const newSplitVal = this._isFromLeft ?
            -rect.w / 2 + value :
            rect.w / 2 - value;

        const maxValue = rect.w / 2 - 10;
        if (Math.abs(newSplitVal) <= maxValue) {
            this._split_val = newSplitVal;
            this._doneSplitWall()
        } else {
            message.error(String(LayoutAI_App.t('输入的打断位置有误，请重新输入')));
        }
    }

    private updateDimensionPosition(wall: TWall) {
        const startPoint = wall.rect.unproject({
            x: this._isFromLeft ? -wall.rect.w / 2 : wall.rect.w / 2,
            y: wall.rect.h + 100
        });
        const endPoint = wall.rect.unproject({
            x: this._split_val,
            y: wall.rect.h + 100
        });
        this._inputDim.setPoints(startPoint, endPoint);
    }

    drawCanvas(): void {
        let painter = this._cad_mode_handler.painter;
        this._p_sc = painter._p_sc;

        if (this.selected_target?.selected_rect) {
            LayoutAI_App.instance.setIsSelecting(true);

            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
            if (!entity) return;

            const wall = this.target_wall;
            if (!wall || !wall.rect) return;

            // 按顺序绘制各个元素
            this._drawSplitLine(wall);

            // 处理标注线和输入框
            this._setupInputDimension(wall);
            this.updateDimensionPosition(wall);

            // 计算显示值
            const leftDist = Math.abs(this._split_val + wall.rect.w / 2);
            const rightDist = Math.abs(wall.rect.w / 2 - this._split_val);
            const displayValue = this._isFromLeft ? leftDist : rightDist;

            // 绘制标注线和输入框
            this._inputDim.drawDimension(this.painter);
            this._inputDim.showInput(this.painter, displayValue, true);

            // 最后绘制中点指示器，确保在最上层
            if (Math.abs(this._split_val) < 0.1) {
                this._drawMiddleIndicator(wall);
            }
        } else {
            LayoutAI_App.instance.setIsSelecting(false);
            this.EventSystem.emit_M(EventName.SelectingTarget, null);

            if (this._inputDim) {
                this._inputDim.hideInput();
            }
        }
    }

    private _doneSplitWall() {
        const wall = this.target_wall;
        if (!wall || !wall.rect) {
            return;
        }

        let opertaion_info = new T_SplitWallOperationInfo(this.manager);
        opertaion_info.makeSplitData(wall, this._split_val);
        opertaion_info.redo();

        this.manager.appendOperationInfo(opertaion_info);
        for (let win_rect of wall._win_rects) {
            let entity = TBaseEntity.getEntityOfRect(win_rect) as TWindowDoorEntity;

            if (entity) {
                entity._wall_rect = null;
            }
        }

        this.cleanSelection();
        this._cad_mode_handler._is_moving_element = false;
        this.updateCandidateRects();
        this.update();
    }

    // 获取鼠标点击的墙
    private _getTargetWallByPos(pos: Vector3Like): TWall | null {
        let target_rect = this.getTargetRectContainsPos(pos);
        if (!target_rect) {
            return;
        }
        let entity = TBaseEntity.getEntityOfRect(target_rect);
        if (entity?.type != "Wall") {
            return null;
        }
        return entity as TWall;
    }


    private _isNearMiddle(pos: Vector3Like, wall: TWall): boolean {
        if (!wall.rect) return false;
        const projectedPos = wall.rect.project(pos);
        const middleX = 0; // 墙体局部坐标系中，中点x坐标为0

        // 增加吸附范围到20像素
        const snapDistance = 20;
        const scale = this.painter._p_sc;
        const scaledSnapDistance = snapDistance / scale; // 根据缩放调整吸附范围

        return Math.abs(projectedPos.x - middleX) < scaledSnapDistance;
    }

    onmousemove(ev: I_MouseEvent): void {
        const pos = { x: ev.posX, y: ev.posY, z: 0 };
        const hoveredWall = this._getTargetWallByPos(pos);

        if (hoveredWall) {
            if (this.target_wall !== hoveredWall) {
                this.selected_target.selected_rect = hoveredWall.rect;
                hoveredWall.initNeighborsWalls(this.container._wall_entities);
            }

            const projectedPos = hoveredWall.rect.project(pos);
            let split_val = projectedPos.x;

            // 检查是否靠近中点
            if (this._isNearMiddle(pos, hoveredWall)) {
                split_val = 0; // 吸附到中点
            }

            // 限制分割位置在墙体范围内
            const max_ww = hoveredWall.rect.w / 2 - 10;
            if (Math.abs(split_val) > max_ww) {
                split_val = max_ww * (split_val > 0 ? 1 : -1);
            }

            this._split_val = split_val;
            this.update();
        } else {
            this.selected_target.selected_rect = null;
            if (this._inputDim) {
                this._inputDim.hideInput();
            }
            this.update();
        }
    }

    onmousedown(ev: I_MouseEvent): void {
        if (ev.button === 0 && this.target_wall) { // 左键点击
            this._doneSplitWall();
        }
    }

    onmouseup(ev: I_MouseEvent): void {
        if (ev.button === 2) { // 右键点击
            this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
            this.update();
            return;
        }
    }

    private _checkSplitPosition(value: number): boolean {
        const wall = this.target_wall;
        if (!wall || !wall.rect) return false;

        // 检查是否超出墙体范围（留10的余量）
        const maxValue = wall.rect.w / 2 - 10;
        if (Math.abs(this._split_val) > maxValue) {
            return false;
        }
        return true;
    }

}