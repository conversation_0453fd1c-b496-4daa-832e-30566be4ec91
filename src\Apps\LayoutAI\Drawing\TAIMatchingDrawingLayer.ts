
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";

import { TAppManagerBase } from "../../AppManagerBase";
import { TFigureElement } from "../Layout/TFigureElements/TFigureElement";
import { LayoutAI_App } from "../../LayoutAI_App";

export class TAIMatchingDrawingLayer extends TDrawingLayer
{
    
    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.AIMatchingDrawing,manager);
        this._dirty = true;
    }


     _updateLayerContent(): void {

    }
    // 画图元和俯视图
    drawFurniture(furniture: TFigureElement, isSelectSeries: boolean) {
        if (furniture._ex_prop['is_deleted'] == "1") return;

        if (isSelectSeries
            && furniture._matched_material?.modelId && (furniture._matched_material?.modelFlag !== '11')) {
            if(furniture.isMaterialMarkAsInvisible()) return;
            furniture.drawPreviewFigure(this.painter);
        } else {
            furniture.drawFigure(this.painter);
        }
    }
    onDraw(): void {

        for(let room of this.layout_container._rooms)
        {
            let drawing_furnitures : TFigureElement[] = room._furniture_list?[...room._furniture_list]:[];
            drawing_furnitures.sort((a,b)=>a.default_drawing_order - b.default_drawing_order);
            for(let furniture of drawing_furnitures) {
                if(!LayoutAI_App.IsDebug && furniture._is_decoration) continue;

                this.drawFurniture(furniture, room.isSelectSeries);
            }  
    
            this.painter.fillStyle = "#000"
            if (!room.selectable) {
                this.painter.fillStyle = "#ccc";
                let poly = room.room_shape._poly;
                this.painter.fillPolygon(poly, 1.0);
                this.painter.fillStyle = "#6c7175";
            }
    
        }

 


    }
}