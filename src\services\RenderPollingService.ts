import { RenderReqOffline } from '@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline';
import { AIGCService } from '@/Apps/LayoutAI/Services/AIGC/AIGCService';
import HomeStore from '@/models/homeStore';

/**
 * 渲染进度轮询器，如果存在渲染任务，则可以通过实例化该类，调用checkAndStartPolling方法实现自动轮询
 */
export class RenderPollingService {
    // 全局唯一定时器
    private static timer: any = null;
    private static polling = false;
    private store: HomeStore;

    private static _instance : RenderPollingService;
    constructor(store: HomeStore) {
        this.store = store;
        RenderPollingService._instance = this;
    }

    static get instance()
    {

        return RenderPollingService._instance 

    }

    
    // 查询AI任务数
    private async query_aiScheme() {
        let res = await AIGCService.instance.queryImageList('');
        if (res && res.result) {
             return res.result.filter((item: { state: number }) => item.state === 0).length;
        }
        console.log('ai渲染任务查询失败');
        return 0;
    }

    // 查询渲染任务数
    private async query_renderScheme() {
        let res = await RenderReqOffline.instance.requestAtlas({
        userId: '',
        flag: '',
        schemeId: '',
        pageIndex: 1,
        pageSize: 15,
        all: '',
        isRtx: 0,
        resolutionTag: '',
        type: 0,
        exclusiveRealAdjustLight: false,
        authCode: ''
        });
        if (res && res.success) {
        return res.res.data?.ReturnList?.filter((item: { Status: number }) =>
            item.Status === 1 || item.Status === 0
        ).length ?? 0;
        }
        console.log('标准渲染任务查询失败');
        return 0;
    }

    // 查询总任务数
    private async queryTotalCount() {
        const a = await this.query_aiScheme();
        const r = await this.query_renderScheme();
        return a + r;
    }

    // 启动轮询（全局单例）
    private startPolling() {
        if (RenderPollingService.timer) {
            // clearInterval(RenderPollingService.timer);
            // RenderPollingService.timer = null;
            return;
        }
        RenderPollingService.timer = setInterval(async () => {
            console.log('zzz 渲染任务进度轮询，timer：', RenderPollingService.timer)
            if (RenderPollingService.polling) return;
            RenderPollingService.polling = true;
            const total = await this.queryTotalCount();
            this.store.setGenCount(total);
            if (total === 0) {
                this.stopPolling();
            }
            RenderPollingService.polling = false;
        }, 10000);

            // 立即执行一次
        this.queryTotalCount().then(total => {
            this.store.setGenCount(total);
            if (total === 0) this.stopPolling();
        });
    }

    // 停止轮询
    public stopPolling() {
        if (RenderPollingService.timer) {
            clearInterval(RenderPollingService.timer);
            RenderPollingService.timer = null;
        }
    }

    // 外部调用：根据当前任务数决定是否开启轮询
    public async checkAndStartPolling() {
        const total = await this.queryTotalCount();
        this.store.setGenCount(total);
        if (total > 0) {
          this.startPolling();
        } else {
         this.stopPolling();
        }
    }
}

let pollingServiceInstance: RenderPollingService | null = null;

export function getPollingService(store: HomeStore): RenderPollingService {
    if (!RenderPollingService.instance) {
         new RenderPollingService(store);
    }
    return RenderPollingService.instance;
}
