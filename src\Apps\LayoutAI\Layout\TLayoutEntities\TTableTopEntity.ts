import { <PERSON><PERSON>erG<PERSON>metry, Float32BufferAttribute, Group, Mesh, MeshStandardMaterial, Object3D, <PERSON><PERSON>peUtils, Vector2 } from "three";
import { Scene3D } from "../../Scene3D/Scene3D";
import { ZPolygon } from "@layoutai/z_polygon";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TRoom } from "../TRoom";
import { compareNames } from "@layoutai/z_polygon";
import { TPainter } from "../../Drawing/TPainter";
import { ZRect } from "@layoutai/z_polygon";
import { SimpleInnerWallMaterial } from "../../Scene3D/materials/entityMaterials/SimpleInnerWallMaterial";
import { TDesignMaterialUpdater } from "../../Services/MaterialMatching/TDesignMaterialUpdater";
import { ZRectShape } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "./TFurnitureEntity";
import { MeshName } from "../../Scene3D/NodeName";
import { GeometryBuilder } from "../../Scene3D/builder/GeometryBuilder";
import { MeshBuilder } from "../../Scene3D/MeshBuilder";
import { TRoomEntity } from "./TRoomEntity";
import { EdgesBuilder, SceneMaterialMode, TextureManager } from "@layoutai/model3d_api";

const default_tabletop_texture_url = "https://img3.admin.3vjia.com//UpFile/C00007003/PMC/DesignMaterial/202411/306445714/%E5%A4%A7%E7%90%86%E7%9F%B3-5.jpg?x-oss-process=image/resize,m_fixed,w_320,h_320";
const default_material_id = "258887179";

/**
 *  橱柜-台面
 */
export class TTableTopEntity extends TBaseEntity {
    _table_element: TFigureElement;
    _cooker_elements: TFigureElement[];
    _sink_elements: TFigureElement[];

    /**
     *  地柜图元
     */
    _floor_cabinet_elements: TFigureElement[];


    _tabletop_mesh: Mesh;
    _table_thickness: number;

    _roomEntity : TRoomEntity;
    constructor(figure: TFigureElement = null) {
        super(null, -1);
        this._table_element = figure || TFigureElement.createSimple("台面");

        this._table_element._polygon = this._table_element._polygon || new ZPolygon();
        this._cooker_elements = [];
        this._sink_elements = [];
        this._floor_cabinet_elements = [];
        this._tabletop_mesh = null;

        this._table_thickness = 20;
        this._roomEntity = null;
    }

    get table_polygon() {
        return this._table_element._polygon;
    }

    static updateTableTopEntitiesInRoom(room: TRoom, reGenerate: boolean = false) {
        let floor_cabinets = room._furniture_list.filter((ele) => compareNames([ele.category], ["地柜"]));

        let rects = floor_cabinets.map((cabinet) => {
            let rect = cabinet.rect.clone();
            let r_center = rect.rect_center;
            rect._h = Math.max(rect._h + 20, 600);

            rect.updateRect();

            let left_int_p = room.room_shape._poly.getRayIntersection(r_center, rect.dv.clone().negate());
            let right_int_p = room.room_shape._poly.getRayIntersection(r_center, rect.dv.clone());
            let left_extend = 10;
            let right_extend = 10;
            if (left_int_p) {
                let left_wall_dist = Math.abs(rect.project(left_int_p.point).x) - rect.w / 2;
                if (left_wall_dist > 0 && left_wall_dist < 100) {
                    left_extend = Math.max(left_wall_dist, left_extend);
                }
            }
            if (right_int_p) {
                let right_wall_dist = Math.abs(rect.project(right_int_p.point).x) - rect.w / 2;
                if (right_wall_dist > 0 && right_wall_dist < 100) {
                    right_extend = Math.max(right_wall_dist, right_extend);
                }
            }
            let back_extend = 100;
            let pos = rect.unproject({ x: (right_extend - left_extend) / 2, y: -back_extend / 2 });
            rect._w += (left_extend + right_extend);
            rect._h += back_extend;
            rect.rect_center = pos;
            rect.reOrderByOrientation(true);
            return rect;

        });

        if (rects.length == 0) {
            return [];
        }
        let polys = rects[0].union_polygons(rects);
        let t_polys: ZPolygon[] = [];
        let room_poly = room.room_shape._poly.clone();
        room_poly.reOrderByOrientation(true);
        polys.forEach(poly => {
            let inside_polys = poly.intersect(room.room_shape._poly);
            t_polys.push(...inside_polys);
        });

        let tabletop_list: TTableTopEntity[] = [];
        t_polys.forEach((poly) => {
            let entity = new TTableTopEntity();
            entity.table_polygon.initByVertices(poly.positions);
            entity.table_polygon.reOrderByOrientation(true);
            entity.updateTableTopElements(room, true);
            tabletop_list.push(entity);
        });
        tabletop_list.forEach(entity=>{
            entity._roomEntity = room?._room_entity || null;
        })
        return tabletop_list;
    }
    updateTableTopElements(room: TRoom = null, force: boolean = true) {
        if (!force) return;
        if (room && force) {
            this._floor_cabinet_elements = [];
            let floor_cabinets = room._furniture_list.filter((ele) => compareNames([ele.category], ["地柜"]));

            floor_cabinets = floor_cabinets.filter((ele) => {
                return this.table_polygon.containsPoint(ele.rect.rect_center);
            });
            this._floor_cabinet_elements = floor_cabinets;

            let max_z = 600;
            this._floor_cabinet_elements.forEach(ele => {
                let rect = ele.matched_rect || ele.rect;
                max_z = Math.max(rect.zval + ele.height, max_z);
            });
            this.table_polygon.zval = max_z;
        }
        if (force) {
            this._cooker_elements = this._floor_cabinet_elements.filter(ele => compareNames([ele.category], ["炉灶地柜"])).map((ele) => {
                let fig = TFigureElement.createSimple("炉灶");
                fig.category = "炉灶";
                fig.rect.copy(ele.rect);
                fig.height = 150;
                fig.rect.zval = this.table_polygon.zval;

                return fig;
            });
            this._sink_elements = this._floor_cabinet_elements.filter(ele => compareNames([ele.category], ["水槽地柜"])).map((ele) => {
                let fig = TFigureElement.createSimple("星盆");
                fig.rect.copy(ele.rect);
                let r_center = fig.rect.rect_center;
                fig.length -= 40;
                fig.width -= 40;
                fig.rect.rect_center = r_center;
                let hh = 480;
                fig.height = hh;
                let z_offset = 240;
                fig.rect.zval = this.table_polygon.zval + this._table_thickness - z_offset;
                return fig;
            });
        }


    }

    updateTableTopGeometry() {


        let main_positions = this.table_polygon.vertices.map((v => new Vector2(v.pos.x, v.pos.y)));
        let holes: Vector2[][] = [];
        let makeHoleRect = (rect: ZRect, type = 0) => {
            let h_rect = new ZRectShape(rect._w, rect._h);

            let int0 =this.table_polygon.getRayIntersection(rect.rect_center,rect.nor);
            let int1 = this.table_polygon.getRayIntersection(rect.rect_center,rect.nor.clone().negate());

            let max_depth = 450;
            if(int0.edge && int1.edge)
            {
                max_depth = Math.max(int1.point.distanceTo(int0.point) - 40,max_depth);
            }
            h_rect.nor = rect.nor;
            let r_center = rect.rect_center;
            h_rect._w -= 40;
            h_rect._h = Math.min(max_depth, h_rect._h - 40);
            h_rect.rect_center = r_center;

            return h_rect;
        }
        holes.push(...this._cooker_elements.map((ele) => makeHoleRect(ele.matched_rect||ele.rect).toPoints().map(v => new Vector2(v.x, v.y))));
        holes.push(...this._sink_elements.map((ele) => makeHoleRect(ele.matched_rect||ele.rect, 1).toPoints().map(v => new Vector2(v.x, v.y))));
        let faceIds = ShapeUtils.triangulateShape(main_positions, holes);

        let positions: number[] = [];
        let poly_size = this.table_polygon.vertices.length;

        main_positions.forEach((p) => positions.push(p.x, p.y, this.table_polygon.zval + this._table_thickness));
        holes.forEach((hole) => hole.forEach(p => positions.push(p.x, p.y, this.table_polygon.zval + this._table_thickness)));

        let v_size_0 = positions.length / 3;

        main_positions.forEach((p) => positions.push(p.x, p.y, this.table_polygon.zval + 1.));
        holes.forEach((hole) => hole.forEach(p => positions.push(p.x, p.y, this.table_polygon.zval + 1.)));
        let geometry: BufferGeometry = null;

        if (this._tabletop_mesh) {
            geometry = this._tabletop_mesh.geometry;
        }
        geometry = geometry || new BufferGeometry();

        let flist: number[] = [];
        faceIds.forEach(f => flist.push(...f));
        faceIds.forEach(f => {
            for (let i = 0; i < f.length; i++) {
                flist.push(f[f.length - 1 - i] + v_size_0);
            }
        });

        for (let i = 0; i < poly_size; i++) {
            let f0 = i;
            let f1 = (i + 1) % poly_size;
            let f2 = f1 + v_size_0;
            let f3 = f0 + v_size_0;
            flist.push(f2, f1, f0);
            flist.push(f0, f3, f2);
        }

        let flat_positions: number[] = [];

        flist.forEach(fid => flat_positions.push(positions[3 * fid + 0], positions[3 * fid + 1], positions[3 * fid + 2]));
        geometry.setAttribute("position", new Float32BufferAttribute(flat_positions, 3));

        geometry.setIndex(GeometryBuilder.getIndices(flat_positions));

        let flat_uvs: number[] = [];
        flist.forEach(fid => flat_uvs.push(positions[3 * fid + 0] / 1000, positions[3 * fid + 1] / 1000));
        geometry.setAttribute("uv", new Float32BufferAttribute(flat_uvs, 2));

        geometry.computeVertexNormals();




        return geometry;

    }

    updateMesh3D(mode?: SceneMaterialMode): Object3D {
        if (!this._mesh3d) {
            this._mesh3d = new Group();
        }

        if (!this._tabletop_mesh) {
            this._tabletop_mesh = new Mesh();
            this._tabletop_mesh.name = MeshName.TableTop;
            this._tabletop_mesh.material = new MeshStandardMaterial();
        }
        for (let child of this._mesh3d.children) {
            this._mesh3d.remove(child);
        }
        let hasMatchedMaterials = (this._floor_cabinet_elements || []).find((ele)=>{
            return ele.haveMatchedMaterial();
        });
        if(!hasMatchedMaterials)
        {
            return;
        }

        this.updateTableTopGeometry();


        if(this._tabletop_mesh)
        {
            this._mesh3d.add(this._tabletop_mesh);
            EdgesBuilder.makeEdgesOfObject(this._tabletop_mesh);
            this._tabletop_mesh.userData.category = "台面";
            MeshBuilder.bindMeshMaterials(this._tabletop_mesh);
            TextureManager.updateMeshTextureWithImg(this._tabletop_mesh, default_tabletop_texture_url, default_material_id);
        }

        let t_elements = [...this._cooker_elements,...this._sink_elements];
        t_elements.forEach(ele=>{
            let hh = ele.height;
            if(ele._matched_material)
            {
                hh = ele._matched_material.height;
                ele.height = hh;
            }
            let z_offset = hh - 50;
            ele.rect.zval = this.table_polygon.zval + this._table_thickness - z_offset;


            ele.params.alignTopZval = this.table_polygon.zval + this._table_thickness + (compareNames([ele.category],["星盆"])?5: 5);
            

            if(ele.matched_rect)
            {
                ele.matched_rect.zval = ele.rect.zval;
            }

        })
        this._cooker_elements.forEach((ele) => {
            let entity = ele.furnitureEntity || new TFurnitureEntity(ele);
            this._mesh3d.add(entity.updateMesh3D());
        });

        this._sink_elements.forEach((ele) => {
            let entity = ele.furnitureEntity || new TFurnitureEntity(ele);
            this._mesh3d.add(entity.updateMesh3D());

        });

        TDesignMaterialUpdater.instance.updateFurnituresDesignMaterials([...this._cooker_elements, ...this._sink_elements], true);


        return this._mesh3d;
    }
    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (options.is_draw_figure) {
            if (this.table_polygon && this.table_polygon.vertices.length > 0) {
                painter.fillStyle = "#aaa";
                painter.fillPolygons([this.table_polygon], 0.3);
            }
        }
    }
    clearMatchedMaterials() {
        this._cooker_elements.forEach(ele => ele.clearMatchedMaterials());
        this._sink_elements.forEach(ele => ele.clearMatchedMaterials());
        this._floor_cabinet_elements.forEach(ele => ele.clearMatchedMaterials());
        this._table_element.clearMatchedMaterials();
        if (this._tabletop_mesh) {
            this._tabletop_mesh.removeFromParent();
            this._tabletop_mesh = null;
        }
        if (this._mesh3d) {
            this._mesh3d.removeFromParent();
            this._mesh3d = null;
        }
    }
}