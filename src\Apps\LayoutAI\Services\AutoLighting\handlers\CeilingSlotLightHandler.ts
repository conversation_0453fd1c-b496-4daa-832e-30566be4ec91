import { <PERSON><PERSON><PERSON>Handler } from "./BaseLightHandler";
import { LightingRuler } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { TFigureElement } from "../../../Layout/TFigureElements/TFigureElement";
import { MeshName, SwitchConfig, UserDataKey } from "../../../Scene3D/NodeName";
import { TDesignMaterialUpdater } from "../../MaterialMatching/TDesignMaterialUpdater";

export class CeilingSlotLightHandler extends BaseLightHandler {
    
    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        let layout_container = this.getLayoutContainer();
        let slotLightElements: TFigureElement[] = [];
        let downLightElements: TFigureElement[] = [];
        let entities: TFillLightEntity[] = [];
        layout_container._room_entities.forEach(roomEntity => {
            if (this.checkCondition(ruler, roomEntity)) {
                let downLights = roomEntity.room_ceiling_entity.getAllDownLights();
                downLightElements.push(...downLights);

                roomEntity.room_ceiling_entity.ceiling_layer_entities.forEach(layer => {
                    let figures = layer.createLampStrip({
                        materialId: ruler.lighting.materialId,
                        color: ruler.lighting.color,
                        brightness: ruler.lighting.intensity,
                    });
                    if (ruler.lighting.materialId) {
                        slotLightElements.push(...figures);
                    }
                });
                roomEntity.room_ceiling_entity.updateMesh3D();
            }
        });

        await TDesignMaterialUpdater.instance.updateFurnituresDesignMaterials(slotLightElements);
        for (let ele of slotLightElements) {
            if (!ele._solid_mesh3D) {
                continue;
            }
            ele._solid_mesh3D.name = MeshName.LightModel;
            if (SwitchConfig.useFillLight) {
                // 用补光板灯带时，模型不显示
                ele._solid_mesh3D.visible = false;
            } else {
                let userData: any = {
                    color: ruler.lighting.color,
                    power: ruler.lighting.intensity,
                };
                if (ruler.lighting.materialId) {
                    userData.materialId = ruler.lighting.materialId;
                }
                if (ruler.lighting.targetObjectName) {
                    userData.targetObjectName = ruler.lighting.targetObjectName;
                }
                ele._solid_mesh3D.userData[UserDataKey.MeshLight] = userData;
            }
        }

        // updateMesh3D 时，会移除所有子节点，这里需要重新添加
        downLightElements.forEach(ele => { ele.resetMesh3DParent(); });

        return entities;
    }
}