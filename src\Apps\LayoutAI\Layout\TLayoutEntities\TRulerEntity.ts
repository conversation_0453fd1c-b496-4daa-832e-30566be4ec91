import { ZRect } from "@layoutai/z_polygon";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { Vector3 } from "three";
import { TPainter } from "../../Drawing/TPainter";
import { ZDistanceDimension } from "@layoutai/z_polygon";
import { IRoomEntityType } from "../IRoomInterface";

export class TRulerEntity extends TBaseEntity {

    _color_style: string;

    _line_style: string;

    _v0: Vector3;
    _v1: Vector3;
    static readonly EntityType: IRoomEntityType = "Ruler";
    constructor() {
        super(new ZRect(1, 1));
        this.type = "Ruler";

        this._color_style = "#66b8ff";
        this._line_style = "";
    }
    static RegisterGenerators()
    {
        TBaseEntity.RegisterPolyEntityGenerator(this.EntityType,this.getOrMakeEntityOfCadRect)
    } 
    get color_style() {
        return this._color_style;
    }

    set color_style(color: string) {
        this._color_style = color;
    }

    updateRect() {
        if (this._v0 && this._v1) {
            let length = this._v0.distanceTo(this._v1);
            this._rect = new ZRect(length, 3);
            this._rect.rect_center = this._v0.clone().add(this._v1).multiplyScalar(0.5);
            this._rect.nor = this._v1.clone().sub(this._v0).cross(new Vector3(0, 0, 1)).normalize();
            this.update();
        }
    }

    initProperties(): void {
        this._bindPropertiesOnChange();
    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {


        if (this._rect && this._rect._w > 1) {
            painter.fillStyle = "#147FFA";
            painter.strokeStyle = "#147FFA";
            let dim = new ZDistanceDimension(this._rect.leftEdge.center, this._rect.rightEdge.center.clone());
            dim.nor = new Vector3(0, 1, 0);
            dim.offset_len = 0;
            dim._font_size = 3 / painter._p_sc;
            dim.text_offset_len = 90;
            painter.drawDimension(dim);
        }
    }
}