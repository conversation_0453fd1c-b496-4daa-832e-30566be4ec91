import { EventName } from '@/Apps/EventSystem';
import { AI_CadData } from '@/Apps/LayoutAI/AICadData/AI_CadData';
import { I_SwjXmlScheme } from '@/Apps/LayoutAI/AICadData/SwjLayoutData';
import { CadDrawingLayerType } from '@/Apps/LayoutAI/Drawing/TDrawingLayer';
import {
  AI_PolyTargetType,
  IRoomEntityRealType,
  IRoomEntityType,
  KeyEntity
} from '@/Apps/LayoutAI/Layout/IRoomInterface';
import { LayoutAiCadDataParser } from '@/Apps/LayoutAI/Layout/TLayoutEntities/loader/LayoutAiCadDataParser';
import { TBaseEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity';
import { TStructureEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TStructureEntity';
import { TWall } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TWall';
import { TWindowDoorEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity';
import { LayoutContainerUtils } from '@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils';
import {
  FileOpenResult,
  I_MouseEvent,
  openFileInput,
  uploadImageToOss
} from '@/Apps/LayoutAI/Utils/basic_utils';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { mode_type } from '@/config';
import { clearFiles, loadFile } from '@/IndexDB';
import { Vector3 } from 'three';
import { g_FigureImagePaths } from '../../../LayoutAI/Drawing/FigureImagePaths';
import { AI2BaseModeHandler } from '../../AI2BaseModeHandler';
import { AI2DesignBasicModes, AI2DesignManager } from '../../AI2DesignManager';
import { AddFurnitureHandler } from '../SubHandlers/CadEditSubHandlers/AddFurnitureHandler';
import { DimensionFurnitureHandler } from '../SubHandlers/CadEditSubHandlers/DimensionFurnitureHandler';
import { DimensionWallHandler } from '../SubHandlers/CadEditSubHandlers/DimensionWallHandler';
import { MoveFurnitureSubHandler } from '../SubHandlers/CadEditSubHandlers/MoveFurnitureSubHandler';
import { MoveWinDoorHandler } from '../SubHandlers/CadEditSubHandlers/MoveWinDoorHandler';
import { RotateEntityHandler } from '../SubHandlers/CadEditSubHandlers/RotateEntityHandler';
import { ScaleEntitySubHandler } from '../SubHandlers/CadEditSubHandlers/ScaleEntitySubHandler';
import { AddWallsSubHandler } from '../SubHandlers/HouseDesignSubHandlers/AddWallsSubHandler';
import { HouseDesignSubHandler } from '../SubHandlers/HouseDesignSubHandlers/HouseDesignBaseHandler';
import { MoveCopyImageHandler } from '../SubHandlers/HouseDesignSubHandlers/MoveCopyImageHandler';
import { MoveWallSubHandler } from '../SubHandlers/HouseDesignSubHandlers/MoveWallSubHandler';
import { RectDrawWallSubHandler } from '../SubHandlers/HouseDesignSubHandlers/RectDrawWallSubHandler';
import { SplitWallSubHandler } from '../SubHandlers/HouseDesignSubHandlers/SplitWallSubHandler';
import { T_DimensionDWElement } from '../../../LayoutAI/Layout/TransformElements/T_DimensionDWElement';
import { T_DimensionElement } from '../../../LayoutAI/Layout/TransformElements/T_DimensionElement';
import { T_DimensionWallElement } from '../../../LayoutAI/Layout/TransformElements/T_DimensionWallElement';
import { T_DimensionOutterWallElement } from '../../../LayoutAI/Layout/TransformElements/T_DimensionOutterWallElement';
import { T_MoveElement } from '../../../LayoutAI/Layout/TransformElements/T_MoveElement';
import { T_MoveWallElement } from '../../../LayoutAI/Layout/TransformElements/T_MoveWallElement';
import { T_MoveWinDoorElement } from '../../../LayoutAI/Layout/TransformElements/T_MoveWinDoorElement';
import { T_RotateElement } from '../../../LayoutAI/Layout/TransformElements/T_RotateElement';
import { T_ScaleElement } from '../../../LayoutAI/Layout/TransformElements/T_ScaleElement';
import { T_ScaleWallElement } from '../../../LayoutAI/Layout/TransformElements/T_ScaleWallElement';
import { T_UpdateLengthElement } from '../../../LayoutAI/Layout/TransformElements/T_UpdateLengthElement';
import { ShapeDrawWallSubHandler } from '../SubHandlers/HouseDesignSubHandlers/ShapeDrawWallSubHandler';
import { T_DeleteElement } from '@/Apps/LayoutAI/Layout/TransformElements/T_DeleteElement';
import { HotelDesignSubHandler } from './HotelDesignSubHander/HotelDesignBaseHandler';
import { TBaseSpaceDrawingLayer } from '@/Apps/LayoutAI/Drawing/TBaseSpaceDrawingLayer';
import { DrawBuildingSpaceSubHandler } from './HotelDesignSubHander/DrawBuildingSpaceSubHandler';

export class AIHotelLayoutModeHandler extends AI2BaseModeHandler {
  private _shape_draw_type: string;

  protected _base_space_layer: TBaseSpaceDrawingLayer;
  constructor(manager: AI2DesignManager) {
    super(manager, AI2DesignBasicModes.HotelLayoutMode);

    this._cad_default_sub_handler = new HotelDesignSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.AddFurniture] = new AddFurnitureHandler(this); // 从左侧拖拽图元进来
    this._sub_handlers[LayoutAI_Commands.Transform_Scaling] = new ScaleEntitySubHandler(this);

    // 从形状绘制墙体
    this._sub_handlers[LayoutAI_Commands.Transform_MovingStruture] = new MoveWinDoorHandler(this); //移动结构件

    this._sub_handlers[LayoutAI_Commands.Transform_Moving] = new MoveFurnitureSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Rotate] = new RotateEntityHandler(this); //进入旋转模式
    this._sub_handlers[LayoutAI_Commands.Transform_Dimension] = new DimensionFurnitureHandler(this);
    this._sub_handlers[LayoutAI_Commands.MoveCopyImageHandler] = new MoveCopyImageHandler(this); //进入临摹底图移动模式


    this._sub_handlers[LayoutAI_Commands.DrawingBuildingSpace] = new DrawBuildingSpaceSubHandler(this);

    this._transform_elements = [];
    this._transform_elements.push(
      new T_RotateElement(1, 1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleWallElement(1, 0, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleWallElement(-1, 0, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(
      new T_DeleteElement(1, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(1, 0, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(0, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(0, 1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(-1, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(-1, 0, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(-1, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(new T_DimensionElement(0, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(1, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(2, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(3, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionWallElement(this.manager.layout_container));
    // this._transform_elements.push(new T_DimensionOutterWallElement(this.manager.layout_container));

    this._transform_elements.push(
      new T_UpdateLengthElement(this._selected_target, this.manager.layout_container, [
        AI_PolyTargetType.Wall
      ])
    );
    this._transform_elements.push(new T_DimensionDWElement(this.manager));

    this._transform_moving_element = new T_MoveElement(
      this._selected_target,
      this.manager.layout_container
    );
    this._transform_moving_wall_element = new T_MoveWallElement(
      this._selected_target,
      this.manager.layout_container
    );
    this._transform_moving_struture_element = new T_MoveWinDoorElement(
      this._selected_target,
      this.manager.layout_container
    );

    this._transform_elements.push(this._transform_moving_element);
    this._transform_elements.push(this._transform_moving_struture_element);
    this._transform_elements.push(this._transform_moving_wall_element);
    this._initial_scheme_data = null; // 初始的方案数据

    this._base_space_layer = new TBaseSpaceDrawingLayer(this.manager);

  }
  get shape_draw_type() {
    return this._shape_draw_type;
  }
  set shape_draw_type(type: string) {
    this._shape_draw_type = type;
  }

  get ai_cad_data() {
    return this.manager.layout_container?._ai_cad_data;
  }
  set ai_cad_data(data: AI_CadData) {
    LayoutAiCadDataParser.Container = this.manager.layout_container;
    LayoutAiCadDataParser.fromAiCadData(data);
  }
  get exsorb_rects() {
    return this._exsorb_target_rects;
  }

  get container() {
    return this.manager.layout_container;
  }

  get wall_entities() {
    return this.manager.layout_container._wall_entities;
  }

  set wall_entities(rects: TWall[]) {
    this.manager.layout_container._wall_entities = rects;
  }

  get layer_dimension_wall_element() {
    return this.manager.drawing_layers[CadDrawingLayerType.CadDimensionWallElement];
  }

  // get layer_dimension_outter_wall_element() {
  //   return this.manager.drawing_layers[CadDrawingLayerType.CadDimensionOutterWallElement];
  // }

  async prepare(load_test_data: boolean = true) {
    await this.prepareTestData();
    let solver = this.manager.layout_graph_solver;
    let promises = [];
    for (let i = 0; i < 1; i++) {
      promises.push(
        solver.queryModelSubSpacesFromServer(null, { pageIndex: i + 1, source: 'SubSpace' })
      );
    }
    Promise.allSettled(promises);
  }
    /**
   *  本地测试数据准备
   */
    async prepareTestData() {
      let has_ezdxf = null;
      if (!has_ezdxf && this.manager.layout_container._room_entities.length == 0) {
        let res = await this.manager._load_local_XmlSchemeData();
        if (res) {
          this.manager.layer_CadEzdxfLayer._clean();
          this.manager.layer_CadEzdxfLayer.ezdxf_data = null;
  
          this.EventSystem.emit(EventName.LayoutSchemeOpened, {
            id: this.manager.layout_container._scheme_id,
            name: this.manager.layout_container._layout_scheme_name
          });
  
          // this.manager.layout_container.focusCenter();
          if (window.innerWidth < window.innerHeight * 0.8) {
            LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.7);
          } else {
            LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.5);
          }
  
          this.manager.layout_container.needs_making_wall_xml = true;
          this._cad_default_sub_handler.updateCandidateRects();
          LayoutAI_App.emit_M(EventName.RoomList, this.room_list);
  
          LayoutContainerUtils.updateEntityUids(true);
          this.update();
  
          if (LayoutAI_App.instance.Configs.prepare_auto_layout) {
            // this.queryModelRoomsFromServer();
            // this.computeWholeLayoutSchemeList(false);
          }
        } else {
          if(!LayoutAI_App.IsDebug)
          {
            LayoutAI_App.emit(EventName.ShowWelcomePage, true);
          }
        }
      }
    }
  enter(state?: number): void {
    super.enter(state);
    console.log('enter AIHotelDesignMode');
    this.manager.layer_CadFloorLayer.visible = true;
    this.manager.layer_CadRoomFrameLayer.visible = true;
    this.manager.layer_CadFurnitureLayer.visible = false;
    this.manager.layer_LightingLayer.visible = false;
    this.manager.layer_OutLineLayer.visible = true;
    this.manager.layer_CadRoomNameLayer.visible = true;
    this.manager.layer_CadEzdxfLayer.visible = true;
    this.manager.layer_CadCabinetLayer.visible = false;
    this.manager.layer_CadCopyImageLayer.visible = false;

    if (this.manager.drawing_layers[CadDrawingLayerType.CadDecorates]) {
      this.manager.drawing_layers[CadDrawingLayerType.CadDecorates].visible = false;
    }
    if (this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing]) {
      this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing].visible = false;
    }

    if (this.manager.layer_CadCopyImageLayer?.img?.src) {
      this.manager.layer_CadCopyImageLayer.visible = true;
    }

    if (this.layer_dimension_wall_element) {
      this.layer_dimension_wall_element.visible = false;
    }

    // if (this.layer_dimension_outter_wall_element) {
    //   this.layer_dimension_outter_wall_element.visible = false;
    // }

    if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.updateCandidateRects();
    }
    this.manager.onLayerVisibilityChanged();


    // this.manager.layout_container.focusCenter();
    // this.update();
  }
  leave(state?: number): void {
    super.leave(state);
    if (this.layer_dimension_wall_element) {
      this.layer_dimension_wall_element.visible = false;
    }
    // if (this.layer_dimension_outter_wall_element) {
    //   this.layer_dimension_outter_wall_element.visible = false;
    // }
    this.manager.layout_container.updateEntityRelations();
    this.manager.layout_container.updateRoomsFromEntities();
    for (let room of this.room_list) {
      this.manager.layout_container.addFunitureEnitiesInRoom(room, true);
    }

    this.manager.layout_container.cleanDimension();
    this._cad_default_sub_handler.cleanSelection();

    console.log('leave AIHouseDesignMode');
  }

  onmousedown(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      this._active_sub_handler.onmousedown(ev);
    } else if (this._cad_default_sub_handler) {
      // 默认方法mouse_down后, 有可能触发进入新的active_handler
      this._cad_default_sub_handler.onmousedown(ev);

      if (this._active_sub_handler) {
        this._active_sub_handler.onmousedown(ev);
      }
    }

    this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

    this._is_painter_center_moving = false;
  }
  onmousemove(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmousemove(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmousemove(ev);
      }
      super.onmousemove(ev);
    }
  }
  onmouseup(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmouseup(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmouseup(ev);
      }
      super.onmouseup(ev);
    }
  }

  onwheel(ev: WheelEvent): void {
    if (this._active_sub_handler) {
      super.onwheel(ev);
      this._active_sub_handler.onwheel(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onwheel(ev);
      }
      super.onwheel(ev);
    }
  }

  onkeydown(ev: KeyboardEvent): boolean {
    super.onkeydown(ev);
    if (ev.key == 'Delete') {
      console.log('delete...');
      this._cad_default_sub_handler.deleteElement();
    }
    return true;
  }




  async runCommand(cmd_name: string): Promise<void> {
    if (!cmd_name) return;

    await super.runCommand(cmd_name);
    if (cmd_name === LayoutAI_Commands.Reset) {
      this.reset();
    }
    // 删除
    else if (cmd_name === LayoutAI_Commands.DeleteFurniture) {
      this._cad_default_sub_handler.deleteElement();
    } else if (cmd_name === LayoutAI_Commands.RotateFurniture) {
      this._cad_default_sub_handler.rotate();
    }
  }

  async handleEvent(event_name: string, event_param: any) {
      super.handleEvent(event_name, event_param);
      if (event_name === LayoutAI_Events.DrawShapeWall) {
        this.shape_draw_type = event_param;
        LayoutAI_App.RunCommand(LayoutAI_Commands.DrawShapeWall);
      }

      if(event_name === LayoutAI_Events.DrawingBuildingSpace)
      {
        LayoutAI_App.RunCommand(LayoutAI_Commands.DrawingBuildingSpace);   
      }
  }


  reset() {
    this.manager.layout_container.fromXmlSchemeData(
      this._initial_scheme_data as any as I_SwjXmlScheme
    );
    this._cad_default_sub_handler.cleanSelection();
    this._cad_default_sub_handler.updateCandidateRects();
    this.update();
  }
  drawCanvas(): void {
    if (this.manager.layer_DefaultBatchLayer) {
      let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
      if (!batchDirty) {
        if (
          this.manager.layout_container.checkIfOutOfView() &&
          this.manager.layer_DefaultBatchLayer.cleaned_drawing_count > 10
        ) {
          batchDirty = true;
        }
      }
      this.manager.layer_DefaultBatchLayer.drawLayer(batchDirty);
    }

    if (this.layer_dimension_wall_element) {
      if (this.layer_dimension_wall_element.visible) {
        this.layer_dimension_wall_element.onDraw();
      }
    }
    // if (this.layer_dimension_outter_wall_element) {
    //   if (this.layer_dimension_outter_wall_element.visible) {
    //     this.layer_dimension_outter_wall_element.onDraw();
    //   }
    // }

    if (this._base_space_layer && this._base_space_layer.visible) {
      this._base_space_layer.onDraw();
    }
    if (this._active_sub_handler) {
      this._active_sub_handler.drawCanvas();
    } else if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.drawCanvas();
    }
  }
}
