import { Vector3 } from "three";
import { <PERSON><PERSON><PERSON>, ZPolygon, ZRectShapeType } from "@layoutai/z_polygon";
import { ZRectShape } from "@layoutai/z_polygon";
import { MinHallwayWidth } from "../IRoomInterface";
import { I_TRoom, TRoomShape } from "../TRoomShape";
import { TFeatureShape } from "./TFeatureShape";


export class T_L_Shape extends TFeatureShape
{
    constructor()
    {
        super();
        this.shape_code = "L";
        this._order = 1;

    }


    checkPolyFeature(poly : ZPolygon)
    {
        // if(poly.edges[1].length < HallwayWidth)
        // {
        //     return false;
        // }
        let tlen = Math.min(poly.edges[2].length, poly.edges[3].length);
        if(tlen < 200)
        {
            return false;
        }
        // for(let edge of poly.edges)
        // {
        //     if(edge.dv.dot(edge.next_edge.dv) < -0.9)
        //     {
        //         return false;
        //     }
        // }

        for(let edge of poly.edges)
        {
            if(edge.length < MinHallwayWidth * 1.1)
            {
                if(edge.prev_edge.dv.dot(edge.next_edge.dv)<-0.5)
                {
                    return false;
                }
            }
        }

        return true;
    }

    fitPolygon(poly:ZPolygon)
    {
        if(poly.vertices.length != 6)
        {
            return null;
        }

        let bbox = poly.computeBBox();

        let size = bbox.getSize(new Vector3());

        let nor = new Vector3(1,0,0);
        if(size.y > size.x)
        {
            nor.set(0,1,0);
        }

        let back_edge :ZEdge = null;
        for(let edge of poly.edges)
        {
            if(Math.abs(edge.dv.dot(nor)) <0.1)
            {
                if(!back_edge || edge.length > back_edge.length)
                {
                    back_edge = edge;
                }
            }
        }

        if(!back_edge)
        {
            console.log("Error back edge nor found!",this._room._t_id);
            return null;
        }
        let l_rect = new ZRectShape(1,1);
        l_rect._shape = ZRectShapeType.LShape;

        nor = back_edge.next_edge.dv.clone();
        l_rect._nor.copy(nor);
        l_rect._back_center.copy(back_edge.center);
        l_rect._w = back_edge.length;
        
        
        if(back_edge.next_edge.length > back_edge.prev_edge.length)
        {
            l_rect.u_dv = back_edge.dv.clone();
            l_rect._h = back_edge.prev_edge.length;
            l_rect._l_extrude_height = back_edge.next_edge.length - back_edge.prev_edge.length;
            l_rect._l_extrude_width = back_edge.next_edge.next_edge.length;
            
        }
        else{
            l_rect.u_dv = back_edge.dv.clone().negate();

            l_rect._h = back_edge.next_edge.length;
            l_rect._l_extrude_height = back_edge.prev_edge.length - back_edge.next_edge.length;
            l_rect._l_extrude_width = back_edge.prev_edge.prev_edge.length;
        }


        l_rect.updateRect();

        if(!this.checkPolyFeature(l_rect))
        {
            return null;
        }

        return l_rect;
    }
    findFeatureShape(room:I_TRoom): number {

        let target_poly : ZRectShape = null;
        let target_shape : TRoomShape = null;
        this._room_shapes = [];
        this._poly = null;
        this._room = room;
        for(let shape of room.shape_list)
        {
            let ans = this.fitPolygon(shape._poly);
            if(ans)
            {
                if(!target_poly || (target_poly._w < ans._w))
                {
                    target_poly = ans;
                    target_shape = shape;
                }
            }
        }

        if(target_poly)
        {
            this._poly = target_poly;
            this._room_shapes.push(target_shape);
            return this.getFitAreaScore();
        }
        return 0;
    }

    _updateShapeEdgeProp(): void {
        super._updateShapeEdgeProp();
        
    }


}