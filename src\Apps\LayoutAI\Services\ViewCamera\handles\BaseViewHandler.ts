import { BaseViewCameraRule } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/ViewCameraRules/BaseViewCameraRule";
import { ViewCameraRulerHandler } from "./ViewCameraRulerFactory";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { ViewCameraRuler } from "../ViewCameraRuler";
import { IType2UITypeDict } from "../../../Layout/IRoomInterface";
import { TRoomEntity } from "../../../Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "../../../Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";

export abstract class BaseViewHandler extends BaseViewCameraRule implements ViewCameraRulerHandler {
    abstract handle(
        ruler: ViewCameraRuler,
        roomEntity: TRoomEntity,
        options: IViewCameraGenerateOptions
    ): TViewCameraEntity[];

    /**
     * @description 分区条件是否满足, 如果满足则返回true, 否则返回false
     * @param ruler 灯光规则
     * @param subRoomAreas 分区列表,一个满足即可
     * @return boolean
     */
    protected checkCondition(ruler: ViewCameraRuler, roomEntity: TRoomEntity): boolean {
        // 房间ID条件
        let subRoomAreas: TSubSpaceAreaEntity[] = roomEntity._sub_room_areas;
        let roomId = ruler?.condition?.roomId;
        if (roomId) {
            let roomIds = (roomId as string).trim().split("|");
            return roomIds.includes(roomEntity._room.uuid);
        }

        // 分区条件
        let spaceArea = ruler?.condition?.spaceArea;
        if (spaceArea) {
            // 多个区域用|分隔
            let spaceAreaNames = spaceArea.trim().split("|");
            // 分区列表
            let areaNames = subRoomAreas.map(area => IType2UITypeDict[area.space_area_type]);
            // 判断是否满足条件
            return spaceAreaNames.some(name => areaNames.includes(name));
        }

        // 房间名称条件
        let roomName = ruler?.condition?.roomName;
        if (roomName) {
            let roomNames = (roomName as string).trim().split("|");
            return roomNames.includes(roomEntity._room.name);
        }

        return true;
    }

    protected getViewCameraPosByRect(
        ruler: ViewCameraRuler,
        rect: ZRect,
    ): { x: number; y: number; z: number } {
        let center = rect.rect_center_3d;
        let nor = rect.nor;
        let pos = { x: center.x, y: center.y, z: center.z };

        if (!ruler.pose) {
            return pos;
        }
        // 绝对位置
        if (ruler.pose.x) {
            pos.x = ruler.pose.x;
        }
        if (ruler.pose.y) {
            pos.y = ruler.pose.y;
        }
        if (ruler.pose.z) {
            pos.z = ruler.pose.z;
        }

        // x y 偏移
        if (ruler.pose.norOffset) {
            let vec = new Vector3(pos.x, pos.y, 0);
            vec.add(nor.clone().multiplyScalar(ruler.pose.norOffset));
            pos.x = vec.x;
            pos.y = vec.y;
        } else if (ruler.pose.gapOffset) {
            // let width = this.getSize(ruler.lighting.width, rect.h);
            // let wDiff = (rect.w + width) * 0.5 + ruler.pose.gapOffset;
            // let norOffset = ruler.pose.gapOffset + wDiff;
            // let vec = new Vector3(pos.x, pos.y, 0);
            // vec.add(nor.clone().multiplyScalar(norOffset * 0.5));
            let norOffset = rect.h * 0.5 + ruler.pose.gapOffset;
            let vec = new Vector3(pos.x, pos.y, 0);
            vec.add(nor.clone().multiplyScalar(norOffset));
            pos.x = vec.x;
            pos.y = vec.y;
        }

        return pos;
    }

    protected getViewCameraPosByPoint(
        ruler: ViewCameraRuler,
        point: Vector3,
        nor: Vector3
    ): { x: number; y: number; z: number } {
        let pos = { x: point.x, y: point.y, z: point.z };

        if (!ruler.pose) {
            return pos;
        }
        // 绝对位置
        if (ruler.pose.x) {
            pos.x = ruler.pose.x;
        }
        if (ruler.pose.y) {
            pos.y = ruler.pose.y;
        }
        if (ruler.pose.z) {
            pos.z = ruler.pose.z;
        }

        // x y 偏移
        if (ruler.pose.norOffset) {
            let vec = new Vector3(pos.x, pos.y, 0);
            vec.add(nor.clone().multiplyScalar(ruler.pose.norOffset));
            pos.x = vec.x;
            pos.y = vec.y;
        }
        // gapOffset暂时无效

        return pos;
    }

    protected createViewCameraEntity(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        rect: ZRect,
        target: string[],
        roomEntity: TRoomEntity,
        name?: string
    ): TViewCameraEntity {
        let entity = new TViewCameraEntity();
        entity.rect.copy(rect);
        entity._room_entity = roomEntity;
        entity._target = target;

        const { ukey, fov, near, far, direction, hideNames } = ruler.viewCamera;
        entity.ukey = ukey;
        if (fov) entity.fov = fov;
        if (near) entity.near = near;
        if (far) entity.far = far;
        if (direction) entity._dir = direction;
        if (hideNames) entity._hide_names = hideNames;
        entity._is_focus_mode = options.no_focus_mode || false ? false : true;
        entity._view_center = rect.rect_center.clone();

        entity.name = ruler.name + "-朝向" + target.join("") + entity._dir;
        if(name) entity.name = name;

        return entity;
    }
}
