
import {I_TRoom } from "../TRoomShape";
import { TFeatureShape } from "./TFeatureShape";

/**
 *  去掉过道后的简单形状, 编码是I --- Simple中 的i
 */
export class T_Simple_Shape extends TFeatureShape
{
    constructor()
    {
        super();
        this.shape_code = "I";
        this._order = 1;

    }


    findFeatureShape(room: I_TRoom): number {
       this._room = room;
       this.shape_code = "I";

       if(!room.valid_shape_list || room.valid_shape_list.length == 0)
       {
            return 0.;
       }
       // 取最大有效形状的poly
       this._poly = room.valid_shape_list[0]._poly.clone(); 

       this._initContours();
       return 1.;
   
    }


}