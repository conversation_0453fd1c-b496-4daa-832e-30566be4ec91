import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        previewContainer: css`
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1000;
        overflow: auto;
    `,
    previewMask: css`
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.7);
        transition: opacity 0.3s;
    `,
    previewWrap: css`
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
    `,
    previewBody: css`
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    `,
    previewImage: css`
        display: block;
        position: relative;
        margin: 0 auto;
        left: 0;
    `,
    operationsBar: css`
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 15px;
        padding: 10px 20px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 25px;
    `,
    }
});
