import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
    panel: css`
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        // 平板样式
        @media screen and (max-width: 1024px) { // 平板宽度
            width: 700px !important;
        }

        @media screen and (max-width: 768px){
            width: 600px !important;
        }

        // 手机样式
        @media screen and (max-width: 450px) { // 手机宽度
            width: 300px !important;
        }
        .swj-baseComponent-Containersbox-body div
        {
            @media screen and (max-width: 1024px) { // 平板宽度
                height: 450px !important;
            }

            // @media screen and (max-width: 768px){
            //     width: 600px !important;
            // }

            // // 手机样式
            // @media screen and (max-width: 450px) { // 手机宽度
            //     width: 300px !important;
            // }
        }
    `,
    contain: css`
        display: flex;
        width: 100%;
        /* padding: 20px; */
    `,
    left: css`
        border-radius: 9px;
        background-color: #F4F5F5;
        display: flex;
        align-items: center;
        img {
            width: 350px;
            height: auto;
            margin-top: 0;
        }
    `,
    right: css`
        display: flex;
        flex-direction: column;
        margin-left: 30px;
        flex-grow: 1;
    `,
    name: css`
        color: #282828;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 24px;
        line-height: 1.33;
        letter-spacing: 0px;
        text-align: left;
        margin-top: 10px;
    `,
    schemeDetailsContainer: css`
        display: flex;
        width: 100%;
        gap: 50px;
        @media (max-width: 900px) {
        display: block;
        }
    `,
    schemeDetails: css`
        display: grid;
        grid-template-columns: auto 1fr;
        gap: 16px;
        margin-top: 32px;
        @media (max-width: 900px) {
            margin-right: 0;
        }
    `,
    btnDesign: css`
        width: 120px;
        height: 40px;
        color: #ffffff;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 16px;
        line-height: 1.5;
        letter-spacing: 0px;
        margin-right: 16px;
    `,
    label: css`
    color: #282828;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
    `,
    value: css`
    color: #5b5e60;
    font-family: PingFang SC;
    font-weight: normal;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
    `,
    btnContain: css`
    display: flex;
    margin-top: 50px;
    margin-bottom: 10px;
    .btnPPT {
        margin-left: 16px;
        width: 160px;
        height: 40px;
        color: #ffffff;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 16px;
        line-height: 1.5;
        letter-spacing: 0px;
        border: none;
        border-radius: 8px;
        background: #52c41a;
        text-align: center;
        transition: background 0.3s;

        &:hover {
        background: #61d825 !important;
        color: white !important;
        }
    }
    `,
    tabTitle: css`
    color: #5B5E60;
    font-family: PingFang SC;
    font-weight: normal;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
    `,
    activeTab: css`
    color: #282828;
    font-family: PingFang SC;
    font-weight: 540;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
    `,
    infoGrid: css`
        margin-top: 30px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 18px 20px;
        margin-bottom: 32px;
        @media (max-width: 900px) {
        grid-template-columns: 1fr;
        gap: 12px 0;
    }`,
    infoItem: css`
        display: flex;
        align-items: center;
        min-width: 0;
    `,
    idValue: css`
        display: inline-block;
        height: 21px;
        max-width: 180px;
        overflow-x: auto;
        white-space: nowrap;
        vertical-align: bottom;
        color: #5b5e60;
        font-family: PingFang SC;
        font-size: 14px;
        /* 隐藏滚动条美化，可选 */
        &::-webkit-scrollbar {
            height: 4px;
            background: #eee;
        }
        &::-webkit-scrollbar-thumb {
            background: #ccc;
            border-radius: 2px;
    }`,
}));