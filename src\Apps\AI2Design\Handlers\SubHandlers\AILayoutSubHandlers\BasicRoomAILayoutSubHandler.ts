
import { AI2Base<PERSON>odeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { EventName } from "@/Apps/EventSystem";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TRoomLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme";

import { I_PainterTransform } from "@layoutai/z_polygon";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";

import { Vector3 } from "three";
import { ZRect } from "@layoutai/z_polygon";


export class BasicRoomAILayoutSubHandler extends CadBaseSubHandler
{
    handler : AI2BaseModeHandler;

    _origin_layout_scheme : TRoomLayoutScheme;

    _painter_ts : I_PainterTransform;

    _previous_rect: ZRect;
    _last_pos: Vector3;

    constructor( handler:AI2BaseModeHandler)
    {
        super(handler);
        this.name = "BasicRoomAILayout";
        this.handler = handler;
        this._origin_layout_scheme = null;
        this._painter_ts = null;

        this._previous_rect = null;

        this._last_pos = null;
    }   

    get current_room()
    {
        return this.manager.layout_container._selected_room;
    }
    
    enter(state?: number): void {
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn,true);
        this.manager.layout_container._drawing_layer_mode = "SingleRoom";

        if(!this.current_room)
        {
            LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }

        this._painter_ts = this.painter.exportTransformData();
        this.updateCandidateRects();

        this.focusRoom(true);
        this.update();
    }
    leave(state?: number): void {
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList,true);
        LayoutAI_App.emit(EventName.ShowSchemeTestingLeftPanel,false);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn,false);

        this.manager.layout_container._drawing_layer_mode = "FullHouse";
        this.focusRoom(false);
        // this.handler._draw_default_layers = true;
        this.update();

    }

    focusRoom(t:boolean)
    {
        if(t)
        {
            if(this.current_room)
            {
                this.container.focusOnRoom(this.current_room);


            }
        }
        else{

            if(this._painter_ts)
            {
                this.painter.importTransformData(this._painter_ts,false);

            }
        }
    }

    appendCurrentLayoutScheme()
    {
        if(!this.current_room) return;

        this.manager.layout_container.addFunitureEnitiesInRoom(this.current_room);
    }

    postProcessInRoom(room:TRoom)
    {

    }
    onmousedown(ev: I_MouseEvent): void {

        let pos = { x: ev.posX, y: ev.posY, z: 0 };

        this.updateSelectedRect(pos);

        this.updateExsorbRects();

        if(!this.selected_target.selected_rect)
        {
            if(this.current_room?._room_entity?.rect)
            {
                this.selected_target.selected_rect = this.current_room._room_entity.rect;
            }
        }


        this._cad_mode_handler._is_moving_element = false;

        this.updateAttributes("init");

        if (!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect) {
            return;
        }
        if (this.selected_target && this.selected_target.selected_rect) {
            this._cad_mode_handler._is_moving_element = true;
        }
        else {
            this._cad_mode_handler._is_moving_element = false;
        }

        if(this.selected_target.selected_rect)
        {
            this.EventSystem.emit_M(EventName.SelectingTarget, this.selected_target.selected_rect, ev._ev);
        }
        else{
            this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);

        }
        
        this._previous_rect = this.selected_target.selected_transform_element._target_rect.clone();

        if(this.selected_target.selected_combination_entitys.length > 0)
        {
            this.selected_target.selected_transform_element.recordOriginCombinationRect(this.selected_target.selected_combination_entitys);
        }
        this.selected_target.selected_transform_element.recordOriginRect(this._previous_rect);
        this.selected_target.selected_transform_element.startTransform(this.exsorb_rects);
        this._last_pos = new Vector3(ev.posX, ev.posY, 0);

    }
    onmousemove(ev: I_MouseEvent): void {

        if(!this.handler._is_moving_element)
        {
            super.onmousemove(ev);
        }
        else{
            if (ev.buttons != 1) return;

            let transform_element = this.selected_target.selected_transform_element;
            if (!transform_element) return;
            let current_pos = new Vector3(ev.posX, ev.posY, 0);
    
            let movement = current_pos.clone().sub(this._last_pos);
    
            transform_element.applyTransformByMovement(movement,this.exsorb_rects);
    
        
            this.updateTransformElements();
    
            this.handler._is_moving_element = false;
            this.update();
            this.handler._is_moving_element = true;
        }

        // this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);
    }

    onmouseup(ev: I_MouseEvent): void {
        this._cad_mode_handler._is_moving_element = false;


        if (this.selected_target.selected_transform_element) {
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info) {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);
            // 对齐线吸附
            this.selected_target.selected_transform_element.alignAdsorption();

        }
        this.updateCandidateRects();

        this._cad_mode_handler._is_moving_element = false;

        this.update();

    }
    onkeyup(ev: KeyboardEvent): boolean {
        return true;
    }

    drawCanvas(): void {
        super.drawCanvas();
    }
    handleEvent(evt_name: string, evt_param: any): void {
        if(evt_name === LayoutAI_Events.ClickLayoutScheme)
        {
            
            if(this.current_room)
            {
                this.onSelectLayoutScheme(this.current_room,evt_param);

            }
        }
    }
}