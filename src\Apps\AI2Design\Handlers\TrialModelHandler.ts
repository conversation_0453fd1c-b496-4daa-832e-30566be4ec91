import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { AI2DesignBasicModes, AI2DesignManager } from "../AI2DesignManager";
import { trialDataService } from "../Services/TrialDataService";


/**
 *   体验版本模式
 */
export class TrialModelHandler extends AI2BaseModeHandler {


    constructor(manager: AI2DesignManager) {
        super(manager, AI2DesignBasicModes.RemodelingMode);
        this._transform_elements = [];
    }

    enter(state?: number): void {
        super.enter(state);
        this.runCommand(LayoutAI_Commands.SelectHouse);
        this.manager.layer_CeilingLayer.visible = false;
        this.manager.layer_CadCopyImageLayer.visible = false;
        this.update();
    }

    leave(state?: number): void {
        super.leave(state);
    }

    async runCommand(cmd_name: string): Promise<void> {
        super.runCommand(cmd_name);
    }
    async handleEvent(event_name: string, event_param: any): Promise<void> {
        super.handleEvent(event_name, event_param);

        if (event_name === LayoutAI_Events.SeriesSampleSelected) {

            this.manager.layout_container.updateRoomsFromEntities(false);
            TSeriesFurnisher.instance.updateCurrentRooms();
            await TSeriesFurnisher.instance.onSeriesSampleSelected(event_param.scope, event_param.series);
            trialDataService.generateLayoutAndColorImage('colorScreen');
        }
    }

}