import { AI2B<PERSON><PERSON>odeHand<PERSON> } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { AI2DesignBasicModes } from "@/Apps/AI2Design/AI2DesignManager";
import { T_AddFurnitureOprationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_AddFurnitureOprationInfo";
import { T_AddOrDeleteEntityOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_AddOrDeleteEntityOperationInfo";
import { T_CleanFurnitureOpration } from "@/Apps/LayoutAI/OperationInfos/Operations/T_CleanFurnitureOpration";
import { T_CopyEntitiesOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_CopyEntitiesOperationInfo";
import { T_DeleteFurnitureOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_DeleteFurnitureOperationInfo";
import { T_DeleteStructureOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_DeleteStructureOperationInfo";
import { T_DeleteWindowOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_DeleteWindowOperationInfo";
import { T_GroupOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_GroupOpertaionInfo";
import { T_MoveOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_MoveOperationInfo";
import { EventName } from "@/Apps/EventSystem";
import { g_FigureImagePaths } from "@/Apps/LayoutAI/Drawing/FigureImagePaths";
import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { AI_PolyTargetType, DrawingFigureMode, IRoomEntityRealType, IRoomEntityType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { SolverMethods } from "@/Apps/LayoutAI/Layout/TAppSolvers/TSwjLayoutGraphSolver";
import { TPostLayoutCeiling } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutCeiling";
import { TPostDecoratesLayout } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutDecorates";
import { TRoomLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme";
import { TWholeLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TWholeLayoutScheme";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TBaseGroupEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity";
import { TCombinationEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TCombinationEntity";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TGroupTemplateEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TGroupTemplateEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TStructureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TStructureEntity";
import { TWindowDoorEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomShape } from "@/Apps/LayoutAI/Layout/TRoomShape";
import { I_MouseEvent, compareNames } from "@layoutai/z_polygon";
import { Logger } from "@/Apps/LayoutAI/Utils/logger";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { checkIsMobile } from "@/config";
import { Box3, Vector3, Vector3Like } from "three";
import { SubHandlerBase } from "../SubHandlerBase";
import { notification, Modal, Layout } from "@svg/antd";
import { NotificationPlacements } from "@/Apps/LayoutAI/Layout/IUIInterface";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { T_UpdateLayoutOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_UpdateLayoutOperationInfo";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { roomSubAreaService } from "@/Apps/LayoutAI/Services/Basic/RoomSubAreaService";
import { T_MoveElement } from "../../../../LayoutAI/Layout/TransformElements/T_MoveElement";
import { T_ReplaceFurnituresOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_ReplaceFunituresOperationInfo";
import { FilterTransformElementMode, T_TransformElement } from "../../../../LayoutAI/Layout/TransformElements/T_TransformElement";
import { LayoutAI_Configs } from "@/Apps/LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs";

export class CadBaseSubHandler extends SubHandlerBase {
    _cad_mode_handler: AI2BaseModeHandler;

    _drawing_rect: ZRect;
    _current_rect: ZRect;
    _old_rect: ZRect;
    _p_sc: number;
    _adding_figure_entity: TBaseEntity;
    group_rect: ZRect;


    _combination_entity: TCombinationEntity;

    current_rooms: TRoom[];
    _debug: boolean = true;
    _openRuler: boolean;
    _is_painter_center_wheel: boolean;
    private debounceTimer: number | any = null;
    _dblclick_group_layout_edit_tips: boolean;
    _dblclick_group_matched_edit_tips: boolean;

    /**
     *  外部扩展的绘制函数
     */
    _ex_ondraw_func: (painter: TPainter) => void;

    // 添加长按检测变量
    protected _longPressThreshold: number = 15; // 长按阈值
    protected _isLongPressFlag: boolean = false;
    protected _mouseDownPos: Vector3Like = null;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler.manager, "CadModeBaseSubHandler");

        this._cad_mode_handler = cad_mode_handler;

        this._drawing_rect = new ZRect(1, 1);
        this._current_rect = null;
        this._p_sc = 0.1;
        this._adding_figure_entity = null;
        this._combination_entity = null;

        this._openRuler = false;
        this.current_rooms = [];
        // CadBaseSubHandler._combination_edit = false;
        this._is_painter_center_wheel = false;

        this._dblclick_group_layout_edit_tips = false;
        this._dblclick_group_matched_edit_tips = false;

        this._mouseDownPos = null;
    }
    get container() {
        return this.manager.layout_container;
    }
    get current_room() {
        return this.manager.layout_container._selected_room;
    }
    // get ai_cad_data() {
    //     return this._cad_mode_handler.ai_cad_data;
    // }

    get transform_elements() {
        return this._cad_mode_handler.transform_elements;
    }

    get candidate_rects() {
        return this._cad_mode_handler.candidate_rects;
    }

    get exsorb_rects() {
        return this._cad_mode_handler.exsorb_rects;
    }

    get selected_target() {
        return this.container.entity_selector.selected_target;
    }

    get combination_target() {
        return this.container.entity_combiner.combination_target;
    }

    get furniture_entities() {
        return this._cad_mode_handler.furniture_entities;
    }

    set furniture_entities(entities: TFurnitureEntity[]) {
        this._cad_mode_handler.furniture_entities = entities;
    }
    get wall_rects() {
        return this.container.getCandidateRects(["Wall"]);
    }
    get room_list() {
        return this.manager.layout_container._rooms;
    }

    set existingInput(data: HTMLInputElement) {
        this._cad_mode_handler._existingInput = data;
    }
    updateScene3D() {
        if (this.manager && this.manager.scene3D && this.manager.scene3D.isValid()) {
            this.manager.updateScene3D();
        }
    }
    updateCandidateRects() {
        // 还是要考虑图层的显示问题，比如隐藏家具图元，this.candidate_rects不添加家具图元
        let ignore_realtypes: IRoomEntityRealType[] = [
            "Decoration",
            "Electricity",
            ...(!this.manager.layer_CadFurnitureLayer.visible ? ["SoftFurniture" as IRoomEntityRealType] : []),
            ...(!this.manager.layer_CadCabinetLayer.visible ? ["Cabinet" as IRoomEntityRealType] : []),
            ...(!this.manager.layer_LightingLayer.visible ? ["Lighting" as IRoomEntityRealType] : [])
        ];
        let targets: IRoomEntityType[] = ["RoomArea", "Wall", "Door", "Window",
        "StructureEntity", "Furniture",
        ...(this.manager.layer_CadSubAreaLayer.visible ? ["SubArea" as IRoomEntityType]:[])];

        let filter_conditions = {ignore_realtypes:ignore_realtypes};


        if (this.manager._current_handler_mode === AI2DesignBasicModes.HouseDesignMode) {
            targets =       ["RoomArea", "Wall", "Door", "Window",
            "StructureEntity"];
            filter_conditions.ignore_realtypes = ["Decoration","Electricity"];
            this.candidate_rects.push(...this.container.getCandidateRects(
                targets,filter_conditions, { "Door": 2, "Window": 2 }));

            this.container.entity_selector.updateCandidates(targets, { ignore_realtypes: ignore_realtypes });

        }
        else if (this.manager.layout_container._drawing_layer_mode == "FullHouse") {
            this.container.entity_selector.updateCandidates(targets, { ignore_realtypes: ignore_realtypes });
        }
        else {
            let targets: IRoomEntityType[] = ["RoomArea", "Furniture", "StructureEntity"];
            if (this.manager.layer_CadSubAreaLayer?.visible) {
                targets.push(AI_PolyTargetType.RoomSubArea);
            }
            this.container.entity_selector.updateCandidates(targets, { ignore_realtypes: ignore_realtypes });



        }
        this.candidate_rects.length = 0;
        // 线框图拿的是matched_rect
        this.candidate_rects.push(...this.container.entity_selector.candidate_entities.map((entity)=>this.manager.layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D ? entity.rect : entity.matched_rect || entity.rect));

        // 添加相机图元到候选矩形中
        if (this.manager.layout_container._ext_drawing_entities) {
            for (let entity of this.manager.layout_container._ext_drawing_entities) {
                if (entity.realType === "ViewCamera" && entity.rect) {
                    this.candidate_rects.push(entity.rect);
                    // 同时添加到entity_selector的候选实体中
                    if (this.container.entity_selector.candidate_entities.indexOf(entity) === -1) {
                        this.container.entity_selector.candidate_entities.push(entity);
                    }
                }
            }
        }

        this.updateExsorbRects();
    }

    updateExsorbRects() {
        this.exsorb_rects.length = 0;

        this.exsorb_rects.push(...this.container.getCandidateRects(["Wall", "StructureEntity"], { ignore_realtypes: ["Beam"] }));


        if (this.manager.layout_container._drawing_layer_mode === "SingleRoom") return;
        let other_furniture_rects: ZRect[] = [];

        let target_furniture_names: string[] = ["背景墙"];

        if (this.selected_target.selected_entity) {
            let entity: TFurnitureEntity = this.selected_target.selected_entity  as TFurnitureEntity;

            if (entity?.type === "Furniture") {
                if (entity.realType === "Cabinet") {
                    if (entity.figure_element.sub_category.indexOf("吊柜") >= 0) {
                        target_furniture_names.push("吊柜")
                    }
                    else {
                        target_furniture_names.push("地柜");

                    }
                }
            }
        }

        
        for (let entity of this.container._furniture_entities) {

            let rect = entity.rect;
            if (!rect) {
                continue;
            }
            if (TBaseEntity.is_deleted(rect)) continue;
            if (this.selected_target && this.selected_target.selected_rect) {
                if ((rect === this.selected_target.selected_rect) || (entity?.matched_rect === this.selected_target.selected_rect)) {
                    continue;
                };
            }
            if (entity && entity.figure_elements?.length > 1) continue;
            let figure = entity.figure_element;
            if (!figure) continue;

            if (compareNames([figure.sub_category, figure.category], target_furniture_names)) {
                other_furniture_rects.push(rect);
            }
            
        }
        if(this.selected_target &&this.selected_target.selected_entity?.type === "Group" && this.selected_target.selected_combination_entitys.length > 0)
        {
            for(let entity of this.selected_target.selected_combination_entitys)
            {
                if (compareNames([entity.figure_element.sub_category, entity.figure_element.category], ["背景墙"])) {
                    // 从 other_furniture_rects 中移除这个 rect
                    other_furniture_rects.splice(other_furniture_rects.indexOf(entity.rect), 1);
                }
            }
        }
        this.exsorb_rects.push(...other_furniture_rects);
    }

    updateWholeBox() {
        this.manager.layout_container.updateWholeBox();
    }
    /**
     * 
     * @param pos 
     * @param options   filterMode- 0:不考虑移动元素;  1: 仅考虑移动元素; 2: 
     * @returns 
     */
    getTransformElementContainPos(pos: Vector3Like,options:{filterMode?:FilterTransformElementMode}={filterMode:FilterTransformElementMode.Default}) {
        for (let ele of this.transform_elements) {
            if (!ele.visible) continue;
            if(options.filterMode === FilterTransformElementMode.MovingElementOnly)
            {
                if(!ele.IsMovingElement) continue;
            }
            if(options.filterMode === FilterTransformElementMode.NotMovingElementsOnly)
            {
                if(ele.IsMovingElement) continue;
            }
            if (ele.checkEditRect(pos, this._p_sc)) {
                return ele;
            }
        }
        return null;
    }

    getTextElementContainPos(pos: Vector3Like) {
        for (let room of this.container._room_entities) {
            let font_size = Math.round(38 * ((true ? Math.min(0.1, this._p_sc) : this._p_sc) / 0.15) * 10) / 10;
            this.painter._context.font = font_size + "px arial 宋体";
            let _main_rect = TRoomShape.computeMaxRectBySplitShape(room._room_poly);
            if (!_main_rect) continue;
            let measure = this.painter._context.measureText(room.name);
            let t_width = measure.width;
            let t_h = measure.actualBoundingBoxAscent + (measure.actualBoundingBoxDescent || 0) + 15;
            let rect = new ZRect(t_width, t_h);
            rect.rect_center = _main_rect.rect_center;
            let t = rect.rect_center.distanceTo(pos) < t_h / this._p_sc;
            if (!t) continue;
            return room;
        }
    }

    getTargetRectContainsPos(pos: Vector3Like) {
        let target_rect: ZRect = null;
        let max_priority = -999;

        // 备选的素材或者图元
        this.candidate_rects.forEach(rect => {
            let entity = TBaseEntity.getEntityOfRect(rect);
            if(!entity) return;
            let dist = (entity.getDistanceForMousePosSelection(pos));

            if (dist < 0) {

                // 点击位置在实体内，选择优先级高的实体
                if (entity._priority_for_selection >= max_priority) {
                    max_priority = entity._priority_for_selection;
                    target_rect = rect;
                }
            }
        });

        return target_rect;
    }
    updateHoverRect(pos: Vector3Like) {
        let target_ele = this.getTransformElementContainPos(pos);

        this.selected_target.hover_transform_element = target_ele;

        if (target_ele) {
            target_ele.onhover();
            this.manager.setCanvasCursorState(target_ele._cursor_state);
        }
        else {
            this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        }

        let hoverInfo = this.container.entity_selector.selectByPos(new Vector3().copy(pos),"Hover");
        let target_rect = hoverInfo?.target_entity?.rect || null;
        if (target_ele && target_ele.element_name === "T_rotateElement") {
            target_rect = null;
        }

        if (target_rect !== this.selected_target.hover_rect) {
            if(this.selected_target.hovered_entity)
            {
                this.selected_target.hovered_entity.is_hovered = false;
            }
            this.selected_target.hover_rect = target_rect;
            if(hoverInfo.target_entity)
            {
                this.selected_target.hovered_entity = hoverInfo.target_entity;
                hoverInfo.target_entity.is_hovered = true;
            }
            // this.updateSelectionState();
            this.update();
        }
        else if (!target_rect) {
            if(this.selected_target.hovered_entity)
            {
                this.selected_target.hovered_entity.is_hovered = false;
            }
            this.selected_target.hover_rect = null;
            this.selected_target.hovered_entity = null;
            // this.updateSelectionState();

            // this.update();
        }
    }

    updateSelectedRect(pos: Vector3Like) {

        // 重点
        let selection_info = this.container.entity_selector.selectByPos(new Vector3().copy(pos),"Select");
        


        // 使用新的辅助方法确定目标矩形

        let target_entity = selection_info.target_entity;
        // 线框图拿的是matched_rect
        let target_rect = this.manager.layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D ? target_entity?.rect : target_entity?.matched_rect || target_entity?.rect || null;
        if (target_rect !== this.selected_target.selected_rect) {
            this.selected_target.selected_rect = target_rect;
        }

        this.selected_target.selected_entity = target_entity;
        this.selected_target.selected_group_entity = selection_info.group_entity;
        this.updateTransformElements();
        let target_ele = this.getTransformElementContainPos(pos);
        this.selected_target.selected_transform_element = target_ele;
        this.selected_target.selected_combination_entitys = [];

        if (target_entity && target_entity.type === 'BaseGroup') {
            this.selected_target.selected_combination_entitys = [...(target_entity as TBaseGroupEntity).combination_entitys];
        }
        if (target_entity && target_entity.type === 'Group') {
            this.selected_target.selected_combination_entitys = [...(target_entity as TCombinationEntity).combination_entitys];
        }
        if (this.combination_target._all_sub_entitys.indexOf(target_entity as any) >= 0) {
            this.container.entity_selector.combination_edit = true;
        } else {
            if (this.combination_target._draw_combination_entitys && this.combination_target._draw_combination_entitys.length > 0) {
                this.reGenerateBaseGroup();
            }
            this.container.entity_selector.combination_edit = false;
        }

        // 和前端通信FigureElementSelected
        if(this.selected_target.selected_entity instanceof TWindowDoorEntity)
        {
            if(this.selected_target.selected_entity.realType === 'SingleDoor')
            {
                LayoutAI_App.emit_M(EventName.FigureElementSelected, this.selected_target.selected_entity?._win_figure_element);
            }else{
                LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
            }
        } else if(this.selected_target.selected_entity instanceof TFurnitureEntity 
            && this.selected_target.selected_entity?.figure_element)
        {
            LayoutAI_App.emit_M(EventName.FigureElementSelected, this.selected_target.selected_entity?.figure_element);
        } else if(this.selected_target.selected_entity instanceof TViewCameraEntity) {
            // 相机图元选中时，发送相机实体本身
            LayoutAI_App.emit_M(EventName.FigureElementSelected, this.selected_target.selected_entity);
        } else {
            LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
        }
        this.updateSelectionState();
        this.update();
    }

    // 双击进入组内编辑提示词语
    updateToolTips() {
        if (this.selected_target?.selected_entity?.type === 'BaseGroup') {
            let entity = this.selected_target.selected_entity as TBaseGroupEntity;
            // if(entity.figure_element.haveMatchedMaterial()) return;
            if (entity.figure_element.haveMatchedMaterial()) {
                if (!this._dblclick_group_matched_edit_tips) {
                    this._dblclick_group_matched_edit_tips = true;
                } else {
                    return;
                }
            } else {
                if (!this._dblclick_group_layout_edit_tips) {
                    this._dblclick_group_layout_edit_tips = true;
                } else {
                    return;
                }
            }
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }
            this.debounceTimer = setTimeout(() => {
                LayoutAI_App.emit(EventName.MessageTip, LayoutAI_App.t("双击进入组内编辑"));
                this.debounceTimer = null;
            }, 500);
        }
    }


    // 重组Base_Group
    reGenerateBaseGroup() {
        if(this.container.entity_combiner)
        {
            this.container.entity_combiner.reGenerateBaseGroup();
        }
        this.updateCandidateRects();
    }

    updateTransformElements() {
        let target_rect = this.selected_target.selected_rect;


        // 暂时只支持家具和组合

        if (target_rect &&
            (TBaseEntity.get_polygon_type(target_rect) == AI_PolyTargetType.RoomArea)) {
            target_rect = null;
        }



        for (let ele of this.transform_elements) {
            let combination_entitys = this.selected_target.selected_combination_entitys;
            ele.bindTargetRect(target_rect, combination_entitys);
        }
    }


    // 旋转s
    rotate() {

        if(this.container.entity_transformer)
        {
            this.container.entity_transformer.rotate();

            this.updateTransformElements();
            this.updateAttributes('init');
            this.update();
        }
     

    }

    // 镜像
    flip() {

        if(this.container.entity_transformer)
        {
            this.container.entity_transformer.flip();

            this.updateTransformElements();
            this.updateAttributes('init');
            this.update();
        }
     
    }

    // 上下镜像
    flipVertical() {
        if(this.container.entity_transformer)
        {
            this.container.entity_transformer.flipVertical();

            this.updateTransformElements();
            this.updateAttributes('init');
            this.update();
        }
    }

    // 清空图元
    cleanData() {
        if (this._debug) Logger.instance.log("清空图元");
        let clean_funitures_operation_info = new T_CleanFurnitureOpration(this.manager);
        clean_funitures_operation_info.redo();
        this.manager.appendOperationInfo(clean_funitures_operation_info);
        this.updateCandidateRects();
        this.cleanSelection();
        this.update()
    }


    deleteElement() {
        if (!this.selected_target.selected_rect) return;

        let selected_rect = this.selected_target.selected_rect;
        let selected_entity = this.selected_target.selected_entity;

        if (TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.Furniture ||
            TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.BaseGroup
        ) {
            let operation_info = new T_DeleteFurnitureOperationInfo(this.manager);
            operation_info._targetFurnitureEntities = [this.selected_target.selected_entity] as any;
            operation_info.selected_group_entity = this.selected_target.selected_group_entity;
            console.log(this.selected_target.selected_group_entity);
            operation_info.redo(this.manager);
            this.manager.appendOperationInfo(operation_info);
            this.selected_target.selected_combination_entitys = [];
            this.cleanSelection();
            this.update();
        }
        else if (TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.Door ||
            TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.Window) {
            let operation_info = new T_DeleteWindowOperationInfo(this.manager);
            operation_info.target_rect = this.selected_target.selected_rect;
            operation_info.redo(this.manager);
            this.manager.appendOperationInfo(operation_info);
            this.selected_target.selected_combination_entitys = [];
            this.cleanSelection();
            this.update();
        }
        else if (TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.Group) {
            let operation_info = new T_DeleteFurnitureOperationInfo(this.manager);
            operation_info._target_rects = this.selected_target.selected_combination_entitys.map((entity) => entity.rect);
            operation_info._targetFurnitureEntities = [... this.selected_target.selected_combination_entitys];

            operation_info.redo(this.manager);
            this.manager.appendOperationInfo(operation_info);
            this.cleanSelection();
            this.update();
        } else if (TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.StructureEntity) {
            let operation_info = new T_DeleteStructureOperationInfo(this.manager);
            operation_info.target_rect = this.selected_target.selected_rect;
            operation_info.redo(this.manager);
            this.manager.appendOperationInfo(operation_info);
            this.cleanSelection();
            this.update();
        }
        else if (selected_entity?.type === AI_PolyTargetType.RoomSubArea) {
            let info = new T_AddOrDeleteEntityOperationInfo("Delete", this.selected_target.selected_entity, this.manager);
            info.redo();
            this.manager.appendOperationInfo(info);

            this.cleanSelection();
            this.update();
            this.updateAttributes("init");

        }
        this.EventSystem.emit_M(EventName.SelectingTarget, null, null);
        this.updateScene3D();
        this.updateCandidateRects();
    }

    // 复制图元
    copySelectedTarget(): void {
        if (this.selected_target && this.selected_target.selected_rect && this.selected_target.selected_rect.IsZRect) {
            let copy_rect = this.selected_target.selected_rect.clone();

            let copy_offset = copy_rect.dv.clone().multiplyScalar(copy_rect.w + 100);
            let copy_opertaion_info = new T_CopyEntitiesOperationInfo(this.manager);
            copy_opertaion_info._copy_offset.copy(copy_offset);
            copy_opertaion_info._src_entities = [];

            let entity = null;
            if (this.selected_target.selected_combination_entitys && this.selected_target.selected_combination_entitys.length > 0) {
                for (let entity of this.selected_target.selected_combination_entitys) {
                    if (entity && (entity.type === "Furniture" || entity.type === "Group")) {
                        copy_opertaion_info._src_entities.push(entity);
                    }
                }
            }
            else {
                let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TFurnitureEntity;
                if (entity && (entity.type === "Furniture" || entity.type === "Group")) {
                    copy_opertaion_info._src_entities.push(entity);
                }
            }

            if (copy_opertaion_info._src_entities.length > 0) {
                copy_opertaion_info.redo();
                this.manager.appendOperationInfo(copy_opertaion_info);
            }
            
            if (this.selected_target.selected_combination_entitys.length == 0 || !this.selected_target.selected_combination_entitys) {
                if(this.manager.layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D)
                {
                    this.selected_target.selected_rect = copy_opertaion_info._copy_entities[0].rect;
                }
                else
                {
                    this.selected_target.selected_rect = this.selected_target.selected_entity.matched_rect;
                }
            }
            // this.cleanSelection();
            this.updateSelectionState();
            this.updateCandidateRects();
            this.manager.layout_container.updateRoomsFromEntities(false);
            this.update();
        }

    }

    combineSelectedCombinationRects() {
        if (!this?.selected_target?.selected_combination_entitys || this.selected_target.selected_combination_entitys.length == 0) return;
        let bbox = new Box3();
        if (!this.group_rect) {
            this.group_rect = new ZRect(1, 1);

        }
        for (let entity of this.selected_target.selected_combination_entitys) {
            let rect = this.manager.layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D ? entity.rect : entity.matched_rect || entity.rect;
            for (let v of rect.vertices) {
                bbox.expandByPoint((v.pos));
            }
        }

        let t_nor = new Vector3();


        let group_nor = ['沙发', '床', '餐桌', '书桌', '岛台', '转角沙发'];
        t_nor.set(0, -1, 0);
        for (let entity of this.selected_target.selected_combination_entitys) {
            if (entity.rect.ex_prop['label'] && group_nor.includes(entity.rect.ex_prop['label'])) {
                t_nor = entity.rect.nor.clone();
                break;
            }
        }
        this.group_rect = ZRect.fromBox3(bbox, t_nor);

        this.group_rect.ex_prop['poly_target_type'] = "Group";
        this.group_rect.updateRect();
        let group_entity = TCombinationEntity.getOrMakeEntityOfCadRect(this.group_rect) as TCombinationEntity;
        // if(this.selected_target.selected_combination_entitys.length == this.combination_target._draw_combination_entitys.length && this.group_rect.intersect(this.combination_target.draw_group_rect)) return;
        
        if (this.selected_target.selected_combination_entitys.length > 1) {
            this.selected_target.selected_entity = group_entity;
            this.selected_target.selected_rect = this.group_rect;  //赋值给选中的图元
            for (let t_entity of this.selected_target.selected_combination_entitys) {
                TCombinationEntity.recordGroupRectData(t_entity, group_entity);
            }
            let moving_element = this._cad_mode_handler.transform_moving_element;
            if (moving_element) {
                moving_element.recordOriginRect(this.group_rect);
            }
            this.addCombinationGroupRect();
        } else {
            this.selected_target.selected_rect = this.selected_target.selected_combination_entitys[0].rect;
            this.updateAttributes("init");
        }
        this.updateTransformElements();
        this.update();
    }
    addNewEntitiy() {
        if (!this.ai_cad_data._adding_figure_entity) return;
        let _adding_figure_rect = this.ai_cad_data._adding_figure_entity._rect;
        if (_adding_figure_rect) {
            _adding_figure_rect.ex_prop["is_added"] = "added";
            let info = new T_AddFurnitureOprationInfo(this.manager);
            let entity = TBaseEntity._FuncGetOrMakeEntityOfCadRect(_adding_figure_rect);
            // if (_adding_figure_rect.ex_prop['label'].indexOf("GroupTemplate:") >= 0) {
            //     let _entity = entity as TGroupTemplateEntity;
            //     let group_entity = _entity.toBaseGroupEntity() as TBaseGroupEntity;
            //     this.selected_target.selected_combination_entitys = group_entity.combination_entitys;
            //     entity = group_entity;
            // }

            info.target_rect = _adding_figure_rect;
            info._entity = entity as TFurnitureEntity | TWindowDoorEntity | TBaseGroupEntity | TStructureEntity;
            info._history_info.current_ploy = _adding_figure_rect.clone();
            info.isCopyGroup = false;
            info.redo(this.manager);
            if(entity instanceof TFurnitureEntity && entity.realType === "Lighting" && !this.manager.layer_LightingLayer.visible)
            {
                this.manager.layer_LightingLayer.visible = true;
                this.manager.onLayerVisibilityChanged();
            }


            this.manager.appendOperationInfo(info);
            if (info._entity instanceof TFurnitureEntity) {
                this.manager.layout_container.findAndBindRoomForFurnitures([info._entity]);
            }

            this.updateCandidateRects();


            this.selected_target.selected_rect = entity.matched_rect || _adding_figure_rect;
            if(entity instanceof TBaseGroupEntity)
            {
                this.selected_target.selected_group_entity = entity;
                this.selected_target.hovered_entity = entity;
                this.selected_target.selected_entity = entity;
                this.selected_target.selected_combination_entitys = entity.combination_entitys;
                this.selected_target.selected_rect = entity.rect;
                this.selected_target.hovered_entity = entity;
            }
            // this.selected_target.hover_rect = null;
            entity.is_selected = true;

            this.container.updateEntityRelations();
            this.updateAttributes("init");
            // this.cleanSelection();
        }
        // 更新素材到TRoom中
        this.manager.layout_container.updateRoomsFromEntities(false);
        // 离开当前子模式
        this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
        this._adding_figure_entity = null;
        console.log('this.selected_target',this.selected_target);
        
        this.update();
    }

    addCombinationGroupRect() {
        if (this.group_rect) {

            let entity = TCombinationEntity.getOrMakeEntityOfCadRect(this.group_rect) as TCombinationEntity;

            // 赋值给entity
            entity.combination_entitys = [...this.selected_target.selected_combination_entitys];

            this.updateCandidateRects();
            this.updateAttributes("init");
        }
        this.group_rect = null;
    }

    unGroupTemplate(group_template_entity: TGroupTemplateEntity = null) {
        if (!group_template_entity) return;

        group_template_entity.disassembled = true;
        let opertion_info = new T_GroupOperationInfo(this.manager, "UnGroup");
        opertion_info._group_base_entity = group_template_entity;
        opertion_info.redo();

        this.manager.appendOperationInfo(opertion_info);

        this.cleanSelection();
        // 我觉得解组之后就不要选中了

        this.updateCandidateRects();
        this.update();
    }

    unBaseGroup(baseGroup_entity: TBaseGroupEntity = null) {
        if (!baseGroup_entity) return;

        let opertion_info = new T_GroupOperationInfo(this.manager, "UnGroup");
        opertion_info._group_base_entity = baseGroup_entity;
        opertion_info.redo();

        this.manager.appendOperationInfo(opertion_info);
        this.selected_target.selected_combination_entitys = [];
        this.cleanSelection();
        this.updateCandidateRects();
        this.update();
    }

    makeGroupTemplate() {

    }
    toAiCadMode() {
    }

    async ai_layout_for_whole_house(append_furniture_entites: boolean = true) {
        // console.log("全屋推荐...");
        for (let room of this.room_list) {
            room._layout_scheme_list = [];
        }
        await this.container.applyRoomEntitiesWithSolvingMethods(null, ["BasicTransfer", "GroupTransfer", "SpacePartition"], {append_furniture_entites:append_furniture_entites,needs_make_group_templates:true},Logger.instance);

    }

    onmobileMouseDown(ev: I_MouseEvent) {
        // 1、第一步先判断移入的和点击的是否是同一个图元，如果是同一个才去进入对应的onselect
        // 2、如果不是同一个图元，那么就是在moseup的时候重新选中当前的图元
        // 3、如果是点击空白处，不清空选中的图元，相应的mousemove的时候就进入移动画布的状态，移动完也不清空选中的图元，也不触发任何事件
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        this.updateHoverRect(pos);
        this.updateCandidateRects();
        let target_ele = this.getTransformElementContainPos(pos);
        this.selected_target.selected_transform_element = target_ele;
        this.updateTransformElements();
        if (this.selected_target.selected_rect) {
            if (!this.selected_target.selected_transform_element) {
                let moving_element = this._cad_mode_handler.transform_moving_element;
                let moving_struture_element = this._cad_mode_handler.transform_moving_struture_element;

                if (moving_element._target_rect === this.selected_target.hover_rect && moving_element._target_rect) {
                    const type = moving_element._target_rect.ex_prop['poly_target_type'];

                    if (['Furniture', 'Group', 'BaseGroup', 'StructureEntity'].includes(type)) {
                        this.selected_target.selected_transform_element = moving_element;
                    } else if (['Door', 'Window'].includes(type)) {
                        this.selected_target.selected_transform_element = moving_struture_element;
                    }
                }
            }
        }

        if (this.selected_target.selected_rect) {
            this.updateExsorbRects();
            this.updateWholeBox();
            if (this.selected_target.selected_transform_element) {
                this.selected_target.selected_transform_element.onselect();
            }
            this._cad_mode_handler._is_moving_element = false;
            this.makeSelectedRoom();
        }
    }

    onmousedown(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.button == 2) return;

        this._isLongPressFlag = false;
        this._mouseDownPos = new Vector3(pos.x, pos.y, pos.z);

        // if (!checkIsMobile()) {

            this.updateExsorbRects();
            this.updateWholeBox();
            
            let selRect = this.selected_target.selected_rect;
            let selTransformEle :T_TransformElement = null;
            let needsUpdateSelectRect = false;
            if(selRect)
            {
                let target_ele = this.getTransformElementContainPos(pos);
                selTransformEle = target_ele;
                if(!selTransformEle)
                {
                    needsUpdateSelectRect = true;
                }                
            }
            else{
                needsUpdateSelectRect = true;
            }
            if(needsUpdateSelectRect && LayoutAI_Configs.Configs?.mouse_state_to_update_selections==="OnMouseDown")
            {
                // 重点位置
                this.updateSelectedRect(pos);
                selRect = this.selected_target.selected_rect;
            }
     

            this._cad_mode_handler._is_moving_element = true;
            this.selected_target.selected_transform_element = selTransformEle;
            if (selTransformEle) {                
                selTransformEle.onselect();
            } else {
                if(selRect)
                {
                    this.EventSystem.emit_M(EventName.SelectingTarget, this.selected_target.selected_entity, null);
                }
                else{
                    this.EventSystem.emit_M(EventName.SelectingTarget, null);

                }
            }
            

            this.makeSelectedRoom();
        // } else {
        //     this.onmobileMouseDown(ev);
        // }
    }

    onmousemove(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.buttons == 0) {
            this.updateHoverRect(pos);
        }
        if(this._mouseDownPos)
        {
            let dv = new Vector3(pos.x, pos.y, pos.z).sub(this._mouseDownPos);
            if(dv.length() > this._longPressThreshold)
            {
                this._isLongPressFlag = true;
            }
        }

        this._cad_mode_handler._is_moving_element = false;

    }
    onmouseup(ev: I_MouseEvent): void {
        
        this._cad_mode_handler._is_moving_element = false;

        // if (!checkIsMobile()) {
            let pos = { x: ev.posX, y: ev.posY, z: 0 };
            if( LayoutAI_Configs.Configs?.mouse_state_to_update_selections!=="OnMouseDown")
            {
                this.updateSelectedRect(pos);
            }

            // 更新计数器
            this.container.entity_selector.updateSelectedEntityClickedCounter();
            this.updateTransformElements();

            let entity = null;
            if (this.selected_target?.selected_rect) {
                entity =   TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
                if (entity && entity.type === "RoomArea") {
                    this.onRoomEntitySelected(entity as TRoomEntity);
                }
                else {
                    this.onRoomEntitySelected(null);
                }
                if(entity?.type === AI_PolyTargetType.RoomSubArea)
                {
                    roomSubAreaService.updateSubAreaLayoutScheme(entity as any,false);
                }
                for(let transform_element of this.transform_elements)
                {
                    if(transform_element.IsDimensionElement)
                    {
                        transform_element.startTransform(this.exsorb_rects);
                    }
                }
                
            }
            else {
                this.onRoomEntitySelected(null);
            }
            let rightTop =  this.computeRightVerical();
            let rightTopPos = null;
            if(rightTop) {
                rightTopPos = this.painter.worldToCanvas(rightTop);
            };
            this.EventSystem.emit_M(EventName.SelectingTarget, entity, ev._ev, rightTopPos);
            this.updateSelectionState();

            LayoutAI_App.emit_M(EventName.UpdateLayoutScore, true);
            this.updateAttributes("force");
        

            // 更新操作
            this.updateRoomSelected(ev);
            LayoutAI_App.emit_M(EventName.SelectingRoom, { current_rooms: TSeriesFurnisher.instance.current_rooms });
            this.updateAttributes("init");
            this.updateCandidateRects();
            this.update();

        this._isLongPressFlag = false;
        this._mouseDownPos = null;
    }

    ondbclick(ev: I_MouseEvent): void {
    }
    clickRoom(ev: I_MouseEvent) {
        if (this.selected_target.selected_rect && this.selected_target.selected_rect.ex_prop['poly_target_type'] == 'RoomArea') {
            let entity =TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TRoomEntity;
            if (entity) {
                LayoutAI_App.emit(EventName.selectRoom, entity);
                this.EventSystem.emit_M(EventName.SelectingTarget, entity, ev._ev);
            }
        } else {
            LayoutAI_App.emit(EventName.selectRoom, null);
        }
    }

    computeRightVerical() {
        if (!this.selected_target.selected_rect || !this.selected_target.selected_rect.rect_center) return null;
        let maxDvY = null;
        let rightVertex = null;
        for (let vertex of this.selected_target.selected_rect.vertices) {
            let dv = vertex.pos.clone().sub(this.selected_target.selected_rect.rect_center).normalize();
            if (dv.x > 0) {
                if (maxDvY === null || dv.y > maxDvY) {
                    maxDvY = dv.y;
                    rightVertex = vertex;
                }
            }
        }
        return rightVertex ? rightVertex.pos : null;
    }

    onMobileMouseup(ev: I_MouseEvent) {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        // 在不移动画布mouseup的时候才去触发一系列更新的动作
        if (!this._cad_mode_handler._is_painter_center_moving && !this._is_painter_center_wheel) {
            // 添加图元的时候，修复保留选中状态
            if (this.ai_cad_data._adding_figure_entity) {
                this.ai_cad_data._adding_figure_entity = null;
            } else {
                this.updateSelectedRect(pos);
            }

            this.updateAttributes("init");
            this.updateCandidateRects();
            this.updateWholeBox();

            if (this.selected_target?.selected_rect) {
                let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
                this.EventSystem.emit_M(EventName.SelectingTarget, entity, ev._ev);
                if (entity && entity.type === "RoomArea") {
                    this.onRoomEntitySelected(entity as TRoomEntity);

                    LayoutAI_App.emit(EventName.selectRoom, entity);
                }
                else {
                    this.onRoomEntitySelected(null);
                }
            }
            else {
                this.onRoomEntitySelected(null);
                this.EventSystem.emit_M(EventName.SelectingTarget, null, ev._ev);
                LayoutAI_App.emit(EventName.selectRoom, null);
            }
            this.updateSelectionState();

            LayoutAI_App.emit_M(EventName.UpdateLayoutScore, true);
            this.updateAttributes("force");
                    }
        this._is_painter_center_wheel = false;
        
        // console.log('layout_container',this.manager.layout_container._rooms);


        this.updateRoomSelected(ev);
        this.update();
    }

    protected _onMouseUpDone() {
        if (checkIsMobile()) {
            this.selected_target.hover_rect = null;
        }

    }
    onwheel(ev: WheelEvent): void {

    }

    onkeydown(ev: KeyboardEvent): boolean {

        return true;
    }

    cleanSelection() {
        // this.updateTransformElements();
        this.container.entity_selector.cleanSelection();

        this.updateSelectionState();
        this.update();
    }

    updateSelectionState() {
        this.selected_target.hovered_entity = null;
        for (let rect of this.candidate_rects) {
            let entity = TBaseEntity.getEntityOfRect(rect);

            if(!entity) continue;
            if (this.selected_target?.hover_rect === rect) {
                this.selected_target.hovered_entity = entity;
                entity.is_hovered = true;
            }
            else {
                entity.is_hovered = false;
            }
            if (this.selected_target?.selected_rect === rect) {
                entity.is_selected = true;
            }
            else {
                entity.is_selected = false;
            }
        }
        if(this.selected_target.selected_entity &&this.selected_target.selected_entity.type === "Group")
        {
            for(let entity of this.selected_target.selected_combination_entitys)
            {
                entity.is_selected = true;
            }
        }
    }

    updateRoomSelected(ev: I_MouseEvent) {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };

        let room_list = this.room_list;
        let target_room: TRoom = null;
        // 点击空间
        if (room_list && room_list.length > 0) {
            for (let room of room_list) {
                let poly = room.room_shape._poly;
                let dist = poly.distanceToPoint(pos);
                if (dist < 0) {
                    target_room = room;
                    break;
                }
            }
        }
        // 判断是否点击
        if (target_room && !target_room.selectable) {
            LayoutAI_App.emit(EventName.PerformFurnishResult, { progress: "error", message: "暂不支持其他空间的布置" });
            return;
        }
        let previousRooms = [...TSeriesFurnisher.instance.current_rooms];
        let previousRoomsLength = previousRooms.length;

        let current_rooms = TSeriesFurnisher.instance.current_rooms;
        let roomsFoundFromPrevious = (current_rooms == null || target_room == null) ? null : current_rooms.filter(function (roomItem: TRoom) {
            if (roomItem == null) return roomItem;
            return roomItem.uid == target_room.uid;
        });
        if (ev.ctrlKey) {
            if (roomsFoundFromPrevious != null && roomsFoundFromPrevious.length > 0) {
                TSeriesFurnisher.instance.current_rooms = TSeriesFurnisher.instance.current_rooms.filter(function (roomItem: TRoom) {
                    return roomItem.uid != target_room.uid;
                });
            } else {
                if (target_room != null) {
                    TSeriesFurnisher.instance.current_rooms.push(target_room);
                }
            }
        } else {
            if (target_room == null) {
                let rooms: TRoom[] = [];
                this.room_list.forEach((roomItem: TRoom) => {
                    rooms.push(roomItem);
                });
                TSeriesFurnisher.instance.current_rooms = rooms;
            } else {
                TSeriesFurnisher.instance.current_rooms = [target_room];
            }
        }
    }


    drawCanvas(): void {
        // console.info("  ****  " + this.constructor.name + ".drawCanvas() ");
        let painter = this._cad_mode_handler.painter;
        this._p_sc = painter._p_sc;
        if (this.selected_target.selected_rect) {
            LayoutAI_App.instance.setIsSelecting(true);

        } else {
            LayoutAI_App.instance.setIsSelecting(false);
        }

        if(this.container.entity_selector)
        {
            this.container.entity_selector.drawSelectTarget(this.painter,this.selected_target,{is_moving_element:this._cad_mode_handler._is_moving_element});
        }


        this.updateTransformElements();
        for (let ele of this.transform_elements) {
            ele.is_moving = this._cad_mode_handler._is_moving_element;
            ele._openRuler = this._openRuler;
            ele.isWallAdsorpt = this.selected_target.selected_transform_element?.isWallAdsorpt || false;
            ele.drawCanvas(painter);
        }

        if(this.container.entity_combiner)
        {
            this.container.entity_combiner.drawCanvas(painter);
        }
    }





    unbindTargetProperties(rect: ZRect) {
        let entity = TBaseEntity.getEntityOfRect(rect);
        if (entity) {
            entity.onPanelUpdate = null;
            entity.onHandlerUpdate = null;
        }

    }
    onEntityPanelUpdate(key: string, value: number | string) {

        if (key == "pos_z") {
            let selectEntity = this.selected_target.selected_rect._attached_elements.Entity;
            if (selectEntity instanceof TFurnitureEntity) {
                if ((selectEntity as TFurnitureEntity).roomEntity) {
                    (selectEntity as TFurnitureEntity).roomEntity.updateInRoomProperties();
                }
            }
        }
        if (key == "is_auto_ceiling") {
            let selectEntity = this.selected_target.selected_entity;

            if (selectEntity && selectEntity instanceof TRoomEntity) {
                if (!value) {
                    const placement: NotificationPlacements = "topLeft";
                    notification.config({
                        top: 50,
                        duration: 3,
                    });
                    notification.warning({
                        message: null,
                        description: LayoutAI_App.t('已锁定下吊高度，该空间柜体将自动调整高度适配吊顶'),
                        style: {
                            fontSize: 10,
                            width: 400,
                            height: 100,
                            marginRight: 20
                        },
                        placement
                    });
                }
            }
        }
        LayoutAI_App.instance.update();
    }
    bindTargetPropertiesOfRect(rect: ZRect) {

        let entity = TBaseEntity._FuncGetOrMakeEntityOfCadRect(rect);
        return this.bindTargetPropertiesOfEntity(entity);
    }

    bindTargetPropertiesOfEntity(entity: TBaseEntity) {

        if(!entity) return;
        let scope = this;
        entity.onPanelUpdate = (key: string, value: number | string) => {

            scope.onEntityPanelUpdate(key, value);
        }
        entity.onHandlerUpdate = () => {

            scope.updateAttributes("edit");
        }
        entity.forcePanelUpdate = (force: boolean) => {
            scope.updateAttributes("init");
            if (force) {
                scope.updateAttributes("force");
            }
        }

        let properties = entity.getUiProperties();
        if (entity instanceof TFurnitureEntity) {
            // 拦截家具的高度修改函数
            if (properties.hasOwnProperty("height")) {
                let oldOnChange = properties.height.onChange;
                properties.height.onChange = (value: number | string) => {
                    if (entity.isNeedConfirming(value as number)) {
                        entity.setIsConfirming(true);
                        Modal.confirm({
                            title: null,
                            content: LayoutAI_App.t('已锁定下吊高度，该空间柜体最高为') + entity.maxHeight,
                            onOk: () => {
                                entity.setIsConfirming(false);
                                entity.height = value as number;
                            },
                            onCancel: () => {
                                entity.setIsConfirming(false);
                            },
                            style: {
                                fontSize: 10,
                                width: 400,
                                height: 100,
                                marginRight: 20,
                                top: 380,
                                right: 80
                            },
                        });
                    }
                    else {
                        oldOnChange(value);
                    }
                }
            }
        }

        let params = {
            mode: 'init', // 当前模式，支持edit ,hide 
            title: entity.getTitle(), // 当前面板的标题
            properties: properties
        }

        return params;

    }


    showLeftAILayoutPanel(t: boolean) {
        if (t) {
            LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
            LayoutAI_App.emit(EventName.ShowSchemeTestingLeftPanel, true);
        }
        else {
            LayoutAI_App.emit(EventName.ShowSchemeTestingLeftPanel, false);
            LayoutAI_App.emit_M(EventName.ShowFigureMenuList, true);
        }
    }

    async computeRoom(room: TRoom) {
        let solver_methods: SolverMethods[] = ["BasicTransfer", "GroupTransfer", "SpacePartition"];

        if(room._room_entity)
        {
            if((room._room_entity as any)?.inside_room_entities?.length > 2)
            {
                return [];
            }
        }
        
        let result_scheme_list = await this.manager.layout_container.applyRoomWithSolvingMethods(room, solver_methods);

        if (!result_scheme_list || result_scheme_list.length == 0) {
            if (compareNames([room.room_type], ["卧室", "书房"]) == 0) {
                // solver_methods.push("Turing");

            }
            if (compareNames([room.room_type], ["厨房", "客餐厅", "卧室", "书房"])) {
                solver_methods.push("SpacePartition");
            }
            await this.manager.layout_graph_solver.queryModelRoomsFromServer([room], false, false);
            result_scheme_list = await this.manager.layout_container.applyRoomWithSolvingMethods(room, solver_methods);
        }
        this.update();

        return result_scheme_list || [];


    }
    onSelectLayoutScheme(room: TRoom, data: { value: TRoomLayoutScheme, index: number }) {
        let scheme = data.value;
        if (scheme.room.layoutLock) return;
        if (!room) room = scheme.room;
        if (room !== scheme?.room) return;

        // let info = new T_UpdateLayoutOperationInfo(this.manager);
        // AI_CadData._timestamp++;
        // info.history_furniture_entities = this.manager.layout_container._furniture_entities.map(item => item.clone());
        let res =  LayoutContainerUtils.onSelectLayoutScheme(room,data,this.manager.layout_container);

        let info = new T_ReplaceFurnituresOperationInfo(this.manager);
        info.record(res.removed_entities,res.added_entities);
        this.manager.appendOperationInfo(info);
        this.updateCandidateRects();

        if (this._debug) {
            Logger.instance.log("选中布局...(" + (data.index + 1).toString() + ")" + scheme._scheme_name);
            let logContent = "#############################";
            logContent += "\nSeletced Layout for " + room.name + room.uid + ": " + room.room_type + "," + room.room_size + "m²";
            room.furnitureList.forEach((fe) => {
                logContent += "\n   " + fe.toString();
            });
            Logger.instance.log(logContent);
        }
        // info.current_furniture_entities = this.manager.layout_container._furniture_entities.map(item => item.clone());
        // this.manager.appendOperationInfo(info);

    }
    onSelectWholeLayoutScheme(data: { value: TWholeLayoutScheme, index: number }) {
        let scheme = data.value;
        if (!scheme) return;


        if (this._debug) Logger.instance.log("选择了布局：全屋方案" + (data.index + 1).toString());

        let removed_furnitures = [...this.manager.layout_container._furniture_entities];
        let added_furnitures =  LayoutContainerUtils.onSelectWholeLayoutScheme(data,this.manager.layout_container);
        let info = new T_ReplaceFurnituresOperationInfo(this.manager);
        info.record(removed_furnitures,added_furnitures);
        this.manager.appendOperationInfo(info);

        this._cad_mode_handler._whole_layout_selected_index = this._cad_mode_handler.whole_layout_scheme_list.indexOf(scheme);
        this.updateCandidateRects();



    }
    makeSelectedRoom() {
        if (!this.selected_target?.selected_rect) return;
        let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
        if (entity && entity.type === "RoomArea") {
            let room_entity = entity as TRoomEntity;
            if(!room_entity._room || room_entity._room._furniture_list.length == 0)
            {
                room_entity.makeTRoom(room_entity.furniture_entities, false);
            }
        }
    }
    async onRoomEntitySelected(entity: TRoomEntity) {
        if (this.manager.layout_container._drawing_layer_mode === "SingleRoom" && !entity) {
            return;
        };

        if (!entity) {
            if (this.manager.layout_container._drawing_layer_mode !== "SingleRoom") {
                this.manager.layout_container._selected_room = null;

            }
            // this.showLeftAILayoutPanel(false);


            if (this._cad_mode_handler && this._cad_mode_handler.whole_layout_scheme_list) {
                LayoutAI_App.emit(EventName.selectRoom, null);
                LayoutAI_App.emit_M(EventName.WholeLayoutSchemeList, { schemeList: this._cad_mode_handler.whole_layout_scheme_list || [], index: this._cad_mode_handler._whole_layout_selected_index })
            }
        }
        else {
            let room_entity = entity as TRoomEntity;
            this.manager.layout_container._selected_room =  room_entity.makeTRoom(room_entity.furniture_entities, false);
            this.showLeftAILayoutPanel(true);
            room_entity.updateSpaceLivingInfo();
            this.manager.layout_container.updateSubAreasInRooms();

            LayoutAI_App.emit(EventName.selectRoom, room_entity);

            let _origin_layout_scheme = room_entity._room.toLayoutScheme();

            if (room_entity._room._layout_scheme_list && room_entity._room._layout_scheme_list.length > 0) {
                let c_room = entity._room;
                if (_origin_layout_scheme.figure_list.figure_elements.length > 0) {
                    _origin_layout_scheme.room = c_room;
                    _origin_layout_scheme._scheme_name = "DIY布局";
                    if (c_room._layout_scheme_list[0]._scheme_name.includes("DIY布局")) {
                        c_room._layout_scheme_list[0] = _origin_layout_scheme;
                    }
                    else {
                        c_room._layout_scheme_list = [_origin_layout_scheme, ...c_room._layout_scheme_list];
                        // c_room.selectIndex = c_room.selectIndex + 1;
                    }
                    _origin_layout_scheme.computeScores();
                }
                LayoutAI_App.emit(EventName.LayoutSchemeList, { schemeList: c_room._layout_scheme_list, index: c_room.selectIndex });

            }
            else {
                let result_scheme_list = await this.computeRoom(entity._room);
                let c_room = entity._room;

                _origin_layout_scheme.room = c_room;
                _origin_layout_scheme._scheme_name = "DIY布局";
                if (_origin_layout_scheme && _origin_layout_scheme.figure_list.figure_elements.length > 0) {
                    if (c_room._layout_scheme_list) {
                        c_room._layout_scheme_list = [_origin_layout_scheme, ...c_room._layout_scheme_list];
                        // c_room.selectIndex = c_room.selectIndex + 1;
                    } else {
                        c_room._layout_scheme_list = [_origin_layout_scheme];
                    }
                    _origin_layout_scheme.computeScores();
                }
                LayoutAI_App.emit(EventName.LayoutSchemeList, { schemeList: c_room._layout_scheme_list, index: c_room.selectIndex });
                this._cad_mode_handler.upudateWholeLayoutSchemeList();

            }

            if (localStorage && LayoutAI_App.IsDebug && this.container._selected_room) {
                localStorage.setItem("layout_ai_training_current_room_data", JSON.stringify(this.container._selected_room.exportExtRoomData()));
            }

        }

        if (LayoutAI_App.IsDebug) {
            TSeriesFurnisher.instance.updateCurrentRooms();
        }
        // if(this.current_room && this.current_room._furniture_list.length == 0 && this.current_room.selectable)
        // {
        //     this.onSelectLayoutScheme(this.current_room,{value:this.current_room._layout_scheme_list[0],index:0});

        // }

    }
    async updateAttributes(mode: "init" | "hide" | 'edit' | 'force' = "init", panel_type: number = 0) {
        let entity = this.selected_target.selected_entity || null;
        if (!entity) {
            if (this.selected_target.selected_rect) {
                entity = TBaseEntity._FuncGetOrMakeEntityOfCadRect(this.selected_target.selected_rect);
            }
        }

        if (!entity) {
            this.EventSystem.emit(EventName.AttributeHandle, {
                mode: "hide",
                panel_type: panel_type
            })
        }
        else if (mode === "edit") {

            entity.updateDataByRect();

            this.EventSystem.emit(EventName.AttributeUpdate);
        }
        else if (mode === "hide") {
            this.EventSystem.emit(EventName.AttributeHandle, {
                mode: "hide",
                panel_type: panel_type
            });
        }
        else if (mode === "force") {
            this.EventSystem.emit(EventName.AttributeHandle, {
                mode: "force",
                panel_type: panel_type
            })
        }
        else {

            this.EventSystem.emit(EventName.AttributeHandle,
                this.bindTargetPropertiesOfEntity(entity));
        }


    }

    handleEvent(evt_name: string, evt_param: any): void {
        if (evt_name === LayoutAI_Events.ClickLayoutScheme) {

            // this.cleanSelection();

            if (this.current_room) {
                this.onSelectLayoutScheme(this.current_room, evt_param);

            }
        }

        if (evt_name === LayoutAI_Events.ClickWholeLayoutScheme) {

            this.cleanSelection();

            this.onSelectWholeLayoutScheme(evt_param);
        }
        // 清空当前的布局
        if (evt_name === LayoutAI_Events.ClearLayout) {
            
            if (this.selected_target.selected_rect && TBaseEntity.get_polygon_type(this.selected_target.selected_rect) == 'RoomArea') {
                let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TRoomEntity;
                if (entity) {
                    entity._room.clearMatchedMaterials();
                    entity._room.furnitureList = [];
                    this.container.cleanInRoomFurnitures(entity._room);
                    // 再清除备选布局内容
                    if (entity._room._layout_scheme_list) {
                        delete entity._room._layout_scheme_list;
                        entity._room._layout_scheme_list = null;
                    }
                    if(entity._sub_room_areas.length > 0)
                    {
                        entity._sub_room_areas.length = 0;
                        this.container.updateSubAreasInRooms();
                    }
                }
            }
            // this.updateCandidateRects();
            // this.updateScene3D();
            this.update();
        }

        if (evt_name === LayoutAI_Events.ResetSize) {
            if (this.selected_target.selected_rect) {
                let r_center = this.selected_target.selected_rect.rect_center.clone();
                let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TFurnitureEntity;
                let info = g_FigureImagePaths[(this.selected_target.selected_entity as TFurnitureEntity).figure_element.sub_category];
                if (info) {
                    this.selected_target.selected_rect._w = info.length;
                    this.selected_target.selected_rect._h = info.depth;
                    entity.pos_z = info.height || 2400;
                    entity.figure_element.height = info.height || 2400;
                    this.selected_target.selected_rect.rect_center = r_center;
                    entity.updateDataByRect();
                    this.selected_target.selected_rect.updateRect();
                }
                this.updateAttributes("init");
                this.update();
            }
        }

        if (evt_name === LayoutAI_Events.HandleSwitchDrawingLayer) {
            let state: { [key: string]: boolean } = evt_param;
            for (let layer_name in state) {
                if (this.manager.drawing_layers[layer_name] !== undefined) {
                    this.manager.drawing_layers[layer_name].visible = state[layer_name];
                }

                if (this.manager.layer_CadFloorLayer) {
                    this.manager.layer_CadFloorLayer.visible = this.manager.layer_CadRoomFrameLayer.visible;
                }
            }
            this.updateCandidateRects();
            this.cleanSelection();
            this.manager.onLayerVisibilityChanged();
            this.update();
        }
        if (evt_name === LayoutAI_Events.OnExtraAttributeChange) {
            let entity = this.selected_target.selected_rect._attached_elements["Entity"];
            if (evt_param == "lock") {
                entity.locked = true;
            } else if (evt_param == "unlock") {
                entity.locked = false;
            }
        }
        if (evt_name === LayoutAI_Events.DimensionInput) {
            let input = evt_param;
            if (!input || !input.value) return;
            let num = parseFloat(input.value);
            let _nor_val = JSON.parse(input.getAttribute('_nor_v3'));
            // 编辑前的值
            let origin_num = Number(input.getAttribute('origin_num'));
            this.container.entity_transformer.onDimensionInput(num,origin_num,_nor_val);

        }
        if (evt_name === LayoutAI_Events.AutoRuler) {
            this._openRuler = evt_param.AutoRuler;
            this.update();
        }

        if (evt_name === LayoutAI_Events.UpdateCandidateRects) {
            this.updateCandidateRects();
        }

        if (evt_name === LayoutAI_Events.scale) {
            this.painter._p_sc = evt_param;
            this._is_painter_center_wheel = true;
            this.update();

        }
        if (evt_name === LayoutAI_Events.updateGroup) {
            this.reGenerateBaseGroup();
        }
        if (evt_name === LayoutAI_Events.cleanSelect) {
            this.cleanSelection();
            this.EventSystem.emit_M(EventName.SelectingTarget, null);
            this.update();
        }
        if (evt_name === LayoutAI_Events.selectRoomArea) {
            if (evt_param) {
                this.selected_target.selected_rect = evt_param._rect;
                let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
                if (entity && entity.type === "RoomArea") {
                    this.onRoomEntitySelected(entity as TRoomEntity);
                    LayoutAI_App.emit(EventName.selectRoom, entity);
                    this.updateAttributes("init");
                }
            }
            else {
                this.onRoomEntitySelected(null);
                LayoutAI_App.emit(EventName.selectRoom, null);
            }
            this.updateSelectionState();
            this.update();
        }
        if(evt_name == LayoutAI_Events.UpdateRoomSubAreaType)
        {
            if(this.selected_target?.selected_entity?.type == AI_PolyTargetType.RoomSubArea){
                let entity = this.selected_target.selected_entity as TSubSpaceAreaEntity
                roomSubAreaService.updateSubAreaType(entity, evt_param);
                this.update();
            }
        }
        if(evt_name == LayoutAI_Events.CopyRoomSubArea)
        {
            if(this.selected_target?.selected_entity?.type == AI_PolyTargetType.RoomSubArea){
                let entity = this.selected_target.selected_entity as TSubSpaceAreaEntity
                roomSubAreaService.copySubArea(entity);
                this.update();
            }
        }   
        if(evt_name == LayoutAI_Events.MaterialSquareDragup){
            this.addNewEntitiy();
            this.update();
            LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
        }
    }
}
