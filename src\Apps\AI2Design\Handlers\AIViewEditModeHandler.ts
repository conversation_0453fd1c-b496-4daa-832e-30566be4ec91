import { EventName } from "@/Apps/EventSystem";
import { TAILayoutDrawingLayer } from "@/Apps/LayoutAI/Drawing/TAILayoutDrawingLayer";
import { TAIMatchingDrawingLayer } from "@/Apps/LayoutAI/Drawing/TAIMatchingDrawingLayer";
import { TCadFloorDrawingLayer } from "@/Apps/LayoutAI/Drawing/TCadFloorDrawingLayer";
import { CadDrawingLayerType } from "@/Apps/LayoutAI/Drawing/TDrawingLayer";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { TPostFigureElementAdjust } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostFigureElementAdjust";
import { TPostDecoratesLayout } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutDecorates";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { Logger } from "@/Apps/LayoutAI/Utils/logger";
import { LayoutAI_App, LayoutAI_Commands } from '@/Apps/LayoutAI_App';
import { ENV } from "@/config";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { AI2DesignManager } from "../AI2DesignManager";
import { AIMatchingSubHandler } from "./SubHandlers/AIMatchingSubHandlers/AIMatchingSubHandler";
import { DimensionFurnitureHandler } from "./SubHandlers/CadEditSubHandlers/DimensionFurnitureHandler";
import { MoveFurnitureSubHandler } from "./SubHandlers/CadEditSubHandlers/MoveFurnitureSubHandler";
import { RotateEntityHandler } from "./SubHandlers/CadEditSubHandlers/RotateEntityHandler";
import { T_DimensionElement } from "../../LayoutAI/Layout/TransformElements/T_DimensionElement";
import { T_MoveElement } from "../../LayoutAI/Layout/TransformElements/T_MoveElement";
import { DrawingFigureMode } from "@/Apps/LayoutAI/Layout/IRoomInterface";


export class AIViewEditModeHandler extends AI2BaseModeHandler {
    //当前选中的图元
    _figure_element_selected: TFigureElement;
    _enable_hard_furnish: boolean;
    _layout_scheme_id: string = null;
    _debug: boolean = true;

    _enable_turing_layout: boolean = true;
    constructor(manager: AI2DesignManager) {
        super(manager, "AIViewEditMode");
        this._enable_hard_furnish = true;
        this.logger.quiet = ENV == "prod";

        new TPostDecoratesLayout();
        this._cad_default_sub_handler = new AIMatchingSubHandler(this);
        this._sub_handlers[LayoutAI_Commands.Transform_Moving] = new MoveFurnitureSubHandler(this);
        this._sub_handlers[LayoutAI_Commands.Transform_Dimension] = new DimensionFurnitureHandler(this);
        this._sub_handlers[LayoutAI_Commands.Transform_Rotate] = new RotateEntityHandler(this);  //进入旋转模式

        this._candidate_target_rects = [];
        this._exsorb_target_rects = [];
        this._transform_elements = [];

        this._transform_elements.push(new T_DimensionElement(0, this.manager.layout_container));
        this._transform_elements.push(new T_DimensionElement(1, this.manager.layout_container));
        this._transform_elements.push(new T_DimensionElement(2, this.manager.layout_container));
        this._transform_elements.push(new T_DimensionElement(3, this.manager.layout_container));
        this._transform_moving_element = new T_MoveElement(this._selected_target, this.manager.layout_container);

        this._transform_elements.push(this._transform_moving_element);
    }

    protected initGraphs() {
    }

    get logger() {
        return Logger.instance;
    }
    get layer_CadFloorLayer() {
        return this.manager.drawing_layers[CadDrawingLayerType.CadFloorDrawing] as TCadFloorDrawingLayer;
    }
    get layer_ai_layout_layer() {
        return this.manager.drawing_layers[CadDrawingLayerType.AILayoutDrawing] as TAILayoutDrawingLayer;
    }

    get layer_ai_matching_layer() {
        return this.manager.drawing_layers[CadDrawingLayerType.AIMatchingDrawing] as TAIMatchingDrawingLayer;

    }

    /**
     *  swj_layout_scheme_layer 的房间列表
     */
    get room_list() {
        return this.manager.layout_container._rooms;
    }

    get painter() {
        return this.manager?.painter;
    }

    async prepareTestData(): Promise<void> {
        if (this.manager.layout_container._room_entities.length == 0) {
            await this.manager._load_local_XmlSchemeData();
            this.manager.layer_CadEzdxfLayer._clean();
            this.manager.layer_CadEzdxfLayer.ezdxf_data = null;
            this.EventSystem.emit(EventName.LayoutSchemeOpened, { id: this.manager.layout_container._scheme_id, name: this.manager.layout_container._layout_scheme_name });


            this.manager.layout_container.focusCenter();

            this.manager.layout_container.needs_making_wall_xml = true;
            this._cad_default_sub_handler.updateCandidateRects();

            for (let room of this.room_list) {
                if (!room.room_shape?._feature_shape?._w_poly) {
                    room.updateFeatures();
                }

                if (room._furniture_list && room._furniture_list.length > 0) {
                    TPostFigureElementAdjust.post_remove_unvalid_onwall_elements(room);
                    TPostDecoratesLayout.post_add_decorations(room);
                }
            }
            TSeriesFurnisher.instance.current_rooms = this.room_list;
            TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering();

            LayoutAI_App.emit_M(EventName.RoomList, this.room_list);

            this.update();
        }
    }

    async prepare(load_test_data: boolean = true): Promise<void> {
        if (load_test_data) {
            await this.prepareTestData();
        }
    }

    addFigureToList(figure: TFigureElement, room: TRoom) {
        room.addFurnitureElement(figure);
    }

    async prepareschemeData() {
        // 直接读取自定义布局的数据
        for (let room of this.room_list) {
            if (!room.room_shape?._feature_shape?._w_poly) {
                room.updateFeatures();
            }

            if (room._furniture_list && room._furniture_list.length > 0) {
                TPostFigureElementAdjust.post_remove_unvalid_onwall_elements(room);
                TPostDecoratesLayout.post_add_decorations(room);
            }
        }
        LayoutAI_App.emit(EventName.PrepareProgress, { progress: "finish" });

        this.manager.layout_container.focusCenter();

        this.update();

        // 判断空间是否可选
        this.isSelectRoom();

        TSeriesFurnisher.instance.refreshRoom2SeriesSample(this.room_list);

        TSeriesFurnisher.instance.clearCurrentRooms();

        await TSeriesFurnisher.instance.tryToFurnishRooms(this.room_list);
        this._logger.uploadLogs();
        this.manager.layout_container.focusCenter();

        this.update();
    }



    enter(state?: number): void {
        super.enter(state);
        this.manager.layout_container.drawing_figure_mode = DrawingFigureMode.Texture;

        this.manager.layer_CadCopyImageLayer.visible = false;
        this.manager.layer_CadFloorLayer.visible = true;
        this.manager.layer_CadRoomFrameLayer.visible = true;
        this.manager.layer_CadCabinetLayer.visible = false;
        this.manager.layer_CadFurnitureLayer.visible = false;
        this.manager.layer_CadCabinetLayer.visible = false;
        this.manager.layer_OutLineLayer.visible = true;
        this.manager.onLayerVisibilityChanged();

        this.manager.layout_container._drawing_layer_mode = "AIViewEdit";
        this.prepareschemeData();
    }

    leave(state?: number | undefined): void {
        super.leave(state);
        this._figure_element_selected = null;
        TSeriesFurnisher.instance.current_rooms = [];
        this.manager.layer_AILayoutLayer.visible = false;
        this.manager.layout_container._drawing_layer_mode = "FullHouse";
        this.manager.layout_container.cleanDimension();
        this._cad_default_sub_handler.cleanSelection();
        this.manager.layout_container.updateRoomsFromEntities();
    }

    ondbclick(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            this._active_sub_handler.ondbclick(ev);
        }
        else if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.ondbclick(ev);
        }
    }

    onmousedown(ev: I_MouseEvent): void {
        if (ev.ctrlKey && !this._active_sub_handler) {
            this.runCommand(LayoutAI_Commands.Transform_Combination);
        }
        if (this._active_sub_handler) {
            this._active_sub_handler.onmousedown(ev);
        }
        else if (this._cad_default_sub_handler) {
            // 默认方法mouse_down后, 有可能触发进入新的active_handler
            this._cad_default_sub_handler.onmousedown(ev);

            if (this._active_sub_handler) {
                this._active_sub_handler.onmousedown(ev);
                return;
            }
        }
        this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

        this._is_painter_center_moving = false;
    }

    onmousemove(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            super.onmousemove(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onmousemove(ev);
            }
            super.onmousemove(ev);
        }
    }

    onmouseup(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            super.onmouseup(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onmouseup(ev);
            }
            super.onmouseup(ev);
        }
    }

    onwheel(ev: WheelEvent): void {
        if (this._active_sub_handler) {
            super.onwheel(ev);
            this._active_sub_handler.onwheel(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onwheel(ev);
            }
            super.onwheel(ev);
        }
    }

    onkeydown(ev: KeyboardEvent): boolean {
        super.onkeydown(ev);
        return true;
    }

    async runCommand(cmd_name: string): Promise<void> {
        super.runCommand(cmd_name);
        switch (cmd_name) {
            default:
                break;
        }
    }

    // 画图元和俯视图
    drawFurniture(furniture: TFigureElement, isSelectSeries: boolean) {
        if (furniture._ex_prop['visible'] == 0 || furniture._ex_prop['is_deleted'] == "1") return;

        if (isSelectSeries && furniture.rect._attached_elements['pictureView']) {
            if (!furniture._matched_material?.modelId) return;
            if (furniture.isMaterialMarkAsInvisible()) return;
            furniture.drawPreviewFigure(this.painter);
        } else {
            furniture.drawFigure(this.painter);
        }
    }

    drawCanvas(): void {
        if (this.manager.layer_DefaultBatchLayer && this.manager.layer_DefaultBatchLayer.visible) {
            let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
            this.manager.layer_DefaultBatchLayer.drawLayer(batchDirty);
        }

        this.drawWithActiveHandler();
    }

    drawWithActiveHandler(): void {

        if (this._active_sub_handler) {
            this._active_sub_handler.drawCanvas();
        }
        else if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.drawCanvas();
        }
    }
}