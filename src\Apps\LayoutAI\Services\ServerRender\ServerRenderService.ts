import { Camera } from "three";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { getEnv, getImgDomain } from "@svg/request";
import { IRoySceneExportResult, RoySceneExporter } from "../../Scene3D/exporters/RoySceneExporter";
import { AILightService } from "../../Scene3D/light/AILightService";
import { RenderReqOffline, CouponData } from "../../Scene3D/light/req/RenderReqOffline";
import { LightRuleService } from '../../Scene3D/light/rule/LightRuleService';
import { EventName } from '@/Apps/EventSystem';
import { AIGCService } from '../AIGC/AIGCService';
import { RenderFlag, ResolutionConfig, PanoResolutionConfig, ResolutionTag } from './OfflineRenderType';
import { Scene3D } from '../../Scene3D/Scene3D';
import { FigureViewControls } from '../../Scene3D/controls/FigureViewControls';
import { AutoLightingService } from '../AutoLighting/AutoLightService';
import { RoomSpaceAreaType } from '@layoutai/basic_data';
import { TSubSpaceAreaEntity } from '../../Layout/TLayoutEntities/TSubSpaceAreaEntity';
import { panoReplaceUrl } from "@/config";

/**
 * @description 服务端渲染服务
 * <AUTHOR>
 * @date 2025-01-08
 * @lastEditTime 2025-01-08 11:30:23
 * @lastEditors xuld
 */
export class ServerRenderService {
    // 分辨率类型
    private static _resolutionTag: ResolutionTag = ResolutionTag.SD;

    // 渲染类型
    private static _renderFlag: RenderFlag = RenderFlag.Normal;

    // 构图宽高比
    private static _ratio = {
        w: 16,
        h: 9
    };

    public static get ratio() {
        return this._ratio;
    }

    // 获取输出图片的尺寸
    public static getImageSize() {
        // pad端标准渲染修改输出图片的尺寸
        // let renderSubmitObject = LayoutAI_App.instance.renderSubmitObject;
        // if (renderSubmitObject.drawPictureMode === "render") {
        //     let canvasWidth = LayoutAI_App.instance.scene3D.viewWidth;
        //     let canvasHeight = LayoutAI_App.instance.scene3D.viewHeight;
        //     const tempDir = AIGCService.instance.handleImageRatio(renderSubmitObject.radioMode, canvasWidth, canvasHeight)
        //     return {
        //         width: Math.round(tempDir.width),
        //         height: Math.round(tempDir.height)
        //     }
        // }

        let renderSubmitObject = LayoutAI_App.instance.renderSubmitObject;
        if (renderSubmitObject.drawPictureMode === "render") {
            // ratio=1=>4/3 2->16:9 3->3:4 4->9:16 5->原图
            let ratio = renderSubmitObject.radioMode;
            // 原图
            let originWidth = LayoutAI_App.instance.scene3D.viewWidth;
            let originHeight = LayoutAI_App.instance.scene3D.viewHeight;

            let resolutionConfig = this.getResolutionConfig();
            let maxNum = resolutionConfig.maxNum;

            switch (ratio) {
                case 1:
                    return {
                        width: resolutionConfig.width,
                        height: resolutionConfig.height
                    };
                case 2:
                    return {
                        width: resolutionConfig.width1,
                        height: resolutionConfig.height1
                    };
                case 3:
                    return {
                        width: resolutionConfig.height,
                        height: resolutionConfig.width
                    };
                case 4:
                    return {
                        width: resolutionConfig.height1,
                        height: resolutionConfig.width1
                    };
                case 5:
                    if (originWidth > maxNum || originHeight > maxNum) {
                        return {
                            width: originWidth,
                            height: originHeight
                        };
                    } else {
                        if (originWidth < originHeight) {
                            return {
                                width: maxNum,
                                height: (maxNum * originHeight) / originWidth
                            };
                        } else {
                            return {
                                width: (maxNum * originWidth) / originHeight,
                                height: maxNum
                            };
                        }
                    }
            }
        }

        let w = this.getResolutionConfig().width;
        let h = (w * this.ratio.h) / this.ratio.w;
        return {
            width: w,
            height: h
        };
    }

    // 分辨率类型
    public static get resolutionTag() {
        return this._resolutionTag;
    }

    public static set resolutionTag(value: ResolutionTag) {
        this._resolutionTag = value;
    }

    // 获取分辨率配置
    public static getResolutionConfig() {
        let key = this._resolutionTag;
        let renderFlag = this.getRenderFlag();
        if (renderFlag === RenderFlag.Panorama) {
            return PanoResolutionConfig[key];
        } else if (renderFlag === RenderFlag.Normal) {
            return ResolutionConfig[key];
        }
    }

    public static getResolutionConfigByTag(tag: string) {
        let renderFlag = this.getRenderFlag();
        if (renderFlag === RenderFlag.Panorama) {
            return PanoResolutionConfig[tag];
        } else if (renderFlag === RenderFlag.Normal) {
            return ResolutionConfig[tag];
        }
    }

    // 获取最大的视口大小
    public static getMaxViewSize() {
        let regionWidth: number;
        let regionHeight: number;

        let ratio = ServerRenderService.ratio;
        let ratioW = ratio.w;
        let ratioH = ratio.h;

        let viewWidth = LayoutAI_App.instance.scene3D.viewWidth;
        let viewHeight = LayoutAI_App.instance.scene3D.viewHeight;
        if (viewWidth / viewHeight > ratioW / ratioH) {
            regionHeight = viewHeight;
            regionWidth = Math.round((regionHeight * ratioW) / ratioH);
        } else {
            regionWidth = viewWidth;
            regionHeight = Math.round((regionWidth * ratioH) / ratioW);
        }
        return {
            width: regionWidth,
            height: regionHeight
        };
    }

    /**
     * @description 获取视口区域
     * @return 矩形对角两点坐标 [326,116,1654,863]
     */
    public static getRenderRegion() {
        let renderSubmitObject = LayoutAI_App.instance.renderSubmitObject;
        let canvasWidth = LayoutAI_App.instance.scene3D.viewWidth;
        let canvasHeight = LayoutAI_App.instance.scene3D.viewHeight;

        // pad端修改裁剪区域
        if (renderSubmitObject.drawPictureMode === "render") {
            const tempDir = AIGCService.instance.handleImageRatio(
                renderSubmitObject.radioMode,
                canvasWidth,
                canvasHeight
            );
            // 计算裁剪后的 Canvas 相对屏幕左上角的坐标
            const leftTopX = (canvasWidth - tempDir.width) / 2; // 左上角 X 坐标
            const leftTopY = (canvasHeight - tempDir.height) / 2; // 左上角 Y 坐标
            const rightBottomX = leftTopX + tempDir.width; // 右下角 X 坐标
            const rightBottomY = leftTopY + tempDir.height; // 右下角 Y 坐标
            return [leftTopX, leftTopY, rightBottomX, rightBottomY];
        }

        let viewSize = this.getMaxViewSize();
        let start = {
            x: (canvasWidth - viewSize.width) / 2,
            y: (canvasHeight - viewSize.height) / 2
        };
        let end = {
            x: start.x + viewSize.width,
            y: start.y + viewSize.height
        };
        return [start.x, start.y, end.x, end.y];
    }

    /**
     * @description 导出当前方案的RoyScene
     * @return IRoySceneExportResult
     */
    public static async exportSchemeRoyScene(): Promise<IRoySceneExportResult> {
        let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let schemeId = layout_container._layout_scheme_id;
        if (!schemeId) {
            let msg = "请先保存方案";
            console.error(msg);
            return {
                success: false,
                msg: msg,
                roySceneSchemeId: "",
                roySceneId: "",
                roySceneVersion: 0
            };
        }

        const rs = new RoySceneExporter();
        let scene = LayoutAI_App.instance.scene3D.scene;
        let rsRes = await rs.exportScene(scene, schemeId);
        console.log("royScene", rsRes.roySceneSchemeId, rsRes.roySceneId, rsRes.roySceneVersion);
        return rsRes;
    }

    /**
     * 
     * @param scene3d  太阳光随着视角变化
     */
    public static handleSunLightingWithView(scene3d: Scene3D) {
            const layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
            const roomEntities = layout_container._room_entities;
            const controls = scene3d.active_controls as FigureViewControls;
            const position = scene3d.active_controls.getViewPosition(1);
    
            console.log("根据视角计算灯光");
            // 视角优先
            if (controls.target_view_entity && controls._checkViewCameraValid()) {
                let roomEntity = controls.target_view_entity._room_entity;
                // 如果是客餐厅，先看有没有分区名，否则判断当前位置距离哪个分区中心点近
                if (roomEntity?.name === "客餐厅") {
                    let areaName = controls.target_view_entity.name.split("-")[0];
                    if (areaName) {
                        if (areaName === "客厅") {
                            AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.LivingArea);
                            // console.log(roomEntity.name, RoomSpaceAreaType.LivingArea);
                        } else if (areaName === "餐厅"){
                            AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.DiningArea);
                            // console.log(roomEntity.name, RoomSpaceAreaType.DiningArea);
                            
                        }
                    } else {
                        let areas = roomEntity._sub_room_areas as TSubSpaceAreaEntity[];
                        let LivingArea = null as TSubSpaceAreaEntity; // 客厅
                        let DiningArea = null as TSubSpaceAreaEntity; // 餐厅
                        areas.forEach(area => {
                            if (area.space_area_type === "LivingArea") {
                                LivingArea = area;
                            }
                            else if (area.space_area_type === "DiningArea") {
                                DiningArea = area;
                            }
                        })
                        if (LivingArea && DiningArea) {
                            let livingAreaCenter = LivingArea.rect.rect_center;
                            let diningAreaCenter = DiningArea.rect.rect_center;
                            let livingAreaDistance = position.distanceTo(livingAreaCenter);
                            let diningAreaDistance = position.distanceTo(diningAreaCenter);
                            if (livingAreaDistance < diningAreaDistance) {
                                AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.LivingArea);
                                // console.log(roomEntity.name, RoomSpaceAreaType.LivingArea);
                            } else {
                                AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.DiningArea);
                                // console.log(roomEntity.name, RoomSpaceAreaType.DiningArea);
                            }
                        }
                    }
                } else {
                    AutoLightingService.instance.autoRoomSunLighting(roomEntity);
                    // console.log(roomEntity.name);
                }
            } else {
                roomEntities.forEach((roomEntity) => {
                    if (roomEntity.polygon.containsPoint(position)) {
                        if (roomEntity._sub_room_areas.length > 0) {
                            roomEntity._sub_room_areas.forEach(area => {
                                if (area.polygon.containsPoint(position)) {
                                    let areaType = area.space_area_type;
                                    if (areaType === RoomSpaceAreaType.LivingArea || areaType === RoomSpaceAreaType.DiningArea) {
                                        AutoLightingService.instance.autoRoomSunLighting(roomEntity, areaType);
                                        // console.log(roomEntity.name, areaType);
                                    }
                                }
                            })
                        } else {
                            AutoLightingService.instance.autoRoomSunLighting(roomEntity);
                            // console.log(roomEntity.name);
                        }
                    }
                })
            }
    }
    
    /**
    * @description 提交离线渲染任务
    * @return { success: boolean, msg: string, queueId: string }  queueId 队列 id，提交失败是为空
    */
    public static async commitOfflineRender(camera?: Camera, renderNum?: number): Promise<{
        success: boolean;
        msg: string;
        queueId: string;
        schemeId: string;
        couponData?: CouponData;
    }> {
        let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let schemeId = layout_container._layout_scheme_id;
        if (!schemeId) {
            console.error("请先保存方案");
            return { success: false, msg: "请先保存方案", queueId: "", schemeId: "" };
        }
        let scene3D = LayoutAI_App.instance.scene3D;
        if (!scene3D.isRendering()) {
            return { success: false, msg: "请打开3D预览", queueId: "", schemeId: schemeId };
        }
        ServerRenderService.handleSunLightingWithView(scene3D as Scene3D);
        let scene = scene3D.scene;
        let res = await AILightService.instance.renderSchemeOffline(
            schemeId,
            scene,
            camera,
            renderNum
        );
        if (!res?.success) {
            console.error(res.msg);
            return {
                success: false,
                msg: res.msg,
                queueId: "",
                schemeId: schemeId,
                couponData: res.couponData
            };
        }
        return {
            success: true,
            msg: "提交离线渲染任务成功",
            queueId: res.queueId,
            schemeId: schemeId
        };
    }

    /**
     * @description 获取离线渲染任务的图册url
     * @param schemeId 方案 id
     * @param atlasQueueId 图册队列 id
     * @return 图片url
     */
    public static async getOfflineRenderUrl(schemeId: string, atlasQueueId: string) {
        let targetAtlasUrl = await RenderReqOffline.instance.getTargetAtlasUrl(
            schemeId,
            atlasQueueId
        );
        console.log("targetAtlasUrl", targetAtlasUrl);
        const pageIndex = 1;
        const pageSize = 10;
        let resList = await RenderReqOffline.instance.sendAtlas(schemeId, pageIndex, pageSize);

        const env = getEnv();
        const imgDomain = getImgDomain(env);

        const item = resList.queueList.data.ReturnList[0];
        let imageResult = `${imgDomain}${item.FileIdOutPut2}`;
        console.log("imageResult", imageResult);
        return imageResult;
    }

    public static async applyLightRulerByIndex(index: number) {
        LayoutAI_App.emit(EventName.ApplySeriesSample, {
            seriesOpening: true,
            title: "正在应用灯光规则中...",
            timeout: 20000
        });
        let board = AILightService.instance.getBlackboard();
        let lights = await LightRuleService.getLightTemplate();
        let selectedTemplates = [lights[index]];
        board.setSelectedTemplates(selectedTemplates);
        let templateDataList = await LightRuleService.getTemplateLights(selectedTemplates);
        board.updateTemplatesLightData(selectedTemplates, templateDataList);
        LightRuleService.applyLightRules(templateDataList[0], true);
        LayoutAI_App.emit(EventName.ApplySeriesSample, {
            seriesOpening: false,
            title: ""
        });
    }

    /**
     * @description 获取指定queueId的图片的url
     * @param schemeId 方案ID
     * @param queueId 队列ID
     * @returns
     */
    public static async getRenderImgUrl(
        schemeId: string,
        queueId: string
    ): Promise<{ success: boolean; msg: string; url: string }> {
        const res = await RenderReqOffline.instance.sendAtlas(schemeId);
        if (!res.success) {
            return { success: false, msg: res.msg, url: "" };
        }

        const atlasList = res.queueList?.data?.ReturnList || [];
        const target = RenderReqOffline.instance.getTargetAtlas(atlasList, queueId);
        if (target && target.FileIdOutPut2) {
            const renderURL = `${getImgDomain()}/${target.FileIdOutPut2}`;
            return { success: true, msg: "获取图片url成功", url: renderURL };
        }
        return { success: true, msg: "排队中...", url: "" };
    }

    /**
     * @description 获取指定queueId的全景图的url
     * @param schemeId 方案ID
     * @param queueId 队列ID
     * @returns { success: boolean; msg: string; url: string } url: 全景图的url, 排队中为空字符串
     */
    public static async getPanoRenderImgUrl(
        schemeId: string,
        queueId: string
    ): Promise<{ success: boolean; msg: string; url: string }> {
        const res = await RenderReqOffline.instance.sendAtlas(schemeId);
        if (!res.success) {
            return { success: false, msg: res.msg, url: "" };
        }

        const atlasList = res.queueList?.data?.ReturnList || [];
        const target = RenderReqOffline.instance.getTargetAtlas(atlasList, queueId);
        if (target && target.FileIdOutPut) {
            const host = panoReplaceUrl;
            const fullUrl = `https://3vj-render.3vjia.com${target.FileIdOutPut}`;
            const viewUrl = `${host}/?schemeId=${schemeId}&queueId=${queueId}&url=${fullUrl}&t=${Date.now()}`;
            return { success: true, msg: "获取图片url成功", url: viewUrl };
        }
        return { success: true, msg: "排队中...", url: "" };
    }

    /**
     * @description 设置渲染类型, 0:普通渲染图，1全景 2鸟瞰图 3神笔视频 4 自定义材质 5 照明模拟
     * @param flag 渲染类型
     * @return void
     */
    public static setRenderFlag(flag: RenderFlag) {
        this._renderFlag = flag;
        console.log("flag", this._renderFlag);
    }

    public static getRenderFlag(): RenderFlag {
        return this._renderFlag;
    }
}

// 放入全局对象，方便调试
(globalThis as any).ServerRenderService = ServerRenderService;
