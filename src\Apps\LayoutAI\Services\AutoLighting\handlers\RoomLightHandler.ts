import { <PERSON><PERSON><PERSON>Hand<PERSON> } from "./BaseLightHandler";
import { LightingRuler } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { TRoomType } from "../../../Layout/RoomType";
import { TRoomEntity } from "../../../Layout/TLayoutEntities/TRoomEntity";
import { ZRect } from "@layoutai/z_polygon";
import { TRoomShape } from "../../../Layout/TRoomShape";

export class RoomLightHandler extends BaseLightHandler {
    
    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        let entities: TFillLightEntity[] = [];
        let layout_container = this.getLayoutContainer();
        layout_container._room_entities.forEach(roomEntity => {
            if (this.checkCondition(ruler, roomEntity)) {
                let entity = this.createRoomLight(ruler, roomEntity);
                entities.push(entity);
            }
        });
        return entities;
    }

    private createRoomLight(ruler: LightingRuler, roomEntity: TRoomEntity): TFillLightEntity {
        // 最大连线矩形
        let mainRect = roomEntity._main_rect;
        let lightZ = roomEntity.storey_height - roomEntity.ceiling_height - 30;

        // 厨房
        if (roomEntity._room.room_type === TRoomType.kitchen) {
            mainRect = this.calculateMaxAreaRemovedFurniture(roomEntity);
        }

        let entity = this._createFillLightEntity(
            ruler.lighting.intensity,
            ruler.lighting.color,
            { x: mainRect.rect_center_3d.x, y: mainRect.rect_center_3d.y, z: lightZ },
            this.getSize(ruler.lighting.length, mainRect.h),
            this.getSize(ruler.lighting.width, mainRect.w),
            0, 0, mainRect.rotation_z,
            ruler.name,
        );
        return entity;
    }

    private calculateMaxAreaRemovedFurniture(roomEntity: TRoomEntity): ZRect {
        let roomPolygon = roomEntity._room.room_shape._poly;
        let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
        let furniture_holes: ZRect[] = [];
        let floorHeight = roomEntity.floor_thickness;
        let groundThreshold = floorHeight + 100; // 容错
        furnitureList.forEach((furniture) => {
            let furnitureZ = furniture.pos_z || furniture.position.z || furniture.rect.rect_center_3d.z;
            if (furnitureZ <= groundThreshold) {
                let furnitureRect = furniture.matched_rect || furniture.rect;
                furniture_holes.push(furnitureRect);
            }
        });

        // 使用布尔运算从房间多边形中减去家具
        let sub_polys = roomPolygon.substract_polygons(furniture_holes);
        // 否则使用原始房间多边形
        if (!sub_polys || sub_polys.length === 0) {
            sub_polys = [roomPolygon];
        }
        let main_rects = sub_polys.map((poly) => {
            let rect = TRoomShape.computeMaxRectBySplitShape(poly);
            if (rect && rect.area > 0) {
                return rect;
            }
            else {
                return null;
            }
        }).filter((rect => rect));
        main_rects.sort((a, b) => b.area - a.area);
        return main_rects[0];
    }
}