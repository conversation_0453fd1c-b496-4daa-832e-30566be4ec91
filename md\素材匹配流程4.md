# TMaterialMatcher.furnishRoom() 方法分析

## 方法概述

`furnishRoom()` 是 LayoutAI 应用中的核心方法，负责为房间匹配套系素材。该方法接收四个参数：
- `roomItem`: 需要匹配套系素材的房间
- `seriesSampleItem`: 套系样本
- `logger`: 日志记录器
- `matchedMaterials`: 用于存储匹配到的素材

## 流程图

```
+---------------------+     +------------------------+     +-------------------------+
| 初始化和准备阶段    | --> | 素材匹配服务调用阶段   | --> | 匹配结果处理和后处理阶段 |
+---------------------+     +------------------------+     +-------------------------+
```

## 详细流程分析

### 1. 初始化和准备阶段

1. **锁定检查**：如果房间被锁定，则直接返回，不进行匹配。
2. **套系信息更新**：更新房间当前应用的套系信息和应用范围对应的套系。
3. **图元清理**：从房间中删除电器装饰图元。
4. **图元收集**：根据应用场景不同，收集需要匹配套系素材的图元列表：
   - 常规套系应用：过滤房间中需要匹配的图元
   - 补全套系应用：获取房间中未匹配套系素材的图元
5. **硬装图元处理**：如果需要应用硬装类别图元，则生成或获取硬装图元。
6. **范围过滤**：过滤掉当前房间应用范围外的图元。
7. **装饰图元收集**：收集所有台面家具图元上的装饰图元。
8. **组合图元处理**：识别组合类图元，收集其成员图元。

### 2. 素材匹配服务调用阶段

1. **固定装置生成**：如果需要匹配硬装类型，生成固定装置图元（如筒灯、插座等）。
2. **组合图元成员添加**：将组合类图元的成员添加到匹配列表。
3. **服务调用**：调用 `MaterialService.materialMatching` 进行素材匹配，传递参数包括：
   - 需要匹配素材的图元列表
   - 套系ID和名称
   - 种子方案ID
   - 组织ID
   - 房间类型和面积
   - 日志ID

### 3. 匹配结果处理和后处理阶段

1. **未匹配图元处理**：记录未匹配到素材的组合类图元，并更新房间中未匹配图元列表。
2. **默认素材设置**：为未匹配到素材的图元设置默认素材。
3. **素材关联**：将素材与房间关联，设置 roomUid。
4. **组合图元实体生成**：为匹配到素材的组合图元生成家具实体。
5. **素材后处理**：进入匹配素材的后处理流程，包括：
   - 调整素材尺寸和位置
   - 调整背景墙相关图元
   - 根据定制柜调整吊顶图元
   - 调整饰品高度和位置
   - 处理窗帘和吊顶素材
   - 隐藏重复素材
   - 移除多余的装饰图元
6. **顶视图材质生成**：根据不同图元类型生成顶视图材质。
7. **未匹配图元高亮**：对未匹配到素材的图元添加高亮标记。
8. **装饰图元隐藏**：如果台面家具匹配到组合素材，隐藏附加的装饰图元。

## 核心功能模块

### 图元收集与过滤
根据应用场景和条件收集需要匹配素材的图元，并过滤不符合条件的图元。

### 素材匹配服务调用
调用 MaterialService.materialMatching 接口，传递相关参数进行素材匹配。

### 匹配结果处理
处理匹配结果，包括记录未匹配图元、设置默认素材、关联素材与房间等。

### 素材后处理
对匹配到的素材进行后处理，包括调整素材尺寸、位置、生成顶视图材质等。

## 辅助方法

### makeHardFurnishingElements
生成硬装图元，包括天花吊顶、门窗、墙面和地面等。

### makeFixtureElements
生成固定装置图元，如筒灯、强电插座、开关等。

### checkAndUpdateDefaultMaterialIds
检查并更新图元的默认素材ID，为未匹配到素材的图元设置默认素材。

### generateGroupMemberMatchedEntities
根据组合图元所匹配到的组合素材，生成家具实体对象。

## 技术特点

1. **智能匹配**：根据套系样本智能匹配适合的素材。
2. **分类处理**：根据图元类型（硬装、软装、定制柜等）分别处理。
3. **组合图元支持**：支持组合类图元的素材匹配和后处理。
4. **后处理机制**：完善的素材后处理机制，确保匹配效果。
5. **默认素材兜底**：为未匹配到素材的图元提供默认素材。

## 调用依赖

- MaterialService：提供素材匹配服务
- TPostFigureElementAdjust：图元调整工具
- TPostLayoutCeiling：吊顶布局优化工具
- TPostDecoratesLayout：装饰布局优化工具
- MatchingPostProcesser：匹配后处理工具
- TDesignMaterialUpdater：设计材质更新工具 