import { AI2B<PERSON>ModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { AI2DesignBasicModes } from "@/Apps/AI2Design/AI2DesignManager";
import { SchemeXmlParseService } from "@/Apps/LayoutAI/Services/Basic/SchemeXmlParseService";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import { I_SwjXmlScheme } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { GenDateUUid, I_MouseEvent } from "@layoutai/z_polygon";
import { deflate_to_base64_str } from "@/Apps/LayoutAI/Utils/xml_utils";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { _from, _magiccubeToken, predictHouseImitateApiUrl } from "@/config";
import { Vector3 } from "three";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { t } from "i18next";
import { message } from "@svg/antd";
import { MeasureScaleRuler } from "./MeasureScaleRuler";
import { LayoutSchemeXmlJsonParser } from "@/Apps/LayoutAI/Layout/TLayoutEntities/loader/LayoutSchemeXmlJsonParser";
import { SchemeSourceType } from "@layoutai/basic_data";


export class MeasureScaleSubHandler extends CadBaseSubHandler {
    img: HTMLImageElement;
    orgin_rect: ZRect;

    mode: string;
    xmlSchemeJson: I_SwjXmlScheme;
    img_base64: string;

    private _start_pos: Vector3;
    private _end_pos: Vector3;
    private _orc_num: any;
    private _isDragging: boolean = false;
    private _activePoint: 'start' | 'end' | null = null;
    private _snapAngle: number = 5; // 将吸附角度从15度改为5度，使吸附更柔和
    private _isSnapped: boolean = false;
    private _isMovingWhole: boolean = false;
    private _moveOffset: Vector3 = null;

    private _ruler: MeasureScaleRuler;

    private _boundInputHandler: (event: Event) => void;
    // 保存上一次的mode
    private _previous_mode: string;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = "MeasureScaleSubHandler";
        this.img = new Image();
        this.mode = '';
        this.xmlSchemeJson = null;
        this.img_base64 = '';
        this._previous_mode = '';
        // 创建一个绑定的事件处理函数
        this._boundInputHandler = this.handleInputChange.bind(this);
    }

    enter(state?: number): void {
        super.enter(state);
        // 创建标尺实例
        this._ruler = new MeasureScaleRuler(this.painter._context);
        this._previous_mode = this.manager._previous_mode;
        // 显示提示
        let config = {
            key: 'measure-scale-message',
            duration: 0,
            content: t('请拖动量尺，并输入墙或标尺的真实长度').toString(),
            className: 'custom-class',
            style: {
                marginTop: '4vh',
            }
        }
        message.info(config);
    }

    // 修改设置输入框监听的方法
    private setupInputListener(): void {
        if (this._ruler && this._ruler['inputElement']) {
            const inputElement = this._ruler['inputElement'];
            // 先移除旧的监听器
            inputElement.removeEventListener('input', this._boundInputHandler);
            // 添加新的监听器
            inputElement.addEventListener('input', this._boundInputHandler);
        }
    }

    leave(state?: number): void {
        // 先清理消息
        message.destroy('measure-scale-message')

        let handler = (LayoutAI_App.instance as TAppManagerBase)._current_handler as AI2BaseModeHandler;
        if (this.xmlSchemeJson && this.img_base64) {
            LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(this.xmlSchemeJson,{schemeSource:SchemeSourceType.LayoutLibrary});
            let distance = this._start_pos.distanceTo(this._end_pos);
            let scale = ((distance * 0.05) / this._orc_num);
            this.img = new Image();
            this.img.src = this.img_base64;
            this.img.onload = () => {
                handler.manager.layer_CadCopyImageLayer.loadData(this.img, scale);
                this.update();
            }
        } else {
            handler.manager.layer_CadCopyImageLayer.loadData(null, null);
            handler.manager.layer_CadCopyImageLayer.clean();
        }
        // 重置所有状态
        this._start_pos = null;
        this._end_pos = null;
        this._orc_num = null;
        this._isDragging = false;
        this._activePoint = null;
        this._isSnapped = false;
        this._isMovingWhole = false;
        this._moveOffset = null;
        this._previous_mode = '';
        this._clearRuler();
        this.update();
        super.leave(state);
    }

    cleanSelection(): void {
        let container = this.container;
        if (container.ezdxf_cad_data) {
            container.ezdxf_cad_data.modelspace.entities.forEach(entity => {
                if (entity.attribs.is_entity_selected) {
                    delete entity.attribs.is_entity_selected;
                }
            })
        }
    }

    onmousedown(ev: I_MouseEvent): void {
        if (!this._start_pos || !this._end_pos || !this.painter || !this._ruler) return;

        const mousePos = { x: ev.posX, y: ev.posY };
        const start2d = this._start_pos;
        const end2d = this._end_pos;
        const scale = this.painter._p_sc;

        const isOverStart = this._ruler.isOverEndpoint(mousePos, start2d, scale);
        const isOverEnd = this._ruler.isOverEndpoint(mousePos, end2d, scale);
        const isOverRuler = this._ruler.isOverRuler(mousePos, start2d, end2d, scale);

        if (isOverStart) {
            this._isDragging = true;
            this._activePoint = 'start';
            this._cad_mode_handler._is_moving_element = true;
        } else if (isOverEnd) {
            this._isDragging = true;
            this._activePoint = 'end';
            this._cad_mode_handler._is_moving_element = true;
        } else if (isOverRuler) {
            this._isMovingWhole = true;
            // 直接保存鼠标按下的位置
            this._moveOffset = new Vector3(mousePos.x, mousePos.y, 0);
            this._cad_mode_handler._is_moving_element = true;
        }

        this.update();
    }
    onmousemove(ev: I_MouseEvent): void {
        if (!this._start_pos || !this._end_pos || !this.painter || !this._ruler) return;

        const mousePos = { x: ev.posX, y: ev.posY };

        if (this._isMovingWhole && this._moveOffset) {
            // 计算鼠标移动的距离
            const dx = (mousePos.x - this._moveOffset.x);
            const dy = (mousePos.y - this._moveOffset.y);

            // 更新标尺位置
            this._start_pos.add(new Vector3(dx, dy, 0));
            this._end_pos.add(new Vector3(dx, dy, 0));

            // 更新鼠标位置
            this._moveOffset.set(mousePos.x, mousePos.y, 0);

            // 移动整体时不需要吸附
            this._isSnapped = false;
        } else if (this._isDragging && this._activePoint) {
            // 端点移动模式
            const otherPos = this._activePoint === 'start' ? this._end_pos : this._start_pos;
            // const other2d = this.painter.project2D(otherPos);
            const other2d = otherPos

            // 计算鼠标相对于固定点的角度
            const moveAngle = Math.atan2(
                mousePos.y - other2d.y,
                mousePos.x - other2d.x
            ) * 180 / Math.PI;

            // 重置吸附状态
            this._isSnapped = false;

            // 标准化角度到 -180 到 180 度范围
            const normalizedAngle = ((moveAngle % 360) + 360) % 360 - 180;

            // 检查是否需要吸附
            const isNearHorizontal = Math.abs(normalizedAngle) < this._snapAngle ||
                Math.abs(Math.abs(normalizedAngle) - 180) < this._snapAngle;
            const isNearVertical = Math.abs(Math.abs(normalizedAngle) - 90) < this._snapAngle;

            if (isNearHorizontal || isNearVertical) {
                const distance = Math.sqrt(
                    Math.pow(mousePos.x - other2d.x, 2) +
                    Math.pow(mousePos.y - other2d.y, 2)
                );

                if (isNearHorizontal) {
                    // 水平吸附
                    if (this._activePoint === 'start') {
                        this._start_pos.x = other2d.x + (mousePos.x < other2d.x ? -distance : distance);
                        this._start_pos.y = other2d.y;
                    } else {
                        this._end_pos.x = other2d.x + (mousePos.x < other2d.x ? -distance : distance);
                        this._end_pos.y = other2d.y;
                    }
                } else {
                    // 垂直吸附
                    if (this._activePoint === 'start') {
                        this._start_pos.x = other2d.x;
                        this._start_pos.y = other2d.y + (mousePos.y < other2d.y ? -distance : distance);
                    } else {
                        this._end_pos.x = other2d.x;
                        this._end_pos.y = other2d.y + (mousePos.y < other2d.y ? -distance : distance);
                    }
                }
                this._isSnapped = true;
            } else {
                // 如果没有吸附，则正常移动到鼠标位置
                if (this._activePoint === 'start') {
                    this._start_pos.x = mousePos.x;
                    this._start_pos.y = mousePos.y;
                } else {
                    this._end_pos.x = mousePos.x;
                    this._end_pos.y = mousePos.y;
                }
            }
        }

        this.update();
    }
    onmouseup(ev: I_MouseEvent): void {
        this._isDragging = false;
        this._isMovingWhole = false;
        this._activePoint = null;
        this._isSnapped = false;
        this._moveOffset = null;
        this._cad_mode_handler._is_moving_element = false;
        this.update();
    }

    private drawGuideLines(): void {
        if (!this._isDragging || !this._isSnapped) return;

        const ctx = this.painter._context;
        const activePos = this._activePoint === 'start' ? this._start_pos : this._end_pos;

        // 将世界坐标转换为屏幕坐标
        const screenActivePos = this.painter.project2D(activePos);

        // 计算尺子的角度
        const rulerAngle = Math.atan2(
            this._end_pos.y - this._start_pos.y,
            this._end_pos.x - this._start_pos.x
        ) * 180 / Math.PI;

        // 绘制辅助线
        ctx.save();
        ctx.setLineDash([5, 5]);
        ctx.strokeStyle = '#147FFA';
        ctx.lineWidth = 1;

        const extensionLength = 1000; // 延伸线的长度

        // 根据尺子的角度判断是水平还是垂直吸附
        if (Math.abs(rulerAngle) < this._snapAngle || Math.abs(Math.abs(rulerAngle) - 180) < this._snapAngle) {
            // 水平吸附 - 绘制水平延伸线
            ctx.beginPath();
            ctx.moveTo(screenActivePos.x - extensionLength, screenActivePos.y);
            ctx.lineTo(screenActivePos.x + extensionLength, screenActivePos.y);
            ctx.stroke();
        } else if (Math.abs(Math.abs(rulerAngle) - 90) < this._snapAngle) {
            // 垂直吸附 - 绘制垂直延伸线
            ctx.beginPath();
            ctx.moveTo(screenActivePos.x, screenActivePos.y - extensionLength);
            ctx.lineTo(screenActivePos.x, screenActivePos.y + extensionLength);
            ctx.stroke();
        }

        ctx.restore();
    }

    drawCanvas(): void {
        if (!this.painter || !this._start_pos || !this._end_pos || !this._ruler) return;

        // 先绘制辅助线
        this.drawGuideLines();

        // 再绘制标尺，传入当前激活的端点
        this._ruler.draw(this._start_pos, this._end_pos, this._orc_num, this.painter, this._activePoint);

        // 在绘制完成后设置输入框监听
        this.setupInputListener();
    }

    // 处理事件
    async handleEvent(evt_name: string, evt_param: any) {
        // 进入测量模式
        if (evt_name === LayoutAI_Events.MeasureScale) {
            const { b64_img, content } = evt_param;
            let back_img = new Image();
            back_img.src = b64_img;
            const img = new Image();
            img.src = b64_img;
            img.onload = () => {
                this.manager.layer_CadCopyImageLayer.loadData(img, null);
                this._start_pos = new Vector3(content.ocr_start_x / this.painter._p_sc, content.ocr_start_y/this.painter._p_sc, 0);
                this._end_pos = new Vector3(content.ocr_end_x / this.painter._p_sc, content.ocr_end_y/this.painter._p_sc, 0);

                this._orc_num = content.ocr_number;
                this.update();
            }
            this.update();

        }
        else if (evt_name === LayoutAI_Events.ExitMeasureScale) {
            if(this._previous_mode === AI2DesignBasicModes.HouseDesignMode){
                LayoutAI_App.RunCommand(AI2DesignBasicModes.HouseDesignMode);
            }else{
                LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
            }
        }
        else if (evt_name === LayoutAI_Events.ConfirmScale) {
            let id = evt_param.img_base64.indexOf("base64,");
            let base64_str = evt_param.img_base64.substring(id + 'base64,'.length);
            let distance = this._start_pos.distanceTo(this._end_pos);
            let scale = ((distance * 0.05) / this._orc_num);
            let query_data = {
                'base64String': base64_str,
                'scale': Number(scale),
                'imageMd5': '00000000000000000000000000000000',
                'type': '',
                'extendName': 'png'
            }
            LayoutAI_App.emit(EventName.OpeningImitateFileStart, null);
            let res = await fetch(predictHouseImitateApiUrl, {
                method: 'POST',
                credentials: "include",
                mode: "cors", // no-cors, *cors, same-origin
                headers: {
                    'Content-Type': 'application/json',
                    'Magiccube-Token': _magiccubeToken
                },
                body: JSON.stringify(query_data)
            }).then(val => val.json()).catch(err => {
                this._handleException();
                return null;
            });
            if (!res.success || !res?.data?.content) {
                this._handleException();
                return;
            }
            const houseXmlContent = res.data.content;
            const houseXmlBase64 = deflate_to_base64_str(houseXmlContent);
            let xmlSchemeJson: I_SwjXmlScheme = await SchemeXmlParseService.parseSchemeXml2Json(houseXmlBase64, GenDateUUid());
            if (!xmlSchemeJson) {
                this._handleException();
                return;
            }

            this.xmlSchemeJson = xmlSchemeJson;
            this.img_base64 = evt_param.img_base64;
            LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false });


            let config = {
                duration: 2,
                content: t('识别成功').toString(),
                className: 'custom-class',
                style: {
                    marginTop: '4vh',
                }
            }
            message.success(config);

            if(this._previous_mode === AI2DesignBasicModes.HouseDesignMode){
                LayoutAI_App.RunCommand(AI2DesignBasicModes.HouseDesignMode);
            }else{
                LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
                if (_from === 'local') {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                }
            }
        }
    }

    //异常处理
    private _handleException(): void {
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
        LayoutAI_App.emit(EventName.OpeningImitateFileFail, { opening: false });
    }

    private handleInputChange(event: Event): void {
        const input = event.target as HTMLInputElement;
        const value = parseFloat(input.value);
        if (!isNaN(value)) {
            this._orc_num = value;
            // 使用 requestAnimationFrame 来优化性能
            requestAnimationFrame(() => {
                this.update();
            });
        }
    }

    private _clearRuler() {
        // 清理标尺和输入框，确保移除事件监听器
        if (this._ruler && this._ruler['inputElement']) {
            this._ruler['inputElement'].removeEventListener('input', this._boundInputHandler);
            this._ruler.cleanup();
            this._ruler = null;
        }
    }

}