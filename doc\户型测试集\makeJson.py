import openpyxl
import os
import json
dirname = os.path.dirname(__file__)

def process_xlsx(filename:str,name:str):
    workbook = openpyxl.load_workbook(filename=filename)
    sheet = workbook.active

    title = sheet.title
    print(title)
    res = {
        "dataset_name" : title,
        "data_list":[]
    }
    min_row = sheet.min_row
    max_row = sheet.max_row

    data_list = []
    for i in range(min_row,max_row+1):
        cell = sheet.cell(i,2)
        data_list.append({
            "buildingRoomId":cell.value
        })
        
    res['data_list'] = data_list

    t_filename = "./static/Testing/"+name.replace(".xlsx",".json")
    print(t_filename)
    fp = open(t_filename,'w',encoding='utf-8')
    json.dump(res,fp=fp,ensure_ascii=False,indent=2)
    fp.close()
            




for root,dirs,files in os.walk(dirname):
    for file in files:
        if file.endswith(".xlsx"):
            process_xlsx(filename=os.path.join(root,file),name=file)