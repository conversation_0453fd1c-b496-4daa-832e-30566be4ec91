import { LayoutAI_App } from "../../../LayoutAI_App";
import { I_SwjEntityBase, I_SwjFurnitureGroup } from "../../AICadData/SwjLayoutData";
import { TPainter } from "../../Drawing/TPainter";
import { FigureShapeType, TFigureElement } from "../TFigureElements/TFigureElement";
import { TGroupTemplate } from "../TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TFurnitureEntity } from "./TFurnitureEntity";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { ZRect } from "@layoutai/z_polygon";
import { TSize } from "../TModels/TSizeRange";
import { checkIsMobile } from "@/config";
import { TBaseGroupEntity } from "./TBaseGroupEntity";
import { IRoomEntityType, KeyEntity } from "../IRoomInterface";
import { TSerialSizeRangeDB } from "../TLayoutGraph/TGroupTemplate/TSeriesSizeRangeDB";
import { TSeriesFigureGroupDB } from "../TLayoutGraph/TGroupTemplate/TSeriesFigureGroupDB";


export class TGroupTemplateEntity extends TFurnitureEntity {
    _group_template: TGroupTemplate;
    _entity_height: number;
    _disassembled: boolean;

    static readonly EntityType: IRoomEntityType = "GroupTemplate";
    constructor(group_template: TGroupTemplate) {
        let groupCode: string = group_template?.seed_figure_group?.group_code || group_template?.current_s_group?.group_code || "";
        let groupCategoryName: string = "组合";
        let shapeType: FigureShapeType = null;
        if (groupCode.startsWith("餐桌")) {
            groupCategoryName = "餐桌椅组合";
        } else if (groupCode.indexOf("床") >= 0) {
            groupCategoryName = "床具组合";
        } else if (groupCode.indexOf("沙发") >= 0) {
            groupCategoryName = "沙发组合";
        } else if (groupCode.indexOf("书桌") >= 0) {
            groupCategoryName = "书桌组合";
        } else if (groupCode.indexOf("榻榻米") >= 0) {
            groupCategoryName = "榻榻米组合";
        } else if (groupCode.indexOf("岛台") >= 0) {
            groupCategoryName = "岛台组合";
        } else if (groupCode.indexOf("餐桌") >= 0) {
            groupCategoryName = "餐桌椅组合";
        }
        else {
            let main_category = group_template?.seed_figure_group?.main_figure?.category || "";
            groupCategoryName = main_category + "组合";

        }
        if (groupCode.indexOf("圆") >= 0) {
            shapeType = "圆形";
        }
        let groupRect: ZRect = group_template._target_rect;
        let groupFigureElement = TFigureElement.createSimple(groupCategoryName);
        if (shapeType != null) groupFigureElement._shape = shapeType;
        groupFigureElement.bindRect(groupRect);

        super(groupFigureElement);

        this._is_single_furniture = false;
        this._group_template = group_template;
        this._rect = this._group_template._target_rect;
        this._entity_height = -1;
        this._need_update = true;
        this._disassembled = false;

        this._default_priority_for_selection = this._priority_for_selection = 10;

    }

    get figure_elements() {
        if (!this.disassembled) {
            if (this.group_element != null) {
                return [this.group_element];
            } else {
                return [];
            }
        } else if (this._group_template && this._group_template?.current_s_group) {
            return this._group_template.current_s_group.figure_elements;
        }
        else {
            return [];
        }
    }

    get disassembled(): boolean {
        return this._disassembled;
    }

    set disassembled(value: boolean) {
        this._disassembled = value;
    }

    exportData(): I_SwjEntityBase {
        let data = super.exportData() as I_SwjFurnitureGroup;

        data.group_template_code = this._group_template.group_code;
        data.group_template_uuid = this._group_template._group_uuid;

        return data;
    }

    clone() {
        let ele = this._group_template.clone();
        let entity = new TGroupTemplateEntity(ele);
        entity.bindFigureEntities();
        return entity;
    }
    static getOrMakeEntityOfCadRect(rect:ZRect):TFurnitureEntity
    {
        let entity: TFurnitureEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            if (rect._attached_elements[TGroupTemplate.EntityName]) {
                entity = new TGroupTemplateEntity(rect._attached_elements[TGroupTemplate.EntityName]);
                entity._rect = rect;
                entity.update();
                rect._attached_elements[KeyEntity] = entity;
            }
        }
        return entity;
    }
    static RegisterGenerators()
    {
        TBaseEntity.RegisterPolyEntityGenerator(this.EntityType,this.getOrMakeEntityOfCadRect)
    } 
    update(): void {
        if (!this._need_update) return;
        super.update();
        if (this._rect !== this._group_template._target_rect) {
            this._group_template._target_rect = this.rect;

        }


        if (!this._group_template.current_s_group) {
            this._group_template.updateByTargetRect();
        }
        else {

            this._group_template.updateGroupBySize(new TSize(Math.round(this.rect.w + 0.5), Math.round(this.rect.h + 0.5), 5000))
        }

        // this._group_template._target_rect.copy(this._group_template.current_s_group.group_rect);

        if (this._group_template.current_s_group) {
            this._group_template.current_s_group.applyInRect(this._rect, { target_point: this._scale_fixed_point });



            let group_figure_elements = this._group_template.current_s_group.figure_elements;

            group_figure_elements.forEach((ele) => {
                if (ele.matched_rect) {
                    ele.matched_rect.back_center = ele.rect.back_center;
                    ele.matched_rect.nor = ele.rect.nor;
                    ele.matched_rect.u_dv = ele.rect.dv;
                    ele.matched_rect.updateRect();
                }
            })

        }
        if (this._entity_height < 0) // 如果entity_height未初始化, 那么entity_height被初始化赋值
        {
            this._entity_height = this.height;
        }
        else // 如果entity_height > 0
        {
            this.height = this._entity_height;
        }


        this._need_update = true;
    }

    bindEntity(): void {
        super.bindEntity();
    }

    bindFigureEntities(): void {

        this.bindEntity();
        this.update();
        this.initProperties();
    }
    setRectProperties(): void {
        if (!this._rect) {
            return;
        }
        TBaseEntity.set_polygon_type(this._rect, this.type);
        this._rect.ex_prop.label = this.category;
        this.rect._attached_elements[TGroupTemplate.EntityName] = this._group_template;

    }
    get category() {
        return this._group_template.group_code;
    }

    set category(str: string) {
        this._group_template.group_code = str;
        this._rect.ex_prop.label = TGroupTemplate.EntityName + ":" + str;
    }



    get public_category() {
        return ""
    }

    set public_category(str: string) {
        return;
    }

    get size() {
        return `${Math.round(this.depth)} * ${Math.round(this.length)} * ${Math.round(this.height)}`
    }


    get imageUrl() {
        let data = TGroupTemplate.GroupCodeUiInfo[this._group_template.group_code];
        if (data) return data.image;
        return null;
    }
    get material_name() {
        let data = TGroupTemplate.GroupCodeUiInfo[this._group_template.group_code];
        if (data) return data.title;
        return null;
    }
    set material_name(s: string) {

    }

    get figure_element() {
        if (!this._disassembled) {
            return this.group_element;
        } else {
            return null;
        }
    }

    get group_element(): TFigureElement {
        return this._figure_element;
    }

    get disassembled_figure_elements(): TFigureElement[] {
        let instance = TSeriesFigureGroupDB.getInstance();
        let db = instance._dict.default[this._group_template.group_code];
        let list = db.seed_figure_group.sub_figures[0]?.figure_array || db.seed_figure_group.sub_figures?.map(item => item?.figure);
        if (this._group_template && list) {
            let figure_elements = [...list, db.seed_figure_group.main_figure];
            let new_figure_elements: TFigureElement[] = [];
            figure_elements.forEach((fe) => {
                let new_fe = fe.clone();
                new_fe.rect.updateRect();
                new_figure_elements.push(new_fe);
            });
            return new_figure_elements;
        }
        else {
            return [];
        }
    }

    get height() {
        if (this.figure_element) {
            return this.figure_element.params.height || 10;
        }
        return 10;
    }

    set height(h: number) {
        if (this.figure_element) {
            this.figure_element.params.height = h;

        }
    }

    get locked(): boolean {
        let lockValue: boolean = false;
        this.figure_elements.forEach((fe) => {
            if (fe.locked) lockValue = true;
        });
        return lockValue;
    }

    set locked(value: boolean) {
        this.figure_elements.forEach((fe) => {
            fe.locked = value;
        });
        return;
    }

    doneTransform(): void {
        this._scale_fixed_point = null;
        if (this._group_template?.current_s_group?.group_rect) {
            this._rect.copy(this._group_template.current_s_group.group_rect)
            this.updateDataByRect();
        }
    }

    // 转换成TBaseGroupEntity
    toBaseGroupEntity() {
        let sub_entitys = [];
        // 主组合
        if (!this._figure_element) return;
        this._figure_element.rect._attached_elements = {};
        this._figure_element.rect.ex_prop['GroupName'] = this._figure_element.category;
        TBaseEntity.set_polygon_type(this._figure_element.rect, 'BaseGroup');
        let baseGroup_entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(this._figure_element.rect) as TBaseGroupEntity;
        baseGroup_entity.figure_element._shape = this._figure_element._shape;
        for (let fig of this.disassembled_figure_elements) {
            let entity = new TFurnitureEntity(fig);
            entity.figure_element._room = this._figure_element._room;
            if (fig.sub_category.includes('圆')) {
                entity.figure_element._shape = '圆形';
            } else {
                entity.figure_element._shape = null;
            }
            entity.bindFigureEntities();
            sub_entitys.push(entity);
            TBaseGroupEntity.recordGroupRectData(entity, baseGroup_entity);
        }
        baseGroup_entity.combination_entitys = sub_entitys;
        baseGroup_entity.recordBaseGroup(baseGroup_entity);
        baseGroup_entity.updateCategory(this._figure_element.category);

        return baseGroup_entity as TBaseGroupEntity;
    }


    getCategoryOptions(): { label: string; value: string }[] {
        return [];
    }
    initProperties(): void {

        super.initProperties();
        this._ui_properties["goods"] = {
            name: LayoutAI_App.t("信息组件"),
            widget: "infoItem",
            value: {
                name: LayoutAI_App.t(this.material_name),
                size: this.size,
                id: 'null',
                imageUrl: this.imageUrl,
                type: 'Group',
            },
        }
        this._ui_properties["public_category"] = {
            name: LayoutAI_App.t("图元分类"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.public_category,
            editable: false,
            props: {
                type: "select",
                options: [{ label: this.public_category, value: this.public_category }],
                optionWidth: 100
            }
        }

        this._ui_properties["length"] = {
            name: LayoutAI_App.t("宽度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 50000,
            editable: true,
            defaultValue: '' + Math.round(this.length),
            props: {
                type: "number",
                // suffix :"mm"
            }
        }
        this._ui_properties["width"] = {
            name: LayoutAI_App.t("深度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 50000,
            editable: true,
            defaultValue: '' + Math.round(this.width),
            props: {
                type: "number",
                // suffix :"mm"
            }
        }

        this._ui_properties["locked"] = {
            name: LayoutAI_App.t("锁定"),
            widget: "LabelItem",
            type: "boolean",
            defaultValue: this.locked
        };
        this._ui_props_keys.push("locked");

        this._ui_props_keys = ["goods", "ui_type", "length", "width", "height", "pos_z", "locked"];
        this._bindPropertiesOnChange();
    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {

        if (TBaseEntity.is_deleted(this.rect)) return;

        painter._context.lineWidth = Math.max(4 * painter._p_sc, 3);
        if (options.is_selected) {
            painter.strokeStyle = "#147FFA";
            if (options.is_draw_texture) {
                painter.strokePolygons([this.matched_rect || this.rect]);
            }
            else {
                painter.strokePolygons([this._rect]);

            }
        }
        if (options.is_hovered && !checkIsMobile()) {
            painter.strokeStyle = "#147FFA";
            if (options.is_draw_texture) {
                painter.strokePolygons([this.matched_rect || this.rect]);
            }
            else {
                painter.strokePolygons([this._rect]);

            }

        }
        if (options.is_draw_figure) {
            if (options.is_draw_texture) {

                if (this.group_element.haveMatchedMaterial() && this.group_element.pictureViewImg != null) {
                    this.group_element.drawPreviewFigure(painter, false);
                }
                else if (this._group_template && this._group_template.current_s_group) {

                    this._group_template.current_s_group.applyInRect(this._rect, { target_point: this._scale_fixed_point });
                    let figure_elements = [...this._group_template.current_s_group.figure_elements];

                    figure_elements.sort((a, b) => (a.default_drawing_order - b.default_drawing_order));
                    for (let fig of figure_elements) {
                        fig.drawPreviewFigure(painter, false);
                    }
                }
            }
            else {
                if (this._group_template.current_s_group) {
                    let figure_elements = [...this._group_template.current_s_group.figure_elements];

                    figure_elements.sort((a, b) => (a.default_drawing_order - b.default_drawing_order));
                    for (let fig of figure_elements) {
                        fig.drawFigure(painter, false);
                    }
                }
            }


        }
    }


    protected onEntityPropertyChanged(propertyName: string, oldValue: any, newValue: any): void {
        if (propertyName == "pos_z") {
            let offsetZ: number = newValue as number - oldValue as number;
            // let logTxt:string = "TGroupTemplateEntity.onEntityPropertyChanged() " + propertyName + ":" + oldValue + "=>"+ newValue;
            this.figure_elements.forEach((fe: TFigureElement) => {
                // logTxt = logTxt + "\n   " + fe.toString();
                fe.rect.zval = fe.rect.zval + offsetZ;
                // logTxt = logTxt + "\n =>" + fe.toString()
            });
            // console.info(logTxt);
            if (this.onPanelUpdate) {
                this.onPanelUpdate(propertyName, newValue);
            }
        }
    }

    public onRoomFloorThicknessChanged() {
        if (this.roomEntity != null) {
            const oldFloorZ: number = this.rect.floorZ;
            const newFloorZ: number = this.roomEntity.floor_thickness;
            const newPosZ = this.rect.zval + newFloorZ - oldFloorZ;
            if (newPosZ + this.height <= this.roomEntity.storey_height) {
                this.rect.zval = newPosZ;
                this.rect.floorZ = newFloorZ;
            }
        }
        this.figure_elements.forEach((fe) => {
            fe.onRoomFloorThicknessChanged();
        });
    }
}