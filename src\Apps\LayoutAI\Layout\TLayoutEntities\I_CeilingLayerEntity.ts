import { Vector3Like } from "three";
import { I_SwjEntityBase } from "../../AICadData/SwjLayoutData";
import { GenCeilingLayerMethod, I_LightSlotData } from "../IRoomInterface";
import { I_FigureElement } from "../TFigureElements/TFigureElement";

/**
 *   吊顶层: 是一个树状图层
 */
export interface I_CeilingLayerEntity extends I_SwjEntityBase {

    method: GenCeilingLayerMethod;
    ceiling_type?: string;
    offset_value?: number;
    offset_func?: string;

    drawing_points?: Vector3Like[];
    /**
     *   子吊顶-层
     */
    children?: I_CeilingLayerEntity[];
    /**
     *  离上一层的偏移
     */
    zvalToTop: number;

    /**
     *  函数动态计算
     */
    zvalToTopFunc?: string;

    /**
     *  灯槽数据
     */
    lightSlotData?: I_LightSlotData;


    downLightsData?: I_FigureElement[];
    /**
     *  2D绘制样式
     */
    strokeStyle?: string;

}