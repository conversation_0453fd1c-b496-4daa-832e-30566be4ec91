import {
    IEdgeLayout,
    IKitchenLayout,
    KitchenAreaType,
    KitchenLayoutType
} from "@layoutai/layout_service";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoomLayoutScheme } from "../../../TLayoutScheme/TRoomLayoutScheme";
import { TRoom } from "../../../TRoom";
import { TCheckRule } from "../TCheckRule";
import { ZEdge, ZRect } from "@layoutai/z_polygon";
import { WPolygon } from "../../../TFeatureShape/WPolygon";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";

/**
 * @description 窗下禁区
 * 烹饪区、鲜储区、高柜不能在窗下
 * <AUTHOR>
 * @date 2025-08-30
 * @lastEditTime 2025-08-30 17:32:58
 * @lastEditors xuld
 */
export class TKitchenAreaWinCheckRule extends TCheckRule {
    private _winForbidTypes = [
        KitchenAreaType.HighCabinetArea,
        KitchenAreaType.FreshStorageArea,
        KitchenAreaType.CookingArea
    ];

    checkValue(
        room: TRoom,
        figure_elements?: TFigureElement[],
        layout_scheme: TRoomLayoutScheme = null
    ) {
        if (!layout_scheme) {
            return { value: 0 };
        }

        let extra = layout_scheme.extra as IKitchenLayout;
        if (!extra) {
            return { value: 0 };
        }

        let isOnForbid = false;

        extra.edgeLayouts.forEach(edgeLayout => {
            edgeLayout.areaTypeRects.forEach(rectInfo => {
                if (
                    this._winForbidTypes.includes(rectInfo.areaType) &&
                    this.isRectOnWinEdge(rectInfo.area, edgeLayout.edge)
                ) {
                    isOnForbid = true;
                    return;
                }
            });
        });

        let score = 0;
        if (!isOnForbid) {
            score += 1;
        }

        return { value: score };
    }

    // // 判断两条线段是否重叠
    private isEdgeOverLay(edge1: ZEdge, edge2: ZEdge) {
        // 判断两条边是否平行
        if (!TBaseRoomToolUtil.instance.isParallelTwoEdges(edge1, edge2)) {
            return false;
        }

        // 获取边的起点和终点坐标
        const edge1Start = edge1.v0.pos;
        const edge1End = edge1.v1.pos;
        const edge2Start = edge2.v0.pos;
        const edge2End = edge2.v1.pos;

        // 获取边的方向向量
        const edge1Dir = edge1.dv;
        const edge2Dir = edge2.dv;

        // 判断两条边是否在同一直线上
        const crossProduct = edge1Dir.clone().cross(edge2Dir);
        if (Math.abs(crossProduct.length()) > 0.001) {
            return false;
        }

        // 计算投影区间
        const edge1Min = Math.min(edge1Start.x, edge1End.x);
        const edge1Max = Math.max(edge1Start.x, edge1End.x);
        const edge2Min = Math.min(edge2Start.x, edge2End.x);
        const edge2Max = Math.max(edge2Start.x, edge2End.x);

        // 判断投影是否重叠
        if (edge1Max < edge2Min || edge2Max < edge1Min) {
            return false;
        }

        return true;
    }

    private isRectOnWinEdge(rect: ZRect, edge: ZEdge) {
        let isOnWin = false;
        let lastEdge = edge;

        while (lastEdge) {
            if (WPolygon.getWindowOnEdge(lastEdge)) {
                if (this.isEdgeOverLay(lastEdge, rect.backEdge)) {
                    isOnWin = true;
                    break;
                }
            }
            lastEdge = lastEdge.prev_edge;
            if (TBaseRoomToolUtil.instance.isParallelTwoEdges(lastEdge, edge)) {
            } else {
                lastEdge = null;
            }
        }

        return isOnWin;
    }
}
