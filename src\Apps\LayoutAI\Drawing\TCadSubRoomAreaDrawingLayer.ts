
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { TAppManagerBase } from "../../AppManagerBase";

export class TCadSubRoomAreaDrawingLayer extends TDrawingLayer
{
    

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.<PERSON>ad<PERSON>oom<PERSON>tr<PERSON><PERSON>,manager);
        this._dirty = true;
    
    }
    
     _updateLayerContent(): void {

    }
    
    onDraw(): void {
        // getCandidateRects 中判断单空间哪些需要绘制
        let entities = this.layout_container.getCandidateEntities(["SubArea"]);
        for(let entity of entities)
        {
            if(!entity || entity.is_selected){
                continue;
            }
            entity.drawEntity(this.painter,{is_draw_figure:true});
        }

    }
}