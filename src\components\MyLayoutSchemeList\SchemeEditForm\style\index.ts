import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
desc: css`
    color: #282828;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
    margin-bottom: 12px;
`,
customSubmitButton: css`
    width: 100px;
    height: 40px;
    background: linear-gradient(90deg, #ba63f0 0%, #5c42fb 100%) !important;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    transition: background 0.3s;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 14px;
    line-height: 1.5;
    letter-spacing: 0px;
    &:hover {
    background: linear-gradient(90deg, #d07bff 0%, #7a5bff 100%) !important;
    }
`,
customCancelButton: css`
    width: 100px;
    height: 40px;
    background: #f4f5f5 !important;
    color: #282828 !important;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 14px;
    line-height: 1.5;
    letter-spacing: 0px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    transition: background 0.3s;
`,
}));