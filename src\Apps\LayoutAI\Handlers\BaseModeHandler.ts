import { T_Handlerbase } from "@/Apps/LayoutAI/Handlers/HandlerBase";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { Vector3 } from "three";
import { TAppManagerBase } from "../../AppManagerBase";


export class BaseModeHandler extends T_Handlerbase {
    protected _manager: TAppManagerBase;

    protected _last_ev_pos: { x: number, y: number };
    public _is_painter_center_moving: boolean;
    public _is_painter_center_wheel: boolean;
    protected _sub_handlers: { [key: string]: T_Handlerbase };

    protected _active_sub_handler: T_Handlerbase;
    protected _default_sub_handler_name: string;
    public _is_moving_element: boolean;

    protected _last_p_center: Vector3;
    private targetScale: number;
    private animationFrameId: number | null = null;
    constructor(manager: TAppManagerBase, name: string = "AICadMode") {
        super(name);
        this._manager = manager;

        this._last_ev_pos = null;
        this._is_painter_center_moving = false;
        this._is_painter_center_wheel = false;
        this._sub_handlers = {};
        this._active_sub_handler = null;
        this._last_p_center = null;

        this._is_moving_element = false;  //是否是墙体
        this.targetScale = this.painter?._p_sc || 1.0;

    }
    enter(state?: number): void {
        super.enter(state);
        this.manager._current_handler_mode = this.name;

        if (this._last_p_center) {
            if (this.painter) {
                this.painter.p_center = this._last_p_center;
            }
        }
    }

    leave(state?: number): void {
        super.leave(state);
        if (this.painter) {
            this._last_p_center = this.painter.p_center.clone();
        }
    }
    get manager() {
        return this._manager;
    }
    get EventSystem() {
        return this._manager.EventSystem;
    }
    get painter() {
        return this._manager?.painter;
    }
    async prepare(load_test_data: boolean = true) {

        // 看看有什么数据要提前载入

        if (load_test_data) {
            await this.prepareTestData();
        }
    }

    /**
     *  本地测试数据准备
     */
    async prepareTestData() {

    }
    /**
     * 初始化控件, 在react下, 需要研究怎么跟组件化配合
     * @returns 
     */
    initWidget(): void {
        return; // 
    }


    drawCanvas(): void {
        this.drawWithActiveHandler();
    }

    drawWithActiveHandler() {
        if (this._active_sub_handler) {
            this._active_sub_handler.drawCanvas();
        }
    }
    onmousedown(ev: I_MouseEvent): void {
        // console.log(ev)
        if (this._active_sub_handler) {
            this._active_sub_handler.onmousedown(ev);
        }
        this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

        this._is_painter_center_moving = false;


    }
    onmousemove(ev: I_MouseEvent): void {

        if (this._active_sub_handler) {
            this._active_sub_handler.onmousemove(ev);
        }
        if (ev.buttons && this._last_ev_pos && !this._is_moving_element) // 或左键
        {

            let dx = ev._ev.pageX - this._last_ev_pos.x;
            let dy = ev._ev.pageY - this._last_ev_pos.y;

            dx = Math.floor((dx / this.painter._p_sc * 2) * 100) / 100;
            dy = Math.floor((dy / this.painter._p_sc * 2) * 100) / 100;

            let vv = new Vector3(dx, dy, 0).normalize().multiplyScalar(2 / this.painter.p_scale);
            this.painter.moveCenterDv(-dx, -dy);
            this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };
            this.update();

            // 移动超过1个像素, 就认为是移动了画布
            if (Math.abs(dx) > 1 || Math.abs(dy) > 1) {
                this._is_painter_center_moving = true;
            }
        }

    }
    onmouseup(ev: I_MouseEvent): void {
        this._is_painter_center_moving = false;
        if (this._active_sub_handler) {
            this._active_sub_handler.onmouseup(ev);
        }


        this.update();

        this._last_ev_pos = null;
    }

    onkeydown(ev: KeyboardEvent): boolean {

        if (this._active_sub_handler) {
            this._active_sub_handler.onkeydown(ev);
        }
        if (ev.key === "Escape") {
            this.manager.resetPointers();
            this.runCommand(LayoutAI_Commands.LeaveSubHandler);
        }

        if (ev.ctrlKey) {
            if (ev.code === 'KeyZ') {
                this.runCommand(LayoutAI_Commands.Undo);
            }
            else if (ev.code === "KeyY") {
                this.runCommand(LayoutAI_Commands.Redo);

            }
        }

        return true;
    }
    onkeyup(ev: KeyboardEvent): boolean {
        if (this._active_sub_handler) {
            this._active_sub_handler.onkeyup(ev);
        }
        return true;
    }
    onwheel(ev: WheelEvent): void {
        this._is_painter_center_wheel = true;
        const scaleFactor = 1.2;
        const minScale = 0.01;
        const maxScale = 10.0;
        if(ev.deltaY < 0)
        {
            if(this.painter._p_sc < 10.)
            {
                this.painter._p_sc *= 1.05;
            }
            else{
                this.painter._p_sc = 10.;
            }
            
        }
        else{
            this.painter._p_sc /= 1.05;
            if(this.painter._p_sc < 0.01)
            {
                this.painter._p_sc = 0.01;
            }
        }
        this.update();
    }
    private smoothZoom() {
        const zoomStep = () => {
            const delta = this.targetScale - this.painter._p_sc;
            this._is_painter_center_wheel = true;

            if (Math.abs(delta) < 0.001) {
                this.painter._p_sc = this.targetScale;
                this._is_painter_center_wheel = false;
                this.animationFrameId = null;
                setTimeout(() => {
                    // LayoutAI_App.instance.update();
                }, 1);

                return;
            }

            this.painter._p_sc += delta * 0.3;
            this.update();
            this.animationFrameId = requestAnimationFrame(zoomStep);
        };

        this.animationFrameId = requestAnimationFrame(zoomStep);
    }
    update() {
        this._manager.update();
    }

    setActiveHandler(name: string, leave_state: number = 0) {
        if (this._active_sub_handler) {
            this._active_sub_handler.leave(leave_state);
        }
        this._active_sub_handler = null;
        let handler = this._sub_handlers[name];
        if (handler) {
            this._active_sub_handler = handler;
            handler.enter();
        }
    }

    setActiveHandlerDirectly(handler: T_Handlerbase, leave_state: number = 0) {
        if (this._active_sub_handler) {
            this._active_sub_handler.leave(leave_state);
        }
        this._active_sub_handler = null;
        if (handler) {
            this._active_sub_handler = handler;
            handler.enter();
        }
    }


    runCommand(cmd_name: string): void {
        console.log(cmd_name);
        if (this._sub_handlers[cmd_name]) {
            this.setActiveHandler(cmd_name);
            // return;
        }
        this.update();
    }



}