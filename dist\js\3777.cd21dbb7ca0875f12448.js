"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[3777],{63777:function(e,a,r){r.r(a),r.d(a,{default:function(){return d}});var n={items_per_page:"/ página",jump_to:"Ir a",jump_to_confirm:"confirmar",page:"Página",prev_page:"Página anterior",next_page:"Página siguiente",prev_5:"5 páginas previas",next_5:"5 páginas siguientes",prev_3:"3 páginas previas",next_3:"3 páginas siguientes",page_size:"tamaño de página"},i=r(74866),l=r(50799),o=(0,i.A)((0,i.A)({},l.I),{},{locale:"es_ES",today:"Hoy",now:"Ahora",backToToday:"Volver a hoy",ok:"Aceptar",clear:"Limpiar",week:"Semana",month:"Mes",year:"Año",timeSelect:"Seleccionar hora",dateSelect:"Seleccionar fecha",monthSelect:"Elegir un mes",yearSelect:"Elegir un año",decadeSelect:"Elegir una década",dateFormat:"D/M/YYYY",dateTimeFormat:"D/M/YYYY HH:mm:ss",previousMonth:"Mes anterior (PageUp)",nextMonth:"Mes siguiente (PageDown)",previousYear:"Año anterior (Control + left)",nextYear:"Año siguiente (Control + right)",previousDecade:"Década anterior",nextDecade:"Década siguiente",previousCentury:"Siglo anterior",nextCentury:"Siglo siguiente"});var t={placeholder:"Seleccionar hora"};const c={lang:Object.assign({placeholder:"Seleccionar fecha",rangePlaceholder:["Fecha inicial","Fecha final"],shortWeekDays:["Dom","Lun","Mar","Mié","Jue","Vie","Sáb"],shortMonths:["Ene","Feb","Mar","Abr","May","Jun","Jul","Ago","Sep","Oct","Nov","Dic"]},o),timePickerLocale:Object.assign({},t)};const s="${label} no es un ${type} válido";var d={locale:"es",Pagination:n,DatePicker:c,TimePicker:t,Calendar:c,global:{placeholder:"Seleccione"},Table:{filterTitle:"Filtrar menú",filterConfirm:"Aceptar",filterReset:"Reiniciar",filterEmptyText:"Sin filtros",filterCheckAll:"Seleccionar todo",filterSearchPlaceholder:"Buscar en filtros",emptyText:"Sin datos",selectAll:"Seleccionar todo",selectInvert:"Invertir selección",selectNone:"Vacíe todo",selectionAll:"Seleccionar todos los datos",sortTitle:"Ordenar",expand:"Expandir fila",collapse:"Colapsar fila",triggerDesc:"Click para ordenar en orden descendente",triggerAsc:"Click para ordenar en orden ascendente",cancelSort:"Click para cancelar ordenamiento"},Tour:{Next:"Siguiente",Previous:"Anterior",Finish:"Finalizar"},Modal:{okText:"Aceptar",cancelText:"Cancelar",justOkText:"Aceptar"},Popconfirm:{okText:"Aceptar",cancelText:"Cancelar"},Transfer:{titles:["",""],searchPlaceholder:"Buscar aquí",itemUnit:"elemento",itemsUnit:"elementos",remove:"Eliminar",selectCurrent:"Seleccionar página actual",removeCurrent:"Eliminar página actual",selectAll:"Seleccionar todos los datos",removeAll:"Eliminar todos los datos",selectInvert:"Invertir página actual"},Upload:{uploading:"Subiendo...",removeFile:"Eliminar archivo",uploadError:"Error al subir el archivo",previewFile:"Vista previa",downloadFile:"Descargar archivo"},Empty:{description:"No hay datos"},Icon:{icon:"ícono"},Text:{edit:"Editar",copy:"Copiar",copied:"Copiado",expand:"Expandir"},Form:{optional:"(opcional)",defaultValidateMessages:{default:"Error de validación del campo ${label}",required:"Por favor, rellena ${label}",enum:"${label} debe ser uno de [${enum}]",whitespace:"${label} no puede ser un carácter en blanco",date:{format:"El formato de fecha de ${label} es inválido",parse:"${label} no se puede convertir a una fecha",invalid:"${label} es una fecha inválida"},types:{string:s,method:s,array:s,object:s,number:s,date:s,boolean:s,integer:s,float:s,regexp:s,email:s,url:s,hex:s},string:{len:"${label} debe tener ${len} caracteres",min:"${label} debe tener al menos ${min} caracteres",max:"${label} debe tener hasta ${max} caracteres",range:"${label} debe tener entre ${min}-${max} caracteres"},number:{len:"${label} debe ser igual a ${len}",min:"${label} valor mínimo es ${min}",max:"${label} valor máximo es ${max}",range:"${label} debe ser entre ${min}-${max}"},array:{len:"Debe ser ${len} ${label}",min:"Al menos ${min} ${label}",max:"Como máximo ${max} ${label}",range:"El valor de ${label} debe estar entre ${min}-${max}"},pattern:{mismatch:"${label} no coincide con el patrón ${pattern}"}}},Image:{preview:"Previsualización"}}}}]);