import { <PERSON><PERSON><PERSON>, Matrix4, Vector3, Vector3Like } from "three";
import { range_substract } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { ZVertex } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";

export interface I_BoardBasicParams {
    [key: string]: string | number;
    W?: number;
    H?: number;
    D?: number;
    RX?: number;
    RY?: number;
    RZ?: number;
    PX?: number;
    PY?: number;
    PZ?: number;
}
export interface I_BoardHole3D {
    center: Vector3; width: number; height: number; nor?: Vector3Like; face_nor: Vector3; zval?: number;
    face?: TFace3D;
    hole_flag?: number;
}
export interface I_Board3D {
    W?: number; // 长度- x_len
    D?: number;  // 宽度- y_len
    H?: number; // 高度- z_len
    CuttedType?: number; // 0:None; 1|2|3|4---不同位置
    Cutted_L0?: number;
    Cutted_L1?: number;
    BevelType?: number; // 0:None; 1|2|3|4---不同位置
    Bevel_D1?: number; // 斜角
    HoleRects?: I_BoardHole3D[];

}
export interface I_Vertex3D {
    pos: Vector3;
}

export class TVertex3D {
    i_pos: Vector3;
    pos: Vector3;
    constructor(p: Vector3) {
        this.pos = p;
        this.i_pos = this.pos.clone();
    }
}

export interface I_Mesh3D {
    vertices: TVertex3D[];
}
export class TFace3D {
    face_ids: number[];
    face_nor: Vector3;
    mesh: I_Mesh3D;



    constructor(face_ids: number[], mesh: I_Mesh3D = null) {
        this.face_ids = [];
        for (let id of face_ids) this.face_ids.push(id);
        this.mesh = mesh;

        this.face_nor = new Vector3();

        this.computeNor();

    }


    get positions(): Vector3[] {
        let vertices = this.vertices;
        let res: Vector3[] = [];
        for (let v of vertices) {
            let pos = new Vector3().copy(v.pos);
            // pos.x = 80000 + pos.x;
            // pos.y = 80000 - pos.y;
            res.push(pos);
        }
        return res;
    }

    getPositionsWithOffset(offset: Vector3Like = { x: 0, y: 0, z: 0 }) {
        let vertices = this.vertices;
        let res: Vector3[] = [];
        for (let v of vertices) {
            let pos = new Vector3().copy(v.pos).add(offset);
            // pos.x = 80000 + pos.x;
            // pos.y = 80000 - pos.y;
            res.push(pos);
        }
        return res;
    }
    get vertices(): I_Vertex3D[] {
        if (!this.mesh) return [];

        let res = [];
        for (let id of this.face_ids) {
            res.push(this.mesh.vertices[id]);
        }
        return res;
    }


    computeNor() {
        this.face_nor = new Vector3();

        let vertices = this.vertices;
        if (vertices.length <= 2) return this.face_nor;


        if (this.vertices.length > 4) {
            let nor_sum = new Vector3();

            for (let i = 0; i < this.vertices.length; i++) {
                let dv0 = vertices[(i + 1) % this.vertices.length].pos.clone().sub(vertices[(i)].pos);
                let dv1 = vertices[(i + 2) % this.vertices.length].pos.clone().sub(vertices[(i + 1) % this.vertices.length].pos);


                let nor = dv0.cross(dv1).normalize();

                nor_sum.add(nor);
            }
            nor_sum.normalize();


            this.face_nor.copy(nor_sum);
        }
        else {
            let dv0 = vertices[1].pos.clone().sub(vertices[0].pos);
            let dv1 = vertices[2].pos.clone().sub(vertices[1].pos);


            let nor = dv0.cross(dv1).normalize();

            this.face_nor.copy(nor);
        }

        return this.face_nor;

    }



}
export class TBoard3D {
    W: number; // 长度- x_len
    D: number;  // 宽度- y_len
    H: number; // 高度- z_len

    CuttedType?: number; // 0:None; 1|2|3|4---不同位置
    Cutted_L0?: number;
    Cutted_L1?: number;
    BevelType?: number; // 0:None; 1|2|3|4---不同位置
    Bevel_D1?: number; // 斜角

    _init_positions: Vector3[];


    faces: TFace3D[];
    vertices: TVertex3D[];

    _model_matrix: Matrix4;

    _front_f_id: number;


    HoleRects?: I_BoardHole3D[];


    constructor(params: I_Board3D = {}) {
        this.W = params.W || 1;
        this.D = params.D || 1;
        this.H = params.H || 1;
        this.CuttedType = params.CuttedType || 0;
        this.Cutted_L0 = params.Cutted_L0 || 0;
        this.Cutted_L1 = params.Cutted_L1 || 0;
        this.BevelType = params.BevelType || 0;
        this.Bevel_D1 = params.Bevel_D1 || 0;
        this._init_positions = [];
        this._front_f_id = 2;
        this._model_matrix = null;
        this.HoleRects = params.HoleRects || [];
        this.initMesh3D();
    }

    initMesh3D() {


        this._init_positions = [
            new Vector3(0, 0, 0),
            new Vector3(1, 0, 0),
            new Vector3(1, -1, 0),
            new Vector3(0, -1, 0),
            new Vector3(0, 0, 1),
            new Vector3(1, 0, 1),
            new Vector3(1, -1, 1),
            new Vector3(0, -1, 1),
        ];

        this.vertices = [];
        for (let p of this._init_positions) {
            this.vertices.push(new TVertex3D(p));
        }
        for (let i in this.vertices) {
            let v = this.vertices[i];
            let p = this._init_positions[i];

            p.x = p.x * this.W;
            p.y = p.y * this.D;
            p.z = p.z * this.H;


            v.pos.copy(p);
            v.i_pos.copy(p);
        }

        this.faces = [];
        this.faces.push(new TFace3D([3, 2, 1, 0], this));
        this.faces.push(new TFace3D([4, 5, 6, 7], this));

        this.faces.push(new TFace3D([0, 1, 5, 4], this));
        this.faces.push(new TFace3D([1, 2, 6, 5], this));
        this.faces.push(new TFace3D([2, 3, 7, 6], this));
        this.faces.push(new TFace3D([3, 0, 4, 7], this));



        this._model_matrix = new Matrix4();




    }


    static BasicParamsToMatrix4(basic_params: I_BoardBasicParams) {
        let px = basic_params.PX || 0;
        let py = basic_params.PY || 0;
        let pz = basic_params.PZ || 0;
        let rx = basic_params.RX || 0;
        let ry = basic_params.RY || 0;
        let rz = basic_params.RZ || 0;


        let euler = new Euler(rx / 180 * Math.PI, ry / 180 * Math.PI, rz / 180 * Math.PI, "ZYX");
        let offset = new Vector3(px, py, pz);

        let matrix = new Matrix4().makeRotationFromEuler(euler);
        matrix.setPosition(offset);

        return matrix;
    }

    setAndApplyModelMatrix(matrix: Matrix4) {
        this._model_matrix = matrix.clone();
        for (let v of this.vertices) {
            v.pos.applyMatrix4(matrix);
        }
        for (let f of this.faces) {
            f.computeNor();
        }

        if (this.HoleRects) {
            for (let hole_rect of this.HoleRects) {
                if (hole_rect.face) {
                    hole_rect.face.computeNor();
                }
            }
        }
    }

    getFaceListByNormal(nor: Vector3Like = { x: 0, y: 0, z: -1 }) {
        let faces: TFace3D[] = [];
        for (let f of this.faces) {
            if (f.face_nor.dot(nor) > 0.9) {
                faces.push(f);
            }
        }
        return faces;
    }

    getFaceByNormal(nor: Vector3Like = { x: 0, y: 0, z: -1 }) {
        let faces = this.getFaceListByNormal(nor);
        return faces[0] || null;
    }
    get z_faces() {
        return this.getFaceListByNormal();
    }

    get front_face() {
        return this.faces[this._front_f_id];
    }

    get back_normal() {
        let v = new Vector3(0, 1, 0);
        v.applyMatrix4(this._model_matrix);
        let v0 = new Vector3();
        v0.applyMatrix4(this._model_matrix);
        v.sub(v0);
        v.normalize();
        v.y = -v.y;
        return v;
    }

    get left_normal() {
        let v = new Vector3(1, 0, 0);
        v.applyMatrix4(this._model_matrix);
        let v0 = new Vector3();
        v0.applyMatrix4(this._model_matrix);
        v.sub(v0);
        v.normalize();
        v.y = -v.y;
        return v;
    }

    get bottom_back_center() {
        let pos = (this.vertices[0].pos.clone().add(this.vertices[1].pos.clone()).multiplyScalar(0.5));

        pos.x = 80000 + pos.x;
        pos.y = 80000 - pos.y;
        return pos;
    }
    static getT_ZValInPoly(poly: ZPolygon, p: Vector3) {
        for (let i = 0; i < poly.vertices.length - 2; i++) {
            let vlist: ZVertex[] = [poly.vertices[0], poly.vertices[i], poly.vertices[i + 1]];
            let areas: number[] = [];

            let main_area: number = (vlist[1].pos.clone().sub(vlist[0].pos)).cross(vlist[2].pos.clone().sub(vlist[0].pos)).length();

            for (let j = 0; j < vlist.length; j++) {
                let dv0 = p.clone().sub(vlist[j].pos);
                let dv1 = p.clone().sub(vlist[(j + 1) % vlist.length].pos);
                areas.push(dv0.cross(dv1).length());
            }

            let sum_aera = 0.;
            for (let area of areas) sum_aera += area;

            if (Math.abs(sum_aera - main_area) < 1.) {
                let t_val = 0.;
                for (let j = 0; j < areas.length; j++) {
                    let v0 = vlist[(j + 2) % areas.length];
                    t_val += (v0._t_zval * (areas[j] / sum_aera));
                }
                return t_val;
            }
        }

        return -10000;

    }


    static getT_ZValInEdge(edge: ZEdge, p: Vector3) {
        let pp = edge.projectEdge2d(p);

        let tval0 = edge.v0._t_zval;
        let tval1 = edge.v1._t_zval;

        let alpha = pp.x / edge.length;

        let ans_tval = tval0 * (1. - alpha) + tval1 * alpha;

        return ans_tval;
    }


    static computePolyOcclusion(polys: ZPolygon[], is_quiet: boolean = true) {
        for (let poly of polys) {
            let zz = -10000;
            for (let v of poly.vertices) {
                zz = Math.max(v._t_zval || 0, zz);
            }
            poly.zval = zz;
            poly.computeZNor();


            let t_points: Vector3[] = [];
            for (let v of poly.vertices) {
                let pos = v.pos.clone();
                pos.z = v._t_zval;
                t_points.push(pos);
            }
            let p0 = poly.vertices[0].pos.clone();
            let p1 = poly.vertices[1].pos.clone();
            let p2 = poly.vertices[2].pos.clone();
            p0.z = poly.vertices[0]._t_zval;
            p1.z = poly.vertices[1]._t_zval;
            p2.z = poly.vertices[2]._t_zval;
            let p_nor = (p2.clone().sub(p0)).cross(p1.clone().sub(p0)).normalize();

            let center = p0.clone();
            let dx = (p1.clone().sub(p0)).normalize();
            let dy = (p2.clone().sub(p1)).normalize();
            poly._attached_elements['points_3d'] = t_points;
            poly._attached_elements['plane_data'] = { nor: p_nor, p0: center, dir_x: dx, dir_y: dy };
            poly._attached_elements['int_edges'] = [];
        }

        for (let poly0 of polys) {
            let points0: Vector3[] = poly0._attached_elements['points_3d'];

            for (let poly1 of polys) {
                if (poly0 == poly1) continue;
                let points1: Vector3[] = poly1._attached_elements['points_3d'];

                let plane0: { nor: Vector3, p0: Vector3, dir_x: Vector3, dir_y: Vector3 } = poly0._attached_elements['plane_data'];
                let plane1: { nor: Vector3, p0: Vector3, dir_x: Vector3, dir_y: Vector3 } = poly1._attached_elements['plane_data'];

                if (Math.abs(plane0.nor.dot(plane1.nor)) >= 0.1) continue;

                // 暂时只处理垂直交线
                let dir = plane0.nor.clone().cross(plane1.nor.clone()).normalize();
                let p_nor0 = dir.clone().cross(plane0.nor).normalize(); // 交线的垂线
                let p_nor1 = dir.clone().cross(plane1.nor).normalize();
                let val0 = plane0.p0.clone().sub(plane1.p0).dot(plane1.nor);
                let val1 = p_nor0.clone().dot(plane1.nor);
                let vk = -val0 / val1;

                let pp = plane0.p0.clone().add(p_nor0.clone().multiplyScalar(vk)); // 线上点

                // console.log(val1,pp.clone().sub(plane0.p0).dot(plane0.nor), pp.clone().sub(plane1.p0).dot(plane1.nor));

                // 计算交点
                let e_points0: Vector3[] = [];
                let e_points1: Vector3[] = [];
                let insert_point = (int_p: Vector3, points: Vector3[], e_points: Vector3[]) => {
                    let has_val = false;
                    for (let e_pp of points) {
                        if (e_pp.distanceTo(int_p) < 3.) {
                            has_val = true;
                            break;
                        }
                    }
                    if (!has_val) {
                        e_points.push(int_p);
                    }
                }
                for (let k = 0; k < 2; k++) {
                    let tt_points = k == 0 ? points0 : points1;
                    let e_points = k == 0 ? e_points0 : e_points1;
                    let p_nor = k == 0 ? p_nor0 : p_nor1;
                    for (let i = 0; i < tt_points.length; i++) {
                        let p0 = tt_points[i];
                        let p1 = tt_points[(i + 1) % tt_points.length];

                        let side0 = p0.clone().sub(pp).dot(p_nor);
                        let side1 = p1.clone().sub(pp).dot(p_nor);

                        let dv_p = p1.clone().sub(p0);
                        if (Math.abs(side0) < 0.01) {
                            insert_point(p0.clone(), tt_points, e_points);
                        }
                        else if ((side0 < 0 && side1 > 0) || (side0 > 0 && side1 < 0)) {
                            let int_p = p0.clone().add(dv_p.clone().multiplyScalar(Math.abs(side0) / (Math.abs(side0) + Math.abs(side1))));
                            if (int_p) {
                                insert_point(int_p, tt_points, e_points);
                            }
                        }
                    }
                }

                if (e_points0.length == 2 && e_points1.length == 2) {




                    let dv = e_points0[1].clone().sub(e_points0[0]);

                    let len = dv.length();
                    dv.normalize();
                    let ll = e_points1[0].clone().sub(e_points0[0]).dot(dv);
                    let rr = e_points1[1].clone().sub(e_points0[0]).dot(dv);

                    if (ll > rr) {
                        let tmp = ll; ll = rr; rr = tmp;
                    }
                    if (rr < 0 || ll > len) continue;


                    ll = Math.max(0, ll);
                    rr = Math.min(rr, len);


                    let e_p0 = e_points0[0].clone().add(dv.clone().multiplyScalar(ll));
                    let e_p1 = e_points0[0].clone().add(dv.clone().multiplyScalar(rr));

                    let t_edge = new ZEdge({ pos: e_p0.clone() }, { pos: e_p1.clone() });

                    t_edge.v0._t_zval = t_edge.v0.pos.z;
                    t_edge.v0.pos.z = 0;
                    t_edge.v1._t_zval = t_edge.v1.pos.z;
                    t_edge.v1.pos.z = 0;
                    t_edge.computeNormal();


                    poly0._attached_elements['int_edges'].push(t_edge);



                }


            }
        }

        for (let poly0 of polys) {
            let poly0_edges: ZEdge[] = [];
            for (let edge of poly0.edges) {
                poly0_edges.push(edge);
            }
            for (let edge of poly0._attached_elements['int_edges']) {
                poly0_edges.push(edge);
            }

            for (let edge of poly0_edges) {
                let occlusion_pairs: number[][] = [];
                for (let poly1 of polys) {
                    if (poly0 == poly1) continue;
                    let t_check = false;

                    if (poly0.ex_prop['board_name'] && poly0.ex_prop['board_name'].indexOf("basin_0_0") >= 0) {
                        if (poly0.edges.indexOf(edge) == 0) {
                            // console.log(poly0.edges.indexOf(edge));
                            // t_check = true;
                            // console.log(poly0,poly1);
                            // console.log(edge.length);
                            // t_check = true;

                        }
                    }
                    let t_points: { x: number, z0: number, z1: number, dz?: number, is_valid?: boolean, zc0?: number, zc1?: number }[] = [];
                    for (let t_edge of poly1.edges) {
                        let int_p = edge.getIntersection(t_edge);
                        if (!int_p) continue;
                        let pp_t = t_edge.projectEdge2d(int_p);
                        if (pp_t.x < 0 || pp_t.x > t_edge.length) continue;


                        let pp_e = edge.projectEdge2d(int_p);

                        let z0 = TBoard3D.getT_ZValInEdge(edge, int_p);
                        let z1 = TBoard3D.getT_ZValInEdge(t_edge, int_p);

                        // if(z0 < z1 - 0.1)
                        // {
                        //     continue;
                        // }

                        let is_valid = pp_e.x >= 0 && pp_e.x <= edge.length;
                        let has_val = false;
                        for (let tx of t_points) {
                            if ((Math.abs(tx.x - pp_e.x) < 0.1)) {
                                has_val = true;
                                break;
                            }
                        }
                        if (!has_val) {
                            t_points.push({ x: pp_e.x, z0: z0, z1: z1, is_valid: is_valid });
                        }
                    }

                    t_points.sort((a, b) => a.x - b.x);

                    for (let dp of t_points) {
                        dp.dz = dp.z0 - dp.z1;


                    }

                    for (let ti = 0; ti < t_points.length - 1; ti++) {
                        let t_point0 = t_points[ti];
                        let t_point1 = t_points[ti + 1];
                        {
                            if (t_point0.x > edge.length || t_point1.x < 0) continue;

                            if (t_check) {

                                // console.log('t', t_points);
                            }
                            let dz0 = t_point0.z0 - t_point0.z1;
                            let dz1 = t_point1.z0 - t_point1.z1;

                            if (!t_point0.is_valid && !t_point1.is_valid) {
                                let alpha = Math.abs(t_point0.x / (t_point1.x - t_point0.x));

                                let zc0 = t_point0.z0 * (1. - alpha) + t_point1.z0 * alpha;
                                let zc1 = t_point0.z1 * (1. - alpha) + t_point1.z1 * alpha;

                                if (zc0 > zc1 + 0.01) continue;
                                // console.log(zc0,zc1);


                            }
                            else if (t_point0.is_valid && !t_point1.is_valid) {
                                if (dz0 > 0.01) continue;
                                if (Math.abs(dz0) < 0.01) {
                                    if (dz1 > 0.01) continue;
                                }
                            }
                            else if (t_point1.is_valid && !t_point0.is_valid) {
                                if (dz1 > 0.01) continue;
                                if (Math.abs(dz1) < 0.01) {
                                    if (dz0 > 0.01) continue;
                                }
                            }
                            else {

                                if (dz0 > dz1) {
                                    let tmp = dz0; dz0 = dz1; dz1 = tmp;
                                }

                                if (dz1 > 0.1) {
                                    continue;

                                }
                            }



                            if (Math.abs(dz0) < 0.01 && Math.abs(dz1) < 0.01) {
                                continue;
                            }
                            let pair = [t_point0.x, t_point1.x];
                            pair.sort((a, b) => a - b);
                            occlusion_pairs.push(pair);
                            if (t_check) {
                                console.log("tttt", t_points, pair, edge.length, poly1.ex_prop['board_name']);
                            }

                        }
                    }

                }


                let visible_pairs = range_substract(edge.length, occlusion_pairs);
                let invisible_pairs = range_substract(edge.length, visible_pairs);

                let visible_edges: ZEdge[] = [];
                let invisible_edges: ZEdge[] = [];
                for (let pair of visible_pairs) {
                    let pp0 = edge.unprojectEdge2d({ x: pair[0], y: 0 });
                    let pp1 = edge.unprojectEdge2d({ x: pair[1], y: 0 });
                    visible_edges.push(new ZEdge({ pos: pp0 }, { pos: pp1 }));
                }

                edge._attached_elements['visible_edges'] = visible_edges;
                for (let pair of invisible_pairs) {
                    let pp0 = edge.unprojectEdge2d({ x: pair[0], y: 0 });
                    let pp1 = edge.unprojectEdge2d({ x: pair[1], y: 0 });
                    invisible_edges.push(new ZEdge({ pos: pp0 }, { pos: pp1 }));
                }

                edge._attached_elements['invisible_edges'] = invisible_edges;

                // console.log(occlusion_pairs);
            }
        }
    }

    static computePolyOcclusionByThreeJS(polys: ZPolygon[]) {
    }

}