import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { LayoutSchemeXmlJsonParser } from "./LayoutSchemeXmlJsonParser";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { GradeStarsDict, UI_FormatType } from "../../IUIInterface";
import { uploadFileToOss } from "@/Apps/LayoutAI/Utils/file_utils";

interface appletRoom {
    roomId: string;
    roomName: string;
    roomImage: string;
    scoreData: any;
    rate: number;
}
export class LayoutSchemeAppletData
{
    
    /**
     * 组装小程序需要的数据
     * @returns 
     */
   static  async _makeAppletData(){
        let applet = {
            layoutSchemeId: "schemeId",
            rooms: [] as appletRoom[]
        }
        if(!LayoutSchemeXmlJsonParser.Container)
        {
            LayoutSchemeXmlJsonParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutSchemeXmlJsonParser.Container;
        for (let room of container._rooms) {
 
            let layout_scheme = room.toLayoutScheme();
            layout_scheme.computeScores();
            let canvas = document.createElement("canvas") as HTMLCanvasElement;
            layout_scheme.drawOnCanvas(container.painter,canvas,576,576);
            let imgUrl = canvas.toDataURL();
            let scoreData = [];
            if(layout_scheme._layout_scores){
                for(let key of layout_scheme._layout_scores){
                    let children = [] as any[];
                    if(key.children && key.children.length > 0){
                        key.children.forEach(child => {
                            children.push({
                                key: child.name,
                                value: child.score,
                            });
                        });
                    }
                    let percent = null;
                    if (key.ui_format?.includes(UI_FormatType.Percentage) && key.name === '空间利用率') {
                        const obj = key.children.find((item: any)=>item.name === '家具利用率')
                        if(obj)
                        {
                            percent = (obj.percent* 100).toFixed(0);
                        }
                    }
                    else if(key.ui_format?.includes(UI_FormatType.Percentage))
                    {
            
                        percent = (key.percent * 100).toFixed(0);
                    }
                    else
                    {
                        percent = null 
                    }
                    scoreData.push({
                        key: key.name,
                        value: key.score,
                        children: children,
                        rate: GradeStarsDict[key.grade],
                        percent: Number(percent),
                    });
                }
            }

            let totalGrade = 0;
            let rate = 0;
            layout_scheme._layout_scores.forEach((item: any) => {
                totalGrade += item.grade;
            })
            if(totalGrade)
            {
                rate = Math.round(totalGrade / layout_scheme._layout_scores.length);
            }


          if(totalGrade)
          {
            rate = Math.round(totalGrade / layout_scheme._layout_scores.length);
          }

            // let imgUrl = await uploadImageToOss(canvas.toDataURL(), "snapShot" + Math.floor(Math.random() * 10000) + ".png");
            applet.rooms.push({
                roomId: room.uid,
                roomName: room.name,
                roomImage: imgUrl,
                scoreData: scoreData,
                rate: GradeStarsDict[rate]
            });
        }
        console.log('applet', applet);
        const jsonString = JSON.stringify(applet); // 将 applet 对象转换为 JSON 字符串，格式化为可读的形式
        const file = new File([jsonString], 'applet.json', { type: 'application/json' }); // 创建 File 对象
        let ossUrl = await uploadFileToOss(file, "snapShot" + Math.floor(Math.random() * 10000) + ".json");
        console.log('ossUrl', ossUrl);
        return ossUrl;
    }
}