import { ZRect } from "@layoutai/z_polygon";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { I_Range2D, TBaseRoomToolUtil } from "../../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TRoom } from "../../TRoom";
import { WPolygon } from "../../TFeatureShape/WPolygon";
import { TLivingRoomToolUtil } from "../../TLayoutScore/CheckRules/LivingRoomCheckRules/TLivingRoomToolUtil";
import { Vector3 } from "three";
import { ZEdge } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";

const xDir: Vector3 = new Vector3(1, 0, 0);
const yDir: Vector3 = new Vector3(0, 1, 0);

export function Range2DtoRect(range:I_Range2D)
{
    let v0: Vector3 = new Vector3(range.xMin, range.yMin, 0);
    let v1: Vector3 = new Vector3(range.xMax, range.yMin, 0);
    let v2: Vector3 = new Vector3(range.xMax, range.yMax, 0);
    let v3: Vector3 = new Vector3(range.xMin, range.yMax, 0);
    let poly = new ZPolygon();
    poly.initByVertices([v0, v1, v2, v3]);
    let zrect = ZRect.computeMainRect(poly);
 
    return zrect;
}
export function splitSpaceForLivingRoom(room: TRoom, figures: TFigureElement[]) : {[key:string]:I_Range2D[]}
{
    if(!figures || figures.length == 0)
    {
        return null;
    }
    let minDiningSpaceRanges: Map<Vector3, I_Range2D> = calMinDiningSpaceRanges(room, figures);
    let minLivingSpaceRanges: Map<Vector3, I_Range2D> = calMinLivingSpaceRanges(room, figures);
    let minEntranceSpaceRanges: I_Range2D[] = calMinEntranceSpaceRanges(room, figures);
    let allHallwayRects: ZRect[] = WPolygon._computeHallwayRects(room.room_shape._poly, 1200).filter(rect => {
        let isOverlay: boolean = false;
        let hallwayRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect);
        if(!isOverlay)
        {
            for(let minLivingSpaceRange of minLivingSpaceRanges)
            {
                if(TBaseRoomToolUtil.instance.isOverlayRange2ds(hallwayRange, minLivingSpaceRange, false))
                {
                    isOverlay = true;
                    break;
                }
            }
        }

        if(!isOverlay)
        {
            for(let minDiningSpaceRange of minDiningSpaceRanges)
            {
                if(TBaseRoomToolUtil.instance.isOverlayRange2ds(hallwayRange, minDiningSpaceRange, false))
                {
                    isOverlay = true;
                    break;
                }
            }
        }
        return rect.max_hh > 1200 && rect.min_hh > 500 && !isOverlay;
    });
    let livingSpaceRangeMap: Map<Vector3, I_Range2D> = TBaseRoomToolUtil.instance.expandeRangeToWall(minLivingSpaceRanges, room, minDiningSpaceRanges);
    let diningSpaceRangeMap: Map<Vector3, I_Range2D> = TBaseRoomToolUtil.instance.expandeRangeToWall(minDiningSpaceRanges, room, livingSpaceRangeMap);
    let livingSpaceRanges: I_Range2D[] = Array.from(livingSpaceRangeMap.values());
    let diningSpaceRanges: I_Range2D[] = Array.from(diningSpaceRangeMap.values());
    let entranceSpaceRanges: any[] = calNewEntranceSpaceRanges(room, figures, livingSpaceRanges, diningSpaceRanges, 1000, 800);
    let hallwaySpaceRanges: any[] = calNewHallwaySpaceRanges(allHallwayRects, room, livingSpaceRanges, diningSpaceRanges, []);
    return {
        livingSpace: livingSpaceRanges, 
        diningSpace: diningSpaceRanges, 
        entranceSpace: entranceSpaceRanges, 
        hallwaySpace: hallwaySpaceRanges,
        minLivingSpace: Array.from(minLivingSpaceRanges.values()),
        minDiningSpace: Array.from(minDiningSpaceRanges.values()),
    };
}

function unionRanges(ranges: I_Range2D[]): I_Range2D
{
    if(!ranges || ranges.length == 0)
    {
        return null;
    }
    let xMin: number = Number.POSITIVE_INFINITY;
    let xMax: number = Number.NEGATIVE_INFINITY;
    let yMin: number = Number.POSITIVE_INFINITY;
    let yMax: number = Number.NEGATIVE_INFINITY;
    for(let range of ranges)
    {
        if(xMin > range.xMin)
        {
            xMin = range.xMin;
        }
        if(xMax < range.xMax)
        {
            xMax = range.xMax;
        }
        if(yMin > range.yMin)
        {
            yMin = range.yMin;
        }
        if(yMax < range.yMax)
        {
            yMax = range.yMax;
        }
    }
    return {xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax};
}

function unionRangeByFigures(figures: TFigureElement[], uuid: string): I_Range2D
{
    let targetRange = null;
    for(let figure of figures)
    {
        if(uuid.length > 0 && figure._group_uuid != uuid)
        {
            continue;
        }
        let otherFigureRange = TBaseRoomToolUtil.instance.getRange2dByPolygon(figure.rect);
        if(!targetRange)
        {
            targetRange = otherFigureRange;
        }
        else
        {
            let xMin: number = Math.min(targetRange.xMin, otherFigureRange.xMin);
            let xMax: number = Math.max(targetRange.xMax, otherFigureRange.xMax);
            let yMin: number = Math.min(targetRange.yMin, otherFigureRange.yMin);
            let yMax: number = Math.max(targetRange.yMax, otherFigureRange.yMax);
            targetRange = {xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax};
        }
    }
    return targetRange;
}

export function calMinDiningSpaceRanges(room: TRoom, figures: TFigureElement[]): Map<Vector3, I_Range2D> 
{
    let diningTableFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getDiningTableFigures(figures);
    let diningChairFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getDiningChairFigures(figures);
    let diningCabinetFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getDiningCabinetFigures(figures);
    let diningSpaceRanges: Map<Vector3, I_Range2D> = new Map<Vector3, I_Range2D>();
    for(let diningTableFigure of diningTableFigures)
    {
        let diningSpaceRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(diningTableFigure.rect);
        let otherRange: any = unionRangeByFigures(diningChairFigures, diningTableFigure._group_uuid);
        if(otherRange)
        {
            diningSpaceRange = unionRanges([otherRange, diningSpaceRange]);
        }
        expandDiningRange(diningSpaceRange, room, diningCabinetFigures);
        diningSpaceRanges.set(diningTableFigure.rect.rect_center.clone(), diningSpaceRange);
    }
    return diningSpaceRanges;
}

export function calMinEntranceSpaceRanges(room: TRoom, figures: TFigureElement[]): any
{
    let entranceCabinetFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getEntranceCabinetFigures(figures);
    let entranceSpaceRanges: any[] = [];
    for(let entranceCabinetFigure of entranceCabinetFigures)
    {
        let entranceSpaceRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(entranceCabinetFigure.rect);
        entranceSpaceRanges.push(entranceSpaceRange);
    }
    return entranceSpaceRanges;
}

export function calMinLivingSpaceRanges(room: TRoom, figures: TFigureElement[]): Map<Vector3, I_Range2D>
{
    let sofaFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getSofaFigures(figures);
    let livingCabinetFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getLivingCabinetFigures(figures);
    let otherLivingFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getLivingOtherToCreateSpaceFigures(figures);
    let sofaSpaceRanges: Map<Vector3, I_Range2D> = new Map<Vector3, I_Range2D>();
    for(let sofaFigure of sofaFigures)
    {
        let sofaSpaceRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(sofaFigure.rect);
        // 扩展原始区域到电视柜以及包含沙发组合周边体（如果前方有电视柜的情况下优先电视柜，无电视柜则包含沙发前方的图元）
        // 这一步的本意并非要获取确切的范围值，只是拿去一个大概往四周延拓，如果前后方柜体距离超过设定的限制值，则此柜体失效
        expandSofaRange(sofaSpaceRange, sofaFigure, room, livingCabinetFigures, otherLivingFigures, 3000);
        sofaSpaceRanges.set(sofaFigure.rect.rect_center.clone(), sofaSpaceRange);
    }
    return sofaSpaceRanges;
}

export function calNewEntranceSpaceRanges(room: TRoom, figures: TFigureElement[], livingSpaceRanges: I_Range2D[], diningSpaceRanges: I_Range2D[], entranceSpaceExtendLen1: number, entranceSpaceExtendLen2: number): any
{
    let xDir: Vector3 = new Vector3(1, 0, 0);
    let yDir: Vector3 = new Vector3(0, 1, 0);
    let entranceCabinetFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getEntranceCabinetFigures(figures);
    let entranceSpaceRanges: any[] = [];
    if(entranceCabinetFigures.length == 0)
    {
        return entranceSpaceRanges;
    }
    for(let entranceCabinetFigure of entranceCabinetFigures)
    {
        let entranceSpaceRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(entranceCabinetFigure.rect);
        let fronEdge: ZEdge = null;
        let frontMinDist: number = Number.POSITIVE_INFINITY;
        room.room_shape._poly.edges.forEach(edge => {
            if(TBaseRoomToolUtil.instance.isParallelTwoEdges(entranceCabinetFigure.rect.frontEdge, edge) 
                && TBaseRoomToolUtil.instance.edgeIsInFrontArea(entranceCabinetFigure.rect.frontEdge, entranceCabinetFigure.rect.backEdge, edge))
            {
                let dist: number = TBaseRoomToolUtil.instance.calDistance(entranceCabinetFigure.rect.frontEdge, edge);
                if(dist < frontMinDist && dist <= entranceSpaceExtendLen1)
                {
                    frontMinDist = dist;
                    fronEdge = edge;
                }
            }
        });
        let xDot: number = entranceCabinetFigure.rect.nor.clone().dot(xDir);
        let yDot: number = entranceCabinetFigure.rect.nor.clone().dot(yDir);
        if(!fronEdge)
        {
            if(Math.abs(xDot) > 0.9)
            {
                if(xDot > 0)
                {
                    entranceSpaceRange.xMax += entranceSpaceExtendLen2;
                }
                else
                {
                    entranceSpaceRange.xMin -= entranceSpaceExtendLen2;
                }
            }
            else if(Math.abs(yDot) > 0.9)
            {
                if(yDot > 0)
                {
                    entranceSpaceRange.yMax += entranceSpaceExtendLen2;
                }
                else
                {
                    entranceSpaceRange.yMin -= entranceSpaceExtendLen2;
                }
            }
        }
        else
        {
            let frontEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(fronEdge);
            if(Math.abs(xDot) > 0.9)
            {
                if(xDot > 0)
                {
                    entranceSpaceRange.xMax = Math.max(entranceSpaceRange.xMax, frontEdgeRange.xMin);
                }
                else
                {
                    entranceSpaceRange.xMin = Math.min(entranceSpaceRange.xMin, frontEdgeRange.xMax);
                }
            }
            else if(Math.abs(yDot) > 0.9)
            {
                if(yDot > 0)
                {
                    entranceSpaceRange.yMax = Math.max(entranceSpaceRange.yMax, frontEdgeRange.yMin);
                }
                else
                {
                    entranceSpaceRange.yMin = Math.min(entranceSpaceRange.yMin, frontEdgeRange.yMax);
                }
            }
        }
        // cutEntranceSpaceByLivingSpaceOrDiningSpace(entranceSpaceRange, livingSpaceRanges);
        // cutEntranceSpaceByLivingSpaceOrDiningSpace(entranceSpaceRange, diningSpaceRanges);
        extendRangeToNearWall(entranceSpaceRange, [...livingSpaceRanges, ...diningSpaceRanges], room, entranceSpaceExtendLen1);
        let entranceSpaceRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(entranceSpaceRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(entranceSpaceRange));
        if(entranceSpaceRect.min_hh > 200)
        {
            entranceSpaceRanges.push(entranceSpaceRange);
        }
    }
    return entranceSpaceRanges;
}

export function calNewHallwaySpaceRanges(allHallwayRects: ZRect[], room: TRoom, livingSpaceRanges: any[], diningSpaceRanges: any[], entranceSpaceRanges: any[]): any
{
    let hallwaySpaceRanges: any[] = [];
    let toRoomEdgeHallwayRects: ZRect[] = [];
    for(let hallwayRect of allHallwayRects)
    {
        let otherHallwayRects: ZRect[] = allHallwayRects.filter(rect => rect != hallwayRect);
        let tempHallwayRange: any = calExtendToRoomEdgeRange(hallwayRect, room, otherHallwayRects, [...livingSpaceRanges,...diningSpaceRanges,...entranceSpaceRanges]);
        let tempRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(tempHallwayRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(tempHallwayRange));
        let dot: number = Math.abs(tempRect.nor.clone().dot(hallwayRect.nor));
        if(dot < 0.1)
        {
            let tempCenter: Vector3 = tempRect.rect_center.clone();
            let tempW: number = tempRect.length;
            let tempH: number = tempRect.depth;
            tempRect.nor = hallwayRect.nor.clone();
            tempRect.length = tempH;
            tempRect.depth = tempW;
            tempRect.rect_center = tempCenter;
            tempRect.updateRect();
        }
        toRoomEdgeHallwayRects.push(tempRect);
    }
    let newToRoomEdgeHallwayRects: ZRect[] = [];
    // 1. 先筛选出与任何过道无重叠的过道到新记录的过道中
    for(let i = 0; i < toRoomEdgeHallwayRects.length; ++i)
    {
        let isOverlayOtherHallway: boolean = false;
        let overlayOtherHallwayRect: ZRect = null;
        for(let j = 0; j < toRoomEdgeHallwayRects.length; ++j)
        {
            if(i == j)
            {
                continue;
            }
            if(TBaseRoomToolUtil.instance.isOverlayByRects(toRoomEdgeHallwayRects[i], toRoomEdgeHallwayRects[j], true))
            {
                isOverlayOtherHallway = true;
                overlayOtherHallwayRect = toRoomEdgeHallwayRects[j];
                break;
            }
        }
        if(!isOverlayOtherHallway)
        {
            newToRoomEdgeHallwayRects.push(toRoomEdgeHallwayRects[i]);
        }
        else
        {
            let subPolys1: ZPolygon[] = toRoomEdgeHallwayRects[i].substract(overlayOtherHallwayRect);
            let subPolys2: ZPolygon[] = overlayOtherHallwayRect.substract(toRoomEdgeHallwayRects[i]);
            if(subPolys1.length == 1 && subPolys1[0].edges.length == 4)
            {
                let subPloyToRect1: ZRect = getRectByPoly(subPolys1[0], toRoomEdgeHallwayRects[i]); 
                if(subPolys2.length == 1 && subPolys2[0].edges.length == 4)
                {
                    let subPloyToRect2: ZRect = getRectByPoly(subPolys2[0], overlayOtherHallwayRect);
                    if(subPloyToRect1.length > subPloyToRect2.length)
                    {
                        newToRoomEdgeHallwayRects.push(toRoomEdgeHallwayRects[i]);
                    }
                    else
                    {
                        newToRoomEdgeHallwayRects.push(subPloyToRect1);
                    }
                }
                else
                {
                    newToRoomEdgeHallwayRects.push(subPloyToRect1);
                }
            }
            else if(subPolys1.length == 2 && subPolys2.length == 2 && subPolys1[0].edges.length == 4 && subPolys1[1].edges.length == 4)
            {
                let subPloyToRect1: ZRect = getRectByPoly(subPolys1[0], toRoomEdgeHallwayRects[i]);
                let subPloyToRect2: ZRect = getRectByPoly(subPolys1[1], toRoomEdgeHallwayRects[i]);
                newToRoomEdgeHallwayRects.push(subPloyToRect1);
                newToRoomEdgeHallwayRects.push(subPloyToRect2);
            }
            else
            {
                newToRoomEdgeHallwayRects.push(toRoomEdgeHallwayRects[i]);
            }
        }
    }

    // 过道与过道之间纵向对着
    let newAddHallyWaySpaceRanges: any[] = [];
    for(let i = 0; i < newToRoomEdgeHallwayRects.length; ++i)
    {
        for(let j = i + 1; j < newToRoomEdgeHallwayRects.length; ++j)
        {
            let newHallwayRange: any = getNewHallwayRange(newToRoomEdgeHallwayRects[i], newToRoomEdgeHallwayRects[j], room, [...livingSpaceRanges, ...diningSpaceRanges, ...entranceSpaceRanges, ...hallwaySpaceRanges]);
            if(newHallwayRange)
            {
                newAddHallyWaySpaceRanges.push(newHallwayRange);
            }
        }
        hallwaySpaceRanges.push(TBaseRoomToolUtil.instance.getRange2dByPolygon(newToRoomEdgeHallwayRects[i]));
    }
    hallwaySpaceRanges.push(...newAddHallyWaySpaceRanges);
    return hallwaySpaceRanges;
}

function extendRangeToNearWall(range: any, otherRanges: any[], room: TRoom, extendLen1: number): any
{
    // 循环找最近
    let recordIndexs: number[] = [];
    let xDir: Vector3 = new Vector3(1, 0, 0);
    let yDir: Vector3 = new Vector3(0, 1, 0);
    while(true)
    {
        if(!TBaseRoomToolUtil.instance.isVaildRange(range))
        {
            break;
        }
        let rangeCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(range);
        let rangeRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(range, rangeCenter);
        let nearRoomEdge: ZEdge = null;
        let minRoomEdgeDist: number = Number.POSITIVE_INFINITY;
        let nearRectIndex: number = null;
        for(let i = 0; i < 4; ++i)
        { 
            let firstEdge: ZEdge = rangeRect.edges[i];
            let secondEdge: ZEdge = rangeRect.edges[(i + 2) % 4];
            if(recordIndexs.includes(i) || TBaseRoomToolUtil.instance.edgeOnPolygon(firstEdge, room.room_shape._poly, 5, 0.1))
            {
                if(!recordIndexs.includes(i))
                {
                    recordIndexs.push(i);
                }
                continue;
            }
            for(let roomEdge of room.room_shape._poly.edges)
            {
                if(Math.abs(firstEdge.dv.clone().dot(roomEdge.dv)) > 0.9  && TBaseRoomToolUtil.instance.edgeIsInFrontArea(firstEdge, secondEdge, roomEdge, 0, true))
                {
                    let dist: number = TBaseRoomToolUtil.instance.calDistance(firstEdge, roomEdge);
                    let minOtherDist: number = Number.POSITIVE_INFINITY;
                    let isInFrontArea: boolean = false;
                    for(let otherRange of otherRanges)
                    {
                        let otherRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(otherRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(otherRange));
                        if(TBaseRoomToolUtil.instance.polygonIsInFrontArea(firstEdge, secondEdge, otherRect))
                        {
                            isInFrontArea = true;
                            let otherDist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(firstEdge, otherRect);
                            if(otherDist < minOtherDist)
                            {
                                minOtherDist = otherDist;
                            }
                        }
                    }

                    if(dist < extendLen1 && dist < minRoomEdgeDist && (isInFrontArea ? minOtherDist > dist : true))
                    {
                        minRoomEdgeDist = dist;
                        nearRoomEdge = roomEdge;
                        nearRectIndex = i;
                    }
                }
            }
        }
        if(nearRoomEdge)
        {
            recordIndexs.push(nearRectIndex);
            let nearRoomEdgeDv: Vector3 = nearRoomEdge.dv.clone();
            
            let xDot: number = nearRoomEdgeDv.clone().dot(xDir);
            let yDot: number = nearRoomEdgeDv.clone().dot(yDir);
            let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge((nearRoomEdge));
            if(Math.abs(xDot) > 0.9)
            {
                if(range.yMax < roomEdgeRange.yMin)
                {
                    range.yMax = roomEdgeRange.yMin;
                }
                else if(range.yMin > roomEdgeRange.yMax)
                {
                    range.yMin = roomEdgeRange.yMax;
                }
            }
            else if(Math.abs(yDot) > 0.9)
            {
                if(range.xMax < roomEdgeRange.xMin )
                {
                    range.xMax = roomEdgeRange.xMin;
                }
                else if(range.xMin > roomEdgeRange.xMax)
                {
                    range.xMin = roomEdgeRange.xMax;
                }
            }
        }
        if(recordIndexs.length == 4 || !nearRoomEdge)
        {
            break;
        }
    }
    // 在完成扩展之后再检查一遍是否有墙被包含在此范围内，若有被包含则提取此墙对这个范围进行分割
    range = cutRangeByContainEdge(range, room.room_shape._poly.edges);
    return range;
}

function cutRangeByContainEdge(range: any, edges: ZEdge[]): any
{
    for(let roomEdge of edges)
    {
        let edgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
        if(!TBaseRoomToolUtil.instance.isOverlayRange2ds(edgeRange, range, false))
        {
            continue;
        }
        let edgeRect: ZRect = new ZRect(roomEdge.length, 0);
        edgeRect.u_dv = roomEdge.dv.clone();
        range = getSplitRangeByOtherRange(range, edgeRect);
    }
    return range;
}

function getSplitRangeByOtherRange(range: any, hallwayRect: ZRect): any
{
    let cutDv: Vector3 = hallwayRect.dv.clone();
    let hallwayRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(hallwayRect);
    // 确定完裁剪方向后，再对我们的目标区域进行裁剪
    let xDir: Vector3 = new Vector3(1, 0, 0);
    let yDir: Vector3 = new Vector3(0, 1, 0);
    let xDot: number = cutDv.clone().dot(xDir);
    let yDot: number = cutDv.clone().dot(yDir);
    let splitTargetRange: any = null;
    if(Math.abs(xDot) > 0.9)
    {
        
        let firstSplitRange: any = {};
        firstSplitRange.xMin = range.xMin; firstSplitRange.xMax = range.xMax; firstSplitRange.yMin = range.yMin; firstSplitRange.yMax = range.yMax;
        firstSplitRange.yMin = Math.max(firstSplitRange.yMin, hallwayRange.yMax);

        let secondSplitRange: any = {};
        secondSplitRange.xMin = range.xMin; secondSplitRange.xMax = range.xMax; secondSplitRange.yMin = range.yMin; secondSplitRange.yMax = range.yMax;
        secondSplitRange.yMax = Math.min(secondSplitRange.yMax, hallwayRange.yMin);

        // 判断这两块分割区域是否为有效区域
        let isFirstVaild: boolean = TBaseRoomToolUtil.instance.isVaildRange(firstSplitRange);
        let isSecondVaild: boolean = TBaseRoomToolUtil.instance.isVaildRange(secondSplitRange);
        if(isFirstVaild && isSecondVaild)
        {
            let firstArea: number = TBaseRoomToolUtil.instance.calAreaForBox2d(firstSplitRange);
            let secondArea: number = TBaseRoomToolUtil.instance.calAreaForBox2d(secondSplitRange);
            splitTargetRange = firstArea > secondArea ? firstSplitRange : secondSplitRange;
        }
        else if(isFirstVaild)
        {
            splitTargetRange = firstSplitRange;
        }
        else if(isSecondVaild)
        {
            splitTargetRange = secondSplitRange;
        }
        else
        {
            splitTargetRange = range;
        }
    }
    else if(Math.abs(yDot) > 0.9)
    {
        let firstSplitRange: any = {};
        firstSplitRange.xMin = range.xMin; firstSplitRange.xMax = range.xMax; firstSplitRange.yMin = range.yMin; firstSplitRange.yMax = range.yMax;
        firstSplitRange.xMin = Math.max(firstSplitRange.xMin, hallwayRange.xMax);

        let secondSplitRange: any = {};
        secondSplitRange.xMin = range.xMin; secondSplitRange.xMax = range.xMax; secondSplitRange.yMin = range.yMin; secondSplitRange.yMax = range.yMax;
        secondSplitRange.xMax = Math.min(secondSplitRange.xMax, hallwayRange.xMin);

        let isFirstVaild: boolean = TBaseRoomToolUtil.instance.isVaildRange(firstSplitRange);
        let isSecondVaild: boolean = TBaseRoomToolUtil.instance.isVaildRange(secondSplitRange);
        if(isFirstVaild && isSecondVaild)
        {
            let firstArea: number = TBaseRoomToolUtil.instance.calAreaForBox2d(firstSplitRange);
            let secondArea: number = TBaseRoomToolUtil.instance.calAreaForBox2d(secondSplitRange);
            splitTargetRange = firstArea > secondArea ? firstSplitRange : secondSplitRange;
        }
        else if(isFirstVaild)
        {
            splitTargetRange = firstSplitRange;
        }
        else if(isSecondVaild)
        {
            splitTargetRange = secondSplitRange;
        }
        else
        {
            splitTargetRange = range;
        }
    }
    return splitTargetRange;
}


function isEntranceOverlayOtherRanges(entranceRange: any, livingSpaceRanges: any[], diningSpaceRanges: any[], entranceSpaceRanges: any[]): boolean
{
    let isOverlay: boolean = false;
    isOverlay = isRangeOverlayOtherRanges(entranceRange, [...livingSpaceRanges, ...diningSpaceRanges, ...entranceSpaceRanges]);
    return isOverlay;;
}

function isRangeOverlayOtherRanges(range: any, otherRanges: any[]): boolean
{
    for(let otherRange of otherRanges)
    {
        if(TBaseRoomToolUtil.instance.isOverlayRange2ds(range, otherRange, false))
        {
            return true;
        }
    }
    return false;
}

function expandSofaRange(sofaSpaceRange: any, sofaFigure: TFigureElement, room: TRoom, livingCabinetFigures: TFigureElement[], otherLivingFigures: TFigureElement[], maxDistanceToCabinet: number = 2000)
{
    let sofaRect: ZRect = sofaFigure.rect;
    let frontEdge: ZEdge = sofaRect.frontEdge;
    let backEdge: ZEdge = sofaRect.backEdge;
    let inFrontSofaEdge: ZEdge = null;
    let inBackSofaEdge: ZEdge = null;
    inFrontSofaEdge = getFrontOrBackEdge(frontEdge, backEdge, room, livingCabinetFigures, maxDistanceToCabinet);
    inBackSofaEdge = getFrontOrBackEdge(backEdge, frontEdge, room, livingCabinetFigures, maxDistanceToCabinet);
    if(!inFrontSofaEdge)
    {
        // 主要是这个扩展到前方茶几之类的
        let allFrontOtherFigures: TFigureElement[] = otherLivingFigures.filter(
            otherFigure => TBaseRoomToolUtil.instance.polygonIsInFrontArea(frontEdge, backEdge, otherFigure.rect));
        if(allFrontOtherFigures.length > 0)
        {
            let otherRange: any= unionRangeByFigures(allFrontOtherFigures, sofaFigure._group_uuid);
            if(otherRange)
            {
                let tempSofaRange: any = unionRanges([otherRange, sofaSpaceRange]);
                sofaSpaceRange.xMin = tempSofaRange.xMin;
                sofaSpaceRange.xMax = tempSofaRange.xMax;
                sofaSpaceRange.yMin = tempSofaRange.yMin;
                sofaSpaceRange.yMax = tempSofaRange.yMax;
            }
        }
    }
    else
    {
        let xDot: number = inFrontSofaEdge.dv.clone().dot(xDir);
        let yDot: number = inFrontSofaEdge.dv.clone().dot(yDir);
        let inFrontEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(inFrontSofaEdge);
        if(Math.abs(xDot) > 0.9)
        {
            sofaSpaceRange.yMin = Math.min(sofaSpaceRange.yMin, inFrontEdgeRange.yMin);
            sofaSpaceRange.yMax = Math.max(sofaSpaceRange.yMax, inFrontEdgeRange.yMax);
        }
        else if(Math.abs(yDot) > 0.9)
        {
            sofaSpaceRange.xMin = Math.min(sofaSpaceRange.xMin, inFrontEdgeRange.xMin);
            sofaSpaceRange.xMax = Math.max(sofaSpaceRange.xMax, inFrontEdgeRange.xMax);
        }
    }

    if(inBackSofaEdge)
    {
        let xDot: number = inBackSofaEdge.dv.clone().dot(xDir);
        let yDot: number = inBackSofaEdge.dv.clone().dot(yDir);
        let inBackEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(inBackSofaEdge);
        if(Math.abs(xDot) > 0.9)
        {
            sofaSpaceRange.yMin = Math.min(sofaSpaceRange.yMin, inBackEdgeRange.yMin);
            sofaSpaceRange.yMax = Math.max(sofaSpaceRange.yMax, inBackEdgeRange.yMax);
        }
        else if(Math.abs(yDot) > 0.9)
        {
            sofaSpaceRange.xMin = Math.min(sofaSpaceRange.xMin, inBackEdgeRange.xMin);
            sofaSpaceRange.xMax = Math.max(sofaSpaceRange.xMax, inBackEdgeRange.xMax);
        }
    }
}


function getFrontOrBackEdge(frontEdge: ZEdge, backEdge: ZEdge, room: TRoom, otherFigures: TFigureElement[], limitDistance: number = 2000): ZEdge
{
    let minDistance: number = null;
    let minEdge: ZEdge = null;
    for(let roomEdge of room.room_shape._poly.edges)
    {
        if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(frontEdge, roomEdge) || !TBaseRoomToolUtil.instance.edgeIsInFrontArea(frontEdge, backEdge, roomEdge))
        {
            continue;
        }
        let roomDist: number = TBaseRoomToolUtil.instance.calDistance(roomEdge, frontEdge);
        if(minDistance == null)
        {
            minDistance = roomDist;
            minEdge = roomEdge;
        }
        if(roomDist < minDistance)
        {
            minDistance = roomDist;
            minEdge = roomEdge;
        }
    }
    let isFindOther: boolean = false;
    for(let otherFigure of otherFigures)
    {
        if(!TBaseRoomToolUtil.instance.polygonIsInFrontArea(frontEdge, backEdge, otherFigure.rect))
        {
            continue;
        }
        let cabinetDist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(frontEdge, otherFigure.rect);
        if(minDistance == null)
        {
            minDistance = cabinetDist;
            minEdge = otherFigure.rect.edges.filter(edge => TBaseRoomToolUtil.instance.isParallelTwoEdges(edge, frontEdge)).sort(
                (edge1, edge2) => TBaseRoomToolUtil.instance.calDistance(edge2, frontEdge) - TBaseRoomToolUtil.instance.calDistance(edge1, frontEdge))[0];
        }
        if(cabinetDist < minDistance)
        {
            isFindOther = true;
            minDistance = cabinetDist;
            minEdge = otherFigure.rect.edges.filter(
                edge => TBaseRoomToolUtil.instance.isParallelTwoEdges(edge, frontEdge)).sort(
                    (edge1, edge2) => TBaseRoomToolUtil.instance.calDistance(edge2, frontEdge) - TBaseRoomToolUtil.instance.calDistance(edge1, frontEdge))[0];
        }
        if(minDistance > limitDistance)
        {
            minDistance = null;
            minEdge = null;
        }
    }
    if(!isFindOther)
    {
        minDistance = null;
        minEdge = null;
    }

    return minEdge;
}

function expandDiningRange(diningSpaceRange: any, room: TRoom, diningCabinetFigures: TFigureElement[], limitDistance: number = 2000)
{
    let diningSpaceRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(diningSpaceRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(diningSpaceRange));
    for(let i = 0; i < diningSpaceRect.edges.length; i++)
    {
        let frontEdge: ZEdge = diningSpaceRect.edges[i];
        let backEdge: ZEdge = diningSpaceRect.edges[(i + 2) % diningSpaceRect.edges.length];
        let minDistance: number = null;
        let limitEdge: ZEdge = null;
        for(let diningCabinetFigure of diningCabinetFigures)
        {
            if(!TBaseRoomToolUtil.instance.polygonIsInFrontArea(frontEdge, backEdge, diningCabinetFigure.rect))
            {
                continue;
            }
            let cabinetDist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(frontEdge, diningCabinetFigure.rect);
            if(!minDistance)
            {
                minDistance = cabinetDist;
                let sameDvEdges: ZEdge[] = diningCabinetFigure.rect.edges.filter(
                    edge => TBaseRoomToolUtil.instance.isParallelTwoEdges(edge, frontEdge));
                limitEdge = sameDvEdges.sort(
                        (edge1, edge2) => TBaseRoomToolUtil.instance.calDistance(edge2, frontEdge) - TBaseRoomToolUtil.instance.calDistance(edge1, frontEdge))[0];
            }
            if(cabinetDist < minDistance)
            {
                minDistance = cabinetDist;
                let sameDvEdges: ZEdge[] = diningCabinetFigure.rect.edges.filter(
                    edge => TBaseRoomToolUtil.instance.isParallelTwoEdges(edge, frontEdge));
                limitEdge = sameDvEdges.sort(
                        (edge1, edge2) => TBaseRoomToolUtil.instance.calDistance(edge2, frontEdge) - TBaseRoomToolUtil.instance.calDistance(edge1, frontEdge))[0];
            }
        }
        if(minDistance == null)
        {
            continue;
        }
        let isLimitByRoom: boolean = false;
        for(let roomEdge of room.room_shape._poly.edges)
        {
            if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(frontEdge, roomEdge) || !TBaseRoomToolUtil.instance.edgeIsInFrontArea(frontEdge, backEdge, roomEdge))
            {
                continue;
            }
            let roomDist: number = TBaseRoomToolUtil.instance.calDistance(roomEdge, frontEdge);
            if(roomDist < minDistance)
            {
                minDistance = roomDist;
                limitEdge = roomEdge;
                isLimitByRoom = true;
            }
        }
        if(limitEdge && limitDistance < minDistance)
        {
            minDistance = null;
            limitEdge = null;
            continue;
        }
        if(limitEdge)
        {
            let inFrontEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(limitEdge);
            if(isLimitByRoom)
            {
                diningSpaceRange.xMin = Math.min(diningSpaceRange.xMin, inFrontEdgeRange.xMin);
                diningSpaceRange.xMax = Math.max(diningSpaceRange.xMax, inFrontEdgeRange.xMax);
                diningSpaceRange.yMin = Math.min(diningSpaceRange.yMin, inFrontEdgeRange.yMin);
                diningSpaceRange.yMax = Math.max(diningSpaceRange.yMax, inFrontEdgeRange.yMax);
            }
            else
            {
                let xDot: number = limitEdge.dv.clone().dot(xDir);
                let yDot: number = limitEdge.dv.clone().dot(yDir);
                if(Math.abs(xDot) > 0.9)
                {
                    diningSpaceRange.yMin = Math.min(diningSpaceRange.yMin, inFrontEdgeRange.yMin);
                    diningSpaceRange.yMax = Math.max(diningSpaceRange.yMax, inFrontEdgeRange.yMax);
                }
                else if(Math.abs(yDot) > 0.9)
                {
                    diningSpaceRange.xMin = Math.min(diningSpaceRange.xMin, inFrontEdgeRange.xMin);
                    diningSpaceRange.xMax = Math.max(diningSpaceRange.xMax, inFrontEdgeRange.xMax);
                }
            }
        }
    }
}


function getExtendToRoomEdgeRange(hallwayRect: ZRect, room: TRoom, otherHallwayRects: ZRect[], otherRanges: any[]): any
{
    let hallywayRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(hallwayRect);
    let firstEdge: ZEdge = hallwayRect.leftEdge;
    let secondEdge: ZEdge = hallwayRect.rightEdge;
    let minDistanceRoomEdge: ZEdge = null;
    let minDistance: number = null;
    for(let roomEdge of room.room_shape._poly.edges)
    {
        if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(firstEdge, roomEdge))
        {
            continue;
        }
        if(TBaseRoomToolUtil.instance.edgeIsInFrontArea(firstEdge, secondEdge, roomEdge) && !firstEdge.islayOn(roomEdge))
        {
            let dist: number = TBaseRoomToolUtil.instance.calDistance(firstEdge, roomEdge);
            if(minDistance == null)
            {
                minDistance = dist;
                minDistanceRoomEdge = roomEdge;
                continue;
            }
            if(dist < minDistance)
            {
                minDistance = dist;
                minDistanceRoomEdge = roomEdge;
            }
        }
        else if(TBaseRoomToolUtil.instance.edgeIsInFrontArea(secondEdge, firstEdge, roomEdge) && !secondEdge.islayOn(roomEdge))
        {
            let dist: number = TBaseRoomToolUtil.instance.calDistance(secondEdge, roomEdge);
            if(minDistance == null)
            {
                minDistance = dist;
                minDistanceRoomEdge = roomEdge;
                continue;
            }
            if(dist < minDistance)
            {
                minDistance = dist;
                minDistanceRoomEdge = roomEdge;
            }
        }

    }

    let xDot: number = Math.abs(minDistanceRoomEdge.dv.clone().dot(xDir));
    let yDot: number = Math.abs(minDistanceRoomEdge.dv.clone().dot(yDir));
    let tempHallywayRange: any = TBaseRoomToolUtil.instance.cloneRange(hallywayRange);
    let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(minDistanceRoomEdge);
    if(xDot > 0.9)
    {
        tempHallywayRange.yMin = Math.min(tempHallywayRange.yMin, roomEdgeRange.yMin);
        tempHallywayRange.yMax = Math.max(tempHallywayRange.yMax, roomEdgeRange.yMax);
    }
    if(yDot > 0.9)
    {
        tempHallywayRange.xMin = Math.min(tempHallywayRange.xMin, roomEdgeRange.xMin);
        tempHallywayRange.xMax = Math.max(tempHallywayRange.xMax, roomEdgeRange.xMax);
    }
    // 检查tempHallywayRange 是否与其他区域存在重叠，这些重叠域包括户型的其他房间，过道，以及其他的区域块
    let isVaildHallwayRange: boolean = true;
    if(isOverlayOtherBlocks(tempHallywayRange, hallwayRect.nor.clone(), room, otherHallwayRects, otherRanges))
    {
        isVaildHallwayRange = false;
    }
    if(isVaildHallwayRange)
    {
        return tempHallywayRange;
    }
    return hallywayRange;
}

function calExtendToRoomEdgeRange(hallwayRect: ZRect, room: TRoom, otherHallwayRects: ZRect[], otherRanges: any[]): any
{
    let sideEdges: ZEdge[] = [hallwayRect.leftEdge, hallwayRect.rightEdge];
    let hallywayRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(hallwayRect);
    let tempHallwayRange: any = TBaseRoomToolUtil.instance.cloneRange(hallywayRange);
    for(let i= 0; i < sideEdges.length; ++i)
    {
        let firstEdge: ZEdge = sideEdges[i];
        let secondEdge: ZEdge = sideEdges[(i + 1) % sideEdges.length];
        let isLayonRoomEdge: boolean = false;
        room.room_shape._poly.edges.forEach(roomEdge => {
            if(TBaseRoomToolUtil.instance.isParallelTwoEdges(firstEdge, roomEdge) && firstEdge.islayOn(roomEdge))
            {
                isLayonRoomEdge = true;
            }
        });
        if(isLayonRoomEdge)
        {
            continue;
        }
        let minDistanceRoomEdge: ZEdge = null;
        let minDistance: number = null;
        for(let roomEdge of room.room_shape._poly.edges)
        {
            if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(firstEdge, roomEdge) ||!TBaseRoomToolUtil.instance.edgeIsInFrontArea(firstEdge, secondEdge, roomEdge))
            {
                continue;
            }
            let dist: number = TBaseRoomToolUtil.instance.calDistance(firstEdge, roomEdge);
            if(minDistance == null)
            {
                minDistance = dist;
                minDistanceRoomEdge = roomEdge;
                continue;
            }
            if(minDistance < dist)
            {
                minDistance = dist;
                minDistanceRoomEdge = roomEdge;
            }
        }
        if(!minDistanceRoomEdge)
        {
            continue;
        }
        let xDot: number = Math.abs(minDistanceRoomEdge.dv.clone().dot(xDir));
        let yDot: number = Math.abs(minDistanceRoomEdge.dv.clone().dot(yDir));
        let cloneTempRange: any = TBaseRoomToolUtil.instance.cloneRange(tempHallwayRange);
        let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(minDistanceRoomEdge);
        if(xDot > 0.9)
        {
            cloneTempRange.yMin = Math.min(cloneTempRange.yMin, roomEdgeRange.yMin);
            cloneTempRange.yMax = Math.max(cloneTempRange.yMax, roomEdgeRange.yMax);
        }
        if(yDot > 0.9)
        {
            cloneTempRange.xMin = Math.min(cloneTempRange.xMin, roomEdgeRange.xMin);
            cloneTempRange.xMax = Math.max(cloneTempRange.xMax, roomEdgeRange.xMax);
        }
        let isVaildHallwayRange: boolean = true;
        if(isOverlayOtherBlocks(cloneTempRange, hallwayRect.nor.clone(), room, otherHallwayRects, otherRanges))
        {
            isVaildHallwayRange = false;
        }
        if(isVaildHallwayRange)
        {
            tempHallwayRange.xMin = Math.min(cloneTempRange.xMin, tempHallwayRange.xMin);
            tempHallwayRange.xMax = Math.max(cloneTempRange.xMax, tempHallwayRange.xMax);
            tempHallwayRange.yMin = Math.min(cloneTempRange.yMin, tempHallwayRange.yMin);
            tempHallwayRange.yMax = Math.max(cloneTempRange.yMax, tempHallwayRange.yMax);
        }
    }
    return tempHallwayRange;
}

function isOverlayOtherBlocks(hallwayRange: any, hallwayNor: Vector3,  room: TRoom, otherHallwayRects: ZRect[], otherRanges: any[], isCheckRoom: boolean = true, isCutHallwayRange: boolean = false): boolean
{
    let isOverlayOtherBlocks: boolean = false;
    if(isCheckRoom)
    {
        room.room_shape._poly.edges.some(roomEdge => {
            let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
            let isOverlayRoomEdge: boolean = TBaseRoomToolUtil.instance.isOverlayRange2ds(hallwayRange, roomEdgeRange, false);
            if(isOverlayRoomEdge)
            {
                isOverlayOtherBlocks = true;
            }
            return isOverlayRoomEdge;
        });
    }
    let otherHallwayRectRanges: any[] = otherHallwayRects.map(otherHallwayRect => TBaseRoomToolUtil.instance.getRange2dByPolygon(otherHallwayRect));
    [...otherHallwayRectRanges, ...otherRanges].some(otherRange => {
        let isOverlayOtherRange: boolean = TBaseRoomToolUtil.instance.isOverlayRange2ds(hallwayRange, otherRange, false);
        if(isOverlayOtherRange)
        {
            if(!isCutHallwayRange)
            {
                isOverlayOtherBlocks = true;
                return true;
            }
            // 1. 分别将这两个相交的区域转成rect,调用裁剪接口，再判断原有的区域是否合理
            let tempHallwayRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(hallwayRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(hallwayRange));
            let otherRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(otherRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(otherRange));
            let tempSubPolys: ZPolygon[] = tempHallwayRect.clone().substract(otherRect);
            if(!tempSubPolys || tempSubPolys.length == 0)
            {
                return isOverlayOtherBlocks;
            }
            // TODO 这边的inners实际上是在道理的法向向量上进行切割裁剪的，所以
            let subTempRange: any = TBaseRoomToolUtil.instance.cloneRange(hallwayRange);
            let hallwayXDot: number = Math.abs(hallwayNor.clone().dot(xDir));
            let hallwayYDot: number = Math.abs(hallwayNor.clone().dot(yDir));
            if(hallwayXDot > 0.9)
            {
                // 更改X
                if(otherRange.xMax > hallwayRange.xMin && otherRange.xMin < hallwayRange.xMin)
                {
                    subTempRange.xMin = otherRange.xMax;
                }
                else if(otherRange.xMin < hallwayRange.xMax && otherRange.xMax > hallwayRange.xMax)
                {
                    subTempRange.xMax = otherRange.xMin;
                }  
            }
            else if(hallwayYDot > 0.9)
            {
                // 更改Y
                if(otherRange.yMax > hallwayRange.yMin && otherRange.yMin < hallwayRange.yMin)
                {
                    subTempRange.yMin = otherRange.yMax;
                }
                else if(otherRange.yMin < hallwayRange.yMax && otherRange.yMax > hallwayRange.yMax)
                {
                    subTempRange.yMax = otherRange.yMin;
                }
            }
            let oldHallwayXDist: number = hallwayRange.xMax - hallwayRange.xMin; let oldHallwayYDist: number = hallwayRange.yMax - hallwayRange.yMin;
            let newHallwayXDist: number = subTempRange.xMax - subTempRange.xMin; let newHallwayYDist: number = subTempRange.yMax - subTempRange.yMin;
            let subDistTol: number = 450;
            if(Math.abs(oldHallwayXDist - newHallwayXDist) < subDistTol && Math.abs(oldHallwayYDist - newHallwayYDist) < subDistTol)
            {
                hallwayRange.xMin = subTempRange.xMin;
                hallwayRange.xMax = subTempRange.xMax;
                hallwayRange.yMin = subTempRange.yMin;
                hallwayRange.yMax = subTempRange.yMax;
            }
            else
            {
                isOverlayOtherBlocks = true;
            }
        }
        return isOverlayOtherRange;
    });
    return isOverlayOtherBlocks;
}

function getNewHallwayRange(hallwayRect1: ZRect, hallwayRect2: ZRect, room: TRoom, otherBlocks: any[]): any
{
    let firstEdge1: ZEdge = hallwayRect1.leftEdge;
    let secondEdge1: ZEdge = hallwayRect1.rightEdge;
    let firstEdge2: ZEdge = hallwayRect2.leftEdge;
    let secondEdge2: ZEdge = hallwayRect2.rightEdge;

    if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(firstEdge1, firstEdge2) || TBaseRoomToolUtil.instance.isLayOnPolygons(hallwayRect1, hallwayRect2, null))
    {
        return null;
    }

    // 检查是否正对着，如果正对的话则分辨选取两个最近的边， 以第一个作为基准，构造区域然后做检查判断是否合理
    let newHallwayRange: any = null;
    if(TBaseRoomToolUtil.instance.polygonIsInFrontArea(firstEdge1, secondEdge1, hallwayRect2))
    {
        let dist1: number = TBaseRoomToolUtil.instance.calDistance(firstEdge1, firstEdge2);
        let dist2: number = TBaseRoomToolUtil.instance.calDistance(firstEdge1, secondEdge2);
        let firstRange1: any = TBaseRoomToolUtil.instance.getRange2dByEdge(firstEdge1);
        let otherRange: any = null;
        if(dist1 < dist2)
        {
            otherRange = TBaseRoomToolUtil.instance.getRange2dByEdge(firstEdge2);
        }
        else
        {
            otherRange = TBaseRoomToolUtil.instance.getRange2dByEdge(secondEdge2);
        }
        if(otherRange)
        {
            newHallwayRange = {};
            newHallwayRange.xMin = Math.min(firstRange1.xMin, otherRange.xMin);
            newHallwayRange.xMax = Math.max(firstRange1.xMax, otherRange.xMax);
            newHallwayRange.yMin = Math.min(firstRange1.yMin, otherRange.yMin);
            newHallwayRange.yMax = Math.max(firstRange1.yMax, otherRange.yMax);
        }
    }
    else if(TBaseRoomToolUtil.instance.polygonIsInFrontArea(secondEdge1, firstEdge1, hallwayRect2))
    {
        let dist1: number = TBaseRoomToolUtil.instance.calDistance(secondEdge1, firstEdge2);
        let dist2: number = TBaseRoomToolUtil.instance.calDistance(secondEdge1, secondEdge2);
        let secondRange1: any = TBaseRoomToolUtil.instance.getRange2dByEdge(secondEdge1);
        let otherRange: any = null;
        if(dist1 < dist2)
        {
            otherRange = TBaseRoomToolUtil.instance.getRange2dByEdge(firstEdge2);
        }
        else
        {
            otherRange = TBaseRoomToolUtil.instance.getRange2dByEdge(secondEdge2);
        }
        if(otherRange)
        {
            newHallwayRange = {};
            newHallwayRange.xMin = Math.min(secondRange1.xMin, otherRange.xMin);
            newHallwayRange.xMax = Math.max(secondRange1.xMax, otherRange.xMax);
            newHallwayRange.yMin = Math.min(secondRange1.yMin, otherRange.yMin);
            newHallwayRange.yMax = Math.max(secondRange1.yMax, otherRange.yMax);
        }
    }
    if(newHallwayRange)
    {
        if(isOverlayOtherBlocks(newHallwayRange, hallwayRect1.nor.clone(), room, [], otherBlocks, true, true))
        {
            newHallwayRange = null;
        }
    }
    return newHallwayRange;
}

function getRectByPoly(poly: ZPolygon, sourceRect: ZRect): ZRect
{
    if(poly.edges.length != 4)
    {
        return null;
    }
    let rect: ZRect = ZRect.computeMainRect(poly);
    if(Math.abs(rect.nor.clone().dot(sourceRect.nor)) < 0.1)
    {
        let rectCenter: Vector3 = rect.rect_center;
        let oldW: number = rect.length;
        let oldH: number = rect.depth;
        rect.depth = oldW;
        rect.length = oldH;
        rect.nor = sourceRect.nor.clone();
        rect.rect_center = rectCenter;
        rect.updateRect();
    }
    return rect;
}
