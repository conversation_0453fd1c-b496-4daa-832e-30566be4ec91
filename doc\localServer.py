from flask  import Flask,request,Response
from flask_cors import CORS
import json
import os
import base64
app = Flask(__name__)
CORS(app, supports_credentials=True)

## 本文件用于本地存储一些内容
print(os.getcwd())
def saveImgFile(filename,img0,suffix):
    src_img_file= open(filename+"_"+suffix+".png",'wb')
    img0Data = base64.b64decode(img0)
    src_img_file.write(img0Data)
    src_img_file.close()


@app.route("/")
def hello_world():
    return 'hello world'

@app.route("/save_text_file",methods=['POST','GET'])
def save_text_file():
    data = request.json
    filename = data['filename']
    text = data['text']

    fp = open(file=filename,mode='w',encoding='utf-8')
    fp.write(text)
    fp.close()
    return {"success":True}
@app.route("/save_training_img",methods=['POST','GET'])
def save_training_img():
    data = request.json
    scheme_id = data['scheme_id']
    img0 = data['src_img']
    img1 = data['layout_img']

    dirname = os.path.dirname(__file__)
    filename = os.path.join(dirname,"training_imgs/"+scheme_id)
    saveImgFile(filename=filename,img0=img0,suffix="src")
    saveImgFile(filename=filename,img0=img1,suffix='layout')
    saveImgFile(filename=filename,img0=data['label_img'],suffix="label")

    print(scheme_id)
    return {"success":True}




    return {"success":True}
if __name__ == '__main__':
    app.run("0.0.0.0",8934)