import { test } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import { ifEnterAILayout, matchSets } from '../utils/basicProcess';
import { compareScreenshot } from '../utils/screenshotUtils';

test.describe('页面访问', async () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('返回布局', async ({ page }) => {
    await matchSets(page, designPage);

    // 点击“返回布局”按钮
    const backToLayoutButton = page.locator('button').getByText(' 返回布局 ');
    await backToLayoutButton.waitFor({ state: 'visible', timeout: 5000 });
    await backToLayoutButton.click();

    // 判定是否成功进入布局界面
    await ifEnterAILayout(page);
    await page.waitForTimeout(3000);
    await compareScreenshot(page, 'page-visiting', 'visit.png');
  });
});
