"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[3491],{93491:function(n,e,t){t.d(e,{A:function(){return R}});var r=t(13274),i=t(41594);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function o(){var n=a(["\n      background: #fff;\n      height: 500px;\n      padding: 0 16px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        padding: 0 12px;\n      }\n      .ant-segmented\n      {\n        background-color: #EAEBEA;\n        color: #282828 !important;\n      }\n    "]);return o=function(){return n},n}function l(){var n=a(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        @media screen and (orientation: landscape) {\n          margin: 12px 0px;\n          padding: 0 0px;\n        }\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n            @media screen and (orientation: landscape) {\n              width: 80px;\n              height: 80px;\n              margin-right: 12px;\n            }\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n          @media screen and (orientation: landscape) {\n            padding: 0px 0px;\n          }\n            .size\n            {\n              color: #5B5E60;\n              margin-top: 4px;\n              @media screen and (orientation: landscape) {\n                margin-top: 8px;\n                font-size: 10px;\n              }\n            }\n         } \n      "]);return l=function(){return n},n}function s(){var n=a(["\n      margin: 20px 0 14px 0px;\n      font-size: 14px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        margin: 0px 0 8px 0px;\n      }\n    "]);return s=function(){return n},n}function c(){var n=a(["\n      display: flex;\n      flex-wrap: wrap;\n      overflow-y: scroll;\n      max-height: calc(var(--vh, 1vh) * 100 - 240px);\n      margin-top: 10px;\n      align-items: flex-start;\n       /* 隐藏滚动条 */\n      &::-webkit-scrollbar {\n          display: none; /* 隐藏滚动条 */\n      }\n      \n      /* 对于 Firefox */\n      scrollbar-width: none; /* 隐藏滚动条 */\n      -ms-overflow-style: none; /* IE 和 Edge */\n\n      \n    "]);return c=function(){return n},n}function d(){var n=a(["\n      text-align: center;\n      padding: 20px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin: 0 auto;\n    "]);return d=function(){return n},n}function u(){var n=a(["\n      overflow: hidden;\n      width: 104px;\n      margin: 6px 12px 0 12px;\n      text-align: center;\n      @media screen and (max-width: 800px){\n         width: 122px;\n      }\n      @media screen and (max-width: 450px){\n         width: 106px;\n      }\n      @media screen and (max-width: 400px){\n         width: 94px;\n      }\n      @media screen and (orientation: landscape) {\n        margin: 6px 6px 0 6px;\n        width: 88px;\n        font-size: 10px;\n        text-align: left;\n      }\n      img{\n        width: 100%;\n        aspect-ratio: 1 / 1;\n        border-radius: 4px;\n        background-color: #eaeaea;\n        border-radius: 8px;\n      }\n    "]);return u=function(){return n},n}function m(){var n=a(["\n    \n    "]);return m=function(){return n},n}function p(){var n=a(["\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      margin-top: 4px;\n    "]);return p=function(){return n},n}function f(){var n=a(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      margin: 0 auto;\n      .emptyImg{\n        width: 120px;\n        height: 120px;\n        margin-bottom: 12px;\n      }\n      span{\n        color: #5B5E60;\n      }\n    "]);return f=function(){return n},n}var h=(0,t(79874).rU)((function(n){var e=n.css;return{root:e(o()),topInfo:e(l()),divider:e(s()),goodsInfo:e(c()),loading:e(d()),goodsItem:e(u()),selectIcon:e(m()),sizeInfo:e(p()),noData:e(f())}})),g=t(27347),v=t(15696),x=t(9003),y=t(85783),b=t(36134),w=t(7991),I=t(61214),_=t(31033),j=t(23825),S=t(63038),E=t(44186),k=t(64186),A=t(41544),N=t(87961),z=t(9455),M=t(65640);function P(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function C(n,e,t,r,i,a,o){try{var l=n[a](o),s=l.value}catch(n){return void t(n)}l.done?e(s):Promise.resolve(s).then(r,i)}function O(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var a=n.apply(e,t);function o(n){C(a,r,i,o,l,"next",n)}function l(n){C(a,r,i,o,l,"throw",n)}o(void 0)}))}}function L(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function U(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,a=[],o=!0,l=!1;try{for(t=t.call(n);!(o=(r=t.next()).done)&&(a.push(r.value),!e||a.length!==e);o=!0);}catch(n){l=!0,i=n}finally{try{o||null==t.return||t.return()}finally{if(l)throw i}}return a}}(n,e)||F(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(n){return function(n){if(Array.isArray(n))return P(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||F(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(n,e){if(n){if("string"==typeof n)return P(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?P(n,e):void 0}}function W(n,e){var t,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=e.call(n,a)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var R=(0,v.observer)((function(n){var e=n.selectedFigureElement,t=(0,x.P)(),a=(0,y.B)().t,o=h().styles,l=U((0,i.useState)(null==e?void 0:e._candidate_materials),2),s=l[0],c=l[1],d=U((0,i.useState)("套系素材"),2),u=d[0],m=d[1],p=U((0,i.useState)(!1),2),f=p[0],v=p[1],P=(0,i.useRef)(null),C=U((0,i.useState)(""),2),F=C[0],R=C[1],B=function(){var n=O((function(n){var e,t,r,i,a,o,l,s,d,u,p,f,h,g,x;return W(this,(function(y){switch(y.label){case 0:if(!n)return M.error("无效的图元实例"),[2];if(n._candidate_materials)return c(n._candidate_materials),[2];(null===(t=n._room)||void 0===t||null===(e=t._series_sample_info)||void 0===e?void 0:e.seriesKgId)&&R(n._room._series_sample_info.seriesKgId),y.label=1;case 1:if(y.trys.push([1,3,4,5]),m("套系素材"),c([]),v(!0),u=(null===(i=n._room)||void 0===i||null===(r=i._series_sample_info)||void 0===r?void 0:r.seriesKgId)||F,p=n.modelLoc.includes("门"),!u){if(!p||!F)throw new Error("图元实例的 kgId 缺失");M.warn("门的图元实例中缺少 kgId, 使用缓存的 kgId 替代")}return f={traceId:(0,A.lk)(),roomArea:(null===(a=n._room)||void 0===a?void 0:a.area)||0,seriesKgId:u,appId:1,enterpriseId:null,roomName:(null===(o=n._room)||void 0===o?void 0:o.name)||"",layouts:[{length:n.length,width:n.width,height:n.height,index:0,modelLoc:n.modelLoc,publicCategoryName:n.public_category,cornerWidth:(null===(l=n.rect)||void 0===l?void 0:l.cornerWidth)||0,cornerDepth:(null===(s=n.rect)||void 0===s?void 0:s.cornerDepth)||0}]},[4,(0,k.C6)({method:"post",url:"dp-ai-web/materialMatching",data:f,timeout:6e4})];case 2:return(null==(h=y.sent())?void 0:h.success)&&(null===(d=h.data)||void 0===d?void 0:d.materials)?(g=h.data.materials).length>0?(n._candidate_materials=g.map((function(n){return n.candidate})).flat(),c(D(n._candidate_materials))):(M.warn("未匹配到任何素材"),c([])):(M.error("素材匹配请求失败: ",(null==h?void 0:h.message)||"未知错误"),c([])),[3,5];case 3:return x=y.sent(),M.error("处理素材请求时出错: ",x),c([]),[3,5];case 4:return v(!1),[7];case 5:return[2]}}))}));return function(e){return n.apply(this,arguments)}}();(0,i.useEffect)((function(){B(e)}),[e]);var K=function(){var n=O((function(n){var t,r,i;return W(this,(function(a){switch(a.label){case 0:return"套系素材"!==n?[3,1]:((null==e?void 0:e._candidate_materials)&&(null==e?void 0:e._candidate_materials.length)>0?c(null==e?void 0:e._candidate_materials):c([]),[3,3]);case 1:return v(!0),[4,(0,_.t5)({categoryId:"",current:1,designMaterialId:null==e||null===(t=e._matched_material)||void 0===t?void 0:t.modelId,size:50,tagIds:[]})];case 2:(r=a.sent()).success&&r.data?(i=r.data.materials.records.map((function(n){return{imageUrl:j.L4+n.imagePath+"?x-oss-process=image/resize,m_fixed,w_120,h_120",name:n.materialName,materialId:n.materialId}})),c(i)):c([]),v(!1),a.label=3;case 3:return[2]}}))}));return function(e){return n.apply(this,arguments)}}();return(0,i.useEffect)((function(){K(u)}),[u]),(0,r.jsx)("div",{className:o.root,children:e&&(0,r.jsxs)(r.Fragment,{children:[e&&(0,r.jsx)("div",{className:o.topInfo,children:(0,r.jsxs)("div",{className:"info",children:[(0,r.jsx)("div",{children:(0,r.jsx)("img",{src:e._matched_material.imageUrl||e.image_path,alt:""})}),(0,r.jsxs)("div",{className:"sizeInfo",children:[(0,r.jsx)("div",{children:e._matched_material.name}),(0,r.jsxs)("div",{className:"size",children:[a("图元尺寸"),"：",Math.round(e.rect._w),"*",Math.round(e.rect._h)]}),(0,r.jsxs)("div",{className:"size",children:[a("模型尺寸"),"：",Math.round(e._matched_material.length),"*",Math.round(e._matched_material.width),"*",Math.round(e._matched_material.height)]}),e._matched_material.modelId&&(0,r.jsxs)("div",{className:"size",children:[a("素材ID"),"：",e._matched_material.modelId]})]})]})}),(0,r.jsxs)("div",{className:o.divider,children:[(0,r.jsx)("div",{children:a("可用素材")}),(0,r.jsx)("div",{children:["衣柜","玄关柜","餐边柜"].some((function(n){var t;return null==e||null===(t=e.sub_category)||void 0===t?void 0:t.includes(n)}))&&!t.userStore.aihouse&&"C00002170"!==t.userStore.userInfo.tenantId&&(0,r.jsx)(b.A,{style:{marginLeft:10},type:"primary",size:"small",onClick:function(){P.current.onModal()},children:a("AI搭柜")})})]}),(0,r.jsx)(w.A,{block:!0,value:u,options:z.x.instance.hasPermission(N.J.SERIES.CLOUD_MATERIALS)?[a("套系素材"),a("云素材")]:[a("套系素材")],onChange:function(n){m(n)}}),(0,r.jsx)("div",{className:o.goodsInfo,children:f?(0,r.jsxs)("div",{className:o.loading,children:[(0,r.jsx)(I.A,{size:"large"})," "]}):s&&s.length>0?s.map((function(n,i){return(0,r.jsxs)("div",{className:o.goodsItem,onClick:O((function(){var r,a,o,l,s,c,d,m,p;return W(this,(function(f){switch(f.label){case 0:return e.locked?[2]:(t.designStore.setSelectedIndex(i),"套系素材"!==u?[3,1]:(g.nb.DispatchEvent(g.n0.ReplaceMaterial,n),[3,4]));case 1:return o=null,[4,(0,_.Y2)({materialIds:null==n?void 0:n.materialId})];case 2:return(null==(l=f.sent())||null===(a=l.result)||void 0===a||null===(r=a.result)||void 0===r?void 0:r[0])&&(o=null==l||null===(c=l.result)||void 0===c||null===(s=c.result)||void 0===s?void 0:s[0]),[4,(0,S.h)(n.materialId)];case 3:d=f.sent(),o&&(m={modelId:o.MaterialId,imageUrl:n.imageUrl.startsWith("https://")?n.imageUrl:j.L4+n.imageUrl,name:o.MaterialName,originalLength:o.PICLength,originalWidth:o.PICWidth,originalHeight:o.PICHeight,length:o.PICLength,width:o.PICWidth,height:o.PICHeight,modelLoc:e.modelLoc,modelFlag:o.ModelFlag.toString(),topViewImage:d,figureElement:e},p=function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){L(n,e,t[e])}))}return n}({},n,m),g.nb.DispatchEvent(g.n0.ReplaceMaterial,p)),f.label=4;case 4:return[2]}}))})),children:[i===t.designStore.selectedIndex&&(0,r.jsx)("div",{className:o.selectIcon}),(0,r.jsx)("img",{src:n.imageUrl,alt:""}),(0,r.jsx)("div",{className:o.sizeInfo,children:n.name}),(null==n?void 0:n.length)?(0,r.jsx)("div",{className:o.sizeInfo,style:{color:"#959598"},children:Math.round(null==n?void 0:n.length)+"*"+Math.round(null==n?void 0:n.width)+"*"+Math.round(null==n?void 0:n.height)}):null]},i)})):(0,r.jsxs)("div",{className:o.noData,children:[(0,r.jsx)("img",{className:"emptyImg",src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("span",{children:a("暂无可用素材")})]})}),(0,r.jsx)(E.A,{onParams:function(){},selectedFigureElement:e,ref:P})]})})}))}}]);