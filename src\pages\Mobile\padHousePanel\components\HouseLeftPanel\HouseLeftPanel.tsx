import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { useState, useMemo, useEffect } from "react";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Segmented } from "@svg/antd";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import useStyles from './HouseLeftPanel.style';
import { IRoomEntityRealType, IRoomEntityType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { Image } from '@svg/antd';
import { FigureDataList } from "@/Apps/LayoutAI/Drawing/FigureImagePaths";
import React from "react";


/**
 * 户型编辑器左侧面板组件
 */
const HouseLeftPanels: React.FC<HouseLeftPanelsProps> = ({ isCollapse, onCollapseChange }) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const [currentTab, setCurrentTab] = useState<string>("doors");

    const { doorWindowItems, structureItems } = useOrganizedFigureData();
    const imgPath = 'https://3vj-fe.3vjia.com/layoutai/figures_imgs/';
    useEffect(() => {
        // 通知AppManagerBase组件已经渲染完成
        const manager = LayoutAI_App.instance as TAppManagerBase;
        if (manager) {
            manager.addHouseLeftPanelEvent();
        }
    }, []);

    // 绘制墙体的菜单配置
    const DRAW_WALL_MENU = [
        // 从形状绘制
        {
            title: t('横墙'),
            icon: 'iconstraightwall',
            command: 'horizontalWall',
        },
        {
            title: t('竖墙'),
            icon: 'iconstraightwall',
            command: 'verticalWall',
            isVertical: true,
        },
        {
            title: t('正方形'),
            icon: 'iconsquare',
            command: "square",
        },
        {
            title: t('长方形'),
            icon: 'iconrectangle1',
            command: "rectangle",
        },
        {
            title: t('L形'),
            icon: 'iconLshape',
            command: "LShape",
        },
        {
            title: t('凸形'),
            icon: 'iconconvex',
            command: "convex",
        },
        {
            title: t('凹形'),
            icon: 'iconconcave',
            command: "concave",
        },
        {
            title: t('多边形'),
            icon: 'iconpolygon',
            command: "polygon",
        },
    ];

    const tabItems = [
        {
            value: "draw",
            label: t('绘制'),
        },
        {
            value: "doors",
            label: t('门窗'),
        },
        {
            value: "structure",
            label: t('结构件'),
        }
    ];

    const handleItemClick = (label: string) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
    };

    const drawWallHandle = (command: string) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.DrawShapeWall, command);
    };

    const isLeftPanelOpen = !isCollapse;

    return (
        <>
            <div id="pad_house_left_panel" className={styles.leftPanelRoot + " leftPanelRoot " + (!isLeftPanelOpen ? "panel_hide" : "")} >
                {isLeftPanelOpen && <div className="closeBtn iconfont iconclose1" onClick={() => onCollapseChange(true)}></div>}
                {isLeftPanelOpen && (
                    <>
                        <div className={styles.tabBar}>
                            <div className="tab-content">
                                <Segmented
                                    value={currentTab}
                                    onChange={(val) => setCurrentTab(val.toString())}
                                    block
                                    options={tabItems}
                                />
                            </div>
                        </div>
                        <div className={styles.popupContainer}>
                            <div className={styles.itemGrid}>
                                {currentTab === "draw" ? (
                                    // 绘制功能的渲染
                                    DRAW_WALL_MENU.map((item, index) => (
                                        <div key={index} className={styles.gridItem}>
                                            <div
                                                className={styles.itemIcon + " ant-image-img"}
                                                onPointerDown={() => drawWallHandle(item.command)}
                                            >
                                                <svg className="icon ant-image-img" aria-hidden="true" style={{ 
                                                    width: 50, 
                                                    height: 50,
                                                    transform: item.isVertical ? 'rotate(90deg)' : 'none'
                                                }}>
                                                    <use className="ant-image-img" xlinkHref={`#${item.icon}`}></use>
                                                </svg>
                                            </div>
                                            <div className={styles.itemLabel}>{item.title}</div>
                                        </div>
                                    ))
                                ) : currentTab === "doors" ? (
                                    // 门窗功能的渲染
                                    doorWindowItems.map((item, index) => (
                                        <div key={index} className={styles.gridItem}>
                                            <div
                                                className={styles.itemIcon}
                                                onPointerDown={() => handleItemClick(item.label)}
                                            >
                                                <Image
                                                    src={`${imgPath}${item.image}`}
                                                    preview={false}
                                                    title={item.label}
                                                    alt={item.label}
                                                />
                                            </div>
                                            <div className={styles.itemLabel}>{t(item.label)}</div>
                                        </div>
                                    ))
                                ) : (
                                    // 结构件功能的渲染
                                    structureItems.map((item, index) => (
                                        <div key={index} className={styles.gridItem}>
                                            <div
                                                className={styles.itemIcon}
                                                onPointerDown={() => handleItemClick(item.label)}
                                                onPointerUp={() => {
                                                    LayoutAI_App.DispatchEvent(LayoutAI_Events.mobileAddFurniture, item.label);
                                                }}
                                            >
                                                <Image
                                                    src={`${imgPath}${item.image}`}
                                                    preview={false}
                                                    title={item.label}
                                                    alt={item.label}
                                                />
                                            </div>
                                            <div className={styles.itemLabel}>{t(item.label)}</div>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>
                    </>
                )}
            </div>
            {<div
                className={styles.collapseBtn + (!isLeftPanelOpen ? " panel_hide iconfont iconfill_right" : " iconfont iconfill_left")}
                onClick={() => onCollapseChange(!isCollapse)}
            />}
        </>
    );
};

export default observer(HouseLeftPanels); 

interface HouseLeftPanelsProps {
    isCollapse: boolean;
    onCollapseChange: (collapse: boolean) => void;
}

interface FigureItem {
    icon: string;
    label: string;
    type: IRoomEntityType;
    realType: IRoomEntityRealType;
    image: string;
}

/**
 * 重新组织户型数据，分为门窗和结构件两类
 */
const useOrganizedFigureData = () => {
    return useMemo(() => {
        const houseTypeData = FigureDataList.find(item => item.label === '户型');
        if (!houseTypeData || !houseTypeData.child) return { doorWindowItems: [], structureItems: [] };

        const structureData = houseTypeData.child.find(item => item.label === '结构件');
        if (!structureData || !structureData.figureList) return { doorWindowItems: [], structureItems: [] };

        const doorWindowItems: FigureItem[] = [];
        const structureItems: FigureItem[] = [];

        structureData.figureList.forEach((item: any) => {
            // 如果没有 icon，则使用 image 字段作为备选
            const iconName = item.icon || item.image?.split('.')[0] || '';

            const commonProps: FigureItem = {
                icon: iconName,
                label: item.title,
                type: isDoorOrWindow(item.label) ? 'Door' as IRoomEntityType : 'StructureEntity' as IRoomEntityType,
                realType: item.label as IRoomEntityRealType,
                image: item.image,
            };

            // 根据类型分类
            if (isDoorOrWindow(item.label)) {
                doorWindowItems.push(commonProps);
            } else {
                structureItems.push(commonProps);
            }
        });

        return { doorWindowItems, structureItems };
    }, []);
};

/**
 * 判断是否为门窗类型
 */
const isDoorOrWindow = (label: string): boolean => {
    const doorWindowTypes = ['Door', 'Window', 'door', 'window', 'Railing'];
    return doorWindowTypes.some(type => label.includes(type));
};