import { ZPolygon, ZRect } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { I_Range2D, TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomClassfierCheckRule } from "./TLivingRoomClassfierCheckRule";

export class TDiningTableSpaceAreaLenAndDepthCheckRule extends TLivingRoomClassfierCheckRule
{
    private outSideHallwayDist: number = 450;

    private minLenOrDepth: number = 1800;

    private maxLenOrDepth: number = 4000;

    private overValidScore: number = -30;

    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    protected setParamConfig(): void {
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        void(figure_element);
        void(main_figures);
        // 计算就餐区
        let diningTables: TFigureElement[] = this.categoryFigures.get(this.diningTableName);
        let diningChairs: TFigureElement[] = this.categoryFigures.get(this.diningChairName);
        // 算这两个的包围盒, 然后向外扩展
        let diningTableRange: I_Range2D = TBaseRoomToolUtil.instance.getBoxRangByFigurs([...diningTables,...diningChairs]);
        if(!diningTableRange)
        {
            return {score: 0};
        }
        diningTableRange.xMin -= this.outSideHallwayDist;
        diningTableRange.xMax += this.outSideHallwayDist;
        diningTableRange.yMin -= this.outSideHallwayDist;
        diningTableRange.yMax += this.outSideHallwayDist;
        let diningGroupRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(diningTableRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(diningTableRange));
        let roomPoly: ZPolygon = room.room_shape._poly;
        let subRoomPolys: ZPolygon[] = roomPoly.clone().substract(diningGroupRect);
        let diningGroupPolys: ZPolygon[] = roomPoly.clone().substract_polygons(subRoomPolys);
        if(!diningGroupPolys || diningGroupPolys.length == 0)
        {
            return {score: 0};
        }
        diningGroupPolys.sort((poly1: ZPolygon, poly2: ZPolygon) => {
            return TBaseRoomToolUtil.instance.calPolygonArea(poly2) - TBaseRoomToolUtil.instance.calPolygonArea(poly1);
        });
        let maxInnerRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(diningGroupPolys[0]);
        if(maxInnerRect.min_hh < this.minLenOrDepth || maxInnerRect.max_hh > this.maxLenOrDepth)
        {
            return {score: this.overValidScore};
        }
        return {score: 0};
    }
}