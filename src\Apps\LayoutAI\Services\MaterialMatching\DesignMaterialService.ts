import { openApiRequest } from "@/utils";
import { I_DesignMaterialInfo } from "../../Layout/IMaterialInterface";

export class DesignMaterialService
{
    static async getDesignMaterialInfoByIds(materialIds: string[]): Promise<I_DesignMaterialInfo[]> {
        let results : I_DesignMaterialInfo[] =[];

        let has_found_info : {[key:string]:I_DesignMaterialInfo} = {};
        results.forEach(data=>has_found_info[data.MaterialId] = data);
        materialIds = materialIds.filter((id)=>!has_found_info[id]);
        if(materialIds.length == 0) return results;
        let postReqBody = {
            materialIds: materialIds.join(",")
        }
        try {
            let openRequest = openApiRequest;
  
            if(!openRequest) return results;
            const res = await openRequest({
                method: 'post',
                url: `/api/sdapi/designmaterial/getDesignMaterialByIdsWithOutPlaceheights`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
                withCredentials : true
            });    
            if (!res || res?.success == false) {
                return results;
            }
            const materialInfos = res.result?.result as I_DesignMaterialInfo[] || [];

            return [...results,...materialInfos];
        } catch (error) {
            console.log(error);
            return results;
        }
    }
}