// 灯光参数数据接口
export interface LightParamData {
    lightName: string;
    brightness: number;
    color: string;
}

// 灯光配置组属性接口
export interface LightConfigGroupProps {
    groupName: string;
    groupData: LightParamData[];
}

// 模板接口
export interface Template {
    id: string;
    templateName: string;
    templateImage: string;
    createDate: string;
    templateType: number;
    key: string;
}

// 模板管理属性接口
export interface TemplateManagementProps {
    templates: Template[];
}

// 配置数据接口
export interface ConfigData {
    dayLightParamConfig: {
        data: Record<string, { data: Record<string, LightParamData> }>;
    };
    nightLightParamConfig: {
        data: Record<string, { data: Record<string, LightParamData> }>;
    };
    modelLightParamConfig: {
        data: Record<string, { data: Record<string, LightParamData> }>;
    };
}
    