import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }: any) => {
  return {
    panelContainer: css`
      background-color: rgb(242, 242, 242);
      border-radius: 8px !important;
      position: relative;
      .swj-baseComponent-Containersbox-title {
        background-color: rgb(242, 242, 242) !important;
      }
      .swj-baseComponent-Containersbox-body {
        background-color: rgb(242, 242, 242) !important;
      }
    `,
    title: css`
      font-size: 16px;
    `,
    subtitle: css`
      font-size: 13px;
      font-weight: normal;
      color: rgb(170, 170, 170);
      margin-left: 10px;
    `,
    tab: css`
      display: flex;
      padding: 5px 20px;
      gap: 23px;
    `,
    content: css`
      height: 420px;
      padding: 5px 20px;
      overflow: auto;
      ::-webkit-scrollbar {
        display: none;
      };
    `,
    contentTitle: css`
      font-size: 14px;
      margin-bottom: 5px;
    `,
    contentList: css`
      display: flex;
      flex-wrap: wrap;
      .ant-pro-checkcard-checked:after {
        border: none;
      };
      .ant-pro-checkcard-body {
        padding-block: 0;
      }
    `,
    contentItem: css`
      width: 156px;
      margin-right: 12px;
      :nth-child(4n) {
        margin-right: -5px;
      };
    `,
    contentItemNum: css`
      position: absolute;
      top: 5px;
      right: 5px;
      background: rgb(20, 127, 250, 0.8);
      color: white;
      width: 20px;
      height: 20px;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      font-weight: bold;
    `,
    submitBatchRender: css`
      .ant-btn-variant-outlined:disabled {
        background: white !important;
      }
    `,
    submitBtn: css`
      z-index: 999;
      border-radius: 20px;
      border: none !important;
      color: #eee !important;
      font-size: 16px;
      width: 120px;
      height: 42px;
      background: linear-gradient(90deg,#d07bff,#7a5bff) !important;
      float: right;
      margin-right: 20px;
      margin-top: 7px;
      @media screen and (max-width: 450px) { // 手机宽度
        width: 120px;
        left: 75%;
      };
    `,
}
})