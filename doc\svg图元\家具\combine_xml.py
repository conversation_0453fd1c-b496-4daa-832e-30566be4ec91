import xml
import xml.etree.ElementTree as ET
import os

def combine_xml():
    dirname = os.path.dirname(__file__)
    t_root = ET.fromstring('<root xmlns:ns0="http://www.w3.org/2000/svg"></root>')

    for root_path,dirs,files in os.walk(dirname):
        for file in files:
            # print(file)
            if not file.endswith("svg"):
                continue
            path = os.path.join(root_path,file)
            ele = ET.parse(path)
            root = ele.getroot()
            figure_name = file.replace(".svg","")
            namespace = "{http://www.w3.org/2000/svg}"
            if root.tag == namespace+"svg":
                svg_ele = root
            else:
                svg_ele = ele.find(namespace+"svg")
            svg_ele.set("figure_name",figure_name)
            t_root.append(svg_ele)


    text = ET.tostring(t_root,encoding="unicode",default_namespace="",xml_declaration=False)
    text = text.replace("ns0:","")
    text = "export const DefaultFigureXml = `"+text+"`"


    t_path = path[0:path.find("layout_ai")] + "/layout_ai/src/Apps/LayoutAI/Drawing/"
    if os.path.exists(t_path):
        fp = open(os.path.join(t_path,"DefaultFigureXml.ts"),"w",encoding="utf-8")
        fp.write(text)
        fp.close()
        print("rewrite done!")
    else:
        fp = open(os.path.join(dirname,"figure_xml.js"),"w",encoding="utf-8")
        fp.write(text)
        fp.close()

   
combine_xml()



