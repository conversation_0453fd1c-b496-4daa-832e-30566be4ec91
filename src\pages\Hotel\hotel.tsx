import { AI2DesignBasicModes, AI2DesignManager } from '@/Apps/AI2Design/AI2DesignManager';
import { LayoutSchemeService } from '@/Apps/LayoutAI/Services/Basic/LayoutSchemeService';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { EventName } from '@/Apps/EventSystem';
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { AttributeEdit, ExitBar, HomeProgress, LeftPanel, MyCommandBar, TopMenu } from '@/components';
import LayoutIssueReportList from '@/components/LeftPanel/components/LayoutIssueReportList/layoutIssueReportList';
import MyLayoutSchemeList from '@/components/MyLayoutSchemeList/MyLayoutSchemeList';
import FiguresReplace from '@/components/Replace/replace';
import RoomImagePredict, { queryPredict } from '@/components/RoomImagePredict/roomImagePredict';
import SaveLayoutSchemeDialog from '@/components/SaveLayoutSchemeDialog/SaveLayoutSchemeDialog';
import TopMenuBar from '@/components/TopMenuBar/topMenuBar';
import WelcomePage from '@/components/WelcomePage/welcomePage';
import { _createHxId, _editHxId, is_debugmode_website, mode_type, scheme_Id, workDomainMap } from '@/config';
import { loadFile } from '@/IndexDB';
import { useStore } from '@/models';
import SunvegaAPI from '@api/clouddesign';
import { Button, Modal, message } from '@svg/antd';
import { LeftMenuBar, GuidanceChannel } from '@svg/antd-cloud-design';
import type { ILeftMenuItem } from '@svg/antd-cloud-design/lib/LeftMenuBar';
import { observer } from "mobx-react-lite";
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from "react-router-dom";
import MultiSchemeList from '../Mobile/multiScheme/multiSchemeList/multiSchemeList';
import useStyles from '../Home/style';
import SaveRoomTemplatesPopup from '@/components/SaveRoomTemplatePopup/saveRoomTemplatesPopup';
import HouseSchemeAddForm from '@/components/HouseSchemeAddForm/HouseSchemeAddForm';
import { SdkService } from '@/services/SdkService';
import DesignModeButtons from '../Home/DesignModeButtons/designModeButtons';
import LayoutScoreDialog from '../Home/LayoutScoreDialog/layoutScoreDialog';
import HotelLayoutTopBar from '@/components/LeftPanel/components/HotelLayoutMenu/hotelLayoutTopBar';
import { HotelRoomDivisionDialog } from '@/components/LeftPanel/components/HotelLayoutMenu/hotelRoomDivisionDialog';

/**
 * @description 主页
 */
const App: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [preparing, setPreparing] = useState<{ opening: boolean; title: string }>({ opening: false, title: "" });
  const [openingScheme, setOpeningScheme] = useState<boolean>(false);
  const [showSubHandlerBtn, setShowSubHandlerBtn] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();
  const messageKey = 'SaveSchemeProgress';
  const [issueReportVisible, setIssueReportVisible] = useState<boolean>(false);

  const [isSaveAs, setIsSaveAs] = useState<boolean>(false);

  const [IsLandscape, setIsLandscape] = useState<boolean>(window.innerWidth < window.innerHeight);

  const [layoutSchemeName, setLayoutSchemeName] = useState<string>(null);

  const navigate = useNavigate();

  LayoutAI_App.UseApp(AI2DesignManager.AppName); // 确保当前的app_id
  if (LayoutAI_App.instance) {
    LayoutAI_App.instance._is_landscape = IsLandscape;
    LayoutAI_App.t = t;
  }
  const containerRef = useRef(null);

  let store = useStore();
  const updateLandscape = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_landscape = IsLandscape;
    }
    setIsLandscape(window.innerWidth < window.innerHeight);

  }

  const updateIsWebSiteDebug = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_website_debug = is_debugmode_website;
    }
  }
  updateIsWebSiteDebug();

  const updateCanvasSize = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
      LayoutAI_App.instance.update();
    }
    updateLandscape();
  };


  const toLayoutPage = () => {
    navigate('/layout')
  }

  const onApplyLayout = () => {
    // setOpeningScheme(true);
    if (LayoutAI_App.instance) {
      LayoutAI_App.RunCommand(LayoutAI_Commands.ApplyLayout);
    }
  }

  const onConfirmSubHandler = () => {
     if (store.homeStore.designMode === AI2DesignBasicModes.ExDrawingMode) {
      LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
      return;
    }
    if((LayoutAI_App.instance as TAppManagerBase)?.layout_container?._drawing_layer_mode =='SingleRoom')
    {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
      return;
    }
    LayoutAI_App.RunCommand(LayoutAI_Commands.AcceptLeaveSubHandler);
    

  }





  // const onMakeWalls = ()=>{
  //   if(LayoutAI_App.instance)
  //   {
  //       LayoutAI_App.RunCommand(LayoutAI_Commands.MakeWallXml);
  //       LayoutAI_App.closeApp();
  //   }    
  // }

  const switchIntoDesign = async (data: any) => {
    setOpeningScheme(false);
    setTimeout(() => {
      let url = '/design?importType=importHouse&&from=2dedit';
      if (is_debugmode_website) {
        url += "&debug=true";
      }
      navigate(url);
    }, 100);

  }

  const getSchemeData = async () => {
    if (!scheme_Id) return;
    let params = {
      isDelete: 0,
      pageIndex: 1,
      pageSize: 9,
      keyword: scheme_Id
    }
    const { layoutSchemeDataList, total } = await LayoutSchemeService.getLayoutSchemeList(params);
    if (layoutSchemeDataList) {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, layoutSchemeDataList[0]);
      LayoutAI_App.emit(EventName.OpenHouseSearching, false);
    }
  }


  useEffect(() => {
    setLayoutSchemeName((LayoutAI_App.instance as TAppManagerBase).layout_container._layout_scheme_name);
    store.homeStore.setAppOpenAsCadPlugin(true)
    // LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
    window.addEventListener('resize', updateCanvasSize);
    updateCanvasSize();


    if (LayoutAI_App.instance) {

      if (!LayoutAI_App.instance.initialized) {
        LayoutAI_App.instance.init();

        LayoutAI_App.instance.Configs.prepare_auto_layout = false;
        LayoutAI_App.instance.Configs.app_specific_domain = "Hotel";
        LayoutAI_App.instance.Configs.saving_localstorage_layout_scheme = true;
        LayoutAI_App.RunCommand(AI2DesignBasicModes.HotelLayoutMode);
        LayoutAI_App.instance.prepare().then(() => {
          LayoutAI_App.RunCommand(AI2DesignBasicModes.HotelLayoutMode);

          // 个人工作台跳转过来的相关逻辑
          getSchemeData();
        });

        (LayoutAI_App.instance as TAppManagerBase).layout_graph_solver._is_query_server_model_rooms = false;
        LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
      }
      if (window?.URLSearchParams) {
        const urlParams = new URLSearchParams(window.location.search);
        const debug = urlParams.get("debug");
        if (debug !== null) { // 只有在 debug 存在时才执行赋值
          const debugValue = debug === '1' ? 1 : 0; // 根据 debug 的值设置 debugValue
          if (localStorage) {
            localStorage.setItem("LayoutAI_Debug", String(debugValue));
            LayoutAI_App.instance._debug_mode = debugValue;
          }
        }
      }
      LayoutAI_App.instance.update();
    }
    const object_id = "cad_home";
    // LayoutAI_App.on_M(EventName.ShowFigureMenuList,object_id,(t)=>{
    //   setShowLeftFigureMenu(t);
    // })

    LayoutAI_App.on(EventName.ShowWallTopMenu, (event) => {
      store.homeStore.setIsShowWallTopMenu(event);
    })

    SunvegaAPI.BasicBiz.Room.bindOnOpenSchemeFinish(switchIntoDesign);

    LayoutAI_App.on(EventName.setIssueReportVisible, setIssueReportVisible);

    LayoutAI_App.on(EventName.ShowSubHandlerBtn, setShowSubHandlerBtn);

    LayoutAI_App.on(EventName.SwitchIntoDesign, switchIntoDesign);

    LayoutAI_App.on_M(EventName.RoomList, 'room_list1', (roomList: TRoom[]): void => {
      store.homeStore.setRoomInfos(roomList);

    });

    LayoutAI_App.on_M(EventName.SelectingRoom, object_id, (event: { current_rooms: TRoom[], event_param: any }): void => {
      setTimeout(() => {
        store.homeStore.setSelectData({ rooms: event?.current_rooms, clickOnRoom: true });
      }, 20);
      store.homeStore.setCurrenScheme(event?.event_param);
    });

    LayoutAI_App.on(EventName.PerformFurnishResult, (event: { progress: string, message: string }) => {
      if (event.progress === 'error') {
        message.error(event.message);
      }
      else if (event.progress === 'info') {
        message.info({
          key: messageKey,
          type: 'info',
          content: t(event.message),
          duration: 1,
          style: {
            marginTop: '4vh',
            zIndex: 9999,
          }
        });
      }
    });

    LayoutAI_App.on(EventName.OpenMySchemeList, () => {
      store.homeStore.setShowMySchemeList(true);
    });

    LayoutAI_App.on(EventName.selectRoom, (event: TRoomEntity) => {
      store.homeStore.setSelectedRoom(event);
    });

    LayoutAI_App.on(EventName.LayoutSchemeOpening, (event: any) => {
      setLayoutSchemeName(event.name);
    });

    LayoutAI_App.on(EventName.LayoutSchemeOpened, (event: any) => {
      setLayoutSchemeName(event.name);
    });

    LayoutAI_App.on(EventName.LayoutSchemeOpenFail, (event: any) => {
      setLayoutSchemeName(event.name);
    });

    LayoutAI_App.on(EventName.ShowWelcomePage, (t:boolean)=>{
      store.homeStore.setShowWelcomePage(t);
    })

    LayoutAI_App.on(EventName.SaveProgress, (event: { progress: string, id: string, name: string }) => {
      if (event.progress === "success") {
        messageApi.open({
          key: messageKey,
          type: 'success',
          content: t('布局方案保存成功'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
      } else if (event.progress === "fail") {
        messageApi.open({
          key: messageKey,
          type: 'error',
          content: t('布局方案保存失败'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
      } else if (event.progress === "ongoing") {
        messageApi.open({
          key: messageKey,
          type: 'loading',
          content: t('正在保存布局方案'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
      }
    });

    LayoutAI_App.on(EventName.MessageTip, (event: any) => {
      message.config({
        top: 50, // 设置距离顶部的距离
        duration: 3, // 消息显示的持续时间（秒）
        maxCount: 1, // 最大显示数量
      });
      message.info(event);
    })

    store.userStore.getCheckCurrent();

  }, []); // 空数组-> 渲染完成后第一次启动


  const handleMenuCommand = (command: string) => {
    let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    switch (command) {
      case LayoutAI_Commands.SaveMyLayoutSchemeAs:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content: t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          setIsSaveAs(true);
          store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: 'topMenu'});
        }
        break;
      case LayoutAI_Commands.SaveMyLayoutScheme:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content: t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          if (layout_container._layout_scheme_id == null) {
            setIsSaveAs(false);
            store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: 'topMenu'});
          } else {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);
          }
        }
        break;
      case LayoutAI_Commands.SaveSchemeAs:
        if (layout_container._room_entities.length == 0) {
          messageApi.open({
            key: messageKey,
            type: 'error',
            content: t("当前方案为空，无法保存！"),
            duration: 2,
            style: {
              marginTop: '13vh',
              zIndex: 9999,
            }
          });
        } else {
          store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: 'topMenu'});
        }
        break;
      default:
        break;
    }
  };

  const items: ILeftMenuItem[] = [
    {
      key: '',
      label: ``,
      icon: [''],
      children: <LeftPanel />
    },
  ]

  const onExitDirectly = () => {
    // 慧引流退出
    SdkService.exitSDK();
    // 梦想家退出
    window.parent.postMessage({
      origin: 'layoutai.api',
      type: 'canClose',
      data: {
        canClose: true
      }
    }, '*');
    setTimeout(() => {
      window.location.href = workDomainMap;
    }, 200);
  }

  return (
    <div className={styles.root + " " + (IsLandscape ? styles.landscape : "")}>
      <TopMenu create3DLayout={null} title={<><span style={{ color: '#FFFFFF0F' }}>|</span>{(!layoutSchemeName ? "" : " 【" + layoutSchemeName + "】")}</>} handler={handleMenuCommand} />
      <HotelLayoutTopBar></HotelLayoutTopBar>
      <HotelRoomDivisionDialog></HotelRoomDivisionDialog>
      <div id='Canvascontent' className={styles.content}>
        <div ref={containerRef} id="body_container" className={styles.canvas_pannel}>
          <div className={styles.side_pannel} id={'side_pannel'}>
            {/* {issueReportVisible && <LayoutIssueReportList issueReportVisible={issueReportVisible} setIssueRoomSelected={(data: any) => { }}>
            </LayoutIssueReportList>} */}
            {store.homeStore.designMode !== AI2DesignBasicModes.MeasurScaleMode
              && store.homeStore.designMode !== AI2DesignBasicModes.HouseCorrectionMode
              && <LeftMenuBar
                items={items}
                contentClassName={styles.left_content}
              />
            }
          </div>
          <canvas
            id="cad_canvas"
            className="canvas"
            onMouseEnter={() => {
              store.homeStore.setIsmoveCanvas(false);
            }}
            onMouseLeave={() => {
              store.homeStore.setIsmoveCanvas(true);
            }}
            onTouchStart={(e) => {
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy)
                store.homeStore.setInitialDistance(distance / store.homeStore.scale);
              }
            }}
            onTouchMove={(e) => {
              if (e.touches.length === 2) {
                const dx = e.touches[0].clientX - e.touches[1].clientX;
                const dy = e.touches[0].clientY - e.touches[1].clientY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                let newScale = distance / store.homeStore.initialDistance;
                if (newScale > 5) {
                  newScale = 5;
                } else if (newScale < 0.05) {
                  newScale = 0.05;
                }
                store.homeStore.setScale(newScale);

                LayoutAI_App.DispatchEvent(LayoutAI_Events.scale, newScale)
              }
            }}
            onTouchEnd={() => {
              store.homeStore.setInitialDistance(null);
            }}
          />
          <div className="canvas_btns">
            {
              showSubHandlerBtn ?
                <Button className="btn" type="primary" onClick={onConfirmSubHandler} disabled={preparing.opening || openingScheme}>
                  {t('完 成')}
                </Button> :
                <DesignModeButtons disabled={preparing.opening || openingScheme}></DesignModeButtons>
            }
          </div>
        </div>
        {/* <SpaceCommandBar></SpaceCommandBar> */}
        {store.homeStore.designMode !== AI2DesignBasicModes.MeasurScaleMode && <AttributeEdit></AttributeEdit>}

        {/* {LayoutAI_App.IsDebug && <div className={styles.scene3d}><Scene3DDiv></Scene3DDiv></div>} */}

        <ExitBar ></ExitBar>
        {store.homeStore.designMode == AI2DesignBasicModes.AiCadMode && <MultiSchemeList></MultiSchemeList>}
      </div>
      
      <MyCommandBar></MyCommandBar>
      <HomeProgress></HomeProgress>
      <LayoutScoreDialog></LayoutScoreDialog>
      <RoomImagePredict></RoomImagePredict>
      {
        <Modal
          wrapClassName={'welcome_page'}
          open={store.homeStore.showWelcomePage}
          centered={true}
          // height={720}
          width={'60%'}
          // showHeader={false}
          zIndex={999999}
          closable={true}
          destroyOnClose={true}
          title=''
          footer={null}
          onCancel={() => {
            store.homeStore.setShowWelcomePage(false)
          }}
          mask={true}
        >
          <WelcomePage isFixed={false}></WelcomePage>
        </Modal>
      }

      {/* 后续完善独立的路由后，在做独立站点的欢迎页*/}
      {/* {false && <WelcomePage isFixed={true}></WelcomePage>} */}
      <MyLayoutSchemeList schemeNameCb={(schemeName: string) => { setLayoutSchemeName(schemeName) }} />
      {store.homeStore.isShowWallTopMenu && <TopMenuBar></TopMenuBar>}
      {store.homeStore.showSaveLayoutSchemeDialog.show &&
        <div className={styles.overlay}>
          <SaveLayoutSchemeDialog 
            schemeName={layoutSchemeName} 
            closeCb={() => { store.homeStore.setShowSaveLayoutSchemeDialog({show: false, source: 'topMenu'});
          }} 
            isSaveAs={isSaveAs} 
            exitCb={() => {
              onExitDirectly();
            }}
          />
        </div>
      }
      {store.homeStore.showReplace && <FiguresReplace></FiguresReplace>}
      {store.homeStore.showHouseSchemeAddForm && <HouseSchemeAddForm></HouseSchemeAddForm>}
      {/* {store.homeStore.showAiDraw && <div className={styles.aiDraw}>
        <AiDraw modelType={true}></AiDraw>
      </div>} */}

      <SaveRoomTemplatesPopup></SaveRoomTemplatesPopup>

      {contextHolder}
    </div>
  );
};

export default observer(App);
