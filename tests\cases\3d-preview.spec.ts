import { test } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import { clickToSave, matchSets } from '../utils/basicProcess';
import { takeScreenshot } from '../utils/screenshotUtils';

test.use({ ignoreHTTPSErrors: true });

test.describe('3D预览相关的测试', async () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('打开3D预览', async ({ page }) => {
    await matchSets(page, designPage);

    await clickToSave(page, designPage);
    await designPage.clickTopMenuAppointedItem('导出', '3D预览');

    await page.waitForTimeout(3000);
    await takeScreenshot(page, '3d-preview', 'open-3d-preview.png');
  });
});
