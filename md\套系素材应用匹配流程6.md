# 套系应用流程详解

## 背景介绍

套系应用是我们系统中的核心功能之一，能够让用户快速将一套风格统一的家具、装饰和材质应用到一个或多个房间中。本文档主要介绍从用户选择套系开始，到系统完成套系应用的整个流程，帮助产品经理理解系统的工作原理和优化方向。

## 名词解释

- **套系/风格套系**: 包含一系列匹配的家具、装饰和材质的集合，用于快速统一房间风格
- **应用范围**: 套系应用的范围，包括：
  - **软装(soft)**: 如沙发、床、桌椅等可移动家具
  - **定制柜(cabinet)**: 如橱柜、衣柜等定制家具
  - **硬装(hard)**: 如地板、墙面、天花板等固定装修
  - **补全(remaining)**: 对未布置素材的图元进行自动补充
- **图元**: 房间中的家具、装饰、墙面等可布置物品的基本单位
- **素材**: 可以应用到图元上的具体模型和材质

## 整体流程

套系应用流程可以分为以下几个主要阶段：

1. **用户交互触发**
2. **套系选择初始化**
3. **房间级处理**
4. **素材匹配**
5. **匹配后处理**
6. **场景更新与微调**

下面将详细介绍每个阶段的具体流程：

## 1. 用户交互触发

当用户在界面上选择一个套系后，系统会触发`TSeriesFurnisher.onSeriesSampleSelected`方法，这是套系应用流程在系统内核层的起点。

## 2. 套系选择初始化

系统首先会执行以下初始化操作：

- 取消当前选中图元的选中状态
- 确定目标房间列表（如果未指定，则使用当前选中的房间）
- 检查目标房间是否存在，如不存在则提示用户先选择空间

## 3. 房间级处理

系统会遍历每个目标房间，对每个房间执行以下操作：

1. 检查房间是否已被锁定，如已锁定则跳过处理
2. 为房间记录需要应用套系的范围和套系信息
3. 设置当前应用范围并清除此范围内图元已匹配的素材
4. 更新房间和套系的映射关系
5. 调用`tryToFurnishRooms`方法开始套系应用过程

在`tryToFurnishRooms`方法中：
- 将家具绑定到房间
- 更新房间已应用套系的范围
- 如果房间已应用套系且有图元，则调用`TMaterialMatcher.furnishRoom`进行素材匹配

如果需要补全功能，会调用`tryToFurnishRoomRemainings`方法：
- 更新房间未匹配素材的图元列表
- 对未匹配素材的图元进行套系应用

## 4. 素材匹配流程

`TMaterialMatcher.furnishRoom`方法是素材匹配的核心，主要完成以下工作：

1. **准备阶段**:
   - 更新房间当前应用的套系信息
   - 清理房间中已有的素材（根据应用范围）
   - 收集需要匹配素材的图元列表

2. **硬装处理**:
   - 如果当前应用范围包括硬装，生成或获取硬装图元
   - 将硬装图元添加到匹配列表中

3. **图元筛选**:
   - 过滤出当前应用范围内未锁定的图元
   - 对于常规套系应用，清空图元的所有匹配素材
   - 对于补全应用，仅清空未匹配素材的图元

4. **素材匹配执行**:
   - 按照图元类别进行分组（如地毯类、沙发类等）
   - 使用套系中的素材库进行匹配
   - 对每个图元生成候选素材列表
   - 根据匹配度、风格相似度等因素为每个图元选择最佳素材

5. **初步位置调整**:
   - 为匹配到的素材设置初步的位置和旋转角度
   - 根据图元与素材的尺寸差异进行初步调整

## 5. 匹配后处理流程

在素材匹配完成后，系统会通过`MatchingPostProcesser`类进行一系列后处理操作，确保素材放置的合理性和视觉效果：

1. **位置和尺寸调整**：
   - 修改素材的目标布置位置和旋转角度
   - 根据不同类型的素材（组合、可缩放等）设置合适的目标尺寸
   - 同步图元的匹配矩形与素材尺寸

2. **特殊素材处理**：
   - 调整背景墙相关图元，限制其深度最大为120mm
   - 调整背景墙相关的家具图元，使其与背景墙对齐
   - 对窗帘等特殊素材进行尺寸限制

3. **重复素材处理**：
   - 隐藏同属于一种模型位的重复素材（如地毯、挂画等）
   - 处理组合家具内的子图元素材

4. **高度调整**：
   - 检查素材高度，限制素材高度不超过层高
   - 调整吊顶相关的素材高度

5. **细节优化**：
   - 调整窗帘尺寸以适应窗户
   - 根据天花板位置调整素材的Z轴位置
   - 调整电器和装饰品的位置

## 6. 场景更新与微调

完成上述步骤后，系统会执行：

1. **多样化处理**：
   - 如果开启了多样化匹配选项，系统会确保同一模型位的图元使用不同的素材
   - 这样可以避免房间内出现完全相同的家具，提升美观度

2. **场景更新**：
   - 更新3D场景显示
   - 更新2D视图

3. **自动微调**：
   - 如果开启了自动微调功能，系统会对家具位置进行微调
   - 避免家具之间的碰撞和不合理摆放

## 总结

套系应用流程是一个复杂而精细的过程，它结合了用户交互、素材匹配算法和场景优化技术，为用户提供了快速布置整个房间的能力。这个流程经过精心设计，确保了套系应用的效果美观、合理，同时保留了足够的灵活性，可以满足不同用户的个性化需求。

通过了解这个流程，产品经理可以更好地规划功能迭代和用户体验优化，特别是在素材匹配算法和后处理流程方面，有很大的优化空间可以提升套系应用的效果和用户满意度。 