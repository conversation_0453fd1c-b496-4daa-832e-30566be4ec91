import { RoomWindowLightHandler } from "./RoomWindowLightHandler";
import { LightingRuler, WinDoorLightType, AutoLightingTypeMap } from "../AutoLightingRuler";
import { RealType } from "../../../Scene3D/NodeName";
import { TRoomType } from "../../../Layout/RoomType";
import { IRoomEntityRealType } from "../../../Layout/IRoomInterface";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { TWindowDoorEntity } from "../../../Layout/TLayoutEntities/TWinDoorEntity";
import { TSubSpaceAreaEntity } from "../../../Layout/TLayoutEntities/TSubSpaceAreaEntity";

export class RoomAreaWindowDoorLightHandler extends RoomWindowLightHandler {

    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        let entities: TFillLightEntity[] = [];
        let typeConfig = AutoLightingTypeMap[ruler.typeId];
        let type = typeConfig.type;
        let areaType = typeConfig.areaType;

        let layout_container = this.getLayoutContainer();
        layout_container._room_entities.forEach(roomEntity => {
            if (this.checkCondition(ruler, roomEntity)) {
                let entityList = roomEntity.getWindowEntities().filter((winDoor) => {
                    return roomEntity._sub_room_areas.some(subRoomArea => {
                        if (type === WinDoorLightType.Window && (winDoor as TWindowDoorEntity).isWindow) {
                            return subRoomArea.space_area_type === areaType &&
                                !((winDoor as TWindowDoorEntity).realType === RealType.Railing as IRoomEntityRealType) &&
                                this.isWinDoorInArea(winDoor as TWindowDoorEntity, subRoomArea)
                        }
                        if (type === WinDoorLightType.DoorHole && (winDoor as TWindowDoorEntity).isDoor) {
                            return subRoomArea.space_area_type === areaType &&
                                (winDoor as TWindowDoorEntity).realType === RealType.DoorHole as IRoomEntityRealType &&
                                this.isWinDoorInArea(winDoor as TWindowDoorEntity, subRoomArea)
                        }
                        if (type === WinDoorLightType.BalconyDoor && (winDoor as TWindowDoorEntity).isDoor) {
                            return subRoomArea.space_area_type === areaType &&
                                (winDoor as TWindowDoorEntity).roomtypes.includes(TRoomType.balcony) &&
                                this.isWinDoorInArea(winDoor as TWindowDoorEntity, subRoomArea)
                        }
                        return false;
                    });
                }) as TWindowDoorEntity[];

                entityList.forEach(entity => {
                    let dot = this.getEntityToRoomDot(entity, roomEntity._main_rect.rect_center);
                    // 判断向量方向
                    let offset = ruler.pose.norOffset + entity.thickness;
                    if (dot > 0) {
                        offset = -offset;
                    }
                    const newRuler = { ...ruler, pose: { ...ruler.pose, norOffset: offset } };
                    let lightEntity = this.createWinDoorLight(newRuler, entity);
                    entities.push(lightEntity);
                });
            }
        });
        return entities;
    }

    private isWinDoorInArea(entity: TWindowDoorEntity, area: TSubSpaceAreaEntity): boolean {
        let rect = entity.rect;
        if (rect.intersect(area.rect).length > 0) {
            return true;
        }
        const distanceToArea = area.rect.distanceToPoint(rect.rect_center);
        return distanceToArea <= entity.thickness;
    }
}