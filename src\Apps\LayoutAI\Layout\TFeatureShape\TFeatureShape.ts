import { Vector3 } from "three";
import { compareNames, range_substract } from "@layoutai/z_polygon";
import { I_TContours, TContours } from "@layoutai/z_polygon";
import { I_ZPolygon, ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { AlignWallDist, HallwayWidth, I_Entity3D, I_Room, I_Window, MaxWindowLayonDepth, MidHallwayWidth, MinHallwayWidth, TRoomEdgeProp } from "../IRoomInterface";
import { TCollisionJudge } from "../TLayoutScore/TCollisionJudge";
import { I_TRoom, TRoomShape } from "../TRoomShape";
import { encodeFeatureShape } from "./FeatureEncoder/FeatureEncodersDict";
import { WPolygon } from "./WPolygon";

export interface I_CodedFeatureShapesData {
    code: string; count: number; first_count: number; feature_shapes: I_TFeatureShape[];
}
export interface I_CodedFeatureShapes {
    code: string; count: number; first_count: number; feature_shapes: TFeatureShape[];

}
export interface I_TFeatureShape {
    shape_code: string;
    level_shape_codes: string[];
    _poly: I_ZPolygon;
    _contours: I_TContours;
    _room_uuid?: string;
    _room?: I_Room;
    _order?: number;
}


export class TFeatureShape {
    shape_code: string;
    level_shape_codes: string[];
    _poly: ZPolygon;
    _contours: TContours;
    _room_shapes: TRoomShape[];
    _fit_area_score: number;

    _w_poly: ZPolygon; // 门、窗、墙拆分后的多边形

    _align_candidate_edges: ZEdge[];
    _forbidden_area_edges: ZEdge[];
    _forbidden_area_rects: ZRect[];
    _room: I_TRoom;

    _order: number;


    _src_data: I_TFeatureShape;


    _sub_space_rects: ZRect[]; // 子空间计算
    constructor() {
        this.shape_code = "R";
        this.level_shape_codes = [];
        this._poly = null;
        this._contours = null;
        this._room_shapes = [];
        this._fit_area_score = -1;
        this._room = null;
        this._order = 0;

        this._align_candidate_edges = [];
        this._forbidden_area_edges = [];
        this._forbidden_area_rects = [];
    }


    get level_1_shape_code() {
        return this.level_shape_codes[0];
    }

    get code_text() {
        return this.shape_code + "<br>" + this.level_shape_codes.join(" <br>");
    }
    get valid_area() {
        if (this._room_shapes) {
            let area = 0;
            for (let shape of this._room_shapes) {
                area += shape._area;
            }
            return area;
        }

        return 0.;
    }
    compare_W_Polys(other: TFeatureShape, start_si: number = 0, max_edge_count: number = -1): { same_props: boolean, si: number, order: number, diff_val?: number, max_diff_val?: number } {
        if (this._w_poly.edges.length !== other._w_poly.edges.length) return { same_props: false, si: -1, order: 1 };

        let res: { same_props: boolean, si: number, order: number, diff_val?: number, max_diff_val?: number } = { same_props: false, si: 0, order: 1, diff_val: 9999999 };

        if (max_edge_count <= 0) max_edge_count = this._w_poly.edges.length;

        let compare_win_type = (t0: string, t1: string) => {
            // let th0 = t0 === "Hallway"? "Door":t0;
            // let th1 = t1 === "Hallway"? "Door":t1;

            // return (th0 == th1);
            let th0 = t0 === "Wall" ? "Wall" : "Door";
            let th1 = t1 === "Wall" ? "Wall" : "Door";

            return (th0 == th1);
        }
        for (let ci = 0; ci <= max_edge_count; ci++) {
            let si = (start_si + ci) % this._w_poly.edges.length;
            let check_flag = true;
            let diff_sum = 0;
            let max_diff_val = 0;
            let s_edge_0 = this._w_poly.edges[si];
            let t_edge_0 = other._w_poly.edges[0];

            for (let i = 0; i < this._w_poly.edges.length; i++) {
                let s_i = (si + i) % this._w_poly.edges.length;
                let s_edge = this._w_poly.edges[s_i];
                let t_edge = other._w_poly.edges[i];

                let s_val = Math.abs(s_edge_0.dv.dot(s_edge.dv));

                let t_val = Math.abs(t_edge_0.dv.dot(t_edge.dv));

                // if(Math.abs(s_val - t_val) > 0.5) {
                //     check_flag = false;
                //     break;
                // }



                let s_win = WPolygon.getWindowOnEdge(s_edge)?.type || "Wall";
                let t_win = WPolygon.getWindowOnEdge(t_edge)?.type || "Wall";
                if (!compare_win_type(s_win, t_win)) {
                    check_flag = false;
                    break;
                }
                diff_sum += Math.abs(s_edge.length - t_edge.length);
                max_diff_val = Math.max(max_diff_val, Math.abs(s_edge.length - t_edge.length));
            }
            if (check_flag) {
                let t_res = { same_props: true, si: si, order: 1, diff_val: diff_sum, max_diff_val: max_diff_val };

                if (!res.same_props || t_res.diff_val < res.diff_val) {
                    res = t_res;
                }

            }
        }
        for (let ci = 0; ci <= max_edge_count; ci++) {
            let si = (start_si + ci) % this._w_poly.edges.length;

            let check_flag = true;
            let diff_sum = 0;

            let s_edge_0 = this._w_poly.edges[si];
            let t_edge_0 = other._w_poly.edges[0];
            let max_diff_val = 0;

            for (let i = 0; i < this._w_poly.edges.length; i++) {
                let s_i = (si - i + this._w_poly.edges.length) % this._w_poly.edges.length;

                let s_edge = this._w_poly.edges[s_i];
                let t_edge = other._w_poly.edges[i];
                let s_val = Math.abs(s_edge_0.dv.dot(s_edge.dv));

                let t_val = Math.abs(t_edge_0.dv.dot(t_edge.dv));

                // if(Math.abs(s_val - t_val) > 0.5) {
                //     check_flag = false;
                //     break;
                // }

                let s_win = WPolygon.getWindowOnEdge(s_edge)?.type || "Wall";
                let t_win = WPolygon.getWindowOnEdge(t_edge)?.type || "Wall";
                if (s_win !== t_win) {
                    check_flag = false;
                    break;
                }
                diff_sum += Math.abs(s_edge.length - t_edge.length);
                max_diff_val = Math.max(max_diff_val, Math.abs(s_edge.length - t_edge.length));
            }
            if (check_flag) {
                let t_res = { same_props: true, si: si, order: -1, diff_val: diff_sum, max_diff_val: max_diff_val };

                if (!res.same_props || t_res.diff_val < res.diff_val) {
                    res = t_res;
                }
            }
        }

        return res;
    }
    bindAvaiableEdges() {
        for (let edge of this._poly.edges) {

        }
    }

    exportData() {
        let data: I_TFeatureShape = {
            shape_code: this.shape_code,
            level_shape_codes: this.level_shape_codes,
            _poly: (this._poly && this._contours._polygons.length <= 1) ? this._poly.exportData() : null,
            _contours: (this._contours._polygons.length > 1) ? this._contours.exportContoursData() : null,
            _room_uuid: this._room._uuid,
            _room: this._room.exportRoomData(),
            _order: this._order
        }
        return data;
    }


    importData(data: I_TFeatureShape) {
        this.level_shape_codes = data.level_shape_codes;
        this._contours = new TContours();
        if (data._poly) {
            this._poly = new ZPolygon();
            this._poly.importData(data._poly);
            this._contours.addContour(this._poly);
        }
        else if (data._contours) {
            for (let poly_data of data._contours._polygons) {
                let poly = new ZPolygon();
                poly.importData(poly_data);
                this._contours.addContour(poly);
            }
        }
        this._contours.reInitVE_byPolys();
        this._contours.computeZNor();
        // if(data._sunning_edge_id !== undefined)
        // {
        //     this._sunning_edge =  this._contours.edges[data._sunning_edge_id] || null;

        //     this.bindSunningEdges();
        // }
        this._src_data = data;

    }
    findFeatureShape(room: I_TRoom): number {
        this._room = room;
        this.shape_code = "X";

        if (room.room_shape) {
            this._poly = null;

            let structure_elements: I_Entity3D[] = [];
            this._room.flues && structure_elements.push(...this._room.flues);
            this._room.pillars && structure_elements.push(...this._room.pillars);
            this._room.pipes && structure_elements.push(...this._room.pipes);
            this._room.platforms && structure_elements.push(...this._room.platforms);

            let structure_rects: ZRect[] = [];

            if (compareNames([room.roomname], ["卫生间", "厨房", "卧室"])) {
                for (let ele of structure_elements) {
                    if (ele.rect) {
                        if (ele.rect.min_hh < 200) continue;
                        let t_rect = ele.rect.clone();

                        if (t_rect.orientation_z_nor.z < 0) {
                            t_rect.u_dv = t_rect.dv.negate();
                            t_rect.updateRect();
                        }

                        let target_edge: ZEdge = null;
                        for (let edge of room.room_shape._poly.edges) {
                            if (Math.abs(edge.nor.dot(t_rect.nor)) < 0.9) continue;

                            let pp = edge.projectEdge2d(t_rect.rect_center);

                            if (pp.x < 0 || pp.x > edge.length) continue;

                            if (Math.abs(pp.y) > t_rect.h / 2 + 100) continue;

                            target_edge = edge;
                            break;
                        }

                        if (target_edge) {

                            let pp = target_edge.projectEdge2d(t_rect.rect_center);

                            let ll = pp.x - t_rect.w / 2;
                            let rr = pp.x + t_rect.w / 2;

                            if (ll < 10) {
                                ll = 0;
                            }
                            if (rr > target_edge.length - 10) {
                                rr = target_edge.length;
                            }

                            let cx = (ll + rr) / 2;
                            let ww = rr - ll;
                            if (ww > 0) {
                                t_rect._w = ww;
                                t_rect._h = t_rect._h / 2 - pp.y;
                                t_rect.nor = target_edge.nor.clone().negate();
                                t_rect._back_center = target_edge.unprojectEdge2d({ x: cx, y: 0 });
                                t_rect.updateRect();
                                structure_rects.push(t_rect);
                            }


                        }
                    }
                }
                if (structure_rects.length > 0) {

                    let t_poly = room.room_shape._poly.clone().substract_polygons(structure_rects);

                    if (t_poly && t_poly.length > 0) {
                        this._poly = t_poly[0].clone();
                        this._poly.computeZNor();

                    }
                    TRoomShape.optimizePoly(this._poly);

                    let room_shape = new TRoomShape();
                    room_shape.initByPoints(this._poly.positions);


                    let iter = 5;

                    while (iter--) {
                        let res = room_shape.splitShape();

                        if (!res || res.length == 0) break;

                        if (res.length == 1) {
                            room_shape = res[0];
                        }
                        else if (res.length == 2) {
                            let o_room_shape = res[1];

                            let rect = o_room_shape.getRect() || ZRect.computeMainRect(o_room_shape._poly);

                            if (rect.min_hh < 10) {
                                room_shape = res[0];
                            }
                            else {
                                break;
                            }

                        }
                    }
                    this._poly = room_shape._poly;

                    room_shape._children = [];


                    for (let edge of this._poly.edges) {
                        if (edge.regular_val() > 0.0001 && edge.regular_val() < 0.01) {
                            let n_dv = edge.next_edge.dv;


                        }
                    }
                    this._poly.computeZNor();


                }
            }


            if (!this._poly) {
                this._poly = room.room_shape._poly.clone();
            }

            if (this._poly && this._poly.edges.length == 4) {
                this.shape_code = "R";
            }
        }
        this._initContours();


        return 0;
    }

    getFitAreaScore(): number {

        if (!this._room || this._room_shapes.length == 0) return 0;

        if (this._room.shape_list.length == 0) return 0;
        if (this._fit_area_score > -0.1) {
            return this._fit_area_score;
        }
        this._fit_area_score = this.valid_area / this._room.shape_list[0]._valid_area;
        return this._fit_area_score;
    }
    _initContours() {
        if (this._contours) return this._contours;
        if (this._room_shapes.length > 1) {
            this._contours = new TContours();

            for (let shape of this._room_shapes) {
                this._contours.addContour(shape._poly);
            }
        }
        else {
            this._contours = new TContours();
            this._contours.addContour(this._poly);
        }
        this._contours.reInitVE_byPolys();
        this._contours.computeZNor();


        return this._contours;
    }
    _resortPolyEdges() {
        if (!this._contours) return;
        for (let poly of this._contours._polygons) {
            let t_edges: ZEdge[] = [];

            for (let edge of poly.edges) {
                t_edges.push(edge);
            }

            t_edges.sort((a, b) => b.getProperty(TRoomEdgeProp.WindowWeight) - a.getProperty(TRoomEdgeProp.WindowWeight));

            let target_edge = t_edges[0];

            if (target_edge.getProperty(TRoomEdgeProp.WindowWeight) > 1.3) {
                let next_edge = target_edge.next_edge;
                let id = poly.edges.indexOf(next_edge);

                let p_size = poly.edges.length;

                let res_edges: ZEdge[] = [];
                for (let i = 0; i < p_size; i++) {
                    res_edges.push(poly.edges[(id + i) % p_size]);
                }

                poly.edges.length = 0;
                for (let edge of res_edges) {
                    poly.edges.push(edge);
                }
                poly._updateVerticesByEdgesList();
                poly.computeZNor();
            }
        }


    }

    updateShapeProperties(strictly: boolean = false) {
        let shape = this;
        shape._updateShapeEdgeProp();
        shape.update_W_Polygon();
    }
    _updateShapeEdgeProp() {
        if (!this._room) return;
        if (!this._initContours()) return;

        for (let edge of this._contours.edges) {
            TRoomShape.initWindowOfEdge(edge);
        }
        this._room.addWindowsToPoly(this._contours as any as ZPolygon);

        let contained_shapes: TRoomShape[] = [];

        for (let shape of this._room_shapes) {
            shape.visitContainedShapes(contained_shapes, true);
        }
        for (let edge of this._contours.edges) {
            let layon_edges: ZEdge[] = [];
            for (let r_edge of this._room.room_shape._poly.edges) {
                if (r_edge.islayOn(edge, MaxWindowLayonDepth, 0.01)) {
                    layon_edges.push(r_edge);
                }
            }

            if (layon_edges.length == 0) continue;

            let valid_pairs: number[][] = [];
            for (let l_edge of layon_edges) {
                let ll = l_edge.v0.pos.clone().sub(edge.v0.pos).dot(edge.dv);
                let rr = l_edge.v1.pos.clone().sub(edge.v0.pos).dot(edge.dv);

                if (ll > rr) {
                    let tmp = ll; ll = rr; rr = tmp;
                }
                valid_pairs.push([ll, rr]);
            }


            // TsAI_app.log("layon_edges",edge.length, layon_edges,valid_pairs);

            valid_pairs.sort((a, b) => a[0] - b[0]);

            // console.log(pairs,edge.length);
            let add_window = (l: number, r: number) => {
                // console.log(l,r);

                if (r - l < 100) return;
                let center = edge.v0.pos.clone().add(edge.dv.clone().multiplyScalar((l + r) / 2.));
                let length = r - l;
                let rect = new ZRect(length, 50);
                rect._nor.copy(edge.nor);
                rect.rect_center = center;

                let window: I_Window = {
                    center: center,
                    rect: rect,
                    nor: rect._nor.clone(),
                    type: "Hallway",
                    length: length,
                    width: 50,
                    room_names: [],
                    rooms: []
                }
                TRoomShape.pushWindowToEdge(edge, window);

            }

            let xl = 0;
            let xr = edge.length;
            let unvalid_range_pairs: number[][] = range_substract(edge.length, valid_pairs);
            for (let pair of unvalid_range_pairs) {
                add_window(pair[0], pair[1]);
            }

        }


        for (let edge of this._contours.edges) {
            let windows = TRoomShape.getWindowsOfEdge(edge);

            edge.setProperty(TRoomEdgeProp.WindowWeight, 1);
            if (windows) {
                let w = 0;
                for (let window of windows) {
                    let w1 = this._window_weight(window);
                    // window._is_sunning = w1 > 1.45;
                    if (Math.abs(w1) > Math.abs(w)) w = w1;
                }

                edge.setProperty(TRoomEdgeProp.WindowWeight, w);

            }
        }


    }

    _window_weight(window: I_Window) {
        let weight = 1.;
        if (window.type == "Hallway") {
            return weight;
        }
        window.room_names = window.room_names || [];
        for (let room_name of window.room_names) {
            if (room_name.indexOf("阳台") >= 0) {
                weight = 2;
                if (window.type == "Door") {
                    weight += 1. + window.length / (HallwayWidth * 5.);
                }
            }
            if (room_name.indexOf("厨房") >= 0) {
                weight = -2;
            }
        }
        if (window.room_names.length == 1) {
            if (window.type == "Window") {
                weight += 0.5;
            }

        }
        if (window.length) {
            if (window.length > HallwayWidth) {
                weight += 0.25;
            }
        }

        return weight;
    }

    updateShapeCode() {
        encodeFeatureShape(this);
    }

    update_W_Polygon(ignore_window_names: string[] = null, strictly: boolean = false) {
        if (this._contours._polygons.length == 0) return;

        if (!ignore_window_names) {
            ignore_window_names = [];

            if (this._room.roomname.indexOf("厅") >= 0) {
                ignore_window_names.push("Window"); // 忽略窗户
            }
        }
        // 暂时只取最大的那个多边形

        let poly = this._contours._polygons[0];

        this._w_poly = new ZPolygon();

        poly.computeZNor();
        for (let edge of poly.edges) {
            let windows: I_Window[] = [];
            for (let t_edge of poly.edges) {
                if (Math.abs(t_edge.nor.dot(edge.nor)) <= 0.9) continue;

                let dist = edge.projectEdge2d(t_edge.center).y;
                if (Math.abs(dist) > MinHallwayWidth * 0.5) continue; // 要考虑对边
                let merged_door_length = 300;

                if (this._room.roomname) {
                    if (compareNames([this._room.roomname], ["阳台", "客餐厅"], false) == 1) {
                        merged_door_length = 600; // 阳台可以合并得更长一点
                    }
                    if (compareNames([this._room.roomname], ["客餐厅"], false) == 1) {
                        merged_door_length = 0; // 阳台可以合并得更长一点
                    }
                    if (compareNames([this._room.roomname], ["厨房"], false) == 1) {
                        merged_door_length = 400; // 厨房的侧柜一般是600深, 取400
                    }
                    if (compareNames([this._room.roomname], ["卧室"], false) == 1) {
                        merged_door_length = 300;
                    }
                }



                let e_wins = TRoomShape.mergeWindowsOfEdge(t_edge, merged_door_length, this._room.roomname);
                // console.log(TRoomShape.getWindowsOfEdge(t_edge), e_wins);

                if (!e_wins) continue;




                for (let win of e_wins) {
                    if (this._room.roomname.indexOf("餐厅") >= 0) {
                        if (win.type === "Window") {
                            if (win.length < MidHallwayWidth) continue;
                        }
                    }
                    else {
                        if (ignore_window_names.indexOf(win.type) >= 0) continue;
                    }

                    let pp = edge.projectEdge2d(win.center);

                    if (pp.x < 0 || pp.x > edge.length) continue;
                    windows.push(win);
                }
            }

            if (windows.length == 1) {
                if (windows[0].type == "Window") {

                    let n_win = windows[0];

                    if (compareNames([this._room.roomname], ["厨房", "卫生间"]) == 1 && Math.max(n_win.length, 600) > edge.length / 4) {
                        n_win.center = edge.center.clone();
                        n_win.posX = n_win.center.x;
                        n_win.posY = n_win.center.y;
                        n_win.length = edge.length;
                    }
                }
            }
            WPolygon.splitEdgeByWindows(edge, windows, this._w_poly);

        }



        this._w_poly.is_orderd_array = true;
        this._w_poly._updateEdgeNeighbors();


        let longest_edge: ZEdge = null;

        let room_nor: Vector3 = null;
        let room_dv: Vector3 = null;

        let room_rect = ZRect.computeMainRect(poly);

        let main_rect = this._room?.max_R_shape?.getRect();

        room_rect = main_rect || room_rect;

        if (room_rect) {
            room_nor = room_rect.nor;
            room_dv = room_rect.dv;
        }


        let room_name = this._room.roomname;
        let get_edge_valid_length = (edge: ZEdge) => {
            if (!edge) return 0;

            if (compareNames([room_name], ["卧室", "书房", "厨房", "阳台", "卫生间"]) == 1) {
                if (room_nor) {
                    let t_val = (edge.nor.dot(room_nor));
                    if (t_val < -0.5) {

                        t_val = Math.abs(t_val) + 2;

                    }
                    else {
                        t_val = Math.abs(t_val);
                    }
                    return t_val * edge.length;
                }

            }
            else {
                let nm_length = Math.min(edge?.next_edge?.length || 0, edge?.prev_edge?.length || 0);
                if (room_nor) {
                    let t_val = Math.abs(edge.dv.dot(room_nor));

                    if (t_val > 0.5) {
                        if (edge.nor.dot(room_dv) < 0) {
                            t_val += 0.5;
                        }
                    }

                    return Math.abs(t_val) * edge.length + nm_length * 0.01;
                }
                else {
                    return edge.length + nm_length * 0.01;
                }
            }

        }




        let t_edges: ZEdge[] = [...this._w_poly.edges];
        t_edges.sort((a, b) => get_edge_valid_length(b) - get_edge_valid_length(a));
        longest_edge = t_edges[0];

        // TsAI_app.log("longest_edge",longest_edge.dv,room_dv,room_nor,room_rect.edges[0].dv);



        let s_i = this._w_poly.edges.indexOf(longest_edge);

        let flag = 1;
        if (room_dv) {
            if (longest_edge.dv.dot(room_dv) < 0) flag = -1;
        }
        let temp_edges: ZEdge[] = [];

        for (let i = 0; i < this._w_poly.edges.length; i++) {
            let edge = this._w_poly.edges[(s_i + i * flag + this._w_poly.edges.length) % this._w_poly.edges.length];
            if (flag < 0) {
                let tmp = edge.v0;
                edge.v0 = edge.v1;
                edge.v1 = tmp;
            }

            temp_edges.push(edge);
        }

        this._w_poly.edges.length = 0;
        this._w_poly.edges.push(...temp_edges);
        temp_edges = null;

        this._w_poly._updateEdgeNeighbors();
        this._w_poly._updateVerticesByEdgesList();

        this._w_poly._bindGeometries();
        if (this._room) {
            for (let edge of this._w_poly.edges) {
                for (let s_edge of this._room.room_shape._poly.edges) {
                    if (s_edge.islayOn(edge, AlignWallDist * 2, 0.1)) {
                        edge.bindEdge(TRoomEdgeProp.SrcRoomEdge, s_edge);
                    }
                }
            }
        }

    }






    computeSubspaceRects() {

        console.time("ComputeSubspaceRects");
        this._sub_space_rects = [];


        let x_level_vals: number[] = [];
        let y_level_vals: number[] = [];

        for (let edge of this._forbidden_area_edges) {
            let pos0 = edge.v0.pos.clone();
            let pos1 = edge.v1.pos.clone();
            x_level_vals.push(pos0.x);
            y_level_vals.push(pos0.y);
            x_level_vals.push(pos1.x);
            y_level_vals.push(pos1.y);
        }

        let simple_vals = function (list: number[]) {
            let n_list: number[] = [];
            list.sort((a, b) => a - b);

            for (let val of list) {
                if (n_list.length == 0 || val - n_list[n_list.length - 1] > 1.) {
                    n_list.push(val);
                }
            }
            return n_list;
        }

        x_level_vals = simple_vals(x_level_vals);
        y_level_vals = simple_vals(y_level_vals);


        let end_points: Vector3[] = [];
        for (let x_val of x_level_vals) {
            for (let y_val of y_level_vals) {
                end_points.push(new Vector3(x_val, y_val, 0));
            }
        }

        let count = 0;
        for (let rect of this._forbidden_area_rects) {
            rect.computeBBox();
        }

        let ans_rects: ZRect[] = [];
        for (let p0 of end_points) {
            for (let p1 of end_points) {
                if (p1.x < p0.x + MinHallwayWidth / 2) continue;
                if (p1.y < p0.y + MinHallwayWidth / 2) continue;


                let ww = p1.x - p0.x;
                let hh = p1.y - p0.y;
                let center = p0.clone().add(p1).multiplyScalar(0.5);

                let rect = new ZRect(ww, hh);
                rect._nor.set(0, 1, 0);
                rect.rect_center = center;

                let t_rect = new ZRect(ww - 5, hh - 5);
                t_rect._nor.set(0, 1, 0);
                t_rect.rect_center = center;


                let t_bbox = t_rect.computeBBox();
                let r_bbox = rect.computeBBox();
                let has_intersection = false;

                for (let f_rect of this._forbidden_area_rects) {
                    if (f_rect._boundingbox.containsBox(t_bbox) || r_bbox.containsBox(f_rect._boundingbox)) {
                        has_intersection = true;
                    }
                }

                if (has_intersection) continue;

                let res = TCollisionJudge.checkCollision_rect_edges(rect, this._forbidden_area_edges);

                if (res.length > 0) {
                    has_intersection = true;
                    continue;
                }

                for (let edge of this._forbidden_area_edges) {
                    if (t_bbox.containsPoint(edge.center)) {
                        has_intersection = true;
                    }
                }


                let rect_center = rect.rect_center;

                let min_y = 999999;
                for (let edge of this._contours.edges) {
                    let pp = edge.projectEdge2d(rect_center);

                    if (pp.x < 0 || pp.x > edge.length) continue;

                    if (Math.abs(pp.y) < Math.abs(min_y)) min_y = pp.y;
                }

                if (min_y > 10) {
                    has_intersection = true;
                }

                if (has_intersection) {
                    continue;
                }

                let r_rect = rect.clone();
                r_rect._w += 2;
                r_rect._h += 2;
                r_rect.updateRect();
                let r_rect_bbox = r_rect.computeBBox();
                for (let t_rect of ans_rects) {
                    let t_rect_bbox = t_rect.computeBBox();

                    if (r_rect_bbox.containsBox(t_rect_bbox)) {
                        t_rect.ex_prop["checked"] = "1";
                    }

                    if (t_rect_bbox.containsBox(t_bbox)) {
                        rect.ex_prop["checked"] = "1";
                    }

                }
                ans_rects.push(rect);
                // this._sub_space_rects.push(rect);

                count++;


            }


        }
        for (let rect of ans_rects) {
            if (rect.ex_prop["checked"] == "1") {
                continue;
            }
            this._sub_space_rects.push(rect);
        }

        for (let rect of this._sub_space_rects) {
            let r_bbox = rect.computeBBox();
            for (let id in this._contours._polygons) {
                let poly = this._contours._polygons[id];
                let p_bbox = poly.computeBBox();
                p_bbox.min.sub(new Vector3(10, 10, 0));
                p_bbox.max.add(new Vector3(10, 10, 0));

                if (p_bbox.containsBox(r_bbox)) {
                    rect.ex_prop[TRoomEdgeProp.SrcPolyId] = id;
                }
            }
        }

        this._sub_space_rects.sort((a, b) => Math.min(b._w, b._h) - Math.min(a._w, a._h));

        console.timeEnd("ComputeSubspaceRects");

    }
}

