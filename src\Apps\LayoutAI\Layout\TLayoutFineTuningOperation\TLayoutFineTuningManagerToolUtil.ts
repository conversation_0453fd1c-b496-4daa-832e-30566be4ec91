import { Vector3 } from "three";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { I_LayoutScore, TLayoutJudgeContainter, TrimType } from "../TLayoutScore/TLayoutJudge";
import { TRoom } from "../TRoom";
import { FineTuningType, TLayoutFineTuningBaseTool, TLayoutFineTuningOperationToolUtil } from "./TLayoutFineTuningOperationToolUtil";
import { RectEdgeType, TBaseRoomToolUtil } from "../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { I_Window } from "../IRoomInterface";
import { ZEdge } from "@layoutai/z_polygon";
import { TBaseGroupEntity } from "../TLayoutEntities/TBaseGroupEntity";
import { TLayoutGroupFineTuningManagerToolUtil, TLayoutGroupFineTuningType } from "./TLayoutGroupFineTuningManagerToolUtil";
import { TLayoutAllMatchFineTuningManagerToolUtil } from "./TLayoutAllMatchFineTuningManagerToolUtil";

export class TLayoutFineTuningManagerToolUtil {
    private static _instance: TLayoutFineTuningManagerToolUtil;
    private static _fineTuningMoveStep: number = 50;
    private static _fineTuningScaleStep: number = 5;
    private constructor() 
    {
    }

    public static get instance(): TLayoutFineTuningManagerToolUtil {
        if (!TLayoutFineTuningManagerToolUtil._instance) {
            TLayoutFineTuningManagerToolUtil._instance = new TLayoutFineTuningManagerToolUtil();
        }
        return TLayoutFineTuningManagerToolUtil._instance;
    }

    public fineTuningRoom(room: TRoom)
    {
        let sourcefigures: TFigureElement[] = room._furniture_list;
        sourcefigures = TBaseRoomToolUtil.instance.getAllSingleFigureFromGroup(sourcefigures);
        let figures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.copyFigures(sourcefigures);
        this.preStickWallForFineTuningFigures(room, figures);
        while(true)
        {
            let lastTrimInfo: any = getFineTuningInfoByLayoutScore(room, figures);
            this.fineTuningFigures(room, figures, TLayoutFineTuningManagerToolUtil._fineTuningMoveStep, TLayoutFineTuningManagerToolUtil._fineTuningScaleStep);
            let fineTuningInfo: any = getFineTuningInfoByLayoutScore(room, figures);
            if(Math.abs(lastTrimInfo.fineTuningValue - fineTuningInfo.fineTuningValue) < 0.1)
            {
                break;
            }
        }
        TLayoutFineTuningOperationToolUtil.instance.setRoomFurnitures(room, figures);
    }

    public getAllTrimValueByLayoutScores(layoutScores: I_LayoutScore[]): any
    {
        let allTrimValue: number = 0;
        let fineTuningFigures: TFigureElement[] = [];
        for(let layoutScore of layoutScores)
        {
            if(!layoutScore?.children)
            {
                allTrimValue += layoutScore.fineTuningValue;
                if(layoutScore.score == -100)
                {
                    if(layoutScore.fineTuningFigures)
                    {
                        fineTuningFigures.push(...layoutScore.fineTuningFigures);
                    }
                }
            }
            else
            {
                for(let subLayoutScore of layoutScore.children)
                {
                    allTrimValue += subLayoutScore.fineTuningValue;
                    if(subLayoutScore.score == -100)
                    {
                        if(subLayoutScore.fineTuningFigures)
                        {
                            fineTuningFigures.push(...subLayoutScore.fineTuningFigures);
                        }
                    }
                }
            }
        }
        fineTuningFigures = Array.from(new Set(fineTuningFigures));
        return {fineTuningValue: allTrimValue, fineTuningFigures: fineTuningFigures};
    }

    public postMatchAutoFineTuning(room_list: TRoom[])
    {
        let group_element_list : TFigureElement[] = [];
        // TODO 这个需要包含单品以及组合内的素材图元
        let testGroupFigure: TFigureElement = null;
        let roomElementInfo: Map<TRoom, TFigureElement[]> = new Map<TRoom, TFigureElement[]>();
        for (let room of room_list) {
            if (room._furniture_list && room._furniture_list.length > 0) {
                if(!roomElementInfo.has(room))
                {
                    roomElementInfo.set(room, []);
                }
                let matchedFigures: TFigureElement[] = roomElementInfo.get(room);
                for (let furniture of room._furniture_list) {
                    if (furniture.haveMatchedGroupMaterial() 
                        && (furniture.modelLoc.indexOf("沙发组合") >= 0 || furniture.modelLoc.indexOf("餐桌椅组合") >= 0 ||
                            furniture.modelLoc.indexOf("床具组合") >= 0)) 
                    {
                        group_element_list.push(furniture);
                    }
    
                    // 收集所有匹配图元如果无匹配则直接用原有图元
                    let groupEntity:TBaseGroupEntity = furniture.furnitureEntity as TBaseGroupEntity;
                    if(groupEntity)
                    {
                        if(furniture.haveMatchedGroupMaterial())
                        {
                            matchedFigures.push(furniture);
                            testGroupFigure = furniture;
                        }
                        else
                        {
                            matchedFigures.push(...groupEntity.displayed_figure_elements);  
                        }
                    }
                    else
                    {
                        // 组合是组合那个板块微调，后续组合会当成那一个整体图元参与到单品微调过程中去
                        if(furniture.haveMatchedMaterial2())
                        {
                            matchedFigures.push(furniture._matched_material.figureElement);
                        }
                        else
                        {
                            matchedFigures.push(furniture);
                        }
                    }
                    
                }
            }
        }
    
        group_element_list.forEach((group_element) => {
            if (group_element != null) {
                const group_entity = group_element.furnitureEntity as TBaseGroupEntity;
                if (group_entity == null) return;
    
                if (group_element.modelLoc.indexOf("沙发") >= 0 || group_element.modelLoc.indexOf("床") >= 0 || group_element.modelLoc.indexOf("餐桌") >= 0) {
                    let groupType: TLayoutGroupFineTuningType = TLayoutGroupFineTuningType.k_sofaGroupFigure;
                    if(group_element.modelLoc.indexOf("餐桌") >= 0)
                    {
                        groupType = TLayoutGroupFineTuningType.k_diningTableGroupFigure;
                    }
                    else if(group_element.modelLoc.indexOf("床") >= 0)
                    {
                        groupType = TLayoutGroupFineTuningType.k_bedGroupFigure;
                    }
    
                    TLayoutGroupFineTuningManagerToolUtil.instance.fineTuningGroup(
                        group_entity.displayed_figure_elements, group_element.length, group_element.width, 
                        groupType, group_entity.center, group_entity.nor);
                }
            }
        });
    
        roomElementInfo.forEach((figures, room) => {
            TLayoutAllMatchFineTuningManagerToolUtil.instance.allMatchFigureFineTuning(room, figures);
        });
    }

    private fineTuningFigures(room: TRoom, figures: TFigureElement[], moveStep: number, scaleStep: number)
    {
        // 对不合规的尺寸家具删除
        for(let figure of figures)
        {
            deleteAbnormalSizeFigure(figure, figures);
        }
        // 贴墙处理
        let fineTuningInfo = getFineTuningInfoByLayoutScore(room, figures);
        

        let lastLayoutTrimValue: number = fineTuningInfo.fineTuningValue;
        // 前后左右移动微调
        let moveTol: number = 0.1;
        let scaleTol: number = 0.1;
        lastLayoutTrimValue = this.moveTrimFigures(room, figures, lastLayoutTrimValue, moveStep, scaleStep, moveTol, scaleTol);

        // 前后左右尺寸调整
        let scaleLayoutTrimValue: any = this.scaleTrimFigure(room, figures, lastLayoutTrimValue, scaleStep, scaleTol);
    }

    // 素材移动这种有些图元不能进行移动
    private initTrimFigureMoveInfos(
        room: TRoom, figures: TFigureElement[], fineTuningFigures: TFigureElement[], sourceTrimValue: number,
         sourceMoveStep: number, sourceScaleStep: number, moveTol: number, scaleTol: number): Map<TFigureElement, any>
    {
        let targetTrimFigureMoveInfos: Map<TFigureElement, any> = new Map<TFigureElement, any>();
        for(let figure of fineTuningFigures)
        {
            let currentFigurInfo: any = {
                leftOrRightDir: figure.rect.backEdge.dv.clone(),
                leftOrRightMoveStep: 0,
                leftOrRightMoveLen: 0,
                leftOrRightTrimValue: sourceTrimValue,

                frontOrBackDir: figure.rect.leftEdge.dv.clone(),
                frontOrBackMoveStep: 0,
                frontOrBackMoveLen: 0,
                frontOrBackTrimValue: sourceTrimValue,

                frontScaleDir: figure.rect.frontEdge.v0.pos.clone().sub(figure.rect.backEdge.v1.pos).normalize(),
                frontScaleStep: 0,
                frontScaleLen: 0,
                frontScaleTrimValue: sourceTrimValue,

                backScaleDir: figure.rect.backEdge.v0.pos.clone().sub(figure.rect.frontEdge.v1.pos).normalize(),
                backScaleStep: 0,
                backScaleLen: 0,
                backScaleTrimValue: sourceTrimValue,

                leftScaleDir: figure.rect.leftEdge.v0.pos.clone().sub(figure.rect.rightEdge.v1.pos).normalize(),
                leftScaleStep: 0,
                leftScaleLen: 0,
                leftScaleTrimValue: sourceTrimValue,

                rightScaleDir: figure.rect.rightEdge.v0.pos.clone().sub(figure.rect.leftEdge.v1.pos).normalize(),
                rightScaleStep: 0,
                rightScaleLen: 0,
                rightScaleTrimValue: sourceTrimValue,

                fineTuningType: TrimType.k_none,
            };
            let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [figure]);
            let leftOrRightMoveInfo: any = this.getLeftOrRightMoveInfo(room, figure, otherFigures, currentFigurInfo, sourceMoveStep, sourceTrimValue, moveTol);
            let frontOrBackMoveInfo: any = this.getFrontOrBackMoveInfo(room, figure, otherFigures, currentFigurInfo, sourceMoveStep, sourceTrimValue, moveTol);
            let leftScaleSizeInfo: any = this.getLeftScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);
            let rightScaleSizeInfo: any = this.getRightScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);
            let frontScaleSizeInfo: any = this.getFrontScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);
            let backScaleSizeInfo: any = this.getBackScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);

            let figureMoveInfo: any = parseFigureMoveInfo(
                room, figure, otherFigures, sourceTrimValue, leftOrRightMoveInfo, frontOrBackMoveInfo, leftScaleSizeInfo, rightScaleSizeInfo, frontScaleSizeInfo, backScaleSizeInfo);
            currentFigurInfo.fineTuningType = figureMoveInfo.fineTuningType;
            switch(figureMoveInfo.fineTuningType)
            {
                case TrimType.k_leftOrRightMove:
                {
                    currentFigurInfo.leftOrRightMoveLen = leftOrRightMoveInfo.moveLen;
                    currentFigurInfo.leftOrRightMoveStep = leftOrRightMoveInfo.moveStep;
                    currentFigurInfo.leftOrRightTrimValue = leftOrRightMoveInfo.moveTrimValue;
                    break;
                }
                case TrimType.k_frontOrBackMove:
                {
                    currentFigurInfo.frontOrBackMoveLen = frontOrBackMoveInfo.moveLen;
                    currentFigurInfo.frontOrBackMoveStep = frontOrBackMoveInfo.moveStep;
                    currentFigurInfo.frontOrBackTrimValue = frontOrBackMoveInfo.moveTrimValue;
                    break;
                }
                default:
                    break;
            }
            targetTrimFigureMoveInfos.set(figure, currentFigurInfo);
        }
        return targetTrimFigureMoveInfos;
    }

    private initTrimFigureScaleInfos(
        room: TRoom, figures: TFigureElement[], fineTuningFigures: TFigureElement[], sourceTrimValue: number, sourceScaleStep: number, scaleTol: number): Map<TFigureElement, any>
    {
        let targetTrimFigureScaleInfos: Map<TFigureElement, any> = new Map<TFigureElement, any>();
        for(let figure of fineTuningFigures)
        {
            let currentFigurInfo: any = {
                frontScaleDir: figure.rect.frontEdge.v0.pos.clone().sub(figure.rect.backEdge.v1.pos).normalize(),
                frontScaleStep: 0,
                frontScaleLen: 0,
                frontScaleTrimValue: sourceTrimValue,

                backScaleDir: figure.rect.backEdge.v0.pos.clone().sub(figure.rect.frontEdge.v1.pos).normalize(),
                backScaleStep: 0,
                backScaleLen: 0,
                backScaleTrimValue: sourceTrimValue,

                leftScaleDir: figure.rect.leftEdge.v0.pos.clone().sub(figure.rect.rightEdge.v1.pos).normalize(),
                leftScaleStep: 0,
                leftScaleLen: 0,
                leftScaleTrimValue: sourceTrimValue,

                rightScaleDir: figure.rect.rightEdge.v0.pos.clone().sub(figure.rect.leftEdge.v1.pos).normalize(),
                rightScaleStep: 0,
                rightScaleLen: 0,
                rightScaleTrimValue: sourceTrimValue,

                fineTuningType: TrimType.k_none,
            };
            let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [figure]);
            let frontScaleSizeInfo: any = this.getFrontScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);
            let backScaleSizeInfo: any = this.getBackScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);
            let leftScaleSizeInfo: any = this.getLeftScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);
            let rightScaleSizeInfo: any = this.getRightScaleSizeInfo(room, figure, otherFigures, currentFigurInfo, sourceScaleStep, sourceTrimValue, scaleTol);
            let figureScaleInfo: any = parseFigureScaleInfo(
                room, figure, figures, sourceTrimValue, frontScaleSizeInfo, backScaleSizeInfo, leftScaleSizeInfo, rightScaleSizeInfo, sourceScaleStep * 2);
            currentFigurInfo.fineTuningType = figureScaleInfo.fineTuningType;
            switch(figureScaleInfo.fineTuningType)
            {
                case TrimType.k_frontScaleSize:
                {
                    currentFigurInfo.frontScaleLen = frontScaleSizeInfo.scaleLen;
                    currentFigurInfo.frontScaleStep = frontScaleSizeInfo.scaleStep;
                    currentFigurInfo.frontScaleTrimValue = frontScaleSizeInfo.scaleTrimValue;
                    break;
                }
                case TrimType.k_backScaleSize:
                {
                    currentFigurInfo.backScaleLen = backScaleSizeInfo.scaleLen;
                    currentFigurInfo.backScaleStep = backScaleSizeInfo.scaleStep;
                    currentFigurInfo.backScaleTrimValue = backScaleSizeInfo.scaleTrimValue;
                    break;
                }
                case TrimType.k_leftScaleSize:
                {
                    currentFigurInfo.leftScaleLen = leftScaleSizeInfo.scaleLen;
                    currentFigurInfo.leftScaleStep = leftScaleSizeInfo.scaleStep;
                    currentFigurInfo.leftScaleTrimValue = leftScaleSizeInfo.scaleTrimValue;
                    break;
                }
                case TrimType.k_rightScaleSize:
                {
                    currentFigurInfo.rightScaleLen = rightScaleSizeInfo.scaleLen;
                    currentFigurInfo.rightScaleStep = rightScaleSizeInfo.scaleStep;
                    currentFigurInfo.rightScaleTrimValue = rightScaleSizeInfo.scaleTrimValue;
                    break;
                }
                default:
                    break;
            }
            targetTrimFigureScaleInfos.set(figure, currentFigurInfo);
        }
        return targetTrimFigureScaleInfos;
    }

    // 左右进行移动
    private getLeftOrRightMoveInfo(
        room: TRoom, figure: TFigureElement, otherFigures: TFigureElement[], currentFigurInfo: any,
        sourceMoveStep: number, sourceTrimValue: number, moveTol: number): any
    {
        let leftOrRightDir: Vector3 = currentFigurInfo.leftOrRightDir;
        let leftOrRightMoveLen: number = currentFigurInfo.leftOrRightMoveLen;
        let leftOrRightMoveVec1: Vector3 = leftOrRightDir.clone().multiplyScalar(leftOrRightMoveLen + sourceMoveStep);
        let leftOrRightMoveFigure1: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.moveFigure(leftOrRightMoveFigure1, leftOrRightMoveVec1);
        // 计算得分
        let leftOrRightTrimInfo1: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, leftOrRightMoveFigure1]);
        let leftOrRightDiff1: number = leftOrRightTrimInfo1.fineTuningValue - sourceTrimValue;

        let leftOrRightMoveVec2: Vector3 = leftOrRightDir.clone().multiplyScalar(leftOrRightMoveLen - sourceMoveStep);
        let leftOrRightMoveFigure2: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.moveFigure(leftOrRightMoveFigure2, leftOrRightMoveVec2);
        let leftOrRightTrimInfo2: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, leftOrRightMoveFigure2]);
        let leftOrRightDiff2: number = leftOrRightTrimInfo2.fineTuningValue - sourceTrimValue;

        let targetMoveStep: number = 0;
        let targetMoveLen: number = 0;
        let targetTrimValue: number = sourceTrimValue;
        if(leftOrRightDiff1 >= leftOrRightDiff2 && leftOrRightDiff1 > moveTol)
        {
            targetMoveLen = leftOrRightMoveLen + sourceMoveStep;
            targetMoveStep = sourceMoveStep;
            targetTrimValue = leftOrRightTrimInfo1.fineTuningValue;
        }
        else if(leftOrRightDiff2 >= leftOrRightDiff1 && leftOrRightDiff2 > moveTol)
        {
            targetMoveLen = leftOrRightMoveLen - sourceMoveStep;
            targetTrimValue = leftOrRightTrimInfo2.fineTuningValue;
            targetMoveStep = -sourceMoveStep;
        }
        return {fineTuningType: TrimType.k_leftOrRightMove, moveStep: targetMoveStep, moveLen: targetMoveLen, moveTrimValue: targetTrimValue};
    }

    // 前后进行移动
    private getFrontOrBackMoveInfo(
        room: TRoom, figure: TFigureElement, otherFigures: TFigureElement[], currentFigurInfo: any,
        sourceMoveStep: number, sourceTrimValue: number, moveTol: number): any
    {
        let frontOrBackDir: Vector3 = currentFigurInfo.frontOrBackDir;
        let frontOrBackMoveLen: number = currentFigurInfo.frontOrBackMoveLen;
        let frontOrBackMoveVec1: Vector3 = frontOrBackDir.clone().multiplyScalar(frontOrBackMoveLen + sourceMoveStep);
        let frontOrBackMoveFigure1: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.moveFigure(frontOrBackMoveFigure1, frontOrBackMoveVec1);
        // 计算得分
        let frontOrBackTrimInfo1: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, frontOrBackMoveFigure1]);
        let frontOrBackDiff1: number = frontOrBackTrimInfo1.fineTuningValue - sourceTrimValue;

        let frontOrBackMoveVec2: Vector3 = frontOrBackDir.clone().multiplyScalar(frontOrBackMoveLen - sourceMoveStep);
        let frontOrBackMoveFigure2: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.moveFigure(frontOrBackMoveFigure2, frontOrBackMoveVec2);
        let frontOrBackTrimInfo2: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, frontOrBackMoveFigure2]);
        let frontOrBackDiff2: number = frontOrBackTrimInfo2.fineTuningValue - sourceTrimValue;

        let targetMoveStep: number = 0;
        let targetMoveLen: number = 0;
        let targetTrimValue: number = sourceTrimValue;
        if(frontOrBackDiff1 >= frontOrBackDiff2 && frontOrBackDiff1 > moveTol)
        {
            targetMoveLen = frontOrBackMoveLen + sourceMoveStep;
            targetMoveStep = sourceMoveStep;
            targetTrimValue = frontOrBackTrimInfo1.fineTuningValue;
        }
        else if(frontOrBackDiff2 >= frontOrBackDiff1 && frontOrBackDiff2 > moveTol)
        {
            targetMoveLen = frontOrBackMoveLen - sourceMoveStep;
            targetMoveStep = -sourceMoveStep;
            targetTrimValue = frontOrBackTrimInfo2.fineTuningValue;
        }
        return {fineTuningType: TrimType.k_frontOrBackMove, moveStep: targetMoveStep, moveLen: targetMoveLen, moveTrimValue: targetTrimValue};
    }

    public getFrontScaleSizeInfo(
        room: TRoom, figure: TFigureElement, otherFigures: TFigureElement[], currentFigurInfo: any,
        sourceScaleStep: number, sourceTrimValue: number, scaleTol: number): any
    {
        let frontScaleDir: Vector3 = currentFigurInfo.frontScaleDir;
        let frontScaleLen: number = currentFigurInfo.frontScaleLen;
        let frontScaleVec1: Vector3 = frontScaleDir.clone().multiplyScalar(frontScaleLen + sourceScaleStep);
        let frontScaleFigure1: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(frontScaleFigure1, frontScaleVec1, FineTuningType.k_front);
        // 计算得分
        let frontScaleTrimInfo1: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, frontScaleFigure1]);
        let frontScaleDiff1: number = frontScaleTrimInfo1.fineTuningValue - sourceTrimValue;

        let frontScaleVec2: Vector3 = frontScaleDir.clone().multiplyScalar(frontScaleLen - sourceScaleStep);
        let frontScaleFigure2: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(frontScaleFigure2, frontScaleVec2, FineTuningType.k_front);
        let frontScaleTrimInfo2: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, frontScaleFigure2]);
        let frontScaleDiff2: number = frontScaleTrimInfo2.fineTuningValue - sourceTrimValue;

        let targetScaleStep: number = 0;
        let targetScaleLen: number = 0;
        let targetTrimValue: number = sourceTrimValue;
        if(frontScaleDiff1 >= frontScaleDiff2 && frontScaleDiff1 > scaleTol)
        {
            targetScaleLen = frontScaleLen + sourceScaleStep;
            targetScaleStep = sourceScaleStep;
            targetTrimValue = frontScaleTrimInfo1.fineTuningValue;
        }
        else if(frontScaleDiff2 >= frontScaleDiff1 && frontScaleDiff2 > scaleTol)
        {
            targetScaleLen = frontScaleLen - sourceScaleStep;
            targetScaleStep = -sourceScaleStep;
            targetTrimValue = frontScaleTrimInfo2.fineTuningValue;
        }
        return {fineTuningType: TrimType.k_frontScaleSize, scaleStep: targetScaleStep, scaleLen: targetScaleLen, scaleTrimValue: targetTrimValue};
    }

    public getLeftScaleSizeInfo(
        room: TRoom, figure: TFigureElement, otherFigures: TFigureElement[], currentFigurInfo: any,
        sourceScaleStep: number, sourceTrimValue: number, scaleTol: number): any
    {
        let leftScaleDir: Vector3 = currentFigurInfo.leftScaleDir;
        let leftScaleLen: number = currentFigurInfo.leftScaleLen;
        let leftScaleVec1: Vector3 = leftScaleDir.clone().multiplyScalar(leftScaleLen + sourceScaleStep);
        let leftScaleFigure1: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(leftScaleFigure1, leftScaleVec1, FineTuningType.k_left);
        // 计算得分
        let leftScaleTrimInfo1: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, leftScaleFigure1]);
        let leftScaleDiff1: number = leftScaleTrimInfo1.fineTuningValue - sourceTrimValue;

        let leftScaleVec2: Vector3 = leftScaleDir.clone().multiplyScalar(leftScaleLen - sourceScaleStep);
        let leftScaleFigure2: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(leftScaleFigure2, leftScaleVec2, FineTuningType.k_left);
        let leftScaleTrimInfo2: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, leftScaleFigure2]);
        let leftScaleDiff2: number = leftScaleTrimInfo2.fineTuningValue - sourceTrimValue;

        let targetScaleStep: number = 0;
        let targetScaleLen: number = 0;
        let targetTrimValue: number = sourceTrimValue;
        if(leftScaleDiff1 >= leftScaleDiff2 && leftScaleDiff1 > scaleTol)
        {
            targetScaleLen = leftScaleLen + sourceScaleStep;
            targetScaleStep = sourceScaleStep;
            targetTrimValue = leftScaleTrimInfo1.fineTuningValue;
        }
        else if(leftScaleDiff2 >= leftScaleDiff1 && leftScaleDiff2 > scaleTol)
        {
            targetScaleLen = leftScaleLen - sourceScaleStep;
            targetScaleStep = -sourceScaleStep;
            targetTrimValue = leftScaleTrimInfo2.fineTuningValue;
        }
        return {fineTuningType: TrimType.k_leftScaleSize, scaleStep: targetScaleStep, scaleLen: targetScaleLen, scaleTrimValue: targetTrimValue};
    }

    public getRightScaleSizeInfo(
        room: TRoom, figure: TFigureElement, otherFigures: TFigureElement[], currentFigurInfo: any,
        sourceScaleStep: number, sourceTrimValue: number, scaleTol: number): any
    {
        let rightScaleDir: Vector3 = currentFigurInfo.rightScaleDir;
        let rightScaleLen: number = currentFigurInfo.rightScaleLen;
        let rightScaleVec1: Vector3 = rightScaleDir.clone().multiplyScalar(rightScaleLen + sourceScaleStep);
        let rightScaleFigure1: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(rightScaleFigure1, rightScaleVec1, FineTuningType.k_right);
        // 计算得分
        let rightScaleTrimInfo1: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, rightScaleFigure1]);
        let rightScaleDiff1: number = rightScaleTrimInfo1.fineTuningValue - sourceTrimValue;

        let rightScaleVec2: Vector3 = rightScaleDir.clone().multiplyScalar(rightScaleLen - sourceScaleStep);
        let rightScaleFigure2: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(rightScaleFigure2, rightScaleVec2, FineTuningType.k_right);
        let rightScaleTrimInfo2: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, rightScaleFigure2]);
        let rightScaleDiff2: number = rightScaleTrimInfo2.fineTuningValue - sourceTrimValue;

        let targetScaleStep: number = 0;
        let targetScaleLen: number = 0;
        let targetTrimValue: number = sourceTrimValue;
        if(rightScaleDiff1 >= rightScaleDiff2 && rightScaleDiff1 > scaleTol)
        {
            targetScaleLen = rightScaleLen + sourceScaleStep;
            targetScaleStep = sourceScaleStep;
            targetTrimValue = rightScaleTrimInfo1.fineTuningValue;
        }
        else if(rightScaleDiff2 >= rightScaleDiff1 && rightScaleDiff2 > scaleTol)
        {
            targetScaleLen = rightScaleLen - sourceScaleStep;
            targetScaleStep = -sourceScaleStep;
            targetTrimValue = rightScaleTrimInfo2.fineTuningValue;
        }
        return {fineTuningType: TrimType.k_rightScaleSize, scaleStep: targetScaleStep, scaleLen: targetScaleLen, scaleTrimValue: targetTrimValue};
    }

    public getBackScaleSizeInfo(
        room: TRoom, figure: TFigureElement, otherFigures: TFigureElement[], currentFigurInfo: any,
        sourceScaleStep: number, sourceTrimValue: number, scaleTol: number): any
    {
        let backScaleDir: Vector3 = currentFigurInfo.backScaleDir;
        let backScaleLen: number = currentFigurInfo.backScaleLen;
        let backScaleVec1: Vector3 = backScaleDir.clone().multiplyScalar(backScaleLen + sourceScaleStep);
        let backScaleFigure1: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(backScaleFigure1, backScaleVec1, FineTuningType.k_back);
        // 计算得分
        let backScaleTrimInfo1: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, backScaleFigure1]);
        let backScaleDiff1: number = backScaleTrimInfo1.fineTuningValue - sourceTrimValue;

        let backScaleVec2: Vector3 = backScaleDir.clone().multiplyScalar(backScaleLen - sourceScaleStep);
        let backScaleFigure2: TFigureElement = figure.clone();
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(backScaleFigure2, backScaleVec2, FineTuningType.k_back);
        let backScaleTrimInfo2: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, backScaleFigure2]);
        let backScaleDiff2: number = backScaleTrimInfo2.fineTuningValue - sourceTrimValue;

        let targetScaleStep: number = 0;
        let targetScaleLen: number = 0;
        let targetTrimValue: number = sourceTrimValue;
        if(backScaleDiff1 >= backScaleDiff2 && backScaleDiff1 > scaleTol)
        {
            targetScaleLen = backScaleLen + sourceScaleStep;
            targetScaleStep = sourceScaleStep;
            targetTrimValue = backScaleTrimInfo1.fineTuningValue;
        }
        else if(backScaleDiff2 >= backScaleDiff1 && backScaleDiff2 > scaleTol)
        {
            targetScaleLen = backScaleLen - sourceScaleStep;
            targetScaleStep = -sourceScaleStep;
            targetTrimValue = backScaleTrimInfo2.fineTuningValue;
        }
        return {fineTuningType: TrimType.k_backScaleSize, scaleStep: targetScaleStep, scaleLen: targetScaleLen, scaleTrimValue: targetTrimValue};
    }

    private moveTrimFigures(room: TRoom, figures: TFigureElement[], lastLayoutTrimValue: number, moveStep: number, scaleStep: number, moveTol: number, scaleTol: number): number
    {
        let fineTuningInfo: any = getFineTuningInfoByLayoutScore(room, figures);
        let fineTuningFigures: TFigureElement[] = fineTuningInfo.fineTuningFigures;
        let fineTuningFigureMoveInfos: Map<TFigureElement, any> = this.initTrimFigureMoveInfos(room, figures, fineTuningFigures, lastLayoutTrimValue, moveStep, scaleStep, moveTol, scaleTol);
        while(true)
        {
            while(true)
            {
                let fineTuningFigureCount: number = 0;
                for(let figure of fineTuningFigures)
                {
                    let currentFigureMoveInfo: any = fineTuningFigureMoveInfos.get(figure);
                    let copyFigure: TFigureElement = figure.clone();
                    let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [figure]);
                    let updateTrimValue: number = lastLayoutTrimValue;
                    if(currentFigureMoveInfo.fineTuningType == TrimType.k_none)
                    {
                        ++fineTuningFigureCount;
                        continue;
                    }
                    if(currentFigureMoveInfo.fineTuningType == TrimType.k_leftOrRightMove)
                    {
                        let leftOrRightMoveStep: number = currentFigureMoveInfo.leftOrRightMoveStep;
                        if(Math.abs(leftOrRightMoveStep) < 0.5)
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        let leftOrRightDir: Vector3 = currentFigureMoveInfo.leftOrRightDir;
                        let leftOrRightMoveLen: number = currentFigureMoveInfo.leftOrRightMoveLen;
                        let leftOrRightMoveVec: Vector3 = leftOrRightDir.clone().multiplyScalar(leftOrRightMoveLen + leftOrRightMoveStep);
                        TLayoutFineTuningOperationToolUtil.instance.moveFigure(copyFigure, leftOrRightMoveVec);
                        
                        let tempTrimInfo: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, copyFigure]);
                        let leftOrRightTempDiff: number = tempTrimInfo.fineTuningValue - currentFigureMoveInfo.leftOrRightTrimValue;
                        updateTrimValue = tempTrimInfo.fineTuningValue;
                        currentFigureMoveInfo.leftOrRightMoveLen = leftOrRightMoveLen + leftOrRightMoveStep;
                        if(leftOrRightTempDiff < moveTol)
                        {
                            currentFigureMoveInfo.leftOrRightMoveStep = -leftOrRightMoveStep / 2;
                        }
                    }
                    if(currentFigureMoveInfo.fineTuningType == TrimType.k_frontOrBackMove)
                    {
                        let frontOrBackMoveStep: number = currentFigureMoveInfo.frontOrBackMoveStep;
                        if(Math.abs(frontOrBackMoveStep) < 0.5)
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        let frontOrBackDir: Vector3 = currentFigureMoveInfo.frontOrBackDir;
                        let frontOrBackMoveLen: number = currentFigureMoveInfo.frontOrBackMoveLen;
                        let frontOrBackMoveVec: Vector3 = frontOrBackDir.clone().multiplyScalar(frontOrBackMoveLen + frontOrBackMoveStep);
                        TLayoutFineTuningOperationToolUtil.instance.moveFigure(copyFigure, frontOrBackMoveVec);
                        
                        let tempTrimInfo: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, copyFigure]);
                        let frontOrBackTempDiff: number = tempTrimInfo.fineTuningValue - currentFigureMoveInfo.frontOrBackTrimValue;
                        updateTrimValue = tempTrimInfo.fineTuningValue;
                        
                        currentFigureMoveInfo.frontOrBackMoveLen = frontOrBackMoveLen + frontOrBackMoveStep;
                        if(frontOrBackTempDiff < moveTol)
                        {
                            currentFigureMoveInfo.frontOrBackMoveStep = -frontOrBackMoveStep / 2;
                        }
                    }
                    currentFigureMoveInfo.leftOrRightTrimValue = updateTrimValue;
                    currentFigureMoveInfo.frontOrBackTrimValue = updateTrimValue;
                }
                if(fineTuningFigureCount == fineTuningFigures.length)
                {
                    break;
                }
            }
            // 将这些移动的最终长度应用到每个图元上
            for(let figure of fineTuningFigures)
            {
                let currentFigureMoveInfo: any = fineTuningFigureMoveInfos.get(figure);

                let leftOrRightDir: Vector3 = currentFigureMoveInfo.leftOrRightDir;
                let leftOrRightMoveLen: number = currentFigureMoveInfo.leftOrRightMoveLen;
                let leftOrRightMoveVec: Vector3 = leftOrRightDir.clone().multiplyScalar(leftOrRightMoveLen);
                TLayoutFineTuningOperationToolUtil.instance.moveFigure(figure, leftOrRightMoveVec);

                let frontOrBackDir: Vector3 = currentFigureMoveInfo.frontOrBackDir;
                let frontOrBackMoveLen: number = currentFigureMoveInfo.frontOrBackMoveLen;
                let frontOrBackMoveVec: Vector3 = frontOrBackDir.clone().multiplyScalar(frontOrBackMoveLen);
                TLayoutFineTuningOperationToolUtil.instance.moveFigure(figure, frontOrBackMoveVec);
            }
            let lastTrimFigures: TFigureElement[] = fineTuningFigures;
            let iterationTrimInfo: any = getFineTuningInfoByLayoutScore(room, figures);
            lastLayoutTrimValue = iterationTrimInfo.fineTuningValue;
            let iterationFigures: TFigureElement[] = iterationTrimInfo.fineTuningFigures;
            if(!iterationFigures.length || TLayoutFineTuningBaseTool.instance.isSameForTwoFigures(lastTrimFigures, iterationFigures))
            {
                break;
            }
            fineTuningFigures = iterationFigures;
            fineTuningFigureMoveInfos = this.initTrimFigureMoveInfos(room, figures, fineTuningFigures, lastLayoutTrimValue, moveStep, scaleStep, moveTol, scaleTol);
        }
        return lastLayoutTrimValue;
    }

    private scaleTrimFigure(room: TRoom, figures: TFigureElement[], lastLayoutTrimValue: number, scaleStep: number, scaleTol: number): number
    {
        let fineTuningInfo: any = getFineTuningInfoByLayoutScore(room, figures);
        let fineTuningFigures: TFigureElement[] = fineTuningInfo.fineTuningFigures;
        let fineTuningFigureScaleInfos: Map<TFigureElement, any> = this.initTrimFigureScaleInfos(room, figures, fineTuningFigures, lastLayoutTrimValue, scaleStep, scaleTol);
        while(true)
        {
            while(true)
            {
                let fineTuningFigureCount: number = 0;
                for(let figure of fineTuningFigures)
                {
                    let currentFigureScaleInfo: any = fineTuningFigureScaleInfos.get(figure);
                    // 抽象一个接口出来
                    let isDeleteFigure: boolean = deleteAbnormalSizeFigure(figure, figures, currentFigureScaleInfo);
                    if(isDeleteFigure)
                    {
                        return null;
                    }
                    let copyFigure: TFigureElement = figure.clone();
                    let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [figure]);
                    let updateTrimValue: number = lastLayoutTrimValue;
                    if(currentFigureScaleInfo.fineTuningType == TrimType.k_none)
                    {
                        ++fineTuningFigureCount;
                        continue;
                    }
                    if(currentFigureScaleInfo.fineTuningType == TrimType.k_frontScaleSize)
                    {
                        let frontScaleStep: number = currentFigureScaleInfo.frontScaleStep;
                        if(Math.abs(frontScaleStep) < 0.5)
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        let frontScaleDir: Vector3 = currentFigureScaleInfo.frontScaleDir;
                        let frontScaleLen: number = currentFigureScaleInfo.frontScaleLen;
                        let frontScaleVec: Vector3 = frontScaleDir.clone().multiplyScalar(frontScaleLen + frontScaleStep);
                        if(isStopFineTuningFigureScaleSize(copyFigure, frontScaleVec, FineTuningType.k_front))
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(copyFigure, frontScaleVec, FineTuningType.k_front);
                        
                        let tempTrimInfo: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, copyFigure]);
                        let frontScaleTempDiff: number = tempTrimInfo.fineTuningValue - currentFigureScaleInfo.frontScaleTrimValue;
                        updateTrimValue = tempTrimInfo.fineTuningValue;
                        
                        currentFigureScaleInfo.frontScaleLen = frontScaleLen + frontScaleStep;
                        if(frontScaleTempDiff < scaleTol)
                        {
                            currentFigureScaleInfo.frontScaleStep = -frontScaleStep / 2;
                        }
                    }
                    if(currentFigureScaleInfo.fineTuningType == TrimType.k_backScaleSize)
                    {
                        let backScaleStep: number = currentFigureScaleInfo.backScaleStep;
                        if(Math.abs(backScaleStep) < 0.5)
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        let backScaleDir: Vector3 = currentFigureScaleInfo.backScaleDir;
                        let backScaleLen: number = currentFigureScaleInfo.backScaleLen;
                        let backScaleVec: Vector3 = backScaleDir.clone().multiplyScalar(backScaleLen + backScaleStep);
                        if(isStopFineTuningFigureScaleSize(copyFigure, backScaleVec, FineTuningType.k_back))
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(copyFigure, backScaleVec, FineTuningType.k_back);
                        
                        let tempTrimInfo: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, copyFigure]);
                        let backScaleTempDiff: number = tempTrimInfo.fineTuningValue - currentFigureScaleInfo.backScaleTrimValue;
                        updateTrimValue = tempTrimInfo.fineTuningValue;
                        
                        currentFigureScaleInfo.backScaleLen = backScaleLen + backScaleStep;
                        if(backScaleTempDiff < scaleTol)
                        {
                            currentFigureScaleInfo.backScaleStep = -backScaleStep / 2;
                        }
                    }
                    if(currentFigureScaleInfo.fineTuningType == TrimType.k_leftScaleSize)
                    {
                        let leftScaleStep: number = currentFigureScaleInfo.leftScaleStep;
                        if(Math.abs(leftScaleStep) < 0.5)
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        let leftScaleDir: Vector3 = currentFigureScaleInfo.leftScaleDir;
                        let leftScaleLen: number = currentFigureScaleInfo.leftScaleLen;
                        let leftScaleVec: Vector3 = leftScaleDir.clone().multiplyScalar(leftScaleLen + leftScaleStep);
                        if(isStopFineTuningFigureScaleSize(copyFigure, leftScaleVec, FineTuningType.k_left))
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(copyFigure, leftScaleVec, FineTuningType.k_left);
                        
                        let tempTrimInfo: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, copyFigure]);
                        let leftScaleTempDiff: number = tempTrimInfo.fineTuningValue - currentFigureScaleInfo.leftScaleTrimValue;
                        updateTrimValue = tempTrimInfo.fineTuningValue;
                        
                        currentFigureScaleInfo.leftScaleLen = leftScaleLen + leftScaleStep;
                        if(leftScaleTempDiff < scaleTol)
                        {
                            currentFigureScaleInfo.leftScaleStep = -leftScaleStep / 2;
                        }
                    }
                    if(currentFigureScaleInfo.fineTuningType == TrimType.k_rightScaleSize)
                    {
                        let rightScaleStep: number = currentFigureScaleInfo.rightScaleStep;
                        if(Math.abs(rightScaleStep) < 0.5)
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        let rightScaleDir: Vector3 = currentFigureScaleInfo.rightScaleDir;
                        let rightScaleLen: number = currentFigureScaleInfo.rightScaleLen;
                        let rightScaleVec: Vector3 = rightScaleDir.clone().multiplyScalar(rightScaleLen + rightScaleStep);
                        if(isStopFineTuningFigureScaleSize(copyFigure, rightScaleVec, FineTuningType.k_right))
                        {
                            ++fineTuningFigureCount;
                            continue;
                        }
                        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(copyFigure, rightScaleVec, FineTuningType.k_right);
                        
                        let tempTrimInfo: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, copyFigure]);
                        let rightScaleTempDiff: number = tempTrimInfo.fineTuningValue - currentFigureScaleInfo.rightScaleTrimValue;
                        updateTrimValue = tempTrimInfo.fineTuningValue;
                        
                        currentFigureScaleInfo.rightScaleLen = rightScaleLen + rightScaleStep;
                        if(rightScaleTempDiff < scaleTol)
                        {
                            currentFigureScaleInfo.rightScaleStep = -rightScaleStep / 2;
                        }
                    }
                    currentFigureScaleInfo.frontScaleTrimValue = updateTrimValue;
                    currentFigureScaleInfo.leftScaleTrimValue = updateTrimValue;
                    currentFigureScaleInfo.rightScaleTrimValue = updateTrimValue;
                }
                if(fineTuningFigureCount == fineTuningFigures.length)
                {
                    break;
                }
            }
            // TODO 标准尺引入，对于某些指定的家具图元只能缩放成指定的尺寸（这里的个人想法是缩放后的图元大小接近此图元尺寸改为指定的尺寸大小）
            for(let figure of fineTuningFigures)
            {
                let currentFigureScaleInfo: any = fineTuningFigureScaleInfos.get(figure);
                let frontScaleDir: Vector3 = currentFigureScaleInfo.frontScaleDir;
                let frontScaleLen: number = currentFigureScaleInfo.frontScaleLen;
                let frontScaleVec: Vector3 = frontScaleDir.clone().multiplyScalar(frontScaleLen);
                TLayoutFineTuningOperationToolUtil.instance.scaleFigure(figure, frontScaleVec, FineTuningType.k_front);

                let backScaleDir: Vector3 = currentFigureScaleInfo.backScaleDir;
                let backScaleLen: number = currentFigureScaleInfo.backScaleLen;
                let backScaleVec: Vector3 = backScaleDir.clone().multiplyScalar(backScaleLen);
                TLayoutFineTuningOperationToolUtil.instance.scaleFigure(figure, backScaleVec, FineTuningType.k_back);

                let leftScaleDir: Vector3 = currentFigureScaleInfo.leftScaleDir;
                let leftScaleLen: number = currentFigureScaleInfo.leftScaleLen;
                let leftScaleVec: Vector3 = leftScaleDir.clone().multiplyScalar(leftScaleLen);
                TLayoutFineTuningOperationToolUtil.instance.scaleFigure(figure, leftScaleVec, FineTuningType.k_left);

                let rightScaleDir: Vector3 = currentFigureScaleInfo.rightScaleDir;
                let rightScaleLen: number = currentFigureScaleInfo.rightScaleLen;
                let rightScaleVec: Vector3 = rightScaleDir.clone().multiplyScalar(rightScaleLen);
                TLayoutFineTuningOperationToolUtil.instance.scaleFigure(figure, rightScaleVec, FineTuningType.k_right);
            }
            let lastTrimFigures: TFigureElement[] = fineTuningFigures;
            let iterationTrimInfo: any = getFineTuningInfoByLayoutScore(room, figures);
            lastLayoutTrimValue = iterationTrimInfo.fineTuningValue;
            let iterationFigures: TFigureElement[] = iterationTrimInfo.fineTuningFigures;
            if(!iterationFigures.length || TLayoutFineTuningBaseTool.instance.isSameForTwoFigures(lastTrimFigures, iterationFigures))
            {
                break;
            }
            fineTuningFigures = iterationFigures;
            fineTuningFigureScaleInfos = this.initTrimFigureScaleInfos(room, figures, fineTuningFigures, lastLayoutTrimValue, scaleStep, scaleTol);
        }
        return lastLayoutTrimValue;
    }

    private preStickWallForFineTuningFigures(room: TRoom, figures: TFigureElement[])
    {
        for(let fineTuningFigure of figures)
        {
            if(["一字形淋浴房", "浴缸", "主灯", 
                "餐椅", "餐桌", "直排沙发", "矩形茶几", "脚踏", "休闲椅"].includes(fineTuningFigure.sub_category))
            {
                continue;
            }                
            let moveDir: Vector3 = fineTuningFigure.rect.backEdge.nor.clone();
            let isNeedRotate180: boolean = false;
            let minDist: number = Number.POSITIVE_INFINITY;
            let moveDist: number = 0;
            let figureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(fineTuningFigure.rect);
            let stickRoomEdge: ZEdge = null;
            for(let roomEdge of room.room_shape._poly.edges)
            {
                if(Math.abs(fineTuningFigure.rect.backEdge.dv.clone().dot(roomEdge.dv)) < 0.9)
                {
                    continue;
                }
                let roomEdgeRange: any = getEdgeRange(roomEdge);
                if(!((roomEdgeRange.xMin <= figureRange.xMax && roomEdgeRange.xMax >= figureRange.xMin) || (roomEdgeRange.yMin <= figureRange.yMax && roomEdgeRange.yMax >= figureRange.yMin)))
                {
                    continue;
                }
                let subBackVec: Vector3 = roomEdge.v0.pos.clone().sub(fineTuningFigure.rect.backEdge.v0.pos);
                let backDist: number = subBackVec.clone().dot(moveDir);
                let subFrontVec: Vector3 = roomEdge.v0.pos.clone().sub(fineTuningFigure.rect.frontEdge.v0.pos);
                let frontDist: number = subFrontVec.clone().dot(moveDir);
                if(Math.abs(backDist) <= Math.abs(frontDist))
                {
                    if(Math.abs(backDist) < minDist)
                    {
                        isNeedRotate180 = false;
                        minDist = Math.abs(backDist);
                        moveDist = backDist;
                    }
                }
                else
                {
                    if(Math.abs(frontDist) < minDist)
                    {
                        isNeedRotate180 = true;
                        minDist = Math.abs(frontDist);
                        moveDist = frontDist;
                        stickRoomEdge = roomEdge;
                    }
                }
            }
            if(isNeedRotate180)
            {
                TLayoutFineTuningOperationToolUtil.instance.rotateFigure(fineTuningFigure, Math.PI);
                moveDir = fineTuningFigure.rect.backEdge.nor.clone();
                let subBackVec: Vector3 = stickRoomEdge.v0.pos.clone().sub(fineTuningFigure.rect.backEdge.v0.pos);
                moveDist = subBackVec.clone().dot(moveDir);
            }
            TLayoutFineTuningOperationToolUtil.instance.moveFigure(fineTuningFigure, moveDir.clone().multiplyScalar(moveDist));

            // 钻石形淋浴房以及弧形淋浴房需要两面靠墙
            if(["钻石形淋浴房", "弧形淋浴房"].includes(fineTuningFigure.sub_category))
            {
                moveDir = fineTuningFigure.rect.rightEdge.nor.clone();
                minDist = Number.POSITIVE_INFINITY;
                moveDist = 0;
                for(let roomEdge of room.room_shape._poly.edges)
                {
                    if(Math.abs(fineTuningFigure.rect.rightEdge.dv.clone().dot(roomEdge.dv)) < 0.9)
                    {
                        continue;
                    }
                    let subVec: Vector3 = roomEdge.v0.pos.clone().sub(fineTuningFigure.rect.rightEdge.v0.pos);
                    let dist: number = subVec.clone().dot(moveDir);
                    if(Math.abs(dist) < minDist)
                    {
                        minDist = Math.abs(dist);
                        moveDist = dist;
                    }
                }
                TLayoutFineTuningOperationToolUtil.instance.moveFigure(fineTuningFigure, moveDir.clone().multiplyScalar(moveDist));
            }
        }
    }
}

function parseFigureMoveInfo(
    room: TRoom, figure: TFigureElement, otherFigures: TFigureElement[], 
    sourceTrimValue: number, leftOrRightMoveInfo: any, frontOrBackMoveInfo: any, 
    leftScaleSizeInfo: any, rightScaleSizeInfo: any, frontScaleSizeInfo: any, backScaleSizeInfo: any): any
{
    let targetTrimType: TrimType = TrimType.k_none;
    let layonInfo: any = TBaseRoomToolUtil.instance.getLayonInfoRoomEdgesByFigure(room, figure);
    if(figure.sub_category.includes("淋浴房"))
    {
        if(figure.sub_category == "一字形淋浴房")
        {
            targetTrimType = TrimType.k_frontOrBackMove;
        }
    }
    else
    {
        let isLayonBack: boolean = false;
        for(let edgeType of layonInfo.figureEdgeTypes)
        {
            if(edgeType == RectEdgeType.k_back || RectEdgeType.k_front)
            {
                isLayonBack = true;
            }
        }
        if(!isLayonBack)
        {
            let bestMoveTrimValue: number = sourceTrimValue;
            if(bestMoveTrimValue < leftOrRightMoveInfo.moveTrimValue)
            {
                bestMoveTrimValue = leftOrRightMoveInfo.moveTrimValue;
                targetTrimType = leftOrRightMoveInfo.fineTuningType;
            }
            if(bestMoveTrimValue < frontOrBackMoveInfo.moveTrimValue)
            {
                bestMoveTrimValue = frontOrBackMoveInfo.moveTrimValue;
                targetTrimType = frontOrBackMoveInfo.fineTuningType;
            }
        }
        else
        {
            let bestMoveTrimType: TrimType = TrimType.k_none;
            let bestMoveTrimValue: number = sourceTrimValue;
            if(bestMoveTrimValue < leftOrRightMoveInfo.moveTrimValue)
            {
                bestMoveTrimType = leftOrRightMoveInfo.fineTuningType;
                bestMoveTrimValue = leftOrRightMoveInfo.moveTrimValue;
            }

            // 获取当前图元可缩放的方向
            let scaleTrimTypes: TrimType[] = getFigureScaleTrimType(layonInfo.figureEdgeTypes);
            let bestScaleTrimValue: number = sourceTrimValue;
            for(let fineTuningType of scaleTrimTypes)
            {
                if(fineTuningType == TrimType.k_leftScaleSize)
                {
                    if(bestScaleTrimValue < leftScaleSizeInfo.scaleTrimValue)
                    {
                        bestScaleTrimValue = leftScaleSizeInfo.scaleTrimValue;
                    }
                }
        
                if(fineTuningType == TrimType.k_rightScaleSize)
                {
                    if(bestScaleTrimValue < rightScaleSizeInfo.scaleTrimValue)
                    {
                        bestScaleTrimValue = leftScaleSizeInfo.scaleTrimValue;
                    }
                }
        
                if(fineTuningType == TrimType.k_frontScaleSize)
                {
                    if(bestScaleTrimValue < frontScaleSizeInfo.scaleTrimValue)
                    {
                        bestScaleTrimValue = frontScaleSizeInfo.scaleTrimValue;
                    }
                }
        
                if(fineTuningType == TrimType.k_backScaleSize)
                {
                    if(bestScaleTrimValue < backScaleSizeInfo.scaleTrimValue)
                    {
                        bestScaleTrimValue = backScaleSizeInfo.scaleTrimValue;
                    }
                }
            }
            if(bestMoveTrimValue > bestScaleTrimValue)
            {
                targetTrimType = bestMoveTrimType;
            }
        }
    }
    return {fineTuningType: targetTrimType};
}

function parseFigureScaleInfo(
    room: TRoom, figure: TFigureElement, figures: TFigureElement[], sourceTrimVale: number,
    frontScaleSizeInfo: any, backScaleSizeInfo: any, leftScaleSizeInfo: any, rightScaleSizeInfo: any, sourceScaleStep: number)
{
    let layonInfo: any = TBaseRoomToolUtil.instance.getLayonInfoRoomEdgesByFigure(room, figure);
    let fineTuningTypes: TrimType[] = getFigureScaleTrimType(layonInfo.figureEdgeTypes);
    let bestTrimFineTuningType: TrimType = TrimType.k_none;
    let minDistance: number = Number.POSITIVE_INFINITY;
    let bestTrimValue: number =sourceTrimVale;
    for(let fineTuningType of fineTuningTypes)
    {
        if(fineTuningType == TrimType.k_leftScaleSize)
        {
            if(bestTrimValue < leftScaleSizeInfo.scaleTrimValue)
            {
                bestTrimValue = leftScaleSizeInfo.scaleTrimValue;
                bestTrimFineTuningType = fineTuningType;
            }
        }

        if(fineTuningType == TrimType.k_rightScaleSize)
        {
            if(bestTrimValue < rightScaleSizeInfo.scaleTrimValue)
            {
                bestTrimValue = leftScaleSizeInfo.scaleTrimValue;
                bestTrimFineTuningType = fineTuningType;
            }
        }

        if(fineTuningType == TrimType.k_frontScaleSize)
        {
            if(bestTrimValue < frontScaleSizeInfo.scaleTrimValue)
            {
                bestTrimValue = frontScaleSizeInfo.scaleTrimValue;
                bestTrimFineTuningType = fineTuningType;
            }
        }

        if(fineTuningType == TrimType.k_backScaleSize)
        {
            if(bestTrimValue < backScaleSizeInfo.scaleTrimValue)
            {
                bestTrimValue = backScaleSizeInfo.scaleTrimValue;
                bestTrimFineTuningType = fineTuningType;
            }
        }
    }

    for(let fineTuningType of fineTuningTypes)
    {
        if((fineTuningType == TrimType.k_frontScaleSize && frontScaleSizeInfo.scaleTrimValue > sourceTrimVale && frontScaleSizeInfo.scaleTrimValue >= backScaleSizeInfo.scaleTrimValue && frontScaleSizeInfo.scaleTrimValue >= leftScaleSizeInfo.scaleTrimValue && frontScaleSizeInfo.scaleTrimValue >= rightScaleSizeInfo.scaleTrimValue)
            || (fineTuningType == TrimType.k_backScaleSize && backScaleSizeInfo.scaleTrimValue > sourceTrimVale && backScaleSizeInfo.scaleTrimValue >= frontScaleSizeInfo.scaleTrimValue && backScaleSizeInfo.scaleTrimValue >= leftScaleSizeInfo.scaleTrimValue && backScaleSizeInfo.scaleTrimValue >= rightScaleSizeInfo.scaleTrimValue)
            || (fineTuningType == TrimType.k_leftScaleSize && leftScaleSizeInfo.scaleTrimValue > sourceTrimVale && leftScaleSizeInfo.scaleTrimValue >= frontScaleSizeInfo.scaleTrimValue && leftScaleSizeInfo.scaleTrimValue >= backScaleSizeInfo.scaleTrimValue && leftScaleSizeInfo.scaleTrimValue >= rightScaleSizeInfo.scaleTrimValue)
            || (fineTuningType == TrimType.k_rightScaleSize && rightScaleSizeInfo.scaleTrimValue > sourceTrimVale && rightScaleSizeInfo.scaleTrimValue >= frontScaleSizeInfo.scaleTrimValue && rightScaleSizeInfo.scaleTrimValue >= backScaleSizeInfo.scaleTrimValue && rightScaleSizeInfo.scaleTrimValue >= leftScaleSizeInfo.scaleTrimValue))
        {
            let scaleDist: number = calScalDistByFigure(room, figure, figures, fineTuningType, sourceScaleStep);
            if(scaleDist && scaleDist < minDistance && scaleDist >= sourceScaleStep)
            {
                bestTrimFineTuningType = fineTuningType;
                minDistance = scaleDist;
            }
        }
    }
    return {fineTuningType: bestTrimFineTuningType};
}

function isContainLayonEdgeType(edgeTypes: RectEdgeType[], checkEdgeType: RectEdgeType): boolean
{
    for(let edgeType of edgeTypes)
    {
        if(edgeType == checkEdgeType)
        {
            return true;
        }
    }
    return false;
}

function isContainLayonEdgeTypes(targetLayonEdgeTypes: RectEdgeType[], sourceLayonEdgeTyps: RectEdgeType[]): boolean
{
    let count: number = 0;
    for(let targetEdgeType of targetLayonEdgeTypes)
    {
        if(sourceLayonEdgeTyps.includes(targetEdgeType))
        {
            ++count;
        }
    }
    return count == targetLayonEdgeTypes.length;
}

function getFigureScaleTrimType(edgeTypes: RectEdgeType[]): TrimType[]
{
    let fineTuningTypes: TrimType[] = [];
    if(!isContainLayonEdgeType(edgeTypes, RectEdgeType.k_front))
    {
        fineTuningTypes.push(TrimType.k_frontScaleSize);
    }
    if(!isContainLayonEdgeType(edgeTypes, RectEdgeType.k_back))
    {
        fineTuningTypes.push(TrimType.k_backScaleSize);
    }
    if(!isContainLayonEdgeType(edgeTypes, RectEdgeType.k_left))
    {
        fineTuningTypes.push(TrimType.k_leftScaleSize);
    }
    if(!isContainLayonEdgeType(edgeTypes, RectEdgeType.k_right))
    {
        fineTuningTypes.push(TrimType.k_rightScaleSize);
    }
    fineTuningTypes.sort((a, b) => a - b);
    return fineTuningTypes;
}

function checkFigureOcculsionDoor(room: TRoom, figure: TFigureElement): I_Window
{
    let doors: I_Window[] = room.windows.filter(window => window.realType == "SingleDoor");
    for(let door of doors)
    {
        let occlusionDoorInfo: any = TBaseRoomToolUtil.instance.calOcclusionWindonLen(door, figure);
        if(occlusionDoorInfo.occlusionLen != 0)
        {
            return door;
        }
    }
    return null;
}

function deleteAbnormalSizeFigure(figure: TFigureElement, figures: TFigureElement[], figureScaleInfo: any = null): boolean
{
    if((figureScaleInfo == null ? false : (figure.sub_category == "一字形淋浴房" && figureScaleInfo?.fineTuningType != 0))
        || (figure.sub_category == "钻石形淋浴房" && Math.min(figure.rect.w, figure.rect.h) < 800)
        ||(figure.sub_category == "毛巾架" && (figure.rect.h < 130 || figure.rect.w < 500)))
    {
        return deleteFigure(figure, figures);
    }
    return false;
}

function deleteFigure(figure: TFigureElement, figures: TFigureElement[]): boolean
{
    let index: number = figures.indexOf(figure);
    if(index != -1)
    {
        figures.splice(index, 1);
        return true;
    }
    return false;
}

function getFineTuningInfoByLayoutScore(room: TRoom, figures: TFigureElement[]): any
{
    let layoutScores: I_LayoutScore[] = TLayoutJudgeContainter.ComputeScoreInRoom(room, figures);
    let fineTuningInfo: any = TLayoutFineTuningManagerToolUtil.instance.getAllTrimValueByLayoutScores(layoutScores);
    return fineTuningInfo;
}

function calScalDistByFigure(room: TRoom, figure: TFigureElement, figures: TFigureElement[], sameTrimType: TrimType, sourceScaleStep: number): number
{
    // 这里可以做个预选，看看朝那个方向进行缩放满足的条件较好的情况下需要移动多少步
    let scaleLen: number = 0;
    let scaleDir: Vector3 = getScaleDirByFigure(figure, sameTrimType);
    if(!scaleDir)
    {
        return null;
    }
    let scaleStep: number = sourceScaleStep;
    let fineTuningType: FineTuningType = convertFineTuningType(sameTrimType);
    let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [figure]);
    let lastFineTuningInfo: any = getFineTuningInfoByLayoutScore(room, figures);
    while(true)
    {
        let copyFigure: TFigureElement = figure.clone();
        if(Math.abs(scaleStep) < 0.5)
        {
            break;
        }
        
        let scaleVec: Vector3 = scaleDir.clone().multiplyScalar(scaleLen + scaleStep);
        TLayoutFineTuningOperationToolUtil.instance.scaleFigure(copyFigure, scaleVec, fineTuningType);
        
        let tempFineTuningInfo: any = getFineTuningInfoByLayoutScore(room, [...otherFigures, copyFigure]);
        let frontScaleTempDiff: number = tempFineTuningInfo.fineTuningValue - lastFineTuningInfo.fineTuningValue;
        lastFineTuningInfo = tempFineTuningInfo;
        
        scaleLen = scaleLen + scaleStep;
        if(frontScaleTempDiff < 0.01)
        {
            scaleStep = -scaleStep / 2;
        }
        if(frontScaleTempDiff == 0)
        {
            break;
        }
    }
    return Math.abs(scaleLen);
}

function getScaleDirByFigure(figure: TFigureElement, trimType: TrimType): Vector3
{
    let scaleDir: Vector3 = null;
    switch(trimType)
    {
        case TrimType.k_frontScaleSize:
        {
            scaleDir = figure.rect.frontEdge.v0.pos.clone().sub(figure.rect.backEdge.v1.pos).normalize();
            break;
        }
        case TrimType.k_backScaleSize:
        {
            scaleDir = figure.rect.backEdge.v0.pos.clone().sub(figure.rect.frontEdge.v1.pos).normalize();
            break;
        }
        case TrimType.k_leftScaleSize:
        {
            scaleDir = figure.rect.leftEdge.v0.pos.clone().sub(figure.rect.rightEdge.v1.pos).normalize();
            break;
        }
        case TrimType.k_rightScaleSize:
        {
            scaleDir = figure.rect.backEdge.dv.clone();
            break;
        }
        default:
            break;
    }
    return scaleDir;
}

function convertFineTuningType(trimType: TrimType): FineTuningType
{
    let fineTuningType: FineTuningType = null;
    switch(trimType)
    {
        case TrimType.k_frontScaleSize:
        {
            fineTuningType = FineTuningType.k_front;
            break;
        }
        case TrimType.k_leftScaleSize:
        {
            fineTuningType = FineTuningType.k_left;
            break;
        }
        case TrimType.k_backScaleSize:
        {
            fineTuningType = FineTuningType.k_back;
            break;
        }
        case TrimType.k_rightScaleSize:
        {
            fineTuningType = FineTuningType.k_right;
            break;
        }
        default:
            break;
    }
    return fineTuningType;
}

function getEdgeRange(edge: ZEdge): any
{
    let xMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
    let xMax: number = Math.max(edge.v0.pos.x, edge.v1.pos.x);
    let yMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
    let yMax: number = Math.max(edge.v0.pos.y, edge.v1.pos.y);
    return {xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax};
}

function isStopFineTuningFigureScaleSize(figure: TFigureElement, scaleVec: Vector3, fineTuningType: FineTuningType): boolean
{
    let copyFigure: TFigureElement = figure.clone();
    TLayoutFineTuningOperationToolUtil.instance.scaleFigure(copyFigure, scaleVec, fineTuningType);
    if(copyFigure.sub_category == "马桶")
    {
        if(copyFigure.depth < 550)
        {
            return true;
        }
        if(copyFigure.length < 350)
        {
            return true;
        }
    }
    return false;
}