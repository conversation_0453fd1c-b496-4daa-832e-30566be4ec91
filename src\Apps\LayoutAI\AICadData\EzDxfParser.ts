import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TPainter } from "../Drawing/TPainter";
import { TRoom } from "../Layout/TRoom";
import { compareNames } from "@layoutai/z_polygon";
import { I_EzdxfEntity } from "@layoutai/z_polygon";
import { ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { ZPolyline } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import {Vector3} from "three"
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { I_EzdxfJsonData } from "./EzdxfEntity";
import { I_SwjFurnitureData, I_SwjStructureData, I_SwjWall, I_SwjXmlScheme } from "./SwjLayoutData";
import { AI_PolyTargetType, IRoomEntityRealType, IUIType2TypeDict } from "../Layout/IRoomInterface";
import { TFurnitureEntity } from "../Layout/TLayoutEntities/TFurnitureEntity";
import { WPolygon } from "../Layout/TFeatureShape/WPolygon";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";
interface I_LevelInterval{
    l:number;
    r:number;
    height:number;
}
function getMinHeightInIntervals(intervals:I_LevelInterval[],x:number)
{
    let min_val = -1;
    for(let interval of intervals)
    {
        if(interval.l <=x && x<interval.r)
        {
            if(min_val < 0 || interval.height < min_val)
            {
                min_val = interval.height;
            }
        }
    }
    return min_val;
}
function union_levelIntervals(intervals:I_LevelInterval[])
{
    intervals.sort((a,b)=>a.l - b.l);
    let ans_intervals :I_LevelInterval[] = [];
    let x_vals:number[] = [];
    intervals.forEach(interval=>x_vals.push(interval.l,interval.r));

    for(let i=0; i < x_vals.length-1; i++)
    {
        let tl = x_vals[i];
        let tr = x_vals[i+1];
        let tmid = (tl + tr) / 2;

        let hh = getMinHeightInIntervals(intervals,tmid);

        if(hh > 1)
        {
            ans_intervals.push({l:tl,r:tr,height:hh});
        }


    }
    return ans_intervals;
}
export class EzdxfParser {
    static _instance : EzdxfParser;

    
    private _wall_lines : ZEdge[];
    private _wall_rects : ZRect[];
    private _window_rects : ZRect[];
    private _door_rects : ZRect[];
    private _structure_rects : ZRect[];

    private _furniture_rects : ZRect[];
    private _area_name_rects : ZRect[]; 
    private _mid_lines : ZEdge[];

    private _wall_line_union_polygons : ZPolygon[];
    _is_drawing_generation : boolean = true;

    _hover_ezdxf_entity : I_EzdxfEntity;
    static std_layer_dict :{[key:string]:{strict?: string[],loose?:string[],ignore?:string[] }} = {
        "墙":{strict:["墙"],loose:["墙","WALL","wall","Wall"]},
        "门":{loose:["门","DOOR","door","Door"],ignore:["柜"]},
        "单开门":{strict:["单开门"]},
        "双开门":{strict:["双开门"]},
        "推拉门":{strict:["推拉门"]},
        "一字窗":{strict:["一字窗"],loose:["窗","WINDOW","window","Window"],ignore:["帘"]},
        "烟道":{strict:["烟道"],loose:["烟道"]},
        "包管":{strict:["包管"],loose:["包管"]},
        "柱子":{strict:["柱子"],loose:["柱子"]},
    }
    constructor()
    {
    }
    static get instance()
    {
        if(!EzdxfParser._instance)
        {
            EzdxfParser._instance = new EzdxfParser();
        }
        return EzdxfParser._instance;
    }

    clean()
    {
        this._wall_lines = [];
        this._wall_rects = [];
        this._window_rects = [];
        this._door_rects = [];
        this._mid_lines = [];
        this._structure_rects = [];
        this._area_name_rects = [];
        this._furniture_rects = [];
        this._wall_line_union_polygons = [];
    }

    drawCanvas(painter:TPainter)
    {
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const ezdxf_cad_data = container?.ezdxf_cad_data;
        if(ezdxf_cad_data)
        {
            painter.drawDxfLayout(container.ezdxf_cad_data.modelspace, new Vector3(0, 0, 0), { x: 1, y: 1, z: 1 }, 0, 0);
        }

        if(this._is_drawing_generation) {
            if(this._wall_rects)
            {
                painter.fillStyle = "#aaa";
                painter.fillPolygons(this._wall_rects,0.3);
                painter.strokeStyle = "#2b2b2b";
                painter.strokePolygons(this._wall_rects);
            }
    
            if(this._window_rects)
            {
                painter.fillStyle = "#ff7";
                painter.fillPolygons(this._window_rects,0.3);
            }
    
            if(this._door_rects)
            {
                painter.fillStyle = "#ff7";
                painter.fillPolygons(this._door_rects,0.3);
                painter.strokeStyle="#2b2b2b";
    
                painter.strokePolygons(this._door_rects);
    
            }


            // if(this._structure_rects)
            // {
            //     painter.strokeStyle="#0f0";
            //     painter.strokePolygons(this._structure_rects);

            // }
        }
        if(container.ezdxf_cad_data){
            painter._draw_ezdxf_selected_only = true;
            painter._context.lineWidth = 1;
            painter.drawDxfLayout(container.ezdxf_cad_data.modelspace, new Vector3(0, 0, 0), { x: 1, y: 1, z: 1 }, 0, 0);
            painter._draw_ezdxf_selected_only = false;

            if(this._hover_ezdxf_entity)
            {
                painter._context.lineWidth = 3;

                if(this._hover_ezdxf_entity._entity_points)
                {

                    for(let i=0; i < this._hover_ezdxf_entity._entity_points.length; i+=2)
                    {
                        let v0 = this._hover_ezdxf_entity._entity_points[i];
                        let v1 = this._hover_ezdxf_entity._entity_points[i+1];

                        painter.drawLineSegment(v0,v1,"#0000ffaa");
                    }

                }

                painter._context.lineWidth = 1;

            }
         }

    }

    initEzdxfData(ezdxf_cad_data:I_EzdxfJsonData,painter:TPainter)
    {
        this.clean();
    }

    getStdLayerNameOnDict(name:string)
    {
        let target_layer = "";
        for(let key in EzdxfParser.std_layer_dict)
        {
            let dict = EzdxfParser.std_layer_dict[key];
            if(dict.ignore)
            {
                if(compareNames([name],dict.ignore))
                {
                    continue;
                }
            }
            if(dict.strict)
            {
                if(compareNames([name],dict.strict,false))
                {
                    target_layer = key;
                    break;
                }
            }
            if(dict.loose)
            {
                if(compareNames([name],dict.loose))
                {
                    target_layer = key;
                }
            }
        }
        return target_layer;
    }
    initStdLayers(ezdxf_cad_data:I_EzdxfJsonData)
    {
        for(let entity of ezdxf_cad_data.modelspace.entities)
        {
            if(!entity.attribs.std_layer && entity.attribs.layer)
            {
                if(entity.type === "LINE")
                {
                    let p0 = entity._entity_points[0];
                    let p1 = entity._entity_points[1];
                    if(p0 && p1)
                    {
                        let dx = Math.abs(p0.x-p1.x);
                        let dy = Math.abs(p0.y-p1.y);
                        if(Math.max(dx,dy) > 50 && Math.min(dx,dy) > 10)
                        {
                            continue;
                        }
                    }
                }
                else if(entity.type === "LWPOLYLINE")
                {
                    let has_wall_line = false;
                    for(let i=0; i < entity._entity_points.length; i+=2)
                    {
                        let p0 = entity._entity_points[i];
                        let p1 = entity._entity_points[i+1];
                        if(p0 && p1)
                        {
                            let dx = Math.abs(p0.x-p1.x);
                            let dy = Math.abs(p0.y-p1.y);
                            if(Math.max(dx,dy) > 50 && Math.min(dx,dy) < 5)
                            {
                                has_wall_line = true;
                            }
                        }
                    }
                    if(!has_wall_line) continue;

                }
                let target_layer =this.getStdLayerNameOnDict(entity.attribs.layer)
                if(target_layer && target_layer.length > 0)
                {
                    entity.attribs.std_layer = target_layer;
                }
            }
            if(!entity.attribs.std_layer)
            {
                if(entity.type === "INSERT")
                {
                    let target_layer = this.getStdLayerNameOnDict(entity.attribs.name);
                    if(target_layer && target_layer.length > 0)
                    {
                        entity.attribs.std_layer = target_layer;
                    }
                }
            }
        }
    }
    parseEzdxf2SwjXmlJson(ezdxf_cad_data:I_EzdxfJsonData)
    {
        if(!ezdxf_cad_data) return null;

        this.clean();
        this._wall_rects =[];
        let swj_xml_scheme_json : I_SwjXmlScheme = {
            wall_list:[],
            window_list:[],
            door_list:[],
            room_list:[],
            pillar_list:[],
            flue_list:[],
            platform_list:[],
            beam_list:[],
            pipe_list:[],
            furniture_list:[],
            furniture_group_list:[],
            cabinet_list:[],
            area_names:[]
        }
        let entities = ezdxf_cad_data.modelspace.entities;
        this.makeWallRects(entities);
        this.makeWindowRects(entities);
        this.mergeWallRects();

        this.makeDoorRects(entities);
        this.mergeWallRects();

        this.makeStructureRects(entities);
        this.makeAreaNamePos(entities);
        this.makeFurnitureRects(entities);
        LayoutAI_App.instance.update();

        let uidN = 6;
        for(let wall of this._wall_rects)
        {
            if(!wall.leftEdge || !wall.rightEdge) continue;
            let start_pos = wall.leftEdge.center;
            let end_pos = wall.rightEdge.center;
            let wall_data :I_SwjWall = {
                uid : ++uidN,
                height : 2800,
                thickness : Math.round(wall.h),
                start_x : Math.round(start_pos.x),
                start_y : Math.round(start_pos.y),
                end_x : Math.round(end_pos.x),
                end_y : Math.round(end_pos.y),
            }
            swj_xml_scheme_json.wall_list.push(wall_data);
        }
        for(let win of this._window_rects)
        {
            let pos = win.rect_center;
            swj_xml_scheme_json.window_list.push({
                pos_x:pos.x,
                pos_y:pos.y,
                pos_z:600,
                rotate_z:win.rotation_z,
                mirror:win.u_dv_flag>0?0:1,
                length:win.length,
                width:win.depth,
                height:1500,
                thickness:win.depth,
                uid : ++uidN,
                type : "Window",
                realType : "OneWindow"
            });
        }
        for(let door of this._door_rects)
        {
            let pos = door.rect_center;
            let realType:IRoomEntityRealType = door.ex_prop.label==="推拉门"?"SlidingDoor":"SingleDoor";
            swj_xml_scheme_json.door_list.push({
                pos_x:pos.x,
                pos_y:pos.y,
                pos_z:0,
                rotate_z:door.rotation_z,
                mirror:door.u_dv_flag>0?0:1,
                length:door.length,
                width:door.depth,
                height:2200,
                thickness:door.depth,
                uid : ++uidN,
                type : "Door",
                realType : realType
            });
        }
 
        for(let structure of this._structure_rects)
        {
            let pos = structure.rect_center;
            let realType : IRoomEntityRealType= (IUIType2TypeDict[structure.ex_prop.label||""] || "Pillar") as IRoomEntityRealType;

            let height = 2800;
            let zpos = 0;
            if(realType === "Platform")
            {
                height = 600;
            }
            if(realType ==="Beam")
            {
               height = 300;
               zpos = 2800 - height;
            }
            let entity :I_SwjStructureData = {
                pos_x:pos.x,
                pos_y:pos.y,
                pos_z:zpos,
                rotate_z:structure.rotation_z,
                mirror:structure.u_dv_flag>0?0:1,
                length:structure.length,
                width:structure.depth,
                height:height,
                realType : realType as IRoomEntityRealType,
                type:"StructureEntity",

            }
            if(realType === "Flue")
            {
                swj_xml_scheme_json.flue_list.push(entity);
            }
            else if(realType === "Pillar")
            {
                swj_xml_scheme_json.pillar_list.push(entity);
            }
            else if(realType === "Platform")
            {
                swj_xml_scheme_json.platform_list.push(entity);
            }
            else if(realType === "Beam")
            {
                swj_xml_scheme_json.beam_list.push(entity);
            }
            else if(realType === "Envelope_Pipe")
            {
                swj_xml_scheme_json.pipe_list.push(entity);
            }
        }
        for(let name_pos of this._area_name_rects)
        {
            let pos = name_pos.rect_center;
            swj_xml_scheme_json.area_names.push({
                name : name_pos.ex_prop.label,
                pos_x:pos.x,
                pos_y:pos.y,
                pos_z:0,
            })
        }

        if(this._furniture_rects)
        {
            for(let rect of this._furniture_rects)
            {
                if(compareNames([rect.ex_prop.label],["电视"],false))
                {
                    rect._h = 200;
                    rect.updateRect();
                }
                TBaseEntity.set_polygon_type(rect,AI_PolyTargetType.Furniture);
                let entity = TFurnitureEntity.getOrMakeEntityOfCadRect(rect) as TFurnitureEntity;                
                if(entity)
                {
                    // console.log(entity, rect.ex_prop.label);
                    let t_data = entity.exportData() as I_SwjFurnitureData;
                    t_data._figure_element = entity.figure_element.exportJson();

                    swj_xml_scheme_json.furniture_list.push(t_data);
                }
    
            }
        }
        return swj_xml_scheme_json;

    }
    protected mergeWallRects()
    {
        let wall_lines = this._wall_lines;
        let wall_rects = [...this._wall_rects];
        wall_rects.sort((a,b)=>b.w-a.w);

        //  共线延长
        const IsMerged = "IsMerged";
        const merge_width = 300;
        for(let i=0; i < wall_rects.length; i++)
        {
            let w_rect0 = wall_rects[i];
            if(w_rect0._attached_elements[IsMerged]===true) continue;
            for(let j=i+1; j < wall_rects.length; j++)
            {
                let w_rect1 = wall_rects[j];
                if(w_rect1._attached_elements[IsMerged]===true) continue;

                let same_nor = w_rect0.checkSameNormal(w_rect1.nor); // 同方向
                let same_thickness = Math.abs(w_rect0._h - w_rect1._h) < 1.;
                let same_co_line = Math.abs(w_rect0.project(w_rect1.rect_center).y) < 1.;

                if(!same_nor || !same_thickness || !same_co_line) continue;

                let cx = w_rect0.project(w_rect1.rect_center).x;
                let ll = cx - w_rect1.w/2;
                let rr = cx + w_rect1.w/2;

                if(rr < 0)
                {
                    if(rr > -w_rect0.w/2 - merge_width)
                    {
                        // console.log(w_rect0,w_rect1);
                        let t_ll = Math.min(ll, -w_rect0._w/2);
                        let t_rr = w_rect0.w/2;

                        let t_center = w_rect0.unproject({x:(t_ll+t_rr)/2,y:0});
                        let w = t_rr - t_ll;
                        w_rect0._w = w;
                        w_rect0.rect_center = t_center;

                        w_rect1._attached_elements[IsMerged] = true;
                    }
                }
                else if(ll > 0)
                {
                    if(ll < w_rect0.w/2 + merge_width)
                    {
                        let t_ll = Math.min(ll, -w_rect0._w/2);
                        let t_rr = Math.max(rr,w_rect0.w/2);
    
                        let t_center = w_rect0.unproject({x:(t_ll+t_rr)/2,y:0});
                        let w = t_rr - t_ll;
                        w_rect0._w = w;
                        w_rect0.rect_center = t_center;
    
                        w_rect1._attached_elements[IsMerged] = true;
                    }
    
                }
            }
        }
        //  去掉被包含的矩形
        wall_rects.forEach(rect0=>{
            if(rect0._attached_elements[IsMerged]===true) return;

            wall_rects.forEach(rect1=>{
                if(rect0 === rect1) return;
                if(rect0._attached_elements[IsMerged]===true) return;
                if(rect0.containsPoly(rect1,1))
                {
                    rect1._attached_elements[IsMerged] = true;
                }
            })
        })
        wall_rects = wall_rects.filter((rect)=>rect._attached_elements[IsMerged] !== true);

        // 填充空隙---往两侧延伸(最大max_extend_length)，碰到其他边界为止
        const max_extend_length = 500;
        wall_rects.sort((a,b)=>b.w-a.w);
        wall_rects.forEach(rect=>{
            if(!rect.leftEdge || !rect.rightEdge) return; 
            let t_wall_edges = [...wall_lines];
            wall_rects.forEach((rect1)=>{
                if(rect1 === rect) return;
                t_wall_edges.push(...rect1.edges);
            });

            let ll = -rect._w/2 - max_extend_length;
            let rr = rect._w/2 + max_extend_length;
            let o_ll = ll;
            let o_rr = rr;
            t_wall_edges.forEach(edge=>{
                if(edge.islayOn(rect.leftEdge,max_extend_length,0.05))
                {
                    let t_ll = rect.project(edge.center).x;
                    if(t_ll <= -rect.w/2 +0.1)
                    {
                        ll = Math.max(ll,t_ll);
                    }
                }
                if(edge.islayOn(rect.rightEdge,max_extend_length,0.05))
                {
                    let t_rr = rect.project(edge.center).x;
                    if(t_rr >= rect.w/2 - 0.1)
                    {
                        rr = Math.min(rr,t_rr);
                    }
                }
            });

            if(rr > o_rr - 1.)  rr = rect._w/2; // 没有碰撞到，就无需延申
            if(ll < o_ll + 1.) ll = -rect._w/2; // 没有碰撞到, 就无需延申

            let t_center = rect.unproject({x:(ll+rr)/2,y:0});
            rect._w = (rr-ll);
            rect.rect_center = t_center;

        });



        wall_rects.forEach(rect=>{
            if(rect.min_hh < 100)
            {

            }
            else{
                if(rect.w < rect.h)
                {
                    rect.swapWidthAndHeight();
                }
            }
        })


        this._wall_rects = wall_rects;
        this._mid_lines = this._wall_rects.map((rect)=>{
            if(!rect.leftEdge || !rect.rightEdge) return;
            let edge = new ZEdge({pos:rect.leftEdge.center},{pos:rect.rightEdge.center});
            edge._nor.copy(rect.nor);
            return edge;
        });

        if(this._window_rects)
        {
            for(let win_rect of this._window_rects)
            {
                let target_wall_rect : ZRect = null;
    
                this._wall_rects.forEach((wall)=>{
                    if(!win_rect.checkSameNormal(wall.nor)) return;
                    let pp = wall.project(win_rect.rect_center);
                    if(Math.abs(pp.x) <= wall.w / 2 && Math.abs(pp.y) <= wall.h/2) 
                    {
                        target_wall_rect = wall;
                    }
                });
    
                if(target_wall_rect)
                {
                    let pp = target_wall_rect.project(win_rect.rect_center);
                    win_rect._h = target_wall_rect._h;
                    win_rect.rect_center = target_wall_rect.unproject({x:pp.x,y:0});
                }
                
    
            }
        }

        

    }
    protected makeWallRects(entities:I_EzdxfEntity[])
    {
        this._wall_rects =[];
        let wall_lines : ZEdge[] = [];
        let wall_elements = entities.filter((entity)=>{
            if(entity.type==="CIRCLE" || entity.type==="INSERT") return false;
            if(compareNames([entity.attribs.std_layer||"---"],["墙"],false))
            {
                if (entity.type === "LWPOLYLINE") {
                    let points = entity.lwpoints_v3;

                    // console.log(entity.attribs);
                    // if(points.length == 4)
                    {
                        let poly = new ZPolyline();
                        if(entity.attribs.flags === 1)
                        {
                            poly = new ZPolygon();
                        }
                        let tpoints: Vector3[] = [];
                        poly.initByVertices(points);
                        poly.computeZNor();
                        wall_lines.push(...poly.edges);
                    }
                }
                else if (entity.type === "LINE") {
                    let pos0 = entity.attribs.start_v3.clone();
                    let pos1 = entity.attribs.end_v3.clone();
                    let edge = new ZEdge({ pos: pos0 }, { pos: pos1 });
                    edge.computeNormal();
                    wall_lines.push(edge);
                }
                return true;
            }
            return false;
        });

        wall_lines.sort((a,b)=>a.length - b.length);

        
        this._wall_lines = wall_lines;
        this._mid_lines = [];
        this._wall_rects = [];
        if(wall_lines.length == 0) return;
        const max_wall_pair_distance = 1300;
        const max_wall_thickness = 600;

        let wall_line_extend_polygons : ZPolygon[] = [];
        let extend_size = 10;
        wall_lines.forEach((edge)=>{
            let rect = new ZRect(edge.length+extend_size, extend_size);
            rect.nor = edge.nor;
            rect.rect_center = edge.center;
            wall_line_extend_polygons.push(rect);
        });
        
        this._wall_line_union_polygons = wall_line_extend_polygons[0].union_polygons(wall_line_extend_polygons);

        this._wall_line_union_polygons = this._wall_line_union_polygons.filter((poly)=>poly.orientation_z_nor.z < 0);

        this._wall_line_union_polygons.forEach(poly=>poly.expandPolygon(extend_size/2))


        for(let poly0 of this._wall_line_union_polygons)
        {
            for(let poly1 of this._wall_line_union_polygons)
            {
                if(poly0===poly1) continue;
                if(poly0.containsPoly(poly1))
                {
                    poly0.ex_prop['is_merged'] = '1';
                    poly1.ex_prop['is_merged'] = '1';
                }
            }
        }
        let valid_wall_line_polygons = this._wall_line_union_polygons.filter((poly)=>!poly.ex_prop['is_merged']);
        // ZPolygon.reArrangePolygons(this._wall_line_union_polygons);
        // 给每条wall line定向外方向


        let other_wall_lines : ZEdge[] = [];

        for(let edge0 of wall_lines)
        {
            let check_flag = false;
            valid_wall_line_polygons.forEach(poly=>{
                poly.edges.forEach((edge1)=>{
                    if(edge1.islayOn(edge0,0.1,0.95))
                    {
                        check_flag = true;
                    }
                })
            });
            if(!check_flag) other_wall_lines.push(edge0);
        }

        valid_wall_line_polygons.forEach((poly)=>{
            let sub_polys = WPolygon.splitPolyIntoRects(poly);
            for(let poly of sub_polys)
            {
                if(poly.edges.length > 4)
                {
                    // console.log(poly);
                    continue;
                }
                
                let rect = new ZRect(1,1);
                rect.initByVertices(poly.positions);
                rect.reParaFromVertices();
                rect.updateRect();
                if(rect.min_hh < 10) continue;
                if(rect.w < rect.h)
                {
                    rect.swapWidthAndHeight();
                }
                this._wall_rects.push(rect);
            }
        })

        for(let edge0 of other_wall_lines)
        {
            let side = 0;
            
            let intervals :I_LevelInterval[] = [];

            let sum_length_side0 = 0;
            let sum_length_side1 = 0;
            let t_max_wall_pair_distance = Math.min(edge0.length + 10, max_wall_pair_distance);
            wall_lines.forEach((edge)=>{
                if(edge == edge0) return false;
                let t_edge = edge.computeLayOnEdge(edge0,t_max_wall_pair_distance,0.05);
                if(t_edge && t_edge.length > 1)
                {
                    let dist = edge0.projectEdge2d(edge.center).y;

                    if(Math.abs(dist)<1) return false;
        
                    let ll = edge0.projectEdge2d(t_edge.v0.pos).x;
                    let rr = edge0.projectEdge2d(t_edge.v1.pos).x;
                    if(ll > rr) {
                        let tmp = ll; ll=rr;rr=tmp;
                    }
                    intervals.push({l:ll,r:rr,height:dist});

                    if(Math.abs(dist) < max_wall_thickness)
                    {
                        if(dist < 0)
                        {
                            sum_length_side0 += (rr - ll);
                        }
                        else{
                            sum_length_side1 += (rr - ll);
                        }
                    }
       
                }
            });

            let side_flag = 1;
            if(sum_length_side0 > sum_length_side1)
            {
                side_flag = -1;
            }
            intervals = intervals.filter((ele)=>ele.height*side_flag > 0);

            intervals.forEach((ele)=>ele.height *= side_flag);
            
            let ans_intervals = union_levelIntervals(intervals);
            for(let interval of ans_intervals)
            {                    
                let pos0 = edge0.unprojectEdge2d({x:interval.l, y:interval.height/2 * side_flag });
                let pos1 = edge0.unprojectEdge2d({x:interval.r, y:interval.height/2 * side_flag });
                let t_edge = new ZEdge({pos:pos0},{pos:pos1});
                t_edge._nor.copy(edge0.nor);

                this._mid_lines.push(t_edge);

                let rect = new ZRect(t_edge.length,interval.height);
                rect.nor = t_edge.nor;
                rect.rect_center = t_edge.center;
                this._wall_rects.push(rect);
            }
            

        }
        
    }



    protected makeInsertRect(entity:I_EzdxfEntity)
    {
        let rotation = entity.attribs.rotation || 0;
        let rotation_z = rotation / 180 * Math.PI;
        let nor = new Vector3(Math.sin(rotation_z),-Math.cos(rotation_z),0);
        return ZRect.fromPoints(entity._entity_points,nor);

    }

    protected checkSingleDoorOriginPoint(entity:I_EzdxfEntity, rect:ZRect)
    {
        let points = entity._entity_points;

        let target_pos : Vector3 = null;
        let min_error : number = -1;
        for(let v of rect.vertices)
        {
            let avg_dist = 0;
            points.forEach((p)=>avg_dist+=p.distanceTo(v.pos));
            avg_dist /= points.length;
            let error = 0.;
            points.forEach((p)=>error += Math.abs(p.distanceTo(v.pos) - avg_dist));
            if(!target_pos || error < min_error)
            {
                target_pos = v.pos.clone();
                min_error = error;
            }
        }
        return target_pos;
    }
    protected makeWindowRects(entities:I_EzdxfEntity[])
    {
        let win_lines : ZEdge[] = [];
        let win_rects : ZRect[] = [];
        
        entities.forEach((entity)=>{
            if(entity.type==="CIRCLE") return false;
            if(compareNames([entity.attribs.std_layer||"---"],["一字窗"],true))
            {
                if (entity.type === "LWPOLYLINE") {
                    let points = entity.lwpoints_v3;
                    if(points.length == 4)
                    {
                        let poly = new ZPolyline();
                        let tpoints: Vector3[] = [];
                        poly.initByVertices(points);
                        poly.computeZNor();
                        win_lines.push(...poly.edges);
                    }
                }
                else if (entity.type === "LINE") {
                    let pos0 = entity.attribs.start_v3.clone();
                    let pos1 = entity.attribs.end_v3.clone();
                    let edge = new ZEdge({ pos: pos0 }, { pos: pos1 });
                    edge.computeNormal();
                    win_lines.push(edge);
                }
                else if(entity.type === "INSERT")
                {
                    let rect = this.makeInsertRect(entity);
                    if(rect && rect.min_hh > 10)
                    {
                        if(rect.w < rect.h)
                        {
                            rect.swapWidthAndHeight();
                        }
                        win_rects.push(rect);
                    }
                }
                return true;
            }
            return false;
        });


        const is_visited = "is_visited";

        win_lines.sort((a,b)=>b.length - a.length);

        const win_thickness = 350;
        win_lines.forEach(line0=>{
            if(line0._attached_elements[is_visited] === true) return;
            let layon_edges = win_lines.filter(line1=>{
                return (line0.islayOn(line1,win_thickness,0.1) && line1._attached_elements[is_visited] !== true);
            });
            let points :Vector3[] = [];

            layon_edges.forEach(edge=>{
                edge._attached_elements[is_visited] = true; 
                points.push(edge.v0.pos); points.push(edge.v1.pos)
            });

            let rect = ZRect.fromPoints(points,line0.nor);
            if(rect.min_hh > 10)
            {
                if(rect.w< rect.h)
                {
                    rect.swapWidthAndHeight();
                }
                win_rects.push(rect);
            }
        });
        

        this._window_rects = win_rects;
        this._wall_rects.push(...win_rects.map(rect=>rect.clone()));

    }

    protected makeDoorRects(entities:I_EzdxfEntity[])
    {
        let win_lines : ZEdge[] = [];
        let single_door_rects : ZRect[] = [];
        
        
        // 1: 门洞计算
        let door_hole_rects : ZRect[] = [];
        let wall_edges :ZEdge[] = [];
        this._wall_rects.forEach(rect0=>{
            wall_edges.push(...rect0.edges);
        });
        let candidate_edges :ZEdge[] =  wall_edges.filter((edge)=>edge.length < 450 && edge.length > 80);
        const is_visited = "is_visited";
        const door_wall_dist = 5000;
        wall_edges.sort((a,b)=>a.length-b.length);
        candidate_edges.sort((a,b)=>a.length-b.length);
        candidate_edges.forEach(edge0=>{
            if(edge0._attached_elements[is_visited]===true) return;
            edge0._attached_elements[is_visited] = true;

            let pair_edge:ZEdge = null;
            let min_dist = 0;
            wall_edges.forEach(edge1=>{
                // if(edge1._attached_elements[is_visited]) return;
                if(!edge0.checkSameNormal(edge1.nor)) return; 

                let t_dist = edge0.projectEdge2d(edge1.center).y;
                if(t_dist < 10) return;
                
                let layon_len = edge1.checkLayOnLength(edge0,door_wall_dist,0.01);
                
                if(layon_len > 10)
                {
                    if(!pair_edge || t_dist < min_dist)
                    {
                        pair_edge = edge1;
                        min_dist = t_dist;
                    }
                }
            });
            if(pair_edge)
            {
                if(!edge0.checkSameNormal(pair_edge.nor.clone().negate(),false)) // 最近的一定要反向
                {
                    return;
                }
                if(min_dist < 500) return;
                let l_edge = pair_edge.computeLayOnEdge(edge0,door_wall_dist,0.1);
                if(!l_edge) return;
                if(l_edge.length < 50) return;
                let rect = new ZRect(min_dist,l_edge.length);
                rect.nor = edge0.dv;
                rect.rect_center = l_edge.unprojectEdge2d({x:l_edge.length/2,y:min_dist/2});
                door_hole_rects.push(rect);
                
            }
        });
        const is_merged = "is_merged";
        // 合并门洞
        door_hole_rects.forEach(door_hole=>{
            for(let door_hole1 of door_hole_rects)
            {
                if(door_hole===door_hole1) continue;
                if(door_hole1._attached_elements[is_merged]) continue;

                if(door_hole1.containsPoly(door_hole,1))
                {
                    door_hole._attached_elements[is_merged] = true;
                }
            }
        });
        door_hole_rects = door_hole_rects.filter((rect)=>rect._attached_elements[is_merged] !== true);

        // 过滤一些
        const DoorOriginPoint = "DoorOriginPoint";
        entities.forEach((entity)=>{
            if(entity.type==="CIRCLE") return false;
            if(compareNames([entity.attribs.std_layer||"---"],["单开门","平面门","推拉门","双开门"],true))
            {
                if(entity.type === "INSERT" || entity.type === "ARC")
                {
                    let rect = this.makeInsertRect(entity);
          
                    if(rect && (rect.min_hh > 50 || (rect.max_hh>1000 && rect.min_hh >5)))
                    {
           
                        let pos = this.checkSingleDoorOriginPoint(entity,rect);
                        rect._attached_elements[DoorOriginPoint] = pos;
                        single_door_rects.push(rect);
                        rect.ex_prop.label = entity.attribs.std_layer;

                        if(rect.ex_prop.label === "门")
                        {
                            if(rect.max_hh > 1200)
                            {
                                rect.ex_prop.label = "推拉门";
                                if(rect.w < rect.h)
                                {
                                    rect.swapWidthAndHeight();
                                }
                            }
                        }
                    }
                }
                return true;
            }
            return false;
        });

        this._door_rects = [];//door_hole_rects; //[]// single_door_rects; //  [];//  door_hole_rects;

        single_door_rects.forEach((door_rect)=>{
            let target_door_hole :ZRect = null;

            let target_point:Vector3 =  door_rect.rect_center;


            // let t_rect = new ZRect(100,100);
            // t_rect.rect_center = target_point;
            // this._door_rects.push(t_rect);
            // return;
            let min_dist = 600;
            door_hole_rects.forEach((door_hole)=>{
                let dist = door_hole.distanceToPoint(target_point);

                let xx = door_hole.project(door_rect.rect_center).x;

                if(Math.abs(xx) > 600) return;
                if(door_hole.max_hh > door_rect.max_hh + 600) return;
                if(dist < min_dist)
                {
                    target_door_hole = door_hole;
                    min_dist = dist;
                }
            });

            //  找到了门洞, 则放到门洞里面
            if(target_door_hole)
            {
                let t_door_rect = target_door_hole.clone();
                let t_center = t_door_rect.rect_center;
                let pp0 = t_door_rect.project(door_rect.rect_center);
                if(pp0.y > 0)
                {
                    t_door_rect._nor.negate();
                    t_door_rect.rect_center = t_center;
                }
                if(door_rect._attached_elements[DoorOriginPoint])
                {
                    let o_point = door_rect._attached_elements[DoorOriginPoint];
                    let pp1 = t_door_rect.project(o_point);
                    if(pp1.x > 0)
                    {
                        t_door_rect._u_dv_flag = -t_door_rect.u_dv_flag;
                        t_door_rect.updateRect();
                    }

                }

                t_door_rect.ex_prop.label = door_rect.ex_prop.label;
                this._door_rects.push(t_door_rect);
                this._wall_rects.push(target_door_hole.clone());
            }
            else{
                if(door_rect.length > 1100) // 一般是推拉门
                {
                    let rect_center = door_rect.rect_center;
                    let wall_rect = new ZRect(door_rect.length + 120,Math.max(120,door_rect._h));
                    wall_rect.nor = door_rect.nor;
                    wall_rect.rect_center = rect_center;

                    let t_door_rect = door_rect.clone();
                    t_door_rect._h = wall_rect.h;
                    t_door_rect.rect_center = rect_center;
                    t_door_rect.ex_prop.label = "推拉门";
                    this._door_rects.push(t_door_rect);
                    this._wall_rects.push(wall_rect);
                }
                else{
                    let target_wall_rect : ZRect = null;
                    let min_dist = 120;
                    this._wall_rects.forEach((rect)=>{
                 
                       door_rect.edges.forEach((edge)=>{
                        let dist = rect.distanceToPoint(edge.center);
                        if(dist< min_dist) {
                            target_wall_rect = rect;
                            min_dist =dist;
                        }
                       })                                                              
                    });
                    if(target_wall_rect)
                    {
                        let pp1 = target_wall_rect.project(door_rect.rect_center);
                        let t_center = target_wall_rect.unproject({x:pp1.x,y:0});
                        let t_rect = new ZRect(door_rect.length, target_wall_rect.h);
                        t_rect.nor = target_wall_rect.nor;
                        t_rect.rect_center = t_center;
                        t_rect.ex_prop.label = door_rect.ex_prop.label;
                        this._door_rects.push(t_rect);
                    }
                }
                // let door_thickness = 240;

                // this._door_rects.push()
            }

        })

    }

    protected makeStructureRects(entities:I_EzdxfEntity[])
    {
        let structure_entities = entities.filter((entity)=>{
            if(entity.type === "INSERT")
            {
                if(compareNames([entity.attribs.std_layer||"---"],["烟道","包管","横梁","柱子","地台"]))
                {
                    return true;
                }
            }
            return false;
        });

        this._structure_rects = [];

        structure_entities.forEach((entity)=>{
            let rect = this.makeInsertRect(entity);
            rect.ex_prop.label = entity.attribs.std_layer;
            this._structure_rects.push(rect);
        })
    }

    protected makeAreaNamePos(entities:I_EzdxfEntity[]){
        this._area_name_rects = [];
        entities.forEach((entity)=>{
            if(entity.type === "MTEXT" || entity.type==="TEXT")
            {
                let text = entity.text || entity.attribs.text || "";

                let name = TRoom.getRoomTypeByName(text,"unknown");
                if(name !== "unknown")
                {
                    let rect = new ZRect(10,10);
                    rect.rect_center = entity.attribs.insert_v3;
                    rect.ex_prop.label = text;
                    this._area_name_rects.push(rect);
                }
            }
        })
    }

    protected makeFurnitureRects(entities:I_EzdxfEntity[])
    {
        let furniture_entities = entities.filter((entity)=>{
            if(entity.type === "INSERT")
            {
                if(entity.attribs.layer==="软装家具" || entity.attribs.layer ==="定制柜")
                {
                    return true;
                }
            }
            return false;
        });
        this._furniture_rects = [];
        furniture_entities.forEach((entity)=>{
            let rect = this.makeInsertRect(entity);
            rect.ex_prop.label = entity.attribs.name;
            this._furniture_rects.push(rect);
        })

    }
}