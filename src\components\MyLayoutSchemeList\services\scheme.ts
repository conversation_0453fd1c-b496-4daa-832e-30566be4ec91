import { openApiRequest } from "@/utils";
import { createMagiccubeRequest } from "@svg/request";

function arrayBufferTo<PERSON>son(buffer: ArrayBuffer) {
    const decoder = new TextDecoder('utf-8');
    const jsonString = decoder.decode(new Uint8Array(buffer));
    return JSON.parse(jsonString);
}

const magiccubeRequest = createMagiccubeRequest();

/** 通用请求方法 */
async function postRequest(url: string, options?: { [key: string]: any }) {
    const args = {
        method: 'post',
        url,
        timeout: 10000,
        responseType: 'arraybuffer',
        data: options
    };
    const res = await magiccubeRequest(args).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    if (res instanceof ArrayBuffer) {
        return arrayBufferToJson(res);
    }
    return res;
}

/** 修改方案  */
export async function editSchemeInfo(params?: { [key: string]: any }) {
    const res = await openApiRequest({
        method: 'post',
        url: 'api/njvr/LayoutScheme/edit',
        data: {
            ...params
        },
        timeout: 10000,
    }).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    return res;
}

/** 删除方案  */
export async function deleteScheme(params?: { [key: string]: any }) {
    const res = await openApiRequest({
        method: 'post',
        url: 'api/njvr/LayoutScheme/delete',
        data: {
            ...params
        },
        timeout: 10000,
    }).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    return res;
}

/** 创建方案副本  */
export async function copySheme(params?: { [key: string]: any }) {
    const res = await openApiRequest({
        method: 'post',
        url: 'api/njvr/LayoutScheme/copy',
        data: {
            ...params
        },
        timeout: 10000,
    }).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    return res;
}

/** 获取方案列表  */
export async function listByPage(params?: { [key: string]: any }) {
    const res = await openApiRequest({
        method: 'post',
        url: 'api/njvr/LayoutScheme/listByPage',
        data: {
            ...params
        },
        timeout: 10000,
    }).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    return res;
}

/** 获取方案表详情  */
export async function getSchemeInfo(params?: { [key: string]: any }) {
    const res = await openApiRequest({
        method: 'post',
        url: 'api/njvr/LayoutScheme/get',
        data: {
            ...params
        },
        timeout: 10000,
    }).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    return res;
}

/** 分页查询方案附件信息表  */
export async function filesListByPage(params?: { [key: string]: any }) {
    const res = await openApiRequest({
        method: 'post',
        url: 'api/njvr/LayoutSchemeFiles/listByPage',
        data: {
            ...params
        },
        timeout: 10000,
    }).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    return res;
}

/** 获取附件效果图列表  */
export async function getAiDrawImgList(params?: { [key: string]: any }) {
    const res = await openApiRequest({
        method: 'post',
        url: 'api/njvr/aidrawImageResult/listByPage',
        data: {
            ...params
        },
        timeout: 10000,
    }).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    return res;
}