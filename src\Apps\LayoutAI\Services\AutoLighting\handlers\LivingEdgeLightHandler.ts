import { Vector3 } from "three";
import { <PERSON><PERSON><PERSON>Handler } from "./BaseLightHandler";
import { LightingRuler } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { TSubSpaceAreaEntity } from "../../../Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { RoomSpaceAreaType } from "../../../Layout/IRoomInterface";

export class LivingEdgeLightHandler extends BaseLightHandler {
    
    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        let entities: TFillLightEntity[] = [];
        let layout_container = this.getLayoutContainer();
        let livingArea: TSubSpaceAreaEntity;
        let hallwayArea: TSubSpaceAreaEntity;
        let diningArea: TSubSpaceAreaEntity;
        layout_container._room_entities.forEach(roomEntity => {
            if (this.checkCondition(ruler, roomEntity)) {
                roomEntity._sub_room_areas.forEach(subRoomArea => {
                    switch (subRoomArea.space_area_type) {
                        case RoomSpaceAreaType.LivingArea:
                            livingArea = subRoomArea;
                            break;
                        case RoomSpaceAreaType.DiningArea:
                            diningArea = subRoomArea;
                            break;
                        case RoomSpaceAreaType.HallwayArea:
                            hallwayArea = subRoomArea;
                            break;
                    }
                });
                if (!livingArea) return;

                let rect = livingArea.rect;
                // 按优先级尝试找重叠区域：过道 > 餐厅
                let pos = [hallwayArea, diningArea]
                    .filter(Boolean)
                    .map(area => this.findCommonEdgeCenter(livingArea, area))
                    .find(Boolean);

                if (pos) {
                    let rotation = this.getLightRotation(rect, ruler, pos);
                    let lightEntity = this._createFillLightEntity(
                        ruler.lighting.intensity,
                        ruler.lighting.color,
                        { x: pos.x, y: pos.y, z: ruler.pose.z },
                        this.getSize(ruler.lighting.length, rect.h),
                        this.getSize(ruler.lighting.width, rect.w),
                        rotation.x,
                        rotation.y,
                        rotation.z,
                        ruler.name,
                    );
                    entities.push(lightEntity);
                }
            }
        });
        return entities;
    }

    private findCommonEdgeCenter(areaA: TSubSpaceAreaEntity, areaB: TSubSpaceAreaEntity): Vector3 {
        const tolerance = 0.01;
        for (let i = 0; i < areaA.rect.edges.length; i++) {
            const edgeA = areaA.rect.edges[i];
            for (let j = 0; j < areaB.rect.edges.length; j++) {
                const edgeB = areaB.rect.edges[j];
                if (edgeA.islayOn(edgeB, tolerance, 0.01) || edgeB.islayOn(edgeA, tolerance, 0.01)) {
                    const edgeCenter = new Vector3(
                        (edgeA.v0.pos.x + edgeA.v1.pos.x) / 2,
                        (edgeA.v0.pos.y + edgeA.v1.pos.y) / 2,
                        areaA.rect.rect_center_3d.z
                    );
                    return edgeCenter;
                }
            }
        }
        return null;
    }
}