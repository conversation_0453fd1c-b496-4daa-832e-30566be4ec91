import { Box3, Group, Vector3, Vector3<PERSON>ike } from "three";
import { LayoutAI_App } from "../../../LayoutAI_App";
import { I_SwjBaseGroup, I_SwjEntityBase, I_SwjFurnitureData } from "../../AICadData/SwjLayoutData";
import { g_FigureImagePaths } from "../../Drawing/FigureImagePaths";
import { TPainter } from "../../Drawing/TPainter";
import { BaseGroupObject3D, MeshName, UserDataKey } from "../../Scene3D/NodeName";
import { compareNames } from "@layoutai/z_polygon";
import { GroupCoordinateTransformer } from "../../Utils/geom_utils";
import { ZRect } from "@layoutai/z_polygon";
import { I_MaterialMatchingItem } from "../IMaterialInterface";
import { FigureCategoryManager } from "../TFigureElements/FigureCategoryManager";
import { FigureShapeType, TFigureElement } from "../TFigureElements/TFigureElement";
import { TFigureGroup } from "../TLayoutGraph/TGroupTemplate/TFigureGroup";
import { TGroupTemplate } from "../TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TMaterialMatchingConfigs } from "../../Services/MaterialMatching/TMaterialMatchingConfigs";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TFurnitureEntity } from "./TFurnitureEntity";
import { AI_PolyTargetType, DrawingFigureMode, IRoomEntityType, I_BaseGroupElementInfo, KeyEntity } from "../IRoomInterface";
import { BaseGroupTransferMethods } from "./algorithms/BaseGroupTransferMethods";
import { ZPolygon } from "@layoutai/z_polygon";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { AI_CadData } from "../../AICadData/AI_CadData";

export interface I_BaseGroupData
{
    combination_entitys: TFurnitureEntity[];          // 组合的矩形
    layout_combination_entitys ?: TFurnitureEntity[];
    matched_combination_entitys: TFurnitureEntity[];
}
/**
 *   组合迁移内置信息
 */

/**
 *  绑定了一个TFigureElement
 */
export class TBaseGroupEntity extends TFurnitureEntity {
    _disassembled: boolean;
    _combination_entitys: TFurnitureEntity[];          // 组合的矩形

    _matched_combination_entitys: TFurnitureEntity[];

    static readonly ElementInfoInBaseGroup="Ei_InGroup";
    static readonly EntityType:IRoomEntityType  = "BaseGroup";
    constructor(figure_element: TFigureElement) {
        super(figure_element || null);
        this.type = TBaseGroupEntity.EntityType;
        this.realType = "SoftFurniture";
        this.title = LayoutAI_App.t("组合信息");
        this._is_single_furniture = false;
        this._figure_element = figure_element || new TFigureElement;
        this._rect = this._figure_element.rect;
        this._combination_entitys = this._figure_element._combined_rects;

        this._matched_combination_entitys = [];
        this._matched_visible = false;

        this._default_priority_for_selection = this._priority_for_selection = 10;
        this._disassembled = false;
        this._figure_element.category = figure_element?.rect.ex_prop['GroupName'] || figure_element?.category || figure_element?.sub_category || '未命名';
        this._figure_element.sub_category = figure_element?.rect.ex_prop['GroupName'] || figure_element?.category || figure_element?.sub_category || '未命名';
        this._figure_element.public_category = figure_element?.rect.ex_prop['GroupName'] || figure_element?.category || figure_element?.sub_category || '未命名';

        this._name = this.figure_element.category;
    }


    bindEntity(): void {
        super.bindEntity();
    }

    static fromGroupTemplate(groupTemplate:TGroupTemplate)
    {
        let groupCode: string = groupTemplate?.seed_figure_group?.group_code || groupTemplate?.current_s_group?.group_code || "";
        let groupCategoryName: string = "组合";
        let shapeType: FigureShapeType = null;
        if (groupCode.startsWith("餐桌")) {
            groupCategoryName = "餐桌椅组合";
        } else if (groupCode.indexOf("床") >= 0) {
            groupCategoryName = "床具组合";
        } else if (groupCode.indexOf("沙发") >= 0) {
            groupCategoryName = "沙发组合";
        } else if (groupCode.indexOf("书桌") >= 0) {
            groupCategoryName = "书桌组合";
        } else if (groupCode.indexOf("榻榻米") >= 0) {
            groupCategoryName = "榻榻米组合";
        } else if (groupCode.indexOf("岛台") >= 0) {
            groupCategoryName = "岛台组合";
        } else if (groupCode.indexOf("餐桌") >= 0) {
            groupCategoryName = "餐桌椅组合";
        }
        else {
            let main_category = groupTemplate?.seed_figure_group?.main_figure?.category || "";
            groupCategoryName = main_category + "组合";

        }
        if (groupCode.indexOf("圆") >= 0) {
            shapeType = "圆形";
        }
        let groupRect: ZRect = groupTemplate._target_rect;
        let groupFigureElement = TFigureElement.createSimple(groupCategoryName);
        if (shapeType != null) groupFigureElement._shape = shapeType;
        groupFigureElement.bindRect(groupRect);


        groupFigureElement.rect._attached_elements = {};
        groupFigureElement.rect.ex_prop['GroupName'] = groupFigureElement.category;

        let baseGroup_entity =new TBaseGroupEntity(groupFigureElement)

        let disassembled_figure_elements = groupTemplate?.current_s_group?.figure_elements;

        baseGroup_entity.figure_element._shape = shapeType;
        let sub_entitys : TFurnitureEntity[] = [];
        for (let fig of disassembled_figure_elements) {
            let entity = new TFurnitureEntity(fig);
            if (fig.sub_category.includes('圆')) {
                entity.figure_element._shape = '圆形';
            } else {
                entity.figure_element._shape = null;
            }
            entity.bindFigureEntities();
            sub_entitys.push(entity);
            TBaseGroupEntity.recordGroupRectData(entity, baseGroup_entity);
        }
        baseGroup_entity.combination_entitys = sub_entitys;
        baseGroup_entity.recordBaseGroup(baseGroup_entity);
        baseGroup_entity.updateCategory(groupFigureElement.category);

        baseGroup_entity.update();
        return baseGroup_entity as TBaseGroupEntity;

    }
    static getOrMakeEntityOfCadRect(rect: ZRect): TBaseGroupEntity {
        let entity: TBaseGroupEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            let figure_element = TFigureElement.getOrMakeFigureElementOfRect(rect);
            entity = new TBaseGroupEntity(figure_element);
            entity._rect = rect;
            entity._rect._attached_elements[KeyEntity] = entity;
            entity._combination_entitys = rect._attached_elements["layout_combination_entitys"] || [];
            entity._matched_combination_entitys = rect._attached_elements["matched_combination_entitys"] || [];
            entity.update();
            rect._attached_elements[KeyEntity] = entity;
        }
        return entity;
    }
    static RegisterGenerators()
    {
        TBaseEntity.RegisterPolyEntityGenerator(TBaseGroupEntity.EntityType,TBaseGroupEntity.getOrMakeEntityOfCadRect)
    }
    static is_in_group(wall_rect: ZPolygon) {
        return wall_rect.ex_prop['figure_in_group'] || null;
    }

    static set_in_group(wall_rect: ZPolygon, group_uuid: string) {
        wall_rect.ex_prop['figure_in_group'] = group_uuid;
        if (!group_uuid) delete wall_rect.ex_prop['figure_in_group'];
    }

    get rect() {
        if (this._matched_visible) {
            return this.matched_rect || this._rect;
        } else {
            return this._rect;
        }
    }

    set combination_entitys(entities: TFurnitureEntity[]) {
        if (!this._matched_visible) {
            this._combination_entitys = entities;
        } else {
            this._matched_combination_entitys = entities;
        }
    }

    get combination_entitys(): TFurnitureEntity[] {
        if (this._matched_visible) {
            return this._matched_combination_entitys;
        } else {
            return this._combination_entitys;
        }
    }

    clone() {
        // let entity = super.clone() as TBaseGroupEntity;
        let figure_element = this.figure_element.clone();
        let entity = new TBaseGroupEntity(figure_element);
        entity._combination_entitys = this._combination_entitys.map(item => item.clone());
        return entity;
    }

    updateOnMovement(currentRect: ZRect, totalOffset: Vector3): void {
        if (currentRect != null) {
            // let offsetMovment = currentRect.back_center.clone().sub((T_TransformElement.getOriginRectData(this._rect) || this._rect).back_center);
            this.alignRectToCenter(currentRect);
            // let logcontent = `TBaseGroupEntity.updateOnMovement: ${this.figure_element.modelLoc} movement=(${Math.round(totalOffset.x)}, ${Math.round(totalOffset.y)})  back_center=(${Math.round(this._rect.back_center.x)}, ${Math.round(this._rect.back_center.y)})\n`;
            this._combination_entitys.forEach((entity) => {
                entity.updateOnMovement(null, totalOffset);
                // logcontent += `    ${entity.figure_element.modelLoc} movement=(${Math.round(totalOffset.x)}, ${Math.round(totalOffset.y)})  back_center=(${Math.round(entity._rect.back_center.x)}, ${Math.round(entity._rect.back_center.y)})\n`;
            });
            // logcontent = logcontent.substring(0, logcontent.length - 1);
            // console.info(logcontent);

            this.alignMatchedRectToCenter(currentRect);

            if (this._matched_combination_entitys) {
                this._matched_combination_entitys.forEach((entity) => {
                    entity.updateOnMovement(null, totalOffset);
                });
            }
        } else {
            this.alignRectToCenterWithMovement(totalOffset);
            // let logcontent = `TBaseGroupEntity.updateOnMovement: ${this.figure_element.modelLoc} movement=(${Math.round(totalOffset.x)}, ${Math.round(totalOffset.y)})  back_center=(${Math.round(this._rect.back_center.x)}, ${Math.round(this._rect.back_center.y)})\n`;
            this._combination_entitys.forEach((entity) => {
                entity.updateOnMovement(null, totalOffset);
                // logcontent += `    ${entity.figure_element.modelLoc} movement=(${Math.round(totalOffset.x)}, ${Math.round(totalOffset.y)})  back_center=(${Math.round(entity._rect.back_center.x)}, ${Math.round(entity._rect.back_center.y)})\n`;
            });
            // logcontent = logcontent.substring(0, logcontent.length - 1);
            // console.info(logcontent);
            if (this.matched_rect) {
                this.alignMatchedRectToCenterWithMovement(totalOffset);
            }
            if (this._matched_combination_entitys) {
                this._matched_combination_entitys.forEach((entity) => {
                    entity.updateOnMovement(null, totalOffset);
                });
            }
        }
    }

    setRectProperties(): void {
        super.setRectProperties();
        this.rect._attached_elements[TFigureElement.EntityName] = this._figure_element;
    }


    exportData(): I_SwjEntityBase {
        let data = super.exportData() as I_SwjBaseGroup;
        // 父类的得到的height是_height是默认值1 与当前类的height 存在不一致的情况，所以需要单独赋值
        data.height = this.height;
        data.sub_list = [];
        for (let entity of this.combination_entitys) {
            let t_data = entity.exportData() as I_SwjFurnitureData;
            t_data._figure_element = entity.figure_element.exportJson();
            data.sub_list.push(t_data);
        }
        data.public_category = this.category;
        return data;
    }

    static importData(swj_base_group: I_SwjBaseGroup): TBaseGroupEntity {
        let fe = TFigureElement.fromSwjFurniture(swj_base_group);
        let entity = new TBaseGroupEntity(fe);
        entity.updateCategory(swj_base_group.public_category);
        let combine_entitys = swj_base_group.sub_list.map((ele) => {
            let fe = TFigureElement.fromSwjFurniture(ele);
            let sub_entity = new TFurnitureEntity(fe);
            sub_entity.bindFigureEntities();
            TBaseGroupEntity.recordGroupRectData(sub_entity, entity);
            return sub_entity;
        });
        entity.combination_entitys = combine_entitys;
        entity.bindFigureEntities();
        entity.update();
        return entity;
    }

    makeCombination() {
        for (let entity of this.combination_entitys) {
            TBaseGroupEntity.set_in_group(entity.rect, this._uuid);
        }
    }

    undoCombination() {
        for (let entity of this.combination_entitys) {
            TBaseGroupEntity.set_in_group(entity.rect, null);
        }
    }


    update(): void {
        super.update();
        for (let entity of this.combination_entitys) {
            entity.group_parent = this;
        }
        // this._drawing_order = level_order;
    }

    get figure_elements() {
        if (this._disassembled) {
            let figure_elements: TFigureElement[] = [];
            this._combination_entitys?.forEach((entity: TFurnitureEntity) => {
                figure_elements.push(...entity.figure_elements);
            });
            return figure_elements;
        } else {
            if (this.figure_element != null) {
                return [this.figure_element];
            } else {
                return [];
            }
        }
    }

    get group_element(): TFigureElement {
        return this._figure_element;
    }

    get disassembled_figure_elements(): TFigureElement[] {
        let figure_elements: TFigureElement[] = [];
        this._combination_entitys?.forEach((entity: TFurnitureEntity) => {
            figure_elements.push(...entity.figure_elements);
        });
        return figure_elements;
    }

    get displayed_figure_elements(): TFigureElement[] {
        if (this._matched_visible) {
            return this._matched_combination_entitys.map((entity) => entity.figure_element);
        } else {
            const disassembled_figure_elements = this.disassembled_figure_elements;
            if (disassembled_figure_elements.length > 0) {
                return disassembled_figure_elements;
            } else {
                return [this.figure_element];
            }
        }
    }

    get category() {
        return this._figure_element.category;
    }

    set category(str: string) {
        this._figure_element.category = str;
        this._rect.ex_prop.label = str;
    }


    get public_category() {
        return this._figure_element.public_category || this._figure_element.sub_category;
    }

    set public_category(str: string) {
        this._figure_element.public_category = str;
        this._figure_element.sub_category = str;
    }

    get size() {
        return `${Math.round(this._figure_element.params.depth)} * ${Math.round(this._figure_element.params.length)} * ${Math.round(this._figure_element.params.height)}`
    }

    get material_name() {
        return this._figure_element.material_name || this._figure_element.sub_category;
    }
    get imageUrl() {
        return this._figure_element.image_path;
    }

    set material_name(s: string) {
        this._figure_element.material_name = s;
    }

    get height() {
        return this._figure_element.params.height || 10;
    }

    set height(h: number) {
        this._height = h;
        this._figure_element.params.height = h;
    }

    get groupName() {
        return this._rect.ex_prop['GroupName'];

    }

    get matched_rect() {
        let rect = this._figure_element.matched_rect;
        if (rect) {
            if (!rect._attached_elements["Entity"]) {
                TBaseEntity.bindEntityOfPolygon(rect, this);
                TBaseEntity.set_polygon_type(rect, this.type);
                rect.ex_prop.label = this.category;
                rect._attached_elements[TFigureElement.EntityName] = this._figure_element;
            }
        }

        return this._figure_element.matched_rect || this._rect;
    }

    set matched_rect(rect: ZRect) {
        if (!this._figure_element.matched_rect) {
            this._figure_element.matched_rect = this.rect.clone();
        }
        this._figure_element.matched_rect.copy(rect);

        rect = this._figure_element.matched_rect;
        if (rect) {
            TBaseEntity.bindEntityOfPolygon(rect, this);
            TBaseEntity.set_polygon_type(rect, this.type);
            rect.ex_prop.label = this.category;
            rect._attached_elements[TFigureElement.EntityName] = this._figure_element;
        }
    }

    get length() {
        return Math.round(this._rect.w);
    }

    set length(l: number) {
        this._rect._w = l;
        this.updateGroup();
    }

    get width() {
        return Math.round(this._rect.h);
    }

    get center() {
        return this._rect.rect_center;
    }

    get nor() {
        return this._rect.nor.clone();
    }

    set width(w: number) {
        this._rect._h = w;
        this.updateGroup();
    }
    /**
     *  更新或生成Matched后的组合内实体列表
     */
    public updateMatchedCombinationEntities()
    {

    }
    updateGroup() {
        for (let t_entity of this.combination_entitys) {
            
            let ele_info : I_BaseGroupElementInfo = t_entity.attached_elements as any;
            BaseGroupTransferMethods.applyGroupTransferInGroup(t_entity.rect,ele_info.parent_rect,ele_info);
            if(t_entity?.matched_rect)
            {
                BaseGroupTransferMethods.applyGroupTransferInGroup(t_entity.matched_rect,ele_info.parent_rect,ele_info);
            }
        }
    }

    static recordGroupRectData(t_entity: TFurnitureEntity, group_entity: TFurnitureEntity) {
        return BaseGroupTransferMethods.recordGroupRectDataToMemberRect(t_entity._rect, group_entity._rect);
    }

    static recordGroupMatchedRectData(t_entity: TFurnitureEntity, group_entity: TFurnitureEntity) {
        return BaseGroupTransferMethods.recordGroupRectDataToMemberRect(t_entity.matched_rect, group_entity.matched_rect);
    }

    static getBaseGroupElementInfo(rect:ZRect)
    {
        return rect._attached_elements as I_BaseGroupElementInfo;
    }
    updateCategory(modelLoc: string) {
        this.figure_element.category = modelLoc;
        this.figure_element.sub_category = modelLoc;
        this.figure_element.public_category = modelLoc;
        this.rect.ex_prop['GroupName'] = modelLoc;
    }

    createMemberMatchedEntitiesFromMatchedMaterials(memberMaterials: I_MaterialMatchingItem[], updateCallback: () => void) : TFigureElement[] {
        if (!this.figure_element.matched_rect) {
            this.figure_element.matched_rect = this.figure_element.rect.clone();
        }

        const groupNewPosition = {
            x: this.figure_element.matched_rect.rect_center.x,
            y: this.figure_element.matched_rect.rect_center.y,
            r: this.figure_element.matched_rect.rotation_z
        };
        const groupOldPosition = {
            x: 0,
            y: 0,
            r: 0
        };

        let groupMaterialMemberElements: TFigureElement[] = [];

        let tables: TFigureElement[] = [];
        memberMaterials.forEach((m) => {
            const transResult = GroupCoordinateTransformer.calculateNewPosition(groupOldPosition,
                { x: m.targetPosition.x, y: m.targetPosition.y, r: m.targetRotation.z },
                groupNewPosition);
            m.targetPosition.x = transResult.x;
            m.targetPosition.y = transResult.y;
            m.targetRotation.z = transResult.r;
            let fe = TFigureElement.fromMaterialMatchingItem(m);
            if (FigureCategoryManager.isTableFigure(fe)) {
                fe.decorationElements = [];
                tables.push(fe);
            }
            TBaseEntity.set_polygon_type(fe.rect, AI_PolyTargetType.Furniture);
            if (m.topViewImage != null) {
                let img = new Image();
                img.src = m.topViewImage;
                img.crossOrigin = "Anonymous";
                img.onload = () => {
                    fe.pictureViewImg = img;
                    updateCallback();
                };
            }
            let furnitureEntity = new TFurnitureEntity(fe);
            fe.furnitureEntity = furnitureEntity;
            this._matched_combination_entitys.push(furnitureEntity);
            groupMaterialMemberElements.push(fe);
            // groupDetailLog += "\n" + toStringForMaterialMatchingItem(m);
        });
        this.recordMatchedMaterialScaleRelation(this.figure_element.matched_rect);
        tables.forEach((table) => {
            // console.info("table: " + table.toString());
            this._matched_combination_entitys.forEach((entity) => {
                if (entity.figure_element == table) return;
                if (table.rect.containsPoly(entity.figure_element.rect)) {
                    if (table.rect.zval + table.height - 30 < entity.figure_element.rect.zval && entity.figure_element.rect.zval < table.rect.zval + table.height + 30) {
                        if(!table.decorationElements) table.decorationElements = [];
                        table.decorationElements.push(entity.figure_element);
                    }
                }
            });
        });
        // console.info(groupDetailLog);
        this.setMatchedVisible(true);
        this._matched_combination_entitys = this._matched_combination_entitys.sort((entity1, entity2) => entity1.rect.zval + entity1.height - entity2.rect.zval - entity2.height);
        return groupMaterialMemberElements;
    }

    // 递归获取最底层的矩形,包括BaseGroup自己，所以还是要保留BaseGroup，不然后续撤销回退都有问题
    recursivelyAddCombinationRects(entity?: TBaseGroupEntity): any[] {
        let combinationEntitys: TFurnitureEntity[] = [];
        if (!this.combination_entitys) return [];
        for (let t_entity of this.combination_entitys) {
            if (TBaseEntity.get_polygon_type(t_entity.rect) === 'BaseGroup') {
                let subEntity = t_entity as TBaseGroupEntity;
                if (subEntity !== entity) {
                    combinationEntitys.push(...subEntity.recursivelyAddCombinationRects(subEntity));
                }
                combinationEntitys.push(t_entity);
            } else {
                combinationEntitys.push(t_entity);
            }
        }
        return combinationEntitys;
    }

    recursivelyCollectLayoutCombinationRects(entity?: TBaseGroupEntity): any[] {
        let combinationEntitys: TFurnitureEntity[] = [];
        if (!this._combination_entitys) return [];
        for (let t_entity of this._combination_entitys) {
            if (TBaseEntity.get_polygon_type(t_entity.rect) === 'BaseGroup') {
                let subEntity = t_entity as TBaseGroupEntity;
                if (subEntity !== entity) {
                    combinationEntitys.push(...subEntity.recursivelyCollectLayoutCombinationRects(subEntity));
                }
                combinationEntitys.push(t_entity);
            } else {
                combinationEntitys.push(t_entity);
            }
        }
        return combinationEntitys;
    }

    recursivelyCollectMatchedCombinationRects(entity?: TBaseGroupEntity): any[] {
        let combinationEntitys: TFurnitureEntity[] = [];
        if (!this._matched_combination_entitys) return [];
        for (let t_entity of this._matched_combination_entitys) {
            if (TBaseEntity.get_polygon_type(t_entity.rect) === 'BaseGroup') {
                let subEntity = t_entity as TBaseGroupEntity;
                if (subEntity !== entity) {
                    combinationEntitys.push(...subEntity.recursivelyCollectMatchedCombinationRects(subEntity));
                }
                combinationEntitys.push(t_entity);
            } else {
                combinationEntitys.push(t_entity);
            }
        }
        return combinationEntitys;
    }

    // 记录组合历史关系
    recordBaseGroup(group_entity: TFurnitureEntity): void {
        for (let t_rect of this.combination_entitys) {
            TBaseGroupEntity.recordGroupRectData(t_rect, group_entity);
        }
    }

    recordMatchedMaterialScaleRelation(group_rect: ZRect): void {
        for (let entity of this._matched_combination_entitys) {
            BaseGroupTransferMethods.recordGroupRectDataToMemberRect(entity.matched_rect, group_rect);
        }
    }

    // 递归重新计算组合矩形
    recursivelyUpdateCombinationRects(): void {
        if (!this.combination_entitys) return;
        for (let entity of this.combination_entitys) {
            if (AI_CadData.get_polygon_type(entity.rect) === 'BaseGroup') {
                let subEntity = entity as TBaseGroupEntity;
                subEntity.recursivelyUpdateCombinationRects();
            }
        }
        this.updateSize();
    }

    // 更新当前组合矩形的尺寸
    public updateSize(): void {
        let bbox = new Box3();
        let matched_bbox = new Box3();
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        for (let ele of this.combination_entitys) {
            let rect = ele.rect;
            for (let v of rect.vertices) {
                bbox.expandByPoint(v.pos);
            }
            if(ele.matched_rect)
            {
                for (let v of ele.matched_rect.vertices) {
                    matched_bbox.expandByPoint(v.pos);
                }
            }
        }
        if(this.matched_rect)
        {
            this.matched_rect.copy(ZRect.fromBox3(bbox, this.matched_rect.nor));
        }
        this._rect.copy(ZRect.fromBox3(bbox, this.rect.nor));


        let group_entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(this.rect);
        for (let t_entity of this.combination_entitys) {
            TBaseGroupEntity.recordGroupRectData(t_entity, group_entity);
            if(t_entity.matched_rect)
            {
                TBaseGroupEntity.recordGroupMatchedRectData(t_entity, group_entity);
            }
        }
    }

    getCategoryOptions(): { label: string; value: string }[] {
        let ans: { label: string; value: string }[] = [];
        for (let key in g_FigureImagePaths) {
            let label = g_FigureImagePaths[key].alias || key;
            let value = key;
            ans.push({ label: label, value: value });
        }
        return ans;
    }
    initProperties(): void {

        super.initProperties();
        this._ui_properties["goods"] = {
            name: "信息组件",
            widget: "infoItem",
            value: {
                id: 'null',
                name: LayoutAI_App.t(this.groupName) || LayoutAI_App.t('未命名'),
                size: this.size,
                imageUrl: this.imageUrl,
                type: 'BaseGroup',
            },
        }
        this._ui_properties["length"] = {
            name: LayoutAI_App.t("长度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 10000,
            editable: true,
            defaultValue: '' + Math.round(this.length),
            props: {
                type: "number",
                suffix: "mm"
            }
        }
        this._ui_properties["width"] = {
            name: LayoutAI_App.t("宽度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 10000,
            editable: true,
            defaultValue: '' + Math.round(this.width),
            props: {
                type: "number",
                suffix: "mm"
            }
        }
        this._ui_props_keys = ["goods", "length", "width", "height", "pos_z"];

        if (LayoutAI_App.IsDebug) {
            let scope = this;
            this._ui_properties['exportTemplate'] = {
                name: LayoutAI_App.t("导出组合模板"),
                widget: 'ButtonItem',
                editable: false,
                onClick: () => {
                    let group_template = scope._makeGroupTemplate();
                    if (group_template) {

                        console.log(JSON.stringify(group_template.current_s_group.toJsonData()));
                    }
                }
            }
            this._ui_props_keys = ["goods", "length", "width", "height", "pos_z", "exportTemplate"];

        }
        this._bindPropertiesOnChange();
    }

    rotateMembersAlignToGroupImpl(combinationRects: TFurnitureEntity[]): void {
        // let logContent = "";
        for (let t_entity of combinationRects) {
            let pp = t_entity.rect?._attached_elements['group_p_center'] as Vector3;
            let p_nor = t_entity.rect?._attached_elements['group_p_nor'] as Vector3;
            let parent_rect = t_entity.rect?._attached_elements['parent_rect'] as ZRect;
            let t_rect_center = parent_rect.unproject(pp);  // 图元的中心坐标
            let t_rect_nor = parent_rect.unproject(p_nor);  // 图元将要移动到的坐标

            t_rect_nor.sub(t_rect_center).normalize();   //算出来图元在世界坐标的法向
            // let orgin_rect_rotation_z = t_entity.rect.rotation_z;
            // let orgin_matched_rect_rotation_z = t_entity.matched_rect?.rotation_z;
            t_entity.rect.nor = t_rect_nor;
            t_entity.rect.rect_center = t_rect_center;
            t_entity.rect.updateRect();
            if (t_entity.matched_rect) {
                t_entity.matched_rect.nor = t_rect_nor;
                t_entity.matched_rect.rect_center = t_rect_center;
                t_entity.matched_rect.updateRect();
                // logContent += "  " + t_entity.figure_element.modelLoc + ":  parent_nor=" + Math.round(parent_rect.rotation_z / Math.PI * 180) + " rect.rotation_z=" + Math.round(orgin_rect_rotation_z / Math.PI * 180) + "->" + Math.round(t_entity.rect.rotation_z / Math.PI * 180) + ", matched_rect.rotation_z=" + Math.round(orgin_matched_rect_rotation_z / Math.PI * 180) + "->" + Math.round(t_entity.matched_rect.rotation_z / Math.PI * 180) + "\n";
            }
        }
        // if (logContent.length > 0) {
        //     logContent = "rotateMembersAlignToGroupImpl()\n" + logContent;
        //     console.info(logContent);
        // }
    }

    rotateAllEntityMembersAlignToGroup(): void {
        let layoutCombinationRects: TFurnitureEntity[] = this.recursivelyCollectLayoutCombinationRects();
        this.rotateMembersAlignToGroupImpl(layoutCombinationRects);
        let matchedCombinationRects: TFurnitureEntity[] = this.recursivelyCollectMatchedCombinationRects();
        this.rotateMembersAlignToGroupImpl(matchedCombinationRects);
        this.update();
    }

    rotateAllMatchedMemberMaterialsAlignToGroup(): void {
        let matchedCombinationRects: TFurnitureEntity[] = this.recursivelyCollectMatchedCombinationRects();
        this.rotateMembersAlignToGroupImpl(matchedCombinationRects);
        this.update();
    }

    updateMesh3D() {
        if (compareNames([this.category], TMaterialMatchingConfigs._ignoreCategories)) return null;
        if (!this._mesh3d) {
            this._mesh3d = new BaseGroupObject3D();
            this._mesh3d.name = MeshName.BaseGroup;
        }
        for (let child of this._mesh3d.children) {
            this._mesh3d.remove(child);
        }
        this.combination_entitys.forEach(entity => {
            entity.updateMesh3D();

            this._mesh3d.add(entity._mesh3d);

        });
        this._mesh3d.userData[UserDataKey.EntityOfMesh] = this;
        return this._mesh3d;
    }

    /**
     *  生成组合模板
     */
    _makeGroupTemplate() {
        let figure_elements = this.displayed_figure_elements;

        let figure_group = TFigureGroup.fromFiguresInRect(figure_elements, this.rect);

        if (figure_group) {
            let group_template = new TGroupTemplate().makeBySeedFigureGroup(figure_group as any);
            return group_template;
        }
        return null;
    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (AI_CadData.is_deleted(this.rect)) return;

        // console.info("TBaseGroupEntity.drawEntity() " + this.figure_element.modelLoc);

        let rect = this._rect;
        painter._context.lineWidth = 2;
        if (options.is_draw_texture || options.is_draw_outline) {
            rect = this.matched_rect || this.rect;
        }
        if (options.is_selected) {
            painter.strokeStyle = "#147FFA";
            painter.strokePolygons([rect]);
            painter._context.lineWidth = 1;
        }
        if (options.is_hovered && !options.is_mobile) {
            painter.strokeStyle = "#147FFA";
            painter.strokePolygons([rect]);
            painter._context.lineWidth = 1;
        }

        if (options.is_draw_figure) {
            let white_style = painter._style_mapping['white'];
            let black_style = painter._style_mapping['black'];
            if (options.is_draw_texture) {
                if (this.group_element.haveMatchedMaterial() && this.group_element.pictureViewImg != null) {
                    if (this._matched_combination_entitys.length == 0) {
                        // console.info(this._uid + " TBaseGrouEntity.drawEntity() " + this.figure_element.modelLoc + "  previewFigure");
                        this.group_element.drawPreviewFigure(painter, false);
                    } else {
                        // console.info(this._uid + " TBaseGrouEntity.drawEntity() " + this.figure_element.modelLoc);
                        this._matched_combination_entitys.sort((a, b) => {
                            let a_entity = a as TFurnitureEntity;
                            let b_entity = b as TFurnitureEntity;
                            return a_entity.rect.zval + a_entity.height - b_entity.rect.zval - b_entity.height;
                        })
                        for (let entity of this._matched_combination_entitys) {
                            if (entity) {
                                if (entity.is_selected) continue;
                                entity.drawEntity(painter, { 
                                    is_selected: false, 
                                    is_hovered: false, 
                                    is_draw_figure: true, 
                                    is_draw_texture: (entity.figure_element.renderedTopViewImg || entity.figure_element.pictureViewImg || entity.figure_element._matched_material.topViewImage) ? true : false,
                                });
                            }
                        }
                    }
                } else {
                    this.combination_entitys.sort((a, b) => {
                        let a_entity = a as TFurnitureEntity;
                        let b_entity = b as TFurnitureEntity;

                        return a_entity._drawing_order - b_entity._drawing_order;
                    })
                    for (let entity of this.combination_entitys) {
                        if (entity) {
                            if (entity.is_selected) continue;
                            const shouldDrawTexture: boolean = entity.figure_element.pictureViewImg != null || entity.figure_element.renderedTopViewImg != null;
                            entity.drawEntity(painter, { 
                                is_selected: false, 
                                is_hovered: false, 
                                is_draw_figure: true, 
                                is_draw_texture: shouldDrawTexture, 
                                draw_decoration: true 
                            });
                        }
                    }
                }
            }
            else {
                if (this.is_selected && this._figure_element.default_drawing_order < 2) {
                    painter._context.globalAlpha = 0.8;
                }
                if(this.group_element._wireFrameImage)
                {
                    this.group_element.drawOutline(painter, false);
                } else 
                {
                    this._combination_entitys.sort((a, b) => {
                        let a_entity = a as TFurnitureEntity;
                        let b_entity = b as TFurnitureEntity;
    
                        return a_entity._drawing_order - b_entity._drawing_order;
                    })
                    for (let entity of this._combination_entitys) {
                        if (entity) {
                            if (entity.is_selected) continue;
                            entity.drawEntity(painter, { is_selected: false, is_hovered: false, is_draw_figure: true, is_draw_outline: options.is_draw_outline });
                        }
                    }
                }
            }

            painter._style_mapping['white'] = white_style;
            painter._style_mapping['black'] = black_style;
        }
    }

    public clearMatchedMaterials() {
        this._matched_combination_entitys.forEach((entity) => {
            entity.clearMatchedMaterials();
        });
        this._matched_combination_entitys = [];
        if (this.rect._attached_elements["layout_combination_entitys"] && this.rect._attached_elements["layout_combination_entitys"].size > 0) {
            this._combination_entitys = this.rect._attached_elements["layout_combination_entitys"];
        }
        delete this.rect._attached_elements["combination_entitys"];
        delete this.rect._attached_elements["matched_combination_entitys"];
        this.setMatchedVisible(false);
    }

    public cleanDecoration(): void {
        if ( this._combination_entitys != null) {
            this._combination_entitys.forEach((entity) => {
                entity.cleanDecoration();
            }); 
        }
        if (this._matched_combination_entitys != null) {
            this._matched_combination_entitys.forEach((entity) => {
                entity.cleanDecoration();
            });
        }
    }
}