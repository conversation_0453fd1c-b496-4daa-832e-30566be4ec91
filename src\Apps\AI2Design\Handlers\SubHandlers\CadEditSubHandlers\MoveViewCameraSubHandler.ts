import { LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { Vector3 } from "three";
import { EventName } from "@/Apps/EventSystem";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";

// 简单的鼠标事件接口定义
interface SimpleMouseEvent {
    posX: number;
    posY: number;
    buttons: number;
    button?: number;
    altKey?: boolean;
    ctrlKey?: boolean;
    shiftKey?: boolean;
    type?: string;
    _ev?: Event;
}

export class MoveViewCameraSubHandler extends CadBaseSubHandler {

    _last_pos: Vector3;
    private _isMouseMoved = false;

    /**
     * 启用吸附
     */
    _using_adsorption = true;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.Transform_Moving;

        this._last_pos = null;
    }

    enter() {
        super.enter();
        console.log("MoveViewCameraSubHandler enter");
    }

    leave() {
        super.leave();
        console.log("MoveViewCameraSubHandler leave");
    }
} 