import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { EventName } from "@/Apps/EventSystem";
import { TRulerEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRulerEntity";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_CursorState } from "@/Apps/LayoutAI_App";
import { Vector3, Vector3Like } from "three";
import { CadBaseSubHandler } from "./CadBaseSubHandler";


export class RulerModeHandler extends CadBaseSubHandler {

    _wall_end_points: Vector3[];

    /**
     *  前次绘制的矩形
     */
    _previous_rect: ZRect;
    selected_entity: TRulerEntity;
    _hover_entity: TRulerEntity;
    _candidate_point: Vector3 = null;
    closeNum: number;

    drawEdage: ZEdge;
    drawPoint: Vector3;
    _last_pos: Vector3;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this._wall_end_points = [];
        this._candidate_point = new Vector3();
        this.closeNum = 0;
        this.drawEdage = null;
        this.drawPoint = null;
        this._previous_rect = null;
        this.selected_entity = null;
        this._hover_entity = null;
        this._last_pos = null;
    }

    get wall_rects(): ZRect[] {
        return this.container.getCandidateRects(["Wall"]);
    }
    get furniture_rects(): ZRect[] {
        return this.container.getCandidateRects(["Furniture"]);
    }

    enter(state?: number): void {
        super.enter(state);
        this.cleanSelection();
        this.manager.layout_container.cleanDimension();
        this.manager.layer_RulerLayer.visible = true;
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);
        this.update();
    }
    leave(state?: number): void {
        super.leave(state);
        this.updateCandidateRects();
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        this._wall_end_points = [];
        this.closeNum = 0;
        this.manager.layer_RulerLayer.visible = true;
        this.update();
    }


    adsorbent(pos: Vector3Like) {
        let t_pos: Vector3 = new Vector3().copy(pos);
        this.drawEdage = null;
        this.drawPoint = null;
        let dist = 100;
        for (let rect of [...this.wall_rects, ...this.furniture_rects]) {
            for (let edge of rect.edges) {
                // 鼠标起始点移动时候的吸附
                let pp = edge.projectEdge2d(t_pos);
                if (pp.x < 0 || pp.x > edge.length) continue;
                if (Math.abs(pp.y) < Math.abs(dist)) {
                    dist = (pp.y);
                    this.drawEdage = edge;
                    let pos = edge.unprojectEdge2d({ x: pp.x, y: 0 });
                    this.drawPoint = pos;
                    if (this._wall_end_points[0]) {
                        let t_v = t_pos.clone().sub(this._wall_end_points[0].clone())
                        if (Math.abs(t_v.x) < 100) {
                            t_v.x = 0;
                            this.drawPoint.x = this._wall_end_points[0].x;
                        }
                        if (Math.abs(t_v.y) < 100) {
                            t_v.y = 0;
                            this.drawPoint.y = this._wall_end_points[0].y;
                        }

                    }
                }
            }
        }
    }

    udpateCandidatePoint(pos: Vector3Like): void {

        let t_pos: Vector3 = new Vector3().copy(pos);

        if (this.drawPoint) {
            this._candidate_point.copy(this.drawPoint);
        } else {
            this._candidate_point.copy(t_pos);
        }

    }

    updateSelectedRule(): void {
        if (this.manager.layout_container._ruler_entities && this.manager.layout_container._ruler_entities.length > 0) {
            this.manager.layout_container._ruler_entities.forEach((entity) => {
                entity.is_selected = false;
            });
        }
        this.selected_entity = null;
    }

    onmousedown(ev: I_MouseEvent): void {
        this.updateSelectedRule();
        if (ev.button == 0) {
            if (this._hover_entity) {
                this._previous_rect = this._hover_entity.rect.clone();
                this.selected_entity = this._hover_entity;
                this._last_pos = new Vector3(ev.posX, ev.posY, 0);
                this.selected_entity.is_selected = true;
                this._cad_mode_handler._is_moving_element = false;
                this.update();
                return;
            }
            if (this._candidate_point) {
                if (this._wall_end_points.length < 2) {
                    this._wall_end_points.push(this._candidate_point.clone());
                }
                if (this._wall_end_points.length == 2) {
                    let ruler_entity = new TRulerEntity();
                    ruler_entity._v0 = this._wall_end_points[0].clone();
                    ruler_entity._v1 = this._candidate_point.clone();
                    ruler_entity.updateRect();
                    this.manager.layout_container._ruler_entities.push(ruler_entity);
                    this._wall_end_points = [];
                    this._candidate_point = new Vector3();
                }
            }
        }

        if (ev.button == 2) {
            this._wall_end_points.pop();
        }
        this.drawPoint = null;
        this.update();
    }

    onmousemove(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        let current_pos = new Vector3(ev.posX, ev.posY, 0);
        if (ev.buttons == 0) {
            this._cad_mode_handler._is_moving_element = true;
        }
        if (ev.buttons == 2) {
            this._cad_mode_handler._is_moving_element = false;
        }

        if (ev.buttons == 1 && this._previous_rect) {
            let movement = current_pos.clone().sub(this._last_pos);
            let dv = this.selected_entity.rect.nor;
            let projection = dv.clone().multiplyScalar(movement.dot(dv) / dv.dot(dv));
            this.selected_entity.rect.rect_center = this._previous_rect.rect_center.clone().add(projection);
            this._cad_mode_handler._is_moving_element = true;
            this.update();
            return;
        }

        this.updateHoverRect(pos)

        this.udpateCandidatePoint(pos);

        this.adsorbent(pos);
        this.update();


    }
    updateHoverRect(pos: Vector3Like) {
        let target_entity = this.getRectContainsPos(pos);
        if (target_entity) {
            this._hover_entity = target_entity;
            this.manager.setCanvasCursorState(LayoutAI_CursorState.Pointer);
        } else {
            this._hover_entity = null;
            this.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);
        }
    }

    getRectContainsPos(pos: Vector3Like): TRulerEntity {
        for (let entity of this.manager.layout_container._ruler_entities) {
            if (entity._rect.containsPoint(pos, 100)) {
                entity.is_hovered = true;
                return entity;
            }
            entity.is_hovered = false;
        }
        return null;
    }

    onmouseup(ev: I_MouseEvent): void {
        this._cad_mode_handler._is_moving_element = false;
        this._previous_rect = null;
        if (this.selected_entity && this.selected_entity.is_selected && ev.button == 0) {
            let _pp = this.painter.worldToCanvas(this.selected_entity.rect.leftEdge.center);
            this.EventSystem.emit(EventName.SelectingRulerTarget, this.selected_entity, _pp);
            // this.selected_entity.is_selected = false;
        } else {
            this.EventSystem.emit(EventName.SelectingRulerTarget, null);
        }
        if (ev.button == 2) {
            this.closeNum++;
            this._wall_end_points = [];
            // if(this.closeNum >= 2)
            // {
            //     this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
            // }

            this.update();
            return;
        }
        this.update();
    }

    drawCanvas(): void {
        if (this.drawEdage && this.drawPoint) {
            this.painter._context.lineWidth = 1;
            this.painter.drawLineSegment(this.drawEdage.v0.pos, this.drawEdage.v1.pos, '#147FFA');
            let radius = 6 / this.painter._p_sc;
            this.painter.strokeStyle = "#147FFA";
            this.painter.drawPointCircle(this.drawPoint, radius, 4);
        }

        if (this._wall_end_points.length > 0) {
            let entity = new TRulerEntity();
            entity._v0 = this._wall_end_points[0].clone();
            entity._v1 = this._candidate_point.clone();
            entity.updateRect();
            entity.drawEntity(this.painter);
        }
        for (let entity of this.manager.layout_container._ruler_entities) {
            if (entity.is_hovered) {
                this.painter._context.lineWidth = 1;
                let radius = 6 / this.painter._p_sc;
                this.painter.strokeStyle = "#147FFA";
                this.painter.fillStyle = "#147FFA";
                this.painter.drawPointCircle(entity._v0, radius, 3);
                this.painter.drawPointCircle(entity._v1, radius, 3);
            }
            if (entity.is_selected) {
                this.selected_entity.drawEntity(this.painter);
            }
        }
    }

} 