// src/Apps/LayoutAI/Services/ViewCameraRules/SofaViewCameraRule.ts
import { ZRect } from "@layoutai/z_polygon";
import { BaseViewCameraRule } from "./BaseViewCameraRule";
import { TRoomEntity } from "../../TRoomEntity";
import { TViewCameraEntity } from "../TViewCameraEntity";
import { Matrix4, PerspectiveCamera, Vector3, Vector3Like, Vector4 } from "three";
import { TRoomShape } from "../../../TRoomShape";
import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../TBaseGroupEntity";
import { BedRoomViewCameraRule } from "./BedRoomViewCameraRule";
import { ZEdge } from "@layoutai/z_polygon";
export class SofaViewCameraRule extends BaseViewCameraRule {
    static test_rect = new ZRect(100, 100);
    // 计算沙发视角
    static generateViewCamera(sofa_element: TFigureElement, room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}, subArea?: ZRect): TViewCameraEntity[] {
        let camera = new PerspectiveCamera(75, 3.0 / 4.0, 300, 20000);
        let main_rect = sofa_element.rect;
        let sofa_rect = null as ZRect;
        let sofa_desk = null as ZRect;
        let tv_cabinet_entity = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], ["电视柜"]))[0];
        // 计算沙发中心和延长线
        let sofa_rect_center = new Vector3();
        let sofa_front_center = new Vector3();
        if(sofa_element.furnitureEntity instanceof TBaseGroupEntity){
            (sofa_element.furnitureEntity as TBaseGroupEntity).combination_entitys.forEach(ele => {
                if(compareNames([ele.category], ["沙发"])){
                    sofa_rect_center = ele?.matched_rect?.rect_center || ele.rect.rect_center;
                    sofa_front_center = ele?.matched_rect?.front_center || ele.rect.front_center;
                    sofa_rect = ele?.matched_rect || ele.rect;
                }
                if(compareNames([ele.category], ["茶几"])){
                    sofa_desk = ele?.matched_rect || ele.rect;
                }
            });
        } else 
        {
            sofa_rect_center = sofa_element?.matched_rect?.rect_center || sofa_element.rect.rect_center;
            sofa_front_center = sofa_element?.matched_rect?.front_center || sofa_element.rect.front_center;
            sofa_rect = sofa_element?.matched_rect || sofa_element.rect;
            room_entity._room._furniture_list.forEach((ele)=>{
                if(compareNames([ele.category], ["茶几"])){
                    sofa_desk = ele?.matched_rect || ele.rect;
                }
            })
        }
        let view_cameras = []
        // 看沙发视角
        if(sofa_desk){
            let tv_rect = new ZRect(500, 500); 
            tv_rect.nor = sofa_rect.nor.clone().negate();
            let pp = sofa_rect.project(sofa_desk.rect_center);
            
            tv_rect.rect_center = sofa_rect.unproject({ x: 0, y: pp.y + 2500 });
   
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(tv_rect);
            view_camera._room_entity = room_entity;
            view_camera._view_center = tv_rect.rect_center;
            view_camera._main_rect = sofa_element.rect;
            view_camera.name = '客厅-朝向沙发';
            view_camera._target = ['沙发'];
            view_camera.ukey = 'sofa_view_camera';
            view_camera.is_focus_mode = true;
            view_camera.hideFurnitures = this.hideFurnitures;
            TViewCameraEntity._test_rect = view_camera.rect;
            view_cameras.push(view_camera);
        } else 
        {   
            view_cameras = TViewCameraEntity.updateCameraByEdge(camera, subArea, room_entity, prefix, options);
        }
      
        // 调整 FOV 根据距离
        if(sofa_rect_center && view_cameras.length > 0){
            const distanceToSofa = sofa_rect_center.distanceTo(view_cameras[0].rect.rect_center);
            view_cameras[0].fov = 60; // 默认 FOV
            view_cameras[0].rect.zval = 1150;
            if ((distanceToSofa - 740) < 1500) {
                view_cameras[0].fov = 75; // 调整 FOV
            }
        }

        // ------------------------------------------------------------------------------------------------------------------------------
        // 新增一个从沙发中心到电视柜的视角的规则
        let tv_rect = new ZRect(500, 500);
        let rect_center = sofa_rect_center.clone();
        if(tv_cabinet_entity){
            let pp = sofa_rect.project(tv_cabinet_entity.rect.rect_center);
            rect_center = sofa_rect.unproject({ x: pp.x, y: 0 });
        }
        
        tv_rect.nor = main_rect.nor.clone();
        tv_rect.rect_center = rect_center.clone();
        tv_rect.zval = 1150;
        let view_camera = new TViewCameraEntity();
        view_camera.rect.copy(tv_rect);
        view_camera._room_entity = room_entity;
        let view_camera_name = prefix;
        view_camera.name = '客厅-朝向电视柜';
        view_camera._target = ['电视柜'];
        view_camera.ukey = 'tv_view_camera';
        view_camera._is_focus_mode = (options.no_focus_mode || false) ? false : true;
        view_camera._view_center = sofa_rect_center.clone();

        // 调整 FOV 根据距离
        let cabinet_entity = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], ["柜"]));
        view_camera.fov = 60; // 默认 FOV
        if(cabinet_entity.length > 0){
            for(let cabinet of cabinet_entity){
                let rect = cabinet?.matched_rect || cabinet.rect;
                let int_data = rect.getRayIntersection(sofa_rect_center, main_rect.nor.clone());
                if(int_data.point){
                    let length = int_data.point.distanceTo(sofa_front_center);
                    if(length < 1500){
                        view_camera.fov = 75; // 调整 FOV
                    }
                }
            }
        }
        
        view_cameras.push(view_camera);


        // 视角3  从餐厅区看向客厅区，主要是看电视柜和沙发 -------------------------------------------------------------------------------------------------------------------------------

        // 第一步先计算沙发到电视柜的中点
        let room_poly = room_entity._room_poly.clone();
        // 第二步计算相机位置
        let wall_edges = [sofa_rect.leftEdge, sofa_rect.rightEdge];
        let maxDist = -Infinity;
        let edgeWithMax = null as ZEdge;
        if(tv_cabinet_entity)

        {
            wall_edges.forEach((edge) => {
                let pp = edge.projectEdge2d(tv_cabinet_entity.rect.frontEdge.center);
                if (!edge) return;
                let int_data = room_poly.getRayIntersection(edge.unprojectEdge2d({ x: (pp.x / 2) + (edge.length / 2), y: -5 }), edge.nor.clone());
                if (!int_data || !int_data.point) return;
                let dist_to_front_wall = int_data.point.clone().sub(edge.center).length();
                if (dist_to_front_wall > maxDist) {
                    maxDist = dist_to_front_wall;
                    edgeWithMax = edge;
                }
    
            });
            let pp = edgeWithMax.projectEdge2d(tv_cabinet_entity.rect.frontEdge.center);
            if (!edgeWithMax) return;
            let int_data = room_poly.getRayIntersection(edgeWithMax.unprojectEdge2d({ x: (pp.x / 2) + (edgeWithMax.length / 2), y: -5 }), edgeWithMax.nor.clone());
            if (!int_data || !int_data.point) return;
    
            let dist_to_front_wall = int_data.point.clone().sub(edgeWithMax.center).length();
            // 沙发离墙距离小于1000mm，过滤
            let max_dist = dist_to_front_wall + 850;
            if(dist_to_front_wall < 1000){
                return;
            }
            if(dist_to_front_wall > 1900){
                max_dist = 3000;
            }
            let t_rect = new ZRect(500, 500);
            t_rect.nor = int_data.edge.nor;
            t_rect.zval = 1150; // 默认高度还是高一些1400mm更合理
            // 计算相机的位置
            let point = edgeWithMax.unprojectEdge2d({ x: (pp.x / 2) + (edgeWithMax.length / 2), y: max_dist });
            t_rect.rect_center = point.clone();
            t_rect.nor = edgeWithMax.nor.clone().negate();
            let _view_camera = new TViewCameraEntity();
            _view_camera.fov = 65;   //默认65 
            _view_camera.rect.copy(t_rect);
            _view_camera.rect.zval = 1150;
            this.test_rect = _view_camera.rect;
            _view_camera._room_entity = room_entity;
            _view_camera.name = '客厅-侧方';
            _view_camera.ukey = 'livingroom_view_camera';
            _view_camera._target = ['沙发','电视柜','茶几','桌几'];
            _view_camera._is_focus_mode = (options.no_focus_mode || false) ? false : true;
    
            if (!_view_camera.is_focus_mode) {
                _view_camera.near = 600;
            }
            _view_camera._view_center = main_rect.rect_center;
            _view_camera._room_entity = room_entity;
            _view_camera._hide_names = ['餐桌','餐椅'];
            _view_camera._is_focus_mode = true;
            _view_camera._main_rect = sofa_rect;
            _view_camera.hideFurnitures = this.hideFurnitures;
    
            if(dist_to_front_wall < 1500){
                _view_camera.fov = 75;
            }
            view_cameras.push(_view_camera);
    
            let test_rect = _view_camera.rect;
            test_rect._w = 1000;
            test_rect._h = 900;
            test_rect.rect_center = _view_camera.rect.rect_center;
            TViewCameraEntity._test_rect = test_rect;

        } 
  

        return view_cameras;
    }
}