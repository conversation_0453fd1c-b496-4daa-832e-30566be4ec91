


import ezdxf
from ezdxf.enums import TextEntityAlignment
from ezdxf.addons import tablepainter
from ezdxf.document import Drawing
from ezdxf import units,zoom
from ezdxf.layouts import Layout
from ezdxf.math import ConstructionArc
from ezdxf.entities.lwpolyline import LWPolyline
from ezdxf.entities.spline import Spline
from ezdxf.entities.hatch import Hatch
from ezdxf.entities.dimension import Dimension
from ezdxf.entities.insert import Insert
from ezdxf.lldxf.tagwriter import AbstractTagWriter
from ezdxf.lldxf.types import *
from ezdxf.entities.boundary_paths import *
from ezdxf.addons import odafc
import json
import time
import os
import io,random


# export interface I_EzdxfJsonData
# {
#     modelspace : I_EzdxfLayout;
#     blocks ?: {[key:string]:I_EzdxfLayout};
#     layers ?: {[key:string]:I_EzdxfAttribs};
#     view_center ?: Vector3Meta;
#     view_radius ?: number;
#     complete ?: boolean;
#     layer_name_dict ?: {[key:string]:string};
# }
# export type EzdxfType = "LINE"|"INSERT"|"LWPOLYLINE"|"CIRCLE"|"ARC"|"HATCH"|"SPLINE"|"MTEXT"|"TEXT"|"DIMENSION"|"ELLIPSE";
# export interface I_EzdxfAttribs
# {
#     layer ?: string;
#     color ?: number;
#     ltscale ?: number;
#     start_param ?: number;
#     end_param ?: number;
#     ratio ?: number;
#     start ?: number[];
#     start_v3?: Vector3;
#     end ?: number[];
#     end_v3?:Vector3;
#     center ?: number[];
#     center_v3?:Vector3;
#     major_axis ?: number[];
#     major_axis_v3 ?: Vector3;
#     extrusion ?: number[];
#     extrusion_v3?:Vector3;
#     flags ?: number;
#     text ?: string;
#     name?: string;
#     insert?: number[];
#     insert_v3?:Vector3;
#     xscale?:number;
#     yscale?:number;
#     rotation?: number;
#     char_height ?:number;
#     radius?:number;
#     start_angle?:number;
#     end_angle?:number;
#     attachment_point?:number;
#     is_layer_selected?:boolean;
#     is_entity_selected?:boolean;
#     std_layer ?: string;
# }
# export interface I_EzdxfEntity
# {
#     type : EzdxfType;
#     attribs : I_EzdxfAttribs;
#     lwpoints ?: number[][];
#     text ?: string;
#     lwpoints_v3 ?: Vector3[];
#     control_points ?: number[][];
#     knots ?: number[];
#     _spline_drawing_points_v3 ?: Vector3[];

#     _bbox3?:Vector3;
#     _entity_points ?: Vector3[];
#     [key:string]:any;
# }

# export interface I_EzdxfLayout
# {
#     name : string;
#     entities : I_EzdxfEntity[];

#     _bbox_width ?: number;
#     _bbox_height ?: number;
# }
class EzdxfExporter:
    def __init__(self):
        pass
    def init_doc(self):
        doc = ezdxf.new(dxfversion='R2010')
        # doc.units = units.MM

        # doc.header['$INSUNITS'] = units.MM

        return doc
    def json2dwg(self,data,filename="test"):
        doc = self.json2doc(data=data)
        dirname = os.path.dirname(__file__)
        save_filename = os.path.join(dirname,filename+".dwg")
        try:
            odafc.export_dwg(doc=doc,filename=save_filename,replace=True,audit=True)
        except odafc.ODAFCError as e:
            print(e)
        try:
            fp = open(save_filename, 'rb')
            binary_data = fp.read()
            # 尝试使用latin-1解码，如果失败打印异常
            # base64_string = binary_data.decode('utf-8')
            base64_string = binary_data.decode('latin-1')
            return base64_string
        except Exception as e:
            print(e)

    def json2doc(self,data):
        doc = self.init_doc()
        self.parseJson(doc=doc,data=data)
        return doc
    
    def parseJson(self,doc:Drawing,data):
        model_space = doc.modelspace()

        if not "modelspace" in data:
            return False
        if not "blocks" in data:
            return False

        if "layers" in data:
            layers = data['layers']
            for layer_name in layers:
                doc.layers.new(layer_name,dxfattribs=layers[layer_name])

        modelspace_data = data['modelspace']
        blocks_data = data['blocks']


        for key in blocks_data:
            block_data = blocks_data[key]
            blk = doc.blocks.new(name=key)
            entities = block_data['entities']
            for entity in entities:
                self.parseEntity(msp=blk,entity=entity)

        entities = modelspace_data['entities']
        for entity in entities:
            self.parseEntity(msp=model_space,entity=entity)
        




        


    def parseEntity(self, msp:Layout,entity):
        if entity['type'] == "LWPOLYLINE":
            self.parseLWPolyline(msp,entity)
        if entity['type'] == "INSERT":
            self.parseInsert(msp,entity)
        if entity['type'] == "MTEXT":
            self.parseMText(msp,entity)


    def parseInsert(self,msp:Layout, ele):
        attribs = ele["attribs"]
        insert = ele['insert']
        name = attribs['name']
        # t_attribs = {'xscale':attribs['xscale'],'yscale':attribs['yscale']}
        msp.add_blockref(name=name,insert=insert,dxfattribs=attribs)

    def parseLine(self,msp:Layout,ele):
        p0 = ele["p0"]
        p1 = ele["p1"]
        # print(p0,p1)
        attribs = ele["attribs"]

        msp.add_line(start=(float(p0[0]),float(p0[1])),end=(float(p1[0]),float(p1[1])),dxfattribs=attribs)
  
  
    def parseLWPolyline(self,msp:Layout,ele):
        points = ele["lwpoints"]
        attribs = ele["attribs"]
        # print(points)

        t_points = []
        for p in points:
            t_points.append([float(p[0]),float(p[1])])

        msp.add_lwpolyline(points=t_points,dxfattribs=attribs)

    def parseMText(self,msp:Layout, ele):
        point = ele["insert"]
        attribs = ele['attribs']
        text = ele['text']
        msp.add_mtext(text, dxfattribs=attribs).set_location(point,attachment_point=5)

    def parseLinearDim(self,msp:Layout,ele):
        base = ele["base"]
        p1 = ele["p1"]
        p2 = ele["p2"]
        angle = ele['angle']
        dimstyle = ele['dimstyle']
        msp.add_linear_dim(base,p1,p2,angle=angle)
        
    def parseAlignedDim(self,msp:Layout,ele):
        p1 = ele["p1"]
        p2 = ele["p2"]
        distance = ele["distance"]
        text_pos = ele['text_pos']
        dim = msp.add_aligned_dim(p1,p2,distance)
        if len(text_pos) > 1:
            dim.set_location(text_pos,True,False)
            dim.render(None,True)
        pass
    def parseRadiusDim(self, msp:Layout,ele):
        center = ele['center']
        angle = ele['angle']
        radius = ele['radius']
        
        dim = msp.add_radius_dim(center,angle=angle,radius=radius,override={"dimtofl":0})
        if "location" in ele:
            location = ele["location"]
            dim.set_location(location=location)
        dim.render()
        
    def parseArc(self,msp:Layout,ele):
        p0 = ele['center']
        radius = ele['radius']
        start_angle = ele['startAngle']
        end_angle = ele['endAngle']
        options = ele['options']
        msp.add_arc(p0,radius,start_angle,end_angle,dxfattribs=options)


if __name__ == '__main__':
    exporter = EzdxfExporter()
    dirname = os.path.dirname(__file__)
    fp = open(os.path.join(dirname,"ezdxf_test.json"),"r",encoding='utf-8')
    data = json.load(fp=fp)
    fp.close()
    doc = exporter.json2doc(data=data)

    # doc.saveas(os.path.join(dirname,"test.dxf"))
    filename = os.path.join(dirname,"test.dwg")
    odafc.export_dwg(doc=doc,filename=filename,replace=True,audit=True)
    # print(data)