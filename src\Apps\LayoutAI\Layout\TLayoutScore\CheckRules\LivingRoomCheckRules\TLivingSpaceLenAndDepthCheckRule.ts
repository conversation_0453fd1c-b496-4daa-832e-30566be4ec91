import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomSplitByPathClassfierCheckRule } from "./TLivingRoomSplitByPathClassfierCheckRule";

export class TLivingSpaceLenAndDepthCheckRule extends TLivingRoomSplitByPathClassfierCheckRule
{
    private minLivingLen: number = 1800;
    private minLivingDepth: number = 2300;
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        void(room);
        void(figure_element);
        void(main_figures);

        let score: number = 0;
        
        if(this.diningSpaceRect)
        {
            let diningLen: number = this.diningSpaceRect.length;
            let diningDepth: number = this.diningSpaceRect.depth;
            if(diningLen < this.minLivingLen && diningDepth < this.minLivingDepth)
            {
                score -= 30;
            }
        }
        return {score: score};
    }
}