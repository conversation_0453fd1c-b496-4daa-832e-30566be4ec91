import { Vector3 } from "three";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";

import { TPainter } from "../../Drawing/TPainter";
import { LightRuleService } from "../../Scene3D/light/rule/LightRuleService";
import { CategoryName, SwitchConfig, UserDataKey } from "../../Scene3D/NodeName";
import { ZPolygon, ZRectShapeType } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { cloneSectionData, DefaultCeilingLayerTemplate, DefaultCeilingType, GenCeilingLayerMethod, I_CeilingLayerData, I_LightSlotData, I_RoomSubAreaSimpleData } from "../IRoomInterface";
import { I_FigureElement, TFigureElement } from "../TFigureElements/TFigureElement";
import { LayoutAI_Configs } from "./configures/LayoutAIConfigs";
import { I_CeilingLayerEntity } from "./I_CeilingLayerEntity";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TFillLightEntity } from "./TFillLightEntity";
import { TRoomEntity } from "./TRoomEntity";
import { CeilingConfigReader } from "../TLayoutGraph/TGraphConfigs/CeilingConfigReader";


/**
* @description 吊顶的层
* <AUTHOR>
* @date 2025-05-13 16:39:43
* @lastEditTime 2025-05-13 16:39:43
* @lastEditors wangsuiwei
*/

export class TCeilingLayerEntity extends TBaseEntity {
    protected method: GenCeilingLayerMethod;
    protected offset_value?: number;
    protected offset_func?: string;
    protected _drawing_polygon?: ZPolygon;
    private _roomEntity: TRoomEntity;

    /**
     *  离顶高度
     */
    zvalToTop: number;

    zvalToTopFunc?: string;

    /**
     *  四周高度
     */
    aroundToTop?: number;

    private _ceiling_type?: string;

    private _lightSlotData?: I_LightSlotData;


    protected downLightsData?: I_FigureElement[];

    private _children: TCeilingLayerEntity[];


    private _down_lights?: TFigureElement[];
    /**
     *  吊顶绑定的figure_elment, 兼容过去的吊顶
     */
    private _ceiling_figure_element: TFigureElement;

    private _parent_layer: TCeilingLayerEntity = null;

    private _back_light_entities: TFillLightEntity[] = [];

    protected _stroke_style: string;
    constructor(parent_layer: TCeilingLayerEntity, ceiling_figure: TFigureElement = null) {
        // 不用自动增加UidN
        super(null, -1);
        this.ceiling_figure_element = ceiling_figure || new TFigureElement();

        this.type = "CeilingLayer";
        this.realType = "CeilingLayer";
        this.children = [];
        this._parent_layer = parent_layer;
        this.zvalToTop = 0;
        this._roomEntity = null;
        this._down_lights = [];
        this._ceiling_type = "";
    }
    public get ceiling_figure_element(): TFigureElement {
        return this._ceiling_figure_element;
    }
    public set ceiling_figure_element(value: TFigureElement) {
        this._ceiling_figure_element = value;
        this._rect = this._ceiling_figure_element.rect;
        this._polygon = this._ceiling_figure_element._polygon;
    }

    public get ceilingType(): string {
        return this._ceiling_type;
    }

    get roomEntity(): TRoomEntity {
        return this._roomEntity;
    }
    set roomEntity(value: TRoomEntity) {
        this._roomEntity = value;
    }
    get parent_layer(): TCeilingLayerEntity {
        return this._parent_layer;
    }
    protected set parent_layer(value: TCeilingLayerEntity) {
        this._parent_layer = value;
    }
    get children(): TCeilingLayerEntity[] {
        return this._children || [];
    }
    set children(value: TCeilingLayerEntity[]) {
        this._children = value || [];
    }
    get down_lights(): TFigureElement[] {
        return this._down_lights;
    }
    protected set down_lights(value: TFigureElement[]) {
        this._down_lights = value;
    }

    /**
     *  吊顶面-离地高
     */
    get layerZVal() {
        return (this.roomEntity.storey_height || 2800) - (this.zvalToTop || 0);
    }


    get layerPolygon() {
        return this.polygon || this.rect;
    }

    get lightSlotData(): I_LightSlotData {
        return this._lightSlotData;
    }
    set lightSlotData(value: I_LightSlotData) {
        this._lightSlotData = value;
    }
    importData(data: I_CeilingLayerEntity): void {
        super.importData(data);

    }

    loadCeilingLayerData(data: I_CeilingLayerData) {
        this._ceiling_type = data.ceiling_type || data.name;
        this.offset_func = data.offset_func;
        this.offset_value = data.offset_value;
        if (data.drawing_points) {
            this._drawing_polygon = new ZPolygon();
            this._drawing_polygon.initByVertices(data.drawing_points);
        }
        this._stroke_style = data.strokeStyle || null;
        this.method = data.method;
        this.zvalToTop = data.zvalToTop || 0;
        this.zvalToTopFunc = data.zvalToTopFunc;
        this.aroundToTop = data.aroundToTop || 400;
        this.children = [];
        let scope = this;
        if (data.children) {
            data.children.forEach((c_data) => {
                let entity = new TCeilingLayerEntity(scope);
                // 子节点继承父节点的吊顶类型
                c_data.ceiling_type = this._ceiling_type;
                entity.loadCeilingLayerData(c_data);
                entity._ceiling_type = c_data.ceiling_type;
                this.children.push(entity);
            });
        }
        if (data.lightSlotData) {
            this._lightSlotData = { ...data.lightSlotData };
            if (data.lightSlotData) {
                if (data.lightSlotData.section_data) {
                    this._lightSlotData.section_data = cloneSectionData(data.lightSlotData.section_data);
                }
            }
        }
    }

    /**
     * 
     * @param data 
     */
    importSubAreaSimpleData(data: I_RoomSubAreaSimpleData, force_update: boolean = false) {
        if (data.area_rect) {
            this.rect.importRectData(data.area_rect);
        }
        else if (data.area_points) {
            this.rect.copy(ZRect.computeMainRect(new ZPolygon().initByVertices(data.area_points)));
        }

        if (data.ceiling_layer_data) {
            this.loadCeilingLayerData(data.ceiling_layer_data);
            this.update();
        }
        else if (data.ceiling_type) {
            if (this._ceiling_type !== data.ceiling_type || force_update) {
                let config = CeilingConfigReader.readConfig(data.ceiling_type);
                if (config) {
                    this._ceiling_type = data.ceiling_type;
                    this.loadCeilingLayerData(config);
                    this.update();
                }
            }
        }
    }

    exportSubAreaSimpleData(index:number=0, export_children:boolean =false):I_RoomSubAreaSimpleData
    {
        let layerEntity = this;
        let room = layerEntity.roomEntity?._room;
        let name = layerEntity.ceiling_figure_element?.sub_category || '';
        if (name === '吊顶' || '') {
          name = room.aliasName!=="未命名"?room.aliasName:room.name;
        }
        return {
          uuid: layerEntity._uuid,
          room_uuid: room.uuid,
          index: index,
          area_points: layerEntity.layerPolygon.positions.map(p => {
            return { x: p.x, y: p.y, z: p.z };
          }),
          area_rect: layerEntity.rect.exportRectData(),
          name: name,
          ceiling_type: layerEntity.ceilingType,
          sub_ceiling_area_rect : layerEntity.children[0]?layerEntity.children[0].rect.exportRectData():null,
          sub_ceiling_area_points : layerEntity.children[0]?layerEntity.children[0].layerPolygon.positions.map(p=>{return {x:p.x,y:p.y,z:p.z}}):null,
        };
    }

    get lightSlot_Section() {
        return this._lightSlotData?.section_data || null;
    }
    getAllDownLights() {
        let down_lights: TFigureElement[] = [];
        down_lights.push(...this.down_lights);
        for (let child of this.children) {
            down_lights.push(...child.getAllDownLights());
        }
        return down_lights;
    }
    /**
     * 绑定筒灯
     * @param down_light  
     */
    bindDownLight(down_light: TFigureElement): TCeilingLayerEntity {
        let polygon = this.layerPolygon;
        if (!polygon) return null;
        if (!polygon.containsPoint(down_light.rect.rect_center)) return null;
        // 默认是自己
        let result: TCeilingLayerEntity = this;
        for (let child of this.children) {
            let child_target = child.bindDownLight(down_light);
            //  如果有儿子, 就绑定在儿子上
            if (child_target) {
                result = child_target;
                break;
            }
        }
        return result;
    }
    updateBackLight() {
        this._down_lights.forEach((ele) => {
            if (ele.category !== CategoryName.DownLight) {
                let rect = ele.rect;
                let light = {
                    "type": "LightFillPlate",
                    "lightBrightness": 1000,
                    "color": '0xffffff',
                    "posX": rect.rect_center_3d.x,
                    "posY": rect.rect_center_3d.y,
                    "posZ": rect.zval - 1,
                    "rotateX": 0,
                    "rotateY": 0,
                    "rotateZ": rect.rotation_z,
                    "length": rect.w,
                    "width": rect.h,
                }
                let entity = LightRuleService.createLightEntityByData(light);
                entity.name = "灯具背光";
                entity.update3D();
                (LayoutAI_App.instance as TAppManagerBase).layout_container.addFillLightEntity(entity);
                this._back_light_entities.push(entity);
            }
        });

        for (let child of this.children) {
            child.updateBackLight();
        }
    }
    cleanDownLights() {
        // this._down_lights.forEach((ele)=>ele.dispose3d());
        this._down_lights.length = 0;

        this._back_light_entities.forEach((ele) => {
            (LayoutAI_App.instance as TAppManagerBase).layout_container.removeFillLightEntity(ele);
        })
        this._back_light_entities.length = 0;

        for (let child of this.children) {
            child.cleanDownLights();
        }
    }

    // 获取当前节点下（包括子节点）所有筒灯
    public getDownLights(): TFigureElement[] {
        let down_lights: TFigureElement[] = [];
        down_lights.push(...this.down_lights);
        for (let child of this.children) {
            down_lights.push(...child.getDownLights());
        }
        return down_lights;
    }

    protected _updateZvalToTop() {
        if (this.zvalToTopFunc) {
            try {

                let inputData: { [key: string]: number } = {
                    maxCeilingHeight: this.roomEntity.ceiling_height,
                    storeyHeight: this.roomEntity.storey_height
                }
                let code = '';
                for (let key in inputData) {
                    code += `let ${key} = arg['${key}'] ?? 0; `;
                }
                code += `return  ${this.zvalToTopFunc};`;

                let val = new Function('arg', code)(inputData);

                this.zvalToTop = val;

            } catch (error) {

            }
        }
        return this.zvalToTop;

    }
    update(): void {
        super.update();
        if (this._parent_layer) {
            let parent_poly = this._parent_layer.layerPolygon;
            if (!parent_poly) return null;
            this._roomEntity = this._parent_layer._roomEntity;
            if (this.method === "Offset") {
                let val = this.offset_value || 0;

                let current_poly = parent_poly.clone().expandPolygon(-val);

                if ((parent_poly as ZRect).IsZRect) {
                    let parent_rect = parent_poly as ZRect;
                    this.rect.copy(ZRect.fromPoints(current_poly.positions, parent_rect.nor));
                }
                else {
                    if (!this.polygon) {
                        this._polygon = new ZPolygon();
                        if (this._ceiling_figure_element) {
                            this._ceiling_figure_element._polygon = this.polygon;
                        }
                    }
                    this.polygon.initByVertices(current_poly.positions);
                }
            }
        }
        this._updateZvalToTop();
        if (this.children) {
            for (let child of this.children) {
                child.update();
            }
        }
    }


    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (this.children) // 先画儿子
        {
            this.children.forEach((entity) => entity.drawEntity(painter, entity));
        }
        if (LayoutAI_Configs.Configs?.is_drawing_ceiling_lines_in_2d) {
            painter.strokeStyle = this._stroke_style || "#f33";
            painter.fillStyle = "#aaa";
            let layerPolygon = this.layerPolygon;
            if (layerPolygon) {
                if (this.method === "Rect") {
                    painter.fillPolygons([layerPolygon], 0.05);
                }
                painter.strokePolygons([layerPolygon]);
            }
        }

        if (LayoutAI_Configs.Configs?.drawing_down_lights_in_ceiling && this.down_lights) {
            this.down_lights.forEach((ele) => {
                ele.drawFigure(painter, false);
            })
        }
    }

    public getLampStripZVal(h: number): number {
        let zval = h;
        if (this._parent_layer) {
            zval = this._parent_layer.layerZVal;
        }
        zval -= this.zvalToTop;
        // 抬高一定距离，避免跟灯槽重合
        let offset = 20;
        return zval + offset;
    }

    public createLampStrip(down_light: { materialId: string, color: number, brightness: number }): TFigureElement[] {
        let stripLamps: TFigureElement[] = [];
        if (this.children) {
            for (let child of this.children) {
                stripLamps.push(...child.createLampStrip(down_light));
            }
        }

        if (!this._lightSlotData) {
            return stripLamps;
        };
        // 灯带固定大小
        let depth = 10; // down_light.depth || (this.lightSlotData.extrude_length || 50) * 0.4;
        let h = 10; // down_light.height || (this.lightSlotData.extrude_height || 20);
        let zval = this.getLampStripZVal(h);
        let slotWidth = (this.lightSlotData.extrude_length || 50) * 0.6;

        let polygon = this.layerPolygon;
        if (!polygon) return;
        for (let edge of polygon.edges) {
            let w = edge.length;
            // 避免漏光
            w = Math.max(w - 100, 1);
            let h = depth;
            let rect = new ZRect(w, h);
            rect.zval = zval;
            rect.nor = edge.nor;
            let mid = new Vector3(edge.center.x, edge.center.y, zval);
            let dir = this._ceiling_type == DefaultCeilingType.OverhangingEdge ? -0.5 : 0.5;
            let pos = mid.add(edge.nor.clone().multiplyScalar((slotWidth) * dir));
            rect.rect_center_3d = pos;;
            let figure_ele = TFigureElement.createSimple(CategoryName.LightStrip);
            figure_ele.depth = edge.length;
            figure_ele.length = depth;
            figure_ele.height = h;
            figure_ele.rect = rect.clone();
            figure_ele._is_decoration = true;
            figure_ele._rect_shape = ZRectShapeType.Rect;
            figure_ele._matched_material = figure_ele.makeMaterialItemByMaterialId(down_light.materialId);
            figure_ele._matched_rect = rect.clone();
            figure_ele.updateMesh3D();

            if (SwitchConfig.useFillLight) {
                let entity = this._createFillLight(rect, h, down_light.color, down_light.brightness);
                entity.name = "吊顶灯槽灯带";
                figure_ele._simple_mesh3D.userData[UserDataKey.EntityOfMesh] = entity;
                entity.setLightColor(down_light.color);
                entity.setLightBrightness(down_light.brightness);
            }

            this.down_lights.push(figure_ele);
            stripLamps.push(figure_ele);
        }
        return stripLamps;
    }

    private _createFillLight(rect: ZRect, h: number, color: number, brightness: number): TFillLightEntity {
        let light = {
            "type": "LightFillPlate",
            "lightBrightness": brightness,
            "color": color.toString(),
            "posX": rect.rect_center_3d.x,
            "posY": rect.rect_center_3d.y,
            "posZ": (rect.zval + h + 1),
            "rotateX": 0,
            "rotateY": 0,
            "rotateZ": rect.rotation_z,
            "length": rect.length,
            "width": rect.depth,
        };
        let entity = LightRuleService.createLightEntityByData(light);
        entity.update3D();
        (LayoutAI_App.instance as TAppManagerBase).layout_container.addFillLightEntity(entity);
        return entity;
    }

    public exportCeilingData(): any {
        let data = {
            ceiling_type: this._ceiling_type,
            offset_value: this.offset_value || 0,
            zvalToTop: this.zvalToTop,
            layerZVal: this.layerZVal,
            downLights: this.down_lights.map((light) => light.uuid),
            layerPolygon: this.layerPolygon.exportData(),
            children: this.children.map((child) => child.exportCeilingData()),
        }
        return data;
    }
}
