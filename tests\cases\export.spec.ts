import { test } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import { clickToSave, openAnyScheme, matchSets,searchAndOpen } from '../utils/basicProcess';
import { compareScreenshot, takeScreenshot } from '../utils/screenshotUtils';

test.use({ ignoreHTTPSErrors: true });

test.describe('导出方案', () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('导出布局图', async ({ page }) => {
    // 1.点击首页中的户型库，打开任意户型
    await openAnyScheme(page, designPage);
    await page.waitForTimeout(5000);
    // 2.点击顶菜保存按钮，保存方案弹框输入方案名称后点击保存按钮
    await clickToSave(page, designPage);
    // 3.点击顶菜导出按钮，选择导出布局图
    await designPage.clickTopMenuAppointedItem('导出', '布局图');
    await page.waitForTimeout(3000);
    await takeScreenshot(page, 'export', 'layout-graph.png');
  });

  test('导出彩平图', async ({ page }) => {
    await matchSets(page, designPage);
  
    await page.waitForTimeout(5000);
    await clickToSave(page, designPage);
    await designPage.clickTopMenuAppointedItem('导出', '彩平图');

    await page.waitForTimeout(3000);
    await takeScreenshot(page, 'export', 'color-graph.png');
  });

  test('保存方案', async ({ page }) => {
    // 1.点击首页中的户型库，打开任意户型
    await openAnyScheme(page, designPage);
    await page.waitForTimeout(5000);
    // 2.点击顶菜保存按钮，保存方案弹框输入方案名称后点击保存按钮
    await clickToSave(page, designPage);
    await page.waitForTimeout(3000);
    await takeScreenshot(page, 'export', 'save.png');
  });
});
