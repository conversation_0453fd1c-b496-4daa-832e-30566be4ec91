"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[2893],{62893:function(e,n,t){t.r(n),t.d(n,{default:function(){return H}});var o=t(13274),r=t(41594),a=t(33100),i=t(77320),s=t(36134),c=t(3727),u=t(17287),l=t(27347),h=t(88934),f=t(98612),m=t(49063),d=t(31281),p=t(9003),b=t(15696),v=t(41980),y=t(19356),S=t(25629),g=t(22640);function x(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}function w(){var e=x(["\n          position: fixed;\n          bottom: 10px;\n          right: 250px;\n          display: flex;\n          align-items: center;\n          @media screen and (max-height: 600px){\n            bottom: 30px !important;\n          }\n          .ant-select-selector\n          {\n            background: #EFEFEF !important;\n            border: none !important;\n            box-shadow: none !important;\n          }\n        "]);return w=function(){return e},e}function j(){var e=x(["\n            height: 24px;\n            border: 1px solid #DDDFE4;\n            border-right-width: 0px;\n            border-top-left-radius: 50%;\n            border-bottom-left-radius: 50%;\n            font-size: 18px;\n            cursor: pointer;\n        "]);return j=function(){return e},e}function _(){var e=x(["\n            height: 24px;\n            border: 1px solid #DDDFE4;\n            border-left-width: 0px;\n            border-top-right-radius: 50%;\n            border-bottom-right-radius: 50%;\n            font-size: 18px;\n            cursor: pointer;\n        "]);return _=function(){return e},e}function A(){var e=x(["\n            font-size: 26px !important;\n            margin-right: 10px;\n            cursor: pointer;\n        "]);return A=function(){return e},e}var C=(0,t(79874).rU)((function(e){var n=e.css;return{focusBar:n(w()),zoomOut:n(j()),zoomIn:n(_()),focusIcon:n(A())}})),D=t(11164),I=t(27164);function E(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function O(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var o,r,a=[],i=!0,s=!1;try{for(t=t.call(e);!(i=(o=t.next()).done)&&(a.push(o.value),!n||a.length!==n);i=!0);}catch(e){s=!0,r=e}finally{try{i||null==t.return||t.return()}finally{if(s)throw r}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return E(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return E(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var k=function(){var e=C().styles,n=O((0,r.useState)(100),2),t=n[0],a=n[1];return(0,r.useEffect)((function(){l.nb.DispatchEvent(l.n0.setFocus,{focus:t})}),[t]),(0,o.jsxs)("div",{className:e.focusBar,children:[(0,o.jsx)(I.A,{className:e.focusIcon,iconClass:"iconhuizhong",onClick:function(){a(100),l.nb.DispatchEvent(l.n0.setFocus,{focus:100})}}),(0,o.jsx)("button",{className:e.zoomOut,onClick:function(){t<=25||a((function(e){return e-25}))},children:"-"}),(0,o.jsxs)(D.A,{size:"small",style:{width:70,background:"#DDDFE4"},value:t+"%",onChange:function(e){e>175||e<100||a(Number(e))},children:[(0,o.jsx)("option",{value:100,children:"100%"}),(0,o.jsx)("option",{value:125,children:"125%"}),(0,o.jsx)("option",{value:150,children:"150%"}),(0,o.jsx)("option",{value:175,children:"175%"})]}),(0,o.jsx)("button",{className:e.zoomIn,onClick:function(){t>=200||a((function(e){return e+25}))},children:"+"})]})},M=t(85783),N=t(26966),L=t(51010),U=t(90974),R=t(61928),z=t(23825);function P(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function F(e,n,t,o,r,a,i){try{var s=e[a](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(o,r)}function T(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var o,r,a=[],i=!0,s=!1;try{for(t=t.call(e);!(i=(o=t.next()).done)&&(a.push(o.value),!n||a.length!==n);i=!0);}catch(e){s=!0,r=e}finally{try{i||null==t.return||t.return()}finally{if(s)throw r}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return P(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return P(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(e,n){var t,o,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(t=1,o&&(r=2&s[0]?o.return:s[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,s[1])).done)return r;switch(o=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(e){s=[6,e],o=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}var H=(0,b.observer)((function(){var e=(0,M.B)().t,n=(0,c.A)().styles,t=T((0,r.useState)({opening:!1,title:""}),2),b=t[0],x=(t[1],T((0,r.useState)(!1),2)),w=x[0],j=x[1],_=T((0,r.useState)(!1),2),A=_[0],C=_[1],D=T((0,r.useState)(0),2),I=(D[0],D[1]),E=T(a.A.useMessage(),2),O=E[0],P=(E[1],(0,r.useRef)(),T((0,r.useState)(!1),2)),H=(P[0],P[1]),X=T((0,r.useState)(!1),2),Y=X[0],q=X[1],K=T((0,r.useState)(!1),2),$=(K[0],K[1],T((0,r.useState)(null),2)),G=$[0],V=$[1],W=(0,m.Zp)();i.A.confirm;l.nb.UseApp(f.e.AppName);var Z=(0,r.useRef)(null),J=(0,p.P)(),Q=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("cad_canvas")),l.nb.instance.update())},ee=function(){var e,n=(e=function(e){return B(this,(function(e){return j(!1),setTimeout((function(){W("/design?importType=importHouse&&from=dreamer")}),100),[2]}))},function(){var n=this,t=arguments;return new Promise((function(o,r){var a=e.apply(n,t);function i(e){F(a,o,r,i,s,"next",e)}function s(e){F(a,o,r,i,s,"throw",e)}i(void 0)}))});return function(e){return n.apply(this,arguments)}}();(0,r.useEffect)((function(){l.nb.instance.bindCanvas(document.getElementById("cad_canvas")),window.addEventListener("resize",Q),Q(),l.nb.instance&&(l.nb.t=e,l.nb.instance.initialized||(l.nb.instance.init(),l.nb.instance._app_route="Dreamer",l.nb.RunCommand(f.f.AiCadMode),l.nb.emit(h.U.OnPreparingHandle,{title:"正在载入应用数据...",opening:!0}),l.nb.instance.prepare().then((function(){l.nb.emit(h.U.OnPreparingHandle,{title:"正在载入应用数据...",opening:!1});l.nb.instance;l.nb.IsDebug||l.nb.emit(h.U.OpenHouseSearching,!0)})),l.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),l.nb.instance.update());l.nb.on(h.U.setIssueReportVisible,H),l.nb.on(h.U.ShowSubHandlerBtn,C),d.A.BasicBiz.Room.bindOnOpenSchemeFinish(ee),l.nb.on(h.U.SwitchIntoDesign,ee),l.nb.on_M(h.U.RoomList,"roomList3",(function(e){J.homeStore.setRoomInfos(e)})),l.nb.on_M(h.U.SelectingRoom,"cad_dreamhouse_moble_home",(function(e){setTimeout((function(){J.homeStore.setSelectData({rooms:null==e?void 0:e.current_rooms,clickOnRoom:!0})}),20),J.homeStore.setCurrenScheme(null==e?void 0:e.event_param)})),l.nb.on(h.U.PerformFurnishResult,(function(e){"error"===e.progress&&a.A.error(e.message)})),l.nb.on(h.U.OpenMySchemeList,(function(){J.homeStore.setShowMySchemeList(!0)})),l.nb.on(h.U.ShowDreamerPopup,(function(e){J.homeStore.setShowDreamerPopup(e)})),l.nb.on(h.U.SaveProgress,(function(n){"success"===n.progress?(a.A.success(e("布局方案保存成功")),"autoExit"===J.homeStore.isAutoExit&&(R.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=z.O9),J.homeStore.setIsAutoExit("")):"fail"===n.progress?a.A.error(e("布局方案保存失败")):"ongoing"===n.progress&&a.A.loading(e("正在保存布局方案"))})),window.addEventListener("message",(function(e){e.data&&"dreamer.api"===e.data.origin&&"checkClose"===e.data.type&&(0==l.nb.instance.layout_container._room_entities.length?window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"):null==l.nb.instance.layout_container._layout_scheme_id?(q(!1),J.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:""})):(l.nb.DispatchEvent(l.n0.SaveLayoutScheme,null),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*")))}))}),[J.homeStore.isAutoExit]);var ne=[{key:"",label:"",icon:[""],children:(0,o.jsx)(u.Wx,{})}];return(0,o.jsxs)("div",{className:n.root,children:[(0,o.jsx)(u.pF,{handler:function(n){if(n===l._I.SaveMyLayoutScheme)0==l.nb.instance.layout_container._room_entities.length?O.open({key:"SaveSchemeProgress",type:"error",content:e("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):null==l.nb.instance.layout_container._layout_scheme_id?(q(!1),J.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"})):l.nb.DispatchEvent(l.n0.SaveLayoutScheme,null)},title:G?" 【"+G+"】":""}),(0,o.jsx)("div",{className:n.side_pannel,id:"side_pannel",children:(0,o.jsx)(v.I5,{items:ne,contentClassName:n.left_content})}),(0,o.jsxs)("div",{id:"Canvascontent",className:n.content,children:[(0,o.jsxs)("div",{ref:Z,id:"body_container",className:n.canvas_pannel,children:[(0,o.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){J.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){J.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,o=Math.sqrt(n*n+t*t);J.homeStore.setInitialDistance(o/J.homeStore.scale),I(J.homeStore.scale)}},onTouchMove:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,o=Math.sqrt(n*n+t*t)/J.homeStore.initialDistance;o>5?o=5:o<.05&&(o=.05);J.homeStore.setScale(o),l.nb.DispatchEvent(l.n0.scale,o)}},onTouchEnd:function(){J.homeStore.setInitialDistance(null),I(J.homeStore.scale)}}),(0,o.jsx)("div",{className:"canvas_btns",children:A?(0,o.jsx)(s.A,{className:"btn",type:"primary",onClick:function(){var e,n;l.nb.RunCommand(l._I.LeaveSubHandler),"厨房"===(null===(n=J.homeStore)||void 0===n||null===(e=n.selectedRoom)||void 0===e?void 0:e.roomname)?l.nb.RunCommand(l._I.AcceptLeaveSubHandler):l.nb.DispatchEvent(l.n0.leaveSingleRoomLayout,{})},disabled:b.opening||w,children:e("完 成")}):(0,o.jsx)(U.A,{disabled:b.opening||w})})]}),(0,o.jsx)(u.Nt,{})]}),J.homeStore.showDreamerPopup&&(0,o.jsx)(g.A,{}),(0,o.jsx)(u.ti,{}),(0,o.jsx)(L.A,{}),(0,o.jsx)(u.RU,{}),(0,o.jsx)(y.A,{schemeNameCb:function(e){V(e)}}),(0,o.jsx)(k,{}),J.homeStore.showReplace&&(0,o.jsx)(N.A,{}),J.homeStore.showSaveLayoutSchemeDialog.show&&(0,o.jsx)("div",{className:n.overlay,children:(0,o.jsx)(S.A,{schemeName:G,closeCb:function(){J.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:""})},isSaveAs:Y})})]})}))}}]);