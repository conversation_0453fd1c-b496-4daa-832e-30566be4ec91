import { Vector3 } from "three";
import { WPolygon } from "../../Layout/TFeatureShape/WPolygon";
import { TLayoutEntityContainer } from "../../Layout/TLayoutEntities/TLayoutEntityContainter";
import { TBaseSpaceEntity } from "../../Layout/TLayoutEntities/TSpaceEntities/TBaseSpaceEntity";
import { TRoomShape } from "../../Layout/TRoomShape";
import { ZRect } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { I_SplitSpaceItem as I_SplitSpaceItem, I_SwjWindow } from "../../AICadData/SwjLayoutData";
import { BaseSpaceType } from "../../Layout/IRoomInterface";
import { TWindowDoorEntity } from "../../Layout/TLayoutEntities/TWinDoorEntity";
import { TBaseEntity } from "../../Layout/TLayoutEntities/TBaseEntity";
import { HotelSpaceRoomLayoutService } from "./HotelSpaceRoomLayoutService";
import { ZPolygon } from "@layoutai/z_polygon";

const MaxRect = "MaxRect";
export interface I_SpaceTypeConfig
{
    name: string;
    defaultWidth: number;
    defaultDepth ?:number;
    minWidth ?: number;
    minDepth ?: number;
    maxDepth ?: number;
    minArea ?: number;
    ratioWeight ?: number;
    targetCount ?: number;
    
}

export interface I_SplitSpaceMethodConfigs
{
    /**
     *  最小长度
     */
    min_length ?: number;

    /**
     *  最大长度
     */
    max_length ?: number;

    default_target_length ?: number;

    /**
     *  
     */
    min_depth ?: number;

    max_depth ?: number;


    /**
     *  期望的长度列表: score_weight默认是1
     */
    expected_length_list ?: {length:number, max_differ?:number, score_weight?:number}[];

    expected_pos_list ?: {interval:number[], score_weight?:number}[];

    unexpected_pos_list ?: {interval:number[],score_weight?:number}[];

    default_length_score_weight ?: number;

    default_pos_expected_score_weight ?: number;

    default_pos_unexpected_score_weight ?: number;
    

    border_thickness ?: number;

    /**
     *  默认的最小gap
     */
    default_min_gap ?: number;

    default_gap ?: number;
}
export class BaseSpaceService 
{
    private static _instance : BaseSpaceService = null;
    private _container : TLayoutEntityContainer = null;

    private _splitMethodConfigs : I_SplitSpaceMethodConfigs;
    private _spaceTypeConfigs: I_SpaceTypeConfig[];

    constructor()
    {
        this._splitMethodConfigs = {
            min_length : 3000,
            max_length : 8000,
            min_depth : 7000,
            max_depth : 10000,
            default_target_length : 4000,
            default_min_gap : 900,
            default_gap : 1800,
            default_length_score_weight : 1,
            default_pos_expected_score_weight : 2,
            default_pos_unexpected_score_weight : -2,
            expected_length_list : [{
                length : 4000,
                max_differ : 450,
            },{
                length : 4500,
                max_differ : 450,
            },{
                length : 5000,
                max_differ : 450
            }],
            expected_pos_list : [],
            unexpected_pos_list : [],
            border_thickness : 120
        }
        this._spaceTypeConfigs =[];
        this.initSpaceNameConfigs();
    }
    static get instance()
    {
        if(!BaseSpaceService._instance)
        {
            BaseSpaceService._instance = new BaseSpaceService();
        }
        return BaseSpaceService._instance;
    }
    initSpaceNameConfigs()
    {
        this._spaceTypeConfigs =[    
        { name: '影音大床房', defaultWidth: 4500, defaultDepth: 8000,minArea:40, targetCount:3,ratioWeight: 0.0 },
        { name: '影音双床房',  defaultWidth: 4500, defaultDepth:8000, minArea:40,targetCount: 3},
        { name: '标准大床房',  defaultWidth: 4000, defaultDepth:8000, ratioWeight: 0.5},
        { name: '标准双床房',  defaultWidth: 4000, defaultDepth:8000, ratioWeight: 0.5},
        { name: '景观通道',  defaultWidth: 800, minWidth:800},
        { name: '过道',  defaultWidth: 2000, minWidth:1800}];
    }

    public get spaceNameConfigs(): I_SpaceTypeConfig[] {
        return this._spaceTypeConfigs;
    }
    public set spaceNameConfigs(value: I_SpaceTypeConfig[]) {
        this._spaceTypeConfigs = value;
    }
    protected reNormalByWPolygon(spaceEntity:TBaseSpaceEntity)
    {  
        let w_polygon = spaceEntity.w_polygon;
        if(w_polygon.edges.length == 0) return;
        let max_win_wall_weight = 0;
        let max_win_wall_index = -1;
        spaceEntity.rect.edges.forEach((rect_edge,index)=>{
            let sub_edges = w_polygon.edges.filter((edge)=>edge.checkSameNormal(rect_edge.nor,false,0.1));
            let sum_weight = 0;
            sub_edges.forEach((edge)=>{
                let win = WPolygon.getWindowOnEdge(edge);
                let wall = WPolygon.getWallRectOnEdge(edge);
                if(win) sum_weight += win.length;
                if(wall) sum_weight += wall.w * 0.5;
            })
            if(sum_weight > max_win_wall_weight + 0.1)
            {
                max_win_wall_weight = sum_weight;
                max_win_wall_index = index;
            }
        });
        if(max_win_wall_index >= 0)
        {
            spaceEntity.rect.copy(ZRect.fromPoints(spaceEntity.rect.positions,spaceEntity.rect.edges[max_win_wall_index].nor.clone().negate(),spaceEntity.rect.u_dv_flag));
        }
        else {
            if(spaceEntity.rect.max_hh < 9000 && spaceEntity.rect.min_hh < 5000)
            {
                if(spaceEntity.rect.w > spaceEntity.rect.h)
                {
                    spaceEntity.rect.swapWidthAndHeight();
                }
            }
        }

    }
    updateWPolygon(spaceEntity:TBaseSpaceEntity)
    {
        let container = this._container;
        if(!container) return;


        let ans_polys = spaceEntity.polygon.substract_polygons(container._wall_entities.filter((wall)=>wall.realType!=="GeneratedWall").map((wall)=>wall.rect.clone().reOrderByOrientation(true)));

        
        if(ans_polys)
        {
            let u_dv_flag = spaceEntity.rect.u_dv_flag;
            WPolygon.sortPolysByInnerMaxRect(ans_polys);

            spaceEntity.w_polygon.initByVertices(ans_polys[0].positions);
            let poly = ans_polys[0];
            poly.reOrderByOrientation(u_dv_flag > 0?true:false);
            poly.computeZNor();
            // 让Poly的第一条边 为 矩形的左下角
            let start_edge_id = -1;

            let min_px = 0;
            poly.edges.forEach((edge,index)=>{
                if(edge.nor.dot(spaceEntity.rect.nor) < -0.5)
                {
                    let px = spaceEntity.rect.project(edge.v0.pos).x;

                    if(start_edge_id < 0 || px < min_px)
                    {
                        min_px = px;
                        start_edge_id = index;
                    }
                }
            });
            if(start_edge_id>=0)
            {
                let positions:Vector3[] = [];
                for(let id=0; id < poly.edges.length; id++)
                {
                    positions.push(poly.edges[(id+start_edge_id)%poly.edges.length].v0.pos);
                }
                poly.initByVertices(positions);
            }

            let w_polygon = spaceEntity.w_polygon;
            w_polygon.initByVertices([]);

            WPolygon.makeWPolygonByWindows(poly,container._window_entities.map((entity)=>entity.toWindowData()),w_polygon);

            w_polygon.edges.forEach((edge)=>{
                container._wall_entities.forEach((entity)=>{
                
                    if(entity.rect.frontEdge.islayOn(edge,120,0.1) || entity.rect.backEdge.islayOn(edge,120,0.1))
                    {
                        WPolygon.setWallRectOnEdge(edge,entity.rect);
                    }
                })
            });
            if(spaceEntity.isRootSpace())
            {
                this.reNormalByWPolygon(spaceEntity);
            }
        };

    }
    protected _initSplitMethodsConfigs(spaceEntity:TBaseSpaceEntity)
    {
        let container = this._container;
        if(!container) return;
        let rect = spaceEntity.rect;

        let poly = spaceEntity.polygon;

        //  已经分割好的w_poly
        let w_poly = spaceEntity.w_polygon;

        //  位于背靠墙侧的边
        let back_edges = w_poly.edges.filter(edge=>edge.checkSameNormal(rect.nor.clone().negate(),false,0.1));

        let methodConfigs : I_SplitSpaceMethodConfigs = {
            ...this._splitMethodConfigs,
            expected_pos_list : [],
            unexpected_pos_list : []
        };

        let structures = container._structure_entities.filter((entity)=>spaceEntity.polygon.containsPoint(entity.rect.rect_center));
        
        let refer = "BackEdge";

        let refer_backEdge = rect.backEdge;
        back_edges.forEach((edge)=>{
            let win = WPolygon.getWindowOnEdge(edge);
            if(win)
            {
                let ll = refer_backEdge.projectEdge2d(edge.v0.pos).x;
                let rr = refer_backEdge.projectEdge2d(edge.v1.pos).x;
                if(ll > rr){
                    let tmp = rr; rr =ll;ll=tmp;
                }
                // let t_len = rr - ll;
                // if(t_len < 3500)
                // {
                //     let d_f = 3500 - t_len;
                //     ll -= d_f/2;
                //     rr += d_f/2;
                // }

                methodConfigs.unexpected_pos_list.push({
                    interval : [ll,rr],
                    score_weight : methodConfigs.default_pos_unexpected_score_weight
                });
            }
        });

        structures.forEach((struct)=>{
            if(struct.realType === "Pillar")
            {
                let ll = 0;
                let rr = refer_backEdge.length;
                struct.polygon.vertices.forEach((v)=>{
                    let x = refer_backEdge.projectEdge2d(v.pos).x;
                    ll = Math.min(ll,x);
                    rr = Math.max(rr,x);
                });
                methodConfigs.expected_pos_list.push({
                    interval : [ll-methodConfigs.border_thickness/2,rr+methodConfigs.border_thickness/2],
                    score_weight : methodConfigs.default_pos_unexpected_score_weight
                });
            }
        });

        return methodConfigs;

    }
    protected _initSplitItems(spaceEntity:TBaseSpaceEntity,method_configs:I_SplitSpaceMethodConfigs)
    {

        if(spaceEntity.rect.max_hh < method_configs.max_length || spaceEntity.rect.min_hh < method_configs.min_length)
        {
            if(spaceEntity.isRootSpace())
            {
                spaceEntity.splitItems.length = 0;
                spaceEntity.splitItems.push({
                    category:"",
                    interval :[0,spaceEntity.rect.backEdge.length],
                    splitRefer:"BackEdge"
                });
            }
            return; // 无需再分割了
        }
        let items : I_SplitSpaceItem[] = [];

        method_configs.unexpected_pos_list.forEach((data,index)=>{
            items.push({
                interval : [data.interval[0],data.interval[1]],
                splitRefer :"BackEdge",
                category : "",
                u_dv_flag : index % 2==0?1:-1
            })
        });
        items.sort((a,b)=>a.interval[0] -b.interval[0]);

        spaceEntity.splitItems.length = 0;
        spaceEntity.splitItems.forEach((item,index)=>{
            item.prev_item = index>0? spaceEntity.splitItems[index-1]:null;
            item.next_item = index<spaceEntity.splitItems.length-1? spaceEntity.splitItems[index+1]:null;
        });
        spaceEntity.splitItems.push(...items);

        if(items.length == 0) // 如果为空, 说明没有任何窗
        {
            let length0 = method_configs.default_target_length;

            let method_type = 1;
            if(method_type == 0)
            {
                let ww = spaceEntity.rect.length;
                let hh = spaceEntity.rect.depth;

                let step_length = length0 + method_configs.default_min_gap;

                let num = Math.floor(ww / step_length);

                let t_step_length = ww / num;

                let i = 0;
                let isOdd = true;

                while(i<num)
                {
                    let ll = i * t_step_length;
                    let rr = Math.min(ll + t_step_length,spaceEntity.rect.backEdge.length);
                    items.push({
                        category:"",
                        interval :[ll,rr],
                        splitRefer:"BackEdge",
                        u_dv_flag : isOdd?1:-1
                    });
                    isOdd = !isOdd;
                    i++;
                }
                
            }
            else if(method_type == 1)
            {
                let ww = spaceEntity.rect.length;
                let hh = spaceEntity.rect.depth;

                let step_length = length0 + method_configs.default_min_gap;

                let num = Math.floor(ww / step_length);

                let t_step_length = ww / num;

                let i = 0;

                let isOdd = true;
                while(i<num)
                {
                    let ll = i * t_step_length;
       
                    let rr = ll + t_step_length;
                    rr = Math.min(rr,spaceEntity.rect.backEdge.length);

                    let item : I_SplitSpaceItem = {
                        interval : [ll,rr-method_configs.default_min_gap],
                        splitRefer:"BackEdge",
                        u_dv_flag :isOdd?1:-1
                    }
                    if(i > 0 && i < num-1)
                    {
                    
                        rr += t_step_length;
                        rr = Math.min(rr,spaceEntity.rect.backEdge.length);
                        item.normalChanged = isOdd? "90":"-90";
                        item.isInSide = true;
                        if(isOdd)
                        {
                            ll += method_configs.default_min_gap;
                        }
        
                        item.interval = [ll,rr - method_configs.default_min_gap]

                        item.childrenSplitItems = [
                            {
                                interval : [0, hh/2],
                                splitRefer : "BackEdge",
                                u_dv_flag : 1
                            },
                            {
                                interval : [hh/2,hh],
                                splitRefer : "BackEdge",
                                u_dv_flag : -1
                            }
                        ]
                        i++;
                    }
                    i++;
                    isOdd = !isOdd;

                    items.push(item);
                }
            }

            
            spaceEntity.splitItems.push(...items);

            

        }
    }

    protected _computePosScoreAtVal(val : number, method_configs:I_SplitSpaceMethodConfigs)
    {
        let score = 0;
        method_configs.expected_pos_list.forEach((data)=>{
            let interval = data.interval;
            if(val >= interval[0] && val <= interval[1])
            {
                score += 1. * (data.score_weight!==undefined?data.score_weight:method_configs.default_pos_expected_score_weight);
            }
        });

        method_configs.unexpected_pos_list.forEach((data)=>{
            let interval = data.interval;
            if(val >= interval[0] && val <= interval[1])
            {
                score += 1. * (data.score_weight!==undefined?data.score_weight:method_configs.default_pos_unexpected_score_weight);
            }
        });
        return score;
    }

    /**
     * 
     * @param val  
     * @param item 
     * @param startOrEnd : 0 是替代线段起点, 1:是替代线段终点
     */
    protected _computeLengthScoreAtVal(val:number,item:I_SplitSpaceItem,method_configs:I_SplitSpaceMethodConfigs, startOrEnd:number=0)
    {
        let isStartVal = startOrEnd==0;
        let length = isStartVal? item.interval[1] - val : val - item.interval[0];
        let score = 0;
        method_configs.expected_length_list.forEach((data)=>{
            let diff = Math.abs(data.length - length);
            let d_rate = diff / (data.max_differ || 100);
            if(d_rate > 1.) return;
            d_rate = Math.exp(-d_rate); // 如果d_rate==0, 那么 d_rate==1
            score += d_rate * (data.score_weight!==undefined?data.score_weight:1.);
        });
        return score;

    }
    protected _computeScoreOnItem(spaceEntity:TBaseSpaceEntity,item:I_SplitSpaceItem,method_configs:I_SplitSpaceMethodConfigs)
    {
        let t_score0 = this._computePosScoreAtVal(item.interval[0],method_configs);
        let t_score1 = this._computeLengthScoreAtVal(item.interval[0],item,method_configs,0);
        return t_score0 + t_score1;
    }

    protected _optimizeSplitItems(spaceEntity:TBaseSpaceEntity, method_configs:I_SplitSpaceMethodConfigs)
    {
        let items = spaceEntity.splitItems;

        items.sort((a,b)=>a.interval[0] -b.interval[0]);

        let rect = spaceEntity.rect;
        let backEdge = rect.backEdge;
        if(items[0])
        {
            items[0].interval[0] = 0;
        }
        if(items[items.length-1])
        {
            items[items.length-1].interval[1] = backEdge.length;
        }

        items.forEach((item,index)=>{
            if(index==0) return;
            let prev_r = items[index-1].interval[1];
            let curr_l = items[index].interval[0];

            if(curr_l - prev_r > method_configs.default_min_gap -0.1 && (item.isInSide || items[index-1].isInSide))
            {
                return;
            }

            let mid = (prev_r + curr_l) / 2;
            items[index-1].interval[1] = mid;
            items[index].interval[0] = mid;

        });

    }
    protected _makeChildSpaceBySplitItem(spaceEntity:TBaseSpaceEntity,item:I_SplitSpaceItem)
    {
        let rect = spaceEntity.rect;
        if(item.interval)
        {
            item.splitRefer = item.splitRefer || "BackEdge";

            let mid = (item.interval[0] + item.interval[1])/2;
            let len = Math.abs(item.interval[1] - item.interval[0]);
            let t_rect : ZRect = null;
            if(item.splitRefer === "BackEdge")
            {

                let back_center = rect.backEdge.unprojectEdge2d({x:mid,y:0});
                t_rect = new ZRect(len,rect.h);
                t_rect.nor = rect.nor;
                t_rect.back_center = back_center;
                t_rect.u_dv = rect.dv.multiplyScalar(item.u_dv_flag || 1);
                t_rect.updateRect();
                
            }
            else if(item.splitRefer === "Rect")
            {
                let rect_center = rect.unproject({x:mid,y:0});
                t_rect = new ZRect(len,rect.h);
                t_rect.nor = rect.nor;
                t_rect.u_dv = rect.dv.multiplyScalar(item.u_dv_flag || 1);
                t_rect.rect_center = rect_center;
                t_rect.updateRect();
            }

            if(t_rect)
            {   
                let int_polys = t_rect.intersect_polygons([spaceEntity.w_polygon]);
                if(int_polys[0])
                {   
                    if(int_polys.length > 0)
                    {
                        // to do: 按面积排序
                    }


                }
                let t_nor = spaceEntity.rect.nor.clone();
                if(item.normalChanged)
                {
                    t_nor.applyAxisAngle(new Vector3(0,0,1),(parseFloat(item.normalChanged)||0)/180*Math.PI);
                }
                t_rect = ZRect.fromPoints(int_polys[0].positions,t_nor,item.u_dv_flag || 1);
                t_rect.updateRect();


                let childSpace = new TBaseSpaceEntity(t_rect,"子空间",spaceEntity.space_type);
                childSpace.parent = spaceEntity;
                this.updateWPolygon(childSpace);
                if(item.childrenSplitItems)
                {
                    childSpace.splitItems.length = 0;
                    childSpace.splitItems.push(...item.childrenSplitItems);
                    this._makeChildrenSpaceByItems(childSpace);
                }
                return childSpace;
            }
        }
        return null;
    }
    protected _makeChildrenSpaceByItems(spaceEntity:TBaseSpaceEntity)
    {
        let items = spaceEntity.splitItems;
        spaceEntity.cleanChildren();
        items.forEach(param=>this._makeChildSpaceBySplitItem(spaceEntity,param));

    }

     getLeafSpaceEntities(spaceEntity:TBaseSpaceEntity,leaves:TBaseSpaceEntity[])
    {
        if(spaceEntity.isLeafSpace())
        {
            leaves.push(spaceEntity);
            return;
        }
        if(spaceEntity.childrenSpaces)
        {
            spaceEntity.childrenSpaces.forEach((sub)=>this.getLeafSpaceEntities(sub,leaves));
        }
    }
    protected _updateInnerSpacePolys(spaceEntity:TBaseSpaceEntity,container:TLayoutEntityContainer=null)
    {
        if(!spaceEntity.isRootSpace()) return;
        container = container || TLayoutEntityContainer.instance;
        this._container = container;

        let current_leaf_spaces: TBaseSpaceEntity[] = [];
    
        this.getLeafSpaceEntities(spaceEntity,current_leaf_spaces);

        const wall_thickness = 120;
        current_leaf_spaces.forEach((target_space)=>{
            // let neigbor_space_entities = current_leaf_spaces.filter((entity)=>{
            //     if(entity == target_space) return false;
            //     let res = entity.rect.comparePolyDistance(target_space.rect,wall_thickness);
            //     if(res.overlap_length > target_space.rect.min_hh/4) return true; // 这个以后再改, 有点魔法数字
            //     return false;
            // });

            let rect = target_space.rect;

            let w_poly = target_space.w_polygon;

            let wall_rects : ZRect[] = [];
            w_poly.edges.forEach((edge)=>{
                let has_wall = WPolygon.getWallRectOnEdge(edge);

           

                let has_sub_wall = false;
                if(has_wall) {
                    let overlap_edge = has_wall.backEdge.computeLayOnEdge(edge,600);
                    if(overlap_edge && overlap_edge.length >= edge.length * 0.99) return;
                    if(overlap_edge && overlap_edge.length >= edge.length * 0.2) has_sub_wall = true;
                }
                
                let t_rect = new ZRect(edge.length,wall_thickness);
                t_rect.nor = edge.nor;
                if(has_sub_wall)
                {
                    t_rect.back_center = edge.center;
                    t_rect.updateRect();
                    t_rect.reOrderByOrientation(true);
                    wall_rects.push(t_rect);
                }
                else if(edge.checkSameNormal(target_space.rect.nor,false))
                {
                    t_rect.nor = edge.nor.clone().negate();
                    t_rect.back_center = edge.center;
                    t_rect.updateRect();
                    t_rect.reOrderByOrientation(true);
                    wall_rects.push(t_rect);
                }
                else{
                    t_rect.rect_center = edge.center;
                    t_rect.reOrderByOrientation(true);
                    wall_rects.push(t_rect);
                }

            });

            target_space.border_rects.length = 0;
            target_space.border_rects.push(...wall_rects);

            let inner_space_polys = target_space.w_polygon.substract_polygons(wall_rects);
            if(inner_space_polys[0])
            {
                if(inner_space_polys.length > 0)
                {
                    WPolygon.sortPolysByInnerMaxRect(inner_space_polys);
                }
                target_space.inner_space_poly.initByVertices(inner_space_polys[0].positions);

                let inner_w_poly :ZPolygon = new ZPolygon();
                WPolygon.makeWPolygonByWindows(target_space.inner_space_poly,container._window_entities.map((entity)=>entity.toWindowData()),inner_w_poly);
                WPolygon.setAttachedWPolygon(target_space.inner_space_poly,inner_w_poly);

            }
            target_space.attached_entities.length = 0;
            
            target_space.updateInnerSpaceArea();


        })
    
        
    }
    computeSplitItems(spaceEntity:TBaseSpaceEntity)
    {
        let container = this._container;
        if(!container) return;

        
        let rect = spaceEntity.rect;

        let poly = spaceEntity.polygon;

        //  已经分割好的w_poly
        let w_poly = spaceEntity.w_polygon;

        let methodConfigs = this._initSplitMethodsConfigs(spaceEntity);

        this._initSplitItems(spaceEntity,methodConfigs);

        this._optimizeSplitItems(spaceEntity,methodConfigs);

        this._makeChildrenSpaceByItems(spaceEntity);

        
    }
    spiltIntoSubSpaces(spaceEntity:TBaseSpaceEntity,container:TLayoutEntityContainer=null)
    {
        container = container || TLayoutEntityContainer.instance;
        this._container = container;
        this.updateWPolygon(spaceEntity);
        this.computeSplitItems(spaceEntity);

        this._updateInnerSpacePolys(spaceEntity,container);
        this.updateSpaceTypes(spaceEntity,container);

        let current_leaf_spaces: TBaseSpaceEntity[] = [];
        this.getLeafSpaceEntities(spaceEntity,current_leaf_spaces);
        current_leaf_spaces.forEach((leaf)=>{
            this.updateInnerSpaceLayout(leaf,container);

        })

    }   


    splitAllSpaces(container:TLayoutEntityContainer= null)
    {
        container = container || TLayoutEntityContainer.instance;
        container._base_space_entities.forEach((entity)=>{
            this.spiltIntoSubSpaces(entity,container);
        });
    }

    _checkSpaceSatisfyConfig(space_entity:TBaseSpaceEntity, config:I_SpaceTypeConfig)
    {
        let inner_poly = space_entity.inner_space_poly;
        if(!inner_poly || inner_poly.edges.length == 0) return false;

        let rect = space_entity.rect;
        if(config.minArea)
        {
            if(space_entity.inner_space_area / 1000 / 1000 < config.minArea) return false;
        }

        if(config.minWidth)
        {
            if(rect.length < config.minWidth) return false;
        }

        if(config.minDepth)
        {
            if(rect.depth < config.minDepth) return false;
        }
        return true;

    }
    updateSpaceTypes(space_entity:TBaseSpaceEntity, container:TLayoutEntityContainer= null)
    {
        container = container || TLayoutEntityContainer.instance;

        let baseEntities = container._base_space_entities;

        let other_room_spaces : TBaseSpaceEntity[] = [];

        baseEntities.forEach((entity)=>entity !== space_entity && this.getLeafSpaceEntities(entity,other_room_spaces));

        let keywords = this.spaceNameConfigs.map((config)=>config.name);
        let nameCount : {[key:string]:TBaseSpaceEntity[]} = {};

        keywords.forEach((name)=>nameCount[name]= []);

        other_room_spaces.forEach((space)=>{
            if(keywords.includes(space.name))
            {
                if(!nameCount[space.name])
                {
                    nameCount[space.name] = [];
                }
                nameCount[space.name].push(space);
            }
        })
        let current_room_spaces:TBaseSpaceEntity[] = [];
        this.getLeafSpaceEntities(space_entity,current_room_spaces);
        current_room_spaces.forEach((space)=>{
            space.name = "子空间";
        });
        let targetCountConfigs = this.spaceNameConfigs.filter((config)=>config.targetCount);

        let total_room_num = other_room_spaces.length + current_room_spaces.length;

        targetCountConfigs.forEach((config)=>{
            let targetCount = config.targetCount;
            if(nameCount[config.name].length >= targetCount) return;

            let targetSpaces = current_room_spaces.filter(space=>this._checkSpaceSatisfyConfig(space,config));

            targetSpaces.forEach((space,index)=>{
                if(nameCount[config.name].length >= targetCount) return;
                space.name = config.name;
                nameCount[config.name].push(space);
            });
        })

        targetCountConfigs.forEach(config=>{
            total_room_num -= nameCount[config.name].length;
        });

        let ratioWeightConfigs = this.spaceNameConfigs.filter((config)=>config.ratioWeight);

        let prev_config : I_SpaceTypeConfig = null; // 主要是希望交替选择

        current_room_spaces.forEach((space)=>{
            if(keywords.includes(space.name)) return;
            let satisfy_configs = ratioWeightConfigs.filter(config=>this._checkSpaceSatisfyConfig(space,config));

            satisfy_configs = satisfy_configs.filter((config)=>{
                let t_count = nameCount[config.name].length;
                let target_count = total_room_num * config.ratioWeight;
                if(t_count >= target_count) return false;
                return true;
            });

            if(satisfy_configs.length >= 2)
            {
                satisfy_configs = satisfy_configs.filter((config)=>config!=prev_config);
            }

            if(satisfy_configs[0])
            {
                space.name = satisfy_configs[0].name;
                nameCount[satisfy_configs[0].name].push(space);
                prev_config = satisfy_configs[0];
            }
        })
    }

    updateInnerSpaceLayout(space_entity:TBaseSpaceEntity,container:TLayoutEntityContainer=null)
    {
        if(!space_entity.isLeafSpace()) return;
        HotelSpaceRoomLayoutService.instance.computeInnerSpaceLayout(space_entity,container);
    }

    
}

export const baseSpaceService = BaseSpaceService.instance;