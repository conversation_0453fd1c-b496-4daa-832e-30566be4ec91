import { IKitchenLayout, KitchenAreaType } from "@layoutai/layout_service";

import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoomLayoutScheme } from "../../../TLayoutScheme/TRoomLayoutScheme";
import { TRoom } from "../../../TRoom";
import { TCheckRule } from "../TCheckRule";

/**
 * @description 相邻评分
 * 最优顺序：鲜储区→水盆区→操作区/填充区→烹饪区
 * <AUTHOR>
 * @date 2025-08-30
 * @lastEditTime 2025-08-30 17:32:58
 * @lastEditors xuld
 */
export class TKitchenAreaOrderCheckRule extends TCheckRule {
    private _bestSortTypes = [
        KitchenAreaType.FreshStorageArea,
        KitchenAreaType.SinkArea,
        KitchenAreaType.OperationArea,
        KitchenAreaType.CookingArea
    ];

    checkValue(
        room: TRoom,
        figure_elements?: TFigureElement[],
        layout_scheme: TRoomLayoutScheme = null
    ) {
        if (!layout_scheme) {
            return { value: 0 };
        }

        let extra = layout_scheme.extra as IKitchenLayout;
        if (!extra) {
            return { value: 0 };
        }

        const tps: Array<KitchenAreaType> = [];
        extra.edgeLayouts.forEach(item => {
            tps.push(...item.areaTypeRects.map(item => item.areaType));
        });

        let isBestSort = this.checkOrder(tps);
        if (!isBestSort) {
            isBestSort = this.checkOrder(tps.reverse());
        }

        let score = 0;
        if (isBestSort) {
            score += 1;
        }

        return { value: score };
    }

    checkOrder(tps: KitchenAreaType[]) {
        const extractedTypes = tps.filter(item => this._bestSortTypes.includes(item));

        let index = 0;
        for (let i = 0; i < this._bestSortTypes.length && index < extractedTypes.length; i++) {
            if (this._bestSortTypes[i] === extractedTypes[index]) {
                index++;
            }
        }
        return index === extractedTypes.length;
    }
}
