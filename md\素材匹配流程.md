# 素材匹配系统（Material Matching System）工作流程分析

## 概述
素材匹配系统是LayoutAI中的核心功能，负责将合适的材料/素材应用到房间中的各个图元上。整个系统由多个类组成，主要包括`TMaterialMatcher`（素材匹配器）、`TSeriesFurnisher`（套系应用器）、`MatchingPostProcesser`（匹配后处理器）等。

## 核心类的职责

### TMaterialMatcher
素材匹配的核心类，负责将合适的素材匹配到房间中的图元。主要职责包括：
- 过滤需要匹配材料的图元
- 调用材料服务API获取匹配结果
- 处理组合图元的特殊情况
- 调用后处理器进行材料位置和尺寸调整

### TSeriesFurnisher
负责套系应用流程，是套系应用在核心层的起点。管理房间和套系的关系，并触发素材匹配过程。主要职责包括：
- 维护房间与套系的映射关系（room2SeriesSampleMap）
- 处理套系选中和应用逻辑
- 调用TMaterialMatcher进行素材匹配
- 执行多样化匹配和后处理

### MatchingPostProcesser
负责对匹配完成的材料进行后处理，如位置调整、尺寸调整等。具体包括：
- 调整材料和图元的位置和尺寸
- 处理特殊图元如背景墙、吊顶等
- 隐藏重复材料
- 生成顶视图材质

### TSeriesSample
表示一个风格套系（系列），包含套系的基本信息，如ID、名称、缩略图等。

## TSeriesFurnisher.onSeriesSampleSelected 方法详解

`onSeriesSampleSelected`是套系应用流程的入口方法，在用户选择套系后被触发。

### 参数说明
- `scope`: 应用范围，包括：
  - `soft`: 软装（如沙发、桌椅等家具）
  - `cabinet`: 定制柜（如衣柜、书柜等）
  - `hard`: 硬装（如地面、墙面、吊顶等）
  - `remaining`: 是否为补全应用模式
- `series`: 选中的套系对象
- `targetRooms`: 需要应用套系的目标房间数组
- `options`: 应用选项，包括：
  - `needsDiversifyMatched`: 是否需要多样化匹配
  - `updateTopViewBy3d`: 是否更新3D视图
  - `needAutoFinetune`: 是否需要自动微调

### 执行流程

```typescript
async onSeriesSampleSelected(scope, series, targetRooms, options) {
    // 1. 准备阶段
    //取消被选中图元的选中状态
    LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
    //如果未指定目标房间列表，则使用当前选中的房间列表
    if (!targetRooms) targetRooms = this.current_rooms;
    
    if (targetRooms != null) {
        //用于记录需要应用套系的房间列表
        let needFurnishRooms: TRoom[] = [];
        
        // 2. 为每个房间应用套系（并行处理）
        let allPromises = targetRooms.map(async (roomItem: TRoom) => {
            //如果房间被锁定，则此房间跳过套系应用流程
            if (roomItem.locked) return;
            //将需要应用套系的房间添加到需要应用套系的房间列表中
            needFurnishRooms.push(roomItem);
            //创建一个新的套系实例
            let newSeries = new TSeriesSample(series);
            
            // 3. 根据应用模式执行不同流程
            if (scope.remaining == false) {
                // 常规应用流程
                //记录此房间的需要应用套系的范围和套系
                roomItem.addApplyScope(scope, newSeries);
                //设置当前应用范围
                roomItem.setCurrentApplyScope(scope);
                //清除当前应用范围中所属图元匹配到的素材
                roomItem.clearMatchedMaterialsInCurrentScope();
                //记录房间和套系的映射关系
                this.room2SeriesSampleMap.set(roomItem, newSeries);
                //尝试应用套系
                promise_list = this.tryToFurnishRooms([roomItem]);
            } else {
                // 补全应用流程
                //获取房间已应用了套系的范围
                let furnishedApplyScope = roomItem.furnishedApplyScope;
                //根据已应用套系的范围，计算和设置当前应用范围
                let applyScope = { 
                    soft: furnishedApplyScope && !furnishedApplyScope.soft, 
                    cabinet: furnishedApplyScope && !furnishedApplyScope.cabinet, 
                    hard: furnishedApplyScope && !furnishedApplyScope.hard, 
                    remaining: true 
                };
                //设置此房间的当前应用范围
                roomItem.setCurrentApplyScope(applyScope);
                //尝试对此空间应用套系，走补全的套系应用流程
                promise_list = this.tryToFurnishRoomRemainings([roomItem], newSeries);
                //记录此房间的应用范围和风格套系的映射关系
                roomItem.addApplyScope(applyScope, newSeries);
            }
        });

        // 4. 等待所有套系应用完成
        await Promise.allSettled(allPromises);
        
        // 5. 后处理阶段
        if(options.needsDiversifyMatched) {
            //对需要应用套系的房间的图元进行多样化匹配
            this.diversifyMatchedMatetrial(needFurnishRooms);
        }

        // 6. 更新UI和通知
        LayoutAI_App.emit(EventName.Room2SeriesSampleRoom, 
            this.orderRoom2SeriesSample(this.room2SeriesSampleMap, this.current_rooms));
        this.update();

        // 7. 更新3D场景
        if (LayoutAI_App.instance.scene3D?.isValid()) {
            LayoutAI_App.emit(Scene3DEvents.UpdateScene3D, true);
        }
        else if (LayoutAI_App.instance.scene3D) {
            if(options.updateTopViewBy3d) {
                await Scene3DEventsProcessor.UpdateEntitiesNeedsTopViewImage();
            }
        }
        
        // 8. 生成报价数据
        LayoutAI_App.emit(EventName.quoteDataSeries, series);
        this._current_series = series;
        
        // 9. 自动微调（如需要）
        if(options.needAutoFinetune) {
            await this.autoFineTuning();
        }
    }
    
    // 10. 如果没有选中房间，提示用户
    if (this.current_rooms == null || this.current_rooms.length == 0) {
        LayoutAI_App.emit(EventName.PerformFurnishResult, 
            { progress: "info", message: LayoutAI_App.t("请先选择空间") });
    }
}
```

### tryToFurnishRooms方法

这是常规套系应用时调用的方法，负责对指定的房间列表应用套系：

```typescript
tryToFurnishRooms(targetRooms: TRoom[]): Promise<any>[] {
    let promises: Promise<any>[] = [];
    for (let roomItem of targetRooms) {
        // 关联图元与房间
        roomItem._furniture_list.forEach(ele => ele._room = roomItem);
        //更新房间已应用套系的范围
        roomItem.updateFurnishedApplyScope();
        //如果房间已关联套系且有图元，则匹配套系素材
        if (this.room2SeriesSampleMap.has(roomItem) && (roomItem._furniture_list.length > 0)) {
            //所有匹配到的素材列表
            let allMatchedMaterials: I_MaterialMatchingItem[] = [];
            //调用TMaterialMatcher.furnishRoom方法进行素材匹配
            promises.push(TMaterialMatcher.instance.furnishRoom(
                roomItem, 
                this.room2SeriesSampleMap.get(roomItem), 
                logger, 
                allMatchedMaterials
            ));
        }
    }
    return promises;
}
```

### tryToFurnishRoomRemainings方法

这是补全套系应用时调用的方法，负责对指定房间中未匹配素材的图元进行套系应用：

```typescript
tryToFurnishRoomRemainings(targetRooms: TRoom[], sereis: TSeriesSample): Promise<any>[] {
    let allSoftMaterials: I_MaterialMatchingItem[] = [];
    let promises: Promise<any>[] = [];
    for (let roomItem of targetRooms) {
        roomItem.updateRemainingFurnishedApplyScope();
        roomItem._furniture_list.forEach(ele => ele._room = roomItem);
        if (roomItem._furniture_list.length > 0) {
            promises.push(TMaterialMatcher.instance.furnishRoom(
                roomItem, 
                sereis, 
                logger, 
                allSoftMaterials
            ));
        }
    }
    return promises;
}
```

### diversifyMatchedMatetrial方法

该方法用于实现多样化匹配，即确保不同房间中相同类型的图元尽可能匹配到不同的材料，提高设计的多样性：

```typescript
diversifyMatchedMatetrial(rooms: TRoom[]) {
    let needUpdate: Boolean = false;
    
    // 第一组：常规图元多样化
    // 床、背景墙、窗帘、地毯等常见软装图元
    let diversifyModelLocs = ["床", "背景墙", "窗帘", "地毯", "挂画", "墙饰", "主灯", "床尾凳", 
        "梳妆台", "书桌", "床具组合", "书桌组合", "榻榻米组合", "沙发组合", "岛台组合", "餐桌椅组合"];
    
    diversifyModelLocs.forEach((modelLoc: string) => {
        // 1. 收集所有指定modelLoc的图元
        let allModellocFigureElements: TFigureElement[] = [];
        for (let roomItem of rooms) {
            if (!roomItem._furniture_list) continue;
            roomItem._furniture_list.filter(ele => ele.modelLoc.endsWith(modelLoc))
                .forEach(ele => allModellocFigureElements.push(ele));
        }
        
        // 2. 记录已匹配材料，避免重复
        let allModellocMatchedMaterials: I_MaterialMatchingItem[] = [];
        
        // 3. 遍历每个图元，检查是否需要替换材料
        allModellocFigureElements.forEach((ele: TFigureElement) => {
            let matchedMaterial = ele._matched_material;
            if (matchedMaterial == null) return;
            
            // 如果当前材料已被其他图元使用，尝试替换为备选材料
            if (allModellocMatchedMaterials.filter(item => 
                item.modelId != null && matchedMaterial.modelId != null && 
                item.modelId == matchedMaterial.modelId).length > 0) {
                
                // 遍历备选材料，找到合适且未被使用的
                for (let i = 0; i < ele._candidate_materials.length; i++) {
                    let candidateMaterial = ele._candidate_materials[i];
                    
                    // 检查尺寸是否合适
                    if (!TFigureElement.checkIsMatchedSizeSuitable(ele, candidateMaterial)) {
                        continue;
                    }
                    
                    // 检查是否已被使用
                    if (allModellocMatchedMaterials.filter(item => 
                        item.modelId == candidateMaterial.modelId).length == 0) {
                        
                        // 替换材料
                        TMaterialMatcher.instance.replaceMaterial(ele, candidateMaterial, 
                            this.room_list, {
                                post_process_in_room: true,
                                needs_update_3d: true
                            });
                        needUpdate = true;
                        break;
                    }
                }
            }
            
            // 添加到已使用材料列表
            allModellocMatchedMaterials.push(ele._matched_material);
        });
    });
    
    // 第二组：背景墙类多样化
    // 专门处理背景墙类图元的多样化匹配
    let diversifyBackgorupWallModelLocs = ["背景墙", "电视背景墙", "沙发背景墙"];
    // 处理逻辑与第一组类似，但有特殊处理...
    
    // 第三组：成对图元多样化
    // 确保成对出现的图元（如床头柜、床头吊灯）使用相同材料，但在不同房间中使用不同材料
    let diversifyPairModelLocs = ["床头柜", "床头吊灯"];
    diversifyPairModelLocs.forEach((modelLoc) => {
        // 按房间收集成组的图元
        let allRoomsBedsideTableGroupList: TFigureElement[][] = [];
        for (let roomItem of rooms) {
            if (!roomItem._furniture_list) continue;
            let roomBedsideTableGroup: TFigureElement[] = [];
            
            // 收集同一房间内的特定类型图元
            roomItem._furniture_list.filter(ele => ele.modelLoc == modelLoc)
                .forEach(ele => roomBedsideTableGroup.push(ele));
                
            if (roomBedsideTableGroup.length > 0) {
                allRoomsBedsideTableGroupList.push(roomBedsideTableGroup);
            }
        }
        
        // 记录已使用材料
        let allBedsideTableMatchedMaterials: I_MaterialMatchingItem[] = [];
        
        // 处理每组图元
        allRoomsBedsideTableGroupList.forEach((group: TFigureElement[]) => {
            let bedsideTableFigure: TFigureElement = group[0];
            let matchedMaterial = bedsideTableFigure._matched_material;
            if (matchedMaterial == null) return;
            
            // 检查是否需要替换材料
            if (allBedsideTableMatchedMaterials.filter(item => 
                item.modelId != null && matchedMaterial.modelId != null && 
                item.modelId == matchedMaterial.modelId).length > 0) {
                
                // 寻找合适的替代材料
                for (let i = 0; i < bedsideTableFigure._candidate_materials.length; i++) {
                    let candidateMaterial = bedsideTableFigure._candidate_materials[i];
                    if (!TFigureElement.checkIsMatchedSizeSuitable(bedsideTableFigure, candidateMaterial)) {
                        continue;
                    }
                    
                    // 如果找到未使用的合适材料
                    if (allBedsideTableMatchedMaterials.filter(item => 
                        item.modelId == candidateMaterial.modelId).length == 0) {
                        
                        // 为组内所有图元替换材料
                        group.forEach(ele => ele.replaceMatchedMaterial(candidateMaterial));
                        needUpdate = true;
                        break;
                    }
                }
            }
            
            // 添加到已使用材料列表
            allBedsideTableMatchedMaterials.push(bedsideTableFigure._matched_material);
        });
    });

    // 如果有更新，刷新界面
    if (needUpdate) {
        this.update();
    }
}
```

## TMaterialMatcher.furnishRoom方法详细分析

`furnishRoom`方法是素材匹配的核心方法，负责将套系中的材料应用到房间的图元上。

### 主要参数
- `room`: 要应用材料的房间
- `seriesSampleItem`: 要应用的套系
- `options`: 应用选项，包括是否为3D预览模式等
- `matchedMaterials`: 用于记录所有匹配到的材料

### 执行流程

1. **前置检查**：
   - 检查房间是否被锁定（`if (roomItem.locked) return;`）
   - 更新房间当前使用的套系（`roomItem._series_sample_info = seriesSampleItem;`）
   - 记录房间应用范围所对应的套系（`roomItem.updateCurrentScopeSeries(seriesSampleItem);`）

2. **清理装饰**：
   - 从房间中删除电器装饰图元（`TPostDecoratesLayout.post_clean_electricity_decorations(roomItem, roomItem._furniture_list);`）

3. **过滤图元**：
   - 确定是常规应用还是补全应用流程（`roomItem.isCurrentApplyRemainingCategory()`）
   - 获取需要匹配材料的图元列表，包括：
     - 如果是常规应用：获取符合当前应用范围的所有图元
     - 如果是补全应用：只获取未匹配材料的图元
   - 对于硬装应用范围：额外生成或获取硬装图元（`makeHardFurnishingElements`）
   - 过滤掉锁定的图元和不在当前应用范围的图元

4. **准备组合图元**：
   - 收集所有台面装饰图元（`allTableDecorationElements`）
   - 收集所有组合类图元及其成员图元（`groupElements`和`allMemberElements`）
   - 设置组合类图元的初始匹配状态（`entity.setMatchedVisible(false)`）

5. **特殊处理**：
   - 处理有收口方向的柜体图元（`TPostFigureElementAdjust.post_process_cabinet_close_direction`）
   - 生成固定装置图元（`makeFixtureElements`）

6. **素材匹配**：
   - 调用材料服务API进行素材匹配（`MaterialService.materialMatching`）
   - 提供的参数包括：图元列表、套系ID、套系名称、种子方案ID、房间类型和面积等

7. **后处理组合图元**：
   - 处理未匹配到材料的组合图元
   - 根据成员图元是否匹配到材料，判断组合图元的匹配状态
   - 生成组合图元的匹配实体（`generateGroupMemberMatchedEntities`）

8. **后处理**：
   - 调用`MatchingPostProcesser`的一系列方法，对匹配的材料进行后处理：
     - `adjustMatchedMaterialsAndFigureElements`: 调整材料和图元
     - `adjust_backgroundwall_related`: 处理背景墙相关图元
     - `TPostLayoutCeiling.adjustCeilingRectByCabinets`: 调整吊顶图元
     - `TPostLayoutCeiling.adjustCurtainSize`: 调整窗帘尺寸
     - `hideDuplicatedMaterialOfSameModelloc`: 隐藏重复材料
     - `createTopViewTexturesForProductMaterial`: 生成顶视图材质

9. **更新房间状态**：
   - 更新房间的应用范围状态
   - 记录房间已应用的套系
   - 更新未匹配图元的高亮标记（`spotlightingUnmatchedFigureElements`）

## 素材匹配的后处理流程详解

后处理主要由`MatchingPostProcesser`类实现，包括以下几个关键方法：

### 1. adjustMatchedMaterialsAndFigureElements 方法

该方法负责调整匹配到材料的图元及其材料的位置与尺寸：

```typescript
public static adjustMatchedMaterialsAndFigureElements(room:TRoom, allMatchedMaterial: I_MaterialMatchingItem[]) {
    // 遍历所有匹配到的材料
    for (let material of allMatchedMaterial) {
        // 将图元和素材进行关联
        let figure_element = material.figureElement;
        
        // 确保图元和材料都有效
        if (!figure_element || !material.modelId) continue;
        
        // 将材料关联到图元
        figure_element._matched_material = material;
        figure_element.params.materialId = material.modelId;
        
        // 修改材料的目标布置位置和尺寸
        MatchingPostProcesser.modifyMaterialPositionSize(figure_element, material);
        
        // 处理吊顶图元的特殊情况
        if(compareNames([material?.figureElement?.category],["吊顶"])) {
            // 将吊顶匹配素材的目标布置位置和尺寸，设置为当前图元的目标布置位置和尺寸
            material.figureElement.matched_rect = material.figureElement.rect.clone();
        } else {
            // 同步图元的matched_rect和修改材料的z位置
            MatchingPostProcesser.syncMatchedRectAndModifyMaterialPositionZ(material, room);
        }
    }
}
```

### 2. syncMatchedRectAndModifyMaterialPositionZ 方法

该方法负责将材料的位置和尺寸信息同步到图元的匹配矩形，并调整材料的z轴位置：

```typescript
private static syncMatchedRectAndModifyMaterialPositionZ(material: I_MaterialMatchingItem, room:TRoom) {
    let figure_element = material.figureElement;
    
    // 同步matched_rect数据的辅助函数
    let sync_rect_pos = (from_rect:ZRect, to_rect:ZRect) => {
        // 处理不同类型图元的对齐方式：
        // 1. 背靠墙的图元：保持back_center一致
        // 2. 侧靠墙的图元：贴墙计算
        // 3. 其他默认居中
    }
    
    // 确保图元有matched_rect
    if(!figure_element.matched_rect) {
        figure_element.matched_rect = figure_element.rect.clone();
    } else {
        // 同步位置
        sync_rect_pos(figure_element.matched_rect, figure_element.rect);
    }
    
    // 修改matched_rect的尺寸
    figure_element.matched_rect.length = material.targetSize.length;
    figure_element.matched_rect.depth = material.targetSize.width;
    figure_element.matched_rect.updateRect();
    
    // 将位置同步回matched_rect
    sync_rect_pos(figure_element.rect, figure_element.matched_rect);
    
    // 设置材料的位置为图元的matched_rect的中心点
    material.targetPosition = Vec3toMeta(figure_element.matched_rect.rect_center);
    
    // 根据不同类型的图元，调整z轴位置：
    // 1. 贴顶墙的图元：设置为层高减去材料高度
    // 2. 落地的图元：设置为地板厚度
    // 3. 特殊情况处理：浴室柜、马桶、电视柜、电视等
    // 4. 其他：使用图元的original rect的z值
    
    // 最后同步z值回图元
    figure_element.rect.zval = material.targetPosition.z;
    figure_element.matched_rect.zval = material.targetPosition.z;
}
```

### 3. adjust_backgroundwall_related 方法

该方法专门处理背景墙及其相关图元：

```typescript
public static adjust_backgroundwall_related(room:TRoom) {
    // 收集所有背景墙图元
    let backgroundWalls:TFigureElement[] = room._furniture_list.filter(fe => 
        fe.modelLoc.indexOf("背景墙") >= 0 && fe.haveMatchedMaterial()
    );
    
    // 处理每个背景墙图元
    for (let backgroudwall of backgroundWalls) {
        // 限制背景墙素材深度最大为120mm
        let max_background_wall_depth = Math.min(backgroudwall._matched_material.width, 120);
        
        // 创建背景墙矩形区域
        let background_wall_rect = backgroudwall.matched_rect.clone();
        background_wall_rect._h = max_background_wall_depth;
        
        // 检查其他图元与背景墙的交互
        room._furniture_list.forEach(other => {
            // 排除自身和不相关图元
            if (backgroudwall == other || !other.haveMatchedMaterial()) return;
            
            // 检查方向是否一致
            if(!other.matched_rect.checkSameNormal(backgroudwall.rect.nor)) return;
            
            // 检查是否相交
            let int_rect = other.matched_rect.intersect_rect(background_wall_rect);
            if (int_rect) {
                // 调整图元位置以避免与背景墙重叠
                let moveVec = background_wall_rect.nor.clone().multiplyScalar(int_rect.h);
                other.matched_rect.rect_center.add(moveVec);
                other.rect.back_center = other.matched_rect.back_center;
                other.rect.updateRect();
            }
        });
    }
}
```

### 4. hideDuplicatedMaterialOfSameModelloc 方法

该方法用于处理同一位置的重复材料，避免视觉干扰：

```typescript
public static async hideDuplicatedMaterialOfSameModelloc(singleFigureElements:TFigureElement[], groupMemberFigureElements:TFigureElement[]) {
    // 隐藏地毯重复素材
    await MatchingPostProcesser._hideDuplicatedMaterialOfSameModelloc("地毯", singleFigureElements, groupMemberFigureElements);
    // 隐藏挂画重复素材
    await MatchingPostProcesser._hideDuplicatedMaterialOfSameModelloc("挂画", singleFigureElements, groupMemberFigureElements, false);
    // 隐藏背景墙重复素材
    await MatchingPostProcesser._hideDuplicatedMaterialOfSameModelloc("背景墙", singleFigureElements, groupMemberFigureElements);
}
```

## 关键流程图

```
用户选择套系
  |
  v
TSeriesFurnisher.onSeriesSampleSelected
  |
  v
确定应用范围和目标房间
  |
  v
对每个房间: TMaterialMatcher.furnishRoom
  |
  ├── 过滤需要匹配的图元
  |   ├── 常规应用：匹配全部范围内图元
  |   └── 补全应用：仅匹配未匹配图元
  |
  ├── 调用MaterialService.materialMatching获取匹配结果
  |
  └── 后处理匹配结果
      ├── adjustMatchedMaterialsAndFigureElements：调整材料和图元位置尺寸
      ├── adjust_backgroundwall_related：处理背景墙相关图元
      ├── hideDuplicatedMaterialOfSameModelloc：隐藏重复材料
      └── createTopViewTexturesForProductMaterial：生成顶视图材质
  |
  v
更新UI和3D场景
  |
  v
（可选）自动微调
```

## 素材匹配算法的决策因素

素材匹配服务 (`MaterialService.materialMatching`) 基于以下因素进行决策：

1. **图元信息**：
   - 模型位置 (modelLoc)
   - 类型 (category)
   - 子类型 (sub_category)
   - 位置和尺寸 (rect)

2. **套系信息**：
   - 套系ID (seriesKgId)
   - 套系名称 (seriesName)
   - 种子方案ID (seedSchemeId)

3. **房间信息**：
   - 房间类型 (room_type)
   - 房间面积 (area)

匹配算法会根据这些因素，为每个图元找到最合适的材料。

## 扩展点和优化建议

1. **素材匹配算法优化**：
   - 可以考虑使用机器学习方法提高匹配精度
   - 建立更完善的材料和图元的关联关系

2. **性能优化**：
   - 对于大量图元的房间，可以并行处理匹配过程
   - 缓存常用套系的匹配结果

3. **用户体验优化**：
   - 提供更细粒度的材料选择和调整功能
   - 增加材料预览功能 