import { magiccubeRequest, openApiRequest } from '@/utils';

/**
 * @description 报价接口
 */
export async function getQuote(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `/quoteweb-wrap/web/calculation/series/evaluate`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}

/**
 * @description 预报价接口
 */
export async function saveQuote(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `/quoteweb-wrap/web/calculation/series/save_source_content`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}


/**
 * @description 装修报价接口
 */
export async function hotelQuote(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `/quoteweb-wrap/web/calculation/quote_xml_biz/hotel_quote`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}


/**
 * @description 检查是否达到相关模块数量限制
 */
export async function checkCurrent(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `/api/njvr/layoutModuleNumberLimit/checkCurrent`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}

/**
 * @description 查询相关模块数量限制
 */
export async function getUseDetail(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `/api/njvr/layoutModuleNumberLimit/getUseDetail`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}

