import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    propertyPanel: css`
      :global {
        .swj-property-label-item-container {
          .swj-property-label-item-content {
            width: 90px !important;
          }
          .swj-property-label-item-tilte {
            min-width: 80px !important;
          }
        }
      }
    `
  };
});
