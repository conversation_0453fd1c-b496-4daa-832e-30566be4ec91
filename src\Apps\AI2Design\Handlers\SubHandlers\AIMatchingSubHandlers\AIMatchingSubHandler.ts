import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { LayoutAI_App, LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { IRoomEntityRealType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TBaseGroupEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";


export class AIMatchingSubHandler extends CadBaseSubHandler
{
    constructor(cad_mode_handler:AI2BaseModeHandler)
    {
        super(cad_mode_handler);
    }
    

    onmousedown(ev: I_MouseEvent): void {
        // super.updateCandidateRects();

        // 当吊顶图层显示的时候
        if(this.manager.layer_CeilingLayer.visible)
        {
            let target_ceiling = this.selectCeilingElement({x:ev.posX,y:ev.posY,z:0},50);

            if(target_ceiling)
            {
                this.cleanSelection();

                this.selected_target.selected_ceiling = target_ceiling;
            }
            else{
                this.selected_target.selected_ceiling = null;
            }
        }

        if(this.selected_target.selected_ceiling)
        {
            LayoutAI_App.RunCommand(LayoutAI_Commands.EditCeiling); // 进入编辑吊顶模式
            return;
        }
        

        super.onmousedown(ev);
    }
    // 空间被选中
    onRoomEntitySelected(entity: TRoomEntity): Promise<void> {

        
        if(!entity)
        {
            this.manager.layout_container._selected_room = null;
        }
        else{
            let room_entity = entity as TRoomEntity;
            this.manager.layout_container._selected_room = room_entity._room ||  room_entity.makeTRoom(room_entity.furniture_entities, false);
        }
        return;
    }

    updateAttributes(mode?: "init" | "hide" | "edit"): Promise<void> {

        return;
    }
    updateCandidateRects() {
        let ignore_realtypes: IRoomEntityRealType[] = [
            "Decoration",
            "Electricity",
            "BayWindow",
            "OneWindow",
            ...(!this.manager.layer_CadFurnitureLayer.visible ? ["SoftFurniture" as IRoomEntityRealType] : []),
            ...(!this.manager.layer_CadCabinetLayer.visible ? ["Cabinet" as IRoomEntityRealType] : []),
            ...(!this.manager.layer_LightingLayer.visible ? ["Lighting" as IRoomEntityRealType] : [])
        ];

        this.candidate_rects.length = 0;

        let candidate_rects = this.container.getCandidateRects(["Furniture","Window"]
             ,{ignore_realtypes:ignore_realtypes}, {"Funiture":10,"Door":2,"Window":2},true);

        this.container.entity_selector.updateCandidates(["Furniture","Window"],{ignore_realtypes:ignore_realtypes});
        if (this.container.entity_selector.combination_edit== true) {
            let new_candidate_rects = [];
            for (let rect of candidate_rects) {
                if (rect.ex_prop["poly_target_type"] !== "BaseGroup") {
                    new_candidate_rects.push(rect);
                } else {
                    let group_entity:TBaseGroupEntity = TBaseGroupEntity.getOrMakeEntityOfCadRect(rect) as TBaseGroupEntity;
                    if(group_entity)
                    {
                        group_entity.combination_entitys.forEach(entity => {
                            new_candidate_rects.push(entity.matched_rect);
                        });
                    }
        
                }
            }
            candidate_rects = new_candidate_rects;
        }
        // let logcontext = this.constructor.name + ".updateCandidateRects() [candidate_rects] " + "\n";
        // candidate_rects.forEach(rect => {
        //     if (rect.ex_prop["poly_target_type"] === "Wall") return;
        //     if (rect.ex_prop["poly_target_type"] === "RoomArea") return;
        //     logcontext += rect.ex_prop["poly_target_type"]  + ": " + (rect.ex_prop["GroupName"] ? ("GroupName=" + rect.ex_prop["GroupName"]) : "") + (rect.ex_prop["label"] ? (" label=" + rect.ex_prop["label"]) : "") + "\n";
        // });
        // console.info(logcontext);

        this.candidate_rects.push(...candidate_rects);

        this.updateExsorbRects();
    }

    /**
     * 尝试选择吊顶
     * @param pos  
     */
    selectCeilingElement(pos:{x:number,y:number,z:number}, min_dist:number = 20)
    {
        
        let target_ceiling : TFigureElement = null;

        this.room_list.forEach(room=>{
            if(room._ceilling_list)
            {
                for(let ceil of room._ceilling_list)
                {
                    let dist = ceil.rect.distanceToPoint(pos);
                    if(Math.abs(dist) < min_dist)
                    {
                        min_dist = Math.abs(dist);
                        target_ceiling = ceil;
                    }            
                }
            }
        });

        return target_ceiling;
    }

    
}