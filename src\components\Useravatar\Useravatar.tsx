import React from 'react'
import useStyles from './style';
import { useStore } from '@/models';
import { AIDeskUrl } from '@/config';
import { useTranslation } from 'react-i18next';
import { observer } from 'mobx-react-lite';
import { logout } from '@/utils/login';
const Useravatar = () => {
    const { styles } = useStyles();
    const store = useStore();
    const { t } = useTranslation();

    return (
        <div className={styles.container}>
            <div className={styles.top}>
                <img
                    src="https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/project/3d-design/headimg.png"
                    style={{ width: '36px', height: '36px', borderRadius: '50%' }}
                />
                {store.userStore.userInfo?.username}
            </div>
            <div className={styles.line}></div>
            {/* {store.userStore.userInfo?.regSource !== 'aihouse' && (
                <div
                    className={styles.item}
                    onClick={() => {
                        window.open(AIDeskUrl);
                    }}
                >
                    {t('工作台')}
                </div>
            )} */}

            <div className={styles.item} onClick={logout}>
                {t('退出登录')}
            </div>
        </div>
    );
};

export default observer(Useravatar);