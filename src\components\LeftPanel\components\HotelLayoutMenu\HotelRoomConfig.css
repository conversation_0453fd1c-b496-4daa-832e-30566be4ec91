.hotel-room-config-container {
    font-family: Arial, sans-serif;
    width: 100%;
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.16);
    overflow: hidden;
  }
  .hotel-room-config-container input {
    line-height:  16px;
    border:1px solid #ccc;
  }
  .closeBtn {
    position: absolute;
    right : 20px;
    top : 10px;
    font-size: 20px;
    line-height: 20px;
  }
  .closeBtn:hover {
    background: rgba(0, 0, 0, 0.16);
  }
  .action-buttons button {
    margin-left: 10px;
    padding: 5px 10px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .design-requirements-banner {
    background-color: #ffd6e7;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
  }
  
  .design-requirements-banner span {
    display: block;
  }
  
  .config-content {
    padding: 15px;
  }
  
  .config-section {
    margin-bottom: 10px;
    background-color: #fff;
    padding: 5px;
    border-radius: 6px;
  }
  
  .config-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
  }
  
  .skip-button {
    float: right;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
  }
  
  .wall-thickness-input {
    display: flex;
    align-items: center;
  }
  
  .wall-thickness-input span:first-child {
    color: #999;
    margin-right: 10px;
  }
  
  .wall-thickness-input input {
    width: 60px;
    margin: 0 5px;
    padding: 5px;
  }
  
  .room-type-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
  }
  
  .room-type-table th, .room-type-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
  }
  .config-item-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;
    gap: 10px;
  }
  .config-item-list input {
    text-align: center;
    margin-left:5px;
  }
  .room-type-table th {
    background-color: #f9f9f9;
  }
  
  .room-type-table input {
    width: 80%;
    padding: 5px;
  }
  
  .room-ratio-note {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
  }
  .config-name {
    font-size: 13px;
  }
  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
  }
  
  .facility-option {
    display: flex;
    align-items: center;
  }
  
  .facility-note {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
  }
  
  .confirm-button {
    display: block;
    width: 100%;
    padding: 10px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
  }
  
  .confirm-button:hover {
    background-color: #40a9ff;
  }