export const V5XmlTemplate = `
<root>
    <AdjustGamma isAdjust="0" colorBleed="0"/>
    <VerInf Version="2.1"/>
    <State currentState="state2d"/>
    <RenderInfo Version="2_1">
      <AnimationSceneData version="1.0" value="bnVsbA=="/>
      <AnimationSettingData version="1.0" stickFloorTypeList="null" onCeilingTypeList="null" stickWallTypeList="null" cabinetTypeList="null" cabinetDoorWindowList="null" aluminumTypeList="null" aluminumDoorWindowTypeList="null" doorWindowTypeList="null"/>
      <SceneInfo lightSceneType="normal" lightTemplate="0" lightdomeIntensityValue="-1" isNormalAutoExposure="false" isPanoramaAutoExposure="false" isAerialAutoExposure="false" fillLightAffectSpecular="0"/>
      <QualityEffect type="1"/>
      <FalseColorGrading leftIndex="5" leftIntensity="1" rightIndex="22" rightIntensity="3000"/>
    </RenderInfo>
    <SetUpViewConfig Version="2_2">
      <FootLine show="true"/>
      <CellingLine show="true"/>
      <WallHeight WallHeight3D="280"/>
      <ProgramType show="false"/>
      <ProgramWorkLayer show="false"/>
    </SetUpViewConfig>
    <FntAreaInfo Version="2_2"/>
    <FurnitureInfo Version="2_1"/>
    <CuboidInfo Version="2_1"/>
    <GroupInfo Version="2_1"/>
    <ParameterInfo Version="2_1"/>
    <InWallInfo Version="2_2"/>
    <Config Version="2_2">
      <SprungRoof show="true"/>
      <LightLayer show="true"/>
      <WallHeight WallHeight3D="280"/>
      <CameraVo targetPosX="0" targetPosY="0" targetPosZ="0" posX="0" posY="0" posZ="180" rotateX="-1.5707963267948966" rotateY="0" rotateZ="0.7853981633974483" fov="1.46563" nearClipping="10" picIndex="0" aspectRatioType="0" timeLightType="0" cameraLensType="0" cameraLensAngle="0" isAdvanceLight="0" exposureValue="0" sunBrightness="正午/中" spotLightBrightness="12w/60w" spotLightTemperature="5000K" exposureV3="0.8" spotLightBrightnessV3="500" isCameraCut="0" cameraCutValue="100" sunLightEnable="1" sunAngle="-45" sunDirection="0" sunBrightnessV3="0.03" sunLightColor="16769984" sunSizeMutilplier="3" cameraName="null" radius="-1" createType="manual" autoSunEnabled="true" autoSunEntityId="-1"/>
    </Config>
    <CameraInfo Version="2_2"/>
    <BuildingBitmapInfo Version="2_2" BuildingBitmapUrl="" scaleX="undefined" scaleY="undefined"/>
    <BuildingBitmapVisibleInfo Version="2_2" isImportHomePlan="0"/>
    <CabinetLayout version="2" ParameterNum="0" LastParameterNum="undefined"/>
    <BrickLayout/>
    <TESTBRICK hasBrick="0"/>
    <CeilingLayout version="1">
      <CeilingErrorInfo>
        <errInfo>0</errInfo>
        <realCount>0</realCount>
        <expectCount>0</expectCount>
      </CeilingErrorInfo>
    </CeilingLayout>
    <waterElectric/>
    <sandPainter>
      <encodeErrInfo info="" version=""/>
      <decodeErrInfo info="" version=""/>
    </sandPainter>
    <MaterialIds value=""/>
    <swjia_scheme_5_0 form="XML">
      <SchemeRoot __protoflag__="0" _UIDN="13">
        <UIDMap UIDMapValueS=""/>
        <ChannelInfo/>
        <su>
          <suLoftEntitys/>
          <suMaterials/>
        </su>
        <BrickLayoutDesign versionS="v_0">
          <PaveBrick>
            <MaterialMap materialIdsU="NA" roomGapMaterialS=""/>
            <Entitys/>
          </PaveBrick>
        </BrickLayoutDesign>
        <WallInfo VersionS="v_0">
          <WallData uidS="3" StartXS="-5490" StartYS="3490" EndXS="-30" EndYS="3490" ThicknessS="240" HeightS="2800" IsBearS="false" wallUIDS="10" IsLockedS="false" isHalfWallS="false" bulgeS="0">
          </WallData>
          <WallData uidS="4" StartXS="-30" StartYS="3490" EndXS="-30" EndYS="-2630" ThicknessS="240" HeightS="2800" IsBearS="false" wallUIDS="12" IsLockedS="false" isHalfWallS="false" bulgeS="0">
          </WallData>
          <WallData uidS="5" StartXS="-5490" StartYS="-2630" EndXS="-30" EndYS="-2630" ThicknessS="240" HeightS="2800" IsBearS="false" wallUIDS="14" IsLockedS="false" isHalfWallS="false" bulgeS="0">
          </WallData>
          <WallData uidS="6" StartXS="-5490" StartYS="3490" EndXS="-5490" EndYS="-2630" ThicknessS="240" HeightS="2800" IsBearS="false" wallUIDS="16" IsLockedS="false" isHalfWallS="false" bulgeS="0">
          </WallData>
        </WallInfo>
        <SolidWoodDesign versionS="v_0">
          <Entitys/>
          <AttachedDatas/>
        </SolidWoodDesign>
        <ShowerRoom versionS="v0">
          <ShowerRoom>
            <Entities/>
            <Log logS="{&quot;subApp&quot;:&quot;bathRoom&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </ShowerRoom>
        </ShowerRoom>
        <WholeHouseDesign versionS="v_0">
          <Order NoS=""/>
          <CupBoard>
            <Entitys/>
            <Log logS="{&quot;subApp&quot;:&quot;cupboard&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </CupBoard>
          <Wardrobe>
            <Entitys/>
            <Log logS="{&quot;subApp&quot;:&quot;wardrobe&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </Wardrobe>
          <SystemCabinet>
            <Entitys/>
            <MaterialIDs idsS=""/>
            <Log logS="{&quot;subApp&quot;:&quot;systemCabinet&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </SystemCabinet>
          <BathCabinet>
            <Entitys/>
            <MaterialIDs idsS=""/>
            <Log logS="{&quot;subApp&quot;:&quot;bathCabinet&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </BathCabinet>
          <StainLessKnob/>
          <CProduceGlobalSchemeNode>
            <templates>
              <schemes/>
              <parts/>
              <accessorys/>
            </templates>
          </CProduceGlobalSchemeNode>
        </WholeHouseDesign>
        <Light>
          <Entitys/>
        </Light>
        <AluminumDoorWindow versionS="v_0">
          <Entitys/>
        </AluminumDoorWindow>
        <BIM_WE versionS="5.0.0">
          <Concerns versionS="5.0.0"/>
          <Systems versionS="5.0.0"/>
          <Pipes versionS="5.0.0"/>
          <Connectors versionS="5.0.0"/>
          <Relations versionS="5.0.0"/>
          <VentAirFlows versionS="5.0.0" ventAirFlowN="0" ventAirFlowLevelN="0"/>
          <AirCondCoolings versionS="5.0.0" totalCoolingCapacityN="0" continuityRatioN="0" recommendHPN="0"/>
        </BIM_WE>
        <CCeilingLayout versionS="v_0">
          <Entitys/>
          <Log/>
        </CCeilingLayout>
        <illuminationDesign versionS="v_0">
          <LightSceneNode/>
          <ParameterSceneNode/>
        </illuminationDesign>
        <Basic versionS="v_0">
          <FurnitureGroups/>
          <Furnitures/>
          <CMakeUpCeilings/>
          <Lights/>
          <LightTemplates selectIdS="" versionS="3.0"/>
          <MaterialIDs idsS=""/>
        </Basic>
        <BIM_WE_HUAWEI versionS="5.0.0">
          <CircuitConfigs versionS="5.0.0">
            <CircuitConfig circuitTypeS="PLCCircuits" idN="13631489" nameS="PLC回路1" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="2.5" linearWayS="CE" pipeColorN="255" remarksS=""/>
            <CircuitConfig circuitTypeS="PLCCircuits" idN="13631490" nameS="PLC回路2" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="2.5" linearWayS="CE" pipeColorN="65280" remarksS=""/>
            <CircuitConfig circuitTypeS="PLCCircuits" idN="13631491" nameS="PLC回路3" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="2.5" linearWayS="CE" pipeColorN="65535" remarksS=""/>
            <CircuitConfig circuitTypeS="MainFrameCircuits" idN="13631492" nameS="PLC强电箱供电" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="2.5" linearWayS="CE" pipeColorN="16095779" remarksS=""/>
            <CircuitConfig circuitTypeS="CtrlCircuits" idN="13631493" nameS="控制线B" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="1" cableSizeN="2.5" linearWayS="CE" pipeColorN="16711680" remarksS=""/>
            <CircuitConfig circuitTypeS="CtrlCircuits" idN="13631494" nameS="控制线B2" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="2" cableSizeN="2.5" linearWayS="CE" pipeColorN="16711680" remarksS=""/>
            <CircuitConfig circuitTypeS="CtrlCircuits" idN="13631495" nameS="干接点控制设备线" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="4" cableSizeN="0.75" linearWayS="CE" pipeColorN="58596" remarksS=""/>
            <CircuitConfig circuitTypeS="CtrlCircuits" idN="13631496" nameS="双绞屏蔽线" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="2" cableSizeN="0.75" linearWayS="CE" pipeColorN="9461808" remarksS=""/>
            <CircuitConfig circuitTypeS="CtrlCircuits" idN="13631497" nameS="0-10V单色温信号线" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="2" cableSizeN="0.75" linearWayS="CE" pipeColorN="15774720" remarksS=""/>
            <CircuitConfig circuitTypeS="CtrlCircuits" idN="13631498" nameS="0-10V双色温信号线" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="0.75" linearWayS="CE" pipeColorN="16711935" remarksS=""/>
            <CircuitConfig circuitTypeS="CtrlCircuits" idN="13631499" nameS="风机盘管控制线" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="7" cableSizeN="0.75" linearWayS="CE" pipeColorN="9002030" remarksS=""/>
            <CircuitConfig circuitTypeS="NetCircuits" idN="13631500" nameS="网络设备布线C" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="CAT6" cableInsulatingMatS="" cableWireS="" cableNumN="1" cableSizeN="2.5" linearWayS="CE" pipeColorN="65280" remarksS=""/>
            <CircuitConfig circuitTypeS="NetCircuits" idN="13631501" nameS="网络设备布线C2" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="CAT6" cableInsulatingMatS="" cableWireS="" cableNumN="2" cableSizeN="2.5" linearWayS="CE" pipeColorN="65280" remarksS=""/>
            <CircuitConfig circuitTypeS="MusicCircuits" idN="13631502" nameS="音乐线路PLC1" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="2.5" linearWayS="CE" pipeColorN="255" remarksS=""/>
            <CircuitConfig circuitTypeS="MusicCircuits" idN="13631503" nameS="音乐线路PLC2" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="2.5" linearWayS="CE" pipeColorN="65280" remarksS=""/>
            <CircuitConfig circuitTypeS="MusicCircuits" idN="13631504" nameS="音乐线路PLC3" pipeMatS="JDG" pipeRadiusS="DN20" cableTypeS="" cableInsulatingMatS="ZC" cableWireS="BV" cableNumN="3" cableSizeN="2.5" linearWayS="CE" pipeColorN="65535" remarksS=""/>
          </CircuitConfigs>
          <PCLCircuitCorrelationSchemes versionS="5.0.0"/>
          <HW_ControlAssociations versionS="5.0.0"/>
          <HW_DeviceInfos versionS="5.0.0"/>
          <HW_NetAssociations versionS="5.0.0"/>
          <HW_KeyConfigs versionS="5.0.0"/>
          <HW_Sensors versionS="5.0.0"/>
          <HW_MusicRelations versionS="5.0.0"/>
        </BIM_WE_HUAWEI>
      </SchemeRoot>
    </swjia_scheme_5_0>
    <ModifyInfo Version="3" AutoSave="undefined" Complete="true" isQualityExistFlag="0"/>
    <Logs ServerTime="2025-06-11 23:42:25">
      <version v="5.0.0.102537"/>
      <Log type="INFO" date="06-11 23:42:25" name="方案编码" message="开始编码方案, id=: " time="114944" className="swjia_base_common.scheme.ProjectDataManager"/>
      <Log type="INFO" date="06-11 23:42:25" name="成品素材模型编码：" message=" 预计编码家具数量0 实际编码家具数量0 预计编码组合数量0 实际编码组合数量0" time="114946" className="swjia_base_common.scheme.SchemeFurnitureInfo"/>
      <Log type="INFO" date="06-11 23:42:25" name="方案编码" message="预计编码数量0 实际编码数量0" time="114949" className="swjia_plugin.parameter.CabinetLayoutEncoder"/>
      <Log type="INFO" date="06-11 23:42:25" name="铝门窗模块编码：" message="" time="114961" className="swjia_alucustomization.service.main.CAluSchemeTreeNode"/>
    </Logs>
</root>

`