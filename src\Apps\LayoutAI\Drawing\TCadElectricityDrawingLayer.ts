
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { ZPolygon } from "@layoutai/z_polygon";
import { TAppManagerBase } from "../../AppManagerBase";

/**
 *  水电开关图层
 */
export class TCadElectricityDrawingLayer extends TDrawingLayer
{
    


    _target_poly : ZPolygon;

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadElectricity,manager);
        this._target_poly = null;
        this._dirty = true;
    
    }


     _updateLayerContent(): void {

    }
    
    onDraw(): void {
       
        if(this.layout_container._drawing_layer_mode === "SingleRoom") return; // 单空间模式 不绘制

        let entities = this.layout_container.getCandidateEntities(["Furniture"],{target_realtypes:["Electricity"]});

        for(let entity of entities)
        {
            if(entity.is_selected)
            {
                continue;
            }
            entity.drawEntity(this.painter,{is_draw_figure:true});
            
        }
        this.painter.strokeStyle= "#a42";
        this.painter.fillStyle = "#fff";
    }
}