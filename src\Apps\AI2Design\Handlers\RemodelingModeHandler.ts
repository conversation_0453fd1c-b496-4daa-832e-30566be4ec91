import { I_MouseEvent } from "@layoutai/z_polygon";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { AI2DesignBasicModes, AI2DesignManager } from "../AI2DesignManager";
import { HouseStructureSelSubHandler } from "./SubHandlers/RemodelingSubHandlers/HouseStructureSelSubHandler";
import { RoomSpaceDesignSubHandler } from "./SubHandlers/RemodelingSubHandlers/RoomSpaceDesignSubHandler";
import { ResultShowSubHandler } from "./SubHandlers/RemodelingSubHandlers/ResultShowSubHandler";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { I_SwjXmlScheme } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { EventName } from "@/Apps/EventSystem";

// 添加一个新的接口来存储图层状态
interface LayerStates {
    CadFloorLayer: boolean;
    CadRoomFrameLayer: boolean;
    CadFurnitureLayer: boolean;
    RemodelingLayer: boolean;
}

/**
 *   AI拆改模式
 */
export class RemodelingModeHandler extends AI2BaseModeHandler {

    public original_swj_layout_data: I_SwjXmlScheme;
    private _remodeling_swj_layout_data: I_SwjXmlScheme;

    constructor(manager: AI2DesignManager) {
        super(manager, AI2DesignBasicModes.RemodelingMode);
        this._cad_default_sub_handler = new HouseStructureSelSubHandler(this);
        this._sub_handlers[LayoutAI_Commands.SelHouseStructure] = this._cad_default_sub_handler;
        // this._sub_handlers[LayoutAI_Commands.SelHouseStructure] = new HouseStructureSelSubHandler(this);
        this._sub_handlers[LayoutAI_Commands.RoomSpaceDesign] = new RoomSpaceDesignSubHandler(this as any);
        this._sub_handlers[LayoutAI_Commands.ShowRemodelingResult] = new ResultShowSubHandler(this as any);
    }

    enter(state?: number): void {
        super.enter(state);

        // 设置拆改模式的图层状态
        this.manager.layer_CadFloorLayer.visible = true;
        this.manager.layer_CadRoomFrameLayer.visible = true;
        this.manager.layer_CadFurnitureLayer.visible = false;
        this.manager.layer_LightingLayer.visible = false;
        this.manager.layer_OutLineLayer.visible = true;
        this.manager.layer_CadRoomNameLayer.visible = true;
        this.manager.layer_CadEzdxfLayer.visible = true;
        this.manager.layer_CadCabinetLayer.visible = false;
        this.manager.layer_CadCopyImageLayer.visible = false;

        // // 保存原始数据
        this.original_swj_layout_data = this.manager.layout_container.current_swj_layout_data;
        this._remodeling_swj_layout_data = this.manager.layout_container.current_swj_layout_data;
        this.manager.layout_container.fromXmlSchemeData(this._remodeling_swj_layout_data);

        this.manager.onLayerVisibilityChanged();
        this.painter._p_sc = 0.05;
        this.manager.layout_container.focusCenter();

        if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.updateCandidateRects();
        }
        this.runCommand(LayoutAI_Commands.SelHouseStructure);
        LayoutAI_App.emit(EventName.SetExitBarVisible, {
            visible: true,
            title: LayoutAI_App.t('AI拆改模式'),
            onExit: () => this._onExitBar()
        });
        this.update();
    }

    leave(state?: number): void {
        super.leave(state);
    }
    private _leaveSubHandler(swj_layout_data: I_SwjXmlScheme | null) {
        if (this._active_sub_handler) {
            this._active_sub_handler.leave();
        }
        if (swj_layout_data) {
            this.manager.layout_container.fromXmlSchemeData(swj_layout_data);
        }
        this.manager.onLayerVisibilityChanged();
        this.manager.layout_container.focusCenter();
        this.painter._p_sc = 0.05;
        this.update()
        this._remodeling_swj_layout_data = null;
        this.manager.layout_container.remodeiling_data = null;
    }


    onmousedown(ev: I_MouseEvent): void {

        if (this._active_sub_handler) {
            this._active_sub_handler.onmousedown(ev);
        }
        else if (this._cad_default_sub_handler) {
            // 默认方法mouse_down后, 有可能触发进入新的active_handler
            this._cad_default_sub_handler.onmousedown(ev);

            this._last_mouse_down_event = ev;
            if (this._active_sub_handler) {
                this._active_sub_handler.onmousedown(ev);
            }


        }

        this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

        this._is_painter_center_moving = false;
        this._is_moving_element = false;
        // super.onmousedown(ev);

    }
    onmousemove(ev: I_MouseEvent): void {

        if (this._active_sub_handler) {
            super.onmousemove(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onmousemove(ev);
            }

            super.onmousemove(ev);
        }
    }
    onmouseup(ev: I_MouseEvent): void {
        if (this._active_sub_handler) {
            super.onmouseup(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onmouseup(ev);
            }
            super.onmouseup(ev);
        }
        this._last_mouse_down_event = null;

    }

    onwheel(ev: WheelEvent): void {
        if (this._active_sub_handler) {
            super.onwheel(ev);
            this._active_sub_handler.onwheel(ev);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.onwheel(ev);
            }
            super.onwheel(ev);
        }
    }

    drawCanvas(): void {
        if (this.manager.layer_DefaultBatchLayer) {
            let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
            this.manager.layer_DefaultBatchLayer.drawLayer(true);
        }

        if (this.manager.layer_RemodelingLayer) {
            this.manager.layer_RemodelingLayer.drawLayer(true);
        }

        if (this._active_sub_handler) {
            this._active_sub_handler.drawCanvas();
        }
        else if (this._cad_default_sub_handler) {
            this._cad_default_sub_handler.drawCanvas();
        }
    }

    async runCommand(cmd_name: string): Promise<void> {
        if(cmd_name == LayoutAI_Commands.Undo) {
            this.manager.undo();
            this.update();
        } else if (cmd_name == LayoutAI_Commands.Redo) {
            this.manager.redo();
            this.update();
        }else{
            super.runCommand(cmd_name);
            if (this._active_sub_handler) {
                this._active_sub_handler.runCommand(cmd_name);
            }
            else if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.runCommand(cmd_name);
            }
        }
    }
    handleEvent(event_name: string, event_param: any): void {
        super.handleEvent(event_name, event_param);
        if (event_name === LayoutAI_Events.ApplyRemodelingScheme) {
            this._applyRemodelingScheme();
        }
    }
    private _applyRemodelingScheme() {
        this._leaveSubHandler(null);
        LayoutAI_App.emit(EventName.SetExitBarVisible, { visible: false });
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
    }

    private _onExitBar(): void {
        this._leaveSubHandler(this.original_swj_layout_data);
        LayoutAI_App.emit(EventName.SetExitBarVisible, { visible: false });
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
    }

}