type ListItem = {
    area: number;
    layoutSchemeName: string;
    id: string;
    coverImage: string;
    updateDate: string;
    address?: string;
    province?: string;
    city?: string;
    district?: string;
  };

type DistrictEntry = {
    value: string;
    label: string;
    children?: DistrictEntry[];
  };

const getFullAddress = (item: ListItem, districtData: any) => {
    if (!districtData) return item.address;

    const findLabel = (list: DistrictEntry[], value: string): string => {
    for (const entry of list) {
        if (entry.value === value) {
        return entry.label;
        }
        if (entry.children) {
        const childLabel = findLabel(entry.children, value);
        if (childLabel) {
            return childLabel;
        }
        }
    }
    return '';
    };

    const provinceName = item.province ? findLabel(districtData, item.province) : '';
    const cityName = item.city ? findLabel(districtData, item.city) : '';
    const districtName = item.district ? findLabel(districtData, item.district) : '';
    const address = item.address || '';

    return `${provinceName} ${cityName} ${districtName} ${address}`;
};

export {getFullAddress}