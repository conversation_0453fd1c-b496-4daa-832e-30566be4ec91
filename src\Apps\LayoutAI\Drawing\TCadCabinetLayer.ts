import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { LayoutAI_App } from '../../LayoutAI_App';
import { TAppManagerBase } from "../../AppManagerBase";
import { TFurnitureEntity } from "../Layout/TLayoutEntities/TFurnitureEntity";
import { DrawingFigureMode } from "../Layout/IRoomInterface";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";

export class TCadCabinetLayer extends TDrawingLayer
{    

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadCabinet,manager);
        this._manager = manager;
    }



    get furnitureRects() {
        return this.ai_cad_data._figure_rects;
    }

    onDraw()
    {   


        if(this.ai_cad_data) {
            LayoutAI_App.instance._haveData = true;
        };

        this.painter.strokeStyle= "#f00";

        let entities = this.layout_container.getCandidateEntities(["Furniture"],{target_realtypes:["Cabinet"]});

        entities.sort((a,b)=>{
            let a_entity = a as TFurnitureEntity;
            let b_entity = b as TFurnitureEntity;

            return (a_entity?._drawing_order||0) - (b_entity?._drawing_order||0);
        })
        for(let entity  of entities)
        {
            if(entity.type== "Group") continue;
            if(entity)
            {
                if(entity.is_selected)
                {
                    continue; 
                }
                entity.drawEntity(this.painter,{
                    is_draw_figure:true,
                    is_draw_outline:this._manager.layout_container.drawing_figure_mode === DrawingFigureMode.Outline,
                    is_draw_texture:this._manager.layout_container.drawing_figure_mode === DrawingFigureMode.Texture
                });   
            }

        }

        this.layout_container._room_entities.forEach((entity)=>{
            if(entity.tabletop_entities)
            {
                entity.tabletop_entities.forEach(tabletop=>{
                    if(!entities.includes(tabletop))
                    {
                        return;
                    }
                    tabletop.drawEntity(this.painter,{is_draw_figure:true});
                })
            }
        })
    }
}
