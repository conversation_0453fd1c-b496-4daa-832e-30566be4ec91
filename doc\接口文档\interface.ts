

/**
 *  
 */
const api_querySearchingCabinet = "querySearchingCabinet";
export interface IQuerySearchingCabinet
{
    /**
     *  长度
     */
    length ?: number;
    /**
     *  最小长度
     */
    minLength ?: number;
    /**
     *  最大长度
     */
    maxLength ?: number;

    /**
     *  深度
     */
    depth ?: number;
    /**
     *  最小长度
     */
    minDepth ?: number;
    /**
     *  最大长度
     */
    maxDepth ?: number;

    /**
     *  柜子模块： 衣柜、玄关柜、餐边柜 ... 
     */
    category ?: string;

    /**
     *  可扩展的标签字段, 会再详细定义字符串格式
     */
    tags ?: string; 

    /**
     *   分段步长---默认为10mm。
     * 
     *    --- 当一个参数化柜可以自由变化时,  也将其能变化的结果离散化。
     * 
     *      比如 当一个柜子长度范围[1000,2000]。 那么按步长为10mm来离散化, 就是[1000,1010,1020,...,1990,2000]
     */
    segment_step ?: number;  
}
export interface ICabinetInfo
{
    /**
     *  柜子的ID
     */
    cabinet_id : number; 

    length : number;
    depth :number;
    height : number;
    lengthRange ?: number[];
    depthRange ?: number[];
    heightRange ?: number[];
    availableSizes ?: {length:number,depth:number;height:number}[];
    /**
     *  柜子的加密XML。
     */
    xml_data ?: string;

    corver_img ?: string; // 封面图片地址
}

export interface IResultOfSearchingCabinet
{
    candidates : ICabinetInfo[];
}

const api_queryCabinetMeshById = "queryCabientMeshById";
export interface IQueryCabinetMeshById
{
    /**
     *  要传入柜子的ID
     */
    cabinet_id : number;

    length : number;

    depth : number;

    height : number;

    params ?: any; // 扩展的参数属性
}

/**
 *  3D模型用glb可能最方便(材质可以encode到glb里面)
 */
export interface IResultOfCabinetMesh
{
    glb_url ?: string;  // glb的cdn地址

    glb_base64 ?: string; // 或者直接范围glb转化成base64后的数据;

    glb_data ?: Uint8Array;  // 或者直接传二进制流数据
}
