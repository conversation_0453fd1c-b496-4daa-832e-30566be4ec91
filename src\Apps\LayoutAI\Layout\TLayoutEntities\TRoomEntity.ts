import { <PERSON>, Mesh, Vector3, Vector3<PERSON><PERSON> } from "three";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { checkIsMobile } from "@/config";
import { CadColorIndex, ZEdge, ZPolygon, ZRect, compareNames } from "@layoutai/z_polygon";
import { SceneMaterialMode } from "@layoutai/model3d_api";
import { EventName } from "../../../EventSystem";
import { LayoutAI_App, LayoutAI_Events } from "../../../LayoutAI_App";
import { get_room_poly_name, get_room_rect_of_room_poly, get_window_of_win_rect, set_room_poly_name } from "../../AICadData/SwjDataBasicFuncs";
import { I_SwjRoom } from "../../AICadData/SwjLayoutData";
import { TPainter } from "../../Drawing/TPainter";
import { MeshBuilder } from "../../Scene3D/MeshBuilder";
import { MeshName, SwitchConfig } from "../../Scene3D/NodeName";
import { Scene3D } from "../../Scene3D/Scene3D";
import { Utils3D } from "../../Scene3D/Utils3D";
import { InnerWallGeometryBuilder } from "../../Scene3D/builder/InnerWallGeometryBuilder";
import { SimpleFloorMaterial } from "../../Scene3D/materials/entityMaterials/SimpleFloorMaterial";
import { SimpleInnerWallMaterial } from "../../Scene3D/materials/entityMaterials/SimpleInnerWallMaterial";
import { IRoomEntityRealType, IRoomEntityType, IRoomFloorColor, KeyEntity } from "../IRoomInterface";
import { WPolygon } from "../TFeatureShape/WPolygon";
import { FigureCategoryManager } from "../TFigureElements/FigureCategoryManager";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TRoom } from "../TRoom";
import { TRoomShape } from "../TRoomShape";
import { IPropertyUI } from "./IPropertyUI";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TBaseGroupEntity } from "./TBaseGroupEntity";
import { TViewCameraEntity } from "./TExtDrawingElements/TViewCameraEntity";
import { TFurnitureEntity } from "./TFurnitureEntity";
import { TRoomCeilingEntity } from "./TRoomCeilingEntity";
import { TSkirtingBoardEntity } from "./TSkirtingBoardEntity";
import { TStructureEntity } from "./TStructureEntity";
import { TSubSpaceAreaEntity } from "./TSubSpaceAreaEntity";
import { TTableTopEntity } from "./TTableTopEntity";
import { TWindowDoorEntity } from "./TWinDoorEntity";
import { LayoutContainerUtils } from "./utils/LayoutContainerUtils";
import { splitSpaceForLivingRoom } from "./utils/SplitSpaceForLivingRoom";

/**
 *   空间区域: 由房间完成面构成的多边形
 *      --- 直接交互的对象是 room_poly 
 *      --- 但也会跟房间基本对象有一个联动关系
 */
export class TRoomEntity extends TBaseEntity {

    _name_uid: number;
    /**
     *  空间轮廓多边形
     */
    _room_poly: ZPolygon;

    /**
     *  空间名称
     */
    roomname: string;

    /**
     *  子功能分区
     */
    _sub_room_areas: TSubSpaceAreaEntity[];

    _main_rect: ZRect;


    _win_rects: ZRect[];


    _wall_rects: ZRect[];

    _structure_rects: ZRect[];

    _area: number;


    _hasCeilingFootLineB: boolean;
    _hasFloorFootLineB: boolean;

    _neighbor_roomareas: TRoomEntity[];

    /**
     *  这个户型所有房间的数量
     */
    _num_of_all_rooms: number;

    /**
     *  绑定的TRoom对象
     */
    _room: TRoom;
    _swj_room_data: I_SwjRoom;


    _room_id?: number;

    isSingle?: boolean;

    /**
     *  视角合集
     */
    _view_cameras: TViewCameraEntity[];

    protected _outter_wall_poly: ZPolygon;

    public static MIN_CEILING_HEIGHT: number = 200;
    public static MAX_CEILING_HEIGHT: number = 400;
    public static DEFAULT_CEILING_HEIGHT: number = TRoomEntity.MAX_CEILING_HEIGHT;

    /**
     *  地铺厚度
     */
    protected _floor_thickness: number = 0;

    /**
     *  吊顶下吊高度
     */
    protected _ceiling_height: number = TRoomEntity.MAX_CEILING_HEIGHT;
    /**
     *  楼层高度
     */
    private _storey_height: number = 2800;

    /**
     *  空间内, 最大柜体高度
     */
    protected _max_cabinet_height: number = 2400;
    protected _is_auto_ceiling: boolean = true;
    /**
     *  分区是否跟踪生成
     */
    private _is_auto_sub_area: boolean = false;



    protected _inner_wall_mesh: Mesh;
    protected _floor_tile_mesh: Mesh;
    protected _tabletop_group: Group;
    
    protected _skirting_group : Group;

    /**
     *  饰品的组合
     */
    protected _decoration_group : Group;

    /**
     *  临时处理,放置墙饰图元
     */
    private _decortaion_elements: TFigureElement[];




    _test_polygon: ZPolygon;
    new_polys: ZPolygon[];
    _test_edge: ZEdge;
    _testRect: ZRect;

    private _furniture_entities: TFurnitureEntity[];

    private _tabletop_entities: TTableTopEntity[];

    private _skirting_boards_entities : TSkirtingBoardEntity[];

    public livingSpaceInfo: { [key: string]: ZRect[] };

    public isShowSpace: boolean;


    private _room_ceiling_entity: TRoomCeilingEntity;

    static readonly EntityType: IRoomEntityType = "RoomArea";
    static readonly EntityName = "RoomEntity";
    static readonly PolyAttachedName = "RoomEntity_Name";
    constructor(poly: ZPolygon, name: string = "未命名") {
        super();
        this._room_poly = poly;
        this._name_uid = -1;
        this._main_rect = null;
        this._area = 0;
        this.type = TRoomEntity.EntityType;
        this._texture_name = this.type;

        this._hasCeilingFootLineB = false;
        this._hasFloorFootLineB = false;
        this._win_rects = [];
        this._structure_rects = [];
        this._wall_rects = [];
        this._outter_wall_poly = null;
        this._neighbor_roomareas = [];

        this._room = null;
        this._room_id = 0;
        this.name = name;
        this.aliasName = name;
        this._num_of_all_rooms = 0;
        this.title = LayoutAI_App.t("空间信息");
        this._test_polygon = null;
        this.new_polys = [];
        this._test_edge = null;
        this._testRect = null;
        this._room_ceiling_entity = null;

        this._priority_for_selection = 2;
        this._sub_room_areas = [];
        this._furniture_entities = [];
        this._tabletop_entities = [];
        this._skirting_boards_entities = [];
        this._decortaion_elements = [];
        this._view_cameras = [];
        this.livingSpaceInfo = null;
        this.isShowSpace = false;

        this.is_auto_sub_area = LayoutAI_App.instance?.Configs?.default_is_auto_sub_area || false;

        this._room_ceiling_entity = new TRoomCeilingEntity(this);
    }
    static getOrMakeEntityOfCadRect(room_poly: ZRect): TRoomEntity {
        let roomarea: TRoomEntity = room_poly._attached_elements[KeyEntity] || null;
        if (!roomarea) {
            roomarea = new TRoomEntity(room_poly, get_room_poly_name(room_poly) || "未命名");
            roomarea.update();
            room_poly._attached_elements[KeyEntity] = roomarea;
        }
        roomarea._rect._attached_elements[KeyEntity] = roomarea;
        return roomarea;
    }
    static RegisterGenerators() {
        TBaseEntity.RegisterPolyEntityGenerator(this.EntityType, this.getOrMakeEntityOfCadRect)
    }
    get inner_wall_mesh() {
        return this._inner_wall_mesh;
    }

    get floor_tile_mesh() {
        return this._floor_tile_mesh;
    }
    get ceiling_mesh() {
        return this.room_ceiling_entity?._mesh3d;
    }
    get floor_thickness() {
        return this._floor_thickness;
    }

    get storey_height(): number {
        return this._storey_height;
    }
    set storey_height(value: number) {
        this._storey_height = value;
    }
    set floor_thickness(t: number) {
        this._floor_thickness = t;
        this.onRoomFloorThicknessChanged();
    }

    get ceiling_height() {
        return this._ceiling_height;
    }
    set ceiling_height(t: number) {
        this._ceiling_height = t;
        if (this._room) {
            this._room.ceilingHeight = t;
        }
    }
    public get room_ceiling_entity(): TRoomCeilingEntity {
        return this._room_ceiling_entity;
    }

    get max_cabinet_height() {
        return this._max_cabinet_height;
    }
    set max_cabinet_height(t: number) {
        this._max_cabinet_height = t;
    }

    get is_auto_ceiling() {
        return this._is_auto_ceiling;
    }
    get decoration_elements(): TFigureElement[] {
        return this._decortaion_elements;
    }


    set is_auto_ceiling(t: boolean) {

        this._is_auto_ceiling = t;
    }
    get is_auto_sub_area(): boolean {
        return this._is_auto_sub_area;
    }
    set is_auto_sub_area(value: boolean) {
        this._is_auto_sub_area = value;
    }
    get name() {
        return LayoutAI_App.t(this._name);
    }
    set name(str: string) {
        if (str.includes("/")) {
            str = str.split("/")[0];
        }
        this._name = str;

        this.roomname = TRoom.getRoomTypeByName(this._name);
        set_room_poly_name(this._room_poly, this.name);

        if (compareNames([this.roomname], [LayoutAI_App.t("厨房"), LayoutAI_App.t("卫生间")]) == 1 || this._name == LayoutAI_App.t("未命名")) {
            this._hasCeilingFootLineB = false;
            this._hasFloorFootLineB = false;
        }
        else {
            this._hasCeilingFootLineB = true;
            this._hasFloorFootLineB = true;
        }

        if (this._room != null) {
            this._room.updateName(this._name);
            this._room.checkIsSelectable();
        }
    }

    get aliasName() {
        return this._aliasName;
    }
    set aliasName(str: string) {
        this._aliasName = str;
        if (this._room) {
            this._room.aliasName = str;
        }
    }

    get room_type() {
        return this.roomname;
    }

    set room_type(roomname: string) {
        this.roomname = roomname;
    }

    get tabletop_entities() {
        return this._tabletop_entities;
    }

    get skirting_boards_entities()
    {
        return this._skirting_boards_entities;
    }

    get skirting_group()
    {
        return this._skirting_group;
    }
    get room_id() {
        return this._room_id;
    }
    bindEntity(): void {
        if (this._room_poly) {
            TBaseEntity.bindEntityOfPolygon(this._room_poly, this);
        }
        if (this._rect) {
            TBaseEntity.bindEntityOfPolygon(this.rect, this);
        }
        this.setRectProperties();
    }


    updateTRoomPoly() {
        if (!this._room) {
            this._room = this.makeTRoom([], true);
        }
        this._room.points = this._room_poly.positions.map(p => p.toArray());
        this._room.initRoomShape();
        this._room.roomname = this.roomname;
    }
    getPointdistanceToBoundary(point: Vector3Like): number {
        let dist = this._room_poly.distanceToPoint(point);

        return dist;
    }

    _updateDecoration()
    {
        if(!this._mesh3d) return;
        if(!this._decoration_group)
        {
            this._decoration_group = new Group();
            this._decoration_group.name = MeshName.DecorationGroup;

        }
        this._mesh3d.add(this._decoration_group);

        this._decoration_group.remove(...this._decoration_group.children);


        let wallDecorations :TFigureElement[] = [];
        let room = this._room;
        if(!room || !room._furniture_list) return;
        wallDecorations.push(...room._furniture_list.filter(decoration => !decoration.furnitureEntity && compareNames([decoration.category], ["墙饰"])));

        wallDecorations.push(...this.decoration_elements);
        wallDecorations.forEach((ele)=>{
            ele._room = room;

            ele.updateMesh3D();

            // if(ele._simple_mesh3D)
            // {
            //     this._decoration_group.add(ele._simple_mesh3D);
            // }
            if(ele._solid_mesh3D)
            {
                this._decoration_group.add(ele._solid_mesh3D);
            }
        
        });



        


    }
    updateMesh3D(mode: SceneMaterialMode = "WhiteModel") {

        if (!this._mesh3d) {
            this._mesh3d = new Group();
            this._floor_tile_mesh = null;
            this._inner_wall_mesh = null;
        }

        if (this._mesh3d) {
            this._mesh3d.remove(...this._mesh3d.children);
        }




        this._updateTileMesh();
        this._mesh3d.add(this._floor_tile_mesh);

        this._updateInnerWallMesh();
        this._mesh3d.add(this._inner_wall_mesh);

        this._updateCeilingMesh();
        this._mesh3d.add(this.ceiling_mesh);

        this._updateTableTopMesh3D();

        this._updateSkirtingBoards3D();
        this._updateDecoration();
        // this._inner_wall_mesh.visible = false;
        // console.log(this._mesh3d.children);
        // this._mesh3d.receiveShadow = true;

        // console.log(this._mesh3d);
        return this._mesh3d;
    }

    /**
     *  生成吊顶网格
     */
    public _updateCeilingMesh() {
        if (!this.room_ceiling_entity) return null;
        return this.room_ceiling_entity.updateMesh3D();
    }

    public _updateInnerWallMesh() {

        // inner_wall_geometry

        if (!this._mesh3d) return;

        if (!this._inner_wall_mesh) {
            let inner_wall_material = SimpleInnerWallMaterial.create();
            // inner_wall_material.side = FrontSide;
            this._inner_wall_mesh = new Mesh(InnerWallGeometryBuilder.build(this), inner_wall_material);
            this._inner_wall_mesh.name = MeshName.InnerWall;
            this._inner_wall_mesh.receiveShadow = true;
            MeshBuilder.bindMeshMaterials(this._inner_wall_mesh, { color_as_standard: false });
            this._mesh3d.add(this._inner_wall_mesh);
        }
        else {
            this._inner_wall_mesh.geometry = InnerWallGeometryBuilder.build(this);
        }

        if (this._inner_wall_mesh) {

            if (this._room?.wallTexture?._matched_material) {
                this._room.wallTexture._solid_mesh3D = this._inner_wall_mesh;
                (LayoutAI_App.instance).scene3DManager.onElementUpdate(this._room.wallTexture, { updateTexture: true })
            }
        }
    }

    public _updateTileMesh() {
        if (!this._floor_tile_mesh) {
            this._floor_tile_mesh = Scene3D.makePolyMesh(this._room_poly.clone().expandPolygon(60), SimpleFloorMaterial.create());
            this._floor_tile_mesh.name = MeshName.Floor;
            this._floor_tile_mesh.receiveShadow = SwitchConfig.shadowSwitch;

            MeshBuilder.bindMeshMaterials(this._floor_tile_mesh, { color_as_standard: false });
        }
        else {
            this._floor_tile_mesh.geometry = Scene3D.makePolyGeometry(this._room_poly.clone().expandPolygon(60));
        }

        if (this._mesh3d && this._floor_tile_mesh) {

            if (this._room?.tile?._matched_material) {
                this._room.tile._solid_mesh3D = this.floor_tile_mesh;
                this._room.tile.fill_color = IRoomFloorColor[this.room_type] || "#aaa";
                (LayoutAI_App.instance).scene3DManager.onElementUpdate(this._room.tile, { updateTexture: true })
            }
        }
    }

    public _updateTabletopEntities(force:boolean=false) {
        if (!this._room) return;

        if(force || !this.tabletop_entities || this.tabletop_entities.length == 0)
        {
            // 先默认强制更新
            this._tabletop_entities = [];

            this._tabletop_entities = TTableTopEntity.updateTableTopEntitiesInRoom(this._room, true);
        }

    }   

    public _updateTableTopMesh3D() {

        this._updateTabletopEntities();

        // console.log(this.roomname, "TableTop",this._tabletop_entities.length);
        if (this._tabletop_entities.length == 0) {
            if (this._mesh3d && this._tabletop_group) {
                this._mesh3d.remove(this._tabletop_group);
            }
            delete this._tabletop_group;
            return;
        }
        if (!this._tabletop_group) {
            this._tabletop_group = new Group();
            this._tabletop_group.name = "GroupTableTop";
            this._tabletop_group.userData.category = "房间-台面";
        }
        if (this._mesh3d) {
            this._mesh3d.add(this._tabletop_group);
        }
        this._tabletop_group.remove(...this._tabletop_group.children);

        this._tabletop_entities.forEach((entity) => {
            entity.updateMesh3D();
            this._tabletop_group.add(entity._mesh3d);
        });
    }

    public _updateSkirtingBoardEntities(force:boolean = false)
    {

        if(force || !this._skirting_boards_entities || this._skirting_boards_entities.length == 0)
        {
            // 先默认强制更新
            this._skirting_boards_entities = [];

            let floorCabinets = this._room.furnitureList.filter((ele)=>ele.category.includes("地柜") && !ele.category.includes("侧封")).map((ele)=>ele.rect);
            let highCabinets = this._room.furnitureList.filter((ele)=>ele.category.includes("高柜") || (ele.category.includes("地柜")&& ele.category.includes("侧封"))).map((ele)=>ele.rect);
            if(floorCabinets.length > 0)
            {
                this._skirting_boards_entities = TSkirtingBoardEntity.makeSkirtingBoardsFromCabinetRects(floorCabinets,highCabinets,this._room_poly);
                
            }

        }
    }

    public _updateSkirtingBoards3D()
    {
        this._updateSkirtingBoardEntities(true);
        if (this._skirting_boards_entities.length == 0) {
            if(this._skirting_group)
            {
                this._skirting_group.removeFromParent();
                Utils3D.disposeObject(this._skirting_group);
                delete this._skirting_group;
                return;
            }

        }

        if(!this._skirting_group)
        {
            this._skirting_group = new Group();
            this._skirting_group.name = "SkirtingBoardGroup";
            this._skirting_group.userData.category = "房间-踢脚线";
        }
        if(this._mesh3d)
        {
            this._mesh3d.add(this._skirting_group);
        }
        this._skirting_group.remove(...this._skirting_group.children);
        this._skirting_boards_entities.forEach((entity) => {
            entity.updateMesh3D();
            this._skirting_group.add(entity._mesh3d);
        });
    }
    makeTRoom(furniture_entities: TFurnitureEntity[], force: boolean = true) {
        const roomLocked = this._room ? this._room.locked : false;
        const scope_series_map = this._room ? this._room._scope_series_map : null
        if (!this._room) force = true;  //如果没有绑定TRoom, 那么久应该是强制force状态
        if (force) this._room = null;

        let entity = this;
        let room_poly = entity._room_poly;
        if (room_poly.orientation_z_nor.z > 0) return null;
        let points: number[][] = [];
        for (let v of room_poly.vertices) {
            points.push([v.pos.x, v.pos.y, v.pos.z]);
        }
        let room_name = entity.name;

        entity._room = force ? new TRoom({
            schemaName: "",
            roomname: room_name,
            points: points,
            uuid: entity._uuid
        }) : entity._room;
        let room = entity._room;
        // entity._room.locked = roomLocked; // 这个应该不需要，会导致图元锁定状态不一致




        entity.updateInRoomProperties();


        room.uuid = entity._uuid;
        room.points = points;
        room.roomname = room_name;
        room._room_entity = entity;
        room.name = entity.name;
        room.roomname = entity.roomname;
        room.aliasName = entity.aliasName;
        room.room_size = '' + (entity as TRoomEntity)._area.toFixed(2);
        room.area = entity._area;
        // 要设置UID!!!
        room.uid = '' + entity.uidN;
        if (!entity.uidN) {
            room.uid = entity.uidS || "";
        }
        room.room_id = entity._room_id || room._t_id;
        room.ceilingHeight = this.ceiling_height;
        room.windows = [];
        for (let win of entity._win_rects) {
            let entity = TBaseEntity.getOrMakeEntityOfCadRect(win) as TWindowDoorEntity;
            if (entity) {
                let data = entity.toWindowData();
                room.windows.push(data);

            }
        }


        room.flues = [];
        room.pillars = [];
        room.pipes = [];
        room.platforms = [];

        for (let structure of entity._structure_rects) {
            let structure_entity = TStructureEntity.getOrMakeEntityOfCadRect(structure) as TStructureEntity;
            if (structure_entity.realType == "Flue") {
                room.flues.push(structure_entity.exportSimpleData());
            }
            else if (structure_entity.realType == "Pillar") {
                room.pillars.push(structure_entity.exportSimpleData());
            }
            else if (structure_entity.realType == "Envelope_Pipe") {
                room.pipes.push(structure_entity.exportSimpleData());
            }
            else if (structure_entity.realType === "Platform") {
                room.platforms.push(structure_entity.exportSimpleData());
            }
        }

        room.initRoomShape();
        room.updateFeatures();
        this.updateFurnitureListFromEntities(furniture_entities);


        if (force) {


            room._swj_room_data = this._swj_room_data;
            if (this._swj_room_data?.scope_series_map) {
                entity._room._scope_series_map = {
                    soft: this._swj_room_data.scope_series_map["soft"],
                    cabinet: this._swj_room_data.scope_series_map["cabinet"],
                    hard: this._swj_room_data.scope_series_map["hard"],
                    remaining: this._swj_room_data.scope_series_map["remaining"]
                };
            }

            if (this._swj_room_data?.ceiling_element_list && this._swj_room_data?.ceiling_element_list.length > 0) {
                room._ceilling_list = [];
                this._swj_room_data.ceiling_element_list.forEach((ceiling_element) => {
                    room._ceilling_list.push(TFigureElement.fromSwjFigureElement(ceiling_element, entity._room.roomname));
                });
            }
            if (this._swj_room_data?.wall_element) {
                room._wallTexture = TFigureElement.fromSwjFigureElement(this._swj_room_data.wall_element, entity._room.roomname);
            }
            if (this._swj_room_data?.floor_element) {
                room._tile = TFigureElement.fromSwjFigureElement(this._swj_room_data.floor_element, entity._room.roomname);
            }
        }
        return room;

    }

    updateFurnitureListFromEntities(furniture_entities: TFurnitureEntity[]) {
        let room = this._room;
        let room_poly = this._room_poly;
        if (!room) return;
        

        room.resetFurnitureList();
        for (let figure_entity of furniture_entities) {
            let figure_rect = figure_entity.rect;
            if (figure_entity.matched_rect && figure_entity.rect._attached_elements['is_move']) {
                figure_entity.matched_rect.rect_center = figure_rect.rect_center.clone();
            }

            if (room_poly.containsPoint(figure_rect.rect_center)) {

                /**
                 *  改成figure_elements 用以兼容 组合模板
                 */
                figure_entity.figure_elements.forEach((fe) => {
                    fe._room = room;
                    if (fe.modelLoc == "地面") {
                        room.setTileElement(fe);
                    } else {
                        room.addFurnitureElement(fe);
                    }
                });


            }
        }

        for (let figure of room._furniture_list) {
            figure.checkCategoryByRoom(room.roomname, room);
        }

        room._furniture_list.sort((a, b) => {
            if (Math.abs(a.min_z - b.min_z) < 100) {
                return a.max_z - b.max_z;
            }
            return a.min_z - b.min_z;
        })
    }

    public clearMatchedMaterials() { 
        let win_rects = this._win_rects;
        for (let win_rect of win_rects) {
            let win_entity = win_rect._attached_elements["Entity"] as TWindowDoorEntity;
            if (win_entity && win_entity._win_figure_element) {
                win_entity._win_figure_element.clearMatchedMaterials();
            }
        }

        for (let tabletop_entity of this._tabletop_entities) {
            tabletop_entity.clearMatchedMaterials();
        }
    }

    fromSwjData(room: I_SwjRoom) {
        this._swj_room_data = room;

        if (!this._room_poly) {
            this._room_poly = new ZPolygon();
        }

        if (room.boundary) {
            let points: Vector3[] = [];
            for (let line of room.boundary) {
                points.push(new Vector3(line.start.x, line.start.y, 0));
                points.push(new Vector3(line.end.x, line.end.y, 0));
            }
            this.updateByPoint(points);



        }
        else if (room.inner_wall_list) {
            let points: Vector3[] = [];
            for (let line of room.inner_wall_list) {
                points.push(new Vector3(line.start_x, line.start_y, 0));
                points.push(new Vector3(line.end_x, line.end_y, 0));
            }
            this.updateByPoint(points);
        }
        this.uidN = ~~('' + (room.uid || "0"));
        if (!this.uidN) {
            this.uidN = room.room_id || 0;
            this.uidS = '' + (room.room_id || '0');
        }

        this._room_id = room.room_id || this.uidN || 0;

        this.updateDataByRect();
        this.bindEntity();

        this.name = room.name || room.roomname || room.room_type || "未命名";
    }

    updateByPoint(points: Vector3Like[]) {
        this._room_poly.initByVertices(points);
        this._room_poly.computeZNor();

        this._outter_wall_poly = null;
        this.update();
    }
    initWallRects() {
        this._wall_rects = [];
    }

    getWallEntities() {
        return this._wall_rects.map((rect) => {
            let entity: TBaseEntity = rect._attached_elements[KeyEntity] || null;
            if (entity.type == "Wall") {
                return entity;
            }
            return null;
        }).filter((entity) => entity);
    }
    initWindowRects() {
        this._win_rects = [];
    }

    getWindowEntities() {
        return this._win_rects.map((rect) => {
            let entity: TBaseEntity = rect._attached_elements[KeyEntity] || null;
            return entity;
        }).filter((entity) => entity);
    }

    initStructureRects() {
        this._structure_rects = [];
    }
    getStructureEntities() {
        return this._structure_rects.map((rect) => {
            let entity: TBaseEntity = rect._attached_elements[KeyEntity] || null;
            return entity;
        }).filter((entity) => entity);
    }

    initInRoomRects() {
        this.initStructureRects();
        this.initWindowRects();
    }

    addAndBindWinRect(win_rect: ZRect) {
        if (!this._room_poly || this._room_poly.edges.length == 0) return;
        if (!win_rect) return;
        for (let edge of this._room_poly.edges) {
            if (Math.abs(edge.nor.dot(win_rect.nor)) < 0.9) continue;

            let pp = edge.projectEdge2d(win_rect.rect_center);

            if (pp.x < 0 || pp.x > edge.length) continue;

            let dist = (Math.abs(pp.y));
            if (dist < win_rect.h / 2 + 600) {
                this._win_rects.push(win_rect);

                TRoomEntity.add_room_entity_to_win_rect(this, win_rect);
                break;
            }
        }
    }

    addAndBindStructureRect(structure_rect: ZRect) {
        if (!structure_rect) return;
        if (!this._main_rect || !this._room_poly) return;
        if (!this._rect.containsPoint(structure_rect.rect_center)) return;

        let dist = this._room_poly.distanceToPoint(structure_rect.rect_center);
        if (dist < structure_rect.min_hh / 2 + 100) {
            this._structure_rects.push(structure_rect);
        }
    }
    computeMainRect() {
        if (!this._room_poly) return;

        this._main_rect = TRoomShape.computeMaxRectBySplitShape(this._room_poly);

        let polys = WPolygon.splitPolyIntoRects(this._room_poly, "SplitRoom");

        let area = 0;
        for (let poly of polys) {
            if (poly.edges.length != 4) {
                // console.log("TRoomArea区域可能存在非四边形的子区域");
                continue;
            }
            let rect = ZRect.computeMainRect(poly);
            let t_area = (rect.w / 1000 * rect.h / 1000);
            area += t_area;
            if (!this._main_rect || rect.min_hh > this._main_rect.min_hh) {
                this._main_rect = rect;
            }
        }
        if (polys.length == 0) {
            this._main_rect = this._rect.clone();
            area = (this._main_rect.w * this._main_rect.h) / (1000 * 1000);
        }


        this._area = area;
    }



    updateCeilingEntity(force: boolean = false) {

        if (!this._room_ceiling_entity) {
            this._room_ceiling_entity = new TRoomCeilingEntity(this);
        }

        this._room_ceiling_entity.updateCeiling(force);

    }


    update(): void {
        /**
         *  默认矩形是最大包围盒矩形
         */
        this._rect = get_room_rect_of_room_poly(this._room_poly);


        this.computeMainRect();


    }
    updateRoomNeighbors() {
        this._neighbor_roomareas = [];
        if (!this._win_rects) return;

        for (let win of this._win_rects) {
            let rooms = TRoomEntity.get_room_entities_of_win_rect(win);

            let window = get_window_of_win_rect(win);

            if (window) {

                let t_room_names: string[] = [];
                if (window.room_names) {
                    t_room_names.push(...window.room_names);
                }
                window.room_names = [];
                for (let room of rooms) {
                    window.room_names.push(room.roomname);
                }
                if (window.room_names.length < 2 && t_room_names.length >= 2) // 这里主要是为了布局模板, 单空间的时候进行处理
                {
                    for (let name of t_room_names) {
                        if (compareNames([name], window.room_names)) continue;
                        window.room_names.push(name);
                        break;
                    }
                }
            }
            for (let room of rooms) {
                if (room !== this) {
                    if (this._neighbor_roomareas.indexOf(room) < 0) {
                        this._neighbor_roomareas.push(room);

                    }
                }
            }
        }


    }

    public get furniture_entities(): TFurnitureEntity[] {
        return this._furniture_entities;
    }

    // 获取家具实体，组合实体展开
    public getFurnitureEntitiesOnFlat(): TFurnitureEntity[] {
        let furnitureList: TFurnitureEntity[] = [];
        this._furniture_entities.forEach(furniture => {
            if (furniture instanceof TBaseGroupEntity) {
                let group = furniture as TBaseGroupEntity;
                group.combination_entitys.forEach(entity => {
                    furnitureList.push(entity);
                });
            }
            else {
                furnitureList.push(furniture);
            }
        });
        return furnitureList;
    }

    get neighbor_names() {
        let names: string[] = [];
        if (!this._neighbor_roomareas) return names;
        for (let area of this._neighbor_roomareas) {
            names.push(area.name);
        }
        return names;
    }
    checkHasStructure_WithRealType(type: IRoomEntityRealType) {
        for (let rect of this._structure_rects) {
            let entity = TStructureEntity.getOrMakeEntityOfCadRect(rect);
            if (entity.realType === type) {
                return true;
            }
        }
        return false;
    }
    checHasDoor_WithRealType(type: IRoomEntityRealType) {
        for (let rect of this._win_rects) {
            let entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(rect);
            if (entity.realType === type) {
                return true;
            }
        }
        return false;
    }
    checkHasWindow_WithCondition(condition: (win_entity: TWindowDoorEntity) => boolean) {
        for (let rect of this._win_rects) {
            let entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(rect) as TWindowDoorEntity;
            if (condition(entity)) {
                return true;
            }

        }
        return false;
    }
    predictRoomName() {
        if (this._name != "未命名") return;



        let names = this.neighbor_names;
        let count = names.length;

        // console.log(names,count,this._win_rects);
        if (count > 3) {
            this.name = "客餐厅";
            return;
        }

        let check_balcony = (win_entity: TWindowDoorEntity) => {
            if (win_entity.length > 2000) {
                return true;
            }
            return false;
        }
        if (count == 2) // 连接了两个房间的
        {
            if (this.checkHasWindow_WithCondition(check_balcony)) {
                this.name = "阳台";
            }
            else {
                this.name = "主卧";

            }
            this.name = "主卧";


        }
        else if (count == 1) {
            if (compareNames(names, ["主卧"]) == 1) {
                this.name = "卫生间";
            }
            else {

                if (this.checkHasStructure_WithRealType("Flue")) {
                    this.name = "厨房";
                }
                else {
                    if (this.checHasDoor_WithRealType("SlidingDoor")) {
                        if (this.checkHasWindow_WithCondition(check_balcony)) {
                            this.name = "阳台";
                        }
                        else {
                            this.name = "厨房";
                        }
                    }
                    else {

                        if (this.checkHasStructure_WithRealType("Envelope_Pipe")) {
                            this.name = "卫生间";
                        }
                        else if (this._area < 4) {
                            this.name = "卫生间";
                        }
                        else {
                            this.name = "卧室";
                        }


                    }

                }


            }
        }
        else {
            this.name = "卧室";

        }
    }

    static getRoomNameOptions(): { label: string; value: string }[] {
        return [
            { label: LayoutAI_App.t("未命名"), value: "未命名" },
            { label: LayoutAI_App.t("客餐厅"), value: "客餐厅" },
            { label: LayoutAI_App.t("厨房"), value: "厨房" },
            { label: LayoutAI_App.t("阳台"), value: "阳台" },
            { label: LayoutAI_App.t("卧室"), value: "卧室" },
            { label: LayoutAI_App.t("卫生间"), value: "卫生间" },
            { label: LayoutAI_App.t("主卧"), value: "主卧" },
            { label: LayoutAI_App.t("次卧"), value: "次卧" },
            { label: LayoutAI_App.t("客卧"), value: "客卧" },
            { label: LayoutAI_App.t("儿童房"), value: "儿童房" },
            { label: LayoutAI_App.t("书房"), value: "书房" },
            { label: LayoutAI_App.t("长辈房"), value: "长辈房" },
            { label: LayoutAI_App.t("入户花园"), value: "入户花园" },
            // { label: LayoutAI_App.t("茶室"), value: "茶室" },
            // { label: LayoutAI_App.t("酒窖"), value: "酒窖" },
        ]
    }

    /**
     *  更新空间内的必要信息: 比如最大柜体高度
     */
    updateInRoomProperties() {
        if (this._room) {
            let cabinets = this._room._furniture_list.filter((ele) => FigureCategoryManager.isCustomCabinet(ele));
            let max_z = 0;




            if (this.is_auto_ceiling) {
                for (let cabinet of cabinets) {
                    max_z = Math.max(max_z, cabinet.max_zval);
                }
                this._max_cabinet_height = Math.min(max_z, this.storey_height);
                this.ceiling_height = Math.min(this.storey_height - this._max_cabinet_height, TRoomEntity.MAX_CEILING_HEIGHT);
            }
            else {
                this._max_cabinet_height = this.storey_height - this.ceiling_height - this.floor_thickness;
                for (let cabinet of cabinets) {
                    if (cabinet.max_zval > 2399) {
                        cabinet.height = this.storey_height - this.ceiling_height - cabinet.min_z;
                    }
                }
            }

        }

    }


    protected _bindPropertiesOnChange() {
        let scope = this;
        for (let key in this._ui_properties) {
            let property = this._ui_properties[key];
            if (property.editable === false) continue;
            property.onChange = (value) => {
                (scope as any)[property._key || key] = (property.type == "number") ? (~~value) : value;

                if (scope.onPanelUpdate) {
                    scope.onPanelUpdate(key, value);
                }
                if (key === "name") {
                    LayoutContainerUtils.updateAliasName((LayoutAI_App.instance as TAppManagerBase).layout_container);
                    LayoutAI_App.instance.update();
                }

                if (key === "ceiling_height") {
                    scope.updateInRoomProperties();
                }
                else if (key === "is_auto_ceiling") {
                    if (scope.forcePanelUpdate) {
                        scope.forcePanelUpdate(false);
                    }
                }
                scope.update();

            }
        }
    }
    initProperties(): void {

        if (!this._ui_properties) {
            this._ui_properties = {};
        }
        this._ui_properties["ui_type"] = {
            name: LayoutAI_App.t("图层"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.ui_type,
            editable: false,
            disabled: true,
            props: {
                type: "input",
            }
        }


        this._ui_properties["name"] = {
            name: LayoutAI_App.t("空间名称"),
            widget: "LabelItem",
            type: "string",
            defaultValue: LayoutAI_App.t(this.name),
            props: {
                type: "select",
                options: TRoomEntity.getRoomNameOptions(),
                optionWidth: 100
            }
        }


        this._ui_properties["length"] = {
            name: LayoutAI_App.t("主矩形长度"),
            widget: "LabelItem",
            type: "number",
            editable: false,
            defaultValue: '' + this.length,
            props: {
                type: "input",

            }
        }

        this._ui_properties["depth"] = {
            name: LayoutAI_App.t("主矩形宽度"),
            widget: "LabelItem",
            type: "number",
            editable: false,
            defaultValue: '' + this.depth,
            props: {
                type: "input",

            }
        }

        this._ui_properties["storey_height"] = {
            name: LayoutAI_App.t("当前层高"),
            widget: "LabelItem",
            type: "number",
            editable: false,
            disabled: true,
            defaultValue: '' + this.storey_height,
            props: {
                type: "input",

            }
        }

        this._ui_properties["floor_thickness"] = {
            name: LayoutAI_App.t("地铺厚度"),
            widget: "LabelItem",
            type: "number",
            editable: true,
            disabled: false,
            defaultValue: '' + this.floor_thickness,
            props: {
                type: "input",

            }
        }
        this._ui_properties["max_cabinet_height"] = {
            name: LayoutAI_App.t("最高柜顶高"),
            widget: "LabelItem",
            type: "number",
            editable: false,
            defaultValue: '' + this.max_cabinet_height,
            disabled: true,
            props: {
                type: "input",

            }
        }
        this._ui_properties["ceiling_height"] = {
            name: LayoutAI_App.t("吊顶下吊"),
            widget: "SlideItem",
            type: "number",
            editable: true,
            defaultValue: '' + this._ceiling_height,
            min: TRoomEntity.MIN_CEILING_HEIGHT,
            max: TRoomEntity.MAX_CEILING_HEIGHT,
            props: {
                type: "input",

            }
        }
        this._ui_properties["is_auto_ceiling"] = {
            name: LayoutAI_App.t("自动下吊"),
            widget: "LabelItem",
            type: "boolean",
            editable: true,
            defaultValue: '' + this._is_auto_ceiling,
            props: {
                type: "switch",

            }
        }
        this._ui_properties["is_auto_sub_area"] = {
            name: LayoutAI_App.t("自动子分区"),
            widget: "LabelItem",
            type: "boolean",
            editable: true,
            defaultValue: '' + this._is_auto_sub_area,
            props: {
                type: "switch",

            }
        }
        // const defaultColorDict: { [key: string]: string } = {
        //     // "Unknown":" #777777",
        //     "Living": "#66b8ff",
        //     "Dining": "#ffb866",
        //     "Kitchen": "#66ffb8",
        // };
        // this._ui_properties["sub_area_widget"] = {
        //     name: "SubAreaWidget",
        //     widget: "SubAreaWidget",
        //     type: "string",
        //     editable: false,
        //     defaultValue: '0',
        //     props: {
        //         recommend_areas: Object.keys(defaultColorDict).map((key, index) => {
        //             return {
        //                 name: key,
        //                 color: defaultColorDict[key],
        //                 label: IType2UITypeDict[key] || "未命名"
        //             }
        //         })
        //     }
        // }

        this._ui_properties["clearLayout"] = {
            name: LayoutAI_App.t("清除布局"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                LayoutAI_App.emit(EventName.ClearLayout, false);
            }
        }

        this._ui_properties["clearSeries"] = {
            name: LayoutAI_App.t("清除套系"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearSeries, this._room);
            }
        }
        this._ui_properties["trimRoom"] = {
            name: LayoutAI_App.t("微调"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.Trim, this._room);
            }
        }

        this._ui_properties["showLivingRoomSpace"] = {
            name: LayoutAI_App.t("客餐厅显示分区"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {

                LayoutAI_App.DispatchEvent(LayoutAI_Events.ShowLivingRoomSpace, this._room);
            }
        }
        this._ui_properties["updateTableTops"] = {
            name: LayoutAI_App.t("更新台面"),
            widget: 'ButtonItem',
            editable: false,
            onClick: () => {

                this._updateTabletopEntities(true);
            }
        }

        this._ui_props_keys = ["ui_type", "name", "storey_height", "floor_thickness", "max_cabinet_height", "ceiling_height", "is_auto_ceiling", "update_decorates", "sub_area_widget", "clearLayout", "clearSeries"];
        if (LayoutAI_App.IsDebug && this.room_type == "卫生间") {
            this._ui_props_keys.push("trimRoom");
        }
        if (LayoutAI_App.IsDebug && this.room_type == "客餐厅") {
            // this._ui_props_keys.push("showLivingRoomSpace");
            this._ui_props_keys = ["ui_type", "name", "storey_height", "floor_thickness", "max_cabinet_height", "ceiling_height", "is_auto_ceiling", "is_auto_sub_area", "update_decorates", "sub_area_widget", "clearLayout", "clearSeries"];
        }
        if (this.room_type == "厨房" || this.room_type === "客餐厅") {
            // this._ui_props_keys.push("showLivingRoomSpace");
            this._ui_props_keys.push("updateTableTops");
        }
        if(checkIsMobile())
        {
            this._ui_props_keys = ["ui_type", "name", "storey_height"];
        }
        this._bindPropertiesOnChange();
    }
    getUiProperties() {
        if (!this._ui_properties) {
            this.initProperties();
        }
        this.updateInRoomProperties();


        // 先更新一下 一些列表信息
        if (this._ui_properties['ui_realType']) {
            this._ui_properties["ui_realType"].defaultValue = '' + this.ui_realType;
            this._ui_properties["ui_realType"].props.options = this.getRealTypeOptions();
        }

        if (this._ui_properties["ceiling_height"]) {
            this._ui_properties["ceiling_height"].disabled = this.is_auto_ceiling;

        }
        let data: { [key: string]: IPropertyUI } = {};

        for (let key of this._ui_props_keys) {
            if (this._ui_properties[key]) {

                this._ui_properties[key].defaultValue = (this as any)[key];


                data[key] = this._ui_properties[key];
            }
        }
        return data;

    }

    updateSimpleOutterWalls(wall_thickness: number) {
        this._outter_wall_poly = this._room_poly.clone().expandPolygon(wall_thickness);

        if (this._outter_wall_poly.orientation_z_nor.z < 0) {
            this._outter_wall_poly.invertOrder();
        }
    }

    drawRoomWithWalls(painter: TPainter, wall_thickness: number = 75, drawing_floor: boolean = true) {
        if (!this._outter_wall_poly) this.updateSimpleOutterWalls(wall_thickness);
        let polys = [this._outter_wall_poly, this._room_poly];

        for (let win of this._win_rects) {
            let t_win_rect = win.clone();
            if (t_win_rect.orientation_z_nor.z > 0) {
                t_win_rect.u_dv = t_win_rect.dv.clone().negate();
                t_win_rect.updateRect();
            }
            polys.push(win)
        }
        painter.fillColorIndex = CadColorIndex.Wall;
        painter.makePolygonsPath(polys);
        painter._context.fill();
        if (drawing_floor) {
            let color = IRoomFloorColor[this.room_type] || "#aaa";
            painter.fillStyle = color;
            painter.fillPolygon(this._room_poly, 1.);

            // let pattern = painter.getPattern(this.roomname+"-RoomArea") || painter.getPattern("RoomArea");

            // if(pattern)
            // {
            //     painter.fillPolygonWithImage(this._room_poly, pattern.img,false,0.6);
            // }

        }


        painter.fillStyle = "#fff";
        painter.strokeStyle = "#000";
        for (let win of this._win_rects) {
            let back_center = win.back_center;
            let win_entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(win);

            if (win_entity && win_entity.realType === "SingleDoor" && !this._room_poly.containsPoint(back_center, 10)) {
                painter.fillPolygons([win], 1.);
                painter.strokePolygons([win]);
            }
            else {
                win_entity.drawEntity(painter, { is_draw_figure: true });


            }


        }

        for (let rect of this._structure_rects) {
            let structure_entity = TStructureEntity.getOrMakeEntityOfCadRect(rect);
            if (structure_entity) {
                structure_entity.drawEntity(painter, { is_draw_figure: true });
            }
        }

    }

    drawEntity(painter: TPainter, options: I_EntityDrawingState = {}) {

        // 这个移动端点击空间时不需要绘制底图
        if (options.is_draw_texture && !options.is_mobile) {
            // painter.fillPolygonWithPattern(this._room_poly,this._texture_name,1.);  
        }
        if (options.is_draw_figure) {
            if (this._room_poly.orientation_z_nor.z > 0) return;
            painter.strokeStyle = "#282828";
            painter.strokePolygons([this._room_poly]);

        }

        if (this._main_rect && options.is_draw_roomname) {
            let text = LayoutAI_App.t(this.aliasName) + "\n" + this._area.toFixed(2) + "m²";
            let center = this._main_rect.rect_center;
            painter.fillStyle = "#000";
            let fontSize = checkIsMobile() ? 40 : (60 * 10 * painter._p_sc);
            if (options.is_show_room_id === true) {
                text = this.name + this._room._t_id;

                painter.drawText(text, center, 0, fontSize, 10, true, true);

            }
            else {
                painter.drawText(text, center, 0, fontSize, 10, true, true);
            }
        }

        if (options.is_selected) {
            if ((this._room?.locked)) {
                return;
            }
            painter.strokeStyle = "#147ffA"; // 设置描边颜色为蓝色
            painter._context.lineWidth = 4; // 设置描边宽度为2px
            painter.strokePolygons([this._room_poly]);
        }
        else if (options.is_hovered) {

        }
        if (this.livingSpaceInfo && this.isShowSpace && LayoutAI_App.IsDebug) {
            painter.strokeStyle = "#CD5555";
            let entranceSpaceRanges: any = this.livingSpaceInfo.entranceSpace;
            if (entranceSpaceRanges) {
                painter.strokePolygons(entranceSpaceRanges);
            }

            painter.strokeStyle = "#FFFF00";
            let hallwaySpaceRanges = this.livingSpaceInfo.hallwaySpace;
            if (hallwaySpaceRanges) {
                painter.strokePolygons(hallwaySpaceRanges);
            }
            painter.strokeStyle = "#0000FF";
            let livingRangs = this.livingSpaceInfo.livingSpace;
            if (livingRangs) {

                painter.strokePolygons(livingRangs);

            }
            painter.strokeStyle = "#00FF7F";
            let diningSpaceRanges: any = this.livingSpaceInfo.diningSpace;
            if (diningSpaceRanges) {

                painter.strokePolygons(diningSpaceRanges);
            }


        }


        // if(this._test_polygon)
        // {
        //     painter.strokeStyle = "red";
        //     painter.strokePolygons([this._test_polygon]);
        // }
        // if(this.new_polys && LayoutAI_App.IsDebug)
        // {
        //     painter.strokeStyle = "#f0caca";
        //     painter.strokePolygons(this.new_polys);
        // }
        // if(this._testRect)
        // {
        //     painter.strokeStyle = "red";
        //     painter.fillStyle = "red";
        //     painter.strokePolygons([this._testRect]);
        // }
    }


    public updateSpaceLivingInfo(options: { force_auto_sub_area?: boolean } = {}, spaceInfo: any = {}) {
        if (!compareNames([this.roomname], ["客餐厅"])) return;
        let figure_elements: TFigureElement[] = [];

        this._room._furniture_list.forEach(fig => {
            if (fig.furnitureEntity) {
                figure_elements.push(...fig.furnitureEntity.disassembled_figure_elements);
            }
            else {
                figure_elements.push(fig);
            }
        });
        let splitSpaceInfo: any = null;
        if(Object.keys(spaceInfo).length === 0)
        {
            splitSpaceInfo = splitSpaceForLivingRoom(this._room, figure_elements);
        }
        else
        {
            splitSpaceInfo = spaceInfo;
        }
        if (splitSpaceInfo) {
            this.livingSpaceInfo = {};
            for (let name in splitSpaceInfo) {
                if (!this.livingSpaceInfo[name]) {
                    this.livingSpaceInfo[name] = [];
                }
                for (let range of splitSpaceInfo[name]) {
                    let v0: Vector3 = new Vector3(range.xMin, range.yMin, 0);
                    let v1: Vector3 = new Vector3(range.xMax, range.yMin, 0);
                    let v2: Vector3 = new Vector3(range.xMax, range.yMax, 0);
                    let v3: Vector3 = new Vector3(range.xMin, range.yMax, 0);
                    let poly = new ZPolygon();
                    poly.initByVertices([v0, v1, v2, v3]);
                    let zrect = ZRect.computeMainRect(poly);

                    this.livingSpaceInfo[name].push(zrect);
                }
            }
        }

        if (this.is_auto_sub_area || options.force_auto_sub_area) {
            let sub_areas = [...this._sub_room_areas]; // 指定一个区域 
            this._sub_room_areas.length = 0; // 然后清空
            for (let name in this.livingSpaceInfo) {
                let sub_space_name = name.replace("Space", "");
                if (sub_space_name.length > 1) {
                    sub_space_name = sub_space_name[0].toUpperCase() + sub_space_name.substring(1);


                    for (let rect of this.livingSpaceInfo[name]) {
                        let target_sub_areas = sub_areas.filter(area => area.space_area_type === sub_space_name || area.name === sub_space_name);

                        target_sub_areas.sort((a, b) => {
                            let df = ZRect.unionArea(rect, a.rect) - ZRect.unionArea(rect, b.rect);
                            if (Math.abs(df) < 1) {
                                return a.rect.rect_center.distanceTo(rect.rect_center) - b.rect.rect_center.distanceTo(rect.rect_center);
                            }
                            else {
                                return df;
                            }
                        });

                        let target_sub_area = target_sub_areas[0];
                        if (target_sub_area) {
                            let id = sub_areas.indexOf(target_sub_area);
                            sub_areas.splice(id, 1);
                        }
                        else {
                            target_sub_area = new TSubSpaceAreaEntity();
                            target_sub_area.setSpaceAreaTypeByName(sub_space_name);

                            target_sub_area.bindEntity();
                        }
                        target_sub_area.rect.copy(rect);
                        target_sub_area.room_entity = this;
                        target_sub_area.setDefaultColor();
                        target_sub_area.bindEntity();
                        target_sub_area.updateSpaceAreaTRoom();
                        target_sub_area.update();

                        this.addSubAreaEntity(target_sub_area);
                    }
                }
            }
            this.furniture_entities.forEach(entity => delete entity.figure_element._subarea);
            this._sub_room_areas.forEach((area) => {
                area.addFurnitureEntities(this.furniture_entities);
            })
        }

    }

    public hasFurnitureEntity(entity: TFurnitureEntity) {
        return (this.furniture_entities.includes(entity));
    }
    public clearFurnitureEntities() {
        this.furniture_entities.length = 0;
    }
    public addFurnitureEntity(entity: TFurnitureEntity) {
        if (this._furniture_entities.indexOf(entity) < 0) {
            this._furniture_entities.push(entity);
        }
    }

    public removeFurnitureEntity(entity: TFurnitureEntity) {
        this._furniture_entities = this._furniture_entities.filter((fe) => fe != entity);
    }

    public removeSubAreaEntity(entity: TSubSpaceAreaEntity) {
        if (!this._sub_room_areas) return false;
        let id = this._sub_room_areas.indexOf(entity);
        if (id >= 0) {
            this._sub_room_areas.splice(id, 1);
        }
        return true;
    }
    public addSubAreaEntity(entity: TSubSpaceAreaEntity) {
        if (!this._sub_room_areas) return false;
        let id = this._sub_room_areas.indexOf(entity);
        if (id < 0) {
            this._sub_room_areas.push(entity);
        }
    }
    public onRoomFloorThicknessChanged() {
        this._furniture_entities.forEach((fe) => {
            fe.onRoomFloorThicknessChanged();
        });
    }

    static clean_room_entity_of_win_rect(win: ZRect) {
        const roomarea_entity = TRoomEntity.EntityName;
        if (win && win._attached_elements[roomarea_entity]) {
            win._attached_elements[roomarea_entity] = null;
        }
    }

    static get_room_entities_of_win_rect(win: ZRect) {

        const roomarea_entity = TRoomEntity.EntityName;
        if (win?._attached_elements && win._attached_elements[roomarea_entity]) {
            return win._attached_elements[roomarea_entity] as TRoomEntity[]
        } else {
            return null;
        }
    }
    static add_room_entity_to_win_rect(room: TRoomEntity, win: ZRect) {
        const roomarea_entity = TRoomEntity.EntityName;
        if (!win._attached_elements[roomarea_entity]) {
            win._attached_elements[roomarea_entity] = [];
        }
        if (win._attached_elements[roomarea_entity].indexOf(room) < 0) {
            win._attached_elements[roomarea_entity].push(room);
        }
    }


}


