importScripts("RoomsDivision.js")

function sendReady(text)
{
    self.postMessage({msgType:"Ready",data:text});
}
RoomsDivisionModule.onRuntimeInitialized = () => {
    console.log("RoomsDivisionModule initialized");
    sendReady("RoomsDivisionModule initialized")
};
function DivideHouse(houseJson,ruleJson)
{
    let res = RoomsDivisionModule.DivideHouse(houseJson,ruleJson);
    return res;

}
self.onmessage = function(e){

     let data = e.data;
     let method = data.method;
     let msgId = data.msgId;
     if(method === "DivideHouse")
     {
       let res =  DivideHouse(data.houseJson,data.ruleJson);
       self.postMessage({msgId:msgId, msgType:"Result",result:res});

     }
}