

import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { TAppManagerBase } from "../../AppManagerBase";

export class TExtDrawingDrawingLayer extends TDrawingLayer
{



    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.ExtDrawingDrawing,manager);

    }


    
    onDraw(): void {
        if(!this.layout_container._ext_drawing_entities) return;
        for(let entity of this.layout_container._ext_drawing_entities)
        {
            entity.drawEntity(this.painter,{is_draw_figure:true});
        }

    }
}