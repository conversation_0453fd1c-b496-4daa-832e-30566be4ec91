import { DefaultCeilingType } from "@/Apps/LayoutAI/Layout/IRoomInterface";

export const IFrameTestConfig = {
    lightMethods: { "中间排列": 0, "四周排布": 1 },
    lightProductTypeMappingTable: {
        "筒灯": 0,
        // "灯带": 1,
        // "轨道线条灯":2,
        // "轨道射灯":3,
        "轨道格栅灯": 4,
        // "轨道": 5,
        "格栅灯": 6,
        // "轨道吊线灯":7,
    }, // !注释掉是暂时不支持
    ceilingOptions: { 
        "悬边吊顶": DefaultCeilingType.OverhangingEdge, 
        "悬浮吊顶": DefaultCeilingType.Suspended, 
        "平顶": DefaultCeilingType.Flattop
    },
    defaultLightCategory: "筒灯",
    defaultLightMethod: "中间排列",
    defaultAddDownLights: false,
    defaultAddTrack: false, // 是否添加轨道
    maxBrightness: 60,
}