import { ZPolygon } from "@layoutai/z_polygon";
import { TsAI_app } from "../IRoomInterface";
import { I_TRoom, TRoomShape } from "../TRoomShape";
import { TFeatureShape } from "./TFeatureShape";
import { T_R_Shape } from "./T_R_Shape";

export class T_Rk_Shape extends T_R_Shape
{
    _max_num  : number;
    constructor(max_num : number = 3)
    {
        super();
        this.shape_code = "R"+max_num;
        this._max_num = max_num;
        this._order = max_num + 2;
    }

  


    findFeatureShape(room: I_TRoom): number {

        this._poly = null;
        this._room_shapes = [];
        this._room = room;
        let t_shapes : TRoomShape[] = [];
        for(let shape of room.shape_list)
        {
            if(shape._children.length>0) continue;
            if(shape._valid_area < 0.1) continue;

            t_shapes.push(shape);

        }

        t_shapes.sort((a,b)=>b._valid_area *b.neighbor_weight - a._valid_area * a.neighbor_weight);
        
        if(t_shapes.length < this._max_num)
        {
            return 0;
        }

      
        for(let i=0; i < this._max_num; i++)
        {
            this._room_shapes.push(t_shapes[i]);

        }
        

        // TsAI_app.log(this._room_shapes);

        return this.getFitAreaScore();
    }

    _updateShapeEdgeProp(): void {
        super._updateShapeEdgeProp();
        if(!this._contours) return;

        this._resortPolyEdges();

    }
}