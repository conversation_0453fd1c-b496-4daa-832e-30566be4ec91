<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
    </head>
    <body>
        <canvas id="canvas" width="150" height="150" style="border:1px solid;"></canvas>
        <input type="file" id="file">
        <script>
            function loadFile(e)
            {
                let canvas = document.getElementById("canvas");
                let ctx = canvas.getContext("2d");
                const file = e.target.files[0];
                if (!file) {
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    const content = e.target.result;
                    // console.log(content);
                    var svgDataURI = 'data:image/svg+xml;base64,' + btoa(content);
                    var image = new Image();
 
                    // 设置Image的src为Data URI
                    image.src = svgDataURI;

                    image.onload =()=>{
                        ctx.clearRect(0,0,canvas.width,canvas.height);
                        let cw = canvas.width;
                        let ch = canvas.height;
                        let iw = image.width;
                        let ih = image.height;
                        let tw = cw - 30;
                        let th = ih / iw * tw;
                        let tx = (cw - tw) / 2;
                        let ty = (ch - th) / 2;

                        ctx.drawImage(image,0,0,iw,ih,tx,ty,tw,th);

                        // console.log(image.width,image.height);
                    }

                };
                reader.readAsText(file);
            }
            let fileInput = document.getElementById("file");
            fileInput.onchange = (ev)=>{
                loadFile(ev);
            }

        </script>
    </body>
</html>