import { <PERSON><PERSON><PERSON>Hand<PERSON> } from "./BaseLightHandler";
import { LightingRuler, AutoLightingRulerType } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { TFurnitureEntity } from "../../../Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "../../../Layout/TLayoutEntities/TRoomEntity";
import { CategoryName } from "../../../Scene3D/NodeName";
import { ZRect } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";


export class RoomCategoryLightHandler extends BaseLightHandler {
    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        console.log('RoomCategoryLightHandler handle_1', ruler);
        let entities: TFillLightEntity[] = [];
        let newRuler = { ...ruler };
        let layout_container = this.getLayoutContainer();
        layout_container._room_entities.forEach(roomEntity => {
            if (this.checkCondition(ruler, roomEntity)) {
                let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
                newRuler = this.checkLightRuler(ruler, furnitureList);
                furnitureList.forEach(furniture => {
                    let categoryList = (newRuler.category as string).trim().split('|');
                    if (categoryList.includes(furniture.category)) {
                        console.log('RoomCategoryLightHandler handle_2', furniture.category);
                        let entity = this.createCategoryLight(newRuler, furniture, furnitureList, roomEntity);
                        if (entity) {   
                            entities.push(entity);
                        } else {
                            console.log('RoomCategoryLightHandler handle_3', 'entity is null', ruler);
                        }
                    }
                });
            }
        });
        return entities;
    }

    private checkLightRuler(ruler: LightingRuler, furnitureList: TFurnitureEntity[]): LightingRuler {
        let newRuler = { ...ruler };
        
        // 沙发测光特殊规则
        if (ruler.typeId === AutoLightingRulerType.SofaSideLight) {
            let isTeapoy = furnitureList.some(furniture => furniture.category === ruler.category);
            newRuler = !isTeapoy ? 
                { ...ruler, category: CategoryName.Sofa, pose: { ...ruler.pose, gapOffset: 2000 } } : 
                ruler;
        }
        return newRuler;
    }

    private createCategoryLight(ruler: LightingRuler, furniture: TFurnitureEntity, furnitureList: TFurnitureEntity[], roomEntity?: TRoomEntity): TFillLightEntity {
        let rect = furniture.rect;
        let newRuler = { ...ruler }
        let minDistance: number = Infinity;
        // 家具侧光防止光源与其它家具重叠/遮挡 或 溢出房间
        if (ruler.pose.lookAt && ruler.pose.gapOffset) {
            minDistance = this.checkLightOverlap(ruler, furniture, furnitureList, roomEntity);
            if (minDistance < ruler.pose.gapOffset) {
                newRuler = { ...ruler, pose: { ...ruler.pose, gapOffset: minDistance - 10 } };
            }
        }
        let pos = this.getLightPos(rect, newRuler);
        let rotation = this.getLightRotation(rect, newRuler, pos);
        let entity = this._createFillLightEntity(
            newRuler.lighting.intensity,
            newRuler.lighting.color,
            { x: pos.x, y: pos.y, z: pos.z },
            this.getSize(newRuler.lighting.width, rect.h),
            this.getSize(newRuler.lighting.length, rect.w),
            rotation.x,
            rotation.y,
            rotation.z,
            newRuler.name,
        );
        return entity;
    }

    private checkLightOverlap(ruler: LightingRuler, furniture: TFurnitureEntity, furnitureList: TFurnitureEntity[], roomEntity?: TRoomEntity): number{
        let rect = furniture.rect;
        let lightWidth = this.getSize(ruler.lighting.width, rect.h);
        let lightLength = this.getSize(ruler.lighting.length, rect.w);
        let frontEdge = rect.frontEdge;
        let edgePoints = this.getLinearEdgePoints(frontEdge, 20);
        let rayDirection = frontEdge.nor.clone();
        let minDistance: number = Infinity;
        // 房间空间（墙）
        for (let rayPoints of edgePoints) {
            let intersectionRoom = roomEntity._room_poly.getRayIntersection(rayPoints, rayDirection);
            if (intersectionRoom && intersectionRoom.point) {
                let distanceToRoom = intersectionRoom.point.clone().sub(rayPoints).length();
                minDistance = Math.min(minDistance, distanceToRoom);
            }
        }
        // 其他家具模型
        let currentLightPos = this.getLightPos(rect, ruler);
        let currentLightRotation = this.getLightRotation(rect, ruler, currentLightPos);
        let lightRect = new ZRect(lightLength, lightWidth);
        lightRect.rotation_z = currentLightRotation.z;
        lightRect.rect_center = currentLightPos;
        
        furnitureList.forEach(otherFurniture => {
             if (otherFurniture.category === ruler.category) return;
             let otherFurnitureZval = otherFurniture.pos_z + otherFurniture.height;
            // 书桌特殊处理（装饰品）
            if (otherFurniture.category === CategoryName.Desk) {
                let decorationList = otherFurniture.figure_element.decorationElements;
                if (decorationList && decorationList.length > 0) {
                    let maxHeight = decorationList.reduce((max, decoration) => Math.max(max, decoration.max_zval), 0);
                    // console.log("装饰品", decorationList, maxHeight, currentLightPos.z - lightWidth / 2);
                    otherFurnitureZval = maxHeight;
                }
            }

            if (otherFurnitureZval <= currentLightPos.z - lightWidth / 2 ||
                otherFurniture.pos_z >= currentLightPos.z + lightWidth / 2
            ) return;

            let overlapRect = otherFurniture.rect.intersect_rect(lightRect);
            let isOverlap = overlapRect && overlapRect.area > 0;
            // console.log('发现:', otherFurniture.category, overlapRect);
            for (let rayPoints of edgePoints) {
                let targetFurniture = otherFurniture.rect.getRayIntersection(rayPoints, rayDirection);
                let targetLight = lightRect.getRayIntersection(rayPoints, rayDirection);
                if (targetFurniture && targetFurniture.point) {
                    let distanceToFurniture = targetFurniture.point.clone().sub(rayPoints).length();
                    // 窗帘特殊处理
                    if (otherFurniture.category === CategoryName.Curtain) {
                            distanceToFurniture = distanceToFurniture - otherFurniture.thickness;
                    }
                    if (isOverlap) {
                        // 1. 光源矩形与家具重叠
                        // console.log('光源矩形与家具重叠:', otherFurniture.category, 'distance:', distanceToFurniture);
                        minDistance = Math.min(minDistance, distanceToFurniture);
                    } else {
                        // 2. 光源矩形不重叠但被家具遮挡
                        if (targetLight && targetLight.point) {
                            let distanceToLight = targetLight.point.clone().sub(rayPoints).length();
                            if (distanceToFurniture < distanceToLight) {
                                // console.log('光源矩形不重叠但被家具遮挡:', otherFurniture.category, 'distance:', distanceToFurniture, 'distanceToLight:', distanceToLight);
                                minDistance = Math.min(minDistance, distanceToFurniture);
                            }
                        }
                    }
                }
            }
        });
        return minDistance;
    }

    private getLinearEdgePoints(edge: ZEdge, n: number) {
        const points = [];
        for (let i = 0; i <= n; i++) {
            const t = i / n;
            const point = edge.v0.pos.clone().lerp(edge.v1.pos, t);
            points.push(point);
        }
        return points;
    }
}