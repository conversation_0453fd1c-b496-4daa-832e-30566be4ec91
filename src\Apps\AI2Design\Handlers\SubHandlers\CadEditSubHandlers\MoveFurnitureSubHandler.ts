
import { LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { EventName } from "@/Apps/EventSystem";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { CadBaseSubHandler } from "./CadBaseSubHandler";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TBaseGroupEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity";
import { TCombinationEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TCombinationEntity";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";

export class MoveFurnitureSubHandler extends CadBaseSubHandler {

    _last_pos: Vector3;
    _contained_lightings: TFurnitureEntity[] = [];

    /**
     *  启用吸附
     */
    _using_adsorption : boolean = true;
    private _isMouseMoved: boolean = false;
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.Transform_Moving;

        this._last_pos = null;
    }
    enter()
    {
        super.enter();
        // console.log("MoveFurnitureSubHandler enter");
    }
    leave()
    {
        super.leave();
        // console.log("MoveFurnitureSubHandler leave");
    }

    onmousedown(ev: I_MouseEvent): void {
        if (!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect) {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        if(!this.selected_target.selected_rect?.containsPoint(new Vector3(ev.posX, ev.posY, 0)))
        {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }
        if (this.selected_target && this.selected_target.selected_rect) {
        }
        else {
            this._cad_mode_handler._is_moving_element = false;
        }
        this._isMouseMoved = false;

        if(this.selected_target.selected_combination_entitys.length > 0)
        {
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect) as TBaseGroupEntity;
            let combinationRects: TFurnitureEntity[] = [];
            if (entity instanceof TCombinationEntity) {
                combinationRects = [...(entity as TCombinationEntity)._combination_entitys];
            } else if (entity instanceof TBaseGroupEntity)  {
                let base_group_entity = entity as TBaseGroupEntity;
                combinationRects.push(...base_group_entity._combination_entitys);
                combinationRects.push(...base_group_entity._matched_combination_entitys);
            }
            this.selected_target.selected_transform_element.recordOriginCombinationRect(combinationRects);
            this.selected_target.selected_combination_entitys = combinationRects;
        }
        this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_rect);
        this.selected_target.selected_transform_element.startTransform(this.exsorb_rects);
        for(let transform_element of this.transform_elements)
        {
            if(transform_element.IsDimensionElement)
            {
                transform_element.startTransform(this.exsorb_rects);
            }
        }
        this._last_pos = new Vector3(ev.posX, ev.posY, 0);

        // 记录包含的灯具
        this._contained_lightings = [];
        let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
        console.log(entity);
        // 餐桌椅组合区域内的灯具随动
        if((entity instanceof TBaseGroupEntity && entity.figure_element?.modelLoc?.indexOf("餐桌椅组合") >= 0 ) 
            && entity.area_entity) {
            let selectedRect = this.selected_target.selected_rect;
            entity.area_entity.furniture_entities.forEach(furniture => {
                if(furniture.realType === "Lighting" && selectedRect.containsPoint(furniture.rect.rect_center)) {
                    this._contained_lightings.push(furniture);
                }
            });
        }

        this.EventSystem.emit_M(EventName.SelectingTarget, entity, null);

    }
    onmousemove(ev: I_MouseEvent): void {
        if (ev.buttons != 1) return;

        let transform_element = this.selected_target.selected_transform_element;
        if (!transform_element) return;
        this._cad_mode_handler._is_moving_element = true;

        let current_pos = new Vector3(ev.posX, ev.posY, 0);
        if(current_pos.distanceTo(this._last_pos) > this._longPressThreshold)
        {
            this._isMouseMoved = true;
        }
        if(this._isMouseMoved)
        {
            let movement = current_pos.clone().sub(this._last_pos);
            transform_element.applyTransformByMovement(movement, !ev.shiftKey? this.exsorb_rects:null);
        }

        this.updateTransformElements();
        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        if (this.selected_target.selected_transform_element && this._isMouseMoved) {
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info) {
                this.manager.appendOperationInfo(info);
                let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect)
                if(entity instanceof TBaseGroupEntity 
                    && entity.figure_element?.modelLoc?.indexOf("餐桌椅组合") >= 0 
                    && entity.roomEntity
                    && this._contained_lightings.length > 0)
                {
                    let roomEntity = entity.roomEntity;
                    roomEntity && LayoutContainerUtils.postAutoUpdateSubAreas(roomEntity,this.manager.layout_container,{force_auto_sub_area:true});
                    // 同步移动灯具
                    for(let lighting of this._contained_lightings) {
                        lighting.rect.rect_center = this.selected_target.selected_rect.rect_center;
                        lighting.rect.updateRect();
                        if(lighting.matched_rect)
                        {
                            lighting.matched_rect.rect_center = this.selected_target.selected_rect.rect_center;
                            lighting.matched_rect.updateRect();
                        }
                    }
                    this.manager.layout_container.findAndBindRoomForFurnitures(this._contained_lightings);
                }
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            this.selected_target.selected_transform_element.recordOriginCombinationRect(null);
        }
        // 更新计数器
        this.container.entity_selector.updateSelectedEntityClickedCounter();
        if(this.selected_target.selected_entity)
        {
            this.selected_target.selected_entity.updatePosition();
        }
        if(!this._isMouseMoved)
        {
            if(this.container.entity_selector._entityClickedCounter.counter > 1)
            {
                let pos = new Vector3(ev.posX, ev.posY, 0);
                if(!this.container.entity_selector.combination_edit)
                {
                    // 进入组内编辑
                    this.container.entity_selector.updateCombinationTarget();
                    // 继续选中组内当前位置的家具
                    this.updateSelectedRect(pos)
    
                }else{
                    // 退出组内编辑
                    this.reGenerateBaseGroup();
                    this.container.entity_selector.combination_edit = false;
                    // 继续选中重组之后当前位置实体一般是组合实体
                    this.updateSelectedRect(pos)
                }
            }
        }
        if (this.selected_target?.selected_entity instanceof TFurnitureEntity) {
            this.manager.layout_container.findAndBindRoomForFurnitures([this.selected_target.selected_entity]);
        }
        if(this.selected_target.selected_entity?.type === 'Group')
        {
            this.combineSelectedCombinationRects();
        }
        
        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);

        super.onmouseup(ev);
    }
}