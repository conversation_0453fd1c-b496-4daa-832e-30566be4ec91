import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { ZPolygon } from "@layoutai/z_polygon";
import { TAppManagerBase } from "../../AppManagerBase";

export class TCadCeilingLayer extends TDrawingLayer
{    
    _target_poly : ZPolygon;

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadRoomStrucure,manager);
        this._target_poly = null;
        this._dirty = true;
    
    }
    
    get room_list()
    {
        return this.layout_container._rooms;
    }
     _updateLayerContent(): void {

    }
    
    onDraw(): void {
       
        // for(let room of this.room_list)
        // {
        //     this.painter.strokeStyle = "#f77";
        //     this.painter.fillStyle = "#aaa";

        //     if(room._ceilling_list)
        //     {
        //         room._ceilling_list.forEach(ceilling=>{

        //             if(ceilling._ex_prop['is_selected'])
        //             {
        //                 return;
        //             }
        //             this.painter.fillPolygons([ceilling.rect],0.1);
        //             this.painter.strokePolygons([ceilling.rect]);
        //         })
        //     }
    
        // }
        for(let room_entity of this.layout_container._room_entities)
        {
            if(room_entity.room_ceiling_entity)
            {
                room_entity.room_ceiling_entity.drawEntity(this.painter,{is_draw_figure:true})
            }
        }

    }
}