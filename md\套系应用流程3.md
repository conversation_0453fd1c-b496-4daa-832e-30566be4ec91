# 套系应用流程详解

## 概述

套系应用是LayoutAI的核心功能之一，它允许用户为房间快速应用一组风格一致的家居素材，实现整体设计效果。本文档详细描述了套系应用的完整流程，从用户交互到底层实现。

## 关键概念

### 套系（Series）

套系是一组风格一致的家居素材集合，包括软装、硬装和定制柜等不同类型的物品。每个套系都有独特的风格特征，可以一键应用到房间中。

### 应用范围（Scope）

套系应用范围决定了哪些类型的家具和装饰会被套系替换：

- **软装（Soft）**：可移动的家具如沙发、床、桌椅等
- **定制柜（Cabinet）**：定制家具如衣柜、鞋柜、书柜等
- **硬装（Hard）**：固定装修元素如地板、墙面、踢脚线等
- **补全（Remaining）**：对未匹配到材质的图元进行补充匹配

### 图元（FigureElement）

图元是布局中的基本元素，代表各种家具和装饰物。每个图元都有位置、尺寸、旋转等属性，以及可能关联的材质信息。

### 素材（Material）

素材是图元的具体表现形式，包含3D模型、材质、纹理等信息。套系应用过程中，系统会为每个图元匹配合适的素材。

## 用户交互流程

1. **选择房间**：用户首先选择一个或多个需要应用套系的房间
2. **选择套系**：从套系列表中选择一个喜欢的风格套系
3. **设置应用范围**：选择应用软装、硬装、定制柜或全部
4. **开始应用**：点击应用按钮，系统开始自动处理
5. **查看结果**：套系应用完成后，用户可以查看效果并进行调整

## 技术实现流程

### 1. 初始化阶段

当用户选择套系并点击应用按钮时，系统会调用`TSeriesFurnisher`类的`onSeriesSampleSelected`方法，开始套系应用流程：

```typescript
async onSeriesSampleSelected(
    scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean }, 
    series: TSeriesSample,
    targetRooms: TRoom[] = null,
    options: { 
        needsDiversifyMatched?: boolean, 
        updateTopViewBy3d?: boolean,
        needAutoFinetune?: boolean
    } = { 
        needsDiversifyMatched: true, 
        updateTopViewBy3d: true, 
        needAutoFinetune: true 
    }
)
```

首先，方法会取消当前被选中图元的选中状态，并确定目标房间列表。如果未指定目标房间列表，则使用当前选中的房间列表。

### 2. 套系应用阶段

系统对每个目标房间并行处理套系应用：

1. **房间准备**：检查房间是否被锁定，如果锁定则跳过该房间
2. **套系实例化**：为当前房间创建一个新的套系实例
3. **应用策略选择**：
   - 如果是常规应用（scope.remaining为false）：
     - 记录应用范围和套系信息
     - 清除当前范围内的已匹配素材
     - 设置房间和套系的映射关系
     - 调用`tryToFurnishRooms`方法进行套系匹配
   - 如果是补全应用（scope.remaining为true）：
     - 设置当前应用范围为remaining
     - 调用`tryToFurnishRoomRemainings`方法对未匹配素材的图元进行匹配

### 3. 套系匹配阶段

`tryToFurnishRooms`和`tryToFurnishRoomRemainings`方法负责套系匹配过程：

1. **图元准备**：将房间中的家具图元与房间关联
2. **更新应用范围**：更新房间已应用套系的范围信息
3. **素材匹配**：如果房间已关联套系且有图元，则调用`TMaterialMatcher.furnishRoom`方法进行素材匹配

### 4. 素材匹配详细过程

`TMaterialMatcher.furnishRoom`是素材匹配的核心方法：

1. **房间套系关联**：更新房间当前应用的套系信息
2. **图元清理**：移除房间中的电器装饰图元
3. **图元筛选**：
   - 常规应用：获取所有需要匹配的图元，清空_remaining_figure_list
   - 补全应用：获取所有未匹配素材的图元
4. **硬装处理**：如果需要应用硬装，则生成或获取硬装图元
5. **图元过滤**：只保留在当前应用范围内且未锁定的图元，并清空相应素材
6. **素材获取**：调用MaterialService.matchMaterials从服务器获取匹配素材
7. **未匹配处理**：检查哪些图元未匹配到素材，将其添加到相应列表
8. **默认素材应用**：为未匹配到素材的图元应用默认素材
9. **素材关联**：将素材与房间关联，设置roomUid
10. **组合家具处理**：为匹配到组合素材的图元生成实体
11. **素材后处理**：调整素材和图元的位置、大小与方向

### 5. 后处理阶段

套系匹配完成后，系统进入后处理阶段：

1. **多样化处理**（可选）：如果启用了多样化匹配，确保不同房间的相同类型图元使用不同的素材，增加变化感
2. **3D场景更新**：更新3D视图以反映套系应用结果
3. **报价数据生成**：根据套系生成报价数据
4. **自动微调**（可选）：如果启用了自动微调，则对家具布局进行优化

### 6. 自动微调过程

自动微调由`TLayoutFineTuningManagerToolUtil`类的方法实现：

1. **图元准备**：获取所有需要微调的图元
2. **墙边处理**：让适合靠墙的家具靠墙放置
3. **迭代微调**：
   - 计算当前布局评分
   - 对图元位置进行微调
   - 重新计算布局评分
   - 如果评分提升不明显，则停止微调
4. **应用微调**：将微调后的图元信息应用到房间

## 关键类和方法

- **TSeriesFurnisher**: 套系匹配器，负责整体套系应用流程
  - `onSeriesSampleSelected`: 套系应用起点
  - `tryToFurnishRooms`: 常规套系应用
  - `tryToFurnishRoomRemainings`: 补全套系应用
  - `autoFineTuning`: 自动微调

- **TMaterialMatcher**: 材质匹配器，负责为图元匹配合适的素材
  - `furnishRoom`: 房间素材匹配
  - `checkAndUpdateDefaultMaterialIds`: 处理默认素材

- **MatchingPostProcesser**: 匹配后处理器，调整素材和图元位置
  - `adjustMatchedMaterialsAndFigureElements`: 调整素材和图元
  - `syncMatchedRectAndModifyMaterialPositionZ`: 同步位置信息

- **TLayoutFineTuningManagerToolUtil**: 布局微调工具，优化家具布局
  - `postMatchAutoFineTuning`: 自动微调入口
  - `fineTuningRoom`: 房间微调
  - `preStickWallForFineTuningFigures`: 家具靠墙处理

## 数据模型

### TSeriesSample（套系样本）

```typescript
export class TSeriesSample {
    seriesKgId: number|string;    // 套系知识图谱ID
    ruleId: number;               // 规则ID
    seedSchemeId: string;         // 种子方案ID
    roomName: string;             // 房间名称
    ruleName: string;             // 规则名称
    seedSchemeName: string;       // 种子方案名称
    seriesName: string;           // 套系名称
    status: number;               // 状态
    thumbnail: string;            // 缩略图
    roomList: any;                // 房间列表
    ruleImageList: any;           // 规则图片列表
    layoutTemplates: any;         // 布局模板
    _selected: boolean;           // 是否被选中
}
```

### 素材匹配项（I_MaterialMatchingItem）

```typescript
interface I_MaterialMatchingItem {
    name: string;                 // 素材名称
    modelId: string;              // 模型ID
    modelLoc: string;             // 模型类别
    publicCategoryName: string;   // 公共类别名
    length: number;               // 长度
    width: number;                // 宽度
    height: number;               // 高度
    imageUrl: string;             // 图片URL
    targetPosition: {x: number, y: number, z: number}; // 目标位置
    targetSize: {length: number, width: number, height: number}; // 目标尺寸
    targetRotation: {x: number, y: number, z: number}; // 目标旋转
    roomUid: string;              // 所属房间UID
    figureElement?: TFigureElement; // 关联的图元
}
```

## 贴地处理

对于需要贴地的家具（如沙发、床等），系统会在素材匹配后进行特殊处理，确保家具底部与地面对齐：

1. 在`MatchingPostProcesser.syncMatchedRectAndModifyMaterialPositionZ`方法中：
   - 计算图元底部的Z坐标值
   - 调整素材的targetPosition.z值，使家具底部与地面对齐
   - 特殊处理某些需要悬空或特定高度的家具（如吊柜、壁灯等）

2. 对于不同类型的家具，贴地逻辑有细微差别：
   - 普通家具：底部直接贴地
   - 悬空家具：保持特定的悬空高度
   - 吊顶装饰：吸附到天花板
   - 壁挂物品：贴墙且保持适当高度

## 性能优化

套系应用过程可能涉及大量图元和素材，为保证性能，系统采用了以下优化：

1. **并行处理**：使用Promise.all和map方法对房间并行处理
2. **懒加载**：素材数据采用按需加载策略
3. **批量API调用**：将素材匹配请求合并为批量调用
4. **缓存机制**：常用套系和素材数据进行缓存
5. **增量更新**：3D场景采用增量更新策略，只更新变化的部分

## 常见问题与解决方案

1. **套系应用失败**：通常是由于网络问题或服务器响应超时，可重试或检查网络
2. **素材匹配不理想**：可能是套系与房间风格不匹配，尝试不同套系或调整家具布局
3. **自动微调不理想**：可关闭自动微调，手动调整家具位置
4. **性能问题**：房间图元过多时可能导致卡顿，考虑分批应用或简化场景

## 总结

套系应用是LayoutAI中一个复杂而强大的功能，它通过智能匹配算法，为用户提供一键式的整体设计体验。从用户选择套系到最终完成应用，系统经过初始化、套系应用、素材匹配、后处理和自动微调等多个阶段，确保最终效果既美观又实用。

完整的调用层级关系如下：

```
onSeriesSampleSelected
├── tryToFurnishRooms / tryToFurnishRoomRemainings
│   └── TMaterialMatcher.furnishRoom
│       ├── MaterialService.matchMaterials (远程API调用)
│       ├── TPostDecoratesLayout.post_clean_electricity_decorations
│       ├── makeHardFurnishingElements (硬装图元生成)
│       ├── generateGroupMemberMatchedEntities (组合家具处理)
│       └── MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements
│           ├── modifyMaterialPositionSize
│           └── syncMatchedRectAndModifyMaterialPositionZ
├── Scene3DEventsProcessor.UpdateEntitiesNeedsTopViewImage (3D场景更新)
└── autoFineTuning 
    └── TLayoutFineTuningManagerToolUtil.postMatchAutoFineTuning
        └── fineTuningRoom
            ├── preStickWallForFineTuningFigures (家具靠墙处理)
            ├── getFineTuningInfoByLayoutScore (布局评分)
            └── fineTuningFigures (图元位置微调)
``` 