/**
* @description 全景图生成器
* <AUTHOR>
* @date 2025-07-16
* @lastEditTime 2025-07-16 17:38:50
* @lastEditors xuld
*/
import * as THREE from 'three';

interface PanoramaGeneratorOptions {
    scene: THREE.Scene;
    camera: THREE.PerspectiveCamera;
    resolution?: number;
    mimeType?: string;
    quality?: number;
}

export class PanoramaGenerator {
    private scene: THREE.Scene;
    private camera: THREE.PerspectiveCamera;
    private resolution: number;
    private mimeType: string;
    private quality: number;


    constructor(options: PanoramaGeneratorOptions) {
        this.scene = options.scene;
        this.camera = options.camera;
        this.resolution = options.resolution || 1024;
        this.mimeType = options.mimeType || 'image/jpeg';
        this.quality = options.quality || 0.9;
    }

    /**
     * 将全景图保存为JPG文件
     */
    public async saveAsJPG(filename: string = 'panorama.jpg'): Promise<void> {
        try {
            const blob = await this.generate();

            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;

            // 触发下载
            document.body.appendChild(a);
            a.click();

            // 清理
            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 100);
        } catch (error) {
            console.error('Error saving panorama as JPG:', error);
            throw error;
        }
    }

    /**
     * 生成全景图
     *       |  up   |
     *  left | front | right | back
     *       | down  |
     */
    public async generate(): Promise<Blob> {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) throw new Error('Could not get canvas context');

        const faceSize = this.resolution;
        canvas.width = faceSize * 4; // 4个面宽
        canvas.height = faceSize * 3; // 3个面高

        // 使用主渲染器直接渲染6个方向
        const directions = [
            { name: 'front', rotation: new THREE.Euler(0, 0, 0) },           // 朝向 +X
            { name: 'back', rotation: new THREE.Euler(0, Math.PI, 0) },      // 朝向 -X
            { name: 'left', rotation: new THREE.Euler(0, Math.PI / 2, 0) },  // 朝向 -Y
            { name: 'right', rotation: new THREE.Euler(0, -Math.PI / 2, 0) }, // 朝向 +Y
            { name: 'up', rotation: new THREE.Euler(-Math.PI / 2, 0, 0) },   // 朝向 +Z
            { name: 'down', rotation: new THREE.Euler(Math.PI / 2, 0, 0) }   // 朝向 -Z
        ];

        // 保存相机的原始状态
        const originalRotation = this.camera.rotation.clone();
        const originalAspect = this.camera.aspect;
        const originalFov = this.camera.fov;

        // 临时调整相机参数为1:1比例，避免变形
        this.camera.aspect = 1;
        this.camera.updateProjectionMatrix();

        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = faceSize;
        tempCanvas.height = faceSize;

        const tempRenderer = new THREE.WebGLRenderer({
            canvas: tempCanvas,
            preserveDrawingBuffer: true,
            antialias: true,
            alpha: true
        });
        tempRenderer.setSize(faceSize, faceSize);
        tempRenderer.setClearColor(0x000000, 0);

        for (let i = 0; i < directions.length; i++) {
            const dir = directions[i];

            // 设置相机方向
            this.camera.rotation.copy(dir.rotation);
            this.camera.updateMatrixWorld();

            // 渲染场景
            tempRenderer.render(this.scene, this.camera);

            // 计算在最终canvas中的标准立方体贴图cross layout位置
            let x = 0, y = 0;
            switch (dir.name) {
                case 'up': x = faceSize; y = 0; break;
                case 'down': x = faceSize; y = faceSize * 2; break;
                case 'left': x = 0; y = faceSize; break;
                case 'front': x = faceSize; y = faceSize; break;
                case 'right': x = faceSize * 2; y = faceSize; break;
                case 'back': x = faceSize * 3; y = faceSize; break;
            }
            ctx.drawImage(tempCanvas, x, y, faceSize, faceSize);
        }

        // 在所有面渲染完成后释放渲染器
        tempRenderer.dispose();

        // 恢复相机的原始状态
        this.camera.rotation.copy(originalRotation);
        this.camera.aspect = originalAspect;
        this.camera.fov = originalFov;
        this.camera.updateProjectionMatrix();

        // 转换为blob
        return new Promise((resolve) => {
            canvas.toBlob((blob) => {
                resolve(blob!);
            }, this.mimeType, this.quality);
        });
    }
}