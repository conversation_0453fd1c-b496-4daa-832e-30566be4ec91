import { TFigureElement } from "./TFigureElement";

export class FigureCategoryManager {

    private static allFigureTypeList:string[] = ["定制柜", "软装", "硬装"];

    public static getAllFigureTypes() : string[] {
        return FigureCategoryManager.allFigureTypeList;
    }

    public static isCustomCabinet(fe: TFigureElement) : boolean {
        if (FigureCategoryManager.isDecorationFigure(fe)) return false;
        // 床头柜不属于定制柜
        if (fe.modelLoc.includes("床头柜")) return false;

        // 以下类型都属于定制柜
        return fe.modelLoc.includes("柜") || 
               fe.modelLoc.includes("背景墙") || 
               fe.modelLoc === "榻榻米" || 
               fe.modelLoc.includes("收口板");
        return false;
    }

    public static isHardFigure(fe: TFigureElement) : boolean {
        return FigureCategoryManager.isFloorFigure(fe) 
                || FigureCategoryManager.isWallFigure(fe) 
                || FigureCategoryManager.isCeilingFigure(fe)
                || FigureCategoryManager.isDoorFigure(fe);
    }

    public static isSoftFigure(fe: TFigureElement) : boolean {
        return FigureCategoryManager.isDecorationFigure(fe) || (!FigureCategoryManager.isHardFigure(fe) 
                && !FigureCategoryManager.isCustomCabinet(fe));
    }

    public static isBackgroundWallFigure(figureElement: TFigureElement) : boolean {
        return figureElement.modelLoc.endsWith("背景墙");
    }

    public static isTableFigure(figureElement: TFigureElement) : boolean {
        return figureElement.modelLoc.indexOf("茶几") >= 0
        || figureElement.modelLoc.indexOf("餐桌") >= 0
        || figureElement.modelLoc.indexOf("床头柜") >= 0;
    }

    private static isDecorationFigure(figureElement: TFigureElement) : boolean {
        return figureElement.modelLoc.indexOf("饰品") > -1;
    }

    private static isFloorFigure(figureElement: TFigureElement) : boolean {
        return figureElement.modelLoc === "地面";
    }

    private static isWallFigure(figureElement: TFigureElement) : boolean {
        return figureElement.modelLoc === "墙面";
    }

    private static isCeilingFigure(figureElement: TFigureElement) : boolean {
        return figureElement.modelLoc === "吊顶";
    }
    
    private static isDoorFigure(figureElement: TFigureElement) : boolean {
        return figureElement.modelLoc.endsWith("门");
    }

    public static isCustom(figureElement: TFigureElement) : boolean {
        const modelFlagsMap = ['10', '11', '12', '13', '17', '19', '20', '21', '22', '23', '24', '28'];
        return modelFlagsMap.includes(figureElement._matched_material.modelFlag);
    }
}