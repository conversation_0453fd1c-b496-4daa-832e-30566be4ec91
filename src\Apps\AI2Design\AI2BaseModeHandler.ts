import { <PERSON><PERSON>penR<PERSON>ult, I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { _from, checkIsMobile, is_standalone_website, mode_type, svj3dCloudDesignUrl, xrHostDomain } from '@/config';
import { Cloud3DRpcService } from "@/services/Cloud3DRpcService";
import { getCookie } from "@/utils";
import SunvegaAPI from "@api/clouddesign";
import { EventName } from "../EventSystem";
import { DesignXmlMaker } from "../LayoutAI/AICadData/DesignXmlMaker";
import { I_SwjXmlScheme, SchemeSourceType } from "../LayoutAI/AICadData/SwjLayoutData";
import { BaseModeHandler } from "../LayoutAI/Handlers/BaseModeHandler";
import { TRoomLayoutScheme } from "../LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme";
import { TWholeLayoutScheme } from "../LayoutAI/Layout/TLayoutScheme/TWholeLayoutScheme";
import { TRoom } from "../LayoutAI/Layout/TRoom";
import { TFurnitureEntity } from "../LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "../LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TWall } from "../LayoutAI/Layout/TLayoutEntities/TWall";
import { Logger } from "../LayoutAI/Utils/logger";
import { ZRect } from "@layoutai/z_polygon";
import { AI2DesignBasicModes, AI2DesignManager } from "./AI2DesignManager";
import { CadBaseSubHandler } from "./Handlers/SubHandlers/CadEditSubHandlers/CadBaseSubHandler";
import { T_MoveElement } from "../LayoutAI/Layout/TransformElements/T_MoveElement";
import { T_MoveWallElement } from "../LayoutAI/Layout/TransformElements/T_MoveWallElement";
import { T_MoveWinDoorElement } from "../LayoutAI/Layout/TransformElements/T_MoveWinDoorElement";
import { T_TransformElement } from "../LayoutAI/Layout/TransformElements/T_TransformElement";
import { BuildingService } from "../LayoutAI/Services/Basic/BuildingService";
import { LayoutSchemeData, LayoutSchemeService } from "@/Apps/LayoutAI/Services/Basic/LayoutSchemeService";
import { SchemeXmlParseService } from "../LayoutAI/Services/Basic/SchemeXmlParseService";
import { TRoomTemplateSaver } from "../LayoutAI/Layout/TLayoutEntities/loader/TRoomTemplateSaver";
import { LayoutContainerUtils } from "../LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { TSeriesFurnisher } from "../LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { deflate_to_base64_str } from "../LayoutAI/Utils/xml_utils";
import { LayoutSchemeXmlJsonParser } from "../LayoutAI/Layout/TLayoutEntities/loader/LayoutSchemeXmlJsonParser";
import {  I_SelectedTarget } from "../LayoutAI/Layout/TEntitySelector/TEntitySelector";
import { TBaseEntity } from "../LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { LayoutService } from "../LayoutAI/Services/Basic/LayoutService";
import { base64ToUtf8, openFileInput } from "../LayoutAI/Utils/file_utils";
import { SdkService } from "@/services/SdkService";
import { TAppManagerBase } from "../AppManagerBase";


export class AI2BaseModeHandler extends BaseModeHandler {

    public _selected_target: I_SelectedTarget;


    public _whole_layout_selected_index: number = 0;
    /**
     *  待选择的对象矩形列表
     */
    protected _candidate_target_rects: ZRect[];
    /**
     *  用作吸附考虑的对象目标矩形列表
     */
    protected _exsorb_target_rects: ZRect[];
    protected _transform_elements: T_TransformElement[];
    protected _transform_moving_element: T_MoveElement;

    protected _transform_moving_struture_element: T_MoveWinDoorElement;

    protected _transform_moving_wall_element: T_MoveWallElement;

    protected _cad_default_sub_handler: CadBaseSubHandler;


    _last_mouse_down_event: I_MouseEvent;

    public _existingInput: HTMLInputElement;

    _logger: Logger = null;

    constructor(manager: AI2DesignManager, name: string = "AICadMode") {
        super(manager, name);
        this._selected_target = {
            hover_rect: null,
            selected_rect: null,
            selected_transform_element: null,
            hover_transform_element: null,
            selected_combination_entitys: []
        }
        this._candidate_target_rects = [];
        this._exsorb_target_rects = [];
        this._logger = Logger.instance;
        this._existingInput = null;

    }

    enter(state?: number): void {
        super.enter(state);
        if(this.manager.layout_container.entity_selector)
        {
            this.manager.layout_container.entity_selector.bindSelectedTarget(this._selected_target);
            this.manager.layout_container.entity_selector.bindTransformers(this.transform_elements);
        }
    }
    get manager() {
        return this._manager as AI2DesignManager;
    }


    get whole_layout_scheme_list() {
        return this._manager.layout_container._whole_layout_scheme_list;
    }
    set whole_layout_scheme_list(list: TWholeLayoutScheme[]) {
        this._manager.layout_container._whole_layout_scheme_list = list;
    }
    get candidate_rects() {
        return this._candidate_target_rects;
    }

    get exsorb_rects() {
        return this._exsorb_target_rects;
    }

    get transform_elements() {
        return this._transform_elements;
    }


    get transform_moving_element() {
        return this._transform_moving_element;
    }

    get transform_moving_struture_element() {
        return this._transform_moving_struture_element;
    }

    get transform_moving_wall_element() {
        return this._transform_moving_wall_element;
    }

    get painter() {
        return this.manager?.painter;
    }

    get furniture_entities() {
        return this.manager.layout_container._furniture_entities;
    }

    set furniture_entities(rects: TFurnitureEntity[]) {
        this.manager.layout_container._furniture_entities = rects;
    }
    get wall_entities() {
        return this.manager.layout_container._wall_entities;
    }

    set wall_entities(rects: TWall[]) {
        this.manager.layout_container._wall_entities = rects;
    }
    get room_list() {
        return this.manager.layout_container._rooms;
    }
    get logger() {
        return this._logger;
    }

    protected get _initial_scheme_data(): I_SwjXmlScheme {
        return this.manager.layout_container._initial_scheme_data;
    }
    protected set _initial_scheme_data(data: I_SwjXmlScheme) {
        this.manager.layout_container._initial_scheme_data = data;
    }
    async handleEvent(event_name: string, event_param: any) {
        if (this._active_sub_handler) {
            this._active_sub_handler.handleEvent(event_name, event_param);
        }
        else {
            if (this._cad_default_sub_handler) {
                this._cad_default_sub_handler.handleEvent(event_name, event_param);
            }
        }
        if (event_name === LayoutAI_Events.OpenMyLayoutSchemeData) {
            if (event_param != null) {
                Logger.instance.log("打开我的方案：" + event_param.layoutSchemeName + ", " + event_param.id);
                this.openLayoutSchemeData(event_param);
                this.manager.layout_container.aidraw_img = event_param.coverImage;
            }
        }
        if (event_name === LayoutAI_Events.OpenMyLayoutSchemeIn3D) {
            this.openLayoutSchemeIn3D(event_param);
        }
        if(event_name === LayoutAI_Events.autoSave)
        {
            this._cad_default_sub_handler.cleanSelection();
            await LayoutSchemeService.autoSave(this.manager.layout_container);
            LayoutAI_App.emit_M(EventName.RoomList, this.room_list);            
        }
        if(event_name === LayoutAI_Events.ExitAutoSave)
        {
            LayoutAI_App.emit(EventName.SaveProgress, {
                progress: "ongoing"
            });
            this._cad_default_sub_handler.cleanSelection();
            await LayoutSchemeService.autoSave(this.manager.layout_container);
            LayoutAI_App.emit_M(EventName.RoomList, this.room_list);  
            // 海尔需要完全保存后才能退出
            LayoutAI_App.emit(EventName.ExitAutoSave, null);          
        }

        if (event_name === LayoutAI_Events.OpenMyLayoutSchemeUrl) {
            let layoutSchemeUrl: string = event_param;
            if (layoutSchemeUrl) {
                fetch(layoutSchemeUrl).then((response) => {
                    response.text().then((base64str) => {
                        const layoutSchemeJsonStr = base64ToUtf8(base64str);
                        const schemeJson = JSON.parse(layoutSchemeJsonStr.replace(/'/g, '"'));
                        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(schemeJson);
                    });
                }).catch((e) => {
                    console.error(e);
                });
            }
        }
        if (event_name === LayoutAI_Events.SaveLayoutSchemeAs) {
            console.log("保存LayoutScheme ... Cad");
            this.manager.layout_container.saveSchemeLayout2JsonAs(event_param);
        }
        if (event_name === LayoutAI_Events.SaveLayoutScheme) {
            this.manager.layout_container.saveCabinetQuoteInfo();
            this.manager.layout_container.saveSchemeLayout2Json();
        }
        if (event_name === LayoutAI_Events.PostBuildingId) {
            this.openSwjFileWithBuildingId(event_param.id, event_param.name, event_param.customer_info, event_param.imagePath, event_param.auto_layout, event_param.auto_save);
        }
        if (event_name === LayoutAI_Events.Init) {
            this.manager.layout_container.clearSchemeInfo();
            this.upudateWholeLayoutSchemeList();
            this._cad_default_sub_handler.updateCandidateRects();
            this.update();
        }
        if (event_name === LayoutAI_Events.OnloadedXmlLayoutScheme) {
            this.onloadSwjSchemeJson(event_param);
        }
    }
    async runCommand(cmd_name: string): Promise<void> {
        if (this._sub_handlers[cmd_name]) {
            this.setActiveHandler(cmd_name);
            // return;
        }
        if (cmd_name === LayoutAI_Commands.LeaveSubHandler) {
            this.setActiveHandler(this._default_sub_handler_name);

            LayoutAI_App.emit_M(EventName.SubHandlerLeaved, { new_handler: this._active_sub_handler });

            document.querySelector(".edit_input")?.remove();
        }
        if (cmd_name === LayoutAI_Commands.AcceptLeaveSubHandler) {
            this.setActiveHandler(this._default_sub_handler_name, 1);

            document.querySelector(".edit_input")?.remove();
        }
        if (cmd_name === LayoutAI_Commands.ShowAICadLayer) {
            this.manager.layer_CadRoomFrameLayer?.switch();
            this.manager.updateVisibleLayers();
        }
        else if (cmd_name === LayoutAI_Commands.ShowRawCadLayer) {
            this.manager.layer_CadEzdxfLayer?.switch();
            this.manager.updateVisibleLayers();
        }
        else if (cmd_name === LayoutAI_Commands.ShowCadFurnitureLayer) {
            this.manager.layer_CadFurnitureLayer?.switch();
            this.manager.updateVisibleLayers();
        }
        // else if(cmd_name === LayoutAI_Commands.ShowCadCabinetLayer)
        // {
        //     this.manager.layer_CadCabinetLayer?.switch();
        //     this.manager.updateVisibleLayers();
        // }
        else if (cmd_name === LayoutAI_Commands.ShowOutLineLayer) {
            this.manager.layer_OutLineLayer?.switch();
            this.manager.updateVisibleLayers();
        }
        else if (cmd_name === LayoutAI_Commands.ShowCadNameLayer) {
            this.manager.layer_CadRoomNameLayer?.switch();
            this.manager.updateVisibleLayers();
        }
        else if (cmd_name === LayoutAI_Commands.Undo) {
            this.manager.undo();
            this.setActiveHandler(this._default_sub_handler_name);
        }
        else if (cmd_name === LayoutAI_Commands.Redo) {
            this.manager.redo();
            this.setActiveHandler(this._default_sub_handler_name);

        }
        else if (cmd_name === LayoutAI_Commands.OpenSwjJsonFile) {
            this.openSwjXmlSchemeJson();
        }
        else if (cmd_name === LayoutAI_Commands.SaveSwjJsonFile) {
            await this.saveSwjXmlSchemeJson();
        }
        else if (cmd_name === LayoutAI_Commands.SaveSwjJsonRoomTemplate) {
            this.saveRoomTemplateFile();
        }
        else if (cmd_name === LayoutAI_Commands.OpenSwjJsonRoomTemplate) {
            this.openRoomTemplateFile();
        }
        else if (cmd_name === LayoutAI_Commands.SaveRoomTemplates) {
            this.uploadRoomTemplates(false);
        }
        else if (cmd_name === LayoutAI_Commands.SaveAsRoomTemplates) {
            this.uploadRoomTemplates(true);
        }
        else if (cmd_name === LayoutAI_Commands.OpenSwjFileBySchemeId) {
            this.openSwjFileWithSchemeId();
        }
        else if (cmd_name === LayoutAI_Commands.OpenLayoutSchemeById) {
            this.openLayoutSchemeById();
        }
        else if (cmd_name === LayoutAI_Commands.MakeWallXml) {
            this.makeWallXml();
        }
        else if (cmd_name === LayoutAI_Commands.ApplyLayout) {
            this.EventSystem.emit(EventName.SwitchIntoDesign);
        }
        else if (cmd_name === LayoutAI_Commands.WholeHouseAILayout) {
            if (!this._active_sub_handler) {
                this.computeWholeLayoutSchemeList(true);
            }
        }
        this.update();
    }

    /**
     *  载入方案完成
     */
    onloadSwjSchemeJson(options: { data?: I_SwjXmlScheme, clean_dxf_data?: boolean, 
        updateUid?: boolean, auto_layout?: boolean } = {}) {
        if (options.clean_dxf_data) {

            this.manager.layer_CadEzdxfLayer._clean();
            this.manager.layer_CadEzdxfLayer.ezdxf_data = null;
        }
        if (window.innerWidth < window.innerHeight * 0.8) {
            LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.7);
        }
        else {
            LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.6);
        }
        // if (checkIsMobile()) {
        //     const deviceType = checkDeviceType();

        //     if (deviceType === 'tablet') {
        //         this.painter._p_sc = 0.1;
        //     } else {
        //         this.painter._p_sc = 0.06;
        //     }

        // }
        // this.manager.layout_container.focusCenter();


        this.manager.layout_container.needs_making_wall_xml = !options?.data?.xml_str || options.updateUid;
        this._cad_default_sub_handler.updateCandidateRects();
        this._cad_default_sub_handler.cleanSelection();
        this.update();
        let current_mode = this.manager._current_handler_mode;
        let is_ai_matching_mode = this.manager.layout_container._drawing_layer_mode === "AIMatching";
        // if (this.manager.layout_container) {
        //     this.manager.layout_container._initial_scheme_data = this.manager.layout_container.toXmlSchemeData();
        //     this.manager.layout_container._initial_scheme_data.xml_str = data.xml_str;
        // }

        if (is_ai_matching_mode 
            || current_mode === AI2DesignBasicModes.HouseDesignMode
            || current_mode === AI2DesignBasicModes.MeasurScaleMode    
        ) {
            this.manager.layout_container.updateRoomsFromEntities();
            this.isSelectRoom();
        }
        else {
            if (window.location.pathname.includes('share')) {
                this.update();
                LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
                return;
            }

        }
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
        LayoutContainerUtils.updateAliasName();
        LayoutAI_App.emit_M(EventName.RoomList, this.room_list);
        
        if(current_mode === AI2DesignBasicModes.HouseDesignMode
            || current_mode === AI2DesignBasicModes.MeasurScaleMode
        ){
            // 户型编辑内打开户型库的方案不用去请求布局推荐
            return;
        }
        if(options.auto_layout)
        {
            // 加载方案后计算全屋布局方案
            this.computeWholeLayoutSchemeList(false).then(() => {
                if (this.whole_layout_scheme_list && this.whole_layout_scheme_list.length > 0) {

                    if(!this.manager.layout_container._layout_scheme_id || (this.manager.layout_container._furniture_entities.length === 0))
                    {
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, { value: this.whole_layout_scheme_list[0], index: 0 });
                    }
                    LayoutAI_App.emit_M(EventName.xmlSchemeLoaded, { mode: 'Finish'});
                }
            });
        }

        if(this.manager?.Configs?.update3d_when_xmlscheme_onloaded)
        {
            if(this.manager.scene3D && this.manager.scene3D.isValid())
            {
                this.manager.updateScene3D(true);
            }
        }
    
    }
    unGroupAllGroupTemplates() {
        this.manager.layout_container.unGroupOfAllFurnitureEntities();
    }
    unGroupAllBaseGroup() {
        this.manager.layout_container.unBaseGroupOfAllFurnitureEntities();
    }

    transformGroup() {
        this.manager.layout_container.transformGroup();
    }
    isSelectRoom() {
        for (let room of this.room_list) {
            // 新增判断是否可点击逻辑
            room.checkIsSelectable();
        }
    }
    async computeWholeLayoutSchemeList(append_furniture_entities: boolean = true) {
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "AI推荐计算中..." });


        
        await this._cad_default_sub_handler.ai_layout_for_whole_house(append_furniture_entities);

        // 2025.04.25: 新增默认先query分区模板 用于测试
        LayoutAI_App.RunCommand(LayoutAI_Commands.QuerySpaceTemplates);

        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
        this.upudateWholeLayoutSchemeList();
    }

    upudateWholeLayoutSchemeList() {
        let whole_layout_scheme_list = [];
        let scheme_ids_list: string[] = [];
        let room_schemes_list: TRoomLayoutScheme[][] = [];

        for (let room of this.room_list) {
            if (!room._layout_scheme_list) continue;

            let temp_scheme_list = [...room._layout_scheme_list];
            if (room._furniture_list.length > 0) {
                let diy_scheme = room.toLayoutScheme();
                temp_scheme_list = [diy_scheme, ...room._layout_scheme_list];
            }
            room_schemes_list.push(temp_scheme_list);
        }

        {
            let whole_layout_scheme0 = new TWholeLayoutScheme(this.manager.layout_container);
            let scheme_ids = "";
            for (let room_schemes of room_schemes_list) {
                let scheme = room_schemes[0];
                scheme_ids += '0_';
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme0._room_scheme_list.push(scheme);
                }
            }
            scheme_ids_list.push(scheme_ids);
            whole_layout_scheme_list.push(whole_layout_scheme0);
        }

        {
            let whole_layout_scheme1 = new TWholeLayoutScheme(this.manager.layout_container);
            let scheme_ids = "";
            for (let room_schemes of room_schemes_list) {
                let scheme: TRoomLayoutScheme = null;
                for (let j = 1; j >= 0; j--) {
                    scheme = room_schemes[j];
                    if (scheme) {
                        scheme_ids += j + '_';
                        break;
                    }
                }
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme1._room_scheme_list.push(scheme);
                }
            }
            scheme_ids_list.push(scheme_ids);
            whole_layout_scheme_list.push(whole_layout_scheme1);
        }

        let target_num = 5;
        // 依次类推模式
        for (let id = 2; id < 5; id++) {
            let scheme_ids = "";
            let whole_layout_scheme = new TWholeLayoutScheme(this.manager.layout_container);
            for (let room_schemes of room_schemes_list) {
                let t_id = Math.min(id, room_schemes.length - 1);
                let scheme = room_schemes[t_id];
                scheme_ids += t_id + '_';
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme._room_scheme_list.push(scheme);
                }
            }
            if (!scheme_ids_list.includes(scheme_ids)) {
                scheme_ids_list.push(scheme_ids);
                whole_layout_scheme_list.push(whole_layout_scheme);
                if (whole_layout_scheme_list.length >= target_num) break;
            }
        }


        this.whole_layout_scheme_list = whole_layout_scheme_list.filter((scheme) => scheme._room_scheme_list.length > 0);

        this.postProcessWholeLayoutSchemeList();

        if (!this.manager.layout_container._selected_room) {
            LayoutAI_App.emit_M(EventName.WholeLayoutSchemeList, { schemeList: this.whole_layout_scheme_list || [], index: 0 })
        }
        this.update();
    }
    async makeWallXml() {
        if (this.manager.layout_container.needs_making_wall_xml) {
            LayoutAI_App.emit(EventName.ShowOpenScheme, true);

            console.log("生成墙体XML!");
            let design_xml_maker = new DesignXmlMaker();

            let root = design_xml_maker.makeByEntities(this.manager.layout_container);
            let xml_text = new XMLSerializer().serializeToString(root);

            await SunvegaAPI.BasicBiz.Room.openSchemeFromXml({
                xml: xml_text
            });

            // 生成后 要把needs_making_wall_xml 设置成false
            this.manager.layout_container.needs_making_wall_xml = false;

            LayoutAI_App.emit(EventName.ShowOpenScheme, false);


        }
    }
    loadSwjSchemeXmlJson(data: I_SwjXmlScheme, updateUid: boolean = false, layout_scheme_id: string = null,
        layout_scheme_name: string = null, clean_dxf_data: boolean = true, schemeSource: SchemeSourceType = null) {
        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(data, {
            updateUid: updateUid,
            layout_scheme_id: layout_scheme_id,
            layout_scheme_name: layout_scheme_name,
            clean_dxf_data: clean_dxf_data,
            schemeSource: schemeSource
        })
    }
    async openLayoutSchemeData(data: LayoutSchemeData) {
        return await LayoutSchemeXmlJsonParser.openLayoutSchemeData(data);
    }
    async openSwjXmlSchemeJson() {
        const fileResult: FileOpenResult = await openFileInput(".json", "Text");
        let text = fileResult.content;
        if (!text) return;
        let data = JSON.parse(text);
        const layoutSchemeName: string = fileResult.file;
        this.EventSystem.emit(EventName.LayoutSchemeOpened, { id: null, name: layoutSchemeName });
        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(data, { updateUid: false, layout_scheme_id: null, layout_scheme_name: layoutSchemeName,auto_layout:false });
    }

    async queryModelRoomsFromServer() {
        let rooms: TRoom[] = this.room_list;
        await this.manager.layout_graph_solver.queryModelRoomsFromServer(rooms, false, true);
    }

    protected printMaterialsFrom3D(materialIdPublicCategoryMap: Map<string, string>, houseStruture: I_SwjXmlScheme) {
        let multiLinelog = "";
        multiLinelog = "##### 来自3D方案的原有素材 #####";
        multiLinelog += "\nScheme ID: " + houseStruture.scheme_id + ", area=" + (houseStruture.area ? houseStruture.area.toFixed(2) : 0) + ", organizationId=" + houseStruture.organization_id;
        multiLinelog += "\nFurniture list:";
        if (houseStruture.furniture_list && houseStruture.furniture_list.length > 0) {
            houseStruture.furniture_list.forEach((f) => {
                multiLinelog += "\n    materialId:" + f.material_id + ", name:" + f.name + ", room:" + f.room_ind + (materialIdPublicCategoryMap.has(f.material_id) ? ", publicCategory:" + materialIdPublicCategoryMap.get(f.material_id) : "");
            });
        }
        if (houseStruture.cabinet_list && houseStruture.cabinet_list.length > 0) {
            multiLinelog += "\nCabinet list:";
            houseStruture.cabinet_list.forEach((f) => {
                multiLinelog += "\n    materialId:" + f.material_id + ", name:" + f.name + ", room:" + (f.room_id | f.room_ind) + (materialIdPublicCategoryMap.has(f.material_id) ? ", publicCategory:" + materialIdPublicCategoryMap.get(f.material_id) : "");
            });
        }
        if (houseStruture.furniture_group_list && houseStruture.furniture_group_list.length > 0) {
            multiLinelog += "\nFurniture group list:";
            houseStruture.furniture_group_list.forEach((f) => {
                multiLinelog += "\n    materialId:" + f.material_id + ", name:" + f.name + ", room:" + f.room_ind + (materialIdPublicCategoryMap.has(f.material_id) ? ", publicCategory:" + materialIdPublicCategoryMap.get(f.material_id) : "");
            });
        }
        if (multiLinelog.length > 0) this.logger.log(multiLinelog);
    }
    async saveSwjXmlSchemeJson() {
        this.manager.layout_container.updateRoomsFromEntities();
        let wireFrameImageJsonUrl = await this.manager.layout_container.exportWireFrameImageJson();
        let scheme_data = this.manager.layout_container.toXmlSchemeData();
        scheme_data.wireFrameImageJsonUrl = wireFrameImageJsonUrl;
        const dataStr = JSON.stringify(scheme_data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const schemeId = scheme_data.scheme_id || null;

        // 弹出输入框让用户输入文件名
        let fileName = prompt("当前布局数据数据即将保存到本地，请输入文件名：", 'layout_' + (schemeId ? schemeId : "null") + '.json');

        // 如果用户取消输入，则不进行下载
        if (fileName === null) return;

        // 创建临时的<a>标签用于下载
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName; // 使用用户输入的文件名

        document.body.appendChild(a);
        a.click();

        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    saveRoomTemplateFile() {
        this.manager.layout_container.updateRoomsFromEntities();

        if (this._selected_target.selected_rect) {
            let entity = TBaseEntity.getEntityOfRect(this._selected_target.selected_rect);
            
            if (entity && entity.type === "RoomArea") {
                let swj_json_data = this.manager.layout_container.saveTRoomToJson((entity as TRoomEntity)._room);

                const dataStr = JSON.stringify(swj_json_data, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                let schemeId = this.manager.layout_container._scheme_id + "_" + swj_json_data.swj_room_data.uid;

                let fileName = prompt("选中空间的布局模板保存到本地，请输入文件名：", 'room_template_' + (schemeId ? schemeId : "null") + '.json');

                // 如果用户取消输入，则不进行下载
                if (fileName === null) return;

                // 创建临时的<a>标签用于下载
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName; // 使用用户输入的文件名

                document.body.appendChild(a);
                a.click();

                document.body.removeChild(a);
                URL.revokeObjectURL(url);

            }
        }

    }


    async openRoomTemplateFile() {
        const fileResult: FileOpenResult = await openFileInput(".json", "Text");
        let text = fileResult.content;
        if (!text) return;
        let data = JSON.parse(text);
        if (!data) return;

        this.manager.layout_container.loadRoomEntityFromJson(data);
    }

    async openSwjFileWithSchemeId() {
        let scheme_id = prompt("请输入方案ID");
        if (!scheme_id) return;
        let authCode = getCookie("authCode");

        let res = await SchemeXmlParseService.getSchemeXmlBySchemeId(scheme_id, authCode, "code_debug");

        if (res) {
            LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(res);
            let furniture_list = this.manager.layout_container._furniture_entities;
            furniture_list.forEach((figure_element) => {
                figure_element.figure_element.loadWireFrameImage()
            });
        }
    }

    async openLayoutSchemeById() {
        let scheme_id = prompt("请输入2D布局方案ID");
        if (!scheme_id) return;
        let schemeData = await LayoutSchemeService.getLayoutSchemeById(scheme_id);
        this.openLayoutSchemeData(schemeData);
    }

    uploadRoomTemplates(save_as: boolean = false) {

        let selected_room_only = confirm(`保存选中的空间，还是全屋？ 确定-单空间(${this.manager.layout_container._selected_room?.roomname || "无"}), 取消-全屋`);

        if (selected_room_only && !this.manager.layout_container._selected_room) {
            alert("未选中空间");
            return;
        }


        if (this.manager.layout_container._room_entities.length == 0) {
            window.alert("空数据不能保存");
            return;
        }
        let text = "保存 方案Id: " + this.manager.layout_container._scheme_id;
        if (save_as || !this._manager.layout_container._scheme_id) {
            this.manager.layout_container.generateSchemeId();
            text = "另存为 方案Id: " + this.manager.layout_container._scheme_id;
            console.log(this.manager.layout_container._scheme_id);
        }



        let res = confirm(text);
        if (!res) return;

        TRoomTemplateSaver.saveRoomTemplates(selected_room_only).then((room_names: { [key: string]: { room_name: string, success: boolean } }) => {
            let text = "保存布局模板完成! ";

            if (selected_room_only) {
                let num = Object.keys(room_names).length;
                if (num == 0) {
                    text = "单空间布局模板保存不成功, 布局库中存在相同的模板";
                }
            }
            console.log(room_names);
            for (let key in room_names) {
                let data = room_names[key];
                text += key + "-" + data.room_name + " " + (data.success ? "✔" : "X") + " ";
            }

            confirm(text);

        })
    }

    async openLayoutSchemeIn3D(layoutSchemeData: LayoutSchemeData) {
        const schemeContentUrl = layoutSchemeData.contentUrl;

        let followupActions: SunvegaAPI.BasicBiz.PostLayoutAction = {
            save3dScheme: false,
            createDreamer: false,
            submitRender: false,
            uploadRoyScene: false,
            renderOption: { cameras: [], lightTemplate: "AILightRealisticV2Plus" }
        };

        fetch(schemeContentUrl).then(async (response: any) => {
            response.text().then(async (layoutSchemeBase64: any) => {
                const layoutSchemeText = base64ToUtf8(layoutSchemeBase64);
                const layoutSchemeJson = JSON.parse(layoutSchemeText.replace(/'/g, '"'));
                const xmlSchemeBase64 = layoutSchemeJson.xml_str;
                const matchedLayouts = layoutSchemeJson.matchedLayout;

                SunvegaAPI.BasicBiz.Room.bindOnOpenSchemeFinish(async (data: any) => {
                    if (matchedLayouts && matchedLayouts.length > 0) {
                        let productModels: Array<SunvegaAPI.BasicBiz.ProductModel> = [];
                        let customModels: Array<SunvegaAPI.BasicBiz.CustomCabinetModel> = [];
                        let kitchen: SunvegaAPI.BasicBiz.KitchenModels = null;
                        let walls: Array<SunvegaAPI.BasicBiz.PaintModel> = [];
                        let doors: Array<SunvegaAPI.BasicBiz.DoorModel> = [];
                        let tiles: Array<SunvegaAPI.BasicBiz.TileModel> = [];
                        let ceilings: Array<SunvegaAPI.BasicBiz.CeilingModel> = [];

                        for (let matchedLayout of matchedLayouts) {
                            if (matchedLayout.productFurnitures) {
                                matchedLayout.productFurnitures.forEach((furniture: SunvegaAPI.BasicBiz.ProductModel) => {
                                    productModels.push(furniture);
                                });
                            }
                            if (matchedLayout.customCabinets) {
                                matchedLayout.customCabinets.forEach((furniture: SunvegaAPI.BasicBiz.CustomCabinetModel) => {
                                    customModels.push(furniture);
                                });
                            }
                            if (matchedLayout.kitchen) {
                                kitchen = matchedLayout.kitchen as SunvegaAPI.BasicBiz.KitchenModels;
                            }
                            if (matchedLayout.wallTexture) {
                                walls.push(matchedLayout.wallTexture as SunvegaAPI.BasicBiz.PaintModel);
                            }
                            if (matchedLayout.doors) {
                                matchedLayout.doors.forEach((door: SunvegaAPI.BasicBiz.DoorModel) => {
                                    doors.push(door);
                                });
                            }
                            if (matchedLayout.tile) {
                                tiles.push(matchedLayout.tile as SunvegaAPI.BasicBiz.TileModel);
                            }
                            if (matchedLayout.ceilings) {
                                matchedLayout.ceilings.forEach((ceiling: SunvegaAPI.BasicBiz.CeilingModel) => {
                                    ceilings.push(ceiling);
                                });
                            }
                        }

                        SunvegaAPI.BasicBiz.Room.layoutMaterials({
                            products: productModels,
                            cabinets: customModels,
                            kitchen: kitchen,
                            doors: doors,
                            tiles: tiles,
                            wallTextures: walls,
                            ceilings: ceilings,
                            footLines: null,
                            doorSills: null,
                            layoutSchemeInfo: { id: layoutSchemeData.id, name: layoutSchemeData.layoutSchemeName },
                            followupActions: followupActions
                        });

                        LayoutAI_App.closeApp();
                    }
                });

                await SunvegaAPI.BasicBiz.Room.openSchemeFromXml({
                    xml: base64ToUtf8(xmlSchemeBase64)
                });
            });

        });
    }

    async openSwjFileWithBuildingId(id: string, name: string, customer_info: any, imagePath?: string, auto_layout: boolean = true, auto_save: boolean = false) {
        if (!id) return;
        let authCode = getCookie("authCode");
        let houseTypeParam: any = null;
        let res = await BuildingService.getBuildingRoomSchemeById(id, authCode, true, (data: any) => {
            houseTypeParam = data;
        });
        LayoutAI_App.emit(EventName.LayoutSchemeOpened, { id: null, name: name });
        // 清空临摹图
        if (this._manager.layout_container.copyImageRect) {
            this._manager.layer_CadCopyImageLayer.clean();
        }
        if (res) {
            Logger.instance.log("打开户型库：" + id);
            LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(res, 
            {   
                updateUid: true, 
                layout_scheme_id: null, 
                layout_scheme_name: name, 
                schemeSource: SchemeSourceType.LayoutLibrary, 
                clean_dxf_data: false, 
                auto_layout: auto_layout 
            });
            this.manager.layout_container.aidraw_img = imagePath;
            this.manager.layout_container.hxId = id;
            this.manager.layout_container.houseType = houseTypeParam.roomTypeName;
            this.manager.layout_container._houseTypeParam = {
                area: houseTypeParam.area,
                buildingName: houseTypeParam.buildingName,
                houseSchemeId: houseTypeParam.schemeId,
                houseTypeName: houseTypeParam.buildingName,
                imageUrl: houseTypeParam.imagePath,
                district: houseTypeParam.district,
                platLayoutId: houseTypeParam.id,
            }
        }
        if (_from === 'local' && mode_type === 'HouseId') {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
        }
        if(auto_save) {
            const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
            container._layout_scheme_name = name;
            container._customer_info = {} as any;
            container._customer_info.cntactMan = customer_info?.cntactMan;
            container._customer_info.mobile = customer_info?.mobile;

            LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);

            // 海尔轮询触发
            let _schemeIdPollingTimer = setInterval(() => {
                if(this.manager.layout_container._layout_scheme_id) {
                    SdkService.createScheme(this.manager.layout_container._layout_scheme_id);
                    clearInterval(_schemeIdPollingTimer);
                }
            }, 500);
        }
    }

    openLayoutSchemeInNew3DTab() {
        //在新标签页打开3D云设计
        const layoutSchemeId: string = this.manager.layout_container._layout_scheme_id;
        if (LayoutAI_App.instance.isOverSea) {
            window.open("https://aihouse.3vjia.com/design?AiLayoutSchemeId=" + layoutSchemeId);
        } else {
            window.open(svj3dCloudDesignUrl + (svj3dCloudDesignUrl.indexOf("?") >= 0 ? "&" : "?") + "AiLayoutSchemeId=" + layoutSchemeId);
        }
    }

    async createAndOpenDreamerScheme() {
        const layoutSchemeId: string = this.manager.layout_container._layout_scheme_id;
        const dreamerSchemeUrl: string = LayoutSchemeService.getDreamerScheme(layoutSchemeId);
        if (dreamerSchemeUrl === "") {
            LayoutAI_App.emit(EventName.CreateDreamerSchemeResult, { progress: "processing", id: layoutSchemeId });
            return;
        } else if (dreamerSchemeUrl != null && dreamerSchemeUrl.length > 0) {
            LayoutAI_App.emit(EventName.CreateDreamerSchemeResult, { progress: "created", id: layoutSchemeId, extra: dreamerSchemeUrl });
            return;
        } else {
            LayoutAI_App.emit(EventName.CreateDreamerSchemeResult, { progress: "creating", id: layoutSchemeId });
        }
        LayoutSchemeService.setDreamerScheme(layoutSchemeId, "");
        Cloud3DRpcService.createDreamerScheme(layoutSchemeId).then((svjSchemeId: string) => {
            if (svjSchemeId != null) {
                const xrSchemeUrl: string = xrHostDomain + "/dreamer/scene?schemeId=" + svjSchemeId;
                LayoutSchemeService.setDreamerScheme(layoutSchemeId, xrSchemeUrl);
                LayoutAI_App.emit(EventName.CreateDreamerSchemeResult, { progress: "success", id: layoutSchemeId, extra: xrSchemeUrl });
            } else {
                LayoutSchemeService.removeDreamerScheme(layoutSchemeId);
                LayoutAI_App.emit(EventName.CreateDreamerSchemeResult, { progress: "error" });
                console.error("Fail to create dreamer scheme for layoutSchemId=" + layoutSchemeId);
            }
        });
    }

    async renderAndOpenPanorama() {
        const layoutSchemeId: string = this.manager.layout_container._layout_scheme_id;
        LayoutAI_App.emit(EventName.RenderPanoramaResult, { progress: "rendering", id: layoutSchemeId });
        Cloud3DRpcService.renderPanorama(layoutSchemeId).then((svjSchemeId: string) => {
            if (svjSchemeId != null) {
                LayoutAI_App.emit(EventName.RenderPanoramaResult, { progress: "success", id: layoutSchemeId });
            } else {
                LayoutAI_App.emit(EventName.RenderPanoramaResult, { progress: "error" });
                console.error("Fail to create render panorama for layoutSchemId=" + layoutSchemeId);
            }
        });
    }

    protected postProcessWholeLayoutSchemeList() {
        this.whole_layout_scheme_list.forEach(wholeLayoutScheme => {
            wholeLayoutScheme.postProcess();
        });
    }

    async checkAndUpdateSchemeXml() {
        //  是否需要生成wall xml
        // if (TSeriesFurnisher.instance.room2SeriesSampleMap.size === 0) {
        //     LayoutAI_App.emit(EventName.PerformFurnishResult, { progress: "error", message: LayoutAI_App.t("无法布置，请先选择空间和搭配") });
        //     LayoutAI_App.emit(EventName.ShowOpenScheme, false);
        //     return;
        // }
        let container = this.manager.layout_container;
        let hasError = false;
        let errorRoomNames = [];
        for (let [roomItem, seriesSampleItem] of TSeriesFurnisher.instance.room2SeriesSampleMap.entries()) {

            if (roomItem._furniture_list.length <= 0 && roomItem.room_type != "厨房") {
                errorRoomNames.push(roomItem.roomname);
                hasError = true;
            }
        }

        if (container.needs_making_wall_xml || !container._is_from_3d_scheme) {
            if (container._is_from_3d_scheme) {
                let res = confirm(LayoutAI_App.t("检测到房型结构发生改变, 是否重新生成房型? 若否,仅会执行智能布置结果"));
                if (!res) {
                    LayoutAI_App.emit(EventName.ShowOpenScheme, false);
                    return;
                }
            }
            console.info("生成墙体XML!");
            let xml_text;
            if (container.needs_making_wall_xml) {
                xml_text = LayoutSchemeXmlJsonParser.generateHouseSchemeXml();
                container.current_swj_layout_data.xml_str = deflate_to_base64_str(xml_text);
                container.current_xml_is_regenerated = true;
            } else {
                xml_text = container.current_swj_layout_data.xml_str;
                if (!xml_text) {
                    xml_text = this._initial_scheme_data.xml_str;
                }
            }

            if (!is_standalone_website) {
                await SunvegaAPI.BasicBiz.Room.openSchemeFromXml({
                    xml: xml_text
                });
            }

            // 生成后 要把needs_making_wall_xml 设置成false
            this.manager.layout_container.needs_making_wall_xml = false;
        }
        else {
            LayoutAI_App.emit(EventName.ShowOpenScheme, false);
        }
    }
    updateRoomSelected(ev: I_MouseEvent) {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };

        let room_list = this.room_list;
        let target_room: TRoom = null;
        let scope = this;
        // 点击空间
        if (room_list && room_list.length > 0) {
            for (let room of room_list) {
                let poly = room.room_shape._poly;
                let dist = poly.distanceToPoint(pos);
                if (dist < 0) {
                    target_room = room;
                    break;
                }
            }
        }

        // 判断是否点击

        if (target_room && !target_room.selectable) {
            LayoutAI_App.emit(EventName.PerformFurnishResult, { progress: "error", message: "暂不支持其他空间的布置" });
            return;
        }
        let previousRooms = [...TSeriesFurnisher.instance.current_rooms];

        let current_rooms = TSeriesFurnisher.instance.current_rooms;
        let roomsFoundFromPrevious = (current_rooms == null || target_room == null) ? null : current_rooms.filter(function (roomItem: TRoom) {
            if (roomItem == null) return roomItem;
            return roomItem.uid == target_room.uid;
        });
        if (ev.ctrlKey) {
            if (roomsFoundFromPrevious != null && roomsFoundFromPrevious.length > 0) {
                TSeriesFurnisher.instance.current_rooms = TSeriesFurnisher.instance.current_rooms.filter(function (roomItem: TRoom) {
                    return roomItem.uid != target_room.uid;
                });
            } else {
                if (target_room != null) {
                    TSeriesFurnisher.instance.current_rooms.push(target_room);
                }
            }
        } else {
            if (target_room == null) {
                let rooms: TRoom[] = [];
                this.room_list.forEach((roomItem: TRoom) => {
                    if (roomItem._furniture_list && roomItem._furniture_list.length > 0 && roomItem.selectable) {
                        rooms.push(roomItem);
                    }
                });
                TSeriesFurnisher.instance.current_rooms = rooms;
            } else {
                TSeriesFurnisher.instance.current_rooms = [target_room];
            }
        }


        if (TSeriesFurnisher.instance.current_rooms.length > 1 ||
            (TSeriesFurnisher.instance.current_rooms.length == 1 && target_room != previousRooms[0])) {

            // 延迟10毫秒执行--- 先刷新前端选择, 再尝试计算，体验好一些
            TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering({ clickOnRoom: target_room != null });
            scope.update();
            window.setTimeout(() => {

                // 编辑training后台算法测试, 保存数据到本地
                try {
                    if (localStorage && LayoutAI_App.IsDebug) {
                        localStorage.setItem("layout_ai_training_current_room_data", JSON.stringify(target_room.exportExtRoomData()));
                    }
                } catch (error) {

                }
            }, 10);
        } else {

            TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering({ clickOnRoom: target_room != null });
        }
    }
}



