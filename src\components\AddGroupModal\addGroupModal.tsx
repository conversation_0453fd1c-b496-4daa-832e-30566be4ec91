import React, { useEffect, useImperativeHandle, forwardRef, useState } from 'react';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { Button, message, Modal, Select } from '@svg/antd';
import Icon from '@/components/Icon/icon'
import { observer } from 'mobx-react-lite';
import { useStore } from '@/models';
const AddGroupModal = forwardRef((props, ref) => {
  const store = useStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [combinatName, setCombinatName] = useState('');
  const t = LayoutAI_App.t;
  const onModal = () => {
    setIsModalOpen(true);
  }
  useImperativeHandle(ref, () => ({
    onModal
  }));
  return (
    <Modal
        title={t("创建组合")}
        maskClosable={false}
        centered
        open={isModalOpen}
        footer={false}
        width={300}
        onCancel={() => {
            setIsModalOpen(false);
        }}>

        <Select
        placeholder={t('请选择组合类型')}
        style={{ width: 240, margin: '10 auto' }}
        onChange={(e: any) => {
            setCombinatName(e)
        }}
        options={[
            { value: '沙发组合', label: t('沙发组合') },
            { value: '餐桌椅组合', label: t('餐桌椅组合') },
            { value: '床具组合', label: t('床具组合') },
            { value: '桌几组合', label: t('桌几组合') },
            { value: '岛台组合', label: t('岛台组合') },
            { value: '榻榻米组合', label: t('榻榻米组合') }
        ]}
        />
        <div style={{ textAlign: 'center', marginTop: '10px' }}>
        <Button onClick={() => {
            setIsModalOpen(false);
            LayoutAI_App.DispatchEvent(LayoutAI_Events.CreateCombination, combinatName);
            message.success(t('组合创建成功'));
        }} type='primary'>{t('确定')}</Button>
        </div>
    </Modal>

    )
})

export default observer(AddGroupModal);