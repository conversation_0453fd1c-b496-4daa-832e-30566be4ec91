

export const DesignXmlTemplate = `
<root>
    <AdjustGamma isAdjust="1" colorBleed="0"/>
    <VerInf Version="2.1"/>
    <State currentState="state3d"/>
    <RenderInfo Version="2_1">
      <AnimationSceneData version="1.0" value="bnVsbA=="/>
      <AnimationSettingData version="1.0" stickFloorTypeList="GrowupAnimation" onCeilingTypeList="GrowupAnimation" stickWallTypeList="GrowupAnimation" cabinetTypeList="GrowupAnimation" cabinetDoorWindowList="open" aluminumTypeList="GrowupAnimation" aluminumDoorWindowTypeList="open" doorWindowTypeList="GrowupAnimation"/>
      <SceneInfo lightSceneType="normal" lightTemplate="0" lightdomeIntensityValue="-1" isNormalAutoExposure="false" isPanoramaAutoExposure="false" isAerialAutoExposure="false" fillLightAffectSpecular="0"/>
      <QualityEffect type="1"/>
      <RenderQualityInfo>
        <RenderQuality qualityId="4" qualityCategory="2" selectCategory="true" selectVersion="2"/>
        <RenderQuality qualityId="1" qualityCategory="1" selectCategory="false" selectVersion="0"/>
        <RenderQuality qualityId="5" qualityCategory="3" selectCategory="false" selectVersion="0"/>
      </RenderQualityInfo>
    </RenderInfo>
    <SetUpViewConfig Version="2_2">
      <FootLine show="true"/>
      <CellingLine show="true"/>
      <WallHeight WallHeight3D="280"/>
      <ProgramType show="false"/>
      <ProgramWorkLayer show="false"/>
    </SetUpViewConfig>
    <FntAreaInfo Version="2_2"/>
    <Config Version="2_2">
      <SprungRoof show="true"/>
      <LightLayer show="true"/>
      <WallHeight WallHeight3D="280"/>
      <CameraVo targetPosX="0" targetPosY="0" targetPosZ="0" posX="224.42640451138183" posY="87.85798822181349" posZ="917.7448710736132" rotateX="-2.824171751516917" rotateY="0" rotateZ="1.3979816900534063" fov="1.46563" nearClipping="10" picIndex="0" aspectRatioType="0" timeLightType="0" cameraLensType="0" cameraLensAngle="0" isAdvanceLight="0" exposureValue="0" sunBrightness="正午/中" spotLightBrightness="12w/60w" spotLightTemperature="5000K" exposureV3="0.8" spotLightBrightnessV3="500" isCameraCut="0" cameraCutValue="100" sunLightEnable="1" sunAngle="-45" sunDirection="0" sunBrightnessV3="0.03" sunLightColor="16769984" sunSizeMutilplier="3" cameraName="null" radius="913.3729735203331" createType="manual" autoSunEnabled="true" autoSunEntityId="-1">
        <DomeLight/>
      </CameraVo>
    </Config>
    <CameraInfo Version="2_2"/>
    <BuildingBitmapInfo Version="2_2" BuildingBitmapUrl="" scaleX="undefined" scaleY="undefined" rotation="0" rotationZ="0" culling="front" mirror="false" mirrorUpDown="false"/>
    <BuildingBitmapVisibleInfo Version="2_2" isImportHomePlan="0"/>
    <BrickLayout/>
    <TESTBRICK hasBrick="0"/>
    <CeilingLayout version="1">
      <CeilingErrorInfo>
        <errInfo>0</errInfo>
        <realCount>0</realCount>
        <expectCount>0</expectCount>
      </CeilingErrorInfo>
    </CeilingLayout>
    <waterElectric Version="2_1">
      <nowRuleSetting base64="eJy9WEFrHDcU/itmzy7x2FkCS5JTrs2hgfRotDuyd9jZGXdGq2mhh0JKWwKNITQ0DoVAazA5JITG0GJD+2e63vhfVE96Gj1pJEMvZS/zvm/fp/e9p9XM7N2q7j5blfwRF6KoDrce86Yt6ureSPImG20VVc6/vDdSX9oHYD/b33y43Fy+vr58+fHd6frPJ5uLvz+efbv+4SQb3b/b1rMFF5/WOS+3JCtX/N4o228PmiI/5GKyPj1bP329efb93vrtT1fHz9cXZ9s9PZtkLuD55JO9nR0HHOSTvbGORcfaOW/E5Prl71fvz8eelOFQygREygBKKssMwIpmVld5IZThZHn0S6hMIaU/0FJL7OISc86EqzaxhPkSipsAyr5NJYjmjE2LSo0r2gIkUQwj3YQxTYYu7KJcXS/U5EkDAkXDW0UTKUVVDc0nilPWFlW8PE2hlL6G0rKxS1MyYxNKXomiZKJW7bs6eXf9zStfyvGo5wDi14Gkg0esKetVr7u74wkji6oY2Tlj6Mps6wMWLxAYFIFLaNl4p88hW5pNS24lMl9Dcyiir20dOnBVTHneFjmPF4KkbbuJ9AS9ZE9NsKJM1ISsk4PI1oWhc5fzdpFoNFCoApdWAq6hFtOrvOFty1OzQtaqmKgXMqGrhUN1MiFlSFQygRUykeuP4Cwhohg7Lc76WXGYtv296DNSwjTusCrf0+dk9vX/8dke3VJndFeI2Tw4oxewBdQJcPF0fXq+Pv7wz1/gBlDzQ8dAubhjgrzWv7LMReAQDlpYo4MTK1iiE7XaJerUWn/3hzoWtntkqX6XWgZjGJcO26OmqBYlzF7lbJ68xbQeX5r+OsCd7V1/SHqpiNpEdxje1oPtpkzMxWoaZCFqszB0WTDgZVgkYH2BEJDi8IAMFlGYW8IchbiAvYt4CQa0GeFdorP3Sddug9gE/24IU8sbVlTskKcG9+Ch+urVs+dwIzv+zRtgML5xOL8HD8d+5n+b4SA9OccsmOMgMzlLzFzWR5EshdoMdeks4iAjq0SGiSu0X6yYftY6YrFUSvfNIZgTshMeSKQGPcZfJ2eLR7EntW4Gx3dT10v/7hLgRat6PTlgZctjpODlDaS8OVemuCWcCpFi+medAFdVxHE5TabIASxjPZDJBsike3mDdZnyLWOmZax8GbMrE14lMYqPM/hA0lsl8MCsxwV2fS40HLAJipomMHVCYGqcwr51jwnRwWaneMp+fLMHZKIB0c1OuUgLBpud4pEmxDa7Tw1gGesBbnbRrPiASJiP7XWPixIxzzJWvYy5Dfc6wXusFav8q8GwHToYNaUCrx4VmvXJOEPtOpRacCg1TFDfMSUCUA79Ds+wHo86jc2UMBF4aFAOy5VDa+Eke7RHzHPGTFBXFhvYckTgixChMUrFcGrNYrRki1FzPea7czBAcHcui8O5+Lxo4M+Y/s6sQXhDf7ONgXo5q1Zl2YedmGwuX6x/PdEic8DMPT6UAupxXQqm/5L55f22B8FLwI8vNm8urn8+N//uBLy/jnuUGFRcd5gSZMBjavjlDh9vdTU6cEkIzODVwwb6bcoG887NSOsIC8B6t/z/t+7/CzzcilE="/>
      <PCLDistributionSet>
        <PLC回路1 HW_ElectricParam="PLC回路1" HW_Ep_Filter="16A单机滤波器" HW_Ep_PipeMaterial="JDG" HW_Ep_PipeRadius="DN20" HW_Ep_CableMat="WDZB" HW_Ep_CableType="BV" HW_Ep_CableSize="2.5" HW_Ep_CableNum="3" HW_Ep_LinearWay="CE"/>
        <PLC回路2 HW_ElectricParam="PLC回路2" HW_Ep_Filter="16A单机滤波器" HW_Ep_PipeMaterial="JDG" HW_Ep_PipeRadius="DN20" HW_Ep_CableMat="WDZB" HW_Ep_CableType="BV" HW_Ep_CableSize="2.5" HW_Ep_CableNum="3" HW_Ep_LinearWay="CE"/>
        <PLC回路3 HW_ElectricParam="PLC回路3" HW_Ep_Filter="16A单机滤波器" HW_Ep_PipeMaterial="JDG" HW_Ep_PipeRadius="DN20" HW_Ep_CableMat="WDZB" HW_Ep_CableType="BV" HW_Ep_CableSize="2.5" HW_Ep_CableNum="3" HW_Ep_LinearWay="CE"/>
      </PCLDistributionSet>
    </waterElectric>
    <sandPainter>
      <encodeErrInfo info="" version=""/>
      <decodeErrInfo info="" version=""/>
    </sandPainter>
    <CADInfo>
      <MapInfo DrawType="cadFlatMap" DrawFrame="auto" DrawScale="auto"/>
    </CADInfo>
    <MaterialIds value=""/>
    <swjia_scheme_5_0 form="XML">
      <SchemeRoot __protoflag__="0" _UIDN="322">
        <UIDMap UIDMapValueS=""/>
        <ChannelInfo/>
        <bim_modeling versionS="v_0"/>
        <su precisionModeN="1">
          <suLoftEntitys/>
          <suMaterials/>
        </su>
        <Light>
          <Relates/>
          <Entitys/>
        </Light>
        <Render>
          <AmbientInfo/>
        </Render>
        <ShowerRoom versionS="v0">
          <ShowerRoom>
            <Entities/>
            <Log logS="{&quot;subApp&quot;:&quot;bathRoom&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </ShowerRoom>
        </ShowerRoom>
        <WholeHouseDesign versionS="v_0">
          <Order NoS=""/>
          <CupBoard>
            <Entitys/>
            <Log logS="{&quot;subApp&quot;:&quot;cupboard&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </CupBoard>
          <Wardrobe>
            <Entitys/>
            <Log logS="{&quot;subApp&quot;:&quot;wardrobe&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </Wardrobe>
          <SystemCabinet>
            <Entitys/>
            <MaterialIDs idsS=""/>
            <Log logS="{&quot;subApp&quot;:&quot;systemCabinet&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </SystemCabinet>
          <BathCabinet>
            <Entitys/>
            <MaterialIDs idsS=""/>
            <Log logS="{&quot;subApp&quot;:&quot;bathCabinet&quot;,&quot;sum&quot;:0,&quot;errorNum&quot;:0,&quot;encodedNum&quot;:0,&quot;errorMsgs&quot;:[]}"/>
          </BathCabinet>
          <StainLessKnob/>
          <CProduceGlobalSchemeNode>
            <templates>
              <schemes/>
              <parts/>
              <accessorys/>
            </templates>
          </CProduceGlobalSchemeNode>
        </WholeHouseDesign>
        <SolidWoodDesign versionS="v_0">
          <Entitys/>
          <AttachedDatas/>
        </SolidWoodDesign>
        <BIM_WE versionS="5.0.0">
          <Concerns versionS="5.0.0"/>
          <Systems versionS="5.0.0"/>
          <Pipes versionS="5.0.0"/>
          <Connectors versionS="5.0.0"/>
          <Relations versionS="5.0.0"/>
          <VentAirFlows versionS="5.0.0" ventAirFlowN="0" ventAirFlowLevelN="0"/>
          <Arrangements versionS="5.0.0" nameS="系统方案" typeS="system" descriptionS="暂无描述" materialIdS="120859650">
            <arrSwitchList referenceSubstanceS="入户门" typeS="双联开关" numberN="1" distanceN="100" liftoffN="1300" indexN="0"/>
            <arrSwitchList referenceSubstanceS="卧室门" typeS="三联开关" numberN="1" distanceN="100" liftoffN="1300" indexN="0"/>
            <arrSwitchList referenceSubstanceS="阳台门" typeS="单联开关" numberN="1" distanceN="100" liftoffN="1300" indexN="0"/>
            <arrSwitchList referenceSubstanceS="卫生间门" typeS="单联开关" numberN="1" distanceN="100" liftoffN="1300" indexN="0"/>
            <arrSwitchList referenceSubstanceS="厨房门" typeS="双联开关" numberN="1" distanceN="100" liftoffN="1300" indexN="0"/>
            <arrSwitchList referenceSubstanceS="主卧门外" typeS="单联开关" numberN="1" distanceN="100" liftoffN="1300" indexN="0"/>
            <arrPowerElectricityList referenceSubstanceS="客厅沙发" typeS="五孔插座" numberN="3" distanceN="900" liftoffN="550" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="卧室床头" typeS="五孔插座" numberN="2" distanceN="0" liftoffN="700" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="电视" typeS="五孔插座" numberN="3" distanceN="0" liftoffN="550" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="冰箱" typeS="三孔插座" numberN="1" distanceN="300" liftoffN="350" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="洗衣机" typeS="三孔防水插座" numberN="1" distanceN="300" liftoffN="1200" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="壁挂空调" typeS="三孔插座" numberN="1" distanceN="300" liftoffN="2100" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="柜式空调" typeS="三孔插座" numberN="1" distanceN="300" liftoffN="350" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="热水器" typeS="三孔防水插座" numberN="1" distanceN="400" liftoffN="2100" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="浴室柜" typeS="五孔防水插座" numberN="1" distanceN="500" liftoffN="1400" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="橱柜台面" typeS="五孔插座" numberN="3" distanceN="500" liftoffN="1400" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="烟机" typeS="五孔插座" numberN="1" distanceN="500" liftoffN="1800" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="床尾" typeS="五孔插座" numberN="2" distanceN="0" liftoffN="550" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="星盆" typeS="五孔防水插座" numberN="1" distanceN="0" liftoffN="350" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="餐桌" typeS="五孔插座" numberN="1" distanceN="0" liftoffN="350" indexN="1"/>
            <arrPowerElectricityList referenceSubstanceS="梳妆台" typeS="五孔插座" numberN="1" distanceN="0" liftoffN="0" indexN="1"/>
            <arrWeakElectricityList referenceSubstanceS="电视" typeS="网线+电视插座" numberN="1" distanceN="400" liftoffN="550" indexN="2"/>
            <arrWeakElectricityList referenceSubstanceS="书桌" typeS="网线插座" numberN="1" distanceN="0" liftoffN="350" indexN="2"/>
            <arrWeakElectricityList referenceSubstanceS="床头" typeS="网线插座" numberN="0" distanceN="600" liftoffN="650" indexN="2"/>
            <arrWeakElectricityList referenceSubstanceS="马桶" typeS="报警按钮" numberN="1" distanceN="300" liftoffN="350" indexN="2"/>
            <arrWeakElectricityList referenceSubstanceS="沙发" typeS="网线插座" numberN="1" distanceN="900" liftoffN="550" indexN="2"/>
            <arrWaterSupplyList referenceSubstanceS="马桶" typeS="冷水" numberN="1" distanceN="0" liftoffN="100" indexN="3"/>
            <arrWaterSupplyList referenceSubstanceS="洗衣机" typeS="冷水" numberN="1" distanceN="0" liftoffN="1100" indexN="3"/>
            <arrWaterSupplyList referenceSubstanceS="浴缸" typeS="冷热水" numberN="1" distanceN="0" liftoffN="350" indexN="3"/>
            <arrWaterSupplyList referenceSubstanceS="浴室柜" typeS="冷热水" numberN="1" distanceN="0" liftoffN="350" indexN="3"/>
            <arrWaterSupplyList referenceSubstanceS="星盆" typeS="冷热水" numberN="1" distanceN="0" liftoffN="350" indexN="3"/>
            <arrWaterSupplyList referenceSubstanceS="花洒" typeS="冷热水" numberN="1" distanceN="0" liftoffN="1400" indexN="3"/>
            <listData referenceSubstanceS="单联开关" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/7574324114a7415088b618a38b3c68b2/26868040_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="101249332" widthN="86"/>
            <listData referenceSubstanceS="双联开关" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/dcc9273c7ce4403282a7a3a9e19dbdd4/27009013_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="101250882" widthN="86"/>
            <listData referenceSubstanceS="三联开关" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/8c7bcfdcbb0048a6ac052d2554aa5647/26868042_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="101250021" widthN="86"/>
            <listData referenceSubstanceS="四联开关" imagePathS="https://img3.admin.3vjia.com//UpFile/DesignMaterial/201909/12/09530839/RenderImage_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="09530839" widthN="86"/>
            <listData referenceSubstanceS="三孔插座" imagePathS="https://img3.admin.3vjia.com//UpFile/DesignMaterial/201909/12/09530762/RenderImage_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="09530762" widthN="86"/>
            <listData referenceSubstanceS="五孔插座" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/5c35afdb9d5d46548a6c43bba3cac7f9/42510667_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="101249321" widthN="86.2"/>
            <listData referenceSubstanceS="三孔防水插座" imagePathS="https://img3.admin.3vjia.com//UpFile/DesignMaterial/201909/12/34234102/RenderImage_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="34234102" widthN="88"/>
            <listData referenceSubstanceS="五孔防水插座" imagePathS="https://img3.admin.3vjia.com//UpFile/DesignMaterial/201909/12/09530748/RenderImage_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="09530748" widthN="88"/>
            <listData referenceSubstanceS="网线+电视插座" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/eb3cea1a9ccc480fac96eafe7ff90e8a/27136458_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="101249325" widthN="86"/>
            <listData referenceSubstanceS="网线插座" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/202207/17/172371507/VRenderImage_69007765_200x200.png" indexN="0" imageIndexN="0" materialIdS="172371507" widthN="86.01"/>
            <listData referenceSubstanceS="电话插座" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/990e9d1ac1324f8fb4a60cb9d40775ff/42510646_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="101249314" widthN="86.2"/>
            <listData referenceSubstanceS="报警按钮" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/04b921a012164904a06476111a401ba0/54413900_200x200.jpg" indexN="0" imageIndexN="0" materialIdS="101250878" widthN="86"/>
            <listData referenceSubstanceS="冷水" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/101281835/DefaultMainImage_200x200.png" indexN="0" imageIndexN="0" materialIdS="101281835" widthN="45.98"/>
            <listData referenceSubstanceS="冷热水" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/101281235/DefaultMainImage_200x200.png" indexN="0" imageIndexN="0" materialIdS="101281235" widthN="192.05"/>
            <listData referenceSubstanceS="热水" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/101283620/DefaultMainImage_200x200.png" indexN="5" imageIndexN="0" materialIdS="101283620" widthN="45.98"/>
            <listData referenceSubstanceS="热水源" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/202207/01/167227609/VRenderImage_2030496319_200x200.png" indexN="5" imageIndexN="0" materialIdS="167227609" widthN="45.98"/>
            <listData referenceSubstanceS="强电箱" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/fedd7aef0ce24d7d8f343192a7b15c0b/08477684_200x200.jpg" indexN="5" imageIndexN="0" materialIdS="101283623" widthN="270"/>
            <listData referenceSubstanceS="弱电箱" imagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/PMC/DesignMaterial/202003/5ffce051f5b8491ab80f4cf9cbd2936a/08491630_200x200.jpg" indexN="5" imageIndexN="0" materialIdS="101282728" widthN="410"/>
          </Arrangements>
          <ElectricBoxSchemes versionS="5.0.0" voltageS="220V/50Hz/40A" idN="2" nameS="系统配电">
            <ElectricBoxScheme powerS="配电箱" idN="7340033" modelIdS="">
              <electricBoxCircuit idN="8388609" nameS="总控" iCircuitS="C65H/1P/16A|2*4.0+1*2.5|PVC-DN20|总控"/>
              <electricBoxCircuit idN="8388610" nameS="照明" iCircuitS="C65H/1P/16A|2*2.5|PVC-DN20|照明"/>
              <electricBoxCircuit idN="8388611" nameS="厨房插座" iCircuitS="C65H/1P/16A|2*2.5+1*1.5|PVC-DN20|厨房插座"/>
              <electricBoxCircuit idN="8388612" nameS="卧室插座" iCircuitS="C65H/1P/16A|2*2.5+1*1.5|PVC-DN20|卧室插座"/>
              <electricBoxCircuit idN="8388613" nameS="冰箱专线" iCircuitS="C65H/1P/16A|2*2.5+1*1.5|PVC-DN20|冰箱专线"/>
              <electricBoxCircuit idN="8388614" nameS="集成灶专线" iCircuitS="C65H/1P/16A|2*4.0+1*1.5|PVC-DN20|集成灶专线"/>
              <electricBoxCircuit idN="8388615" nameS="客厅插座" iCircuitS="C65H/1P/16A|2*4.0+1*1.5|PVC-DN20|客厅插座"/>
              <electricBoxCircuit idN="8388616" nameS="卫生间插座" iCircuitS="C65H/1P/16A|2*2.5+1*1.5|PVC-DN20|卫生间插座"/>
              <electricBoxCircuit idN="8388617" nameS="客厅空调专线" iCircuitS="C65H/1P/16A|2*2.5+1*1.5|PVC-DN20|客厅空调专线"/>
              <electricBoxCircuit idN="8388618" nameS="卧室空调专线" iCircuitS="C65H/1P/16A|2*2.5+1*1.5|PVC-DN20|卧室空调专线"/>
              <electricBoxCircuit idN="8388619" nameS="次卧空调专线" iCircuitS="C65H/1P/16A|2*2.5+1*1.5|PVC-DN20|次卧空调专线"/>
              <electricBoxCircuit idN="8388620" nameS="休闲区插座" iCircuitS="C65H/1P/16A|2*4.0+1*2.5|PVC-DN20|休闲区插座"/>
            </ElectricBoxScheme>
          </ElectricBoxSchemes>
          <Distributions versionS="5.0.0" nameS="系统配电" typeS="system" descriptionS="暂无描述" materialIdS="109155238" voltageS="220V/50Hz/40A">
            <Electricbox electricBoxIdN="7340033" nameS="配电箱" pdnameS="PD1" imagePathS="https://img3.admin.3vjia.com//UpFile/DesignMaterial/201909/12/08477684/RenderImage.jpg" materialIdS="08477684" uidS="773AF751-F327-D5E4-555A-8262392464C7">
              <circuit circuitIdN="8388609" switchNameS="C65H/1P/16A" loopNameS="L1" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*4.0+1*2.5" useTypeS="总控"/>
              <circuit circuitIdN="8388610" switchNameS="C65H/1P/16A" loopNameS="L2" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5" useTypeS="照明"/>
              <circuit circuitIdN="8388611" switchNameS="C65H/1P/16A" loopNameS="L3" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5+1*1.5" useTypeS="厨房插座"/>
              <circuit circuitIdN="8388612" switchNameS="C65H/1P/16A" loopNameS="L4" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5+1*1.5" useTypeS="卧室插座"/>
              <circuit circuitIdN="8388613" switchNameS="C65H/1P/16A" loopNameS="L5" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5+1*1.5" useTypeS="冰箱专线"/>
              <circuit circuitIdN="8388614" switchNameS="C65H/1P/16A" loopNameS="L6" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*4.0+1*1.5" useTypeS="集成灶专线"/>
              <circuit circuitIdN="8388615" switchNameS="C65H/1P/16A" loopNameS="L7" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*4.0+1*1.5" useTypeS="客厅插座"/>
              <circuit circuitIdN="8388616" switchNameS="C65H/1P/16A" loopNameS="L8" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5+1*1.5" useTypeS="卫生间插座"/>
              <circuit circuitIdN="8388617" switchNameS="C65H/1P/16A" loopNameS="L9" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5+1*1.5" useTypeS="客厅空调专线"/>
              <circuit circuitIdN="8388618" switchNameS="C65H/1P/16A" loopNameS="L10" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5+1*1.5" useTypeS="卧室空调专线"/>
              <circuit circuitIdN="8388619" switchNameS="C65H/1P/16A" loopNameS="L11" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*2.5+1*1.5" useTypeS="次卧空调专线"/>
              <circuit circuitIdN="8388620" switchNameS="C65H/1P/16A" loopNameS="L12" switchImagePathS="https://img3.admin.3vjia.com//UpFile/C00000022/DesignMaterial/201910/10/55438880/RenderImage_200x200.jpg" switchMaterialIdS="58186140" pipeNameS="PVC-DN20" wireNameS="2*4.0+1*2.5" useTypeS="休闲区插座"/>
            </Electricbox>
          </Distributions>
        </BIM_WE>
        <Assembly versionS="0">
          <MaterialIDs idsS=""/>
        </Assembly>
        <BrickLayoutDesign versionS="v_0">
          <PaveBrick>
            <MaterialMap materialIdsU="NA" roomGapMaterialS=""/>
            <Entitys/>
          </PaveBrick>
        </BrickLayoutDesign>
        <coating versionS="v_0"/>
        <BIM_WE_HUAWEI versionS="5.0.0">
          <PCLDistributionSets versionS="5.0.0">
            <PCLDistributionSet idN="1" nameS="PLC回路1" filterS="16A单机滤波器" pipeMatS="JDG" radiusSetS="DN20" cableMatS="WDZB" cableTypeS="BV" cableSizeN="2.5" cableNumN="3" linearWayS="CE"/>
            <PCLDistributionSet idN="2" nameS="PLC回路2" filterS="16A单机滤波器" pipeMatS="JDG" radiusSetS="DN20" cableMatS="WDZB" cableTypeS="BV" cableSizeN="2.5" cableNumN="3" linearWayS="CE"/>
            <PCLDistributionSet idN="3" nameS="PLC回路3" filterS="16A单机滤波器" pipeMatS="JDG" radiusSetS="DN20" cableMatS="WDZB" cableTypeS="BV" cableSizeN="2.5" cableNumN="3" linearWayS="CE"/>
            <PCLMainFrameSet idN="NaN" nameS="PLC强电箱供电" filterS="空" pipeMatS="JDG" radiusSetS="DN20" cableMatS="WDZB" cableTypeS="BV" cableSizeN="2.5" cableNumN="3" linearWayS="CE"/>
          </PCLDistributionSets>
          <PCLCircuitCorrelationSchemes versionS="5.0.0"/>
          <HW_ControlAssociations versionS="5.0.0"/>
          <HW_DeviceInfos versionS="5.0.0"/>
          <HW_NetAssociations versionS="5.0.0"/>
          <HW_KeyConfigs versionS="5.0.0"/>
          <HW_Sensors versionS="5.0.0"/>
        </BIM_WE_HUAWEI>
        <AluminumDoorWindow versionS="v_0">
          <Entitys/>
        </AluminumDoorWindow>
        <FurnitureDoorWindow versionS="v_0">
          <Entitys/>
        </FurnitureDoorWindow>
        <bim versionS="v_0">
          <elements versionS="v_0">
            <building uidN="1" nameS="bud"/>
            <level uidN="2" buildingUIDN="1" heightN="0" indexN="0"/>
            <level uidN="3" buildingUIDN="1" heightN="3000" indexN="1"/>
            <storey uidN="4" bottomLevelUIDN="2" topLevelUIDN="3" buildingUIDN="1" alphaN="1" floorSlabAlphaN="1"/>
            <mWall uidN="5" wallTypeN="1" thicknessN="240" isLockedB="F" storeyUIDN="4" topLevelUIDN="3" bottomLevelUIDN="2" heightN="2800" isCustomProfileB="F">
              <location typeS="Line" locationTypeS="line">
                <endpoint0 xN="-2760" yN="4320"/>
                <endpoint1 xN="1880" yN="4320"/>
              </location>
              <profile>
                <boundary>
                  <curve typeS="Line">
                    <endpoint0 xN="2000.000000000001" yN="4200"/>
                    <endpoint1 xN="2000.000000000001" yN="4440"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="2000.000000000001" yN="4440"/>
                    <endpoint1 xN="-2880" yN="4440"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="-2880" yN="4440"/>
                    <endpoint1 xN="-2880" yN="4200"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="-2880" yN="4200"/>
                    <endpoint1 xN="2000.000000000001" yN="4200"/>
                  </curve>
                </boundary>
              </profile>
            </mWall>
            <mWall uidN="6" wallTypeN="1" thicknessN="240" isLockedB="F" storeyUIDN="4" topLevelUIDN="3" bottomLevelUIDN="2" heightN="2800" isCustomProfileB="F">
              <location typeS="Line" locationTypeS="line">
                <endpoint0 xN="1880" yN="4320"/>
                <endpoint1 xN="1880" yN="-400"/>
              </location>
              <profile>
                <boundary>
                  <curve typeS="Line">
                    <endpoint0 xN="1760" yN="-520"/>
                    <endpoint1 xN="2000" yN="-520"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="2000" yN="-520"/>
                    <endpoint1 xN="2000.000000000001" yN="4200"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="2000.000000000001" yN="4200"/>
                    <endpoint1 xN="1760" yN="4200"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="1760" yN="4200"/>
                    <endpoint1 xN="1760" yN="-520"/>
                  </curve>
                </boundary>
              </profile>
            </mWall>
            <mWall uidN="7" wallTypeN="1" thicknessN="240" isLockedB="F" storeyUIDN="4" topLevelUIDN="3" bottomLevelUIDN="2" heightN="2800" isCustomProfileB="F">
              <location typeS="Line" locationTypeS="line">
                <endpoint0 xN="1880" yN="-400"/>
                <endpoint1 xN="-2760" yN="-400"/>
              </location>
              <profile>
                <boundary>
                  <curve typeS="Line">
                    <endpoint0 xN="-2880.000000000001" yN="-280"/>
                    <endpoint1 xN="-2880.000000000001" yN="-520"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="-2880.000000000001" yN="-520"/>
                    <endpoint1 xN="1760" yN="-520"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="1760" yN="-520"/>
                    <endpoint1 xN="1760" yN="-280"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="1760" yN="-280"/>
                    <endpoint1 xN="-2880.000000000001" yN="-280"/>
                  </curve>
                </boundary>
              </profile>
            </mWall>
            <mWall uidN="8" wallTypeN="1" thicknessN="240" isLockedB="F" storeyUIDN="4" topLevelUIDN="3" bottomLevelUIDN="2" heightN="2800" isCustomProfileB="F">
              <location typeS="Line" locationTypeS="line">
                <endpoint0 xN="-2760" yN="-400"/>
                <endpoint1 xN="-2760" yN="4320"/>
              </location>
              <profile>
                <boundary>
                  <curve typeS="Line">
                    <endpoint0 xN="-2640" yN="4200"/>
                    <endpoint1 xN="-2880" yN="4200"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="-2880" yN="4200"/>
                    <endpoint1 xN="-2880.000000000001" yN="-280"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="-2880.000000000001" yN="-280"/>
                    <endpoint1 xN="-2640" yN="-280"/>
                  </curve>
                  <curve typeS="Line">
                    <endpoint0 xN="-2640" yN="-280"/>
                    <endpoint1 xN="-2640" yN="4200"/>
                  </curve>
                </boundary>
              </profile>
            </mWall>
            <ceiling uidN="11" heightN="0" roomUIDN="9" visibleB="T"/>
            <floor uidN="12" heightN="0" roomUIDN="9"/>
            <floorFootLine uidN="13" roomUIDN="9" sectionMaterialIDS="00811419" mapMaterialIDS="02000021" sectionWidthN="16" sectionHeightN="99.96" embeddedDepthN="0"/>
            <ceilingFootLine uidN="14" roomUIDN="9" sectionMaterialIDS="18446266" mapMaterialIDS="02008870" sectionWidthN="67" sectionHeightN="90" embeddedDepthN="0">
              <profile>
                <curve typeS="Line">
                  <endpoint0 xN="-2640" yN="-280" zN="2800"/>
                  <endpoint1 xN="1760" yN="-280" zN="2800"/>
                </curve>
              </profile>
            </ceilingFootLine>
          </elements>
          <bim_extra versionS="v_0">
          </bim_extra>
          <MaterialIDs idsS="00811419,02000021,18446266,02008870"/>
        </bim>
        <bimEmbeddings versionS="v_0"/>
        <bimfaces versionS="v_0">
          <face bizObjectTypeS="ASSING_MATERIAL" ownerUIDN="12" isRegionB="F" materialIdS="185639178" materialColorN="0" uvRotateN="0" uvXN="0" uvYN="0" uvScalXN="1" uvScalYN="1">
            <FacePickData>
              <point xN="1759" yN="3752" zN="0"/>
              <normal xN="0" yN="0" zN="1"/>
              <uvPoint xN="2199" yN="1792"/>
            </FacePickData>
            <materialTrf picWidthN="4000" pickHeightN="4000">
              <Matrix3x3 row1U="1,0,0" row2U="0,1,0" row3U="0,0,1"/>
            </materialTrf>
          </face>
          <MaterialIDs idsS="185639178"/>
        </bimfaces>
        <CCeilingLayout versionS="v_0">
          <spaceData/>
          <Entitys/>
          <Log/>
        </CCeilingLayout>
        <Basic versionS="v_0">
          <FurnitureGroups/>
          <Furnitures/>
          <CMakeUpCeilings/>
          <Lights/>
          <LightTemplates selectIdS="LS202311230000000000000013218548" versionS="3.0"/>
          <MaterialIDs idsS=""/>
        </Basic>
      </SchemeRoot>
    </swjia_scheme_5_0>
    <ModifyInfo Version="3" AutoSave="false" Complete="true" isQualityExistFlag="0"/>
    <Logs ServerTime="2023-11-23 20:27:11">
      <version v="4.0.0.0"/>
      <Log type="INFO" date="11-23 20:27:10" name="方案编码" message="开始编码方案, id=: 111399634" time="424982" className="base_service.scheme.service.ProjectDataManager"/>
      <Log type="INFO" date="11-23 20:27:10" name="00-15-0005" message="开始序列化铺砖数据，主区域数量为：0" time="425038" className="swjia_pavebrick.service.BrickSchemeTreeNode"/>
      <Log type="INFO" date="11-23 20:27:10" name="00-15-0005" message="铺砖数据序列化完毕，成功序列化主区域数量为：0" time="425038" className="swjia_pavebrick.service.BrickSchemeManager"/>
      <Log type="INFO" date="11-23 20:27:10" name="铝门窗模块编码：" message="" time="425040" className="swjia_alucustomization.service.main.CAluSchemeTreeNode"/>
      <Log type="INFO" date="11-23 20:27:10" name="成品门窗模块编码：" message="" time="425040" className="swjia_alucustomization.service.main.CFurnitureDoorWindowTreeNode"/>
    </Logs>
</root>
`;