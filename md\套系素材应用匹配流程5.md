# TSeriesFurnisher.onSeriesSampleSelected方法解析

## 方法概述

`onSeriesSampleSelected`方法是套系应用流程在系统内核层的起点，当用户在界面上选择一个套系后，系统会触发此方法，将选定的套系应用到一个或多个目标房间中。

## 名词解释

- **套系/风格套系 (seriesSample/series)**: 包含一系列匹配的家具、装饰和材质的集合，用于快速统一房间风格
- **应用范围 (scope)**: 套系应用的范围，包括以下几种类型：
  - 软装 (soft): 如沙发、床、桌椅等可移动家具
  - 定制柜 (cabinet): 如橱柜、衣柜等定制家具
  - 硬装 (hard): 如地板、墙面、天花板等固定装修
  - 补全 (remaining): 对未布置素材的图元进行自动补充
- **图元 (FigureElement)**: 房间中的家具、装饰、墙面等可布置物品的基本单位
- **素材 (Material)**: 可以应用到图元上的具体模型和材质

## 方法参数说明

```typescript
async onSeriesSampleSelected(
    scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean },
    series: TSeriesSample,
    targetRooms: TRoom[] = null,
    options: {
        needsDiversifyMatched?: boolean,  // 是否需要多样化匹配
        updateTopViewBy3d?: boolean,      // 是否需要更新3D视图
        needAutoFinetune?: boolean        // 是否需要自动微调
    } = { needsDiversifyMatched: true, updateTopViewBy3d: true, needAutoFinetune: true }
)
```

## 方法执行流程详解

### 1. 初始化处理阶段

当用户选择一个套系样本后，系统首先会执行以下初始化操作：

1. 取消当前选中图元的选中状态
2. 确定目标房间列表（如果未指定，则使用当前选中的房间）
3. 检查目标房间是否存在，如不存在则提示用户先选择空间

### 2. 套系应用阶段（房间级处理）

系统会遍历每个目标房间，对每个房间执行以下操作：

1. 检查房间是否已被锁定，如已锁定则跳过处理
2. 为房间记录需要应用套系的范围和套系信息
3. 设置当前应用范围并清除此范围内图元已匹配的素材
4. 更新房间和套系的映射关系
5. 调用`tryToFurnishRooms`方法开始套系应用过程

### 3. 素材匹配阶段（实际匹配流程）

当调用`tryToFurnishRooms`方法后，系统会为每个房间执行以下匹配流程：

1. 绑定房间和家具的关系
2. 更新房间已应用套系的范围
3. 调用`TMaterialMatcher.furnishRoom`方法进行具体的素材匹配

#### 3.1 素材匹配准备

`furnishRoom`方法首先会执行以下准备工作：

1. 更新房间当前应用的套系信息
2. 清理房间中的电器装饰图元
3. 根据应用场景收集需要匹配素材的图元列表：
   - 常规套系应用：收集房间中需要匹配的图元
   - 补全套系应用：收集未匹配套系素材的图元
4. 处理硬装图元（如需要应用硬装类别）
5. 过滤出当前应用范围内的图元
6. 收集台面家具上的装饰图元和组合类图元的成员图元

#### 3.2 服务调用匹配

准备工作完成后，系统会：

1. 如果需要匹配硬装，生成固定装置图元（如筒灯、插座等）
2. 将组合图元成员和装饰图元添加到匹配列表
3. 调用素材匹配服务，为每个图元查找合适的素材
4. 根据匹配规则为每个图元分配最佳匹配素材

#### 3.3 匹配后处理流程

匹配完成后，系统会执行以下后处理操作：

1. **调整素材和图元位置尺寸**：`MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements`
   - 根据图元和素材特性调整素材的目标布置位置
   - 处理特定家具的旋转问题
   - 根据不同类型素材设置目标尺寸（组合素材、可缩放素材等）
   - 对定制柜类型素材进行特殊尺寸调整
   - 限制特定素材（如窗帘）的最大宽度
   - 检查并限制素材高度不超过层高

2. **背景墙处理**：`MatchingPostProcesser.adjust_backgroundwall_related`
   - 限制背景墙素材深度最大为120mm
   - 调整背景墙相关家具图元，使其与背景墙对齐

3. **吊顶处理**：
   - 根据橱柜调整吊顶矩形区域
   - 根据房间调整吊顶顶部偏移量

4. **装饰品调整**：`TPostDecoratesLayout.post_adjust_decorations`
   - 调整装饰品的位置和朝向

5. **特殊类别处理**：
   - 如果是定制柜类别：调整补板
   - 如果是软装类别：调整窗帘尺寸和基于吊顶调整素材位置

6. **重复素材处理**：`MatchingPostProcesser.hideDuplicatedMaterialOfSameModelloc`
   - 隐藏同一模型位的重复素材（如地毯、挂画、背景墙等）

### 4. 完成阶段

所有房间的素材匹配完成后，系统会：

1. 更新UI层，通知套系选择和房间映射关系的变化
2. 根据套系生成报价数据
3. 更新当前套系信息
4. 更新界面，重新绘制canvas
5. 如果配置了自动微调，执行微调流程

## 重要的业务逻辑

1. **锁定机制**：被锁定的房间不会被套系应用流程处理
2. **范围控制**：套系只应用到指定范围内的图元上
3. **补全机制**：可以识别未匹配素材的图元并进行补全
4. **多样化匹配**：允许为同类图元匹配不同风格的素材
5. **后处理优化**：通过一系列后处理步骤确保素材布置合理美观

## 流程图

```
┌───────────────────┐
│  用户选择套系样本  │
└─────────┬─────────┘
          ▼
┌───────────────────┐
│ 调用onSeriesSample │
│   Selected方法     │
└─────────┬─────────┘
          ▼
┌───────────────────┐     ┌───────────────────┐
│  初始化处理参数    │────▶│  确定目标房间列表  │
└─────────┬─────────┘     └─────────┬─────────┘
          ▼                         ▼
┌───────────────────┐     ┌───────────────────┐
│ 遍历处理每个房间   │────▶│ 更新房间套系信息   │
└─────────┬─────────┘     └─────────┬─────────┘
          ▼                         ▼
┌───────────────────┐     ┌───────────────────┐
│ 调用tryToFurnish   │────▶│ 收集待匹配图元    │
│      Rooms         │     └─────────┬─────────┘
└─────────┬─────────┘               ▼
          │           ┌───────────────────┐
          │           │ 调用素材匹配服务   │
          │           └─────────┬─────────┘
          │                     ▼
          │           ┌───────────────────┐
          │           │ 匹配后处理流程     │
          │           └─────────┬─────────┘
          ▼                     ▼
┌───────────────────┐     ┌───────────────────┐
│ 更新UI界面显示    │◀────│ 执行自动微调流程   │
└───────────────────┘     └───────────────────┘
```

## 产品注意事项

1. 套系应用是一个复杂的过程，可能需要较长时间，应考虑添加进度提示
2. 不同类型房间（卧室、客厅、厨房等）的套系匹配规则有所不同
3. 补全功能对于提升用户体验非常重要，可以减少用户手动操作
4. 自动微调功能可以优化家具布局，但也可能导致不符合用户预期的变化
5. 多样化匹配可以增加设计的丰富性，但也可能使设计风格不够统一