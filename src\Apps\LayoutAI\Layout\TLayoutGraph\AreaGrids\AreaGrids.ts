import { Vector3, Vector3<PERSON>ike } from "three";
import { TPainter } from "../../../../LayoutAI/Drawing/TPainter";
import { compareNames, loadImage } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { IRoomEntityType } from "../../IRoomInterface";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { TRoom } from "../../TRoom";

export interface I_GridEdge {
    grid0: AreaGrid;
    grid1: AreaGrid;

    _edge: ZEdge;


    _uid?: string;
    _is_visited?: number;
    _prev_edge?: I_GridEdge;
    _path_dist?: number;
}

/**
 *  区域格子
 */
export class AreaGrid {
    _pos: Vector3;
    _grid_pos: Vector3;
    _borad: AreaGridBoard;

    _neighbor_grids: AreaGrid[];
    _neighbor_edges: I_GridEdge[];

    _area_category: string = "";
    _area_type: IRoomEntityType;

    _seed_category: string = "";

    /**
     *  过道权重---如果是在某个家具内, 其值为0; 如果在家具附近, 其值为0.5
     */
    _hallway_weight: number = 1.;

    _distance_to_boundary: number = 0;


    _ex_prop: { [key: string]: string };
    constructor(pos: Vector3Like, board: AreaGridBoard) {
        this._pos = new Vector3().copy(pos);
        this._grid_pos = this._pos.clone();
        this._borad = board;
        this._neighbor_grids = null;
        this._area_type = "RoomArea";
        this._area_category = "";

        this._hallway_weight = 1.;

        this._distance_to_boundary = 0;

        this._ex_prop = {};
    }

    initGridWeight() {
        this._area_category = "";
        this._hallway_weight = this._distance_to_boundary >= 600 ? 1. : 0.5;
    }


    getNeighborGrid(dx: number, dy: number) {
        let vv = this._borad.posToVec(this._pos);
        vv.x += dx;
        vv.y += dy;
        return this._borad._grid_dict[vv.x + "_" + vv.y];
    }

    addCategory(category: string, type: IRoomEntityType = "Furniture") {
        this._area_type = type;
        if (this._area_category.includes(category)) return;
        this._area_category = this._area_category + category + " ";
    }

    isBoundary() {
        return this._neighbor_edges.length < 4;
    }
    getNeigbhors() {
        if (!this._neighbor_grids) {
            let dx = [1, 0, 0, -1];
            let dy = [0, 1, -1, 0];
            let grids: AreaGrid[] = [];
            for (let i = 0; i < 4; i++) {
                let grid = this.getNeighborGrid(dx[i], dy[i]);
                if (grid) grids.push(grid);
            }
            this._neighbor_grids = grids;
            this._neighbor_edges = [];
            for (let grid of this._neighbor_grids) {
                let edge = new ZEdge({ pos: this._grid_pos }, { pos: grid._grid_pos });
                edge.computeNormal();
                this._neighbor_edges.push({
                    grid0: this,
                    grid1: grid,
                    _edge: edge
                });
            }
        }

        return this._neighbor_grids;
    }
    public drawCanvas(painter: TPainter) {
        let dx = [1, 0];
        let dy = [0, 1];

        let neighbors = this.getNeigbhors();
        if (this._seed_category.length > 0) {
            painter.strokeStyle = "#0f0";
        }
        else if (this._area_category.length == 0) {
            if (this._distance_to_boundary < 100) {
                painter.strokeStyle = "#333";

            }
            else {
                painter.strokeStyle = "#aaa";

            }
        }
        else {
            painter.strokeStyle = "#f00";
        }
        for (let i = 0; i < 2 && i < neighbors.length; i++) {
            let grid = neighbors[i];
            if (grid) {
                let edge = new ZEdge({ pos: this._grid_pos }, { pos: grid._grid_pos });
                painter.drawEdges([edge]);
            }

        }

        painter.drawPointCircle(this._grid_pos, 10, 2);

        // painter.drawText(''+this._distance_to_boundary, this._grid_pos,0,10);
    }
}



/**
 *  格子棋盘
 */
export class AreaGridBoard {
    _grid_dict: { [key: string]: AreaGrid };
    _grid_len: number;


    _main_rect: ZRect;
    _room: TRoom;

    _drawing_image: HTMLImageElement;
    _drawing_rect: ZRect;

    /**
     *  动线种子点
     */
    _seed_grids: AreaGrid[];

    _moving_lines: AreaGridMovingLine[];
    constructor(grid_len: number = 50) {
        this._grid_dict = {};
        this._grid_len = grid_len;
        this._main_rect = new ZRect(1, 1);
        this._room = null;
        this._drawing_image = null;

        this._seed_grids = [];
        this._moving_lines = [new AreaGridMovingLine("厨房", "餐桌", this, "#3f3"),
        new AreaGridMovingLine("入户", "沙发", this, "#03f")];
    }


    protected projectInRect(pos: Vector3Like) {
        return this._main_rect.project(pos);
    }

    protected unprojectInRect(pos: Vector3Like) {
        return this._main_rect.unproject(pos);
    }
    posToCode(pos: Vector3Like) {
        let vv = this.posToVec(pos);

        return `${vv.x}_${vv.y}`;
    }

    posToVec(pos: Vector3Like) {
        let t_pos = this.projectInRect(pos);
        let x = Math.round(t_pos.x / this._grid_len);
        let y = Math.round(t_pos.y / this._grid_len);
        return { x: x, y: y, z: 0 };
    }



    codeToPos(code: string): Vector3Like {
        let t_code = code.replaceAll("_", ",");

        try {
            let arr = eval('[' + t_code + ']')
            return this.unprojectInRect({ x: (arr[0] || 0) * this._grid_len, y: (arr[1] || 0) * this._grid_len, z: 0 });
        } catch (error) {
            return this.unprojectInRect(({ x: 0, y: 0, z: 0 }));
        }

    }

    getGrid(pos: Vector3Like) {
        let code = this.posToCode(pos);
        return this._grid_dict[code] || null;
    }

    clean() {
        for (let key in this._grid_dict) {
            delete this._grid_dict[key];
        }
        this._grid_dict = {};
    }
    public addGrid(pos: Vector3Like) {
        let code = this.posToCode(pos);

        if (!this._grid_dict[code]) {
            this._grid_dict[code] = new AreaGrid(pos, this);
        }
        return this._grid_dict[code];
    }

    public addSeedPoint(pos0: Vector3Like, category: string) {
        let grid = this.getGrid(pos0);

        if (!grid) {
            return null;
        }
        grid._seed_category += category;
        this._seed_grids.push(grid);

        return grid;
    }
    public initGridsByRoom(room: TRoom, furniture_list: TFigureElement[] = null) {
        console.time("initGrids");
        this._room = room;
        this._main_rect = ZRect.computeMainRect(room.room_shape._poly);

        for (let x = -this._main_rect.w / 2 - this._grid_len / 2; x <= this._main_rect.w / 2 + this._grid_len / 2; x += this._grid_len) {
            for (let y = -this._main_rect._h / 2 - this._grid_len / 2; y <= this._main_rect._h / 2 + this._grid_len / 2; y += this._grid_len) {
                let pos = this.unprojectInRect({ x: x, y: y, z: 0 });

                let dist = room.room_shape._poly.distanceToPoint(pos);
                if (dist < 0) {
                    this.addGrid(pos);
                }
            }
        }
        let count = 0;
        for (let key in this._grid_dict) {
            this._grid_dict[key].getNeigbhors();
            count++;
        }

        this.updateDistanceToBoundary();
        for (let door of room.windows) {
            if (!door.rect) continue;
            if (door.type === "Door") {
                if (compareNames(door.room_names, ["厨房"])) {
                    let pos0 = door.rect.unproject({ x: 0, y: door.rect.h / 2 + this._grid_len });
                    let pos1 = door.rect.unproject({ x: 0, y: -door.rect.h / 2 - this._grid_len });

                    this.addSeedPoint(pos0, "厨房") || this.addSeedPoint(pos1, "厨房");
                }
                else if (door.room_names.length < 2 || compareNames(door.room_names, ["入户"])) {
                    let pos0 = door.rect.unproject({ x: 0, y: door.rect.h / 2 + this._grid_len });
                    let pos1 = door.rect.unproject({ x: 0, y: -door.rect.h / 2 - this._grid_len });

                    this.addSeedPoint(pos0, "入户") || this.addSeedPoint(pos1, "入户");
                }
            }
        }

        if (furniture_list) {
            let scope = this;
            furniture_list.forEach((figure) => {
                scope.setCategoryInRect(figure.rect, figure.category);
            })
        }
        console.timeEnd("initGrids");

    }


    public updateDistanceToBoundary() {
        let queue: AreaGrid[] = [];
        const is_visited = 'is_visited';
        for (let key in this._grid_dict) {
            let grid = this._grid_dict[key];
            if (grid.isBoundary()) {
                grid._distance_to_boundary = 0;
                grid._ex_prop[is_visited] = '1';
                queue.push(grid);
            }
            else {
                grid._ex_prop[is_visited] && delete grid._ex_prop[is_visited];
                grid._distance_to_boundary = this._main_rect.w + this._main_rect.h;
            }
        }

        let ll = queue.length;
        for (let qi = 0; qi < queue.length; qi++) {
            let grid = queue[qi];

            let neighbor_grids = grid.getNeigbhors();

            for (let n_grid of neighbor_grids) {
                let p_dist = grid._distance_to_boundary + this._grid_len;

                n_grid._distance_to_boundary = Math.min(n_grid._distance_to_boundary, p_dist);

                let check_visited = n_grid._ex_prop[is_visited] || null;
                if (!check_visited) {
                    n_grid._ex_prop[is_visited] = '1';
                    queue.push(n_grid);
                }
            }
        }

    }
    public updateByFurnitureList(furniture_list: TFigureElement[] = null, ignore_categories: string[] = ["背景墙", "帘", "餐椅", "书椅", "地毯", "灯"]) {
        // console.time("updateByFurnitureList");
        for (let key in this._grid_dict) {
            this._grid_dict[key].initGridWeight();
        }

        if (furniture_list) {
            let scope = this;

            furniture_list.forEach(figure => {
                if (compareNames([figure.sub_category, figure.category], ignore_categories)) return;
                scope.setCategoryInRect(figure.rect, figure.category);

                let t_rect = figure.rect.clone();
                let r_center = t_rect.rect_center;
                t_rect._w += 600;
                t_rect._h += 600;
                t_rect.rect_center = r_center;
                scope.setHallWeightInRect(t_rect, 0.5);
            });

            this._moving_lines.forEach(moving_line => moving_line.updateMovingLine());
        }
        // console.timeEnd("updateByFurnitureList");



    }
    public setCategoryInRect(rect: ZRect, category: string, type: IRoomEntityType = "Furniture") {
        let lw = rect._w / 2;
        let lh = rect._h / 2;
        for (let x = -lw; x <= lw; x += this._grid_len) {
            let tx = Math.min(lw, x);
            for (let y = -lh; y <= lh; y += this._grid_len) {
                let ty = Math.min(lh, y);
                let pos = rect.unproject({ x: tx, y: ty });

                let grid = this.getGrid(pos);
                if (!grid) continue;

                grid.addCategory(category, type);

            }
        }
    }

    public setHallWeightInRect(rect: ZRect, weight: number) {
        let lw = rect._w / 2;
        let lh = rect._h / 2;
        for (let x = -lw; x <= lw; x += this._grid_len) {
            let tx = Math.min(lw, x);
            for (let y = -lh; y <= lh; y += this._grid_len) {
                let ty = Math.min(lh, y);
                let pos = rect.unproject({ x: tx, y: ty });

                let grid = this.getGrid(pos);
                if (!grid) continue;

                grid._hallway_weight = Math.min(weight, grid._hallway_weight);

            }
        }
    }

    public async updateDrawingImage() {
        if (!this._main_rect) return;

        let bbox = this._main_rect.computeBBox();
        let t_rect = ZRect.fromBox3(bbox, { x: 0, y: -1, z: 0 });
        this._drawing_rect = t_rect;
        let ww = t_rect.w;
        let hh = t_rect.h;

        let t_sc = 0.1;
        let canvas = document.createElement("canvas");

        canvas.width = Math.round(ww * t_sc);
        canvas.height = Math.round(hh * t_sc);

        let painter = new TPainter(canvas);
        let ts = painter.exportTransformData();


        painter.bindCanvas(canvas);

        painter._p_sc = t_sc;
        painter.p_center = t_rect.rect_center;

        painter.enter_drawpoly();

        // for(let key in this._grid_dict)
        // {
        //     let grid = this._grid_dict[key];
        //     grid.drawCanvas(painter);
        // }

        this._moving_lines.forEach(line => line.drawCanvas(painter));
        painter.leave_drawpoly();

        painter.importTransformData(ts, false);

        if (!this._drawing_image) {
            this._drawing_image = new Image(canvas.width, canvas.height);

        }




        await loadImage(canvas.toDataURL(), this._drawing_image);


    }
    public drawCanvas(painter: TPainter) {

        if (this._drawing_image && this._drawing_rect) {
            painter.fillImageInRect(this._drawing_image, this._drawing_rect);
        }
    }
}


/**
 *  格子动线
 */
export class AreaGridMovingLine {
    _board: AreaGridBoard;

    _start_category: string;
    _end_category: string;

    _moving_line_edges: I_GridEdge[];
    _line_color: string;

    constructor(start_cate: string, end_cate: string, board: AreaGridBoard, line_color: string = null) {
        this._start_category = start_cate;
        this._end_category = end_cate;
        this._board = board;
        this._line_color = line_color;
    }

    updateMovingLine() {
        this._moving_line_edges = [];
        let start_grid = this._board._seed_grids.find((grid) => grid._seed_category === this._start_category);

        if (!start_grid) return false;


        for (let key in this._board._grid_dict) {
            let grid = this._board._grid_dict[key];
            grid.getNeigbhors();

            for (let edge of grid._neighbor_edges) {
                edge._is_visited && delete edge._is_visited;
                edge._prev_edge && delete edge._prev_edge;

                edge._path_dist = 9999999;
            }
        }
        let queue: I_GridEdge[] = [];

        queue.push(...start_grid._neighbor_edges);

        start_grid._neighbor_edges.forEach(grid_edge => {
            grid_edge._is_visited = 1;
            grid_edge._path_dist = grid_edge._edge.length;
            grid_edge._prev_edge = null;
        });

        let end_edges: I_GridEdge[] = [];
        for (let qi = 0; qi < queue.length; qi++) {
            let grid_edge = queue[qi];

            let t_grid = grid_edge.grid1;

            if (t_grid._area_category.includes(this._end_category)) {
                end_edges.push(grid_edge);
                // break;
                continue;
            }
            if (t_grid._area_category.length > 0) {
                continue;
            }
            let path_dist = grid_edge._path_dist;
            for (let t_grid_edge of t_grid._neighbor_edges) {

                let ww = grid_edge._edge.checkSameDirection(t_grid_edge._edge.dv, true) ? 1 : 3;

                let hallway_weight = 2.0 / (1. + t_grid_edge.grid1._hallway_weight);

                let len = t_grid_edge._edge.length;

                let t_path_dist = path_dist + len * ww * hallway_weight;

                if (t_path_dist < t_grid_edge._path_dist) {
                    t_grid_edge._path_dist = t_path_dist;
                    t_grid_edge._prev_edge = grid_edge;
                }

                if (!t_grid_edge._is_visited) {
                    t_grid_edge._is_visited = 1;
                    queue.push(t_grid_edge);
                }

            }
        }
        if (end_edges.length > 0) {
            end_edges.sort((a, b) => a._path_dist - b._path_dist);
            let t_grid_edge = end_edges[0];

            let ans_edges: I_GridEdge[] = [];

            let t_edge = t_grid_edge;

            while (t_edge) {
                if (ans_edges.includes(t_edge)) break;

                ans_edges.push(t_edge);

                t_edge = t_edge._prev_edge;
            }

            this._moving_line_edges = ans_edges;
        }
        else {
            return false;
        }










        return true;
    }

    drawCanvas(painter: TPainter) {
        if (this._moving_line_edges) {
            painter.strokeStyle = this._line_color || "#00f";
            painter._context.lineWidth = 3;
            painter.drawEdges(this._moving_line_edges.map(g_edge => g_edge._edge));
        }
    }

}