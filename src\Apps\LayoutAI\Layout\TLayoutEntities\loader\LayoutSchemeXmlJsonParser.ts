import { ZPolygon } from "@layoutai/z_polygon";
import { I_SwjBaseGroup, I_SwjCabinetData, I_SwjEntityBase, I_SwjFurnitureData, I_SwjFurnitureGroup, I_SwjWall, I_SwjWindow, I_SwjXmlScheme, SchemeSourceType } from "../../../AICadData/SwjLayoutData";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Events, LayoutAI_Version } from "@/Apps/LayoutAI_App";
import { Vector3 } from "three";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { TBaseEntity } from "../TBaseEntity";
import { TFurnitureEntity } from "../TFurnitureEntity";
import { TGroupTemplateEntity } from "../TGroupTemplateEntity";
import { TLayoutEntityContainer } from "../TLayoutEntityContainter";
import { TRoomEntity } from "../TRoomEntity";
import { TStructureEntity } from "../TStructureEntity";
import { TWall } from "../TWall";
import { TWindowDoorEntity } from "../TWinDoorEntity";

import { EventName } from "@/Apps/EventSystem";
import { ModelLocPublicCategoryMap } from "../../../AICadData/ModelLocPubliccategoryMap";
import { determineFigureLabel } from "../../../Drawing/FigureImagePaths";
import { compareNames } from "@layoutai/z_polygon";
import { DesignXmlMaker } from "../../../AICadData/DesignXmlMaker";
import { LayoutSchemeData } from "../../../Services/Basic/LayoutSchemeService";
import { MaterialService } from "../../../Services/MaterialMatching/MaterialService";
import { TBoard3D } from "@layoutai/z_polygon";
import { TGroupTemplate } from "../../TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TSeriesFurnisher } from "../../../Services/MaterialMatching/TSeriesFurnisher";
import { TRoom } from "../../TRoom";
import { TSeriesSample } from "../../TSeriesSample";
import { TBaseGroupEntity } from "../TBaseGroupEntity";
import { TExtDrawingParser } from "../TExtDrawingElements/TExtDrawingParser";
import { TSubSpaceAreaEntity } from "../TSubSpaceAreaEntity";
import { LayoutContainerUtils } from "../utils/LayoutContainerUtils";
import { AI_PolyTargetType } from "../../IRoomInterface";
import { refineWalls } from "@/Apps/LayoutAI/AICadData/SwjDataBasicFuncs";
import { TBaseSpaceEntity } from "../TSpaceEntities/TBaseSpaceEntity";
import { TEntityTransformer } from "../../TEntityTransformer/TEntityTransformer";
import { base64ToUtf8 } from "@/Apps/LayoutAI/Utils/file_utils";


const debug = false;

export class LayoutSchemeXmlJsonParser {
    static Container: TLayoutEntityContainer = null;
    static _adjustWindowMirror(doors: I_SwjWindow[], schemeSource: SchemeSourceType) {
        for (let door of doors) {
            // 户型纠正和户型库的方案, 需要修正门窗朝向
            if (schemeSource === SchemeSourceType.LayoutCorrection
                || schemeSource === SchemeSourceType.LayoutLibrary
            ) {
                door.mirror = door.mirror == 0 ? 1 : 0;
            }
        }
    }
    static async fetchPublicCategoryForAll3DMaterials(xmlScheme: I_SwjXmlScheme) {
        if (!LayoutSchemeXmlJsonParser.Container) {
            LayoutSchemeXmlJsonParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutSchemeXmlJsonParser.Container;
        let allMaterialIds: string[] = [];
        if (xmlScheme.furniture_list) {
            for (let furniture of xmlScheme.furniture_list) {
                if (furniture.material_id) allMaterialIds.push(furniture.material_id);
            }
        }
        if (xmlScheme.furniture_group_list) {
            for (let furniture of xmlScheme.furniture_group_list) {
                if (furniture.material_id) allMaterialIds.push(furniture.material_id);
            }
        }
        if (xmlScheme.cabinet_list) {
            for (let cabinet of xmlScheme.cabinet_list) {
                if (cabinet.material_id) allMaterialIds.push(cabinet.material_id);
            }
        }

        if (allMaterialIds.length > 0) {
            let allMaterialInfos = await MaterialService.getMaterialInfo(allMaterialIds);
            // console.log(allMaterialInfos);
            container._material_id_public_category_map = new Map<string, string>();
            for (let materialInfo of allMaterialInfos) {
                if (materialInfo.publicCategoryName) {
                    container._material_id_public_category_map.set(materialInfo.id, materialInfo.publicCategoryName);
                }
            }
        }
    }
    static fromXmlSchemeData(xmlScheme: I_SwjXmlScheme, post_processing: boolean = true, schemeSource: SchemeSourceType = null) {
        if (!LayoutSchemeXmlJsonParser.Container) {
            LayoutSchemeXmlJsonParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutSchemeXmlJsonParser.Container;

        if (!xmlScheme) return;

        container._rooms = [];

        if (xmlScheme != null) {
            container._src_swj_layout_data = xmlScheme;
            container._current_swj_layout_data = container._src_swj_layout_data;
        }
        container._scheme_source = schemeSource;
        TBaseEntity.EntityUidNCounter = TBaseEntity.EntityUidDefaultNum;

        // console.log(xmlScheme);

        if (debug) console.log("从3D读取方案数据到插槽...", container._src_swj_layout_data);

        container._scheme_id = container._src_swj_layout_data.scheme_id;
        container._layout_scheme_id = xmlScheme?.layout_scheme_id || null;
        container.hxId = xmlScheme?.hxId || null;
        if (!container._scheme_id) {
            container.generateSchemeId();
        }
        container._scheme_name = xmlScheme.name || "";
        container._layout_scheme_name = xmlScheme?.name || "";
        container.init();

        let wall_rects: ZRect[] = [];
        let wall_rect_dict: { [key: string]: ZRect } = {};

        let addSwjWall = (swjWall: I_SwjWall) => {
            if (swjWall.uid && wall_rect_dict[swjWall.uid]) // 已存在, 不要重复添加
            {
                return false;
            }
            let wall_entity = new TWall(swjWall);
            wall_entity.bindEntity();
            // scope._wall_entities.push(wall_entity);
            wall_rects.push(wall_entity.rect);


            wall_rect_dict[swjWall.uid] = wall_entity.rect;

            return true;


        }
        let needs_adjust_walls = true;
        if (xmlScheme.LayoutAI_Version !== undefined) {
            needs_adjust_walls = false;
        }
        if (container._src_swj_layout_data?.wall_list) {
            for (let swjWall of container._src_swj_layout_data.wall_list) {
                addSwjWall(swjWall);
            }
        }
        if (container._src_swj_layout_data?.room_list) {
            for (let room of container._src_swj_layout_data.room_list) {
                if (!room.wall_list) continue;

                for (let swjWall of room.wall_list) {
                    (addSwjWall(swjWall));

                }
            }
        }
        let t_wall_rects: ZRect[] = refineWalls(wall_rects, 5);

        for (let rect of t_wall_rects) {
            TBaseEntity.set_polygon_type(rect, AI_PolyTargetType.Wall);
            let wall_entity = TWall.getOrMakeEntityOfCadRect(rect);
            container._wall_entities.push(wall_entity);
        }

        // 有房内可能门窗不全, 所以要对所有门窗进行排列
        let windows: I_SwjWindow[] = xmlScheme.window_list || [];
        let doors: I_SwjWindow[] = xmlScheme.door_list || [];
        let flues: I_SwjEntityBase[] = xmlScheme.flue_list || [];
        let pillars: I_SwjEntityBase[] = xmlScheme.pillar_list || [];
        let pipes: I_SwjEntityBase[] = xmlScheme.pipe_list || [];
        let beams: I_SwjEntityBase[] = xmlScheme.beam_list || [];
        let platforms: I_SwjEntityBase[] = xmlScheme.platform_list || [];
        let base_groups: I_SwjBaseGroup[] = xmlScheme.base_group_list || [];
        let furniture_list: I_SwjFurnitureData[] = [];
        let cabinet_list: I_SwjCabinetData[] = [];
        let furniture_element_list: TFigureElement[] = [];

        const append_figure_groups = (figure_group_list: I_SwjFurnitureGroup[]) => {
            if (!figure_group_list) return;
            figure_group_list.forEach((figure_group) => {
                if (figure_group.name && figure_group.name.startsWith("组合")) {

                    if (figure_group.furniture_list) {
                        let pos = new Vector3(figure_group.pos_x || 0, figure_group.pos_y || 0, figure_group.pos_z || 0);
                        let rotate_z = figure_group.rotate_z || 0;

                        for (let child_figure of figure_group.furniture_list) {
                            let child_pos = new Vector3(child_figure.pos_x || 0, child_figure.pos_y || 0, child_figure.pos_z || 0);

                            child_pos.applyAxisAngle({ x: 0, y: 0, z: 1 } as Vector3, rotate_z).add(pos);

                            child_figure.pos_x = child_pos.x;
                            child_figure.pos_y = child_pos.y;
                            child_figure.pos_z = child_pos.z;

                            if (child_figure.rotate_x) {
                                let t_size = new Vector3().copy({ x: child_figure.length, y: child_figure.width, z: child_figure.height });
                                t_size.applyAxisAngle({ x: 1, y: 0, z: 0 } as Vector3, child_figure.rotate_x);

                                child_figure.length = t_size.x;
                                child_figure.width = t_size.y;
                                child_figure.height = t_size.z;
                            }

                        }
                        furniture_list.push(...figure_group.furniture_list);
                        return;
                    }
                }
                furniture_list.push(figure_group);
            })
        }
        container._src_swj_layout_data.furniture_list && furniture_list.push(...container.current_swj_layout_data.furniture_list);
        // scope._src_swj_layout_data.furniture_group_list && furniture_list.push(...scope.current_swj_layout_data.furniture_group_list);
        container._src_swj_layout_data.cabinet_list && cabinet_list.push(...container.current_swj_layout_data.cabinet_list);
        append_figure_groups(container._src_swj_layout_data.furniture_group_list);

        if (container._src_swj_layout_data.room_list) {
            for (let room of container._src_swj_layout_data.room_list) {
                // scope._rooms.push(new TRoom().fromSwjRoom(room));

                let room_entity = new TRoomEntity(new ZPolygon(), "未命名");
                room_entity.fromSwjData(room);

                if (room_entity._room_poly.orientation_z_nor.z > 0) {
                    room_entity._room_poly.invertOrder();
                    room_entity._room_poly.computeZNor();
                }

                container._room_entities.push(room_entity);
                room.window_list && windows.push(...room.window_list);
                room.door_list && doors.push(...room.door_list);
                room.flue_list && flues.push(...room.flue_list);
                room.pillar_list && pillars.push(...room.pillar_list);
                room.pipe_list && pipes.push(...room.pipe_list);
                room.beam_list && beams.push(...room.beam_list);
                room.platform_list && platforms.push(...room.platform_list);

                room.furniture_list && furniture_list.push(...room.furniture_list);
                // room.furniture_group_list && furniture_list.push(...room.furniture_group_list)
                append_figure_groups(room.furniture_group_list);

                room.cabinet_list && cabinet_list.push(...room.cabinet_list);
            }

        }

        // console.log(windows,doors,flues,pillars,beams,platforms);
        let win_rects: ZRect[] = [];
        let structure_rects: ZRect[] = [];
        // let windowUidSet:Set<string> = new Set<string>();
        for (let win of windows) {
            // if(!win.type) continue;

            let win_entity = new TWindowDoorEntity(win, null);
            win_entity.type = "Window";

            if (!win.type) {
                win_entity.predictRealType();
            }
            win_entity.bindEntity();
            let has_same_window = false;
            for (let rect of win_rects) {
                if (rect.is_shape_equal_to(win_entity.rect)) {
                    has_same_window = true;
                }
            }
            if (!has_same_window) {
                // if(windowUidSet.has(win.uid.toString()))
                // {
                //     continue;
                // }
                // windowUidSet.add(win.uid.toString());
                container._window_entities.push(win_entity);
                win_rects.push(win_entity.rect);
            }

        }
        // 修正门的朝向
        LayoutSchemeXmlJsonParser._adjustWindowMirror(doors, schemeSource);
        // let doorUidSet:Set<string> = new Set<string>();
        for (let door of doors) {
            if(door) 
            {
                door.type = "Door";
            }
            let win_entity = new TWindowDoorEntity(door, null);
            win_entity.type = "Door";

            win_entity.bindEntity();
            if (!door.type) {
                win_entity.predictRealType();
            }
            let has_same_window = false;
            for (let rect of win_rects) {
                if (rect.is_shape_equal_to(win_entity.rect)) {
                    has_same_window = true;
                }
            }
            if (!has_same_window) {
                // if(doorUidSet.has(door.uid.toString()))
                // {
                //     continue;
                // }
                // windowUidSet.add(door.uid.toString());
                container._window_entities.push(win_entity);
                win_rects.push(win_entity.rect);
            }
        }

        for (let ele of flues) {
            let entity = new TStructureEntity(ele, null);
            entity.type = "StructureEntity";
            entity.realType = "Flue";
            entity.bindEntity();
            container._structure_entities.push(entity);
            structure_rects.push(entity.rect);
        }


        for (let ele of pillars) {
            let entity = new TStructureEntity(ele, null);
            entity.type = "StructureEntity";
            entity.realType = "Pillar";
            entity.bindEntity();
            container._structure_entities.push(entity);
            structure_rects.push(entity.rect);


        }

        for (let ele of pipes) {
            let entity = new TStructureEntity(ele, null);
            entity.type = "StructureEntity";
            entity.realType = "Envelope_Pipe";
            entity.bindEntity();
            container._structure_entities.push(entity);
            structure_rects.push(entity.rect);


        }

        for (let ele of beams) {
            let entity = new TStructureEntity(ele, null);
            entity.type = "StructureEntity";
            entity.realType = "Beam";
            entity.bindEntity();
            container._structure_entities.push(entity);
            structure_rects.push(entity.rect);


        }
        for (let ele of platforms) {
            let entity = new TStructureEntity(ele, null);
            entity.type = "StructureEntity";
            entity.realType = "Platform";
            entity.bindEntity();
            container._structure_entities.push(entity);
            structure_rects.push(entity.rect);


        }

        let schemeMaterialListLogs: string[] = [];

        // 以下是加载到 全局显示的素材

        let visited_uid: { [key: string]: boolean } = {};

        let swj_group_template_list: I_SwjFurnitureGroup[] = [];
        for (let furniture of furniture_list) {
            
            if (furniture.uid !== undefined) {
                if (visited_uid[furniture.uid]) {
                    continue;
                }
                visited_uid[furniture.uid] = true;
            }

            if (furniture.type === "BaseGroup") {
                base_groups.push(furniture as I_SwjBaseGroup);
                continue;
            }
            if ((furniture as I_SwjFurnitureGroup).group_template_code) {
                swj_group_template_list.push(furniture as I_SwjFurnitureGroup);
                continue;
            }


            schemeMaterialListLogs.push("furniture: " + JSON.stringify(furniture) + "\n");
            let fe = TFigureElement.fromSwjFurniture(furniture);


            if (container._material_id_public_category_map.has(furniture.material_id) && fe.sub_category == "Default") {

                let publicCategory = container._material_id_public_category_map.get(furniture.material_id);

                let modelLoc = furniture.modelLoc || ModelLocPublicCategoryMap.getModelLocByPublicCategory(publicCategory, "default", furniture.name);
                let figureLabel = determineFigureLabel(publicCategory, modelLoc, fe.sub_category);
                fe.public_category = publicCategory;
                fe.category = modelLoc;
                if (figureLabel) fe.sub_category = figureLabel;
            }

            if (compareNames([fe.category], ["地面"])) {
                continue;
            }
            if(fe._is_sub_board)
            {
                continue;
            }
            
            // if (fe.sub_category != "Default") {


            if (fe.rect.min_hh > 10) {
                furniture_element_list.push(fe);
            }
            // }
        }



        // console.log(scope._src_swj_layout_data.cabinet_list);
        for (let cabinet of cabinet_list) {
            if (cabinet.uid !== undefined) {
                if (visited_uid[cabinet.uid]) {
                    continue;
                }
                visited_uid[cabinet.uid] = true;
            }
            schemeMaterialListLogs.push("cabinet: " + JSON.stringify(cabinet));
            // console.log(cabinet.width,cabinet.length,cabinet._children);
            if (cabinet._children && cabinet._children.length > 0 && Math.min(cabinet.width, cabinet.length) > 1000) {
                let t_params = {
                    PX: cabinet.pos_x,
                    PY: cabinet.pos_y,
                    PZ: cabinet.pos_z,
                    RX: cabinet.rotate_x,
                    RY: cabinet.rotate_y,
                    RZ: cabinet.rotate_z
                };

                if (cabinet.rotate_x === undefined) {
                    t_params.RX = 0;
                    t_params.RY = 0;
                    t_params.RZ = cabinet.rotate_z / Math.PI * 180;
                }
                let matrix = TBoard3D.BasicParamsToMatrix4(t_params);

                let cabinets = cabinet._children.filter((ele) => compareNames([ele.name], ["板", "柜", "桌", "榻榻米"]) && !compareNames([ele.name], ["空间"]) && (Math.min(ele.width || 0, ele.length || 0, ele.height || 0) > 0.1)).map((ele) => TFigureElement.fromSwjCabinet(ele, "Default", matrix));

                furniture_element_list.push(...cabinets);
            }
            else {
                let fe = TFigureElement.fromSwjCabinet(cabinet);
                furniture_element_list.push(fe);
            }



        }


        container._furniture_entities.push(...TLayoutEntityContainer.getFurnitureEntitiesFromFigureElements(furniture_element_list, {needs_make_group_templates:false,make_base_group:true}));

        // console.log([...scope._furniture_entities.map(figure=>figure._figure_element)]);
        for (let swj_group_template of swj_group_template_list) {
            let group_code = swj_group_template.group_template_code;
            if (!group_code) continue;

            let rect = new ZRect(swj_group_template.length, swj_group_template.width);

            let group_template = TGroupTemplate.getGroupTemplateByGroupCode(group_code, rect);

            if (group_template && group_template.current_s_group) {
                let entity = new TGroupTemplateEntity(group_template);
                entity.importData(swj_group_template);
                entity.bindFigureEntities();
                entity.update();

                container._furniture_entities.push(entity);

            }
        }

        for (let swj_base_group of base_groups) {
            let entity = TBaseGroupEntity.importData(swj_base_group);
            container._furniture_entities.push(entity);
        }

        container._wall_entities.sort((a, b) => a.uidN - b.uidN);
        // 保证uidN从小到大排序
        container._room_entities.sort((a, b) => a.uidN - b.uidN);
        // scope._ai_cad_data.updateLayoutData();

        container._furniture_entities.sort((a, b) => {
            return a._drawing_order - b._drawing_order;
        });

        container._ext_drawing_entities = [];

        if (xmlScheme && xmlScheme.ext_drawing_list) {
            for (let data of xmlScheme.ext_drawing_list) {
                let entity = TExtDrawingParser.Parse(data);
                entity && (container._ext_drawing_entities.push(entity));
            }
        }
        // console.log(scope._room_entities);
        container._sub_area_entities = [];
        if (xmlScheme && xmlScheme.sub_area_list) {
            for (let area_data of xmlScheme.sub_area_list) {
                // console.log(area_data);
                let entity = new TSubSpaceAreaEntity();
                entity.importData(area_data);
                entity && (container._sub_area_entities.push(entity));
            }
        }


        container.updateEntityRelations();

        if (needs_adjust_walls) {
            // console.log("needs adjust walls");
            container.adjustWallsByNeighbors();
            container._computeRoomPolysByWall(false, false);
            container.updateEntityRelations();

        }
        else if (xmlScheme.LayoutAI_Version !== undefined) {
            container.adjustWallsByNeighbors();
            container.updateEntityRelations();

        }

        // scope.toAiCadData(); // 生成Ai_Cad_data 数据, 主要是对齐hasCadData
        // console.log("area_names",xmlScheme.area_names);

        if (xmlScheme.area_names) {
            container._computeRoomPolysByWall(true);
            for (let area_name_data of xmlScheme.area_names) {
                let pos = { x: area_name_data.pos_x || 0, y: area_name_data.pos_y || 0, z: area_name_data.pos_z || 0 };

                let rooms = container._room_entities.filter((entity) => {
                    return entity.name == "未命名" && entity._room_poly.containsPoint(pos);
                });
                if (rooms[0]) {
                    let room_entity = rooms[0];
                    room_entity.name = area_name_data.name;
                    
                    // room_entity.name = room_entity.roomname;
                }
            }

        }

        container.updateEntityRelations();

        for (let room_entity of container._room_entities) {
            room_entity.predictRoomName();
        }
        container.updateRoomsFromEntities(post_processing, true);
        
        
        container._base_space_entities = [];
        if(xmlScheme.base_space_list)
        {
            xmlScheme.base_space_list.forEach((data)=>{
                let entity = new TBaseSpaceEntity(new ZRect(),"","");
                entity.importData(data);
                container._base_space_entities.push(entity);
            })
        }
        let matchedLayout: any[] = (xmlScheme as any)["matchedLayout"];

        TSeriesFurnisher.instance.room2SeriesSampleMap.clear();
        let room2SeriesMap: any = (xmlScheme as any)["room2Series"];
        if (room2SeriesMap) {
            // 使用Object.keys遍历普通对象
            for (let roomUid of Object.keys(room2SeriesMap)) {
                let seriesSample: TSeriesSample = TSeriesSample.fromJsonData(room2SeriesMap[roomUid]);
                let room: TRoom = container._rooms.find((room) => room.uid === roomUid);
                if (room && seriesSample) {
                    TSeriesFurnisher.instance.room2SeriesSampleMap.set(room, seriesSample);
                }
            }

            LayoutAI_App.emit(EventName.Room2SeriesSampleRoom, TSeriesFurnisher.instance.room2SeriesSampleMap);
        }

        container.findAndBindRoomForFurnitures(container._furniture_entities);
        


        if (!container._outter_border_polygons || container._outter_border_polygons.length == 0) {
            container._computeOutterBorderPolys();
        }
        container.updateWholeBox();
        LayoutContainerUtils.updateAliasName();
        if (container._manager && LayoutAI_App.IsDebug && LayoutAI_App.instance.Configs.saving_localstorage_layout_scheme) {
            container._manager._save_to_local_XmlSchemeData();
        }
        //  发出方案读取完的信号
        LayoutAI_App.emit_M(EventName.SwjSchemeLoaded, true);
        container._initial_scheme_data = xmlScheme;
        container._is_from_3d_scheme = false;
    }

    static toXmlSchemeData() {
        if (!LayoutSchemeXmlJsonParser.Container) {
            LayoutSchemeXmlJsonParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutSchemeXmlJsonParser.Container;
        let scope = container;
        let data: I_SwjXmlScheme = {};

        data.LayoutAI_Version = LayoutAI_Version;
        data.wall_list = [];
        data.hxId = container.hxId;
        for (let wall_entity of scope._wall_entities) {
            data.wall_list.push(wall_entity.exportData());
        }


        data.room_list = [];
        if (scope._rooms && scope._rooms.length > 0) {
            for (let room of scope._rooms) {
                let room_data = room.exportSwjRoomData();
                room_data.pillar_list && delete room_data.pillar_list;
                room_data.pipe_list && delete room_data.pipe_list;
                room_data.platform_list && delete room_data.platform_list;
                room_data.flue_list && delete room_data.flue_list;
                data.room_list.push(room_data);
            }
        }

        data.furniture_list = [];
        data.cabinet_list = [];
        data.furniture_group_list = [];
        data.base_group_list = [];
    
        data.flue_list = [];
        data.pillar_list = [];
        data.platform_list = [];
        data.pipe_list = [];
        data.beam_list = [];
        scope._structure_entities.forEach((entity)=>{
            if(entity.realType === "Pillar")
            {
                data.pillar_list.push(entity.exportData() as any);
            }
            else if(entity.realType === "Envelope_Pipe" || entity.realType==="WastePipe" || entity.realType==="SewagePipe")
            {
                data.pipe_list.push(entity.exportData() as any);
            }
            else if(entity.realType === "Beam")
            {
                data.beam_list.push(entity.exportData() as any);
            }
            else if(entity.realType === "Flue")
            {
                data.flue_list.push(entity.exportData() as any);
            }
        });
        for (let entity of scope._furniture_entities) {
            if (entity.is_single_furniture) {
                let t_data = entity.exportData() as I_SwjFurnitureData;
                t_data._figure_element = entity.figure_element.exportJson();
                data.furniture_list.push(t_data);
            }
            else {
                if (entity.type === "BaseGroup") {
                    let t_data = entity.exportData() as I_SwjBaseGroup;
                    data.base_group_list.push(t_data);
                } else {
                    let t_data = entity.exportData() as I_SwjFurnitureGroup;
                    data.furniture_group_list.push(t_data);
                }
            }

        }

        data.ext_drawing_list = [];
        for (let entity of scope._ext_drawing_entities) {
            data.ext_drawing_list.push(entity.exportData());
        }

        data.scheme_id = scope._scheme_id;
        data.layout_scheme_id = scope._layout_scheme_id;
        // console.log(data.layout_scheme_id,scope._layout_scheme_id);
        data.name = scope._layout_scheme_name;
        data.sub_area_list = [];
        for (let entity of scope._sub_area_entities) {
            data.sub_area_list.push(entity.exportData());
        }
        
        if(scope._base_space_entities && scope._base_space_entities.length > 0)
        {
            data.base_space_list = scope._base_space_entities.map((entity)=>entity.exportData());
        }
        return data;
    }


    static generateHouseSchemeXml(container: TLayoutEntityContainer = null): any {
        if (!LayoutSchemeXmlJsonParser.Container) {
            LayoutSchemeXmlJsonParser.Container = container || (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        container = container || LayoutSchemeXmlJsonParser.Container;
        let design_xml_maker = new DesignXmlMaker();
        let root = design_xml_maker.makeByEntities(container);
        let xml_text = new XMLSerializer().serializeToString(root);
        return xml_text;
    }

    static async openLayoutSchemeData(data: any) {

        if (!data) {
            console.error("Fail to open layout scheme: LayoutSchemeData object is null");
            return;
        }
        let layoutSchemeUrl: string = data.contentUrl;
        if (layoutSchemeUrl) {

            let base64str = await fetch(layoutSchemeUrl).then((res) => res.text()).catch(e => null);
            if (!base64str) {
                return;
            }
            const layoutSchemeJsonStr = base64ToUtf8(base64str);
            const schemeJson = JSON.parse(layoutSchemeJsonStr.replace(/'/g, '"'));
            LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(schemeJson, {
                updateUid: false,
                layout_scheme_id: data.id,
                layout_scheme_name: data.layoutSchemeName,
                init_wire_frame_image: true,
                auto_layout: data.auto_layout,
            });
        }
    }
    static loadSwjSchemeXmlJson(data: I_SwjXmlScheme, options: {
        layout_scheme_id?: string,
        layout_scheme_name?: string, 
        clean_dxf_data?: boolean, 
        updateUid?: boolean,
        schemeSource?: SchemeSourceType, 
        auto_layout?: boolean,
        init_wire_frame_image?: boolean, [key: string]: any,
    } = {}) {
        if (!data) return;
        if (!LayoutSchemeXmlJsonParser.Container) {
            LayoutSchemeXmlJsonParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutSchemeXmlJsonParser.Container;
        container._layout_scheme_id = options.layout_scheme_id || data?.layout_scheme_id || null;
        container._layout_scheme_name = options.layout_scheme_name || data?.name || null;
        container._customer_info = null;
        container._houseTypeParam = null;
        container.manager.layout_container._funcRequire = null;
        data.name = options.layout_scheme_name || data.name;
        data.layout_scheme_id = container._layout_scheme_id;
        container.fromXmlSchemeData(data, true, options.schemeSource || null);

        if (options.clean_dxf_data === undefined) {
            options.clean_dxf_data = true;
        }
        if(options.auto_layout === undefined)
        {
            options.auto_layout = true;
        }
        // console.log('俯视图JsonUrl',data.wireFrameImageJsonUrl);
        
        if (options.init_wire_frame_image && data.wireFrameImageJsonUrl) {
            fetch(data.wireFrameImageJsonUrl)
                .then(res => res.json())
                .then(json => {
                    for (const furniture of container._furniture_entities) {
                        const nodeId = furniture._figure_element._matched_material.nodeId;
                        if (nodeId !== undefined && json[nodeId]) {
                            furniture._figure_element._matched_material.wireFrameImageUrl = json[nodeId];
                            furniture._figure_element.loadWireFrameImage();
                        }
                        if(furniture instanceof TBaseGroupEntity)
                        {
                            for(let sub_entity of furniture._combination_entitys)
                            {
                                if(sub_entity instanceof TFurnitureEntity && json[sub_entity.figure_element._matched_material.nodeId])
                                {
                                    sub_entity._figure_element._matched_material.wireFrameImageUrl = json[sub_entity.figure_element._matched_material.nodeId];
                                    sub_entity._figure_element.loadWireFrameImage();
                                }
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Failed to load wire frame images:', error);
                });
        }
        if (options.updateUid) {
            // this.manager.layout_container._updateEntityUids(true);
            LayoutContainerUtils.updateEntityUids(true);
        }
        LayoutContainerUtils.updateAliasName();

        LayoutAI_App.DispatchEvent(LayoutAI_Events.OnloadedXmlLayoutScheme, options);

    }
}