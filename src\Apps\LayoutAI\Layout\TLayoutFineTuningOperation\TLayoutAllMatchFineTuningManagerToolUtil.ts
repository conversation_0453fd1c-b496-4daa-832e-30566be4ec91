import { Vector3 } from "three";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TBaseRoomToolUtil } from "../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TRoom } from "../TRoom";
import { TLayoutGroupFineTuningData } from "./TLayoutGroupFineTuningData";
import { TLayoutFineTuningBaseTool, TLayoutFineTuningOperationToolUtil } from "./TLayoutFineTuningOperationToolUtil";
import { ZRect } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";


export class TLayoutAllMatchFineTuningManagerToolUtil
{
    private static _instance: TLayoutAllMatchFineTuningManagerToolUtil;

    private static readonly nearTol: number = 200;

    private static readonly moveStep: number = 50;

    private constructor()
    {
    }

    public static get instance(): TLayoutAllMatchFineTuningManagerToolUtil
    {
        if(!this._instance)
        {
            this._instance = new TLayoutAllMatchFineTuningManagerToolUtil();
        }
        return this._instance;
    }

    /**
     * 只是做一些简单的干涉处理，特殊情况暂时不进行处理
     * @param room
     * @param allFigures 套系匹配后的图元
     */
    public allMatchFigureFineTuning(room: TRoom, allFigures: TFigureElement[]): void
    {
        let groupFigureData: TLayoutGroupFineTuningData = new TLayoutGroupFineTuningData(allFigures, false);

        //预处理，对于靠墙或者靠背景墙的图元需要将其进行吸附（如果背景墙与墙都在附近则优先吸附到背景墙）
        preProcessGrouFigureData(room, groupFigureData, TLayoutAllMatchFineTuningManagerToolUtil.nearTol);

        let mainFigures: TFigureElement[] = groupFigureData.getSingleMainFigures();
        while(true)
        {
            let oldScore = this.calRoomLayoutScore(room, mainFigures).score;
            this.fineTuningMatchFigures(room, groupFigureData, TLayoutAllMatchFineTuningManagerToolUtil.moveStep);
            let newScore = this.calRoomLayoutScore(room, mainFigures).score;
            if(newScore - oldScore < 0.1)
            {
                break;
            }
        }
        let abnormalFigures: TFigureElement[] = this.calRoomLayoutScore(room, mainFigures).fineTuningFigures;

        let canHideFigures: TFigureElement[] = abnormalFigures.filter(figure => {
            let isCanDelete: boolean = false;
            for(let canDeleteNoMainCategory of TLayoutGroupFineTuningData.canDeleteNoMainCategory)
            {
                 if(figure.sub_category.includes(canDeleteNoMainCategory))
                {
                    isCanDelete = true;
                    break;
                }
            }
            return isCanDelete;
        });
        canHideFigures.forEach(figure => figure.markMaterialAsInvisible());
    }

    private fineTuningMatchFigures(room: TRoom, groupFigureData: TLayoutGroupFineTuningData, moveStep: number): void
    {
        let fineTuningMoveInfos: Map<TFigureElement, any> = this.initFineTuningFigureMoveInfo(room, groupFigureData, moveStep);
        let oldScore: number = this.calRoomLayoutScore(room, groupFigureData.getSingleMainFigures()).score;
        // 所有的图元都是上下左右移动
        while(true)
        {
            let fineTuningFigureCount: number = 0;
            for(let entry of fineTuningMoveInfos.entries())
            {
                let fineTuningMoveInfo: any = entry[1];
                if(Math.abs(fineTuningMoveInfo.moveStep) < 0.5)
                {
                    ++fineTuningFigureCount;
                    continue;
                }
                let fineTuningfigure: TFigureElement = entry[0];
                let moveVec: Vector3 = fineTuningMoveInfo.moveDir.clone().multiplyScalar(fineTuningMoveInfo.moveStep);
                groupFigureData.moveSingleFigure(fineTuningfigure, moveVec);
                let moveScore: number = this.calRoomLayoutScore(room, groupFigureData.getSingleMainFigures()).score;
                let diffScore: number = moveScore - oldScore;
                oldScore = moveScore;
                if(diffScore < 0.1)
                {
                    fineTuningMoveInfo.moveStep = -fineTuningMoveInfo.moveStep / 2;
                }
            }
            if(fineTuningFigureCount == fineTuningMoveInfos.size)
            {
                break;
            }
        }
    }

    private initFineTuningFigureMoveInfo(room: TRoom, groupFigureData: TLayoutGroupFineTuningData, moveStep: number): Map<TFigureElement, any>
    {
        let figures: TFigureElement[] = groupFigureData.getSingleMainFigures();
        let sourceScoreInfo: any = this.calRoomLayoutScore(room, figures);
        let sourceScore: number = sourceScoreInfo.score;
        let fineTuningFigures: TFigureElement[] = sourceScoreInfo.fineTuningFigures;
        let fineTuningMoveInfos: Map<TFigureElement, any> = new Map<TFigureElement, any>();
        let backGroundFigures: TFigureElement[] = figures.filter(figure => figure.category.includes("背景墙"));
        for(let figure of fineTuningFigures)
        {
            let moveDir: Vector3 = getMoveDirOnWall(room, backGroundFigures, figure, TLayoutAllMatchFineTuningManagerToolUtil.nearTol);
            let upDir: Vector3 = figure.rect.nor.clone();
            let downDir: Vector3 = figure.rect.nor.clone().multiplyScalar(-1);
            let leftDir: Vector3 = figure.rect.dv.clone();
            let rightDir: Vector3 = figure.rect.dv.clone().multiplyScalar(-1);
            let otherFigures: TFigureElement[] = TLayoutFineTuningBaseTool.instance.getOtherFigures(figures, [figure]);
            let upScore: number = sourceScore; let downScore: number = sourceScore; let leftScore: number = sourceScore; let rightScore: number = sourceScore;
            if(!moveDir || Math.abs(moveDir.clone().dot(upDir)) > 0.9)
            {
                upScore = this.calScoreByMoveFigure(room, otherFigures, figure.clone(), upDir.clone().multiplyScalar(moveStep));
                downScore = this.calScoreByMoveFigure(room, otherFigures, figure.clone(), downDir.clone().multiplyScalar(moveStep));
            }
            if(!moveDir || Math.abs(moveDir.clone().dot(leftDir)) > 0.9)
            {
                leftScore = this.calScoreByMoveFigure(room, otherFigures, figure.clone(), leftDir.clone().multiplyScalar(moveStep));
                rightScore = this.calScoreByMoveFigure(room, otherFigures, figure.clone(), rightDir.clone().multiplyScalar(moveStep));
            }
            let moveInfos: Map<Vector3, number> = new Map<Vector3, number>();

            if(upScore > sourceScore)
            {
                moveInfos.set(upDir, upScore);
            }
            if(downScore > sourceScore)
            {
                moveInfos.set(downDir, downScore);
            }
            if(leftScore > sourceScore)
            {
                moveInfos.set(leftDir, leftScore);
            }
            if(rightScore > sourceScore)
            {
                moveInfos.set(rightDir, rightScore);
            }

            let maxScore: number = Number.NEGATIVE_INFINITY;
            for(let entry of moveInfos.entries())
            {
                if(entry[1] > maxScore)
                {
                    maxScore = entry[1];
                }
            }

            let moveDirs: Vector3[] = [];
            for (let entry of moveInfos.entries()) {
                if (entry[1] == maxScore) {
                    moveDirs.push(entry[0]);
                }
            }

            let targetMoveDir: Vector3 = null;
            if(moveDirs.length == 1)
            {
                targetMoveDir = moveDirs[0];
            }
            else if(moveDirs.length > 1)
            {
                let minMoveLen: number = Number.POSITIVE_INFINITY;
                for (let moveDir of moveDirs) {
                    let tempMoveLen: number = 0;
                    let tempMoveStep: number = moveStep;
                    let oldScore: number = sourceScore;
                    while (true) {
                        // TODO 将图元一直往那个方向进行移动，到合适的位置需要记录的长度
                        if (Math.abs(tempMoveStep) < 0.5) {
                            break;
                        }
                        tempMoveLen = tempMoveLen + tempMoveStep
                        let tempMoveVec: Vector3 = moveDir.clone().multiplyScalar(tempMoveLen);
                        let tempScore: number = this.calScoreByMoveFigure(room, otherFigures, figure.clone(), tempMoveVec);
                        let tempDiffScore: number = tempScore - oldScore;
                        oldScore = tempScore;
                        if (tempDiffScore < 0.1) {
                            tempMoveStep = -tempMoveStep / 2;
                        }
                    }
                    if (minMoveLen > tempMoveLen) {
                        minMoveLen = tempMoveLen;
                        targetMoveDir = moveDir;
                    }
                }
            }
            if(targetMoveDir)
            {
                let fineTuningMoveInfo: any = {
                    moveDir: targetMoveDir,
                    moveStep: moveStep,
                    score: sourceScore
                };
                fineTuningMoveInfos.set(figure, fineTuningMoveInfo);
            }
        }
        return fineTuningMoveInfos;
    }

    private calScoreByMoveFigure(room: TRoom, otherFigures: TFigureElement[], figure: TFigureElement, moveVec: Vector3): number
    {
        TLayoutFineTuningOperationToolUtil.instance.moveRect(figure.matched_rect || figure.rect, moveVec);
        let score: number = this.calRoomLayoutScore(room, [...otherFigures, figure]).score;
        return score;
    }

    // 评分器（比较简单的评分规则, 判断是采用3D）
    private calRoomLayoutScore(room: TRoom, figures: TFigureElement[]): any
    {
        let basicScoreInfo: any = this.calBasicOverlayScoreInfo(room, figures);

        // TODO 特殊评分，针对产品关心的某些指标进行配置化计算
        let specialScoreInfo: any = this.calSpecialScoreInfo(room, figures);

        let totalScore: number = basicScoreInfo.score;
        let totalAbnormalFigures: TFigureElement[] = basicScoreInfo.fineTuningFigures;
        if(specialScoreInfo)
        {
            totalScore += specialScoreInfo.score;
            totalAbnormalFigures.push(...specialScoreInfo.fineTuningFigures);
        }
        totalAbnormalFigures = Array.from(new Set(totalAbnormalFigures));
        return {score: totalScore, fineTuningFigures: totalAbnormalFigures};
    }

    private calBasicOverlayScoreInfo(room: TRoom, figures: TFigureElement[]): any
    {
        // 1. 图元干涉计算
        let abnormalFigures: TFigureElement[] = [];
        let figureOverlayRatio: number = 0;
        for(let figure of figures)
        {
            for(let otherFigure of figures)
            {
                if(figure == otherFigure)
                {
                    continue;
                }
                if (!(TBaseRoomToolUtil.instance.isOverlayByRects((figure.matched_rect || figure.rect), (otherFigure.matched_rect || otherFigure.rect), true)  &&
                     (figure.matched_rect || figure.rect).zval < ((otherFigure.matched_rect || otherFigure.rect).zval + (otherFigure.height)) && 
                     ((figure.matched_rect || figure.rect).zval + (figure.height)) > (otherFigure.matched_rect || otherFigure.rect).zval))
                {
                    continue;
                }
                figureOverlayRatio += TBaseRoomToolUtil.instance.calOverlayRatioByFigures(figure, otherFigure, 0, false, true);
                abnormalFigures.push(figure);
                abnormalFigures.push(otherFigure);
            }
        }
        // 图元与墙干涉计算
        let overlayWallTol: number = 5;
        let roomOverlayRatio: number = TBaseRoomToolUtil.instance.calOverRoomRation(room, figures, overlayWallTol,  0, abnormalFigures, true);

        // 图元与门干涉计算, 这个待计算
        let overlayDoorRatio: number = TBaseRoomToolUtil.instance.calMatchFigureOverlayDoorRation(room, figures);

        let figureOverlayWeight: number = -10;
        let roomOverlayWeight: number = -100;
        let figureOverlayDoorWeight: number = -100;
        let score: number = figureOverlayRatio * figureOverlayWeight + roomOverlayRatio * roomOverlayWeight + overlayDoorRatio * figureOverlayDoorWeight;
        return {score: score, fineTuningFigures: abnormalFigures};
    }

    private calSpecialScoreInfo(room: TRoom, allFigures: TFigureElement[]): any
    {
        // TODO 预留接口方便后续进行实现
        return null;
    }
}

function getMoveDirOnWall(room: TRoom, backGroundFigures: TFigureElement[], figure: TFigureElement, nearTol: number, layonWallTol: number = 0.01): Vector3
{
    let matchedRect: ZRect = figure.matched_rect || figure.rect;
    // 当图元不背靠墙的时候，但是存在侧边靠墙的情况，则沿着侧边进行移动
    let getLayonEdge = (matchRectEdge: ZEdge, room: TRoom, backGroundFigures: TFigureElement[]): ZEdge => 
    {
        let edgeOnWall: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(matchRectEdge, room.room_shape._poly, nearTol, layonWallTol);
        if(!edgeOnWall)
        {
            for(let backGroundFigure of backGroundFigures)
            {
                edgeOnWall = TBaseRoomToolUtil.instance.edgeOnPolygon(matchRectEdge, (backGroundFigure.matched_rect || backGroundFigure.rect), nearTol, layonWallTol);
                if(edgeOnWall)
                {
                    break;
                }
            }
        }
        return edgeOnWall;
    };
    let edgeOnWall: ZEdge = getLayonEdge(matchedRect.backEdge, room, backGroundFigures);
    if(edgeOnWall)
    {
        return edgeOnWall?.dv.clone();
    }
    // 检查侧边是否存在贴墙
    edgeOnWall = getLayonEdge(matchedRect.leftEdge, room, backGroundFigures);
    if(!edgeOnWall)
    {
        edgeOnWall = getLayonEdge(matchedRect.rightEdge, room, backGroundFigures);
    }
    if(edgeOnWall)
    {
        return edgeOnWall?.dv.clone();
    }
    return null;
}

function preProcessGrouFigureData(room: TRoom, groupFigureData: TLayoutGroupFineTuningData, nearTol: number)
{
    let figures: TFigureElement[] = groupFigureData.getSingleMainFigures();
    let backGroundFigures: TFigureElement[] = figures.filter(figure => figure.category.includes("背景墙"));
    figures = figures.filter(figure => !figure.category.includes("背景墙"));
    for(let figure of figures)
    {
        // TODO 这个是餐桌组合应用套系是，餐椅与餐桌匹配的都是单品，因此需要进行特殊处理
        if(figure.sub_category == "餐椅")
        {
            continue;
        }
        let edgeOnWall: ZEdge = null;
        for(let backGroundFigure of backGroundFigures)
        {
            edgeOnWall = TBaseRoomToolUtil.instance.edgeOnPolygon((figure.matched_rect || figure.rect).backEdge, (backGroundFigure.matched_rect || backGroundFigure.rect), nearTol);
            if(edgeOnWall)
            {
                // 考虑到弧形墙，暂定深度大于150的背景墙边需要内缩
                if((backGroundFigure.matched_rect || backGroundFigure.rect).h > 180)
                {
                    let edgeOnWallNor: Vector3 = edgeOnWall.nor.clone().negate();
                    let startPoint: Vector3 = edgeOnWall.v0.pos.clone().add(edgeOnWallNor.clone().multiplyScalar(150));
                    let endPoint: Vector3 = edgeOnWall.v1.pos.clone().add(edgeOnWallNor.clone().multiplyScalar(150));
                    edgeOnWall = new ZEdge({pos: startPoint}, {pos: endPoint});
                }
                break;
            }
        }
        if(!edgeOnWall)
        {
            edgeOnWall = TBaseRoomToolUtil.instance.edgeOnPolygon((figure.matched_rect || figure.rect).backEdge, room.room_shape._poly, nearTol);
        }
        if(!edgeOnWall)
        {
            continue;
        }
        let moveVec: Vector3 = (figure.matched_rect || figure.rect).nor.clone().multiplyScalar((figure.matched_rect || figure.rect).nor.clone().dot(edgeOnWall.center.clone().sub((figure.matched_rect || figure.rect).backEdge.center)));
        groupFigureData.moveSingleFigure(figure, moveVec);
    }
}