import { generateUUID } from "three/src/math/MathUtils.js";
import { I_SwjXmlScheme } from "../../AICadData/SwjLayoutData";
import { I_LayoutAI_MsgData } from "../../Api/Model3dWorkerPool";

export interface I_HotelDivisonRule {
    roomSize: I_HotelRoomSizeRule[];
    wallThick: number;
  }
  
export interface I_HotelRoomSizeRule {
    type: string;
    minArea: number;
    maxArea: number;
    minW: number;
    maxW: number;
    minL: number;
    maxL: number;
    minW_L?: number;
    maxW_L?: number;
    rate?: number;
    character?: "窄开间长条形"|"宽开间短进深"|"均衡矩形";
  }
const DefaultVisionRule = {
	"roomSize": [{
		"type": "标单/标双动态尺寸链",
		"minArea": 29.5,
		"maxArea": 33.5,
		"minW": 3.8,
		"maxW": 5.0,
		"minL": 6.0,
		"maxL": 7.9,
		"minW_L": 0,
		"maxW_L": 0.5,
		"rate": 0.6,
		"character": "窄开间长条形"
	  },
	  {
		"type": "标单/标双动态尺寸链",
		"minArea": 32.5,
		"maxArea": 36.5,
		"minW": 4.5,
		"maxW": 6.0,
		"minL": 5.5,
		"maxL": 7.3,
		"minW_L": 0.667,
		"maxW_L": 0.833,
		"character": "均衡矩形"
	  },
	  {
		"type": "标单/标双动态尺寸链",
		"minArea": 35.5,
		"maxArea": 39.5,
		"minW": 5.8,
		"maxW": 7.8,
		"minL": 4.6,
		"maxL": 6.2,
		"minW_L": 1.667,
		"maxW_L": 3,
		"rate": 0.4,
		"character": "宽开间短进深"
	  },
	  {
		"type": "套房/亲子房动态尺寸链",
		"minArea": 49.5,
		"maxArea": 53.5,
		"minW": 5.8,
		"maxW": 7.8,
		"minL": 6.4,
		"maxL": 9.1
	  },
	  {
		"type": "套房/亲子房动态尺寸链",
		"minArea": 52.5,
		"maxArea": 56.5,
		"minW": 6.5,
		"maxW": 8.5,
		"minL": 6.2,
		"maxL": 8.6
	  },
	  {
		"type": "套房/亲子房动态尺寸链",
		"minArea": 55.5,
		"maxArea": 60.5,
		"minW": 7.8,
		"maxW": 9.8,
		"minL": 5.7,
		"maxL": 7.7
	  }
	],
	"wallThick": 120
  }
export class HotelRoomDivisionService
{
    private static _instance : HotelRoomDivisionService= null;

    private _worker : Worker = null;

    private _msgDict : {[key:string]:{msgId?:string, callback:(data:I_LayoutAI_MsgData)=>void}}
    private _readyCallback : ()=>void = null;
    constructor(){
        this._msgDict = {};
    }

    async initWorker()
    {
        this._worker = new Worker("./js/roomDivision/roomDivisionWorker.js");
        
        this._worker.onmessage = (ev)=>{
            let data = ev.data;
            if(data.msgType === "Ready")
            {
                if(this._readyCallback)
                {
                    this._readyCallback();
                }
            }
            if(data && data.msgType === "Result")
            {
               if(this._msgDict && this._msgDict[data.msgId||""])
               {
                    if(this._msgDict[data.msgId].callback)
                    {
                        this._msgDict[data.msgId].callback(data);
                    }
               }
            }
        }
        return await new Promise<void>((resolve,reject)=>{
            this._readyCallback = ()=>{
                console.log("isReady");
                resolve();
            }
        });

    }
    async divisionRoom( houseJson:string,ruleJson:string=null,): Promise<I_SwjXmlScheme>
    {
        if(!this._worker)
        {
            await this.initWorker();
        }
        if(!ruleJson)
        {
            ruleJson = JSON.stringify(DefaultVisionRule);
        }

        let res = await new Promise<string>((resolve,reject)=>{
            let msgData : I_LayoutAI_MsgData = {
                msgId : generateUUID(),
                method : "DivideHouse",
                ruleJson : ruleJson,
                houseJson : houseJson
            }
            this._msgDict[msgData.msgId] = {msgId:msgData.msgId, callback:(data:I_LayoutAI_MsgData)=>{
                if(!data.result)
                {
                    resolve("");
                    delete this._msgDict[msgData.msgId];
                    return;   
                }
                resolve(data.result);
                delete this._msgDict[msgData.msgId];
                if(Object.keys(this._msgDict).length == 0)
                {
                    this._worker.terminate();
                    this._worker = null;
                }

            }}

            this._worker.postMessage(msgData);
    
        });
        let obj = JSON.parse(res);
        return obj;
    }

    public static get instance()
    {
        if(!HotelRoomDivisionService._instance)
        {
            HotelRoomDivisionService._instance = new HotelRoomDivisionService();
        }
        return HotelRoomDivisionService._instance;
    }
}

export const hotelRoomDivisionService = HotelRoomDivisionService.instance;