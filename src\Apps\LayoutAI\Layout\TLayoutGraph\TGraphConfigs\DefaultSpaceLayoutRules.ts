import { I_SpaceLayoutRule } from "../TGraphConfigureInterface"


export var DefaultSpaceLayoutRules: { [key: string]: I_SpaceLayoutRule[] } ={
    "厨房": [
    ],
    "卧室": [
        {
            layout_rule_name: "bedOnly",
            suitable_size: { length: 2300, depth: 2000 },
            initial_rules: [
                {
                    main_rect_edge_id: 0,
                    max_length: 3800,
                    group_array: [
                        {
                            group_space_category: "卧床区",
                            length: 3000,
                            length_val: '(Math.min(e_l, 3000))',
                            depth: 2000
                        }
                    ]
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "挂墙电视区",
                            length: 1150,
                            length_val: '(Math.min(e_l, 1150))',
                            depth: 200,
                            start_val: '' + 0
                        }
                    ]

                }
            ]
        },
        {
            layout_rule_name: "小房间",
            suitable_size: { length: 2300, depth: 1500 },
            initial_rules: [
                {
                    main_rect_edge_id: 1,
                    group_array: [
                        {
                            group_space_category: "卧床区",
                            length: 1000,
                            depth: 1800,
                            start_val: '' + 0
                        }
                    ],
                    max_length : 1800

                }
            ]
        },
        {
            layout_rule_name: "侧边衣柜",
            suitable_size: { length: 3000, depth: 3000 },
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 3000,
                            length_val: "(e_l - 50)",
                            depth: 600,
                            start_val: '' + 50
                        }
                    ],
                    min_length: 1250,

                },
                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "卧床区",
                            length: 3500,
                            length_val: '(Math.min(e_l - 600, 3000))',
                            depth: 2400,
                            start_val: '' + 600
                        }
                    ]
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "梳妆台区",
                            length: 1200,
                            depth: 800,
                            length_val: '(depth>=2899?1200:0)',

                        },
                        {
                            group_space_category: "挂墙电视区",
                            length: 1150,
                            depth: 120
                        }
                    ],

                },
            ]
        },
        {
            layout_rule_name: "侧边衣柜-主卧",
            suitable_size: { length: 3000, depth: 3000 },
            suitable_names:["主卧"],
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 3000,
                            length_val: "(e_l - 50)",
                            depth: 600,
                            start_val: '' + 50
                        }
                    ],
                    min_length: 1250,

                },
                {
                    main_rect_edge_id: 0,
                    window_check:1,
                    group_array: [
                        {
                            group_space_category: "卧床区",
                            length: 2200,
                            length_val: '(Math.min(e_l - 600, 3000))',
                            depth: 2200,
                            start_val: '' + 600
                        },
                        {
                            group_space_category: "梳妆台区",
                            length: 900,
                            depth: 800
                        },
                    ]
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "挂墙电视区",
                            length: 1150,
                            depth: 120,
                            start_val : '(e_l - 600)/2'
                        }
                    ],

                },
            ]
        },
        {
            layout_rule_name: "侧边衣柜-次卧",
            suitable_size: { length: 3000, depth: 3000 },
            suitable_names : ["次卧","客卧"],
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 3000,
                            length_val: "(e_l - 50)",
                            depth: 600,
                            start_val: '' + 50
                        }
                    ],
                    min_length: 1250,

                },
                {
                    main_rect_edge_id: 1,
                    group_array: [
                        {
                            group_space_category: "书桌区",
                            length: 1200,
                            depth: 1050,

                        }
                    ],

                },
                {
                    main_rect_edge_id: 0,
                    window_check:1,
                    group_array: [
                        {
                            group_space_category: "卧床区",
                            length: 2200,
                            length_val: '(Math.min(e_l - 1600, 3000))',
                            depth: 2200,
                            start_val: '' + 600
                        }
                    ],
                    min_length : 3500
                },

            ]
        },
        {
            layout_rule_name: "侧边衣柜-次卧2",
            suitable_size: { length: 3000, depth: 3000 },
            suitable_names : ["次卧","客卧"],
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 3000,
                            length_val: "(e_l - 50)",
                            depth: 600,
                            start_val: '' + 50
                        }
                    ],
                    min_length: 1250,

                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "书桌区",
                            length: 1200,
                            depth: 1050,
                            length_val:"(depth>=3100?1200:0)",
                        },
                        {
                            group_space_category: "书柜区",
                            length: 1000,
                            depth: 450,
                            length_val:"(depth>=2800?Math.min(e_l - 1200,2000):0)",
                            start_val : "(depth>=3100?1200:0)",

                        }
                    ],

                },
                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "卧床区",
                            length: 2200,
                            length_val: '(Math.min(e_l - 600, 3000))',
                            depth: 2200,
                            start_val: '' + 600
                        }
                    ],
                },

            ]
        },
        {
            layout_rule_name: "侧边衣柜-儿童房",
            suitable_size: { length: 3000, depth: 3000 },
            suitable_names : ["儿童房"],
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 3000,
                            length_val: "(e_l - 50)",
                            depth: 600,
                            start_val: '' + 50
                        }
                    ],
                    min_length: 1250,


                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "书柜区",
                            length: 3000,
                            depth: 450
                        },
                    ]
                },
                {
                    main_rect_edge_id: 1,
                    group_array: [
                        {
                            group_space_category: "卧床区",
                            length: 1400,
                            length_val :"(depth>=2600)?0:1400",
                            depth: 1800
                        },
                        {
                            group_space_category: "书桌区",
                            length: 1200,
                            depth: 1050,

                        }
                    ],

                },


            ]
        },
        // {
        //     layout_rule_name: "背靠书柜-次卧",
        //     suitable_size: { length: 3000, depth: 3000 },
        //     suitable_names : ["次卧","客卧"],
        //     initial_rules: [
        //         {
        //             main_rect_edge_id: -1,
        //             group_array: [
        //                 {
        //                     group_space_category: "卧室-衣柜区",
        //                     length: 3000,
        //                     length_val: "(e_l - 50)",
        //                     depth: 600,
        //                     start_val: '' + 50
        //                 }
        //             ],
        //             min_length: 1250,

        //         },
        //         {
        //             main_rect_edge_id: 0,
        //             group_array: [
        //                 {
        //                     group_space_category: "书柜区",
        //                     length: 3000,
        //                     depth: 450,
        //                     start_val: '' + 600
        //                 },
        //                 {
        //                     group_space_category: "卧床区",
        //                     length: 2200,
        //                     not_on_wall:true,
        //                     wall_nor_dist : 1500,
        //                     depth: 2200,
        //                     start_val: '' + 600
        //                 }
        //             ]
        //         },

        //     ]
        // },
        {
            layout_rule_name: "L-入户衣柜",
            suitable_size: { length: 3500, depth: 3500 },
            suitable_shape: "L",
            initial_rules: [
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "梳妆台区",
                            length: 1200,
                            depth: 800
                        },
                        {
                            group_space_category: "挂墙电视区",
                            length: 1150,
                            depth: 120
                        }
                    ]

                },
                {
                    main_rect_edge_id: 0,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2200,
                            depth: 2200,
                            length_val: 'e_l',
                            start_val: '' + 0
                        },
                    ]
                },
                {
                    main_rect_edge_id: 4,

                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ]
                }
            ]
        },
        {
            layout_rule_name: "L-入户衣柜2",
            suitable_size: { length: 3500, depth: 3500 },
            suitable_shape: "L",
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 3000,
                            length_val: "(e_l - 50)",
                            depth: 600,
                            start_val: '' + 50
                        }
                    ]

                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "梳妆台区",
                            length: 900,
                            depth: 800
                        }
                    ]

                },
                {
                    main_rect_edge_id: 0,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2500,
                            depth: 2400,
                            length_val: 'e_l',
                            start_val: '' + 0
                        },
                    ]
                },
                {
                    main_rect_edge_id: 6,

                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ]
                }
            ]
        },
        {
            layout_rule_name: "对面衣柜",
            suitable_size: { length: 2000, depth: 2000 },
            initial_rules: [
                {
                    main_rect_edge_id: 2,
                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ],
                },
                {
                    main_rect_edge_id: 0,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2500,
                            depth: 2400,
                            length_val: 'e_l',
                            start_val: '' + 0
                        },
                        {
                            group_space_category: "梳妆台区",
                            length: 900,
                            depth: 800
                        }
                    ]
                },

            ]
        },
        {
            layout_rule_name: "床在门侧-1",
            suitable_size: { length: 2000, depth: 2000 },
            suitable_shape: "L",
            initial_rules: [
                {
                    main_rect_edge_id: 5,
                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ],
                },
                {
                    main_rect_edge_id: 1,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2500,
                            depth: 2400,
                            length_val: 'e_l',
                            start_val: '' + 0
                        }
                    ]
                },

            ]
        },
        {
            layout_rule_name: "床在门侧-2",
            suitable_size: { length: 2000, depth: 2000 },
            suitable_shape: "L",
            initial_rules: [
                {
                    main_rect_edge_id: 6,
                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ],
                },
                {
                    main_rect_edge_id: 1,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2500,
                            depth: 2400,
                            length_val: 'e_l',
                            start_val: '' + 0
                        }
                    ]
                },

            ]
        },
        {
            layout_rule_name: "床在门侧-2",
            suitable_size: { length: 2000, depth: 2000 },
            initial_rules: [
                {
                    main_rect_edge_id: 2,
                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l-900)',
                            depth: 600,
                            start_val: '' + 900
                        }
                    ],
                },
                {
                    main_rect_edge_id: 1,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2500,
                            depth: 2400,
                            length_val: 'e_l',
                            start_val: '' + 0
                        }
                    ]
                },

            ]
        },
        {
            layout_rule_name: "床在门侧-4",
            suitable_size: { length: 2000, depth: 2000 },
            suitable_shape: "L",
            initial_rules: [
                {
                    main_rect_edge_id: 5,
                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ],
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2500,
                            depth: 2400,
                            length_val: 'e_l',
                            start_val: '' + 0
                        }
                    ]
                },

            ]
        },
        {
            layout_rule_name: "床在门侧-5",
            suitable_size: { length: 2000, depth: 2000 },
            suitable_shape: "L",
            initial_rules: [
                {
                    main_rect_edge_id: 6,
                    min_length: 1250,
                    group_array: [
                        {
                            group_space_category: "卧室-衣柜区",
                            length: 2500,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ],
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [

                        {
                            group_space_category: "卧床区",
                            length: 2500,
                            depth: 2400,
                            length_val: 'e_l',
                            start_val: '' + 0
                        }
                    ]
                },

            ]
        },
    ],
    "书房": [
        {
            layout_rule_name: "背靠书柜",
            suitable_size: { length: 3000, depth: 3000 },
            initial_rules: [
                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "书柜区",
                            length: 3500,
                            length_val: '(Math.min(e_l, 4000))',
                            depth: 600,
                            start_val: '' + 0
                        },
                        {
                            group_space_category: "书桌区",
                            length: 2600,
                            length_val: '(Math.min(e_l * 0.8, 2600))',
                            depth: 1000,
                            not_on_wall: true,
                            dir_type: 180,
                            wall_nor_dist: 1000,
                            wall_dir_dist: 0

                        }
                    ]
                }
            ]
        },
        {
            layout_rule_name: "侧边书柜",
            suitable_size: { length: 3000, depth: 3000 },
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "书柜区",
                            length: 3500,
                            length_val: '(Math.min(e_l, 3500))',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ]
                },
                {
                    main_rect_edge_id: 0,

                    group_array: [
                        {
                            group_space_category: "书桌区",
                            length: 3500,
                            length_val: '(Math.min(e_l * 0.8 , 2600))',
                            depth: 1500,
                            start_val: '' + 600
                        }
                    ]
                }
            ]
        },
    ],
    "阳台": [
        {
            layout_rule_name: "洗衣柜",
            suitable_size: { length: 3000, depth: 1600 },
            initial_rules: [
                {
                    main_rect_edge_id: 1,
                    window_check: -1,
                    group_array: [
                        {
                            group_space_category: "阳台柜区",
                            length: 1600,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ]
                }
            ]
        },
        {
            layout_rule_name: "洗衣柜2",
            suitable_size: { length: 3000, depth: 1600 },
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    window_check: -1,
                    min_length:800,
                    group_array: [
                        {
                            group_space_category: "阳台柜区",
                            length: 1600,
                            length_val: '(e_l)',
                            depth: 600,
                            start_val: '' + 0
                        }
                    ]
                }
            ]
        },
        {
            layout_rule_name: "书桌阳台",
            suitable_size: { length: 3000, depth: 1000 },
            initial_rules: [
                {
                    main_rect_edge_id: 1,
                    group_array: [
                        {
                            group_space_category: "书桌区",
                            length: 1600,
                            length_val: '(e_l)',
                            depth: 1200,
                            start_val: '' + 0
                        }
                    ]
                }
            ]
        },
        {
            layout_rule_name: "休闲阳台",
            suitable_size: { length: 2500, depth: 1600 },
            initial_rules: [
                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "阳台-休闲区",
                            length: 1600,
                            length_val: '(e_l - 800)',
                            depth: 1000,
                            start_val: '' + 0,
                            not_on_wall: true,
                            dir_type: 180,
                            wall_nor_dist: 1500,
                            wall_dir_dist: 0
                        }
                    ]
                }
            ]
        }
    ],
    "卫生间": [
        {
            layout_rule_name: "干湿分离1",
            suitable_size: { length: 2200, depth: 2000 },
            initial_rules: [

                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "浴室柜区",
                            length: 1000,
                            depth: 600,
                        },
                        {
                            group_space_category: "马桶区",
                            length: 650,
                            depth: 750,
                            start_val:'Math.max(e_l - 1400,150)'

                        },
                        {
                            group_space_category: "花洒-淋浴区",
                            length: 700,
                            depth: 900,
                            start_val:'(e_l - 700)'
                        }
                    ]
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
           

                    ]
                }
            ]
        },
        {
            layout_rule_name: "干湿分离2",
            suitable_size: { length: 2200, depth: 2000 },
            initial_rules: [

                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "浴室柜区",
                            length: 900,
                            depth: 600,
                        },
                        {
                            group_space_category: "马桶区",
                            length: 650,
                            depth: 750,
                        },

                    ]
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "花洒-淋浴区",
                            length: 700,
                            depth: 900,
                        }

                    ]
                }
            ]
        },
        {
            layout_rule_name: "干湿分离3",
            suitable_size: { length: 2200, depth: 2000 },
            initial_rules: [
                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "浴室柜区",
                            length: 1200,
                            depth: 600,
                            start_val:'Math.max((e_l - 1200)/2,0)'

                        }

                    ]
                },
                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "马桶区",
                            length: 650,
                            depth: 750,
                            start_val:'600'
                        },
                    ],
                    min_length : 1600
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "花洒-淋浴区",
                            length: 700,
                            depth: 900,
                        }

                    ]
                }
            ]
        },
        {
            layout_rule_name: "紧凑布局",
            suitable_size: { length: 2200, depth: 2000 },
            initial_rules: [

                {
                    main_rect_edge_id: -1,
                    group_array: [
                        {
                            group_space_category: "浴室柜区",
                            length: 1200,
                            depth: 600,
                            start_val:'Math.max((e_l - 1200)/2,0)'

                        }

                    ]
                },
                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "花洒-淋浴区",
                            length: 700,
                            depth: 900,
                            start_val:'(e_l - 700)/2'
                        },

                    ]
                },
                {
                    main_rect_edge_id: 1,
                    group_array: [
                        {
                            group_space_category: "马桶区",
                            length: 650,
                            depth: 750,
                            start_val:'(e_l - 650)/2'
                        }
                    ],
                    min_length:1400
                }
            ]
        },
        {
            layout_rule_name: "沿墙排列",
            suitable_size: { length: 2200, depth: 2000 },
            initial_rules: [

                {
                    main_rect_edge_id: 2,
                    group_array: [
                        {
                            group_space_category: "浴室柜区",
                            length: 1200,
                            depth: 600,
                            start_val:'(e_l - 1200)/2'

                        }

                    ]
                },
                {
                    main_rect_edge_id: 1,
                    group_array: [
                        {
                            group_space_category: "马桶区",
                            length: 650,
                            depth: 750,
                            start_val:'0'
                        },

                    ],
                    min_length : 1400
                    
                },
                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "花洒-淋浴区",
                            length: 700,
                            depth: 900,
                            start_val:'0'
                        }

                    ]
                }
            ]
        },
    ],
    "客厅":[
        {
            layout_rule_name: "干湿分离1",
            suitable_size: { length: 2200, depth: 2000 },
            initial_rules: [

                {
                    main_rect_edge_id: 0,
                    group_array: [
                        {
                            group_space_category: "浴室柜区",
                            length: 1000,
                            depth: 600,
                        },
                        {
                            group_space_category: "马桶区",
                            length: 650,
                            depth: 750,
                            start_val:'Math.max(e_l - 1400,150)'

                        },
                        {
                            group_space_category: "花洒-淋浴区",
                            length: 700,
                            depth: 900,
                            start_val:'(e_l - 700)'
                        }
                    ]
                },
                {
                    main_rect_edge_id: 2,
                    group_array: [
           

                    ]
                }
            ]
        }, 
    ]
};
