import { TFigureElement } from "../Layout/TFigureElements/TFigureElement";
import { TGraphBasicConfigs } from "../Layout/TLayoutGraph/TGraphBasicConfigs";
import { compareNames } from "@layoutai/z_polygon";
import { g_figure_alias_dict } from "./FigureImagePaths";
import { TPainter } from "./TPainter";


export class TFigureDrawing
{
    constructor()
    {

    }

    /**
     *  绘制预览图
     * @param painter 
     * @param show_text 
     * @param custom_fill_color 
     * @param font_size 
     */
    static drawPreviewFigure(figure:TFigureElement, painter: TPainter, show_text: boolean = false, custom_fill_color: string = null, font_size: number = 12) {
        let rect = figure.matched_rect || figure.rect;
        painter._context.lineWidth = 1;
        if (rect) {
            let pictureView = rect._attached_elements['pictureView'] = figure.rect._attached_elements['pictureView'];
            let has_rendered_top_view_img: boolean = figure.renderedTopViewImg != null && figure.renderedTopViewImg.src != null;
            if (pictureView == null && has_rendered_top_view_img) {
                pictureView = figure.renderedTopViewImg;
            }
            if (pictureView) {
                // console.info("    TFigureElement.drawPreviewFigure() " + figure.modelLoc + " w=" + rect.w + " h=" + rect.h);
                painter.strokeStyle = "#000";
                painter.fillStyle = figure.fill_color;
                painter.fillStyle = custom_fill_color ? custom_fill_color : "#fff";
                painter._context.lineWidth = 1;
                let model_flag = figure?._matched_material?.modelFlag || "1";
                let is_cabinet = (model_flag != '1' && model_flag != '15') || ((compareNames([figure.category, figure.sub_category], TGraphBasicConfigs.MainCabinetsCategories) == 1) && figure.sub_category.endsWith("柜"));
                let is_floor_carpet = figure.category.includes("地毯");
                let is_background_wall = figure.category.includes("背景墙");
                let is_filling = is_cabinet || is_floor_carpet || is_background_wall;

                let is_use_u_dv_flag = true;
                if (has_rendered_top_view_img) {
                    is_filling = true;
                    is_cabinet = false;
                    is_use_u_dv_flag = false;
                }
                painter.drawPreviewFigureRect(rect, pictureView, is_filling, is_use_u_dv_flag);

                if (is_cabinet) {
                    painter._style_mapping["white"] = "#ffffff33";
                    painter.drawFigureRect(rect, figure.sub_category || figure.category,
                        [g_figure_alias_dict[figure.category], figure.sub_category, figure.category]);
                    painter._context.globalAlpha = 1.;
                }
                if (is_cabinet) {
                    painter._context.globalAlpha = 1;
                }
                painter.fillStyle = "#000";
            }
            else {
                painter.strokeStyle = "#000";
                if (!figure._is_decoration) {
                    painter.drawFigureRect(rect, figure.sub_category, [figure.category, figure.public_category]);
                    // figure.drawFigure(painter,false);
                }
                else {
                    painter.strokePolygons([rect]);
                }
            }


            // 如果没有匹配到素材, 就要高亮显示
            if (!figure.checkIsMatchedMaterials() &&
                (figure._room && (figure._room.isFigureElementInCurrentScope(figure) || (figure._room.waitingForFurshiRemaining && !figure._room.isFigureElementInFurnishedScope(figure))))) {
                painter.strokeStyle = "#f00";
                painter.strokePolygons([rect]);
            }
        }
    }

    /**
     *  绘制图元
     * @param painter 
     * @param show_text 
     * @param custom_fill_color 
     * @param font_size 
     */
    static drawFigure(figure:TFigureElement, painter: TPainter, show_text: boolean = false, custom_fill_color: string = null, font_size: number = 12, line_color: string = "#777777") {
        let rect = figure.rect;
        if (rect) {
            painter.strokeStyle = "#000";

            painter.fillStyle = figure.fill_color;


            painter.fillStyle = custom_fill_color ? custom_fill_color : "#fff";

            painter._context.lineWidth = 1;

            painter._style_mapping["black"] = line_color;

            painter.drawFigureRect(rect, figure.sub_category || figure.category,
                [g_figure_alias_dict[figure.category], figure.sub_category, figure.category]);
            painter.fillStyle = "#000";


            if (show_text) {
                painter.fillStyle = custom_fill_color ? custom_fill_color : "#000";
                let sub_category = (figure.sub_category !== "Default") ? figure.sub_category : figure.material_name;
                let text = sub_category;
                if (text.length > 5) {
                    let ll = text.length;
                    text = text.substring(0, ll - 3) + "\n" + text.substring(ll - 3);
                }

                let angle = 0;

                if (rect._w < 200 && rect.w < rect.h) {
                    angle = rect.rotation_z + Math.PI / 2;
                    font_size /= 2;

                }

                painter.drawText(text, rect.rect_center, angle, font_size);
            }
            if (figure.spotlight) {
                painter.drawEdges(rect.edges, 0, "#f00");
            }
        }
    }

    /**
     *  绘制轮廓
     * @param painter 
     * @param show_text 
     * @param custom_fill_color 
     * @param font_size 
     */
    static drawOutline(figure:TFigureElement, painter: TPainter, show_text: boolean = false, custom_fill_color: string = null, font_size: number = 12) {
        let rect = figure.rect;
        painter._context.lineWidth = 1;
        if (rect) {
            let pictureView = rect._attached_elements['wireframeImage'] = figure.rect._attached_elements['wireframeImage'];
            if (pictureView) {
                // console.info("    TFigureElement.drawPreviewFigure() " + figure.modelLoc + " w=" + rect.w + " h=" + rect.h);
                painter.strokeStyle = "#000";
                painter.fillStyle = figure.fill_color;
                painter.fillStyle = custom_fill_color ? custom_fill_color : "#fff";
                painter._context.lineWidth = 1;
                let is_use_u_dv_flag = true;
                painter.drawPreviewFigureRect(rect, pictureView, true, is_use_u_dv_flag);
                painter.fillStyle = "#000";
            }
            else {
                painter.strokeStyle = "#000";
                if (!figure._is_decoration) {
                    painter.drawFigureRect(rect, figure.sub_category, [figure.category, figure.public_category]);
                    // figure.drawFigure(painter,false);
                }
                else {
                    painter.strokePolygons([rect]);
                }
            }
        }
    }
}