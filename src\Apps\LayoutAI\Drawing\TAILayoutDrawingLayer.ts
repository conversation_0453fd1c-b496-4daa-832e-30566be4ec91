import { I_SwjXmlScheme } from "../AICadData/SwjLayoutData";
import { ZRect } from "@layoutai/z_polygon";
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { TAppManagerBase } from "../../AppManagerBase";
import { TBaseEntity } from "../Layout/TLayoutEntities/TBaseEntity";


export class TAILayoutDrawingLayer extends TDrawingLayer {
    _src_swj_layout_data: I_SwjXmlScheme;


    constructor(manager: TAppManagerBase) {
        super(CadDrawingLayerType.AILayoutDrawing, manager);
        this._src_swj_layout_data = null;
        this._dirty = true;
    }

    get rooms() {
        return this.layout_container._rooms;
    }
    async fetchPublicCategoryForAll3DMaterials(xmlScheme: I_SwjXmlScheme) {
        await this.layout_container.fetchPublicCategoryForAll3DMaterials(xmlScheme);
    }

    updateRoomsFromXmlData(xmlScheme: I_SwjXmlScheme) {
        this.layout_container.fromXmlSchemeData(xmlScheme);
    }

    _updateLayerContent(): void {

        if (this.rooms.length == 0) {
            this.updateRoomsFromXmlData(this._src_swj_layout_data);
        }


        super._updateLayerContent();
    }
    onDraw(): void {
        // if(!this._src_swj_layout_data) return;

        if (this._dirty) {
            this._updateLayerContent();
        }
        //  这里是设置canvas一些属性
        this.painter.strokeStyle = "#000";
        this.painter.fillStyle = "#777";

        this.painter.strokeStyle = "#000";
        this.painter.fillStyle = "#777"; 

        let wall_rects = this.layout_container.getCandidateRects(["Wall"]);
        let window_rects = this.layout_container.getCandidateRects(["Window","Door"]);
        let room_polys = this.layout_container.getCandidateRects(["RoomArea"]);

        if(wall_rects)
        {
            let target_rects : ZRect[] = [];
            for(let rect of wall_rects)
            {
                if(TBaseEntity.is_deleted(rect)) continue;
                
     
                if(rect.orientation_z_nor.z < 0)
                {
                    rect = rect.clone();
                    rect.invertOrder();
                }
                 target_rects.push(rect);     
            }
            for(let rect of window_rects)
            {
                let t_rect = rect.clone();
                if(t_rect.orientation_z_nor.z > 0)
                {
                    t_rect.invertOrder();
                }
                target_rects.push(t_rect);

            }
            this.painter.fillPolygons(target_rects);  
        }
        let entities = this.layout_container.getCandidateEntities(["Wall","Window","Door","StructureEntity"]);

        for(let entity of entities)
        {
            entity.drawEntity(this.painter,{is_draw_figure:true});            
        }
        this.painter.strokeStyle= "#a42";
        this.painter.fillStyle = "#fff";

        // if(LayoutAI_App.IsDebug)
        // {
        //     for(let room of  this.layout_container._rooms)
        //     {
        //         if(room._ceilling_list)
        //         {
        //             for(let ci = 0; ci < room._ceilling_list.length; ci++)
        //             {
        //                 let ceiling = room._ceilling_list[ci];
        //                 let rect = ceiling.rect;
        //                 this.painter.fillStyle = ci == 0 ? "#004" : "#020";
        //                 this.painter.fillPolygons([rect],0.1);
        //                 this.painter.strokePolygons([rect]);

        //             }
        //         }
        //         if(room._curtain_ceiling_list)
        //         {
        //             for(let ceiling of room._curtain_ceiling_list)
        //             {
        //                 let rect = ceiling.rect;
        //                 this.painter.fillStyle = "#800";
        //                 this.painter.fillPolygons([rect],0.1);
        //                 this.painter.strokePolygons([rect]);

        //             }
        //         }
        //     }
        // }
    }
}