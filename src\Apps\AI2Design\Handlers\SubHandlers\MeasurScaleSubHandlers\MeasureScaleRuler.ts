import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { Vector3 } from "three";
/**
 * 临摹图识别中的绘制尺子
 * 用于绘制测量标尺及其刻度、端点等
 */
export class MeasureScaleRuler {
    /** 标尺高度（像素） */
    private readonly rulerHeight = 22;

    /** 标尺相关的颜色配置 */
    private readonly colors = {
        /** 标尺主体背景色 */
        body: '#FFD700',      // 金黄色
        /** 标尺边框颜色 */
        border: '#147FFA',    // 蓝色
        /** 刻度线颜色 */
        scale: '#333333',     // 深灰色
        /** 文字颜色 */
        text: '#333333',      // 深灰色
        /** 端点相关颜色 */
        point: {
            /** 端点外圈颜色 */
            outer: '#147FFA', // 蓝色
            /** 端点内圈颜色 */
            inner: '#FFFFFF', // 白色
        }
    };

    /** 端点检测的半径范围 */
    private readonly pointHitRadius = 8;

    /** 尺子主体检测的范围（像素） */
    private readonly rulerHitThreshold = 5;

    private inputElement: HTMLInputElement | null = null;
    private _painter: TPainter;

    constructor(private ctx: CanvasRenderingContext2D) { }

    private drawRulerBody(length: number): void {
        // 绘制尺子主体
        this.ctx.beginPath();
        this.ctx.fillStyle = this.colors.body;
        this.ctx.fillRect(0, -this.rulerHeight / 2, length, this.rulerHeight);

        // 绘制边框
        this.ctx.strokeStyle = this.colors.border;
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(0, -this.rulerHeight / 2, length, this.rulerHeight);
    }

    private drawScales(length: number): void {
        // 固定的刻度间距（像素）
        const fixedInterval = 5;

        // 计算可以绘制的刻度数量
        const numberOfScales = Math.floor(length / fixedInterval);

        this.ctx.strokeStyle = this.colors.scale;
        this.ctx.lineWidth = 1;

        // 绘制刻度
        for (let i = 0; i <= numberOfScales; i++) {
            const x = i * fixedInterval;

            // 如果超出标尺长度，则停止绘制
            if (x > length) break;

            // 每5个刻度一个主刻度
            let tickHeight;
            if (i % 5 === 0) {
                // 主刻度
                tickHeight = this.rulerHeight * 0.8;
            } else {
                // 小刻度
                tickHeight = this.rulerHeight * 0.4;
            }

            this.ctx.beginPath();
            this.ctx.moveTo(x, this.rulerHeight / 2);
            this.ctx.lineTo(x, this.rulerHeight / 2 - tickHeight);
            this.ctx.stroke();
        }
    }

    private drawEndPoint(pos: { x: number; y: number; }, isActive: boolean = false): void {
        const radius = isActive ? 6 : 5;  // 选中时略微放大

        // 绘制外圈
        this.ctx.beginPath();
        this.ctx.strokeStyle = isActive ? '#ff4d4f' : this.colors.point.outer;  // 选中时变红
        this.ctx.lineWidth = isActive ? 3 : 2;  // 选中时线条更粗
        this.ctx.arc(pos.x, pos.y, radius, 0, Math.PI * 2);
        this.ctx.stroke();

        // 绘制内圈（填充白色）
        this.ctx.beginPath();
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.arc(pos.x, pos.y, radius - 1, 0, Math.PI * 2);
        this.ctx.fill();
    }

    private drawMeasurement(start2d: { x: number; y: number; }, end2d: { x: number; y: number; }, value: number, angle: number): void {
        if (!this.inputElement) {
            this.inputElement = document.createElement('input');
            this.inputElement.id = 'dimension_input';
            this.inputElement.style.position = 'absolute';
            this.inputElement.style.border = 'none';
            this.inputElement.style.outline = 'none';
            this.inputElement.style.textAlign = 'center';
            this.inputElement.style.color = '#2b2b2b';
            this.inputElement.style.fontSize = '14px';
            this.inputElement.style.fontFamily = 'Arial';
            this.inputElement.style.padding = '2px 4px';
            this.inputElement.style.borderRadius = '2px';
            this.inputElement.style.cursor = 'pointer';
            this.inputElement.style.textShadow = `
                -1px -1px 0 #fff,
                1px -1px 0 #fff,
                -1px 1px 0 #fff,
                1px 1px 0 #fff,
                0px 0px 2px #fff,
                0px 0px 3px #fff
            `;
            this.inputElement.autocomplete = "off";

            // 添加输入限制
            this.inputElement.type = 'text';
            this.inputElement.pattern = '[0-9]*';
            this.inputElement.addEventListener('keypress', (e) => {
                // 只允许数字键 (0-9)
                if (!/[0-9]/.test(e.key)) {
                    e.preventDefault();
                }
            });

            // 阻止粘贴非数字内容
            this.inputElement.addEventListener('paste', (e) => {
                e.preventDefault();
                const pastedText = (e.clipboardData || (window as any).clipboardData).getData('text');
                if (/^[0-9]+$/.test(pastedText)) {
                    const newValue = parseInt(pastedText, 10);
                    this.inputElement!.value = newValue.toString();
                }
            });

            // 失去焦点时确保值为有效整数
            this.inputElement.addEventListener('blur', () => {
                const value = this.inputElement!.value;
                if (value && !/^[0-9]+$/.test(value)) {
                    this.inputElement!.value = '';
                }
            });

            // 添加回车键处理
            this.inputElement.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.inputElement?.blur();
                }
            });

            document.getElementById('Canvascontent')?.appendChild(this.inputElement);
        }

        // 更新输入框位置和值
        if (this.inputElement && this._painter) {
            const center = new Vector3(
                (start2d.x + end2d.x) / 2,
                (start2d.y + end2d.y) / 2,
                0
            );
            const screenPos = this._painter.worldToScreen(center);

            if(this.inputElement.parentElement)
            {
                let t_rect = this.inputElement.parentElement.getBoundingClientRect();
                screenPos.x -= t_rect.left;
                screenPos.y -= t_rect.top;
            }
            this.inputElement.value = value?.toString() || '';
            this.inputElement.style.width = ((this.inputElement.value.length + 2) * 8) + 'px';
            this.inputElement.style.left = (screenPos.x - this.inputElement.clientWidth/2) + "px";
            this.inputElement.style.top = (screenPos.y + this.inputElement.clientHeight/2) + "px";
            this.inputElement.style.display = 'block';
        }
    }

    private drawDimensionLines(start2d: { x: number; y: number; }, end2d: { x: number; y: number; }, angle: number): void {
        const offset = 30; // 标尺偏移距离
        const extensionLength = 15; // 延伸线长度

        this.ctx.strokeStyle = '#147FFA'; // 改回蓝色
        this.ctx.lineWidth = 2;

        // 计算垂直偏移的方向
        const perpX = Math.cos(angle - Math.PI / 2) * offset;
        const perpY = Math.sin(angle - Math.PI / 2) * offset;

        // 绘制起点延伸线
        this.ctx.beginPath();
        this.ctx.moveTo(start2d.x, start2d.y);
        this.ctx.lineTo(start2d.x + perpX, start2d.y + perpY);
        // 继续延伸一小段
        this.ctx.lineTo(
            start2d.x + perpX + Math.cos(angle - Math.PI / 2) * extensionLength,
            start2d.y + perpY + Math.sin(angle - Math.PI / 2) * extensionLength
        );
        this.ctx.stroke();

        // 绘制终点延伸线
        this.ctx.beginPath();
        this.ctx.moveTo(end2d.x, end2d.y);
        this.ctx.lineTo(end2d.x + perpX, end2d.y + perpY);
        // 继续延伸一小段
        this.ctx.lineTo(
            end2d.x + perpX + Math.cos(angle - Math.PI / 2) * extensionLength,
            end2d.y + perpY + Math.sin(angle - Math.PI / 2) * extensionLength
        );
        this.ctx.stroke();
    }

    draw(start: Vector3, end: Vector3, value: number, painter: any, activePoint: 'start' | 'end' | null = null): void {
        const start2d = painter.project2D(start);
        const end2d = painter.project2D(end);

        const angle = Math.atan2(end2d.y - start2d.y, end2d.x - start2d.x);

        // 使用世界坐标计算实际长度
        const length = Math.sqrt(
            Math.pow((end.x - start.x) * painter._p_sc, 2) +
            Math.pow((end.y - start.y) * painter._p_sc, 2)
        );

        // 绘制标注线
        this.drawDimensionLines(start2d, end2d, angle);

        this.ctx.save();

        // 获取当前缩放比例
        const scale = painter._p_sc;
        this._painter = painter;

        // 将标尺绘制位置偏移到左侧
        this.ctx.translate(
            start2d.x,
            start2d.y
        );
        this.ctx.rotate(angle);

        // 绘制尺子主体和刻度
        this.drawRulerBody(length);
        this.drawScales(length);

        this.ctx.restore();

        // 绘制端点，传入激活状态
        this.drawEndPoint(start2d, activePoint === 'start');
        this.drawEndPoint(end2d, activePoint === 'end');

        // 绘制测量值
        if (value !== undefined && value !== null) {
            this.drawMeasurement(start, end, value, angle);
        }
    }

    /**
     * 检查点是否在端点上
     */
    isOverEndpoint(mousePos: { x: number, y: number }, endPoint: { x: number, y: number }, scale: number = 1): boolean {
        const scaledRadius = this.pointHitRadius / scale; // 根据缩放调整检测半径
        const distance = Math.sqrt(
            Math.pow(mousePos.x - endPoint.x, 2) +
            Math.pow(mousePos.y - endPoint.y, 2)
        );
        return distance <= scaledRadius;
    }

    /**
     * 检查点是否在尺子主体上
     */
    isOverRuler(mousePos: { x: number, y: number }, start: { x: number, y: number }, end: { x: number, y: number }, scale: number = 1): boolean {
        const scaledThreshold = (this.rulerHeight / 2 + this.rulerHitThreshold) / scale; // 根据缩放调整检测范围

        // 计算尺子的角度
        const angle = Math.atan2(end.y - start.y, end.x - start.x);

        // 计算鼠标点到直线的距离
        const A = end.y - start.y;  // 直线的 A 系数
        const B = start.x - end.x;  // 直线的 B 系数
        const C = end.x * start.y - start.x * end.y;  // 直线的 C 系数

        // 点到直线的距离公式
        const distance = Math.abs(A * mousePos.x + B * mousePos.y + C) /
            Math.sqrt(A * A + B * B);

        // 计算投影点是否在线段上
        const dot = ((mousePos.x - start.x) * (end.x - start.x) +
            (mousePos.y - start.y) * (end.y - start.y));

        const lenSquared = ((end.x - start.x) * (end.x - start.x) +
            (end.y - start.y) * (end.y - start.y));

        const param = lenSquared ? dot / lenSquared : -1;

        // 检查点是否在线段范围内，并且到直线的距离在阈值范围内
        return distance <= scaledThreshold && param >= 0 && param <= 1;
    }

    // 添加清理方法
    public cleanup(): void {
        if (this.inputElement && this.inputElement.parentElement) {
            this.inputElement.parentElement.removeChild(this.inputElement);
            this.inputElement = null;
        }
    }

    // 获取输入框的值
    public getInputValue(): number | null {
        if (this.inputElement) {
            const value = parseFloat(this.inputElement.value);
            return isNaN(value) ? null : value;
        }
        return null;
    }
} 