import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        root: css`
      width:100%;
      height:calc(var(--vh, 1vh) * 100);
      .custom-keyboard {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #ccc;
        padding: 10px;
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
      }

      .keypad {
          display: flex;
          flex-wrap: wrap;
      }

      .keypad button {
          flex: 1 0 30%; /* 控制按钮大小 */
          margin: 5px;
          padding: 15px;
          font-size: 18px;
          cursor: pointer;
      }
      
    `,
        content: css`
      position: fixed;
      top: 0;
      left: 0; 
      right: 0;
      bottom: 0;
      overflow: hidden;
      input {
        z-index : 3;
      }
      @media screen and (orientation: portrait) {
        top: 0px;
        left: 0; 
        right: 0;
        bottom: 0;
      }

      // 横屏样式
      @media screen and (orientation: landscape) {
          @media screen and (max-width : 900px ) {
            top: 0px;
            left: 0; 
            right: 0px;
            bottom: 0;
        }
    `,
        canvas3d: css`
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      z-index:-1;
    `,
        side_pannel: css`
      position: fixed;
      bottom: 0;
      left: 0;
      width: 0px;
      background-color: #fff;
      z-index: 998;
    `,
        canvas_pannel: css`
        position: absolute;
        left: 0;
        top: 0;        
        width : calc(100%);
        height : calc(100%);
        overflow: hidden;
        background-color: #eaeaea;
        background-image:
         -webkit-linear-gradient(180deg, #e2e2e2 1px, transparent 1px) ,
          -webkit-linear-gradient(90deg, #e2e2e2 1px, transparent 1px);
        background-size: 50px 50px;
        background-position: calc(50% + 25px) 0%;
        z-index:1;
        &.left_panel_layout {
          height : calc(100%);
        }
        .canvas {
          position : absolute;
          left: 0px;
          top: 0px;
          touch-action: none;
          &.canvas_drawing {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;
          }
          &.canvas_moving {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;
          }
          &.canvas_leftmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;
          }
          &.canvas_rightmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;
          }
          &.canvas_acrossmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;
          }
          &.canvas_verticalmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;
          }
          &.canvas_text {
            cursor : text;
          }
          &.canvas_pointer {
            cursor : pointer;
          }
          &.canvas_splitWall {
            cursor : url(./static/icons/split.png) 0 0,auto;
          }
        }

        .canvas_btns {
          width: auto;
          margin: 0 auto;
          position: fixed;
          display: flex;
          justify-content: center;
          bottom: 35px;
          z-index:10;
          left: 50%;
          transform: translateX(-50%);
          .btn {
            ${checkIsMobile() ?
                `
              width: 120px;
              height: 36px;
              font-size: 14px;
            `
                :
                `
              width: 200px;
              height: 48px;
              font-size: 16px;
            `
            }
            border-radius: 6px;
            border: none;

            font-weight: 600;
            margin-right: 10px;
            margin-left: 10px;
          }
          .design_btn {
            background: #e6e6e6;
            margin-right: 20px;
          }
          @media screen and (max-height: 600px){
            bottom: 50px !important;
          }
    }
    `,
        navigation: css`
      position:absolute;
      top:0px;
      width:100%;
      height:50px;
      border-bottom:1px solid #eee;
      background:#fff;
      z-index:5;
    `,
        backBtn: css`
      position:absolute;
      z-index:2;
      padding-left:2px;
      font-size:14px;
      line-height:50px;
      color:#333;
      float:left;
    `,
        forwardBtn: css`
      position:absolute;
      right:0;
      z-index:2;
      padding-right:10px;
      font-size:14px;
      line-height:50px;
      color:#333;
    `,
        schemeNameSpan: css`
      width:100%;
      font-size:16px;
      line-height:50px;
      text-align:center;
    `,
        overlay: css`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
        focusIcon: css`
      position: fixed;
      top: 68px;
      right: 12px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      box-shadow: 0px 6px 20px 0px #00000014;
      font-size: 30px;
      // 竖屏样式

      @media screen and (max-width: 450px) { // 手机宽度
        width: 28px !important;
        height: 28px !important;
        font-size: 16px !important;
      }     
      
      @media screen and (orientation: portrait) {
        width: 48px;
        height: 48px;
      }

      // 横屏样式
      @media screen and (orientation: landscape) {
        width: 40px;
        height: 40px;
        font-size: 25px;
      }
    `,
        multiSchemeIcon: css`
      position: fixed;
      top: 120px; /* Adjust this value to position it below the focusIcon */
      right: 12px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      box-shadow: 0px 6px 20px 0px #00000014;
      font-size: 30px;

      @media screen and (max-width: 450px) {
        width: 28px !important;
        height: 28px !important;
        font-size: 16px !important;
      }     
      
      @media screen and (orientation: portrait) {
        width: 48px;
        height: 48px;
      }

      @media screen and (orientation: landscape) {
        width: 40px;
        height: 40px;
        font-size: 25px;
      }
    `,
        RoomAreaBtns: css`
      position: absolute;
      top: 0px;
      left: 0;
      width: 100%;
      height: 45px;
      z-index: 999;
      background-color: #fff;
    `,
        aiDraw: css`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999;
    `,
    mobile_atlas_container: css`
        padding: 20px;
        position:absolute;
        left: 0;
        top: 0;
        width:100%;
        height:100%;
        z-index:-1;
        background: #f6f7f9;
    `,
    leftPanelRoot: css`
        position:fixed;
        background : #fff;
        z-index: 10;
        .closeBtn {
            display:none;
            position:absolute;
            right : 6px;
            top : 6px;
            font-size:20px;
            width:60px;
            height : 24px;
            text-align:right;
        }
        &.panel_hide {
            box-shadow: 0px 0px 0px 0px #00000000;
        }
        @media screen and (orientation: landscape) {
            position:fixed;
            left: 12px !important;
            top: 52px !important;
            bottom: 12px !important;
            right: auto !important;
            height: auto;
            padding-left: 0 !important;
            max-height: calc(var(--vh, 1vh) * 100);
            max-width:224px;
            width: 224px;
            border-radius: 8px;
            box-shadow:  0px 0px 16px 10px #0000000A;
            &.panel_hide {
            display: none;
            }
        }
        @media screen and (orientation: portrait) {
            position:fixed;
            left:0;
            bottom:0px;
            right:0;
            width : auto;
            height:340px;
            max-width : auto;
            max-height:340px;
            overflow: hidden;
            background-color: #fff;
            border-radius: 8px 8px 0px 0px;
            box-shadow:  0px 0px 16px 10px #0000000A;
            @media screen and (-webkit-min-device-pixel-ratio:2) and (max-height:700px) {
            transform : scale(0.7);
            transform-origin : bottom;
            left : -15%;
            right : -15%;
            }
            &.panel_hide {
            max-width : 0px;
            }
            .closeBtn {
            display : block;
            }
        }


        .fade-enter {
            opacity: 0;
        }

        .fade-enter-active {
            opacity: 1;
            transition: opacity 300ms ease-in-out;
        }

        .fade-exit {
            opacity: 1;
        }

        .fade-exit-active {
            opacity: 0;
            transition: opacity 300ms ease-in-out;
        }
    `,
        listContainer: css`
        height:100%;
        width:100%;
    `,
    }

});
