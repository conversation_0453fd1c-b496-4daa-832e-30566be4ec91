"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[8960],{18960:function(e,n,t){t.r(n),t.d(n,{default:function(){return fe}});var i=t(13274),o=t(85783),a=t(66910),r=t(15696),l=t(77320),s=t(33100),c=t(36134),u=t(41594),d=t(27347),h=t(98612),m=t(9003),f=t(88934),p=t(78154),v=t(78644),b=t(83657),y=t(23825),g=t(17287),x=t(90503),S=t(19356),w=t(25629),j=t(22640),_=t(7130),C=t(11164),A=t(27164),D=t(59525),N=t(76330),I=t(49063),M=t(5640),E=t(14181),k=t(65640);function L(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}function R(e,n,t,i,o,a,r){try{var l=e[a](r),s=l.value}catch(e){return void t(e)}l.done?n(s):Promise.resolve(s).then(i,o)}function z(e){return function(){var n=this,t=arguments;return new Promise((function(i,o){var a=e.apply(n,t);function r(e){R(a,i,o,r,l,"next",e)}function l(e){R(a,i,o,r,l,"throw",e)}r(void 0)}))}}function T(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,a=[],r=!0,l=!1;try{for(t=t.call(e);!(r=(i=t.next()).done)&&(a.push(i.value),!n||a.length!==n);r=!0);}catch(e){l=!0,o=e}finally{try{r||null==t.return||t.return()}finally{if(l)throw o}}return a}}(e,n)||U(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(e){return function(e){if(Array.isArray(e))return L(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||U(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(e,n){if(e){if("string"==typeof e)return L(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?L(e,n):void 0}}function O(e,n){var t,i,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},r=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return r.next=l(0),r.throw=l(1),r.return=l(2),"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;r&&(r=0,l[0]&&(a=0)),a;)try{if(t=1,i&&(o=2&l[0]?i.return:l[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,l[1])).done)return o;switch(i=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,i=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){a.label=l[1];break}if(6===l[0]&&a.label<o[1]){a.label=o[1],o=l;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(l);break}o[2]&&a.ops.pop(),a.trys.pop();continue}l=n.call(e,a)}catch(e){l=[6,e],i=0}finally{t=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var P=(0,r.observer)((function(){var e,n,t,a,r,h,v,b,g,x=function(e,n,t,i){var o=function(e,n,t){return Math.abs((e.x*(n.y-t.y)+n.x*(t.y-e.y)+t.x*(e.y-n.y))/2)};return o(n,t,i)===o(e,t,i)+o(n,e,i)+o(n,t,e)},S=(0,o.B)().t,w=(0,_.A)().styles,j=(0,m.P)(),L=T((0,u.useState)([]),2),R=L[0],U=L[1],P=T((0,u.useState)("null"),2),B=P[0],H=P[1],V=d.nb.instance.layout_container,W=T((0,u.useState)([]),2),Z=W[0],$=W[1],G=T((0,u.useState)(0),2),X=G[0],Y=G[1],q=T((0,u.useState)(""),2),K=q[0],J=q[1],Q=T((0,u.useState)(!1),2),ee=Q[0],ne=Q[1],te=T((0,u.useState)(!1),2),ie=te[0],oe=te[1],ae=T((0,u.useState)(!1),2),re=ae[0],le=ae[1],se=T((0,u.useState)({designStyle:[],rooms:[]}),2),ce=se[0],ue=se[1],de=(0,I.Zp)(),he=[{id:"Layout",label:S("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:S("风格"),icon:"icon-a-TypefenggeStateDefault"},"2D"===j.homeStore.viewMode?{id:"material",label:S("素材"),icon:"icon-a-TypesucaiStateDefault"}:null,{id:"attribute",label:S("属性"),icon:"icon-a-TypeshuxingStateDefault"},"3D_FirstPerson"===j.homeStore.viewMode?{id:"view",label:S("视角"),icon:"icon-smarttemplate"}:null,"2D"===j.homeStore.viewMode?{id:"aidraw",label:S("AI绘图"),icon:"icon-AIchutu"}:null,y.Ic?{id:"similar",label:S("相似匹配"),icon:"icona-Typexuanzebujian"}:null,y.Ic?{id:"create",label:S("新建"),icon:"iconfile"}:null].filter(Boolean),me=[{id:"Layout",label:S("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:S("风格"),icon:"icon-a-TypefenggeStateDefault"},{id:"submitDrawing",label:S("提交绘图"),icon:"icon-xuanranRender"},{id:"atlas",label:S("图册"),icon:"icon-tuku"}].filter(Boolean),fe=[{id:"attribute",label:S("属性"),icon:"icon-a-TypeshuxingStateDefault"},{id:"rotate",icon:"icon-rotate",label:S("旋转"),disabled:!1,divider:!1},{id:"flip",icon:"icon-horizontalflip_line",label:S("左右镜像"),disabled:!1,divider:!1},{id:"flip_vertical",icon:"icon-verflip_line",label:S("上下镜像"),disabled:!1,divider:!1},{id:"copy",icon:"icon-niantie",label:S("复制"),disabled:!1,divider:!1},"Furniture"===(null===(e=j.homeStore.selectEntity)||void 0===e?void 0:e.type)&&!(null===(n=j.homeStore.selectEntity)||void 0===n?void 0:n.figure_element.haveMatchedMaterial())&&{id:"size",icon:"icon-chicun",label:S("尺寸"),disabled:!1,divider:!1},"Furniture"===(null===(t=j.homeStore.selectEntity)||void 0===t?void 0:t.type)&&!(null===(a=j.homeStore.selectEntity)||void 0===a?void 0:a.figure_element.haveMatchedMaterial())&&{id:"pos_z",icon:"icon-lidi",label:S("离地"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:S("删除"),disabled:!1,divider:!0}];fe=fe.filter(Boolean);var pe=[{id:"rotate",icon:"icon-rotate",label:S("旋转"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:S("删除"),disabled:!1,divider:!0}],ve=[{id:"delete",icon:"icon-delete",label:S("删除"),disabled:!1,divider:!0}],be=[{id:"splitWall",icon:"iconsplit",label:S("拆分墙")},{id:"delete",icon:"icon-delete",label:S("删除"),disabled:!1,divider:!0}],ye=[{id:"rotate",icon:"icon-rotate",label:S("旋转"),disabled:!1,divider:!1},{id:"ungroupTemplate",icon:"icon-jiezu-2",label:S("解组"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:S("删除"),disabled:!1,divider:!0}],ge=[{id:"material",label:S("清除布局"),icon:"icon-shanchubuju",EventName:"ClearLayout"}].concat(F("SingleRoom"!==V._drawing_layer_mode?[{id:"LayoutLock",label:(null===(r=D.y.instance.current_rooms[0])||void 0===r?void 0:r.layoutLock)?S("解锁布局"):S("锁定布局"),icon:(null===(h=D.y.instance.current_rooms[0])||void 0===h?void 0:h.layoutLock)?"icon-jiesuobuju":"icon-suodingbuju",EventName:"clockLayout",onClick:function(){D.y.instance.current_rooms[0].layoutLock=!D.y.instance.current_rooms[0].layoutLock,d.nb.emit(f.U.RoomMaterialsUpdated,!0),d.nb.instance.update()}}]:[]),F("SingleRoom"!==V._drawing_layer_mode?[{id:"Matching",label:(null===(v=D.y.instance.current_rooms[0])||void 0===v?void 0:v.locked)?S("解锁风格"):S("锁定风格"),icon:(null===(b=D.y.instance.current_rooms[0])||void 0===b?void 0:b.locked)?"icon-jiesuofengge":"icon-suodingfengge",EventName:"clockStyle",onClick:function(){D.y.instance.current_rooms[0].locked=!D.y.instance.current_rooms[0].locked,d.nb.emit(f.U.RoomMaterialsUpdated,!0),d.nb.instance.update()}}]:[]),[{id:"clearSeries",label:S("清除风格"),icon:"icon-qingchufengge",onClick:function(){D.y.instance.deleteSeriesSample()}},{id:"searchMaterial",label:S("查看素材"),icon:"icon-sucai",onClick:function(){d.nb.emit(p.$.showPopup,"searchMaterial")}},j.homeStore.isSingleRoom||"2D"!==j.homeStore.viewMode?null:{id:"focusSpace",label:S("专注空间"),icon:"icon-zhuanzhukongjian",EventName:"SingleRoomLayout",onClick:function(){d.nb.DispatchEvent(d.n0.SingleRoomLayout,j.homeStore.selectEntity),j.homeStore.setIsSingleRoom(!0)}},j.homeStore.isSingleRoom&&"2D"===j.homeStore.viewMode?{id:"exit",label:"",icon:"icon-a-tianchongFace-1",onClick:function(){d.nb.DispatchEvent(d.n0.leaveSingleRoomLayout,{}),j.homeStore.setIsSingleRoom(!1)}}:null]);ge=ge.filter((function(e){return null!==e})),(0,u.useEffect)((function(){d.nb.on_M(f.U.SelectingTarget,"StatusBar",(function(e){var n,t=e||null;j.homeStore.setSelectEntity(e),e||j.homeStore.setShowReplace(!1);var i=(null==t?void 0:t.type)||null,o=(null==e||null===(n=e.ex_prop)||void 0===n?void 0:n.label)||null;if("Furniture"===i){var a=[];a=F(fe),(null==t?void 0:t.matched_rect)&&a.splice(a.length-1,0,{id:"replace",icon:"icon-change_logo",label:S("替换"),disabled:!1,divider:!1}),$(a)}else if("Group"===i){var r;r=fe.concat([{id:"replace",icon:"icon-change_logo ",label:S("替换"),disabled:!1,divider:!1}]),$(r)}else if("BaseGroup"===i){var l=[];l=F(ye),(null==t?void 0:t.matched_rect)&&l.splice(l.length-1,0,{id:"replace",icon:"icon-change_logo",label:S("替换"),disabled:!1,divider:!1}),$(ye)}else if("Door"===i||"Window"===i){if("baywindow"===o)return void $(ve);$(pe)}else"Wall"===i?$(be):"StructureEntity"===i&&$([{id:"delete",icon:"icon-delete",label:S("删除"),disabled:!1,divider:!0}])})),$e()}),[]),(0,u.useEffect)((function(){"3D_FirstPerson"===j.homeStore.viewMode?U(me):U(he)}),[j.homeStore.viewMode]),(0,u.useEffect)((function(){var e,n,t;if("3D_FirstPerson"===j.homeStore.viewMode&&"Furniture"!==(null===(e=j.homeStore.selectEntity)||void 0===e?void 0:e.type))return d.nb.emit(p.$.showPopup,""),void we(!0);if("3D_FirstPerson"===j.homeStore.viewMode&&"Furniture"===(null===(n=j.homeStore.selectEntity)||void 0===n?void 0:n.type))return d.nb.emit(p.$.showPopup,"replace"),void we(!1);var i=(null===(t=j.homeStore.selectEntity)||void 0===t?void 0:t.type)||null;je(i)}),[null===(g=j.homeStore.selectEntity)||void 0===g?void 0:g.type]);var xe=T((0,u.useState)(!0),2),Se=xe[0],we=xe[1],je=function(e){we(!1),setTimeout((function(){H(e||"null"),we(!0)}),300)},_e=window.innerWidth,Ce=window.innerHeight,Ae=T((0,u.useState)({top:Ce-80,left:_e/2}),2),De=Ae[0],Ne=Ae[1],Ie=T((0,u.useState)(!1),2),Me=Ie[0],Ee=Ie[1],ke=(0,u.useRef)(null),Le=(0,u.useRef)({top:0,left:0}),Re=(0,u.useRef)({x:0,y:0}),ze=T((0,u.useState)(!1),2),Te=ze[0],Fe=ze[1],Ue=function(e){Ee(!0),Le.current={top:De.top,left:De.left},Re.current={x:e.touches[0].clientX,y:e.touches[0].clientY}},Oe=function(e,n,t){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];Ne({top:e,left:n}),oe(t),Fe(i)},Pe=function(e){if(e.preventDefault(),Me){var n=e.touches[0],t=n.clientY,i=n.clientX,o=d.nb.instance._is_landscape?100:150;i<o&&t>o&&t<Ce-o||i>_e-o&&t>o&&t<Ce-o?Oe(t-185,i,!1,!0):t<o&&i>o&&i<_e-o||t>Ce-o&&i>o&&i<_e-o?Oe(t,i,!1,!1):Oe(t,i,!0),le(!0)}},Be=function(e){if(re){var n=e.changedTouches[0],t=n.clientX,i=n.clientY,o=_e/2,a=Ce/2,r=d.nb.instance._is_landscape?100:150;t<r&&i<r?Oe(20,50,!0):t>_e-r&&i<r?Oe(20,_e-50,!0):t<r&&i>Ce-r?Oe(Ce-80,50,!0):t>_e-r&&i>Ce-r?Oe(Ce-80,_e-50,!0):x({x:t,y:i},{x:0,y:0},{x:_e,y:0},{x:o,y:a})?Oe(30,_e/2,!1):x({x:t,y:i},{x:0,y:Ce},{x:_e,y:Ce},{x:o,y:a})?Oe(Ce-80,_e/2,!1):x({x:t,y:i},{x:0,y:0},{x:0,y:Ce},{x:o,y:a})?Oe((Ce-400)/2,50,!1,!0):x({x:t,y:i},{x:_e,y:0},{x:_e,y:Ce},{x:o,y:a})?Oe((Ce-400)/2,_e-50,!1,!0):Oe(Ce-80,_e/2,!1,!1),le(!1)}},He=function(){return{position:"fixed",top:De.top,left:De.left,maxWidth:ie?"64px":Te?"68px":"550px",maxHeight:ie?"64px":Te?"550px":"64px",minWidth:"64px",minHeight:"64px",background:"#FFFFFF",boxShadow:"0px 6px 20px 0px #0000001E",cursor:"grab",transition:"".concat(Te?"max-height":"max-width"," 0.5s, opacity 0.3s, borderRadius 0.5s"),overflow:"hidden",transformOrigin:"center",padding:"".concat(Te?"0px 8px":"12px 0 8px 0px"),flexDirection:Te?"column":"row"}},Ve=function(){return(0,i.jsx)("div",{style:{borderTopLeftRadius:"50%",width:Te?64:24,height:Te?24:64,backgroundColor:"#fff"}})},We=function(){return ie&&(0,i.jsx)(N.A,{type:"icon-a-TypegongjuStateDefault",style:{fontSize:"31px",color:"#282828",margin:"-3px 14px 0px 17px"},onClick:Ze})},Ze=function(){Oe(Ce-80,_e/2,!1,!1)};(0,u.useEffect)((function(){var e=ke.current;return e&&e.addEventListener("touchend",Be),function(){e&&e.removeEventListener("touchend",Be)}}),[Me,re]),(0,u.useEffect)((function(){Ze()}),[j.homeStore.IsLandscape]),(0,u.useEffect)((function(){4===j.homeStore.zIndexOf3DViewer&&Xe()}),[j.homeStore.zIndexOf3DViewer]);var $e=function(){var e=z((function(){var e,n;return O(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,fetch("https://3vj-render.3vjia.com/config/3d/aidraw.json")];case 1:return[4,t.sent().json()];case 2:return e=t.sent(),ue(e),k.log("获取到配置数据:",ce),[3,4];case 3:return n=t.sent(),k.error("获取配置失败:",n),[3,4];case 4:return[2]}}))}));return function(){return e.apply(this,arguments)}}(),Ge=function(){var e=z((function(){var e,n,t,i,o,a,r,l,c;return O(this,(function(u){switch(u.label){case 0:return i=d.nb.instance.layout_container,o=document.createElement("canvas"),a=d.nb.instance.scene3D.renderer.domElement,o.width=a.width,o.height=a.height,o.getContext("2d").drawImage(a,0,0,a.width,a.height),[4,(0,M.nA)(o.toDataURL())];case 1:return r=u.sent(),l=null===(e=ce.rooms)||void 0===e?void 0:e.find((function(e){var n,t,i,o;return(null===(t=j.homeStore.guideMapCurrentRoom)||void 0===t||null===(n=t.name)||void 0===n?void 0:n.includes(e.text))||(null===(o=e.text)||void 0===o?void 0:o.includes((null===(i=j.homeStore.guideMapCurrentRoom)||void 0===i?void 0:i.name)||""))})),c={schemeId:i._layout_scheme_id,diffuseImage:r,imageNum:1,stylizationImage:"",imageWidth:1920,imageHeight:1080,inspiration:(null==l?void 0:l.inspire)||(null===(n=j.homeStore.guideMapCurrentRoom)||void 0===n?void 0:n.name),stylized:"",rooms:(null==l?void 0:l.value)||(null===(t=j.homeStore.guideMapCurrentRoom)||void 0===t?void 0:t.name),designStyle:"modern",layoutId:i.hxId,layoutName:i._layout_scheme_name,aiModel:0===j.homeStore.guideMapCurrentRoom.furnitureList.length?3:0},[4,E.w.instance.aiDrawImage(c)];case 2:return u.sent().success?s.A.success(S("提交成功")):s.A.error(S("提交失败")),[2]}}))}));return function(){return e.apply(this,arguments)}}(),Xe=function(){switch(B){case"null":return(0,i.jsxs)("div",{ref:ke,onTouchStart:Ue,onTouchMove:Pe,style:He(),className:"".concat(w.root," ").concat(w.show),children:[!Te&&!ie&&(0,i.jsx)("div",{className:"topLine"}),We(),Ve(),(0,i.jsx)(i.Fragment,{children:R.map((function(e,n){return(0,i.jsxs)("div",{style:{margin:Te?"8px 0":"0 8px"},className:w.btnInfo,onClick:z((function(){return O(this,(function(n){switch(n.label){case 0:return"aidraw"!==e.id?[3,1]:(0==V._room_entities.length?s.A.warning(S("请先创建方案")):(d.nb.DispatchEvent(d.n0.autoSave,null),setTimeout((function(){de("/aidraw?from=mobilehome")}),500)),[3,6]);case 1:return"similar"!==e.id?[3,2]:(j.homeStore.setShowDreamerPopup(!0),[3,6]);case 2:return"create"!==e.id?[3,3]:(d.nb.emit(f.U.OpenHouseSearching,!0),[2]);case 3:return"submitDrawing"!==e.id?[3,5]:V._layout_scheme_id?[4,Ge()]:(d.nb.DispatchEvent(d.n0.autoSave,null),setTimeout(z((function(){return O(this,(function(e){switch(e.label){case 0:return[4,Ge()];case 1:return e.sent(),[2]}}))})),2e3),[2]);case 4:return n.sent(),[2];case 5:if("atlas"===e.id)return 0==V._room_entities.length?s.A.warning(S("请先创建方案")):(d.nb.DispatchEvent(d.n0.autoSave,null),j.homeStore.setShowAtlas(!0)),[2];d.nb.emit(p.$.showPopup,e.id),Y(Math.floor(1e4*Math.random())),n.label=6;case 6:return[2]}}))})),children:[(0,i.jsx)("div",{children:(0,i.jsx)(N.A,{type:e.icon,style:{fontSize:"20px",color:"#282828"}})}),(0,i.jsx)("div",{children:e.label})]},n)}))}),Ve()]});case"Furniture":case"BaseGroup":case"Door":case"Window":return(0,i.jsxs)("div",{ref:ke,onTouchStart:Ue,onTouchMove:Pe,style:He(),className:"".concat(w.root," ").concat(Se?w.show:w.hide),children:[!Te&&!ie&&(0,i.jsx)("div",{className:"topLine"}),Ve(),We(),"                    ",Z.map((function(e,n){return(0,i.jsxs)("div",{className:w.btnInfo,onClick:function(){switch(e.id){case"rotate":d.nb.RunCommand(d._I.RotateFurniture);break;case"flip":d.nb.RunCommand(d._I.FlipFurniture);break;case"flip_vertical":d.nb.RunCommand(d._I.FlipFurnitureVertical);break;case"delete":d.nb.RunCommand(d._I.DeleteFurniture);break;case"deleteRuler":d.nb.RunCommand(d._I.DeleteRuler);break;case"copy":d.nb.RunCommand(d._I.CopyFurniture);break;case"combination":ne(!0);break;case"ungroupTemplate":d.nb.DispatchEvent(d.n0.HandleUnGroupTemplate,{}),j.homeStore.setKey(Date.now());break;case"replace":d.nb.emit(p.$.showPopup,e.id),we(!1);break;case"size":j.homeStore.setSizeInfo({type:"size",visible:!0});break;case"pos_z":j.homeStore.setSizeInfo({type:"pos_z",visible:!0})}},children:[e.divider&&(0,i.jsx)("div",{className:"divider"}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{children:(0,i.jsx)(N.A,{type:e.icon,style:{fontSize:"20px",color:"#282828"}})}),(0,i.jsx)("div",{children:e.label})]})]},n)})),(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{children:(0,i.jsx)(N.A,{type:"icon-a-tianchongFace-1",onClick:function(){d.nb.DispatchEvent(d.n0.cleanSelect,null)},style:{fontSize:"20px",color:"#BCBEC2"}})})}),Ve()]});case"RoomArea":return(0,i.jsxs)("div",{ref:ke,onTouchStart:Ue,onTouchMove:Pe,className:"".concat(w.root," ").concat(Se?w.show:w.hide),style:He(),children:[!Te&&!ie&&(0,i.jsx)("div",{className:"topLine"}),We(),Ve(),(0,i.jsx)(i.Fragment,{children:ge.map((function(e,n){return(0,i.jsxs)("div",{className:w.btnInfo,style:{margin:Te?"4px 0":"0 8px"},onClick:function(){e.onClick?(e.onClick(),Y(Math.floor(1e4*Math.random()))):(null==e?void 0:e.EventName)&&(Y(Math.floor(1e4*Math.random())),d.nb.DispatchEvent(null==e?void 0:e.EventName,j.homeStore.selectEntity))},children:[(0,i.jsx)("div",{children:(0,i.jsx)(A.A,{iconClass:e.icon,style:{fontSize:"20px",color:"#282828"}})}),(0,i.jsx)("div",{children:e.label})]},n)}))}),Ve()]});default:return null}};return(0,i.jsxs)("div",{className:"statusBar",children:[Xe(),(0,i.jsxs)(l.A,{title:S("创建组合"),maskClosable:!1,centered:!0,open:ee,footer:!1,width:300,onCancel:function(){ne(!1)},children:[(0,i.jsx)(C.A,{placeholder:S("请选择组合类型"),style:{width:240,margin:"10 auto"},onChange:function(e){J(e)},options:[{value:"沙发组合",label:S("沙发组合")},{value:"餐桌椅组合",label:S("餐桌椅组合")},{value:"床具组合",label:S("床具组合")},{value:"桌几组合",label:S("桌几组合")},{value:"岛台组合",label:S("岛台组合")},{value:"榻榻米组合",label:S("榻榻米组合")}]}),(0,i.jsx)("div",{style:{textAlign:"center",marginTop:"10px"},children:(0,i.jsx)(c.A,{onClick:function(){ne(!1),d.nb.DispatchEvent(d.n0.CreateCombination,K),s.A.success(S("组合创建成功"))},type:"primary",children:S("确定")})})]})]},X)})),B=t(7474),H=t(83813),V=t(25076),W=t(70766);function Z(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}function $(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,a=[],r=!0,l=!1;try{for(t=t.call(e);!(r=(i=t.next()).done)&&(a.push(i.value),!n||a.length!==n);r=!0);}catch(e){l=!0,o=e}finally{try{r||null==t.return||t.return()}finally{if(l)throw o}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return Z(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Z(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var G=(0,r.observer)((function(e){var n=(0,o.B)().t,t=(0,B.A)().styles,a=(0,m.P)(),r=$((0,u.useState)(0),2),l=r[0],s=r[1],c=$((0,u.useState)(0),2),h=c[0],p=c[1],v=$((0,u.useState)(0),2),b=v[0],y=v[1],g=$((0,u.useState)(0),2),x=g[0],S=g[1],w=$((0,u.useState)(null),2),j=w[0],_=w[1],C=$((0,u.useState)(!1),2),A=C[0],D=C[1];(0,u.useEffect)((function(){d.nb.instance&&d.nb.on(f.U.AttributeHandle,(function(e){var n,t,i,o,r,l,c,u;"init"===e.mode&&(p(Math.round(null==e||null===(t=e.properties)||void 0===t||null===(n=t.width)||void 0===n?void 0:n.defaultValue)),s(Math.round(null==e||null===(o=e.properties)||void 0===o||null===(i=o.length)||void 0===i?void 0:i.defaultValue)),y(Math.round(null==e||null===(l=e.properties)||void 0===l||null===(r=l.height)||void 0===r?void 0:r.defaultValue)),S(Math.round(null==e||null===(u=e.properties)||void 0===u||null===(c=u.pos_z)||void 0===c?void 0:c.defaultValue)),_(null==e?void 0:e.properties),a.homeStore.setAttribute(e))}))}),[]);var I=(0,u.useCallback)((function(e,n){return function(t){var i;if(e(t),null==j||null===(i=j[n])||void 0===i||i.onChange(t),A){var o,a,r=1,c=1,u=1;if("length"===n)r=t/l,p(Math.round(h*r)),y(Math.round(b*r)),null==j||null===(o=j.width)||void 0===o||o.onChange(h*r),null==j||null===(a=j.height)||void 0===a||a.onChange(b*r);else if("width"===n){var d,m;c=t/h,s(Math.round(l*c)),y(Math.round(b*c)),null==j||null===(d=j.length)||void 0===d||d.onChange(l*c),null==j||null===(m=j.height)||void 0===m||m.onChange(b*c)}else if("height"===n){var f,v;u=t/b,s(Math.round(l*u)),p(Math.round(h*u)),null==j||null===(f=j.length)||void 0===f||f.onChange(l*u),null==j||null===(v=j.width)||void 0===v||v.onChange(h*u)}}}}),[j,A,l,h,b]);return(0,i.jsx)("div",{className:"".concat(t.root," ").concat(a.homeStore.sizeInfo.visible?"show":""," ").concat(1==e.mobileType?"alignOnLeft":""),onClick:function(){a.homeStore.setSizeInfo({type:"",visible:!1})},children:(0,i.jsx)("div",{className:t.container,onClick:function(e){return e.stopPropagation()},children:"size"===a.homeStore.sizeInfo.type?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.title,children:[(0,i.jsxs)("div",{children:[n("尺寸调整"),a.homeStore.IsLandscape&&(0,i.jsx)(W.A,{style:{marginLeft:20},checked:A,onChange:function(e){D(e.target.checked)},children:a.homeStore.IsLandscape?n("等比缩放"):""})]}),!a.homeStore.IsLandscape&&(0,i.jsx)("button",{className:t.resetBtn,onClick:function(){D(!A)},children:(0,i.jsx)(N.A,{type:"icon-suoding1"})}),(0,i.jsx)("button",{className:t.resetBtn,onClick:function(){d.nb.DispatchEvent(d.n0.ResetSize,null)},children:a.homeStore.IsLandscape?n("恢复默认"):(0,i.jsx)(N.A,{type:"icon-reset"})})]}),a.homeStore.IsLandscape?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("宽度"),(0,i.jsx)(H.A,{className:t.slider,value:l,onChange:I(s,"length"),min:0,max:6e3,step:1}),(0,i.jsx)(V.A,{className:t.input,value:l,suffix:"mm",onChange:function(e){return I(s,"length")(Number(e.target.value))}})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("深度"),(0,i.jsx)(H.A,{className:t.slider,value:h,onChange:I(p,"width"),min:0,max:6e3,step:1}),(0,i.jsx)(V.A,{className:t.input,value:h,suffix:"mm",onChange:function(e){return I(p,"width")(Number(e.target.value))}})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("高度"),(0,i.jsx)(H.A,{className:t.slider,value:b,onChange:I(y,"height"),min:0,max:2800,step:1}),(0,i.jsx)(V.A,{className:t.input,value:b,suffix:"mm",onChange:function(e){return I(y,"height")(Number(e.target.value))}})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("投影面积(宽*高)"),(0,i.jsxs)("label",{className:t.input,style:{textAlign:"right"},children:[(l*b/1e3/1e3).toFixed(2),"m²"]})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("宽度"),(0,i.jsx)(V.A,{className:t.input,value:l,suffix:"mm",onChange:function(e){return I(s,"length")(Number(e.target.value))}})]}),(0,i.jsx)(H.A,{className:t.slider,value:l,onChange:I(s,"length"),min:0,max:6e3,step:1}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("深度"),(0,i.jsx)(V.A,{className:t.input,value:h,suffix:"mm",onChange:function(e){return I(p,"width")(Number(e.target.value))}})]}),(0,i.jsx)(H.A,{className:t.slider,value:h,onChange:I(p,"width"),min:0,max:6e3,step:1}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("高度"),(0,i.jsx)(V.A,{className:t.input,value:b,suffix:"mm",onChange:function(e){return I(y,"height")(Number(e.target.value))}})]}),(0,i.jsx)(H.A,{className:t.slider,value:b,onChange:I(y,"height"),min:0,max:2800,step:1}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("投影面积(宽*高)"),(0,i.jsxs)("label",{className:t.input,children:[(l*b/1e3/1e3).toFixed(2),"m²"]})]})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:t.title,children:[(0,i.jsx)("div",{children:n("离地")}),(0,i.jsx)("button",{className:t.resetBtn,onClick:function(){d.nb.DispatchEvent(d.n0.ResetSize,null)},children:a.homeStore.IsLandscape?n("恢复默认"):(0,i.jsx)(N.A,{type:"icon-reset"})})]}),(0,i.jsxs)("div",{className:t.sliderContainer,children:[n("离地"),a.homeStore.IsLandscape&&(0,i.jsx)(H.A,{className:t.slider,value:x,onChange:I(S,"pos_z"),min:0,max:2800,step:1}),(0,i.jsx)(V.A,{className:t.input,value:x,suffix:"mm",onChange:function(e){return I(S,"pos_z")(Number(e.target.value))}})]}),!a.homeStore.IsLandscape&&(0,i.jsx)(H.A,{className:t.slider,value:x,onChange:I(S,"pos_z"),min:0,max:2800,step:1})]})})})})),X=t(84872),Y=t(58567),q=t(51010),K=t(20995),J=t(56697),Q=t(42751),ee=t(49816),ne=t(23184),te=t(90112),ie=t(73062),oe=t(75670),ae=t(99030),re=t(17365),le=t(32184),se=t(65640);function ce(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}function ue(e,n,t,i,o,a,r){try{var l=e[a](r),s=l.value}catch(e){return void t(e)}l.done?n(s):Promise.resolve(s).then(i,o)}function de(e){return function(){var n=this,t=arguments;return new Promise((function(i,o){var a=e.apply(n,t);function r(e){ue(a,i,o,r,l,"next",e)}function l(e){ue(a,i,o,r,l,"throw",e)}r(void 0)}))}}function he(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,a=[],r=!0,l=!1;try{for(t=t.call(e);!(r=(i=t.next()).done)&&(a.push(i.value),!n||a.length!==n);r=!0);}catch(e){l=!0,o=e}finally{try{r||null==t.return||t.return()}finally{if(l)throw o}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return ce(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ce(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function me(e,n){var t,i,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},r=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return r.next=l(0),r.throw=l(1),r.return=l(2),"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;r&&(r=0,l[0]&&(a=0)),a;)try{if(t=1,i&&(o=2&l[0]?i.return:l[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,l[1])).done)return o;switch(i=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,i=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){a.label=l[1];break}if(6===l[0]&&a.label<o[1]){a.label=o[1],o=l;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(l);break}o[2]&&a.ops.pop(),a.trys.pop();continue}l=n.call(e,a)}catch(e){l=[6,e],i=0}finally{t=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var fe=(0,r.observer)((function(){var e=(0,o.B)().t,n=(0,a.A)().styles,t=he((0,u.useState)(""),2),r=t[0],_=t[1],C=he((0,u.useState)(-2),2),A=C[0],D=C[1],N=(0,m.P)(),I=l.A.confirm,M="SaveSchemeProgress",E=he(s.A.useMessage(),2),k=E[0],L=E[1],R=he((0,u.useState)(!1),2),z=R[0],T=(R[1],he((0,u.useState)(!1),2)),F=T[0],U=T[1],O=he((0,u.useState)(null),2),B=O[0],H=O[1],V=he((0,u.useState)(""),2),W=V[0],Z=V[1],$=he((0,u.useState)(-2),2),ce=($[0],$[1]),ue=(0,u.useRef)();d.nb.UseApp(h.e.AppName),d.nb.instance&&(d.nb.t=e);var fe=function(){d.nb.instance&&(d.nb.instance.bindCanvas(document.getElementById("cad_canvas")),d.nb.instance.update()),pe()},pe=function(){d.nb.instance&&(d.nb.instance._is_landscape=window.innerWidth<window.innerHeight),N.homeStore.setIsLandscape(window.innerWidth<window.innerHeight),document.documentElement.style.setProperty("--vh","".concat(.01*window.innerHeight,"px"))},ve=function(){var e=de((function(){var e,n,t;return me(this,(function(i){switch(i.label){case 0:return y.uN?(e={isDelete:0,pageIndex:1,pageSize:9,keyword:y.uN},[4,X.D.getLayoutSchemeList(e)]):[2];case 1:return n=i.sent(),t=n.layoutSchemeDataList,n.total,t&&(d.nb.DispatchEvent(d.n0.OpenMyLayoutSchemeData,t[0]),d.nb.emit(f.U.OpenHouseSearching,!1)),[2]}}))}));return function(){return e.apply(this,arguments)}}(),be=function(){var n=de((function(){var n,t,i,o;return me(this,(function(a){switch(a.label){case 0:return"HouseId"!==y.Zx?[3,1]:(de((function(){var e,n,t;return me(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,(0,Y.ZN)("HouseId")];case 1:return e=i.sent(),se.log(e),n=e.data,d.nb.DispatchEvent(d.n0.PostBuildingId,{id:n,name:""}),[3,3];case 2:return t=i.sent(),se.error("Error loading file:",t),[3,3];case 3:return[2]}}))}))(),[3,10]);case 1:return"DwgBase64"!==y.Zx?[3,2]:(de((function(){return me(this,(function(e){try{d.nb.RunCommand(d._I.OpenDwgFilefromWork)}catch(e){se.error("Error loading file:",e)}return[2]}))}))(),[3,10]);case 2:return"CopyingBase64"!==y.Zx?[3,3]:(de((function(){var n,t,i;return me(this,(function(o){switch(o.label){case 0:return o.trys.push([0,2,,3]),[4,(0,Y.ZN)("CopyingBase64")];case 1:return n=o.sent(),t=n.data,N.homeStore.setImgBase64(t),(0,q.e)(e,N),[3,3];case 2:return i=o.sent(),se.error("Error loading file:",i),[3,3];case 3:return[2]}}))}))(),[3,10]);case 3:return"hxcreate"!==y.Zx?[3,6]:y.fW?[4,(0,K.ON)({id:y.fW})]:[3,5];case 4:(n=a.sent()).result.contentUrl=n.result.dataUrl,d.nb.DispatchEvent(d.n0.OpenMyLayoutSchemeData,n.result),d.nb.DispatchEvent(d.n0.autoSave,null),a.label=5;case 5:return[3,10];case 6:return"hxedit"!==y.Zx?[3,9]:y.vu?[4,(0,K.ON)({id:y.vu})]:[3,8];case 7:(t=a.sent()).success&&t.result&&t.result.dataUrl&&(t.result.contentUrl=t.result.dataUrl,d.nb.DispatchEvent(d.n0.OpenMyLayoutSchemeData,t.result)),a.label=8;case 8:return d.nb.instance&&"SingleRoom"==(null===(o=d.nb.instance)||void 0===o||null===(i=o.layout_container)||void 0===i?void 0:i._drawing_layer_mode)&&d.nb.DispatchEvent(d.n0.leaveSingleRoomLayout,{}),d.nb.instance._current_handler_mode=h.f.HouseDesignMode,d.nb.RunCommand(h.f.HouseDesignMode),N.homeStore.setDesignMode(h.f.HouseDesignMode),[3,10];case 9:"schemecreate"===y.Zx&&d.nb.emit(f.U.OpenHouseSearching,!0),a.label=10;case 10:return[2]}}))}));return function(){return n.apply(this,arguments)}}();return(0,u.useEffect)((function(){re.f.updateAliasName(d.nb.instance.layout_container),N.homeStore.setRoomEntites(d.nb.instance.layout_container._room_entities)}),[d.nb.instance.layout_container._room_entities]),(0,u.useEffect)((function(){if(d.nb.instance&&(d.nb.instance._is_website_debug=y.iG),window.addEventListener("resize",fe),fe(),d.nb.instance){var n;if(d.nb.instance.initialized||(d.nb.emit(f.U.OnPreparingHandle,{opening:!0,title:"加载方案中...."}),d.nb.instance.init(),d.nb.RunCommand(h.f.AiCadMode),d.nb.instance.prepare().then((function(){ve(),be();var e=d.nb.instance.scene3D;e&&e.stopRender()})),d.nb.instance.bindCanvas(document.getElementById("cad_canvas")),y.Ic&&d.nb.emit(f.U.OpenHouseSearching,!0)),d.nb.instance.layout_container.drawing_figure_mode=le.qB.Texture,null===(n=window)||void 0===n?void 0:n.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var i="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(i)),d.nb.instance._debug_mode=i)}}d.nb.instance.update()}d.nb.on_M("showLight3DViewer","LightMain",(function(e){e?(D(2),d.nb.emit(ae.r.UpdateScene3D,!1)):D(-1)})),d.nb.on(f.U.ShowDreamerPopup,(function(e){N.homeStore.setShowDreamerPopup(e)})),d.nb.on(f.U.LayoutSchemeOpened,(function(e){_(e.name),d.nb.emit(b.$T,b.Kw.Default)})),d.nb.on(f.U.ClearLayout,(function(){I({title:e("清空布局"),content:e("确定清空单空间布局？"),okText:e("确定"),cancelText:e("取消"),onOk:function(){d.nb.DispatchEvent(d.n0.ClearLayout,this)},onCancel:function(){}})})),d.nb.on(f.U.OpenMySchemeList,(function(){N.homeStore.setShowMySchemeList(!0)})),d.nb.on_M(f.U.RoomList,"room_list",(function(e){N.homeStore.setRoomInfos(e)})),d.nb.on(f.U.Room2SeriesSampleRoom,(function(e){N.homeStore.setRoom2SeriesSampleArray(e)})),d.nb.on(f.U.showCustomKeyboard,(function(e){setTimeout((function(){U(!0)}),50),e.input&&(Z(e.input.value),H(e.input))})),d.nb.on(f.U.SaveProgress,(function(n){"success"===n.progress?k.open({key:M,type:"success",content:e("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"fail"===n.progress?k.open({key:M,type:"error",content:e("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"ongoing"===n.progress&&k.open({key:M,type:"loading",content:e("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})}))}),[]),(0,u.useEffect)((function(){4===N.homeStore.zIndexOf3DViewer&&D(2)}),[N.homeStore.zIndexOf3DViewer]),(0,i.jsxs)("div",{className:n.root,children:[(0,i.jsx)(b.Ay,{}),(0,i.jsxs)("div",{className:n.side_pannel,id:"side_pannel",children:[(0,i.jsx)(p.A,{}),(0,i.jsx)(G,{})]}),(0,i.jsxs)("div",{id:"Canvascontent",className:n.content,children:[(0,i.jsx)("div",{className:"3d_container "+n.canvas3d,style:{zIndex:A},children:(0,i.jsx)(v.A,{defaultViewMode:4})}),(0,i.jsxs)("div",{id:"body_container",className:n.canvas_pannel,children:[(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){N.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){N.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,i=Math.sqrt(n*n+t*t);N.homeStore.setInitialDistance(i/N.homeStore.scale)}},onTouchMove:function(e){if(e.stopPropagation(),2!=e.touches[e.touches.length-1].identifier&&2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,i=Math.sqrt(n*n+t*t)/N.homeStore.initialDistance;i>5?i=5:i<.05&&(i=.05),N.homeStore.setScale(i),d.nb.DispatchEvent(d.n0.scale,i)}},onTouchEnd:function(e){e.touches.length>0&&d.nb.DispatchEvent(d.n0.updateLast_pos,e),N.homeStore.setInitialDistance(null)}}),N.homeStore.designMode===h.f.MeasurScaleMode&&(0,i.jsxs)("div",{className:"canvas_btns",style:{zIndex:999999,marginBottom:"10vh",gap:"20px"},children:[(0,i.jsx)(c.A,{className:"btn",type:"primary",onClick:function(){d.nb.instance&&(d.nb.RunCommand(h.f.AiCadMode),N.homeStore.setDesignMode(h.f.AiCadMode))},children:e("取消")}),(0,i.jsx)(c.A,{className:"btn",type:"primary",onClick:function(){d.nb.instance&&(d.nb.DispatchEvent(d.n0.ConfirmScale,{img_base64:N.homeStore.img_base64}),N.homeStore.setDesignMode(h.f.AiCadMode))},children:e("确定")})]})]})]}),(0,i.jsx)(P,{}),(0,i.jsx)(x.A,{}),(0,i.jsx)(g.ti,{}),(0,i.jsx)(ne.A,{}),(0,i.jsx)(ee.A,{}),(0,i.jsx)(ie.A,{}),N.homeStore.showDreamerPopup&&(0,i.jsx)(j.A,{}),N.homeStore.isSingleRoom&&(0,i.jsx)("div",{className:n.RoomAreaBtns,children:(0,i.jsx)(Q.A,{})}),N.homeStore.showSaveLayoutSchemeDialog.show&&(0,i.jsx)("div",{className:n.overlay,children:(0,i.jsx)(w.A,{schemeName:r||"",closeCb:function(){N.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:""})},isSaveAs:z})}),(0,i.jsx)(S.A,{schemeNameCb:function(e){_(e)}}),(0,i.jsx)(J.A,{onKeyPress:function(e){B&&(B.value=B.value+e,Z(B.value))},onDelete:function(){B&&(B.value=B.value.slice(0,-1),Z(B.value))},onConfirm:function(){B&&(d.nb.DispatchEvent(d.n0.DimensionInput,B),U(!1),Z(""))},onClose:function(){U(!1),Z("")},inputValue:W,isVisible:F}),(0,i.jsx)(te.A,{ref:ue}),(0,i.jsx)(q.A,{}),N.homeStore.showAtlas&&(0,i.jsx)("div",{className:n.mobile_atlas_container,style:{zIndex:999},children:(0,i.jsx)(oe.A,{setZIndexOfMobileAtlas:ce})}),L]})}))}}]);