

import { LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { CadBaseSubHandler } from "./CadBaseSubHandler";
import { I_MouseEvent } from "@layoutai/z_polygon";

import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";



export class DimensionWallHandler extends CadBaseSubHandler {
    _previous_rect: ZRect;
    _existingInput: HTMLInputElement;
    _last_pos: Vector3;
    initialPosition: { x: number, y: number };
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this._cad_mode_handler = cad_mode_handler;
        this.name = LayoutAI_Commands.Transform_Dimension;
        this._previous_rect = null;

        this._last_pos = null;
        this.initialPosition = { x: 0, y: 0 };
    }

    enter(state?: number): void {
        super.enter(state);
        this.manager.resetPointers();
    }
    setInputStyle(input: HTMLInputElement) {
        input.className = "edit_input";
        input.id = "edit_input";
        input.autocomplete = "off";
    }
    updateInputPosition(input: HTMLInputElement, ev: any) {
        input.style.left = ev.clientX -25 + "px";
        input.style.top = ev.clientY - 15  + "px";
        this.initialPosition = { x: ev.clientX, y: ev.clientY };
    }

    getInputEvalValue(str:string)
    {
        // str = str.replace(/[^0-9.+\-*/]/g, '');
        let ans_str = ''; // 不太会正则表达式 就很烦...

        for(let ch of str)
        {
            if('0123456789.+-/*()'.includes(ch))
            {
                ans_str+=ch;
            }
        }

        let val = 0;
        try {
            val = eval(ans_str);
        } catch (error) {
            
        }
        return val;
    }

    // 触发编辑框确认逻辑
    updateEditNum() {
        if (!this.selected_target.selected_transform_element) return;
        if(!this._existingInput) return;

        let value_text = this._existingInput.value;

        let num = this.getInputEvalValue(value_text);
        if (typeof num === 'number') {
            let rect = this.selected_target.selected_transform_element._target_rect;
            let wall = TBaseEntity.getEntityOfRect(rect) as TWall;

            if(!wall)
            {
                return;
            }
            wall.recordInWallWindowsData();
            wall.recordRectData();
            wall.initNeighborsWalls(this.manager.layout_container._wall_entities);

            let _nor_val = this.selected_target.selected_transform_element._nor_v3;
            
            // 编辑前的值
            let origin_num = this.selected_target.selected_transform_element._edit_num;
            // num是编辑的后的值
            // edit_num是差值
            let edit_num: any = origin_num - num;
            // if(edit_num < 0) return;
            let t_center = { x: rect.rect_center.x + _nor_val.x * edit_num, y: rect.rect_center.y + _nor_val.y * edit_num, z: rect.rect_center.z };
            rect.rect_center = t_center;
            wall.updateInWallWindows();
            wall.updateNeighborWallsAfterMoving();
            wall.cleanNeighborWalls();
            if(this.manager.layout_container)
            {
                this.manager.layout_container._computeRoomPolysByWall();
            }
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info) {
                this.manager.appendOperationInfo(info);
            }

            this._cad_mode_handler._is_moving_element = false;
            this.update();
            this.leaveMode();
            return;
        }

    }

    // 修改长度
    updateLengthEditNum()
    {
        if (!this.selected_target.selected_transform_element) return;
        if(!this._existingInput) return;
        let num = parseFloat(this._existingInput.value);
        if (typeof num === 'number') {
            this.selected_target.selected_rect.length = num;
            this.selected_target.selected_rect.updateRect();
        }
    }
    leaveMode(): void {
        this._existingInput.style.opacity = '0';
        this._existingInput.style.display = 'none';

        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
    }

    onmousedown(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        this.updateSelectedRect(pos);


        if (!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect
            || this.selected_target.selected_transform_element.element_name !== "T_DimensionElement") {
            this.leaveMode();
            return;
        }

        if (this.selected_target && this.selected_target.selected_rect) {
            this._cad_mode_handler._is_moving_element = true;
        }

        else {
            this._cad_mode_handler._is_moving_element = false;
        }

        this._existingInput = document.querySelector(".edit_input");
        if (!this._existingInput) {
            this._existingInput = document.createElement("input") as HTMLInputElement;
            this._existingInput.style.opacity = '1';
            this._existingInput.style.display = 'block';

            this.setInputStyle(this._existingInput);
            document.body.appendChild(this._existingInput);
            // this.body_div_container.addEventListener('wheel',wheel_event,{passive:true});
        } else {
            this.updateInputPosition(this._existingInput, ev._ev);
            this._existingInput.style.opacity = '1';
            this._existingInput.style.display = 'block';
        }

        if(this.selected_target.selected_transform_element)
        {
            this._existingInput.value = Math.round(this.selected_target.selected_transform_element._edit_num).toString();
        }

        setTimeout(() => this._existingInput.focus(), 1);
        // 写触发编辑框逻辑，填入input框

        this._previous_rect = this.selected_target.selected_transform_element._target_rect.clone();
        this.selected_target.selected_transform_element.recordOriginRect(this._previous_rect);
        this._last_pos = new Vector3(ev.posX, ev.posY, 0);
    }

    onmousemove(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.buttons == 1)
        {

            let transform_element = this.selected_target.selected_transform_element;
            if (!transform_element) return;
            let current_pos = new Vector3(ev.posX, ev.posY, 0);
    
            let movement = current_pos.clone().sub(this._last_pos);
    
            transform_element.applyTransformByMovement(movement,this.exsorb_rects);
            this.updateTransformElements();
    
            this.update();
        }
        if (ev.buttons == 0) {
            this.updateHoverRect(pos);
        }

    }

    onmouseup(ev: I_MouseEvent): void {
        if (this.selected_target.selected_transform_element) {
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info) {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
        }
        this.updateCandidateRects();
    }

    onwheel(ev: WheelEvent): void {

        // 
    }

    onkeydown(ev: KeyboardEvent) {
        if(ev.key == "Enter")
        {
            if(!this.selected_target.selected_transform_element._is_update_length)
            {
                this.updateEditNum();
            } else 
            {
                this.updateLengthEditNum();
            }
           this.leaveMode();  
        }
        return true
    }
}