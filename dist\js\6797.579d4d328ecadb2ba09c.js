"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[6797],{66797:function(e,n,t){t.r(n);var s=t(13274),a=t(41594),i=t(33100),r=t(25076),c=t(36134),o=t(41185),l=t(27347),u=t(88934),h=t(98612),d=t(49063),f=t(9003),m=t(15696),v=t(85783),p=t(23825),b=t(64841),y=t(41980),g=t(78644),j=t(61214),x=t(16238),S=t(65640);function w(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,s=new Array(n);t<n;t++)s[t]=e[t];return s}function _(e,n,t,s,a,i,r){try{var c=e[i](r),o=c.value}catch(e){return void t(e)}c.done?n(o):Promise.resolve(o).then(s,a)}function N(e){return function(){var n=this,t=arguments;return new Promise((function(s,a){var i=e.apply(n,t);function r(e){_(i,s,a,r,c,"next",e)}function c(e){_(i,s,a,r,c,"throw",e)}r(void 0)}))}}function k(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var s,a,i=[],r=!0,c=!1;try{for(t=t.call(e);!(r=(s=t.next()).done)&&(i.push(s.value),!n||i.length!==n);r=!0);}catch(e){c=!0,a=e}finally{try{r||null==t.return||t.return()}finally{if(c)throw a}}return i}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return w(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return w(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(e,n){var t,s,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},r=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return r.next=c(0),r.throw=c(1),r.return=c(2),"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function c(c){return function(o){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;r&&(r=0,c[0]&&(i=0)),i;)try{if(t=1,s&&(a=2&c[0]?s.return:c[0]?s.throw||((a=s.return)&&a.call(s),0):s.next)&&!(a=a.call(s,c[1])).done)return a;switch(s=0,a&&(c=[2&c[0],a.value]),c[0]){case 0:case 1:a=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,s=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!a||c[1]>a[0]&&c[1]<a[3])){i.label=c[1];break}if(6===c[0]&&i.label<a[1]){i.label=a[1],a=c;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(c);break}a[2]&&i.ops.pop(),i.trys.pop();continue}c=n.call(e,i)}catch(e){c=[6,e],s=0}finally{t=a=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,o])}}}n.default=(0,m.observer)((function(){var e,n,t,m,w=(0,v.B)().t,_=(0,o.A)().styles,C=k(i.A.useMessage(),2),I=C[0],D=C[1],M=k((0,a.useState)(!1),2),E=(M[0],M[1]),P=k((0,a.useState)(!1),2),U=P[0],B=P[1],L=k((0,a.useState)(null),2),T=L[0],F=L[1],O=k((0,a.useState)(!1),2),z=O[0],H=O[1],Y=k((0,a.useState)(!1),2),R=Y[0],X=Y[1],Z=k((0,a.useState)(null),2),G=Z[0],q=Z[1],V=k((0,a.useState)(""),2),W=V[0],$=V[1],J=k((0,a.useState)(!0),2),K=J[0],Q=J[1],ee=k((0,a.useState)(!1),2),ne=ee[0],te=(ee[1],k((0,a.useState)(!1),2)),se=te[0];te[1],(0,d.Zp)();l.nb.UseApp(h.e.AppName),l.nb.instance&&(l.nb.instance._is_landscape=z,l.nb.t=w);var ae=(0,a.useRef)(null),ie=(0,f.P)();l.nb.instance&&(l.nb.instance._is_website_debug=p.iG);var re=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("cad_canvas")),l.nb.instance.update()),l.nb.instance&&(l.nb.instance._is_landscape=z),H(!1)},ce=function(){var e=N((function(){var e,n,t,s,a,i;return A(this,(function(r){switch(r.label){case 0:Q(!0),r.label=1;case 1:return r.trys.push([1,,3,4]),e=window.location.href,n=/[?&]linkId=([^&#]*)/,t=e.match(n),s=t?t[1]:null,[4,(0,b.dV)({id:s})];case 2:if(a=r.sent(),a.result.shareLink,a.success&&a.result){if(F(a.result),a.result.expireDate&&(i=new Date(a.result.expireDate).getTime(),(new Date).getTime()>i))return X(!0),[2];if(1==a.result.isCancel)return X(!0),[2];a.result.linkPwd?B(!0):(l.nb.DispatchEvent(l.n0.OpenMyLayoutSchemeData,a.result.layoutScheme),l.nb.emit(u.U.OpenHouseSearching,!1))}return[3,4];case 3:return Q(!1),[7];case 4:return[2]}}))}));return function(){return e.apply(this,arguments)}}(),oe=function(){var e=N((function(e,n){var t;return A(this,(function(e){switch(e.label){case 0:return"createCopy"!==n.id?[3,2]:[4,(0,b.MY)({id:T.id,shareLink:T.shareLink})];case 1:(t=e.sent()).success?(I.success(w("创建副本成功")),window.open(p.$N+"?schemeId=".concat(t.result),"_blank")):I.error(w("创建副本失败")),e.label=2;case 2:return[2]}}))}));return function(n,t){return e.apply(this,arguments)}}();return(0,a.useEffect)((function(){window.addEventListener("resize",re),re(),l.nb.instance&&(l.nb.instance.initialized||(l.nb.instance.init(),l.nb.RunCommand(h.f.AiCadMode),l.nb.instance.prepare().then((function(){ce(),q(l.nb.instance.layout_container._layout_scheme_name),ie.homeStore.setAppOpenAsCadPlugin(!0)})),l.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),l.nb.instance.update());l.nb.on(u.U.ShowWallTopMenu,(function(e){ie.homeStore.setIsShowWallTopMenu(e)})),l.nb.on(u.U.setIssueReportVisible,E)}),[]),(0,s.jsxs)("div",{className:_.root+" "+(z?_.landscape:""),children:[!R&&(0,s.jsx)(y.pF,{logo:ie.userStore.aihouse?"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/en_logo.png":"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/logo.png",title:!p.fZ&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{style:{color:"#FFFFFF0F"},children:"|"}),G?" 【"+G+"】":""]}),centerBtnList:[],rightBtnList:["copy"===(null==T?void 0:T.accessPermit)&&{id:"createCopy",title:w("创建副本"),type:"buttonGray"}],onBtnClick:oe}),se&&(0,s.jsx)("div",{className:_.loading,children:(0,s.jsx)(j.A,{spinning:se})}),(0,s.jsxs)("div",{id:"Canvascontent",className:_.content,children:[(0,s.jsx)(g.A,{is3dPreview:ne}),(0,s.jsx)("div",{ref:ae,id:"body_container",className:"".concat(_.canvas_pannel," ").concat((0,p.fZ)()&&_.canvas_pannel_mobile," "),children:(0,s.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){ie.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){ie.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,s=Math.sqrt(n*n+t*t);ie.homeStore.setInitialDistance(s/ie.homeStore.scale)}},onTouchMove:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,s=Math.sqrt(n*n+t*t)/ie.homeStore.initialDistance;s>5?s=5:s<.05&&(s=.05),ie.homeStore.setScale(s),l.nb.DispatchEvent(l.n0.scale,s)}},onTouchEnd:function(){ie.homeStore.setInitialDistance(null)}})})]}),(0,s.jsx)(x.A,{}),U&&(0,s.jsx)(y._w,{center:!0,height:210,width:378,draggable:!0,title:w("查看密码"),hideClose:!0,children:(0,s.jsxs)("div",{className:_.shareBox,children:[(0,s.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,s.jsx)("img",{src:"".concat((null==T?void 0:T.shareUserHeadPic)?null==T?void 0:T.shareUserHeadPic:"https://3vj-render.3vjia.com///vr/layout/def.jpg?x-oss-process=image/resize,m_lfit,w_32/format,webp"),alt:""}),null==T?void 0:T.shareUserNickName,",",w("给您加密了分享方案")]}),(0,s.jsx)("div",{style:{marginBottom:13},children:w("请输入密码：")}),(0,s.jsxs)("div",{children:[(0,s.jsx)(r.A,{onChange:function(e){$(e.target.value)},value:W,style:{width:238,marginRight:8}}),(0,s.jsx)(c.A,{onClick:function(){S.log("linkInfo",T),T.linkPwd==W?(l.nb.DispatchEvent(l.n0.OpenMyLayoutSchemeData,T.layoutScheme),l.nb.emit(u.U.OpenHouseSearching,!1),B(!1)):I.error(w("密码错误"))},type:"primary",children:w("确定")})]})]})}),R&&(0,s.jsx)("div",{className:_.expireEmpty,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/expireDate.png",alt:""}),(0,s.jsx)("div",{className:"first",children:w("链接已失效")}),(0,s.jsx)("div",{className:"second",children:w("该方案链接已失效, 如有疑问请联系该方案管理员")})]})}),!K&&(0,p.fZ)()&&!R&&!U&&(0,s.jsxs)("div",{className:_.mobileBottom,children:[(0,s.jsx)("div",{className:"shcmeName",children:null==T||null===(n=T.layoutScheme)||void 0===n||null===(e=n.layoutSchemeName)||void 0===e?void 0:e.split(" ")[0]}),(0,s.jsxs)("div",{className:"info",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"name",children:w("建筑面积")}),(0,s.jsxs)("div",{children:[null==T||null===(t=T.layoutScheme)||void 0===t?void 0:t.area," m²"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"name",children:w("居室")}),(0,s.jsx)("div",{children:null==T||null===(m=T.layoutScheme.layoutSchemeName)||void 0===m?void 0:m.split(" ")[1]})]})]}),(0,s.jsxs)("div",{className:"shareImage",children:[(0,s.jsx)("img",{src:"".concat((null==T?void 0:T.shareUserHeadPic)?null==T?void 0:T.shareUserHeadPic:"https://3vj-render.3vjia.com///vr/layout/def.jpg?x-oss-process=image/resize,m_lfit,w_32/format,webp"),alt:""}),null==T?void 0:T.shareUserNickName]}),(0,s.jsx)("div",{className:"btnInfo",children:(0,s.jsx)("button",{onClick:function(){navigator.clipboard.writeText(T.shareLink).then((function(){i.A.success(w("复制成功"))})).catch((function(e){S.error("复制失败",e)}))},className:"btn",children:w("复制链接去电脑查看")})})]}),D]})}))}}]);