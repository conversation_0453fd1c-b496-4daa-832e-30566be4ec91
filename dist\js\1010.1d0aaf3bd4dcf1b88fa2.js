"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[1010],{20995:function(t,e,n){n.d(e,{F2:function(){return g},ON:function(){return h},pn:function(){return u}});var i=n(64186);function r(t,e,n,i,r,o,a){try{var c=t[o](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(i,r)}function o(t){return function(){var e=this,n=arguments;return new Promise((function(i,o){var a=t.apply(e,n);function c(t){r(a,i,o,c,s,"next",t)}function s(t){r(a,i,o,c,s,"throw",t)}c(void 0)}))}}function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){a(t,e,n[e])}))}return t}function s(t,e){var n,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(n=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(t,o)}catch(t){c=[6,t],i=0}finally{n=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}function u(t){return l.apply(this,arguments)}function l(){return(l=o((function(t){return s(this,(function(e){switch(e.label){case 0:return[4,(0,i.IW)({method:"post",url:"/sd-biz/api/building/roommodel/v1/scale/point",data:c({},t),timeout:6e4}).catch((function(t){return null}))];case 1:return[2,e.sent()]}}))}))).apply(this,arguments)}function h(t){return f.apply(this,arguments)}function f(){return(f=o((function(t){return s(this,(function(e){switch(e.label){case 0:return[4,(0,i.IW)({method:"post",url:"api/njvr/layoutSchemeHouseType/get",data:c({},t),timeout:6e4}).catch((function(t){return null}))];case 1:return[2,e.sent()]}}))}))).apply(this,arguments)}function g(t){return d.apply(this,arguments)}function d(){return(d=o((function(t){return s(this,(function(e){switch(e.label){case 0:return[4,(0,i.IW)({method:"post",url:"api/njvr/LayoutScheme/listByPage",data:c({},t),timeout:6e4}).catch((function(t){return null}))];case 1:return[2,e.sent()]}}))}))).apply(this,arguments)}},51010:function(t,e,n){n.d(e,{A:function(){return H},e:function(){return _}});var i=n(13274),r=n(98612),o=n(88934),a=n(27347),c=n(9003),s=n(20995),u=n(36134),l=n(41980),h=n(15696),f=n(41594),g=n(85783);function d(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function m(){var t=d([""]);return m=function(){return t},t}function p(){var t=d(["\n        display: flex;\n        flex-direction: column;\n        height: 550px;\n        padding: 16px;\n        "]);return p=function(){return t},t}function x(){var t=d(["\n        display: flex;\n        height: calc(100% - 66px);\n        margin-bottom: 16px;\n    "]);return x=function(){return t},t}function b(){var t=d(["\n        flex: 1;\n        margin-right: 16px;\n        height: 100%;\n    "]);return b=function(){return t},t}function y(){var t=d(["\n        width: 100%;\n        height: 100%;\n        border: 1px solid #f0f0f0;\n        border-radius: 4px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        position: relative;\n        overflow: hidden;\n        background-color: rgba(127, 127, 127, 0.6);\n        cursor: crosshair;\n        user-select: none;\n    "]);return y=function(){return t},t}function v(){var t=d(["\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        background-color: rgba(127, 127, 127, 0.6);\n        transition: transform 0.05s ease-out;\n    "]);return v=function(){return t},t}function w(){var t=d(["\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n        user-select: none;\n        pointer-events: none;\n        -webkit-user-drag: none;\n        background-color: white;\n        transition: transform 0.05s ease-out;\n    "]);return w=function(){return t},t}function M(){var t=d(["\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background-color: rgba(0, 0, 0, 0.5);\n        pointer-events: none;\n    "]);return M=function(){return t},t}function j(){var t=d(["\n        width: 200px;\n        padding: 16px;\n        border: 1px solid #f0f0f0;\n        border-radius: 4px;\n    "]);return j=function(){return t},t}function S(){var t=d(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 16px;\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n    "]);return S=function(){return t},t}function k(){var t=d(["\n        margin-bottom: 16px;\n        \n        .section-header {\n            display: flex;\n            align-items: center;\n            gap: 8px;\n            \n            .section-title {\n            color: #666;\n            min-width: 42px;\n            }\n            \n            .buttons-group {\n            display: flex;\n            gap: 8px;\n            flex: 1;\n            }\n        }\n    "]);return k=function(){return t},t}function z(){var t=d(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        height: 50px;\n    "]);return z=function(){return t},t}function O(){var t=d(["\n        font-size: 14px;\n        color: #999;\n    "]);return O=function(){return t},t}function D(){var t=d(["\n        position: absolute;\n        border: 2px solid #1F6EFF;\n        background: none;\n        cursor: move;\n        z-index: 2;\n        \n        &::after {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background: none;\n            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);\n        }\n        \n        .resize-handle {\n            position: absolute;\n            width: 8px;\n            height: 8px;\n            background-color: #fff;\n            border: 2px solid #1F6EFF;\n            z-index: 4;\n            \n            &.top-left { top: -4px; left: -4px; cursor: nw-resize; }\n            &.top-right { top: -4px; right: -4px; cursor: ne-resize; }\n            &.bottom-left { bottom: -4px; left: -4px; cursor: sw-resize; }\n            &.bottom-right { bottom: -4px; right: -4px; cursor: se-resize; }\n            \n            &.top { top: -4px; left: 50%; margin-left: -4px; cursor: n-resize; }\n            &.right { top: 50%; right: -4px; margin-top: -4px; cursor: e-resize; }\n            &.bottom { bottom: -4px; left: 50%; margin-left: -4px; cursor: s-resize; }\n            &.left { top: 50%; left: -4px; margin-top: -4px; cursor: w-resize; }\n        }\n    "]);return D=function(){return t},t}function N(){var t=d(["\n    "]);return N=function(){return t},t}var R=(0,n(79874).rU)((function(t){t.token;var e=t.css;return{root:e(m()),container:e(p()),imageRow:e(x()),imageContainer:e(b()),imageWrapper:e(y()),imageBackground:e(v()),image:e(w()),overlay:e(M()),editTools:e(j()),toolsHeader:e(S()),toolSection:e(k()),bottomRow:e(z()),hint:e(O()),selectionBox:e(D()),toolButton:e(N())}}));function P(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Y(t,e,n,i,r,o,a){try{var c=t[o](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(i,r)}function X(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var o=t.apply(e,n);function a(t){Y(o,i,r,a,c,"next",t)}function c(t){Y(o,i,r,a,c,"throw",t)}a(void 0)}))}}function I(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function B(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),i.forEach((function(e){I(t,e,n[e])}))}return t}function C(t,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):function(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})),t}function T(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,o=[],a=!0,c=!1;try{for(n=n.call(t);!(a=(i=n.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(t){c=!0,r=t}finally{try{a||null==n.return||n.return()}finally{if(c)throw r}}return o}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return P(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return P(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(t,e){var n,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(n=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(t,o)}catch(t){c=[6,t],i=0}finally{n=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}var _=function(){var t=X((function(t,e){var n,i,c,u,l,h;return E(this,(function(t){switch(t.label){case 0:return n=e.homeStore.img_base64.indexOf("base64,"),i=e.homeStore.img_base64.substring(n+7),c={base64String:i},[4,(0,s.pn)(c)];case 1:return(u=t.sent())&&u.success&&(l=JSON.parse(u.data.content),h={b64_img:e.homeStore.img_base64,content:l},a.nb.RunCommand(r.f.MeasurScaleMode),e.homeStore.setDesignMode(r.f.MeasurScaleMode),a.nb.DispatchEvent(a.n0.MeasureScale,h),a.nb.emit(o.U.OnPreparingHandle,{opening:!1})),[2]}}))}));return function(e,n){return t.apply(this,arguments)}}(),H=(0,h.observer)((function(){var t=(0,g.B)().t,e=(0,c.P)(),n=T((0,f.useState)(!1),2),r=n[0],s=n[1],h=T((0,f.useState)({x:0,y:0,width:0,height:0,isDragging:!1,isResizing:!1,resizeHandle:""}),2),d=h[0],m=h[1],p=T((0,f.useState)({x:0,y:0}),2),x=p[0],b=p[1],y=T((0,f.useState)(!1),2),v=y[0],w=y[1],M=T((0,f.useState)({x:0,y:0}),2),j=M[0],S=M[1],k=T((0,f.useState)(1),2),z=k[0],O=k[1],D=R().styles,N=function(){var n=X((function(){var n;return E(this,(function(i){return(n=new Image).onload=X((function(){var i,r,o,a,c,u,l,h,f,g,m,p,x,b,y,v,w,M,j,S,k;return E(this,(function(O){switch(O.label){case 0:return i=document.createElement("canvas"),r=i.getContext("2d"),o=document.querySelector("."+D.image),(a=o.parentElement)?(c=a.getBoundingClientRect(),u=o.getBoundingClientRect(),l=u.width/z,h=u.height/z,f=(c.width-u.width)/2,g=(c.height-u.height)/2,m=(d.x-f)/z*(n.width/l),p=(d.y-g)/z*(n.height/h),x=d.width/z*(n.width/l),b=d.height/z*(n.height/h),i.width=x,i.height=b,r.fillStyle="#FFFFFF",r.fillRect(0,0,i.width,i.height),y=Math.max(0,m),v=Math.max(0,p),w=Math.min(n.width-y,x),M=Math.min(n.height-v,b),j=Math.max(0,-m),S=Math.max(0,-p),w>0&&M>0&&r.drawImage(n,y,v,w,M,j,S,w,M),k=i.toDataURL("image/jpeg",1),e.homeStore.setImgBase64(k),[4,_(t,e)]):[2];case 1:return O.sent(),s(!1),[2]}}))})),n.src=e.homeStore.img_base64,[2]}))}));return function(){return n.apply(this,arguments)}}();(0,f.useEffect)((function(){a.nb.on(o.U.ShowRoomImagePredictDlg,(function(t){t&&t.length>0?(e.homeStore.setImgBase64(t),s(!0)):e.homeStore.setImgBase64(null)}))}),[]);var P=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(t.stopPropagation(),e)m((function(t){return C(B({},t),{isResizing:!0,resizeHandle:e})}));else{var n=t.clientX-d.x,i=t.clientY-d.y;b({x:n,y:i}),m((function(t){return C(B({},t),{isDragging:!0})}))}},Y=function(){w(!1)},I=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";t.stopPropagation();var n=t.touches[0];if(e)m((function(t){return C(B({},t),{isResizing:!0,resizeHandle:e})}));else{var i=n.clientX-d.x,r=n.clientY-d.y;b({x:i,y:r}),m((function(t){return C(B({},t),{isDragging:!0})}))}};return r&&(0,i.jsx)(l._w,{className:D.root,title:t("导入户型图"),center:!0,width:800,height:590,mask:!0,onClose:function(){s(!1)},bodyStyle:{overflow:"hidden"},children:(0,i.jsxs)("div",{className:D.container,children:[(0,i.jsxs)("div",{className:D.imageRow,children:[(0,i.jsx)("div",{className:D.imageContainer,children:(0,i.jsx)("div",{className:D.imageWrapper,onMouseDown:function(t){if(!t.target.closest(".selectionBox, .resize-handle")){var e=t.currentTarget.getBoundingClientRect(),n=t.clientX-e.left,i=t.clientY-e.top;w(!0),S({x:n,y:i}),m((function(t){return C(B({},t),{x:n,y:i,width:0,height:0,isDragging:!1,isResizing:!1})}))}},onMouseMove:function(t){!function(t){if(v){var e=t.currentTarget.getBoundingClientRect(),n=t.clientX-e.left,i=t.clientY-e.top,r=Math.abs(n-j.x),o=Math.abs(i-j.y),a=Math.min(n,j.x),c=Math.min(i,j.y),s=Math.max(0,Math.min(a,e.width)),u=Math.max(0,Math.min(c,e.height)),l=Math.min(r,e.width-s),h=Math.min(o,e.height-u);m((function(t){return C(B({},t),{x:s,y:u,width:l,height:h})}))}}(t),function(t){if(t.preventDefault(),d.isDragging||d.isResizing){var e=t.currentTarget.getBoundingClientRect(),n=e.width,i=e.height;if(d.isResizing){var r=B({},d);switch(d.resizeHandle){case"top-left":r.width=d.width+(d.x-t.clientX+e.left),r.height=d.height+(d.y-t.clientY+e.top),r.x=Math.max(0,t.clientX-e.left),r.y=Math.max(0,t.clientY-e.top);break;case"top-right":r.width=Math.min(n-d.x,t.clientX-e.left-d.x),r.height=d.height+(d.y-t.clientY+e.top),r.y=Math.max(0,t.clientY-e.top);break;case"bottom-left":r.width=d.width+(d.x-t.clientX+e.left),r.height=Math.min(i-d.y,t.clientY-e.top-d.y),r.x=Math.max(0,t.clientX-e.left);break;case"bottom-right":r.width=Math.min(n-d.x,t.clientX-e.left-d.x),r.height=Math.min(i-d.y,t.clientY-e.top-d.y);break;case"top":r.height=d.height+(d.y-t.clientY+e.top),r.y=Math.max(0,t.clientY-e.top);break;case"right":r.width=Math.min(n-d.x,t.clientX-e.left-d.x);break;case"bottom":r.height=Math.min(i-d.y,t.clientY-e.top-d.y);break;case"left":r.width=d.width+(d.x-t.clientX+e.left),r.x=Math.max(0,t.clientX-e.left)}r.width=Math.max(20,r.width),r.height=Math.max(20,r.height),r.x+r.width>n&&(r.x=n-r.width),r.y+r.height>i&&(r.y=i-r.height),m(r)}else if(d.isDragging){var o=t.clientX-x.x,a=t.clientY-x.y,c=Math.max(0,Math.min(o,n-d.width)),s=Math.max(0,Math.min(a,i-d.height));m((function(t){return C(B({},t),{x:c,y:s})}))}}}(t)},onMouseUp:function(t){Y(),function(t){t.stopPropagation(),m((function(t){return C(B({},t),{isDragging:!1,isResizing:!1,resizeHandle:""})}))}(t)},onMouseLeave:function(t){Y(),function(){if(v)w(!1);else if(d.isDragging||d.isResizing){var t=function(t){var e=document.querySelector("."+D.imageWrapper);if(e){var n=e.getBoundingClientRect();if(t.clientX>=n.left&&t.clientX<=n.right&&t.clientY>=n.top&&t.clientY<=n.bottom){var i=t.clientX-n.left,r=t.clientY-n.top;if(d.isDragging){var o=Math.max(0,Math.min(i-x.x,n.width-d.width)),a=Math.max(0,Math.min(r-x.y,n.height-d.height));m((function(t){return C(B({},t),{x:o,y:a})}))}}}},e=function(){m((function(t){return C(B({},t),{isDragging:!1,isResizing:!1,resizeHandle:""})})),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",t)};document.addEventListener("mouseup",e),document.addEventListener("mousemove",t)}}()},onWheel:function(t){t.preventDefault();var e=t.deltaY>0?.9:1.1,n=Math.min(Math.max(z*e,.1),5);O(n)},onTouchStart:I,onTouchMove:function(t){if(t.preventDefault(),d.isDragging||d.isResizing){var e=t.touches[0],n=t.currentTarget.getBoundingClientRect(),i=n.width,r=n.height;if(d.isResizing){var o=B({},d);switch(d.resizeHandle){case"top-left":o.width=d.width+(d.x-e.clientX+n.left),o.height=d.height+(d.y-e.clientY+n.top),o.x=Math.max(0,e.clientX-n.left),o.y=Math.max(0,e.clientY-n.top);break;case"top-right":o.width=Math.min(i-d.x,e.clientX-n.left-d.x),o.height=d.height+(d.y-e.clientY+n.top),o.y=Math.max(0,e.clientY-n.top);break;case"bottom-left":o.width=d.width+(d.x-e.clientX+n.left),o.height=Math.min(r-d.y,e.clientY-n.top-d.y),o.x=Math.max(0,e.clientX-n.left);break;case"bottom-right":o.width=Math.min(i-d.x,e.clientX-n.left-d.x),o.height=Math.min(r-d.y,e.clientY-n.top-d.y);break;case"top":o.height=d.height+(d.y-e.clientY+n.top),o.y=Math.max(0,e.clientY-n.top);break;case"right":o.width=Math.min(i-d.x,e.clientX-n.left-d.x);break;case"bottom":o.height=Math.min(r-d.y,e.clientY-n.top-d.y);break;case"left":o.width=d.width+(d.x-e.clientX+n.left),o.x=Math.max(0,e.clientX-n.left)}o.width=Math.max(20,o.width),o.height=Math.max(20,o.height),o.x+o.width>i&&(o.x=i-o.width),o.y+o.height>r&&(o.y=r-o.height),m(o)}else if(d.isDragging){var a=e.clientX-x.x,c=e.clientY-x.y,s=Math.max(0,Math.min(a,i-d.width)),u=Math.max(0,Math.min(c,r-d.height));m((function(t){return C(B({},t),{x:s,y:u})}))}}},onTouchEnd:function(t){t.stopPropagation(),m((function(t){return C(B({},t),{isDragging:!1,isResizing:!1,resizeHandle:""})}))},children:e.homeStore.img_base64&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("img",{src:e.homeStore.img_base64,className:D.image,alt:t("户型图"),onLoad:function(t){var e=t.currentTarget,n=e.parentElement;if(n){var i=n.getBoundingClientRect(),r=e.getBoundingClientRect(),o=r.width,a=r.height,c=(i.width-o)/2,s=(i.height-a)/2;O(1),m((function(t){return C(B({},t),{x:c,y:s,width:o,height:a})}))}},style:{transform:"scale(".concat(z,")"),transformOrigin:"center center"}}),(0,i.jsxs)("div",{className:D.selectionBox,style:{left:"".concat(d.x,"px"),top:"".concat(d.y,"px"),width:"".concat(d.width,"px"),height:"".concat(d.height,"px")},onMouseDown:function(t){return P(t)},children:[(0,i.jsx)("div",{className:"resize-handle top-left",onMouseDown:function(t){return P(t,"top-left")},onTouchStart:function(t){return I(t,"top-left")}}),(0,i.jsx)("div",{className:"resize-handle top-right",onMouseDown:function(t){return P(t,"top-right")},onTouchStart:function(t){return I(t,"top-right")}}),(0,i.jsx)("div",{className:"resize-handle bottom-left",onMouseDown:function(t){return P(t,"bottom-left")},onTouchStart:function(t){return I(t,"bottom-left")}}),(0,i.jsx)("div",{className:"resize-handle bottom-right",onMouseDown:function(t){return P(t,"bottom-right")},onTouchStart:function(t){return I(t,"bottom-right")}}),(0,i.jsx)("div",{className:"resize-handle top",onMouseDown:function(t){return P(t,"top")},onTouchStart:function(t){return I(t,"top")}}),(0,i.jsx)("div",{className:"resize-handle right",onMouseDown:function(t){return P(t,"right")},onTouchStart:function(t){return I(t,"right")}}),(0,i.jsx)("div",{className:"resize-handle bottom",onMouseDown:function(t){return P(t,"bottom")},onTouchStart:function(t){return I(t,"bottom")}}),(0,i.jsx)("div",{className:"resize-handle left",onMouseDown:function(t){return P(t,"left")},onTouchStart:function(t){return I(t,"left")}})]})]})})}),(0,i.jsxs)("div",{className:D.editTools,children:[(0,i.jsx)("div",{className:D.toolsHeader,children:t("户型图编辑")}),(0,i.jsx)("div",{className:D.toolSection,children:(0,i.jsxs)("div",{className:"section-header",children:[(0,i.jsx)("span",{className:"section-title",children:t("镜像")}),(0,i.jsxs)("div",{className:"buttons-group",children:[(0,i.jsx)(u.A,{className:"tool-button ".concat(D.toolButton),icon:(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:22,height:22},children:(0,i.jsx)("use",{xlinkHref:"#iconhorizontalflip_line"})}),onClick:function(){var t=new Image;t.onload=function(){var n=document.createElement("canvas");n.width=t.width,n.height=t.height;var i=n.getContext("2d");i.translate(t.width,0),i.scale(-1,1),i.drawImage(t,0,0);var r=n.toDataURL("image/jpeg");e.homeStore.setImgBase64(r)},t.src=e.homeStore.img_base64}}),(0,i.jsx)(u.A,{className:"tool-button ".concat(D.toolButton),icon:(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:22,height:22},children:(0,i.jsx)("use",{xlinkHref:"#iconverflip_line"})}),onClick:function(){var t=new Image;t.onload=function(){var n=document.createElement("canvas");n.width=t.width,n.height=t.height;var i=n.getContext("2d");i.translate(0,t.height),i.scale(1,-1),i.drawImage(t,0,0);var r=n.toDataURL("image/jpeg");e.homeStore.setImgBase64(r)},t.src=e.homeStore.img_base64}})]})]})}),(0,i.jsx)("div",{className:D.toolSection,children:(0,i.jsxs)("div",{className:"section-header",children:[(0,i.jsx)("span",{className:"section-title",children:t("旋转")}),(0,i.jsx)("div",{className:"buttons-group",children:(0,i.jsx)(u.A,{className:"tool-button ".concat(D.toolButton),icon:(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:22,height:22},children:(0,i.jsx)("use",{xlinkHref:"#iconrotate90"})}),onClick:function(){var t=new Image;t.onload=function(){var n=document.createElement("canvas");n.width=t.height,n.height=t.width;var i=n.getContext("2d");i.translate(n.width/2,n.height/2),i.rotate(Math.PI/2),i.drawImage(t,-t.width/2,-t.height/2);var r=n.toDataURL("image/jpeg");e.homeStore.setImgBase64(r)},t.src=e.homeStore.img_base64}})})]})})]})]}),(0,i.jsxs)("div",{className:D.bottomRow,children:[(0,i.jsx)("div",{className:D.hint,children:t("请框选要生成3D户型图的区域")}),(0,i.jsx)(u.A,{type:"primary",onClick:N,children:t("智能识别")})]})]})})}))}}]);