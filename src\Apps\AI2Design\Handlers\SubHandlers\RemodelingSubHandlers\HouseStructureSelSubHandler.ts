import { T_WallsChangedOpertaionInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_WallsChangedOpertaionInfo";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { RemodelingModeHandler } from "../../RemodelingModeHandler";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { RmodelingUtils } from "./RmodelingUtils";
import { Vector3Like } from "three";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";

// 墙体选择子模式
export class HouseStructureSelSubHandler extends CadBaseSubHandler {
    private _selected_wallUIDs: number[] = [];
    private _hovered_wall: TWall = null;
    private _remodelingModeHandler: RemodelingModeHandler;
    private _outer_polygons: ZPolygon[] = [];
    constructor(cad_mode_handler: RemodelingModeHandler) {
        super(cad_mode_handler);
        this._remodelingModeHandler = cad_mode_handler;
        this._selected_wallUIDs = [];
        this._outer_polygons = [];
        this.name = "HouseStructureSelSubHandler";
    }

    enter(state: number = 0) {
        this.manager.resetUndoRedos();
        super.enter(state);
        this.manager.layout_container.fromXmlSchemeData(this._remodelingModeHandler.original_swj_layout_data);
        this.updateCandidateRects();
        this._initOuterPolygons();
        this._selectDefaultOuterWalls();
        this.update();
        this.manager.layer_CadRoomNameLayer.visible = true;
        this.manager.layout_container.remodeiling_data = null;
    }

    leave(state: number = 0) {
        super.leave(state);

        // 清除选中状态
        this.manager.layout_container._wall_entities.forEach(wall => {
            wall.is_selected = false;
        });

        // 获取所有墙体
        const allWalls = this.manager.layout_container._wall_entities;

        // 找出未被选中的墙体
        const unselectedWalls = allWalls.filter(wall => !this._selected_wallUIDs.includes(wall.uidN));
        if (unselectedWalls.length > 0) {
            // 从布局容器中移除未选中的墙体
            let operation_info = new T_WallsChangedOpertaionInfo(this.manager);
            operation_info.recordSrcWalls(unselectedWalls);
            operation_info.recordTargetWalls([]);
            operation_info.redo(this.manager);
        }
        this.manager.resetUndoRedos();
        this.cleanSelection();
        this.update();
    }

    onmousedown(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.button == 2) return;

        let wall = this._getTargetWallByPos(pos);
        if (!wall) {
            return;
        }

        if (!this._selected_wallUIDs.includes(wall.uidN)) {
            wall.is_selected = true;
            this._selected_wallUIDs.push(wall.uidN);
        } else {
            wall.is_selected = false;
            this._selected_wallUIDs = this._selected_wallUIDs.filter(uid => uid != wall.uidN);
        }
    }

    onmousemove(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.buttons != 0) {
            return;
        }
        if (this._hovered_wall) {
            this._hovered_wall.is_hovered = false;
            this._hovered_wall = null;
        }
        let wall = this._getTargetWallByPos(pos);

        if (wall) {
            wall.is_hovered = true;
            this._hovered_wall = wall;
        }

        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
    }

    onwheel(ev: WheelEvent): void {
    }

    drawCanvas(): void {
        let painter = this._cad_mode_handler.painter;
        this._p_sc = painter._p_sc;

        for (let uid of this._selected_wallUIDs) {
            let wall = this.manager.layout_container._wall_entities.find(wall => wall.uidN == uid);
            if (wall) {
                this.painter.fillStyle = "#04f";
                wall.drawEntity(this.painter, { is_selected: true, is_draw_figure: true, is_draw_texture: this.container._drawing_layer_mode === "AIMatching" });
            }
        }
        if (this._hovered_wall) {
            this.painter.fillStyle = "#04f";
            this._hovered_wall.drawEntity(this.painter, { is_hovered: true, is_draw_figure: true, is_draw_texture: this.container._drawing_layer_mode === "AIMatching" });
        }
    }

    // 获取鼠标点击的墙
    private _getTargetWallByPos(pos: Vector3Like): TWall | null {
        let target_rect = this.getTargetRectContainsPos(pos);
        if (!target_rect) {
            return;
        }
        let entity = TBaseEntity.getEntityOfRect(target_rect);
        if (entity.type != "Wall") {
            return null;
        }
        return entity as TWall;
    }

    // 默认选择上外墙
    private _selectDefaultOuterWalls() {
        for (let wall of this.manager.layout_container._wall_entities) {
            if (this._checkWallIsOuter(wall)) {
                wall.is_selected = true;
                this._selected_wallUIDs.push(wall.uidN);
            }
        }
    }

    private _initOuterPolygons() {
        this._outer_polygons = RmodelingUtils.getRoomOutlinePolygons(this.manager.layout_container);
    }

    private _checkWallIsOuter(wall: TWall): boolean {
        return RmodelingUtils.checkWallIsOuter(wall, this._outer_polygons);
    }

} 