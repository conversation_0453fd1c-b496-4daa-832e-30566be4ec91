import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { I_BaseGroupElementInfo } from "../../IRoomInterface";

export interface IBaseGroupTransferOptions
{
    /**
     *  支持放缩的迁移
     */
    scale_transfer ?: boolean;
}
/**
 *  组合迁移的方法
 */
export class BaseGroupTransferMethods
{
    /**
     * 记录并产出 member_rect 和 parent_rect的I_BaseGroupElementInfo
     * @param member_rect  
     * @param parent_rect 
     * @returns I_BaseGroupElementInfo
     */
    static recordGroupRectDataToMemberRect(member_rect: ZRect, parent_rect: ZRect) {
        let pp = parent_rect.project(member_rect.rect_center);
        let p_nor = parent_rect.project(member_rect.rect_center.clone().add(member_rect.nor));

        let right_pos = parent_rect.project(member_rect.edges[0].center);
        let top_pos = parent_rect.project(member_rect.edges[1].center);


        let t_nor = new Vector3().copy(p_nor).normalize();

        let t_angle = Math.atan2(member_rect.nor.dot(parent_rect.nor), member_rect.nor.dot(parent_rect.dv));

        member_rect.ex_prop['uuid'] = Date.now().toString(36) + Math.random().toString(36).substr(2, 16);
        let ele_info = member_rect._attached_elements as I_BaseGroupElementInfo;
        ele_info.group_p_center = pp;
        ele_info.group_p_nor = p_nor;
        ele_info.group_p_angle = t_angle;
        ele_info.group_p_center_pp =  new Vector3().copy({ x: pp.x / parent_rect.w, y: pp.y / parent_rect.h, z: 1 });
        ele_info.group_p_right_pp = new Vector3().copy({ x: right_pos.x / parent_rect.w, y: right_pos.y / parent_rect.h, z: 1 });
        ele_info.group_p_top_pp = new Vector3().copy({ x: top_pos.x / parent_rect.w, y: top_pos.y / parent_rect.h, z: 1 });
        ele_info.parent_rect = parent_rect;

        return ele_info;
        // console.info(logContent);
    }

    /**
     * @description  解耦组合的迁移
     *     --- member_rect | parent_rect |  elementInfo 相互解耦---即迁移
     * @param member_rect 
     * @param parent_rect 
     * @param info 
     */
    static applyGroupTransferInGroup(member_rect:ZRect, parent_rect:ZRect, info:I_BaseGroupElementInfo,options:IBaseGroupTransferOptions={scale_transfer:true})
    {
        let ele_info : I_BaseGroupElementInfo = info;
        let group_p_center_pp =ele_info.group_p_center_pp.clone();
        let group_p_right_pp = ele_info.group_p_right_pp.clone();
        let group_p_top_pp =ele_info.group_p_top_pp.clone(); 
        let group_p_angle = ele_info.group_p_angle; 
        let p_t_nor = parent_rect.dv.clone().multiplyScalar(Math.cos(group_p_angle)).add(parent_rect.nor.clone().multiplyScalar(Math.sin(group_p_angle)));
        group_p_center_pp.x *= parent_rect.w;
        group_p_center_pp.y *= parent_rect.h;
        group_p_right_pp.x *= parent_rect.w;
        group_p_right_pp.y *= parent_rect.h;
        group_p_top_pp.x *= parent_rect.w;
        group_p_top_pp.y *= parent_rect.h;
        let t_rect_center = parent_rect.unproject(group_p_center_pp);
        let t_rect_right = parent_rect.unproject(group_p_right_pp);
        let t_rect_top = parent_rect.unproject(group_p_top_pp);
        let t_rect_w = t_rect_right.clone().sub(t_rect_center).length() * 2;
        let t_rect_h = t_rect_top.clone().sub(t_rect_center).length() * 2;
        if(options.scale_transfer)
        {
            member_rect._w = t_rect_w;
            member_rect._h = t_rect_h;
        }
        member_rect.nor = p_t_nor;
        member_rect.rect_center = t_rect_center;
    }
}