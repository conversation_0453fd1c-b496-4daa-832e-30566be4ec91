import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
antTableThead: css`
    .ant-table-wrapper .ant-table-thead > tr > th {
    background: transparent;
    }
`,
separator: css`
    width: 2px;
    height: 15px;
    background-color: #ccc;
    margin: 0 6px;
`,
title: css`
    background-color: transparent;
    color: #959598;
    font-family: PingFang SC;
    font-weight: normal;
    font-size: 12px;
    line-height: 1.67;
    letter-spacing: 0px;
    text-align: left;
`,
fileName: css`
    color: #282828;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
`,
text: css`
    color: #282828;
    font-family: PingFang SC;
    font-weight: normal;
    font-size: 12px;
    line-height: 1.67;
    letter-spacing: 0px;
    text-align: left;
`,
}));