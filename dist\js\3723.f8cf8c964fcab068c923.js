"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[3723],{63723:function(n,e,t){t.r(e),t.d(e,{default:function(){return io}});var r=t(13274),i=t(98612),o=t(84872),a=t(88934),l=t(63286),c=t(27347),s=t(17287),u=t(19356),d=t(51010),f=t(25629),p=t(75887),h=t(25617),m=t(23825),g=t(58567),v=t(9003),x=t(33100),b=t(77320),y=t(36134),w=t(41980),_=t(15696),S=t(41594),j=t.n(S),k=t(85783),A=t(49063),I=t(49816),C=t(79874);function E(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function D(){var n=E([""]);return D=function(){return n},n}function F(){var n=E(["\n        position :absolute;\n        left:0;\n        top:0;\n        width: 400px;\n        height:100%;\n        z-index:5;\n        background:#fff;\n    "]);return F=function(){return n},n}function N(){var n=E(["\n    overflow-y: auto;\n    background: #fff;\n    .room_ele_div {\n      width: 250px;\n      height: 300px;\n      margin-left: 10px;\n      margin-top: 10px;\n      float: left;\n      border: 1px solid #000;\n      overflow: hidden;\n      text-align: center;\n      cursor: pointer;\n      &:hover {\n        background: rgba(127, 127, 255, 0.5);\n      }\n    }\n    canvas {\n      width: 250px;\n      height: 250px;\n    }\n  "]);return N=function(){return n},n}function O(){var n=E(["\n        position :absolute;\n        right:0;\n        top:0;\n        width: 300px;\n        height:100%;\n        z-index:5;\n        background:#fff;\n        .title {\n          color: #aaa;\n          font-family: PingFang SC;\n          font-weight: semibold;\n          font-size: 16px;\n          line-height: 24px;\n          font-weight: 600;\n          background: #F2F3F5;\n         width: 100%;\n         height: 40px;\n         display: -webkit-box;\n         display: -webkit-flex;\n         display: -ms-flexbox;\n         display: flex;\n         -webkit-align-items: center;\n         -webkit-box-align: center;\n         -ms-flex-align: center;\n         align-items: center;\n         padding-left: 16px;\n         margin-bottom: 10px;\n         cursor:pointer;\n         .active {\n            color:#282828;\n         }\n       }\n       select {\n        margin-top:10px;\n      }\n      .row_div {\n        width:100%;\n        padding:5px;\n        margin-top:5px;\n        .label_div {\n            width:80px;\n            height:20px;\n            float:left;\n        }\n        input {\n          width:130px;\n          margin-left:5px;\n        }\n        button {\n          width:100px;\n          margin-left:5px;\n        }\n        select {\n          width:200px;\n          margin-left:5px;\n          margin-top:0px;\n        }\n        textarea {\n          width:200px;\n          height:150px;\n          margin-left:5px;\n          margin-top:0px;\n        }\n        .labelList {\n          width:200px;\n          height:150px;\n          margin-left:5px;\n          margin-top:0px;\n          overflow-y:auto;\n          border:1px solid;\n        }\n        .labelListRow {\n            width:100%;\n            height:20px;\n            line-height:20px;\n        }\n        .labelListRow.checked {\n          background:rgba(127,127,255,0.5);\n        }\n      }\n    "]);return O=function(){return n},n}function M(){var n=E(["\n    "]);return M=function(){return n},n}function P(){var n=E(["\n      position: absolute;\n      top: 17%;\n      width: 100%;\n      height: 0;\n      background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return P=function(){return n},n}function L(){var n=E(["\n      float:right;\n      margin-right:15px;\n      padding:2px;\n      border-radius:2px;\n      cursor:pointer;\n      &:hover {\n        background :#adf;\n      }\n      &.isRunning {\n        background :#adf;\n      }\n    "]);return L=function(){return n},n}function T(){var n=E(["\n      position : absolute;\n      left:0;\n      top:0;\n      width:100%;\n      height:100%; \n      overflow:hidden;\n      background:#fff;\n      .listDiv {\n        height:calc(100% - 35px);\n        overflow-y:auto;\n      }\n      .dataset_title {\n        width:95%;\n        height:35px;\n        line-height:25px;\n        padding-left:2%;\n        padding-top:5px;\n      }\n      .scheme_row_div {\n          width:95%;\n          height:45px;\n          line-height:20px;\n          padding-left:5%;\n          border-bottom:1px dashed #adf;\n          cursor:pointer;\n          user-select:text;\n      }\n      .scheme_row_div:hover {\n         background :#efefef;\n      }\n      .scheme_row_div.highlight {\n        background :#efefef;\n      }\n      .scheme_row_div.active {\n        background :#ccefef;\n      }\n\n\n    "]);return T=function(){return n},n}function R(){var n=E(["\n      overflow-y:auto;\n      .title {\n         color: #282828;\n         font-family: PingFang SC;\n         font-weight: semibold;\n         font-size: 16px;\n         line-height: 24px;\n         font-weight: 600;\n         background: #F2F3F5;\n        width: 100%;\n        height: 40px;\n        display: -webkit-box;\n        display: -webkit-flex;\n        display: -ms-flexbox;\n        display: flex;\n        -webkit-align-items: center;\n        -webkit-box-align: center;\n        -ms-flex-align: center;\n        align-items: center;\n        padding-left: 16px;\n        margin-bottom: 10px;\n      }\n    .content {\n      position: relative;\n      overflow-y: scroll;\n        .info_text {\n          display: -webkit-box;\n          display: -webkit-flex;\n          display: -ms-flexbox;\n          display: flex;\n          -webkit-box-pack: justify;\n          -webkit-justify-content: space-between;\n          justify-content: space-between;\n          padding: 5px 12px;\n          user-select:text;\n          select {\n             height:100%;\n             margin:0;\n          }\n        }\n        .info_text:hover {\n          background: #eee;\n          cursor:pointer;\n        }\n        .sub_info_text {\n          padding-left: 15px;\n        }\n    }\n    "]);return R=function(){return n},n}function z(){var n=E(["\n      font-size:13px;\n      width:100%;\n      .room_title {\n        height:30px;\n        font-size:14px;\n        padding-left:10px;\n        margin-top:10px;\n        margin-bottom:5px;\n        border-bottom:1px dashed;\n      }\n      .layoutScoreRow{\n        height:20px;\n        line-height:20px;\n        .title {\n          font-weight:700;\n          padding-left:10px;\n          float:left;\n        }\n        .score {\n          float:right;\n          padding-right:20px;\n        }\n      }\n    "]);return z=function(){return n},n}function U(){var n=E(["\n      position:absolute;\n      min-width:200px;\n      min-height:200px;\n      top:0px;\n      ","\n\n      .layoutScoreCard {\n        border-radius: 2px;\n        box-shadow: 0px 5px 24px 0px #00000055;\n      }\n    "]);return U=function(){return n},n}function B(){var n=E(["\n      color:#147ffa;\n      float:left;\n      width:240px;\n      padding:10px;\n      padding-bottom:20px;\n      background:#fff;\n      .layout_score_row {\n        width:100%;\n        min-height:20px;     \n        float:left;    \n      }\n      .sub_layout_score {\n        width:100%;\n        min-height:20px;\n        float:left;\n        margin-bottom:5px;\n        float:left;\n      }\n      .score_level_0 {\n         line-height:30px;\n         font-size : 15px;\n         font-weight:700;\n      }\n      .score_level_1 {\n        line-height:20px;\n        font-size : 12px;\n        font-weight:500;\n      }\n      .score_level_2 {\n        line-height:20px;\n        font-size : px;\n        font-weight:500;\n        padding-left:10px;\n      }\n      .score_label {\n        float : left;\n        padding-left:5px;\n      }\n      .div_Score {\n        float:right;\n        padding-right:5px;\n      }\n      .div_Grade {\n        color : #bd761d;\n        font-size:13px;\n        font-style:italic;\n        margin-left:5px;\n      }\n    "]);return B=function(){return n},n}var H=(0,C.rU)((function(n){n.token;var e=n.css;return{root:e(D()),leftPanel:e(F()),result_list:e(N()),rightPanel:e(O()),returnListBtn:e(M()),progressInfo:e(P()),startRunningBtn:e(L()),houseSchemeList:e(T()),houseSchemeTestingPanel:e(R()),layoutScoreContent:e(z()),schemeLayoutScorePopUp:e(U(),(0,m.fZ)()?"\n        position: fixed;\n        left: 50%;\n        top: 50% !important;\n        transform: translate(-50%, -50%);\n      ":"\n        left: 310px;\n      "),layoutScoreCard:e(B())}})),W=t(31281),K=t(50316),q=t(10371),V=t(67869),Z=t(65640);function G(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function $(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Y(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){$(o,r,i,a,l,"next",n)}function l(n){$(o,r,i,a,l,"throw",n)}a(void 0)}))}}function X(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return G(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return G(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var J=W.A.Platform,nn=b.A.confirm,en=(0,_.observer)((function(n){var e=(0,k.B)().t,t=(0,v.P)(),o=(0,A.zy)(),l=(o.pathname,H().styles,X((0,S.useState)(!0),2)),s=l[0],u=l[1],d=X((0,S.useState)([]),2),f=d[0],p=d[1],h=n.disabled,g=function(){var n=Y((function(){var n,e,r,i,o,l,s;return Q(this,(function(u){return c.nb.DispatchEvent(c.n0.Match3dPreviewMaterials,null),n=c.nb.instance.layout_container,c.nb.emit(a.U.Show3DViewer,1),(e=c.nb.instance.scene3D)&&e.setCemeraMode(q.I5.FirstPerson),t.homeStore.setPreview3D(!0),r=n._room_entities,(i=r.reduce((function(n,e){return n?e._area>n._area?e:n:e}),null))?(e.setCenter((null==i||null===(o=i._main_rect)||void 0===o?void 0:o.rect_center)||new V.Pq0(0,0,0)),e.update()):e.setCenter((null===(s=r[0])||void 0===s||null===(l=s._main_rect)||void 0===l?void 0:l.rect_center)||new V.Pq0(0,0,0)),t.homeStore.setViewMode("3D_FirstPerson"),e&&e.startRender(),[2]}))}));return function(){return n.apply(this,arguments)}}();return(0,S.useEffect)((function(){u(!0),c.nb.on_M(a.U.SubHandlerChanged,"DesignModeButtons",(function(n){u(!0),t.homeStore.designMode===i.f.ExDrawingMode?!1===n.is_default?p([{name:e("完成"),onClick:function(){c.nb.instance&&c.nb.RunCommand(c._I.LeaveSubHandler)}}]):p([{name:e("退出画笔"),onClick:function(){c.nb.instance&&(c.nb.RunCommand(i.f.AiCadMode),t.homeStore.setDesignMode(i.f.AiCadMode))}}]):t.homeStore.designMode===i.f.RemodelingMode&&("ResultShowSubHandler"==n.name||"HouseCorrectionBaseHandler"==n.name?p([{name:e("应用"),onClick:function(){c.nb.instance&&c.nb.DispatchEvent(c.n0.ApplyRemodelingScheme,null)}}]):u(!1))})),setTimeout((function(){t.homeStore.designMode===i.f.AiCadMode?(p([t.homeStore.preview3D?{name:e("返回2D编辑"),onClick:function(){if(K.s.trackClickEvent(t.homeStore.designMode,"返回2D编辑"),t.homeStore.preview3D)return t.homeStore.setPreview3D(!1),t.designStore.setShow(!0),t.homeStore.setViewMode("2D"),void c.nb.emit(a.U.Show3DViewer,0)}}:{name:e("返回户型编辑"),onClick:function(){var n,r;K.s.trackClickEvent(t.homeStore.designMode,"返回户型编辑"),m.Bj?c.nb.instance&&("SingleRoom"==(null===(r=c.nb.instance)||void 0===r||null===(n=r.layout_container)||void 0===n?void 0:n._drawing_layer_mode)&&c.nb.DispatchEvent(c.n0.leaveSingleRoomLayout,{}),c.nb.instance._current_handler_mode=i.f.HouseDesignMode,c.nb.RunCommand(i.f.HouseDesignMode),t.homeStore.setDesignMode(i.f.HouseDesignMode)):nn({title:e("确认退出"),content:e("您有未保存的更改，退出后将丢失这些更改。是否确认退出？"),okText:e("取消"),cancelText:e("确认退出"),onOk:function(){Z.log("取消退出")},onCancel:function(){Z.log("获取宿主的APP_ID",m.sZ),J.Application.closeApp({appId:m.sZ})},okButtonProps:{type:"primary"},cancelButtonProps:{type:"default"}})}},{name:e("生成3D方案"),onClick:Y((function(){return Q(this,(function(e){return n.create3DLayout(),t.schemeStatusStore.pendingOpenSchemeIn3D=!0,[2]}))}))}]),"/dreamer"!==o.pathname&&"/Dreamer"!==o.pathname&&"dreamer"!==m.yH||p([{name:e("相似匹配"),onClick:function(){t.homeStore.setShowDreamerPopup(!0)}},{name:e("3D预览"),onClick:Y((function(){return Q(this,(function(n){return K.s.trackClickEvent(t.homeStore.designMode,"3D预览"),g(),[2]}))}))}]),t.homeStore.preview3D&&p([{name:e("返回2D编辑"),onClick:function(){t.homeStore.setPreview3D(!1),t.designStore.setShow(!0),t.homeStore.setViewMode("2D"),c.nb.emit(a.U.Show3DViewer,0)}}])):t.homeStore.designMode===i.f.ExDrawingMode?p([{name:e("退出画笔"),onClick:function(){c.nb.instance&&(c.nb.RunCommand(i.f.AiCadMode),t.homeStore.setDesignMode(i.f.AiCadMode))}}]):t.homeStore.designMode===i.f.RulerMode?p([{name:e("退出量尺"),onClick:function(){c.nb.instance&&(c.nb.RunCommand(c._I.LeaveSubHandler),t.homeStore.setDesignMode(i.f.AiCadMode))}}]):t.homeStore.designMode===i.f.MeasurScaleMode?p([{name:e("取消"),onClick:function(){c.nb.instance&&(c.nb.RunCommand(i.f.AiCadMode),t.homeStore.setDesignMode(i.f.AiCadMode))}},{name:e("确定"),onClick:function(){c.nb.instance&&(c.nb.DispatchEvent(c.n0.ConfirmScale,{img_base64:t.homeStore.img_base64}),t.homeStore.setDesignMode(i.f.AiCadMode))}}]):t.homeStore.designMode===i.f.RemodelingMode?1==(null==f?void 0:f.length)&&"应用"==f[0].name?p([{name:e("应用"),onClick:function(){c.nb.instance&&c.nb.DispatchEvent(c.n0.ApplyRemodelingScheme,null)}}]):u(!1):t.homeStore.designMode===i.f.HouseCorrectionMode?p([{name:e("应用"),onClick:function(){c.nb.instance&&c.nb.DispatchEvent(c.n0.ApplyHouseCorrectionScheme,null)}}]):p([{name:e("进入布局"),onClick:Y((function(){return Q(this,(function(n){return K.s.trackClickEvent(t.homeStore.designMode,"进入布局"),c.nb.instance&&(c.nb.RunCommand(i.f.AiCadMode),t.homeStore.setDesignMode(i.f.AiCadMode),"local"===m.yH&&"DwgBase64"===m.Zx&&c.nb.DispatchEvent(c.n0.autoSave,null),"local"===m.yH&&"hxedit"===m.Zx&&c.nb.DispatchEvent(c.n0.autoSave,null)),[2]}))}))}])}),50)}),[t.homeStore.designMode,t.homeStore.preview3D]),(0,r.jsx)(r.Fragment,{children:s&&f.map((function(n){return(0,r.jsx)(y.A,{className:"btn",type:"返回户型编辑"===n.name?"default":"primary",onClick:n.onClick,disabled:h,children:e(n.name)},n.name)}))})})),tn=t(55111),rn=t(17432),on=t(94499);function an(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ln(){var n=an(["\n        border-radius: 8px;\n        background: linear-gradient(180deg, #FFF3DCFF 0%, #FFFFFFFF 35%);\n        box-shadow: 0px 8px 24px 0px #00000028;\n        height: auto;\n        min-height: 550px;\n        padding: 20px;\n        position: relative;\n        display: block;\n        @media (max-width: 450px) {\n            width: 300px !important;\n        }\n        ","\n    \n    "]);return ln=function(){return n},n}function cn(){var n=an(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 10px;\n        font-weight: 600;\n        font-size: 16px;\n        color: #502e00;\n        img {\n            width: 80px;\n            height: 40px;\n        }\n    "]);return cn=function(){return n},n}function sn(){var n=an(["\n        display: flex;\n        align-items: center;\n        margin-bottom: 12px;\n        justify-content: space-between;\n        .subContent\n        {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            flex: 3;\n        }\n    "]);return sn=function(){return n},n}function un(){var n=an(["\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: regular;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n        flex: 2;\n    "]);return un=function(){return n},n}function dn(){var n=an(["\n       width: 48px;\n       height: 48px;\n       display: flex;\n       align-items: center;\n       font-weight: 600;\n       .excellent {\n            width: auto;\n            padding: 0 4px;\n            height: 20px;\n            border-radius: 4px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background: #FF9D00;\n            color: #fff;\n        }\n       \n       .good {\n            width: auto;\n            height: 20px;\n            padding: 0 4px;\n            border-radius: 4px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background: #c8e6f8;\n            color: #416d8b;\n        }\n       \n       .qualified {\n            width: auto;\n            height: 20px;\n            padding: 0 4px;\n            border-radius: 4px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background: #007AFF;\n            color: #fff;\n        }\n       \n       .commonly {\n            width: auto;\n            height: 20px;\n            padding: 0 4px;\n            border-radius: 4px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background: #dedede;\n            color: #282828;\n        }\n       \n       .difference {\n            width: auto;\n            height: 20px;\n            padding: 0 4px;\n            border-radius: 4px;\n            display: flex;\n            align-items: center;\n            justify-content: center;    \n            background: #fbb;\n            color: #921313;\n        }\n   "]);return dn=function(){return n},n}function fn(){var n=an(["\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: semibold;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        font-weight: 600;\n    "]);return fn=function(){return n},n}function pn(){var n=an(["\n    "]);return pn=function(){return n},n}function hn(){var n=an(["\n        border-radius: 4px;\n        background: #FAFAFA;\n        height: auto;\n        min-height: 350px;\n        padding: 12px;\n        position: relative;\n        display: block;\n        .name\n        {\n            color: #5B5E60;\n            font-family: PingFang SC;\n            font-weight: regular;\n            font-size: 12px;\n            line-height: 1.67;\n            letter-spacing: 0px;\n            text-align: left;\n        }\n        .item\n        {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            margin-bottom: 10px;\n            .anticon-check-circle\n            {\n                color: #52C41A;\n            }\n        }\n\n    "]);return hn=function(){return n},n}function mn(){var n=an(["\n        height: 230px;\n        width: 300px;\n        min-height: 230px;\n        min-width: 300px;\n        display: block;\n        position: relative;\n    "]);return mn=function(){return n},n}function gn(){var n=an(["\n        position: absolute;\n        top: 5px;\n        right: 5px;\n    "]);return gn=function(){return n},n}var vn=(0,C.rU)((function(n){n.token;var e=n.css;return{sceneBox:e(ln(),(0,m.fZ)()?"\n            width: 450px\n      \n        ":"\n            width: 350px\n        "),title:e(cn()),scoreContent:e(sn()),name:e(un()),grade:e(dn()),percent:e(fn()),start:e(pn()),descBox:e(hn()),chartsBox:e(mn()),cloneIcon:e(gn())}})),xn=t(85237),bn=t(7161),yn=t(87027),wn=t(83197),_n=t(68526),Sn=t(76330);function jn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function kn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return jn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return jn(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var An=function(n){var e,t=vn().styles,i=H().styles,o=c.nb.t,l=n.layoutScoreList||[],s=n.style||0,u=kn((0,S.useState)(!1),2),d=u[0],f=u[1];(0,S.useEffect)((function(){var n=setTimeout((function(){f(!0)}),300);return function(){return clearTimeout(n)}}),[]);var p=function(n,e,t){if(n.ui_format&&0==s)return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"score_label div_Name",children:o(n.name)},"format_sub_"+e+"_0"),(0,r.jsx)("div",{className:"score_label div_Score",children:n.score},"format_sub_"+e+"_1")]})},h=function(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return n.map((function(n,t){return function(n,e,t){return t>=1&&s&&e>2?(0,r.jsx)(r.Fragment,{}):(0,r.jsxs)("div",{className:"layout_score_row score_level_"+t,children:[p(n,e),n.children&&n.children.length>0&&(0,r.jsx)("div",{className:"sub_layout_score score_level_"+t,children:h(n.children,t+1)})]},"layout_score_"+t+"_"+e)}(n,t,e)}))},g=[{subject:o("收纳占比"),percentage:20,fullMark:100},{subject:o("布局"),percentage:20,fullMark:100},{subject:o("空间利用率"),percentage:25,fullMark:100},{subject:o("动线"),percentage:30,fullMark:100}].map((function(n){return{subject:n.subject,A:n.percentage/100*n.fullMark,fullMark:n.fullMark}})),v=(null===(e=l.filter((function(n){return"家居布局"===n.name}))[0])||void 0===e?void 0:e.children)||[],x=function(n){var e,t;if((null===(e=n.ui_format)||void 0===e?void 0:e.includes(on.td.Percentage))&&"空间利用率"===n.name){var r=n.children.find((function(n){return"家具利用率"===n.name}));if(r)return(100*r.percent).toFixed(0)+"%"}else if(null===(t=n.ui_format)||void 0===t?void 0:t.includes(on.td.Percentage))return(100*n.percent).toFixed(0)+"%";return""},b=function(n){var e,t;if((null===(e=n.ui_format)||void 0===e?void 0:e.includes(on.td.Grade))&&"空间利用率"===n.name){var r=n.children.find((function(n){return"家具利用率"===n.name}));if(r)return r.score}else if(null===(t=n.ui_format)||void 0===t?void 0:t.includes(on.td.Grade))return n.grade;return""},y=0,w=0;return l.forEach((function(n){y+=n.grade})),y&&(w=Math.round(y/l.length)),0==s?(0,r.jsxs)("div",{className:i.layoutScoreCard+" layoutScoreCard",children:[(0,m.fZ)()&&(0,r.jsx)(Sn.A,{className:t.cloneIcon,type:"icon-a-tianchongFace-1",style:{fontSize:"16px",color:"#ccc"},onClick:function(){c.nb.emit(a.U.ShowPopUpLayoutScore,{scheme:null,top:0})}}),h(l,0)]}):(0,r.jsxs)("div",{className:t.sceneBox,children:[(0,m.fZ)()&&(0,r.jsx)(Sn.A,{className:t.cloneIcon,type:"icon-a-tianchongFace-1",style:{fontSize:"16px",color:"#ccc"},onClick:function(){c.nb.emit(a.U.ShowPopUpLayoutScore,{scheme:null,top:0})}}),(0,r.jsxs)("div",{className:t.title,children:[o("综合评分"),(0,r.jsx)(rn.A,{disabled:!0,allowHalf:!0,value:on.fT[w]})]}),l.map((function(n,e){var i,a;return(0,r.jsxs)("div",{className:t.scoreContent,children:[(0,r.jsx)("div",{className:t.name,children:o(n.name)}),(0,r.jsxs)("div",{className:"subContent",children:[(0,r.jsx)("div",{className:t.grade,children:(null===(i=n.ui_format)||void 0===i?void 0:i.includes(on.td.Grade))&&(0,r.jsx)("div",{className:"".concat(on.c7[b(n)]),children:o(on.RT[on.c7[b(n)]])})}),(0,r.jsx)("div",{className:t.percent,children:x(n)}),(null===(a=n.ui_format)||void 0===a?void 0:a.includes(on.td.Stars))&&(0,r.jsx)("div",{className:t.start,children:(0,r.jsx)(rn.A,{disabled:!0,allowHalf:!0,value:on.fT[n.grade]})})]})]},e)})),(0,r.jsxs)("div",{className:t.descBox,children:[v.slice(0,3).map((function(n,e){return(0,r.jsxs)("div",{className:"item",children:[(0,r.jsx)("div",{className:"name",children:o(n.name)}),(0,r.jsx)(xn.A,{})]},e)})),(0,r.jsx)("div",{className:t.chartsBox,children:d&&(0,r.jsxs)(bn.V,{width:300,height:200,cx:150,cy:100,outerRadius:80,data:g,children:[(0,r.jsx)(yn.z,{}),(0,r.jsx)(wn.r,{dataKey:"subject"}),(0,r.jsx)(_n.V,{name:"Mike",dataKey:"A",stroke:"#FFC978",fill:"#FFC978",fillOpacity:.6})]})})]})]})};function In(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Cn(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function En(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return In(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return In(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Dn(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Fn=function(){H().styles;var n=En((0,S.useState)(!1),2),e=n[0],t=n[1],i=En((0,S.useState)([]),2),o=i[0],l=i[1],s=c.nb.t,u=((0,S.useRef)(null),c.nb.instance.layout_container),d=function(){var n,e=(n=function(){var n,e;return Dn(this,(function(t){return(n=u._selected_room)&&(e=tn.lj.ComputeScoreInRoom(n),l(e)),[2]}))},function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Cn(o,r,i,a,l,"next",n)}function l(n){Cn(o,r,i,a,l,"throw",n)}a(void 0)}))});return function(){return e.apply(this,arguments)}}(),f=function(n){t(n)};return(0,S.useEffect)((function(){return c.nb.on(a.U.ShowLayoutScoreDialog,(function(){var n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];f(n),n&&d()})),c.nb.on_M(a.U.UpdateLayoutScore,"LayoutScoreDialog",(function(){d()})),function(){}}),[]),(0,r.jsx)(r.Fragment,{children:e&&(0,r.jsx)(w._w,{title:s("布局评分器"),right:250,width:265,height:600,resizable:!0,draggable:!0,onClose:function(){f(!1)},bodyStyle:{background:"#ffffff",border:"0",boxShadow:"0"},children:(0,r.jsx)(An,{layoutScoreList:o,style:0})})})},Nn=t(64186);function On(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Mn(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){On(o,r,i,a,l,"next",n)}function l(n){On(o,r,i,a,l,"throw",n)}a(void 0)}))}}function Pn(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Ln(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){Pn(n,e,t[e])}))}return n}function Tn(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}function Rn(n){return zn.apply(this,arguments)}function zn(){return(zn=Mn((function(n){return Tn(this,(function(e){switch(e.label){case 0:return[4,(0,Nn.IW)({method:"post",url:"api/njvr/layoutSchemeHouseType/get",data:Ln({},n),timeout:6e4}).catch((function(n){return null}))];case 1:return[2,e.sent()]}}))}))).apply(this,arguments)}function Un(n){return Bn.apply(this,arguments)}function Bn(){return(Bn=Mn((function(n){return Tn(this,(function(e){switch(e.label){case 0:return[4,(0,Nn.Ap)({method:"post",url:"api/njvr/kgmaterial/pageKgMagerial",data:Ln({},n),timeout:6e4}).catch((function(n){return null}))];case 1:return[2,e.sent()]}}))}))).apply(this,arguments)}var Hn=t(41185),Wn=t(66742),Kn=t(77012),qn=t(61214),Vn=t(93321),Zn=t(81281);function Gn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function $n(){var n=Gn(["\n    background: #FFF;\n    height : 100%;\n  "]);return $n=function(){return n},n}function Yn(){var n=Gn(["\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  "]);return Yn=function(){return n},n}function Xn(){var n=Gn(["\n    color: #282828;\n    font-family: PingFang SC;\n    font-weight: semibold;\n    font-size: 20px;\n    line-height: 1.4;\n    letter-spacing: 0px;\n    text-align: left;\n    font-weight: 600;\n    margin: 16px 0px;\n  "]);return Xn=function(){return n},n}function Qn(){var n=Gn(["\n    border-radius: 6px;\n    background: #EAEAEB;\n    color: #000;\n    font-family: PingFang SC;\n    font-weight: regular;\n    font-size: 12px;\n    line-height: 20px;\n    letter-spacing: 0px;\n    text-align: left;\n    min-width: 70%;\n    height: 32px;\n    border: none;\n    padding-left: 30px;\n    :focus {\n      border-color: none; /* 取消聚焦边框 */\n      box-shadow: none; /* 取消聚焦阴影效果 */\n      outline: none; /* 取消聚焦时的外边框效果 */\n    }\n  "]);return Qn=function(){return n},n}function Jn(){var n=Gn(["\n    position: absolute;\n    top: 1px;\n    left: 5px;\n  "]);return Jn=function(){return n},n}function ne(){var n=Gn(["\n    position: absolute;\n    top: 3px;\n    right: 90px;\n    cursor: pointer;\n  "]);return ne=function(){return n},n}function ee(){var n=Gn(["\n    width: 24%;\n    display: flex;\n    justify-items: baseline;\n    align-items: center;\n    a {\n      color: #ffffff;\n      padding: 5px;\n      line-height: 23px;\n      height: 34px;\n    }\n    a:hover {\n      color: #3D9EFF;\n      border-radius: 10px;\n      background: #BFD8FF14;\n      transition: all .3s;\n    }\n  "]);return ee=function(){return n},n}function te(){var n=Gn(["\n    display: flex;\n    align-items: center;\n    position: relative;\n  "]);return te=function(){return n},n}function re(){var n=Gn(["\n    height: 100%;\n  "]);return re=function(){return n},n}function ie(){var n=Gn(["\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n  "]);return ie=function(){return n},n}function oe(){var n=Gn(["\n    display: flex;\n    flex-wrap: wrap;\n    box-sizing: border-box;\n    max-height: 550px;\n    height : calc(100vh - 300px);\n    overflow-y: scroll;\n    margin-top: 20px;\n  "]);return oe=function(){return n},n}function ae(){var n=Gn(["\n    display: flex;\n    flex-wrap: wrap;\n    box-sizing: border-box;\n    max-height: 550px;\n    height : calc(100vh - 300px);\n    overflow-y: scroll;\n    margin-top: 20px;\n  "]);return ae=function(){return n},n}function le(){var n=Gn(["\n    width: calc(25% - 10px);\n    height: auto;\n    padding: 2px;\n    box-sizing: border-box;\n    position: relative;\n    margin-right: 10px;\n    @media (max-width: 800px) {\n      width: calc(33.33% - 10px);\n    }\n  "]);return le=function(){return n},n}function ce(){var n=Gn(["\n      padding: 0 5px;\n  "]);return ce=function(){return n},n}function se(){var n=Gn(["\n    color: #282828;\n    font-family: PingFang SC;\n    font-weight: medium;\n    font-size: 14px;\n    line-height: 22px;\n    letter-spacing: 0px;\n    text-align: left;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    width: 100%;\n    margin-top: 5px;\n    font-weight: 600;\n    display: flex;\n    justify-content: space-between;\n    padding: 0 10px;\n    .ant-rate\n    {\n      color: #FFAA00;\n      font-size: 16px !important;\n      .ant-rate-star:not(:last-child)\n      {\n        margin-inline-end: 3px;\n      }\n    }\n  "]);return se=function(){return n},n}function ue(){var n=Gn(["\n      color: #6C7175;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      display: flex;\n      margin-top: 5px;\n    "]);return ue=function(){return n},n}function de(){var n=Gn(["\n      color: #5B5E60;\n      font-family: PingFang SC;\n      font-size: 12px;\n      letter-spacing: 0px;\n      text-align: left;\n      background-color: #F2F3F5;\n      width: auto;\n      border-radius: 2px;\n      padding: 2px 8px;\n      display: block;\n      white-space: nowrap;\n      margin-left: 6px;\n  "]);return de=function(){return n},n}function fe(){var n=Gn(["\n      overflow-y: hidden !important;\n  "]);return fe=function(){return n},n}function pe(){var n=Gn(["\n      width: 100%;\n      height: 98vh;\n      overflow: auto;\n      position: absolute;\n      left: 0;\n      canvas {\n        margin-left:5px;\n        margin-top:5px;\n        cursor : pointer;\n      }\n      canvas:hover {\n        background:rgba(127,127,255,0.5);\n      }\n    "]);return pe=function(){return n},n}function he(){var n=Gn(["\n    height: 100%;\n    position: absolute;\n    right: 0;\n    top: 0;\n    width: 4px;\n    cursor: col-resize;\n    z-index: 998;\n  "]);return he=function(){return n},n}function me(){var n=Gn(["\n    align-items: center;\n    height: 100%;\n    margin-left: 50%;\n    /* margin-top: 50%; */\n    padding-top: 50%;\n    text-align: center;\n    transform: translate(-50%,-50%);\n    height: 40px;\n    img\n    {\n      width: 120px;\n      height: 120px;\n    }\n    .desc\n    {\n      text-align: center;\n      margin-top: 10px;\n      color: #A2A2A5;\n      font-size: 12px;\n    }\n  "]);return me=function(){return n},n}function ge(){var n=Gn(["\n      border-radius: 4px;\n      height: auto;\n      overflow: hidden;\n      transition: all .3s;\n      padding-bottom: 10px;\n      img {\n        width: 100%;\n      }\n    "]);return ge=function(){return n},n}function ve(){var n=Gn(["\n      display: flex;\n      justify-content: right;\n      align-items: center;\n      margin-top: 20px;\n      button {\n        margin-left: 12px;\n        width: 120px;\n        height: 40px;\n        border-radius: 20px;\n        border: none;\n      }\n    "]);return ve=function(){return n},n}function xe(){var n=Gn(["\n     background: #fff;\n    "]);return xe=function(){return n},n}var be=(0,C.rU)((function(n){n.token;var e=n.css;return{container:e($n()),titleContainer:e(Yn()),title:e(Xn()),container_input:e(Qn()),Icon:e(Jn()),IconDelete:e(ne()),selectInfo:e(ee()),findInfo:e(te()),sidePanel:e(re()),filterContainer:e(ie()),container_listInfo:e(oe()),scheme_listInfo:e(ae()),container_list:e(le()),textInfo:e(ce()),container_title:e(se()),container_desc:e(ue()),seriesStyle:e(de()),noScroll:e(fe()),side_list:e(pe()),line:e(he()),emptyInfo:e(me()),Popover_hoverInfo:e(ge()),footer:e(ve()),Popover_Info:e(xe())}})),ye=(t(78553),t(27164)),we=t(32962),_e=t(61307),Se=t(11180),je=t(86014),ke=t(23664),Ae=t(13915),Ie="Popover_Info-Vr1UD",Ce="carouselInfo-i2vhk",Ee="DetailIcon-yqCoM",De=t(33735);function Fe(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Ne(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Fe(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Fe(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Oe=function(n){(0,k.B)().t;var e=Ne((0,S.useState)(""),2),t=e[0],i=e[1],o=Ne((0,S.useState)(!1),2),a=(o[0],o[1],Ne((0,S.useState)(!1),2)),l=(a[0],a[1],function(n){i("".concat(n,"?x-oss-process=image/resize,m_fixed,h_400,w_600"))}),c=(n.noShow,n.isClick,n.seriesSample);n.height,(0,v.P)();return(0,r.jsx)(Zn.A,{placement:"right",trigger:"hover",arrow:!1,autoAdjustOverflow:!0,overlayStyle:{paddingTop:"8px",paddingBottom:"8px",paddingLeft:"21px",paddingRight:"21px"},content:function(){var n,e;return(0,r.jsxs)("div",{className:Ie,children:[(0,r.jsx)("img",{src:t||"".concat((null==c?void 0:c.roomList)?null==c||null===(e=c.roomList)||void 0===e||null===(n=e[0])||void 0===n?void 0:n.imgPath:c.thumbnail,"?x-oss-process=image/resize,m_fixed,h_400,w_600"),alt:""}),(0,r.jsx)("div",{className:Ce,children:c.roomList.length>1?(0,r.jsx)(De.A,{ImgData:c.roomList,ImgSrc:l}):null})]})},children:(0,r.jsx)(Sn.A,{className:Ee,style:{color:"#5B5E60"},type:"icon-xinxi"})})};function Me(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Pe(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Le(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Pe(o,r,i,a,l,"next",n)}function l(n){Pe(o,r,i,a,l,"throw",n)}a(void 0)}))}}function Te(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Re(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){Te(n,e,t[e])}))}return n}function ze(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,r)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function Ue(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||He(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Be(n){return function(n){if(Array.isArray(n))return Me(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||He(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function He(n,e){if(n){if("string"==typeof n)return Me(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Me(n,e):void 0}}function We(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Ke=(0,_.observer)((function(){var n,e,t=be().styles,i=(0,S.useRef)(null),o=(0,S.useRef)(null),l=(0,k.B)().t,s=(0,v.P)(),u=s.homeStore,d=u.sunDEnterOpen,f=u.setSunDEnterOpen,p=Ue((0,S.useState)(0),2),h=p[0],m=p[1],g=Ue((0,S.useState)((null===(n=s.userStore.userInfo)||void 0===n?void 0:n.isFactory)?"2":"1"),2),w=g[0],_=g[1],j=Ue((0,S.useState)(""),2),A=j[0],I=j[1],C=Ue((0,S.useState)(!1),2),E=C[0],D=C[1],F=Ue((0,S.useState)([]),2),N=F[0],O=F[1],M=Ue((0,S.useState)(!1),2),P=M[0],L=M[1],T=Ue((0,S.useState)(!1),2),R=T[0],z=T[1],U=Ue((0,S.useState)([]),2),B=U[0],H=U[1],W=Ue((0,S.useState)({}),2),K=(W[0],W[1],Ue((0,S.useState)(0),2)),q=(K[0],K[1]),V=Ue((0,S.useState)({}),2),Z=V[0],G=V[1],$=Ue((0,S.useState)([]),2),Y=$[0],X=$[1],Q=Ue((0,S.useState)(null),2),J=Q[0],nn=Q[1],en=Ue((0,S.useState)(null),2),tn=en[0],rn=en[1],on=Ue((0,S.useState)({orderBy:"sort asc",ruleType:(null===(e=s.userStore.userInfo)||void 0===e?void 0:e.isFactory)?2:1,pageSize:50,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),an=on[0],ln=on[1],cn=[1,2,3,4,5,6,7,8],sn=function(n){_(n.target.value);var e="1"==n.target.value?1:2;ln(ze(Re({},an),{pageIndex:1,ruleType:e}))},un=function(){var n=Le((function(){var n,e;return We(this,(function(t){switch(t.label){case 0:return[4,(0,Ae.kV)()];case 1:return(e=t.sent())?(null===(n=Object)||void 0===n?void 0:n.keys(e).map((function(n,e){return{id:e+1,screenName:n}})),[2]):[2]}}))}));return function(){return n.apply(this,arguments)}}(),dn=function(){var n=Le((function(){var n,e;return We(this,(function(t){switch(t.label){case 0:return[4,(0,_e.$f)()];case 1:return(n=t.sent())?(e=null==n?void 0:n.map((function(n){return{value:n.key,screenName:n.label}})),O(e),[2]):[2]}}))}));return function(){return n.apply(this,arguments)}}(),fn=function(){var n=Le((function(){var n,e,t,r;return We(this,(function(i){switch(i.label){case 0:return L(!0),n=an,[4,(0,_e.Ic)(n)];case 1:return e=i.sent(),"风格套系"===s.designStore.segmentedValue&&e&&(null==e||null===(t=e.result)||void 0===t||t.map((function(n){var e;n.roomList=null==n||null===(e=n.ruleImageList)||void 0===e?void 0:e.map((function(n){return{imgPath:n}}))}))),L(!1),(null==e?void 0:e.result)?(r=Array.isArray(B)&&B.length>0&&an.pageIndex>1?Be(B).concat(Be((null==e?void 0:e.result)||[])):(null==e?void 0:e.result)||[],H(r)):H([]),q(null==e?void 0:e.recordCount),[2]}}))}));return function(){return n.apply(this,arguments)}}(),pn=function(n,e,t){n&&n.drawOnCanvas(t,e,576,576)},hn=function(n,e){var t=c.nb.instance.painter;if(n.length>0){for(var r in n){var i=n[r];if(i._index=Number(r),!i._drawn_image){var o=document.createElement("canvas");pn(i,o,t),i._drawn_image=new Image,i._drawn_image.src=o.toDataURL(),i._drawn_image.crossOrigin="anonymous",o=null}}X(n)}};(0,S.useEffect)((function(){fn()}),[an]),(0,S.useEffect)((function(){un(),dn(),c.nb.on_M(a.U.WholeLayoutSchemeList,"sunDEnter",(function(n){(null==n?void 0:n.schemeList)?(hn(Be((null==n?void 0:n.schemeList)||[]),n.index),z(!1)):hn([])})),c.nb.on(a.U.quoteDataSeries,(function(n){if(n){var e={},t=c.nb.instance,r=ke.K.instance,i=n;if(!i)return;var o=r.makeQuoteData(t.layout_container,new Se._(i));if(!o)return;e={schemeId:t.layout_container.current_swj_layout_data.scheme_id,seriesId:i.ruleId,seriesName:"",sourceContent:JSON.stringify(o),outputType:1},mn(e),gn(e)}})),z(!0);var n=c.nb.instance.layout_container;if(n){var e=(null==n?void 0:n._whole_layout_scheme_list)||[];e&&hn(Be(e)),z(!1)}}),[]);var mn=function(){var n=Le((function(n){var e,t,r,i;return We(this,(function(o){switch(o.label){case 0:return[4,(0,je.fn)(n)];case 1:return null!=(e=o.sent())&&e.success?(t=JSON.parse(e.data),r=[],i={},t.content.map((function(n){n.itemType?r.push(n):i=Re({},i,n)})),i=ze(Re({},i),{itemPrices:r}),s.designStore.setQuoteInfo(i)):s.designStore.setQuoteInfo(null),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),gn=function(){var n=Le((function(n){var e,t;return We(this,(function(r){switch(r.label){case 0:return n.outputType=0,[4,(0,je.fn)(n)];case 1:return null!=(e=r.sent())&&e.success?(t=e.data.replace("http:","https:"),s.designStore.setQuoteExcel(t)):s.designStore.setQuoteExcel(null),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),vn={paddingTop:"8px",paddingBottom:"8px",paddingLeft:"21px",paddingRight:"21px"};return(0,S.useEffect)((function(){d||(m(0),nn(null),rn(null))}),[d]),(0,r.jsx)(b.A,{title:l(0===h?"选风格":"选布局"),open:d,onOk:function(){f(!1)},width:1e3,footer:null,destroyOnClose:!0,onCancel:function(){return f(!1)},children:function(){switch(h){case 0:var n;return(0,r.jsx)("div",{className:t.container,children:(0,r.jsxs)("div",{className:t.sidePanel,children:[(0,r.jsxs)("div",{className:t.filterContainer,children:[(0,r.jsx)("div",{children:(0,r.jsxs)(Kn.A.Group,{optionType:"button",buttonStyle:"solid",value:w,onChange:sn,children:[(0,r.jsx)(Kn.A.Button,{value:"2",children:l("企业库")}),(0,r.jsx)(Kn.A.Button,{value:"1",children:l("平台库")})]})}),(0,r.jsxs)("div",{className:t.findInfo,children:[(0,r.jsx)("input",{value:A,onKeyDown:function(n){"Enter"==n.key&&("样板间"===s.designStore.segmentedValue?ln(ze(Re({},an),{pageIndex:1,schemeKeyWord:A})):ln(ze(Re({},an),{pageIndex:1,ruleKeyWord:A})))},onChange:function(n){n.persist(),I(n.target.value)},onMouseEnter:function(){D(!0)},onMouseLeave:function(){D(!1)},className:t.container_input,placeholder:"".concat(l("请输入关键词搜索"))}),(0,r.jsx)(ye.A,{onMouseEnter:function(){D(!0)},onClick:function(){"样板间"===s.designStore.segmentedValue?ln(ze(Re({},an),{pageIndex:1,schemeKeyWord:A})):ln(ze(Re({},an),{pageIndex:1,ruleKeyWord:A}))},className:t.Icon,iconClass:"iconsearch",style:{fontSize:"20px",color:"#6C7175"}}),(0,r.jsx)(ye.A,{onMouseEnter:function(){D(!0)},onClick:function(){I(""),ln(ze(Re({},an),{pageIndex:1,schemeKeyWord:"",ruleKeyWord:""}))},className:t.IconDelete,iconClass:"iconclosecirle_fill",style:{color:"#595959",fontSize:"16px",marginTop:"2px",marginLeft:"2px",display:""!==A&&E?"block":"none"}}),(0,r.jsx)("div",{className:t.selectInfo,children:(0,r.jsx)(we.A,{ref:i,data:{screenTitle:l("风格"),screenList:N},onChange:function(n,e){"样板间"===s.designStore.segmentedValue?ln(ze(Re({},an),{pageIndex:1,schemeStyleId:null==n?void 0:n.value})):ln(ze(Re({},an),{pageIndex:1,ruleStyleId:null==n?void 0:n.value})),o.current&&(o.current.scrollTop=0)}})})]})]}),(0,r.jsx)(qn.A,{spinning:P,size:"large",style:{height:"100%"},children:(0,r.jsx)("div",{className:"".concat(t.container_listInfo),ref:o,children:B&&B.length>0?(0,r.jsx)(r.Fragment,{children:null==B||null===(n=B.map)||void 0===n?void 0:n.call(B,(function(n,e){return(0,r.jsxs)("div",{className:t.container_list,onClick:function(){nn(n)},onMouseEnter:function(){return G((function(n){return ze(Re({},n),Te({},e,!0))}))},onMouseLeave:function(){return G((function(n){return ze(Re({},n),Te({},e,!1))}))},children:[Z[e]&&(0,r.jsx)(Oe,{noShow:!0,isClick:!0,seriesSample:n,height:218}),(0,r.jsxs)("div",{className:t.Popover_hoverInfo,style:{border:(null==J?void 0:J.ruleId)===n.ruleId?"2px solid #147FFA":"2px solid #fff"},children:[(0,r.jsx)("img",{src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(0,r.jsx)("div",{className:t.container_title,title:n.seedSchemeName||n.ruleName,children:l(n.seedSchemeName)||l(n.ruleName)}),(0,r.jsxs)("div",{className:t.container_desc,children:[(0,r.jsx)("span",{className:t.seriesStyle,children:l(n.seriesStyle)}),(0,r.jsxs)("span",{style:{display:"".concat("风格套系"===s.designStore.segmentedValue?"none":"inline-block")},children:[" | ",n.schemeArea,"㎡"]})]})]})]},"series_"+e)}))}):(0,r.jsx)("div",{className:t.emptyInfo,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("div",{className:"desc",children:l("暂无数据")})]})})})}),(0,r.jsxs)("div",{className:t.footer,children:[(0,r.jsx)(y.A,{color:"default",variant:"filled",onClick:function(){f(!1)},children:l("取消")}),(0,r.jsx)(y.A,{type:"primary",onClick:function(){J?m(1):x.A.warning(l("请选择风格套系"))},children:l("下一步")})]})]})});case 1:var e;return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"".concat(t.scheme_listInfo),ref:o,children:R?cn.map((function(n,e){return(0,r.jsx)(Vn.A.Button,{style:{width:"210px",height:"264px",margin:"10px"},active:!0},e)})):Y&&Y.length>0?(0,r.jsx)(r.Fragment,{children:null==Y||null===(e=Y.map)||void 0===e?void 0:e.call(Y,(function(n,e){return(0,r.jsx)("div",{className:t.container_list,onMouseEnter:function(){return G((function(n){return ze(Re({},n),Te({},e,!0))}))},onMouseLeave:function(){return G((function(n){return ze(Re({},n),Te({},e,!1))}))},onClick:function(){rn(n)},children:(0,r.jsx)(Zn.A,{placement:"right",trigger:"hover",arrow:!1,autoAdjustOverflow:!0,overlayStyle:vn,content:function(){return(0,r.jsx)("div",{className:t.Popover_Info,children:(0,r.jsx)("img",{style:{width:"600px",height:"600px"},src:n._drawn_image.src,alt:""})})},children:(0,r.jsxs)("div",{className:t.Popover_hoverInfo,style:{border:(null==tn?void 0:tn._index)===(null==n?void 0:n._index)?"2px solid #147FFA":"2px solid #fff"},children:[(0,r.jsx)("img",{style:{background:"#F4F5F5"},src:n._drawn_image.src,alt:""}),(0,r.jsx)("div",{className:t.container_title,children:(0,r.jsx)("div",{children:l("全屋方案")+(n._index+1)})}),(0,r.jsx)("div",{className:t.container_desc})]})})},"whole_scheme_list_"+e)}))}):(0,r.jsxs)("div",{className:t.emptyInfo,children:[(0,r.jsx)("div",{children:(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""})}),(0,r.jsx)("div",{className:"desc",children:l("请先打开【文件】中的户型或方案")})]})}),(0,r.jsxs)("div",{className:t.footer,children:[(0,r.jsx)(y.A,{color:"default",variant:"filled",onClick:function(){m(0)},children:l("上一步")}),(0,r.jsx)(y.A,{type:"primary",onClick:function(){tn?(f(!1),c.nb.DispatchEvent(c.n0.ClickWholeLayoutScheme,{value:tn,index:null==tn?void 0:tn._index}),c.nb.DispatchEvent(c.n0.SeriesSampleSelected,{series:J,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}})):x.A.error(l("请选择布局"))},children:l("确定")})]})]})}}()})})),qe=t(34447),Ve=t(88454),Ze=t(38447),Ge="container-wOo07",$e="apply_category_button_container-HqmkU",Ye="apply_category_button-cJ_lT",Xe="checked-hQlaM",Qe="apply_all_button-oUpuo";function Je(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function nt(){var n=Je(["\n      background: #FFF;\n      height: 100%;\n      z-index: 999;\n      padding: 0 0 0 12px;\n    "]);return nt=function(){return n},n}function et(){var n=Je(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    "]);return et=function(){return n},n}function tt(){var n=Je(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      line-height: 1.4;\n      letter-spacing: 0px;\n      text-align: left;\n      font-weight: 600;\n      margin: 16px 0px;\n    "]);return tt=function(){return n},n}function rt(){var n=Je(["\n      border-radius: 30px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      min-width: 70%;\n      height: 32px;\n      border: none;\n      margin: 16px 0 0 0px;\n      padding-left: 30px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return rt=function(){return n},n}function it(){var n=Je(["\n      position: absolute;\n      top: 53px;\n      left: 7px;\n    "]);return it=function(){return n},n}function ot(){var n=Je(["\n      position: absolute;\n      top: 53px;\n      right: 38%;\n      cursor: pointer;\n    "]);return ot=function(){return n},n}function at(){var n=Je(["\n      width: 24%;\n      margin: 16px 8px 0px 0;\n      display: flex;\n      justify-items: baseline;\n      align-items: center;\n      a {\n        color: #ffffff;\n        padding: 5px;\n        line-height: 23px;\n        height: 34px;\n      }\n      a:hover {\n        color: #3D9EFF;\n        border-radius: 10px;\n        background: #BFD8FF14;\n        transition: all .3s;\n      }\n    "]);return at=function(){return n},n}function lt(){var n=Je(["\n      display: flex;\n      justify-content: space-between;\n      padding-right: 28px;\n      margin-bottom: 16px;\n    "]);return lt=function(){return n},n}function ct(){var n=Je(["\n      height: 100%;\n      padding-top: 12px;\n    "]);return ct=function(){return n},n}function st(){var n=Je(["\n      overflow-y: hidden;\n      height: calc(100% - 100px);\n      padding-right: 12px;\n      box-sizing: border-box;\n      overflow-y: auto;\n      :hover {\n        /* overflow-y: auto; */\n        /* padding-right: 12px; */\n      }\n    "]);return st=function(){return n},n}function ut(){var n=Je(["\n      width: 100%;\n      height: 263px;\n      padding: 2px;\n      box-sizing: border-box;\n      position: relative;\n    "]);return ut=function(){return n},n}function dt(){var n=Je(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin: 8px 0 4px 0;\n    "]);return dt=function(){return n},n}function ft(){var n=Je(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 80%;\n    "]);return ft=function(){return n},n}function pt(){var n=Je(["\n      color: #6C7175;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n    "]);return pt=function(){return n},n}function ht(){var n=Je(["\n      color: #5B5E60;\n      font-family: PingFang SC;\n      font-size: 12px;\n      letter-spacing: 0px;\n      text-align: left;\n      background-color: #F2F3F5;\n      width: auto;\n      border-radius: 2px;\n      padding: 2px 8px;\n      display: block;\n      white-space: nowrap;\n    "]);return ht=function(){return n},n}function mt(){var n=Je(["\n      overflow-y: hidden !important;\n    "]);return mt=function(){return n},n}function gt(){var n=Je(["\n      width: 100%;\n      height: 98vh;\n      overflow: auto;\n      position: absolute;\n      left: 0;\n      canvas {\n        margin-left:5px;\n        margin-top:5px;\n        cursor : pointer;\n      }\n      canvas:hover {\n        background:rgba(127,127,255,0.5);\n      }\n    "]);return gt=function(){return n},n}function vt(){var n=Je(["\n      height: 100%;\n      position: absolute;\n      right: 0;\n      top: 0;\n      width: 4px;\n      cursor: col-resize;\n      z-index: 998;\n    "]);return vt=function(){return n},n}function xt(){var n=Je(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 100%;\n      img\n      {\n        width: 120px;\n        height: 120px;\n      }\n      .desc\n      {\n        text-align: center;\n        margin-top: 10px;\n        color: #A2A2A5;\n        font-size: 12px;\n      }\n    "]);return xt=function(){return n},n}function bt(){var n=Je(["\n      border-radius: 4px;\n      height: 218px;\n      overflow: hidden;\n      img {\n        transition: all .5s;\n        width: 100%;\n        height: 100%;\n      }\n\n      :hover{\n        outline: 2px solid #147FFA;\n      }\n    "]);return bt=function(){return n},n}var yt=(0,C.rU)((function(n){var e=n.css;return{container:e(nt()),titleContainer:e(et()),title:e(tt()),container_input:e(rt()),Icon:e(it()),IconDelete:e(ot()),selectInfo:e(at()),findInfo:e(lt()),sidePanel:e(ct()),container_listInfo:e(st()),container_list:e(ut()),textInfo:e(dt()),container_title:e(ft()),container_desc:e(pt()),seriesStyle:e(ht()),noScroll:e(mt()),side_list:e(gt()),line:e(vt()),emptyInfo:e(xt()),Popover_hoverInfo:e(bt())}}));function wt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function _t(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function St(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){_t(o,r,i,a,l,"next",n)}function l(n){_t(o,r,i,a,l,"throw",n)}a(void 0)}))}}function jt(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function kt(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){jt(n,e,t[e])}))}return n}function At(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,r)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function It(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||Et(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ct(n){return function(n){if(Array.isArray(n))return wt(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||Et(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Et(n,e){if(n){if("string"==typeof n)return wt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?wt(n,e):void 0}}function Dt(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Ft=(0,_.observer)((function(){var n,e,t,i=(0,k.B)().t,o=(0,v.P)(),l=yt().styles,s=(0,S.useContext)(qe.DesignContext),u=(s.waitingToFurnishRemaining,s.setWaitingToFurnishRemaining,Ve.A,It((0,S.useState)(!1),2)),d=(u[0],u[1]),f=It((0,S.useState)(!1),2),p=f[0],h=f[1],g=It((0,S.useState)(0),2),x=g[0],b=g[1],y=It((0,S.useState)(!1),2),w=y[0],_=y[1],j=(0,S.useRef)(null),A=(0,S.useRef)(null),I=((0,S.useRef)(null),(0,S.useRef)(null)),C=It((0,S.useState)([]),2),E=C[0],D=C[1],F=It((0,S.useState)([]),2),N=F[0],O=F[1],M=It((0,S.useState)([]),2),P=(M[0],M[1]),L=It((0,S.useState)(""),2),T=L[0],R=L[1],z=It((0,S.useState)(!1),2),U=z[0],B=z[1],H=It((0,S.useState)(["全案风格"]),2),W=H[0],K=H[1],q=It((0,S.useState)((0,m.fZ)()?260:360),2),V=q[0],Z=q[1],G=It((0,S.useState)((null===(n=o.userStore.userInfo)||void 0===n?void 0:n.isFactory)?"2":"1"),2),$=G[0],Y=G[1],X=It((0,S.useState)(!1),2),Q=(X[0],X[1],It((0,S.useState)({}),2)),J=Q[0],nn=Q[1],en=It((0,S.useState)({}),2),tn=en[0],rn=en[1],on=It((0,S.useState)({orderBy:"sort asc",ruleType:(null===(e=o.userStore.userInfo)||void 0===e?void 0:e.isFactory)?2:1,pageSize:50,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),an=on[0],ln=on[1],cn=function(){var n=St((function(){var n,e,t,r;return Dt(this,(function(i){switch(i.label){case 0:return h(!0),_(!0),n=an,[4,(0,_e.Ic)(n)];case 1:return e=i.sent(),"风格套系"===o.designStore.segmentedValue&&e&&(null==e||null===(t=e.result)||void 0===t||t.map((function(n){var e;n.roomList=null==n||null===(e=n.ruleImageList)||void 0===e?void 0:e.map((function(n){return{imgPath:n}}))}))),_(!1),h(!1),(null==e?void 0:e.result)?(r=Array.isArray(E)&&E.length>0&&an.pageIndex>1?Ct(E).concat(Ct((null==e?void 0:e.result)||[])):(null==e?void 0:e.result)||[],D(r)):D([]),d(!1),b(null==e?void 0:e.recordCount),[2]}}))}));return function(){return n.apply(this,arguments)}}(),sn=function(){var n=St((function(n){var e;return Dt(this,(function(t){return e=n.target,Math.ceil(e.scrollTop)+e.clientHeight+5>=e.scrollHeight&&x>(null==E?void 0:E.length)&&!w&&ln(At(kt({},an),{pageIndex:an.pageIndex+1})),[2]}))}));return function(e){return n.apply(this,arguments)}}(),un=function(){var n=St((function(){var n,e,t;return Dt(this,(function(r){switch(r.label){case 0:return[4,(0,Ae.kV)()];case 1:return(e=r.sent())?(t=null===(n=Object)||void 0===n?void 0:n.keys(e).map((function(n,e){return{id:e+1,screenName:n}})),P(t),[2]):[2]}}))}));return function(){return n.apply(this,arguments)}}(),dn=function(){var n=St((function(){var n,e;return Dt(this,(function(t){switch(t.label){case 0:return[4,(0,_e.$f)()];case 1:return(n=t.sent())?(e=null==n?void 0:n.map((function(n){return{value:n.key,screenName:n.label}})),O(e),[2]):[2]}}))}));return function(){return n.apply(this,arguments)}}();(0,S.useEffect)((function(){cn()}),[an]),(0,S.useEffect)((function(){var n=function(n){n.ctrlKey&&"q"===n.key.toLowerCase()&&K("全案风格"===W[0]?["风格套系","样板间"]:["全案风格"])};return window.addEventListener("keydown",n),function(){window.removeEventListener("keydown",n)}}),[W]),(0,S.useEffect)((function(){d(!0),un(),dn(),c.nb.on(a.U.quoteDataSeries,(function(n){if(n){var e={},t=c.nb.instance,r=ke.K.instance,i=n;if(!i)return;var o=r.makeQuoteData(t.layout_container,new Se._(i));if(!o)return;e={schemeId:t.layout_container.current_swj_layout_data.scheme_id,seriesId:i.ruleId,seriesName:"",sourceContent:JSON.stringify(o),outputType:1},bn(e),yn(e)}}))}),[]);var fn=(0,S.useRef)(null),pn=!1,hn=0,mn=0,gn=function(n){pn=!0,hn=n.clientX,mn=fn.current.offsetWidth},vn=function(n){if(pn){var e=n.clientX-hn,t=mn+e;t>480&&(t=480),t<360&&(t=360),Z(t)}},xn=function(){pn=!1},bn=function(){var n=St((function(n){var e,t,r,i;return Dt(this,(function(a){switch(a.label){case 0:return[4,(0,je.fn)(n)];case 1:return null!=(e=a.sent())&&e.success?(t=JSON.parse(e.data),r=[],i={},t.content.map((function(n){n.itemType?r.push(n):i=kt({},i,n)})),i=At(kt({},i),{itemPrices:r}),o.designStore.setQuoteInfo(i)):o.designStore.setQuoteInfo(null),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),yn=function(){var n=St((function(n){var e,t;return Dt(this,(function(r){switch(r.label){case 0:return n.outputType=0,[4,(0,je.fn)(n)];case 1:return null!=(e=r.sent())&&e.success?(t=e.data.replace("http:","https:"),o.designStore.setQuoteExcel(t)):o.designStore.setQuoteExcel(null),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),wn=function(n,e,t,r){o.schemeStatusStore.layoutSchemeSaved=!1,o.schemeStatusStore.pendingOpenSchemeIn3D=!1,c.nb.DispatchEvent(c.n0.SeriesSampleSelected,{series:n,scope:{soft:e,hard:t,cabinet:r,remaining:!1}}),o.homeStore.selectData&&o.homeStore.selectData.rooms&&_n(o.homeStore.selectData.rooms)},_n=function(n){var e=tn;if(e={},null!=n){var t=!0,r=!1,i=void 0;try{for(var o,a=n[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var l=o.value;if(l._scope_series_map)for(var c in l._scope_series_map){var s=l._scope_series_map[c];s&&s.ruleId&&(e[s.ruleId]||(e[s.ruleId]={}),e[s.ruleId][c]=!0)}}}catch(n){r=!0,i=n}finally{try{t||null==a.return||a.return()}finally{if(r)throw i}}}rn(e)},Sn=function(n,e){return tn[n.ruleId]&&tn[n.ruleId][e]};return(0,S.useEffect)((function(){c.nb.on_M(a.U.SelectingRoom,"SeriesCandidate",(function(n){_n(n.current_rooms||[])}));var n=I.current;if(n)return n.addEventListener("mousedown",gn),window.addEventListener("mousemove",vn),window.addEventListener("mouseup",xn),function(){n.removeEventListener("mousemove",vn),window.removeEventListener("mousedown",gn),window.removeEventListener("mouseup",xn)}}),[]),o.homeStore.preview3D?(0,r.jsx)("div",{}):(0,r.jsx)("div",{className:"".concat(l.container," ").concat(Ge),style:{width:280},ref:fn,children:(0,r.jsxs)("div",{className:l.sidePanel,children:[(0,r.jsxs)("div",{style:{position:"relative"},children:[(0,r.jsxs)(Kn.A.Group,{value:$,onChange:function(n){Y(n.target.value);var e="1"==n.target.value?1:2;ln(At(kt({},an),{pageIndex:1,ruleType:e})),j.current&&(j.current.scrollTop=0)},children:[(0,r.jsx)(Kn.A.Button,{value:"2",children:i("企业")}),(0,r.jsx)(Kn.A.Button,{value:"1",children:i("平台")})]}),(0,r.jsx)("div",{style:{width:"248px"}}),(0,r.jsxs)("div",{className:l.findInfo,children:[(0,r.jsx)("input",{value:T,onKeyDown:function(n){"Enter"==n.key&&("样板间"===o.designStore.segmentedValue?ln(At(kt({},an),{pageIndex:1,schemeKeyWord:T})):ln(At(kt({},an),{pageIndex:1,ruleKeyWord:T})))},onChange:function(n){R(n.currentTarget.value)},onMouseEnter:function(){B(!0)},onMouseLeave:function(){B(!1)},className:l.container_input,placeholder:"".concat(i("请输入关键词搜索"))}),(0,r.jsx)(ye.A,{onMouseEnter:function(){B(!0)},onClick:function(){"样板间"===o.designStore.segmentedValue?ln(At(kt({},an),{pageIndex:1,schemeKeyWord:T})):ln(At(kt({},an),{pageIndex:1,ruleKeyWord:T}))},className:l.Icon,iconClass:"iconsearch",style:{fontSize:"20px",color:"#6C7175"}}),(0,r.jsx)(ye.A,{onMouseEnter:function(){B(!0)},onClick:function(){R(""),ln(At(kt({},an),{pageIndex:1,schemeKeyWord:"",ruleKeyWord:""}))},className:l.IconDelete,iconClass:"iconclosecirle_fill",style:{color:"#595959",fontSize:"16px",marginTop:"2px",marginLeft:"2px",display:""!==T&&U?"block":"none"}}),(0,r.jsx)("div",{className:l.selectInfo,children:(0,r.jsx)(we.A,{ref:A,data:{screenTitle:i("风格"),screenList:N},onChange:function(n,e){"样板间"===o.designStore.segmentedValue?ln(At(kt({},an),{pageIndex:1,schemeStyleId:null==n?void 0:n.value})):ln(At(kt({},an),{pageIndex:1,ruleStyleId:null==n?void 0:n.value})),j.current&&(j.current.scrollTop=0)}})})]})]}),(0,r.jsx)(qn.A,{spinning:w,size:"large",style:{height:"100%"},children:(0,r.jsx)("div",{className:"".concat(l.container_listInfo," ").concat(p?l.noScroll:""),onScroll:sn,ref:j,children:E&&E.length>0?(0,r.jsx)(r.Fragment,{children:null==E||null===(t=E.map)||void 0===t?void 0:t.call(E,(function(n,e){return(0,r.jsxs)("div",{className:l.container_list,style:{height:V/360*218},onMouseEnter:function(){return nn((function(n){return At(kt({},n),jt({},e,!0))}))},onMouseLeave:function(){return nn((function(n){return At(kt({},n),jt({},e,!1))}))},children:[J[e]&&(0,r.jsx)(Ze.A,{noShow:!0,isClick:!0,seriesSample:n,height:170}),(0,r.jsxs)("div",{className:l.Popover_hoverInfo,style:{height:170},children:[(0,r.jsx)("img",{src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_170,w_318"),alt:""}),(J[e]||(0,m.fZ)())&&(0,r.jsx)("button",{onClick:function(){return wn(n,!0,!0,!0)},className:Qe,children:i("全部应用")})]}),(0,r.jsxs)("div",{className:$e,children:[(0,r.jsx)("div",{onClick:function(){return wn(n,!1,!1,!0)},className:Ye+" "+(Sn(n,"cabinet")?Xe:""),children:i("定制")}),(0,r.jsx)("div",{onClick:function(){return wn(n,!0,!1,!1)},className:Ye+" "+(Sn(n,"soft")?Xe:""),children:i("软装")}),(0,r.jsx)("div",{onClick:function(){return wn(n,!1,!0,!1)},className:Ye+" "+(Sn(n,"hard")?Xe:""),children:i("硬装")})]}),(0,r.jsxs)("div",{className:l.textInfo,children:[(0,r.jsx)("div",{className:l.container_title,title:n.seedSchemeName||n.ruleName,children:i(n.seedSchemeName)||i(n.ruleName)}),(0,r.jsxs)("div",{className:l.container_desc,children:[(0,r.jsx)("span",{className:l.seriesStyle,children:i(n.seriesStyle)}),(0,r.jsxs)("span",{style:{display:"".concat("风格套系"===o.designStore.segmentedValue?"none":"inline-block")},children:[" | ",n.schemeArea,"㎡"]})]})]})]},e)}))}):(0,r.jsx)("div",{className:l.emptyInfo,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("div",{className:"desc",children:i("暂无数据")})]})})})})]})})}));function Nt(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Ot(){var n=Nt(["\n    @keyframes fade-in {\n      0% { opacity: 0; }\n      100% { opacity: 1; }\n    }\n  "]);return Ot=function(){return n},n}function Mt(){var n=Nt(["\n      left: 0;\n      position: fixed;\n      color: #6c7175;\n      background-color: #FFF;\n      width: 288px;\n      z-index: 999;\n      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.16);\n      height: 100%;\n    "]);return Mt=function(){return n},n}function Pt(){var n=Nt(["\n      color: #000;\n      font-weight: bold;\n      font-size: 20px;\n      line-height: 1.67;\n      padding: 14px 0 16px 16px;\n      height: 60px;\n      background-color: #fff;\n      width: 100%;\n    "]);return Pt=function(){return n},n}function Lt(){var n=Nt(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 850px;\n      color: #6c7175;\n      text-align: center;\n      flex-direction: column;\n      img{\n        width: 80px;\n        height: 80px;\n      }\n    "]);return Lt=function(){return n},n}function Tt(){var n=Nt(["\n      margin-bottom: '12px';\n      img {\n        cursor : pointer;\n        box-sizing: border-box;\n        width: 300px;\n      }\n    "]);return Tt=function(){return n},n}function Rt(){var n=Nt(["    \n      margin-top: 15px;\n      font-size: 12px;\n      line-height: 18px;\n      color: #A2A2A5;\n    "]);return Rt=function(){return n},n}function zt(){var n=Nt(["\n      position:absolute;\n    "]);return zt=function(){return n},n}function Ut(){var n=Nt(["\n    color: #282828;\n    font-size: 20px;\n    font-weight: 600;\n    margin-bottom: 16px;\n    padding-left: 12px;\n    "]);return Ut=function(){return n},n}function Bt(){var n=Nt(["\n      height: calc(100vh - 165px) !important;\n      min-width: 220px !important;\n      img{\n        height: 216px !important;\n      }\n    "]);return Bt=function(){return n},n}function Ht(){var n=Nt(["\n      width: 100%;\n      min-width: 280px;\n      background-color: #FFF;\n      height: calc(100vh - 100px);\n      overflow-y: scroll;\n      text-align: center;\n      padding: 2px 6px 2px 12px;\n      canvas {\n        cursor : pointer;\n        box-sizing: border-box;\n        width: 100%;\n        padding: 10px;\n        background-color: #F2F3F5; \n      }\n      canvas:hover {\n        outline: 2px solid #9242FB;\n      }\n      .iconfont {\n        width:20px;\n        height:20px;\n        line-height:20px;\n        float:right;\n        color:#1790ff;\n        font-weight: 100;\n        cursor:pointer;\n\n      }\n      .mobile\n      {\n        height: 216px !important;\n      }\n      img {\n        cursor : pointer;\n        box-sizing: border-box;\n        width: 100%;\n        height: 258px;\n        background-color: #F2F3F5; \n      }\n      img:hover {\n        outline: 2px solid #9242FB;\n      }\n      .emptyImg\n      {\n        height: 80px !important;\n        width: 80px !important;\n        display: flex;\n        justify-content: center;\n        border-radius: 50%;\n      }\n      > div {\n          position: relative;\n      }\n\n\n    "]);return Ht=function(){return n},n}function Wt(){var n=Nt(["\n      height: 300px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 250px;\n      }\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 300px);\n        min-width: 224px;\n      }\n    "]);return Wt=function(){return n},n}function Kt(){var n=Nt(["\n      height: 56px;\n      padding-top: 16px;\n      @media screen and (orientation: landscape) {\n        height: 38px;\n        padding-top: 0px;\n      }\n    "]);return Kt=function(){return n},n}function qt(){var n=Nt(["\n        width: 100%;\n        height:100%;\n        background-color: #FFF;\n        overflow-x: scroll;\n        overflow-y:hidden;\n        text-align: center;\n        display:flex;\n        @media screen and (orientation: landscape) {\n          overflow-y:scroll !important;\n          /* width: 200px !important; */\n          height: calc(var(--vh, 1vh) * 100 - 160px);\n          display: block !important;\n          padding: 0 4px;\n          ::-webkit-scrollbar {\n            display: none; /* 隐藏滚动条 */\n          }\n        }\n        .scheme_div {\n          width: 200px;\n          margin: 8px 8px;\n          position: relative;\n          flex: 0 0 auto;\n          @media screen and (max-width: 450px) { // 手机宽度\n            width: 150px;\n          }\n        }\n        .scheme_name {\n          margin: 4px 0px;\n          .ant-rate\n          {\n            font-size: 16px !important;\n          }\n          @media screen and (max-width: 450px) {\n            .ant-rate\n            {\n              font-size: 12px !important;\n              .ant-rate-star:not(:last-child)\n              {\n                margin-inline-end: 3px;\n              }\n            }\n          }\n        }\n        .iconfont {\n          width:20px;\n          height:20px;\n          line-height:20px;\n          float:right;\n          color:#1790ff;\n          font-weight: 100;\n          cursor:pointer;\n  \n        }\n\n        img {\n          cursor : pointer;\n          box-sizing: border-box;\n          background-color: #F2F3F5; \n          width: 100%;\n          border-radius: 2px;\n          padding: 15px;\n        }\n        .emptyImg\n        {\n          height: 80px !important;\n          width: 80px !important;\n          display: flex;\n          justify-content: center;\n          border-radius: 50%;\n        }\n    "]);return qt=function(){return n},n}function Vt(){var n=Nt(["\n      outline: 2px solid #9242FB;\n    "]);return Vt=function(){return n},n}function Zt(){var n=Nt(["\n      position: absolute;\n      top: 4px;\n      right: 4px;\n      border-radius: 4px;\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n      padding: 5px 4px;\n    "]);return Zt=function(){return n},n}function Gt(){var n=Nt(["\n      display: none;\n    "]);return Gt=function(){return n},n}function $t(){var n=Nt(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-size: 14px;\n      line-height: 1.57;\n      letter-spacing: 0px;\n      text-align: left;\n      font-weight: 600;\n      margin: 8px 0px;\n      text-align: center;\n      user-select:text;\n    "]);return $t=function(){return n},n}function Yt(){var n=Nt(["\n      display: flex;\n      padding: 0px 10px;\n      justify-content: space-between;\n    "]);return Yt=function(){return n},n}function Xt(){var n=Nt(["\n      height: 100%;\n      position: absolute;\n      right: 0;\n      position: absolute;\n      top: 0;\n      width: 4px;\n      cursor: col-resize;\n      z-index: 998;\n    "]);return Xt=function(){return n},n}function Qt(){var n=Nt(["\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      img{\n        width: 80px;\n        height: 80px;\n        border-radius: 50%;\n        margin: auto;\n      }\n    "]);return Qt=function(){return n},n}function Jt(){var n=Nt(["\n      text-align: center;\n    "]);return Jt=function(){return n},n}function nr(){var n=Nt(["\n      font-size: 12px;\n      color: #6c7175;\n      margin-top: 10px;\n      color: #A2A2A5;\n      line-height: 18px;\n    "]);return nr=function(){return n},n}function er(){var n=Nt(["\n      position: absolute;\n      top: 0px;\n      left: 0px;\n      background-color: red;\n      color: #fff;\n      width: 25px;\n      height: 25px;\n      line-height: 25px;\n      border-bottom-right-radius: 45%;\n    "]);return er=function(){return n},n}var tr=(0,C.rU)((function(n,e){n.token;var t=n.css;t(Ot());return{left_panel:t(Mt()),left_panel_title:t(Pt()),left_panel_placeholder:t(Lt()),left_panel_placeholder_image:t(Tt()),left_panel_placeholder_text:t(Rt()),left_panel_container:t(zt()),title:t(Ut()),mobile:t(Bt()),left_panel_layout_list:t(Ht()),bottom_panel_container:t(Wt()),roomListBar:t(Kt()),bottom_panel_layout_list:t(qt()),active:t(Vt()),activeTitle:t(Zt()),activeTitleNone:t(Gt()),_scheme_name:t($t()),star_name:t(Yt()),line:t(Xt()),letfEmpty:t(Qt()),letfEmptyItem:t(Jt()),text:t(nr()),difference:t(er())}})),rr=t(33313),ir=t(62837);function or(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ar(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return or(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return or(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var lr=function(){var n=(0,rr.A)().styles,e=c.nb.t,t=ar((0,S.useState)(0),2),i=t[0],o=t[1],l=ar((0,S.useState)([]),2),s=l[0],u=l[1];return(0,S.useEffect)((function(){c.nb.on(a.U.ShowPopUpLayoutScore,(function(n){o(n.top);var t=n.scheme;if(null==t?void 0:t.layout_score_dict){var r=Object.keys(t.layout_score_dict).map((function(n){return t.layout_score_dict[n]})),i=0;r.forEach((function(n){n.children,i+=n.score})),c.nb.IsDebug&&r.push({name:e("总分"),score:i,ui_format:[on.td.Name,on.td.Percentage,on.td.Grade]}),u(r)}else u([])}))}),[]),(0,r.jsx)("div",{className:n.schemeLayoutScorePopUp,style:{top:i+"px",display:s.length>0?"block":"none",minWidth:"300px",minHeight:"400px"},children:(0,r.jsx)(ir.A,{layoutScoreList:s,style:c.nb.IsDebug?0:1})})},cr=t(75206),sr=t.n(cr),ur=t(46909),dr=t(1870),fr=t(42751),pr=t(59525),hr=t(62867),mr=t(32184),gr=t(65640);function vr(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function xr(n){return function(n){if(Array.isArray(n))return vr(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||function(n,e){if(!n)return;if("string"==typeof n)return vr(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return vr(n,e)}(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var br=function(n,e,t){n&&n.drawOnCanvas(t,e,576,576)},yr={},wr=(0,_.observer)((function(n){var e=(0,v.P)(),t=(0,S.useRef)(null),i=tr(n.width).styles,o=n.showSchemeName||!1,l=(0,k.B)().t,s=function(n){var e=t.current;e&&(e.querySelectorAll("img").forEach((function(e,t){e.className=t==n?i.active:""})),e.querySelectorAll("#active_div").forEach((function(e,t){e.className=t==n?i.activeTitle:i.activeTitleNone})))},u=function(n,e){var u,d;if(t.current){t.current.scrollTop=0;var f=t.current;f.innerHTML="",yr={room_scheme_list:n,room_scheme_index:e};var p=c.nb.instance.painter;n.forEach((function(n){var e=0;n._layout_scores.forEach((function(n){e+=n.score})),n.totalScore=e}));var h=null===(d=n[0])||void 0===d||null===(u=d._scheme_name)||void 0===u?void 0:u.includes("DIY"),m=h?n.shift():null;if(h&&m&&n.unshift(m),n.length>0){var g=function(t){var u=n[t],d=document.createElement("canvas");u._drawn_image||(br(u,d,p),u._drawn_image=new Image,u._drawn_image.src=d.toDataURL(),u._drawn_image.crossOrigin="anonymous");var h=u._drawn_image;h.className=~~t==e?i.active:"",h.onclick=function(){var n,e;c.nb.DispatchEvent(c.n0.ClickLayoutScheme,{value:u,index:~~t}),c.nb.emit_M(a.U.OnAILayoutSchemeSelected,{value:u,index:~~t}),s(~~t),1==(null===(n=pr.y.instance.current_rooms)||void 0===n?void 0:n.length)&&(null===(e=pr.y.instance.current_rooms[0])||void 0===e?void 0:e._series_sample_info)&&c.nb.instance.layout_container.drawing_figure_mode!==mr.qB.Figure2D&&c.nb.DispatchEvent(c.n0.SeriesSampleSelected,{series:pr.y.instance.current_rooms[0]._series_sample_info,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}})},h.onpointermove=function(n){n.stopPropagation()},d=null;var m=document.createElement("div");m.className="scheme_div",m.appendChild(u._drawn_image);var g=u._drawn_image;g.id="layout_scheme_cavas_"+t,g.className=~~t==e?i.active:"";var v=document.createElement("div");v.innerHTML=l("正在使用"),v.className=~~t==e?i.activeTitle:i.activeTitleNone,v.id="active_div",f.appendChild(m);var x=document.createElement("div");x.className=i._scheme_name+" scheme_name",x.innerHTML="[".concat(l(u.room.name),"] ").concat(u._scheme_name.includes("DIY")?l(u._scheme_name):l("方案")+(1+~~t)," ")+"".concat(o&&c.nb.IsDebug&&u._scheme_name||"");var b=null,y=document.createElement("div");if((0,dr.MP)([u.room.roomname],["卧室","厨房","卫生间","客餐厅","入户花园"])){var w=0,_=0;u._layout_scores.forEach((function(n){w+=n.grade}));var S=u._layout_scores.some((function(n){return n.children&&n.children.length>0?n.children.some((function(n){return n.score<=-100})):n.score<=-100}));w&&(_=Math.round(w/u._layout_scores.length)),(on.fT[_]<2||S)&&(y.className=i.difference,y.innerHTML=l("差")),x.classList.add(i.star_name),b=document.createElement("div"),sr().createRoot(b).render((0,r.jsx)(rn.A,{disabled:!0,allowHalf:!0,value:on.fT[_]}));var j="".concat(u._scheme_name.includes("DIY")?l(u._scheme_name):l("方案")+(1+~~t)," ")+"".concat(o&&c.nb.IsDebug&&u._scheme_name||"");x.innerHTML="";var k=document.createElement("div");sr().createRoot(k).render((0,r.jsx)(ur.A,{title:j,children:(0,r.jsx)("span",{onDoubleClick:function(){var n;if(null===(n=navigator)||void 0===n?void 0:n.clipboard){var e=j,t=e.indexOf("相似");t>=0&&(e=e.substring(t+2)),navigator.clipboard.writeText(j)}},children:j})})),b.onclick=function(n){var e=n.currentTarget.getBoundingClientRect(),t=e.top+e.height/2-425,r=document.documentElement.clientHeight-680,i=Math.max(-55,Math.min(t,r));c.nb.emit(a.U.ShowPopUpLayoutScore,{scheme:u,top:i})},b.onmouseenter=function(n){var e=n.currentTarget.getBoundingClientRect(),t=e.top+e.height/2-425,r=document.documentElement.clientHeight-680,i=Math.max(-55,Math.min(t,r));c.nb.emit(a.U.ShowPopUpLayoutScore,{scheme:u,top:i})},b.onpointerleave=function(){c.nb.emit(a.U.ShowPopUpLayoutScore,{scheme:null,top:Math.max(m.offsetTop-f.scrollTop,0)})},x.appendChild(k),x.appendChild(b)}m.appendChild(x),m.appendChild(y),m.appendChild(v)};for(var v in n)g(v);c.nb.emit(a.U.ShowPopUpLayoutScore,{scheme:null,top:0})}else f.innerHTML="\n      <div class=".concat(i.letfEmpty,">\n          <div class=").concat(i.letfEmptyItem,">\n            <img class='emptyImg' src=","https://3vj-fe.3vjia.com/layoutai/image/Empty.png",' alt="" />\n            <div class=').concat(i.text,">\n              ").concat(l("暂无内容"),"，").concat(l("请选择其他空间"),"\n            </div>\n          </div>\n      </div>\n  ")}},d=function(n,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(t.current){t.current.scrollTop=0,yr={whole_scheme_list:n,whole_scheme_index:e};var u=t.current;u.innerHTML="";var d=c.nb.instance.painter;if(n.length>0){var f=function(e){var t=n[e];if(!t._drawn_image||r){var f=document.createElement("canvas");br(t,f,d),t._drawn_image=new Image,t._drawn_image.src=f.toDataURL(),t._drawn_image.crossOrigin="anonymous",t._drawn_image.onclick=function(){var n,r;c.nb.DispatchEvent(c.n0.ClickWholeLayoutScheme,{value:t,index:~~e}),c.nb.emit_M(a.U.OnAILayoutSchemeSelected,{value:t,index:~~e}),s(~~e),(null===(n=pr.y.instance.current_rooms)||void 0===n?void 0:n.length)>1&&(null===(r=pr.y.instance.current_rooms[0])||void 0===r?void 0:r._series_sample_info)&&c.nb.instance.layout_container.drawing_figure_mode!==mr.qB.Figure2D&&c.nb.DispatchEvent(c.n0.SeriesSampleSelected,{series:pr.y.instance.current_rooms[0]._series_sample_info,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}})},f=null}var p=document.createElement("div");p.className="scheme_div",p.appendChild(t._drawn_image);var h=t._drawn_image;h.id="layout_whole_scheme_cavans_"+e,h.className=(0,m.fZ)()?"mobile":"",u.appendChild(p);var g=document.createElement("div");g.className=i._scheme_name+" scheme_name",g.innerHTML="".concat(l("全屋")).concat(t._scheme_name.includes("DIY")?l(t._scheme_name):"".concat(l("方案"))+(1+~~e))+"".concat(o&&c.nb.IsDebug&&t._scheme_name||""),p.appendChild(g)};for(var p in n)f(p)}else u.innerHTML="\n      <div class=".concat(i.letfEmpty,">\n          <div class=").concat(i.letfEmptyItem,">\n            <img class='emptyImg' src=","https://3vj-fe.3vjia.com/layoutai/image/Empty.png",' alt="" />\n            <div class=').concat(i.text,">\n              ").concat(l("暂无全屋")).concat(l("推荐内容"),"\n            </div>\n          </div>\n      </div>\n  ")}},f=function(n,e){var t,r;if(hr.a.applySubAreaScheme(n,e),null===(r=n._area_entity)||void 0===r||null===(t=r.room_entity)||void 0===t?void 0:t._room){var i=n._area_entity.room_entity._room;gr.log(i.furnitureList),i._series_sample_info&&pr.y.instance.onSeriesSampleSelected({soft:!0,hard:!0,cabinet:!0,remaining:!1},i._series_sample_info,[i])}},p=function(n,e){if(t.current){t.current.scrollTop=0,yr={subarea_scheme_list:n,subarea_scheme_index:e};var r=t.current;r.innerHTML="";var a=c.nb.instance.painter;if(n.length>0){var u=function(t){var u=n[t];if(!u._drawn_image){var d=document.createElement("canvas");br(u,d,a),u._drawn_image=new Image,u._drawn_image.src=d.toDataURL(),u._drawn_image.crossOrigin="anonymous",u._drawn_image.onclick=function(){f(u,e),s(~~t)},d=null}var p=document.createElement("div");p.appendChild(u._drawn_image);var h=u._drawn_image;h.id="layout_subarea_scheme_cavans_"+t,h.className=(0,m.fZ)()?"mobile":"",r.appendChild(p);var g=document.createElement("div");g.className=i._scheme_name+" scheme_name",g.innerHTML="".concat(l("分区")).concat(u._scheme_name.includes("DIY")?l(u._scheme_name):"".concat(l("布局"))+(1+~~t))+"".concat(o&&c.nb.IsDebug&&u._scheme_name||""),p.appendChild(g)};for(var d in n)u(d)}else r.innerHTML="\n      <div class=".concat(i.letfEmpty,">\n          <div class=").concat(i.letfEmptyItem,">\n            <img class='emptyImg' src=","https://3vj-fe.3vjia.com/layoutai/image/Empty.png",' alt="" />\n            <div class=').concat(i.text,">\n              ").concat(l("暂无分区")).concat(l("推荐内容"),"\n            </div>\n          </div>\n      </div>\n  ")}};return(0,S.useEffect)((function(){var n,t,r;(c.nb.on(a.U.LayoutSchemeList,(function(n){(null==n?void 0:n.schemeList)?u(xr(n.schemeList),n.index):u([],0)})),c.nb.on_M(a.U.WholeLayoutSchemeList,"NewSunD_schemeList",(function(n){(null==n?void 0:n.schemeList)?d(xr((null==n?void 0:n.schemeList)||[]),n.index):d([],0)})),null===(n=c.nb.instance)||void 0===n?void 0:n.layout_container)&&d(xr((null===(r=c.nb.instance)||void 0===r||null===(t=r.layout_container)||void 0===t?void 0:t._whole_layout_scheme_list)||[]),0,!0);c.nb.on(a.U.SubAreaLayoutSchemeList,(function(n){(null==n?void 0:n.schemeList)?(p(xr(n.schemeList),n.index),n.auto_layout&&n.schemeList[0]&&f(n.schemeList[0],0)):p([],0)})),yr&&(yr.room_scheme_list?u(yr.room_scheme_list,yr.room_scheme_index):yr.whole_scheme_list&&d(yr.whole_scheme_list,yr.whole_scheme_index)),e.homeStore.selectedRoom&&u(xr(e.homeStore.selectedRoom._room._layout_scheme_list),e.homeStore.selectedRoom._room.selectIndex)}),[]),n.isLightMobile?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:i.bottom_panel_container,children:[!e.homeStore.isSingleRoom&&(0,r.jsx)("div",{className:i.roomListBar,children:(0,r.jsx)(fr.A,{})}),(0,r.jsx)("div",{className:"".concat(i.bottom_panel_layout_list),id:"side_list_div",ref:t})]}),(0,r.jsx)(lr,{})]}):(0,r.jsxs)("div",{className:i.left_panel_container,style:{marginTop:"16px"},children:[(0,r.jsx)("div",{className:i.title,children:l("布局")}),(0,r.jsx)("div",{className:"".concat(i.left_panel_layout_list," ").concat((0,m.fZ)()?i.mobile:""),id:"side_list_div",ref:t}),(0,r.jsx)(lr,{})]})}));function _r(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Sr(){var n=_r(["\n      margin-top: 16px;\n      padding: 0 16px;\n    "]);return Sr=function(){return n},n}function jr(){var n=_r(["\n      display: flex;\n      justify-content: space-between;\n      font-size: 16px;\n      font-weight: 600;\n      color: #25282D;\n      align-items: center;\n      margin: 16px 0px ;\n    "]);return jr=function(){return n},n}function kr(){var n=_r(["\n      color: #282828;\n      font-size: 20px;\n      font-weight: 600;\n    "]);return kr=function(){return n},n}function Ar(){var n=_r(["\n      color: #282828;\n      font-size: 12px;\n      cursor: pointer;\n    "]);return Ar=function(){return n},n}function Ir(){var n=_r(["\n      margin-bottom: 12px;\n      .ant-tag {\n        border: 1px solid #00000026;\n      }\n    "]);return Ir=function(){return n},n}function Cr(){var n=_r(["\n      display: flex;\n      justify-content: space-between;\n    "]);return Cr=function(){return n},n}function Er(){var n=_r(["\n      box-sizing: border-box;\n      padding: 0px 12px 0 0;\n      transition: all .3s;\n      min-width: 156px;\n      overflow-y: scroll;\n      margin-top: 12px;\n      max-height: calc(100vh - 160px);\n      &::-webkit-scrollbar {\n        width: 0px;\n      }\n      ul {\n        padding: 0;\n      }\n      li {\n        padding: 0;\n        margin: 0;\n        list-style: none;\n      }\n      .menu {\n        > li {\n          margin-bottom: 16px;\n          transition: all .3s;\n        }\n        li:hover{\n          color: #5B5E60;\n        }\n        &_columns {\n          display: flex;\n        }\n\n        &_item {\n          /* background: #f2f2f2; */\n          padding: 8px 0;\n\n          :first-child {\n            margin-right: 12px;\n          }\n          \n          :last-child li:first-child {\n            width: 72px;\n          }\n          li {\n            // padding: 0 16px 0 22px;\n            margin: 8px 0;\n            color: #25282D;\n            font-family: PingFang SC;\n            font-weight: regular;\n            font-size: 14px;\n            line-height: 20px;\n            height: 20px;\n            width: 60px;\n            letter-spacing: 0px;\n            text-align: left;\n            cursor: pointer;\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            user-select:none;\n          }\n        }\n      }\n      .icon {\n        color: red;\n      }\n      .label {\n        color: #25282D;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        height: 24px;\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        user-select:none;\n\n        &_name {\n          // margin-left: 5px;\n          height: 100%;\n        }\n\n        &_name::after {\n          content: '';\n          height: 8px;\n          display: block;\n          position: relative;\n          top: -3px;\n          opacity: 0.5;\n          background: linear-gradient(90deg, #66B8FF 0%, #147FFA00 100%);\n        }\n\n        &.active {\n          color: rgba(20, 127, 250, 1);\n        }\n      }\n    "]);return Er=function(){return n},n}function Dr(){var n=_r(["\n      height: 100%;\n      min-width: 256px;\n      .layout_btns {\n        display: flex;\n        align-items: center;\n        margin-top:10px;\n        padding-left:16px;\n\n        .btn {\n          border-radius: 2px;\n          background: rgba(20, 127, 250, 0.1);\n          color: rgba(20,127,250,1);\n          font-weight: bold;\n          font-size: 14px;\n          margin-bottom:8px;\n          width: 100px;\n          text-align: center;\n          &:first-child{\n            margin-right: 8px;\n          }\n          &:hover{\n            background: rgba(20, 127, 250, 0.25);\n            color: rgba(20,127,250,1);\n          }\n        }\n      }\n    "]);return Dr=function(){return n},n}function Fr(){var n=_r(["\n      border-radius: 30px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 32px;\n      letter-spacing: 0px;\n      text-align: left;\n      // min-width: 326px;\n      width: 100%;\n      height: 32px;\n      border: none;\n      padding-left: 36px;\n      margin-left: 18px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return Fr=function(){return n},n}function Nr(){var n=_r(["\n      position: absolute;\n      top: 50%;\n      translate: 32px -50%;\n    "]);return Nr=function(){return n},n}function Or(){var n=_r(["\n      display: flex;\n    "]);return Or=function(){return n},n}function Mr(){var n=_r(["\n      display: flex;\n      align-items: center;\n    "]);return Mr=function(){return n},n}function Pr(){var n=_r(["\n      display: flex;\n      flex-grow: 1;\n      position: relative;\n    "]);return Pr=function(){return n},n}function Lr(){var n=_r(["\n      color: #147FFA !important;\n    "]);return Lr=function(){return n},n}var Tr=(0,C.rU)((function(n){n.token;var e=n.css;return{root:e(Sr()),title:e(jr()),left:e(kr()),right:e(Ar()),tag_box:e(Ir()),menu_container:e(Cr()),menu_box:e(Er()),figure_box:e(Dr()),container_input:e(Fr()),Icon:e(Nr()),searchInfo:e(Or()),closeInfo:e(Mr()),inputInfo:e(Pr()),select:e(Lr())}}));function Rr(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function zr(){var n=Rr(["\n      display: flex;\n      // justify-content: space-evenly;\n      flex: 1;\n      flex-wrap: wrap;\n      /* padding-top: 16px; */\n      overflow-y: scroll;\n      padding-left: 0px;\n      transition: all .3s;\n      max-height: calc(100vh - 150px);\n      margin-top: 10px;\n      &::-webkit-scrollbar {\n        width: 0px;\n      }\n\n      .item {\n        flex-grow: 1;\n        min-width: 120px;\n        max-width: 33.33%;\n        margin: 4px;\n        cursor: pointer;\n        transition: box-shadow 0.3s ease;\n        user-select:none;\n        .image {\n          height: 120px;\n          background: rgba(242,242,242,1);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 4px;\n          overflow:hidden;\n          transition: all .3s;\n          .ant-image-img {\n            width: 100%;\n            height: 120px;\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n          .group_image.ant-image-img {\n            width:120px;\n            height: 120px;\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n            display: flex;\n            margin-left:0%;\n\n          }\n\n        }\n\n        .title {\n          color: #000000;\n          font-size: 12px;\n          padding: 10px 0;\n          height: 40px;\n          line-height: 20px;\n          text-align: center;\n        }\n        &:hover {\n          /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */\n          .image {\n            background-color: #DDDFE4;\n            \n          }\n        }\n      }\n    "]);return zr=function(){return n},n}function Ur(){var n=Rr(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 100%;\n      margin-left: 50%;\n      transform: translateX(-50%);\n      margin-top: 100%;\n      img\n      {\n        width: 120px;\n        height: 120px;\n      }\n      .desc\n      {\n        text-align: center;\n        margin-top: 10px;\n        color: #A2A2A5;\n        font-size: 12px;\n      }\n    "]);return Ur=function(){return n},n}var Br=(0,C.rU)((function(n){n.token;var e=n.css;return{figure:e(zr()),emptyInfo:e(Ur())}})),Hr=t(17830),Wr=t(13684);function Kr(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function qr(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Kr(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Kr(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Vr=(0,_.observer)((function(n){var e=n.data,t=(0,k.B)().t,i=Br().styles,o=((0,v.P)(),qr((0,S.useState)(""),2)),a=o[0],l=o[1],s=j().useRef(null);return(0,S.useEffect)((function(){var n=new w.Jc({isShowThumbnail:!0,container:document.getElementById("side_pannel"),log:!1});return n.bindDrag(),function(){n.unbindDrag()}}),[]),(0,S.useEffect)((function(){var n=function(){a&&(c.nb.RunCommand(c._I.LeaveSubHandler),l(""))};return window.addEventListener("mouseup",n),function(){window.removeEventListener("mouseup",n)}}),[a]),(0,S.useEffect)((function(){s.current&&(s.current.scrollTop=0)}),[e]),(0,r.jsx)("div",{className:i.figure,ref:s,children:e.length>0?e.map((function(n,e){return(0,r.jsxs)("div",{className:"item",onPointerDown:function(e){Wr.O.instance.getOutLineImage(n)},children:[(0,r.jsx)("div",{className:"image",children:(0,r.jsx)(Hr.A,{preview:!1,src:n.imagePath,alt:n.materialName})}),(0,r.jsx)("div",{className:"title",children:t(n.materialName)})]},e)})):(0,r.jsx)("div",{className:i.emptyInfo,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("div",{className:"desc",children:t("当前套系下无关联素材，请从素材广场添加")})]})})})})),Zr=t(95138),Gr=t(48402);function $r(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Yr(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Xr(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Yr(o,r,i,a,l,"next",n)}function l(n){Yr(o,r,i,a,l,"throw",n)}a(void 0)}))}}function Qr(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return $r(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return $r(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jr(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var ni=(0,_.observer)((function(){var n=(0,k.B)().t,e=(0,v.P)(),t=Tr().styles,i=Qr((0,S.useState)(!0),2),o=i[0],l=i[1],s=Qr((0,S.useState)({}),2),u=(s[0],s[1]),d=Qr((0,S.useState)([]),2),f=d[0],p=d[1],h=Qr((0,S.useState)(!1),2),m=h[0],g=h[1],b=Qr((0,S.useState)(""),2),y=b[0],_=b[1],j=Qr((0,S.useState)(0),2),A=(j[0],j[1]),I=Qr((0,S.useState)(""),2),C=I[0],E=I[1],D=Qr((0,S.useState)(""),2),F=D[0],N=D[1],O=Qr((0,S.useState)([]),2),M=O[0],P=O[1],L=Qr((0,S.useState)([]),2),T=L[0],R=L[1],z=Qr((0,S.useState)(""),2),U=z[0],B=z[1],H=Gr.yM.filter((function(n){return"视角"!==n.label&&"户型"!==n.label&&"定制"!==n.label})),W=(0,S.useRef)(null),K=(0,S.useRef)(null),q=(0,r.jsx)(Ve.A,{style:{fontSize:24},spin:!0}),V=Zr.A.CheckableTag,Z=[],G=[];H.map((function(n,e){n.child.map((function(n){Z=Z.concat(n.figureList)}))})),H.filter((function(n){return"户型"!==n.label})).map((function(n,e){n.child.map((function(n){G=G.concat(n.figureList)}))}));var $=function(n,e){u(n),E(n.child[0].label),Y(n.child[0],0),A((function(n){return n+1}))},Y=function(){var n=Xr((function(n,e){var t;return Jr(this,(function(e){switch(e.label){case 0:return U?[4,Un({modelloc:n.label,pageIndex:1,pageSize:500,ruleId:U})]:(x.A.warning("请先选择风格套系"),[2]);case 1:return(t=e.sent()).success?p(t.result.result):p([]),_(""),[2]}}))}));return function(e,t){return n.apply(this,arguments)}}(),X=function(){var n=Xr((function(n){var e,t,r;return Jr(this,(function(i){switch(i.label){case 0:return(null===(t=pr.y.instance)||void 0===t||null===(e=t._current_series)||void 0===e?void 0:e.ruleId)?[4,Un({materialName:n,pageIndex:1,pageSize:500,ruleId:U})]:(x.A.warning("请先选择风格套系"),[2]);case 1:return(r=i.sent()).success?p(r.result.result):p([]),E(""),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),Q=function(){var n=H.filter((function(n){return!0}));P(n)};(0,S.useEffect)((function(){U&&(u(H[0]),$(H[0],0))}),[U]);var J=function(){if(e.homeStore.room2SeriesSampleArray.length>0){var n,t=Array.from(new Map(e.homeStore.room2SeriesSampleArray.map((function(n){var e=Qr(n,2),t=(e[0],e[1]);return[t.ruleId,t]}))).values());if(R(t),!U)B(null===(n=t[0])||void 0===n?void 0:n.ruleId)}};return(0,S.useEffect)((function(){J()}),[e.homeStore.room2SeriesSampleArray]),(0,S.useEffect)((function(){J(),c.nb.on_M(a.U.AIDesignModeChanged,"newMenu",(function(){Q()})),Q()}),[]),(0,S.useEffect)((function(){W.current&&(o?(W.current.style.display="block",W.current.style.opacity=1):(W.current.style.opacity=0,setTimeout((function(){W.current.style.display="none"}),300)))}),[o]),(0,S.useEffect)((function(){g(!0),setTimeout((function(){g(!1)}),200)}),[f]),(0,r.jsxs)("div",{className:t.root,children:[(0,r.jsxs)("div",{className:t.title,children:[(0,r.jsx)("div",{className:t.left,children:n("风格内素材")}),(0,r.jsxs)("div",{className:t.right,onClick:function(){e.homeStore.setIsShowMaterialPanel(!0)},children:[(0,r.jsx)("img",{style:{width:"24px",height:"24px",marginBottom:"2px"},src:"https://3vj-fe.3vjia.com/project/3d-design/material-dock.png",alt:""}),n("素材广场")]})]}),(0,r.jsx)("div",{className:t.tag_box,children:T.map((function(n){return(0,r.jsx)(V,{checked:U===n.ruleId,onChange:function(e){B(e?n.ruleId:"")},children:n.ruleName},n.ruleId)}))}),(0,r.jsxs)("div",{className:t.searchInfo,children:[(0,r.jsxs)("div",{className:t.closeInfo,children:[(0,r.jsx)(w.In,{iconClass:o?"icona-shouqileimuCollapsetheCatagory":"iconExpandtheCatagory",style:{fontSize:"24px",fontWeight:"500",color:"#000",marginRight:"2px",cursor:"pointer",borderRadius:"2px"},onClick:function(){l(!o)}}),(0,r.jsx)("span",{onClick:function(){l(!o)},style:{color:"#282828",fontSize:"12px",fontWeight:600,lineHeight:1.67,cursor:"pointer"},children:n(o?"收起":"展开")})]}),(0,r.jsxs)("div",{className:t.inputInfo,children:[(0,r.jsx)(w.In,{onMouseEnter:function(){},onClick:function(){X(y)},className:t.Icon,iconClass:"iconsearch",style:{fontSize:"16px",color:"#6C7175",cursor:"pointer"}}),(0,r.jsx)("input",{value:y,onKeyDown:function(n){"Enter"==n.key&&X(y)},onChange:function(n){_(n.currentTarget.value),""===n.currentTarget.value&&K.current.resetFilter()},onMouseEnter:function(){},onMouseLeave:function(){},className:t.container_input,placeholder:n("搜索全部素材")})]})]}),(0,r.jsxs)("div",{className:t.menu_container,children:[(0,r.jsx)("div",{className:t.menu_box,id:"menu_box",ref:W,children:(0,r.jsx)("ul",{className:"menu",children:M.map((function(e,i){return(0,r.jsxs)("li",{children:[(0,r.jsx)("div",{className:"label ".concat((o=n(e.label),F===o?"active":"")),onClick:function(){$(e,i),N(n(e.label))},children:(0,r.jsx)("span",{className:"label_name",children:n(e.label)})}),(0,r.jsxs)("div",{className:"menu_columns",children:[(0,r.jsx)("ul",{className:"menu_item",children:e.child.map((function(i,o){return o%2==1?null:(0,r.jsx)("li",{onClick:function(){Y(i,o),E(n(i.label)),N(n(e.label))},className:C===n(i.label)?"".concat(t.select):"",children:i.label.length>6?(0,r.jsx)(ur.A,{title:n(i.label),children:n(i.label)}):n(i.label)},i.label+o)}))}),(0,r.jsx)("ul",{className:"menu_item",children:e.child.map((function(i,o){return o%2==0?null:(0,r.jsx)("li",{onClick:function(){Y(i,o),E(n(i.label)),N(n(e.label))},className:C===n(i.label)?"".concat(t.select):"",children:i.label.length>4?(0,r.jsx)(ur.A,{title:n(i.label),children:n(i.label)}):n(i.label)},i.label+o)}))})]})]},e.label+i);var o}))})}),(0,r.jsx)("div",{className:t.figure_box,children:(0,r.jsx)(qn.A,{indicator:q,spinning:m,children:(0,r.jsx)(Vr,{data:f})})})]})]})})),ei=t(16238),ti=t(77177),ri=t(10867);function ii(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function oi(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ii(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ii(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ai=(0,_.observer)((function(n){var e=n.selectedFigureElement,t=(0,k.B)().t,i=((0,ti.A)().styles,oi((0,S.useState)(!1),2)),o=(i[0],i[1]),l=(0,v.P)(),s=(0,S.useRef)(null),u=l.homeStore,d=u.setRoom2SeriesSampleArray,f=u.setShowReplace;(0,S.useEffect)((function(){o(!0),setTimeout((function(){o(!1)}),1e3);return c.nb.on(a.U.Room2SeriesSampleRoom,(function(n){if(d(n),(null==n?void 0:n.length)>0)n.every((function(n){return n[0]._furniture_list.length>0}))})),function(){c.nb.off(a.U.Room2SeriesSampleRoom)}}),[]);return(0,r.jsx)(w._w,{left:0,height:document.documentElement.clientHeight-48,width:280,draggable:!0,title:function(){var n,r=t("换素材");(null==e?void 0:e._matched_material)&&("地面"===e._matched_material.modelLoc?r=t("换地面"):"墙面"===e._matched_material.modelLoc?r=t("换墙面"):"吊顶"===(null===(n=e._matched_material.figureElement)||void 0===n?void 0:n.category)&&(r=t("换吊顶")));return r}(),showHeader:!0,onClose:function(){f(!1)},children:(0,r.jsx)("div",{ref:s,children:(0,r.jsx)(ri.A,{selectedFigureElement:e})})})})),li=t(78644),ci=t(87996),si=t(50646),ui=t(24318),di=t(19915),fi=(0,_.observer)((function(n){var e=n.params,t=(0,v.P)(),i=(0,k.B)().t;return(0,r.jsx)(w._w,{left:0,height:document.documentElement.clientHeight-48,width:280,draggable:!1,title:i("空间信息"),top:48,onClose:function(){t.homeStore.setIsShowRoomInfo(!1)},children:(0,r.jsx)(w.Qe,{widgets:{RowButton:ci.A,RotateWidget:si.A,ColorWidget:ui.A,SubAreaWidget:di.A},title:e.title,draggable:!1,schema:e,top:48,framed:!1,right:window.innerWidth-280},t.homeStore.key)})})),pi=t(92935),hi=t(28888),mi=t(84545),gi=t(93783);function vi(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function xi(){var n=vi(["\n          position: absolute;\n          top: 0%;\n          width: 100%;\n          height: 100%;\n          background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */\n          z-index: 999; /* 确保蒙层在其他元素之上 */\n        "]);return xi=function(){return n},n}function bi(){var n=vi(["\n            position: absolute;\n            width: 1080px;\n            height: 671px;\n            left: 50%;\n            top: 50%;\n            background: #fff;\n            border-radius: 12px;\n            padding-bottom: 10px;\n            transform: translate(-50%,-50%);\n            @media screen and (max-width: 768px){\n                width: 580px;\n                min-height: 698px;\n            }\n            @media screen and (orientation: landscape){\n                width: 748px;\n                height: 520px;\n            }\n           \n        "]);return bi=function(){return n},n}function yi(){var n=vi(["\n            width: 100%;\n            height:64px;\n            text-align:center;\n            color: #000000D8;\n            font-weight: 700;\n            font-size: 20px;\n            line-height: 64px;\n            letter-spacing: 0px;\n        "]);return yi=function(){return n},n}function wi(){var n=vi(["\n          position:absolute;\n          top:14px;\n          right:14px;\n          width:24px;\n          height:24px;\n          font-size:20px !important;\n          line-height:24px;\n          text-align:center;\n          border-radius:4px;\n          color:#A2A2A5;\n          &:hover {\n            background :#A2A2A533;\n\n          }\n        "]);return wi=function(){return n},n}function _i(){var n=vi(["\n            display: flex;\n            \n        "]);return _i=function(){return n},n}function Si(){var n=vi(["\n            width: 75%;\n            margin-left: 30px;\n            @media screen and (max-width: 768px){\n                width: 70%;\n            }\n        "]);return Si=function(){return n},n}function ji(){var n=vi(["\n            width: 25%;\n            height:585px;\n            margin: 0px 0 0 20px;\n            background:#F4F5F5;\n            img {\n                width:100%;\n            }\n            @media screen and (max-width: 768px){\n                width: 30%;\n            }\n            @media screen and (orientation: landscape){\n                height: 438px;\n            }\n       "]);return ji=function(){return n},n}function ki(){var n=vi(["\n            position:absolute;\n            bottom : 20px;\n            left:100px;\n            width:88px;\n            height:32px;\n            border-radius:6px;\n            background:#fff;\n            border: 1px solid #00000026;\n            cursor:pointer;\n            &:hover {\n                background:#ffffff88;\n            }\n       "]);return ki=function(){return n},n}function Ai(){var n=vi(["\n            .row_div {\n                display: flex;\n                .row_cate {\n                    margin-bottom:6px;\n                    padding-top:2px;\n                    padding-bottom:2px;                   \n                    border-radius:12px;\n                    width: 10%;\n                    color: #282828;\n                    font-weight: 700;\n                    font-size: 12px;\n                    line-height: 20px;\n                    letter-spacing: 0px;\n                    text-align: left;\n\n                }\n                .row_tags {\n                    /* max-width: 350px;\n                    overflow-x: scroll; */\n                    width: 90%;\n                    margin-bottom:6px;\n                    .tag {\n                        float:left;\n                        padding-left:10px;\n                        padding-right:10px;\n                        padding-top:2px;\n                        padding-bottom:2px;\n                        background:#fafafa;\n\n                        border-radius:12px;\n                        margin-right:8px;\n                        // width:66px;\n                        color: #5B5E60;\n                        font-size: 12px;\n                        line-height: 20px;\n                        letter-spacing: 0px;\n                        text-align: center;\n                        cursor:pointer;\n                        &:hover {\n                            color:#5B5E60;\n                            background:#aaaaaa33;\n                        }\n                        &.checked {\n                            color:#147FFA;\n                            background:#E6F6FF;\n                        }\n                    }\n                }\n            }\n       "]);return Ai=function(){return n},n}function Ii(){var n=vi(["\n            width: 100%;\n            height: calc(100% - 200px);\n            position: relative;\n            overflow:scroll;\n            @media screen and (max-width: 768px){\n                height: calc(100% - 639px);\n            }\n            @media screen and (orientation: landscape){\n                height: calc(100% - 788px);\n            }\n       "]);return Ii=function(){return n},n}function Ci(){var n=vi(["\n        position:absolute;\n        top:0;\n        left:0;\n        width:100%;\n        height:100%;\n        background: #fff;\n        overflow:hidden;\n        z-index: 9;\n       "]);return Ci=function(){return n},n}function Ei(){var n=vi(["\n        width:140px;\n        height:50px;\n        line-height:50px;\n        font-size:20px;\n        border-radius:5px;\n        position:absolute;\n        text-align:center;\n        left:calc( 50% - 70px );\n        top:calc( 50% - 25px );\n       "]);return Ei=function(){return n},n}function Di(){var n=vi(["\n        float:left;\n        position: relative;\n        width:230px;\n        margin-right:20px;\n        margin-bottom:24px;\n        border-radius:4px;\n        cursor:pointer;\n        &:hover {\n            box-shadow:0px 2px 8px -1px rgba(30,41,59,0.12)\n        }\n        @media screen and (max-width: 768px){\n            width: 160px;\n        }\n        img {\n            width: 100%;\n            height:129px;\n            border-radius:4px;\n    \n        }\n        .img_element {\n            position:absolute;\n            width:80px;\n            height:60px;\n            top:4px;\n            left:4px;\n            border-radius: 4px;\n            background: #F8F8F8;\n        }\n        .layout2d_img {\n            width:52px;\n            height:52px;\n            position:absolute;\n            left:14px;\n            top:4px;\n        }\n        .result_title {\n            width:100%;\n            height:22px;\n            color: #25282D;\n            font-weight: 700;\n            font-size: 14px;\n            line-height: 22px;\n            letter-spacing: 0px;\n            text-align: left;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n            overflow:hidden;\n            margin-top: 4px;\n        }\n        .result_tags {\n            width:100%;\n            height:16px;\n            font-size:12px;\n            color:#595959;\n            margin-top: 4px;\n            .bestFit {\n                display:inline;\n                font-weight:700;\n                text-align:center;\n                color:#52C41A;\n                background:linear-gradient(90deg, #F6FFED 0%, #D9F7BE 100%);\n                border:1px solid #D9F7BE;\n                border-radius:4px;\n            }\n            .defaultFit {\n                display:inline;\n                text-align:center;\n                color:#5B5E60;\n                border-radius: 4px;\n                background: linear-gradient(90deg, #FAFAFA 0%, #EAEAEB 100%);\n                border: 1px solid #EAEAEB;\n            }\n        }\n       "]);return Di=function(){return n},n}function Fi(){var n=vi(["\n            display: flex;\n            flex-wrap: wrap;\n            height: 100%;\n            overflow: hidden;\n            .ant-skeleton-button\n            {\n                width: 100% !important;\n                /* height: 100% !important; */\n                margin: 10px 10px 0px 0px;\n            }\n            .Skeleton_content\n            {\n                width: 31%;\n                margin-right: 2%;\n                aspect-ratio: 1 / 1;\n                display: flex;\n                flex-direction: column;\n            }\n        "]);return Fi=function(){return n},n}var Ni=(0,C.rU)((function(n){n.token;var e=n.css;return{popup_root:e(xi()),main_dialog:e(bi()),top_title:e(yi()),close_btn:e(wi()),container:e(_i()),containerInfo:e(Si()),room_layout_img:e(ji()),reset_layout_btn:e(ki()),tags_container:e(Ai()),result_container:e(Ii()),result_progress:e(Ci()),progress_title:e(Ei()),result_element:e(Di()),skeletonBox:e(Fi())}})),Oi=t(65640);function Mi(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Pi(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Li(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Pi(o,r,i,a,l,"next",n)}function l(n){Pi(o,r,i,a,l,"throw",n)}a(void 0)}))}}function Ti(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||zi(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ri(n){return function(n){if(Array.isArray(n))return Mi(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||zi(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zi(n,e){if(n){if("string"==typeof n)return Mi(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Mi(n,e):void 0}}function Ui(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Bi=function(){var n=function(){var n=Array.from({length:6}).map((function(n,e){return(0,r.jsxs)("div",{className:"Skeleton_content",children:[(0,r.jsx)(Vn.A.Button,{style:{height:"160px"},active:!0},"skeleton_button_1_".concat(e)),(0,r.jsx)(Vn.A.Button,{style:{height:"20px"},active:!0},"skeleton_button_2_".concat(e)),(0,r.jsx)(Vn.A.Button,{style:{height:"15px"},active:!0},"skeleton_button_3_".concat(e))]})}));return(0,r.jsx)("div",{className:t.skeletonBox,children:n})},e=(0,k.B)().t,t=Ni().styles,i=Ti((0,S.useState)(!1),2),o=(i[0],i[1]),l=Ti((0,S.useState)(null),2),s=l[0],u=l[1],d=Ti((0,S.useState)([{title:e("方案"),tags:[{name:e("单空间"),checked:!0}]},{title:e("归属"),tags:[{name:e("门店"),checked:!1},{name:e("企业"),checked:!1},{name:e("平台"),checked:!0}]},{title:e("空间"),tags:[{name:"客餐厅",checked:!1},{name:"厨房",checked:!1},{name:"卧室",checked:!1}]}]),2),f=d[0],p=d[1],h=Ti((0,S.useState)(!1),2),m=h[0],g=h[1],v=Ti((0,S.useState)([]),2),b=v[0],y=v[1],w="平台",_=!1;c.nb.on(a.U.ShowSchemeTestingLeftPanel,(function(n){o(n)}));var j=function(){var n=Li((function(n){var t,r,i,o,a,l,c,s,u,d,h,m,g,v;return Ui(this,(function(b){switch(b.label){case 0:if(!1===n.enabled)return x.A.info(e("暂未开放")),[2];if(n.checked)return[2];if(n.checked=!n.checked,n.checked){r=!0,i=!1,o=void 0;try{for(a=f[Symbol.iterator]();!(r=(l=a.next()).done);r=!0)if((c=l.value).tags.includes(n)){s=!0,u=!1,d=void 0;try{for(h=c.tags[Symbol.iterator]();!(s=(m=h.next()).done);s=!0)(g=m.value).checked=g===n}catch(n){u=!0,d=n}finally{try{s||null==h.return||h.return()}finally{if(u)throw d}}}}catch(n){i=!0,o=n}finally{try{r||null==a.return||a.return()}finally{if(i)throw o}}}return v=(null===(t=f[1].tags.find((function(n){return n.checked})))||void 0===t?void 0:t.name)||"",v&&v!==w?(w=v,[4,A()]):[3,2];case 1:b.sent(),b.label=2;case 2:return p(Ri(f)),I(),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),A=function(){var n=Li((function(){var n;return Ui(this,(function(e){switch(e.label){case 0:return n=c.nb.instance,"平台"!==w?[3,2]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer",!0,!0)];case 1:return e.sent(),[3,6];case 2:return"企业"!=w?[3,4]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer",!0,!1)];case 3:return e.sent(),[3,6];case 4:return"门店"!=w?[3,6]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer_store",!0,!1)];case 5:e.sent(),e.label=6;case 6:return _=!0,[2]}}))}));return function(){return n.apply(this,arguments)}}(),I=function(){var n=Li((function(){var n,e,t,r,i,o,a;return Ui(this,(function(l){switch(l.label){case 0:return c.nb.instance?(n=c.nb.instance,g(!0),_?[3,2]:[4,A()]):[3,4];case 1:l.sent(),l.label=2;case 2:return e=f[2].tags.find((function(n){return n.checked})),t=Ri(n.layout_container._rooms),e&&(t=t.filter((function(n){return(0,dr.MP)([(n.name||n.roomname)+n._t_id],[e.name])})))[0]&&(n.layer_CadRoomNameLayer&&(n.layer_CadRoomNameLayer._name_mode=1),u(n.layout_container.saveLayoutSchemeImage(560,1170,.8,!0,t[0])),n.layer_CadRoomNameLayer&&(n.layer_CadRoomNameLayer._name_mode=0)),r=n.layout_graph_solver.queryDreamerRoomsWithLayout(t),i=[],o=function(){var n=Li((function(n){var e,t,r=arguments;return Ui(this,(function(o){switch(o.label){case 0:return e=r.length>1&&void 0!==r[1]?r[1]:90,[4,gi.H.instance.getSchemeInfo(n)];case 1:return(t=o.sent())&&(t.fit_score=e,i.push(t)),[2]}}))}));return function(e){return n.apply(this,arguments)}}(),a=r.map((function(n){return o(n.scheme_id,n.score)})),[4,Promise.all(a)];case 3:l.sent(),i.sort((function(n,e){return(e.fit_score||0)-(n.fit_score||0)})),Oi.log(r,i.map((function(n){return[n.schemeId,n.fit_score]}))),y(i),g(!1),l.label=4;case 4:return[2]}}))}));return function(){return n.apply(this,arguments)}}();return(0,S.useEffect)((function(){!function(){var n=c.nb.instance.layout_container,e=!0,t=!1,r=void 0;try{for(var i,o=n._furniture_entities[Symbol.iterator]();!(e=(i=o.next()).done);e=!0)i.value.is_selected=!1}catch(n){t=!0,r=n}finally{try{e||null==o.return||o.return()}finally{if(t)throw r}}n.updateRoomsFromEntities()}(),function(){if(c.nb.instance){var n=c.nb.instance;if(n.layout_container){n.layout_container.updateWholeBox();var e=f[2];e.tags=[];var t=Ri(n.layout_container._rooms),r=["入户花园","阳台","厨房","卧室","卫生间","客餐厅"];t.sort((function(n,e){return r.indexOf(e.roomname)+e.area/1e3-(r.indexOf(n.roomname)+n.area/1e3)})),t.forEach((function(n,e){return n._t_id=e+1})),e.tags=t.map((function(n,e){return{name:(n.name||n.roomname)+n._t_id,checked:0==e}})),p(Ri(f)),u(n.layout_container.saveLayoutSchemeImage(560,1170,.8,!0))}}}(),I()}),[]),(0,r.jsx)("div",{className:t.popup_root,id:"dramerPopUp",children:(0,r.jsxs)("div",{className:t.main_dialog,id:"dreamerDialog",children:[(0,r.jsx)("div",{className:t.top_title,children:e("布局方案")}),(0,r.jsx)("div",{className:"iconfont iconclose1 "+t.close_btn,onClick:function(){c.nb.emit(a.U.ShowDreamerPopup,!1)}}),(0,r.jsxs)("div",{className:t.container,children:[(0,r.jsx)("div",{className:t.room_layout_img,children:s&&(0,r.jsx)("img",{src:s})}),(0,r.jsxs)("div",{className:t.containerInfo,children:[(0,r.jsx)("div",{className:t.tags_container,children:f.map((function(n,e){return(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("div",{className:"row_cate",children:n.title}),(0,r.jsx)("div",{className:"row_tags",children:n.tags.map((function(n,e){return(0,r.jsx)("div",{className:"tag"+(n.checked?" checked":""),onClick:function(){return j(n)},children:n.name},"tag_ele_"+e)}))})]},"tags_row_"+e)}))}),(0,r.jsxs)("div",{className:t.result_container,id:"dreamhouse_result_container",children:[m&&(0,r.jsx)("div",{className:t.result_progress,children:(0,r.jsx)(n,{})}),b.map((function(n,i){return(0,r.jsxs)("div",{className:t.result_element,onClick:function(){return function(n){var e={origin:"layoutai.api",data:{schemeId:n.schemeId}};window.parent!==window?window.parent.postMessage(e,"*"):window.open("https://xr.3vjia.com/dreamer/scene?schemeId="+n.schemeId),Oi.log("postMessage",JSON.stringify(e))}(n)},children:[(0,r.jsx)("div",{style:{position:"absolute"},children:(0,r.jsx)("div",{className:"img_element",children:(0,r.jsx)("img",{src:n.layout2d,className:"layout2d_img"})})}),(0,r.jsx)("img",{src:n.imagePath+"?x-oss-process=image/resize,w_600"}),(0,r.jsx)("div",{className:"result_title",title:n.schemeName,children:n.schemeName}),(0,r.jsx)("div",{className:"result_tags",children:n.fit_score>80?(0,r.jsx)("div",{className:"bestFit",children:e("匹配度")+":"+Math.min(Math.round(n.fit_score),95)+"%"}):(0,r.jsx)("div",{className:"defaultFit",children:e("匹配度")+":"+Math.min(Math.round(n.fit_score),80)+"%"})})]},"dreamhouse_result_"+i)}))]})]})]})]})})},Hi=t(32422),Wi=t(10447),Ki=t(11164),qi=t(65640);function Vi(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Zi(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Gi(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,r)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function $i(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Vi(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Vi(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Yi=function(){var n=(0,k.B)().t,e=(0,v.P)(),t=$i((0,S.useState)({visible:!1,top:0,left:0}),2),i=t[0],o=t[1],l=$i((0,S.useState)([]),2),s=l[0],u=l[1],d=$i((0,S.useState)(!1),2),f=d[0],p=d[1],h=$i((0,S.useState)(!1),2),g=h[0],_=h[1],j=$i((0,S.useState)(""),2),A=j[0],I=j[1],C=[{id:"rotate",icon:"iconrotate",tips:n("旋转"),disabled:!1,divider:!1},{id:"flip",icon:"iconverflip_fill",tips:n("镜像"),disabled:!1,divider:!1},{id:"copy",icon:"iconCopy",tips:n("复制"),disabled:!1,divider:!1},{id:"delete",icon:"iconempty",tips:n("删除"),disabled:!1,divider:!1},{id:"ruler",icon:"iconbiaochi",tips:n("关闭自动标尺"),disabled:!1,divider:!1}],E=[{id:"rotate",icon:"iconrotate",tips:n("旋转"),disabled:!1,divider:!1},{id:"delete",icon:"iconempty",tips:n("删除"),disabled:!1,divider:!1}],D=[{id:"delete",icon:"iconempty",tips:n("删除"),disabled:!1,divider:!1}],F=[{id:"splitWall",icon:"iconsplit",tips:n("拆分墙")},{id:"delete",icon:"iconempty",tips:n("删除"),disabled:!1,divider:!1}],N=[{id:"ungroupTemplate",icon:"iconungroup",tips:n("解组"),disabled:!1,divider:!1},{id:"replace",icon:"iconexchange ",tips:n("替换产品"),disabled:!1,divider:!1},{id:"rotate",icon:"iconrotate",tips:n("旋转"),disabled:!1,divider:!1},{id:"delete",icon:"iconempty",tips:n("删除"),disabled:!1,divider:!1}],O=(n("旋转"),n("删除"),[{id:"delete",icon:"iconempty",tips:n("删除"),disabled:!1,divider:!1}]),M=hr.a.getRoomSubAreaMenuData(),P=[{id:"roomSubAreaType",icon:"iconxianshiquanbukongjian",tips:n("房间分区类型"),disabled:!1,divider:!1,childType:"table",children:M.map((function(n){return Gi(function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){Zi(n,e,t[e])}))}return n}({},n),{id:"roomSubAreaType_".concat(n.id)})}))}],L=[{id:"info",icon:"iconshuxing",tips:n("空间信息")},{id:"ceiling",icon:"icondiaoding",tips:n("换吊顶")},{id:"floor",icon:"iconpuzhuan",tips:n("换地面")},{id:"wall",icon:"iconqiang",tips:n("换墙面")},{id:"addRoomSubArea",icon:"icona-zaoxingbianjishapeediting",tips:n("添加分区"),disabled:!1,divider:!1}],T=$i(b.A.useModal(),2),R=(T[0],T[1],{x:0,y:0}),z=function(n){R.x=n.clientX,R.y=n.clientY},U=function(n){o({visible:!0,top:n.y-100,left:n.x+10})};return(0,S.useEffect)((function(){c.nb.DispatchEvent(c.n0.AutoRuler,{AutoRuler:g})}),[g]),(0,S.useEffect)((function(){c.nb.on_M(a.U.SelectingTarget,"CommandBarStatus",(function(t,r,i){var a,l=t||null;if(e.homeStore.setSelectEntity(l),t){r||o({visible:!1,top:0,left:0}),C[4].tips=n(g?"关闭自动标尺":"开启自动标尺");var c,s,d=(null==l?void 0:l.type)||null,f=(null==t||null===(a=t.ex_prop)||void 0===a?void 0:a.label)||null,p=(null==r?void 0:r.clientX)||0,h=(null==r?void 0:r.clientY)||0;if("Furniture"===d){if(e.homeStore.setShowReplace(!0),c=l,null!=(s=Wi.r)&&"undefined"!=typeof Symbol&&s[Symbol.hasInstance]?s[Symbol.hasInstance](c):c instanceof s){var m;return m=C.concat([{id:"replace",icon:"iconexchange ",tips:n("替换产品"),disabled:!1,divider:!1},{id:"ungroupTemplate",icon:"iconungroup",tips:n("解组"),disabled:!1,divider:!1}]),o({visible:!0,top:h,left:p+50}),void u(m)}var v;if(v=[{id:"replace",icon:"iconexchange ",tips:n("替换产品"),disabled:!1,divider:!1}].concat(C),!i)return;U(i),u(v)}else if("Group"===d){var x;if(x=C.concat([{id:"combination",icon:"iconcombination",tips:n("组合"),disabled:!1,divider:!1}]),!i)return;U(i),u(x)}else if("BaseGroup"===d){if(e.homeStore.setShowReplace(!0),!i)return;U(i),u(N)}else if("Door"===d||"Window"===d){if("Door"===d&&e.homeStore.setShowReplace(!0),"baywindow"===f)return u(D),void o({visible:!0,top:h,left:p+30});if(!i)return;U(i),u(E)}else if(d===mr.Fz.Wall){if(i)return;o({visible:!0,top:null==r?void 0:r.clientY,left:(null==r?void 0:r.clientX)+30}),u(F)}else"ExDrawing"===d?(o({visible:!0,top:h,left:p+30}),u(O)):d===mr.Fz.RoomArea?(u(L),o({visible:!0,top:68,left:window.innerWidth/2-85})):d===mr.Fz.RoomSubArea?(u(P),o({visible:!0,top:h,left:p+30})):o({visible:!1,top:0,left:0})}else o({visible:!1,top:0,left:0})}))}),[g]),(0,S.useEffect)((function(){c.nb.on(a.U.SelectingRulerTarget,(function(e,t){e?(U(t),u([{id:"deleteRuler",icon:"iconempty",tips:n("删除"),disabled:!1,divider:!1}])):o({visible:!1,top:0,left:0})}))}),[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{style:{position:"absolute",top:!(0,m.fZ)()&&i.top,left:(0,m.fZ)()?"50%":i.left,bottom:(0,m.fZ)()&&"260px",transform:(0,m.fZ)()?"translateX(-50%)":"",pointerEvents:"none",display:i.visible?"block":"none"},id:"command_bar",children:(0,r.jsx)(w.rK,{data:s,onClick:function(t,r){switch(t){case"rotate":c.nb.RunCommand(c._I.RotateFurniture);break;case"flip":c.nb.RunCommand(c._I.FlipFurniture);break;case"delete":c.nb.RunCommand(c._I.DeleteFurniture),o({visible:!1,top:0,left:0});break;case"deleteRuler":c.nb.RunCommand(c._I.DeleteRuler),o({visible:!1,top:0,left:0});break;case"copy":c.nb.RunCommand(c._I.CopyFurniture);break;case"splitWall":c.nb.RunCommand(c._I.SplitWall),x.A.info(n("自由打断模式|按右键退出"));break;case"combination":p(!0);break;case"ungroupTemplate":c.nb.DispatchEvent(c.n0.HandleUnGroupTemplate,{}),e.homeStore.setKey(Date.now());break;case"replace":e.homeStore.setShowReplace(!0);break;case"ruler":_(!g),o({visible:!1,top:0,left:0});break;case"info":e.homeStore.setIsShowRoomInfo(!0);break;case"ceiling":var i,l;c.nb.emit_M(a.U.FigureElementSelected,null===(l=e.homeStore.selectEntity)||void 0===l||null===(i=l._room)||void 0===i?void 0:i._ceilling_list[0]),e.homeStore.setShowReplace(!0);break;case"floor":var s,u;c.nb.emit_M(a.U.FigureElementSelected,null===(u=e.homeStore.selectEntity)||void 0===u||null===(s=u._room)||void 0===s?void 0:s._tile),e.homeStore.setShowReplace(!0);break;case"wall":var d,f;c.nb.emit_M(a.U.FigureElementSelected,null===(f=e.homeStore.selectEntity)||void 0===f||null===(d=f._room)||void 0===d?void 0:d._wallTexture),e.homeStore.setShowReplace(!0);break;case"addRoomSubArea":c.nb.RunCommand(Hi.$.handler_name),c.nb.DispatchEvent(Hi.$.change_area_type_event,null),o({visible:!1,top:0,left:0});break;default:var h=t.split("_");h.length>1&&"roomSubAreaType"==h[0]&&(c.nb.DispatchEvent(c.n0.UpdateRoomSubAreaType,h[1]),o({visible:!1,top:0,left:0}))}},onShortcutEntryClick:function(){return qi.log("快捷入口")},bounds:"parent",draggable:!0,onStart:z,onDrag:function(n){var e={x:i.left+n.movementX,y:i.top+n.movementY};o({visible:!0,top:e.y,left:e.x})},onStop:z})}),(0,r.jsxs)(b.A,{title:n("创建组合"),maskClosable:!1,centered:!0,open:f,footer:!1,width:300,onCancel:function(){p(!1)},children:[(0,r.jsx)(Ki.A,{placeholder:n("请选择组合类型"),style:{width:240,margin:"10 auto"},onChange:function(n){I(n)},options:[{value:"沙发组合",label:n("沙发组合")},{value:"餐桌椅组合",label:n("餐桌椅组合")},{value:"床具组合",label:n("床具组合")},{value:"书桌组合",label:n("书桌组合")},{value:"岛台组合",label:n("岛台组合")},{value:"榻榻米组合",label:n("榻榻米组合")}]}),(0,r.jsx)("div",{style:{textAlign:"center",marginTop:"10px"},children:(0,r.jsx)(y.A,{onClick:function(){p(!1),c.nb.DispatchEvent(c.n0.CreateCombination,A),x.A.success(n("组合创建成功"))},type:"primary",children:n("确定")})})]})]})},Xi=t(65640);function Qi(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Ji(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function no(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Ji(o,r,i,a,l,"next",n)}function l(n){Ji(o,r,i,a,l,"throw",n)}a(void 0)}))}}function eo(n,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](n):n instanceof e}function to(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Qi(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Qi(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ro(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var io=(0,_.observer)((function(){var n,e,t=(0,k.B)().t,_=(0,Hn.A)().styles,j=to((0,S.useState)({opening:!1,title:""}),2),C=j[0],E=(j[1],to((0,S.useState)(!1),2)),D=E[0],F=E[1],N=to((0,S.useState)(!1),2),O=N[0],M=N[1],P=to(x.A.useMessage(),2),L=P[0],T=P[1],R="SaveSchemeProgress",z=to((0,S.useState)(!1),2),U=(z[0],z[1]),B=to((0,S.useState)(null),2),H=B[0],W=B[1],q=to((0,S.useState)(!1),2),V=q[0],Z=q[1],G=to((0,S.useState)(!1),2),$=G[0],Y=G[1],X=to((0,S.useState)(!1),2),Q=(X[0],X[1]),J=to((0,S.useState)(!1),2),nn=J[0],tn=J[1],rn=to((0,S.useState)(window.innerWidth<window.innerHeight),2),on=rn[0],an=rn[1],ln=to((0,S.useState)({}),2),cn=ln[0],sn=ln[1],un=to((0,S.useState)(null),2),dn=un[0],fn=un[1],pn=to((0,S.useState)(!1),2),hn=pn[0],mn=pn[1];b.A.confirm,(0,A.Zp)();c.nb.UseApp(i.e.AppName),c.nb.instance&&(c.nb.instance._is_landscape=on,c.nb.t=t);var gn=(0,S.useRef)(null),vn=(0,v.P)(),xn=vn.homeStore,bn=vn.userStore,yn=xn.designMode,wn=xn.selectedRoom,_n=xn.selectEntity,Sn=xn.isShowMaterialPanel,jn=xn.scale,kn=xn.initialDistance,An=xn.showWelcomePage,In=xn.isShowWallTopMenu,Cn=xn.showSaveLayoutSchemeDialog,En=xn.showReplace,Dn=xn.isshowRoomInfo,Nn=xn.setIsShowRoomInfo,On=xn.setAppOpenAsCadPlugin,Mn=xn.setIsShowWallTopMenu,Pn=xn.setShowReplace,Ln=xn.setCurrenScheme,Tn=xn.setRoomInfos,zn=xn.setSelectData,Un=xn.setShowMySchemeList,Bn=xn.setShowWelcomePage,Kn=xn.setShowSaveLayoutSchemeDialog,qn=xn.setScale,Vn=xn.setSelectedRoom,Zn=xn.setImgBase64,Gn=xn.setDesignMode,$n=xn.setIsShowMaterialPanel,Yn=xn.setIsmoveCanvas,Xn=xn.setInitialDistance,Qn=bn.getCheckCurrent,Jn=yn==i.f.HouseDesignMode||yn==i.f.RemodelingMode||yn==i.f.HouseCorrectionMode;c.nb.instance&&(c.nb.instance._is_website_debug=m.iG);var ne=function(){c.nb.instance&&(c.nb.instance.bindCanvas(document.getElementById("cad_canvas")),c.nb.instance.update()),c.nb.instance&&(c.nb.instance._is_landscape=on),an(window.innerWidth<window.innerHeight)},ee=function(){var n=no((function(){var n,e,t;return ro(this,(function(r){switch(r.label){case 0:return m.uN?(n={isDelete:0,pageIndex:1,pageSize:9,keyword:m.uN},[4,o.D.getLayoutSchemeList(n)]):[2];case 1:return e=r.sent(),t=e.layoutSchemeDataList,e.total,t&&(c.nb.DispatchEvent(c.n0.OpenMyLayoutSchemeData,t[0]),c.nb.emit(a.U.OpenHouseSearching,!1)),[2]}}))}));return function(){return n.apply(this,arguments)}}(),te=function(){var n=no((function(){return ro(this,(function(n){switch(n.label){case 0:return[4,(0,je.mY)({module:"enter3d"})];case 1:return n.sent(),Qn(),[2]}}))}));return function(){return n.apply(this,arguments)}}();(0,S.useEffect)((function(){if(fn(c.nb.instance.layout_container._layout_scheme_name),On(!0),window.addEventListener("resize",ne),ne(),c.nb.instance){var n;if(c.nb.instance.initialized||(c.nb.instance.init(),c.nb.RunCommand(i.f.AiCadMode),c.nb.instance.prepare().then((function(){ee(),ie(),m.um&&c.nb.emit(a.U.OpenHouseSearching,!0)})),c.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(n=window)||void 0===n?void 0:n.URLSearchParams){var e=new URLSearchParams(window.location.search).get("debug");if(null!==e){var o="1"===e?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(o)),c.nb.instance._debug_mode=o)}}c.nb.instance.update()}c.nb.on(a.U.ShowWallTopMenu,(function(n){Mn(n)})),c.nb.on(a.U.setIssueReportVisible,U),c.nb.on(a.U.ShowSubHandlerBtn,M),c.nb.on(a.U.CreateDreamerScheme,(function(n){re()})),c.nb.on_M(a.U.RoomList,"room_list1",(function(n){Tn(n)})),c.nb.on_M(a.U.SelectingRoom,"cad_home",(function(n){setTimeout((function(){zn({rooms:null==n?void 0:n.current_rooms,clickOnRoom:!0})}),20),Ln(null==n?void 0:n.event_param)})),c.nb.on(a.U.PerformFurnishResult,(function(n){"success"===n.progress?(Q(!1),L.open({key:R,type:"success",content:t(n.message)||t("计算完成，将自动跳转3D执行布置结果"),duration:3,style:{marginTop:"4vh",zIndex:9999}}),te()):"finish"===n.progress?(Q(!1),L.destroy(R)):"error"===n.progress?x.A.error(t(n.message)):"info"===n.progress?x.A.info({key:R,type:"info",content:t(n.message),duration:1,style:{marginTop:"4vh",zIndex:9999}}):"furnishremaining"===n.progress&&(Y(!0),Q(!0),F(!1))})),c.nb.on(a.U.OpenMySchemeList,(function(){Un(!0)})),c.nb.on(a.U.selectRoom,(function(n){Vn(n)})),c.nb.on(a.U.LayoutSchemeOpening,(function(n){fn(n.name)})),c.nb.on(a.U.LayoutSchemeOpened,(function(n){fn(n.name)})),c.nb.on(a.U.LayoutSchemeOpenFail,(function(n){fn(n.name)})),c.nb.on_M(a.U.FigureElementSelected,"newReplace",(function(n){W(n),n||Pn(!1)})),c.nb.on(a.U.CheckUnfurnishedResult,(function(n){c.nb.DispatchEvent(c.n0.ApplyFurnishTo3D,null)})),c.nb.on(a.U.AttributeHandle,(function(n){sn(n)})),c.nb.on(a.U.SaveProgress,(function(n){"success"===n.progress?(L.open({key:R,type:"success",content:t("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}),vn.schemeStatusStore.layoutSchemeSaved=!0,vn.schemeStatusStore.pendingOpenSchemeIn3D?(vn.schemeStatusStore.pendingOpenSchemeIn3D=!1,c.nb.DispatchEvent(c.n0.updateGroup,{}),m.Bj?function(n){var e,t=m.PV;(c.nb.instance.isOverSea||"aihouse"===(null===(e=vn.userStore.userInfo)||void 0===e?void 0:e.regSource))&&(t=t.replace("3d","aihouse"));var r=t+(t.indexOf("?")>-1?"&":"?")+"AiLayoutSchemeId="+n;window.open(r,"_blank"),K.s.trackApplyTo3DEvent("mySchemeToNew3d")}(n.id):c.nb.DispatchEvent(c.n0.ApplyFurnishTo3D,null)):vn.schemeStatusStore.pendingCreateDreamerScheme&&(vn.schemeStatusStore.pendingCreateDreamerScheme=!1,re())):"fail"===n.progress?(L.open({key:R,type:"error",content:t("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}),vn.schemeStatusStore.pendingOpenSchemeIn3D&&(vn.schemeStatusStore.pendingOpenSchemeIn3D=!1)):"ongoing"===n.progress&&L.open({key:R,type:"loading",content:t("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})})),c.nb.on(a.U.CreateDreamerSchemeResult,(function(n){if("success"===n.progress)L.open({key:R,type:"success",content:(0,r.jsxs)("div",{children:[t("3D方案已经生成"),(0,r.jsx)("br",{}),t("梦想家方案正在转化中，预计等候3分钟..."),(0,r.jsx)("br",{}),t("请稍候前往【我的方案】查看")]}),duration:3,style:{marginTop:"4vh",zIndex:9999}});else if("error"===n.progress)L.open({key:R,type:"error",content:t("梦想家方案转化出错"),duration:3,style:{marginTop:"4vh",zIndex:9999}});else if("creating"===n.progress)L.open({key:R,type:"success",content:t("正在生成3D方案，请稍候前往【我的方案】查看"),duration:3,style:{marginTop:"4vh",zIndex:9999}});else if("processing"===n.progress)L.open({key:R,type:"success",content:t("正在转化梦想家方案，请稍候前往【我的方案】查看"),duration:3,style:{marginTop:"4vh",zIndex:9999}});else if("created"===n.progress){var e=n.extra;window.open(e,"_blank")}})),c.nb.on(a.U.RenderPanorama,(function(n){oe(c._I.RenderAndOpenPanorama),setTimeout((function(){c.nb.RunCommand(c._I.RenderAndOpenPanorama)}),1)})),c.nb.on(a.U.MessageTip,(function(n){x.A.config({top:50,duration:3,maxCount:1}),x.A.info(n)})),c.nb.on(a.U.FurnishRemainingFinished,(function(n){tn(!1)})),c.nb.on(a.U.ShowDreamerPopup,(function(n){vn.homeStore.setShowDreamerPopup(n)})),Qn()}),[]);var re=(0,pi.throttle)(no((function(){return ro(this,(function(n){return oe(c._I.SaveMyLayoutScheme),setTimeout((function(){c.nb.RunCommand(c._I.CreateAndOpenDreamerScheme)}),2e3),[2]}))})),2e3,{leading:!0,trailing:!1});(0,S.useEffect)((function(){eo(_n,l.H)||Nn(!1)}),[_n]);var ie=function(){var n=no((function(){var n,e,r,o;return ro(this,(function(a){switch(a.label){case 0:return"HouseId"!==m.Zx?[3,1]:(no((function(){var n,e,t;return ro(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,(0,g.ZN)("HouseId")];case 1:return n=r.sent(),Xi.log(n),e=n.data,c.nb.DispatchEvent(c.n0.PostBuildingId,{id:e,name:""}),[3,3];case 2:return t=r.sent(),Xi.error("Error loading file:",t),[3,3];case 3:return[2]}}))}))(),[3,9]);case 1:return"DwgBase64"!==m.Zx?[3,2]:(no((function(){return ro(this,(function(n){try{c.nb.RunCommand(c._I.OpenDwgFilefromWork)}catch(n){Xi.error("Error loading file:",n)}return[2]}))}))(),[3,9]);case 2:return"CopyingBase64"!==m.Zx?[3,3]:(no((function(){var n,e,r;return ro(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,(0,g.ZN)("CopyingBase64")];case 1:return n=i.sent(),e=n.data,Zn(e),(0,d.e)(t,vn),[3,3];case 2:return r=i.sent(),Xi.error("Error loading file:",r),[3,3];case 3:return[2]}}))}))(),[3,9]);case 3:return"hxcreate"!==m.Zx?[3,6]:m.fW?[4,Rn({id:m.fW})]:[3,5];case 4:(n=a.sent()).result.contentUrl=n.result.dataUrl,c.nb.DispatchEvent(c.n0.OpenMyLayoutSchemeData,n.result),c.nb.DispatchEvent(c.n0.autoSave,null),a.label=5;case 5:return[3,9];case 6:return"hxedit"!==m.Zx?[3,9]:m.vu?[4,Rn({id:m.vu})]:[3,8];case 7:(e=a.sent()).success&&e.result&&e.result.dataUrl&&(e.result.contentUrl=e.result.dataUrl,c.nb.DispatchEvent(c.n0.OpenMyLayoutSchemeData,e.result)),a.label=8;case 8:c.nb.instance&&"SingleRoom"==(null===(o=c.nb.instance)||void 0===o||null===(r=o.layout_container)||void 0===r?void 0:r._drawing_layer_mode)&&c.nb.DispatchEvent(c.n0.leaveSingleRoomLayout,{}),c.nb.instance._current_handler_mode=i.f.HouseDesignMode,c.nb.RunCommand(i.f.HouseDesignMode),Gn(i.f.HouseDesignMode),a.label=9;case 9:return[2]}}))}));return function(){return n.apply(this,arguments)}}(),oe=function(n){var e=c.nb.instance.layout_container;switch(n){case c._I.SaveMyLayoutSchemeAs:0==e._room_entities.length?L.open({key:R,type:"error",content:t("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):(Z(!0),Kn({show:!0,source:""}));break;case c._I.SaveMyLayoutScheme:0==e._room_entities.length?L.open({key:R,type:"error",content:t("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):null==e._layout_scheme_id?(Z(!1),Kn({show:!0,source:""})):c.nb.DispatchEvent(c.n0.SaveLayoutScheme,null);break;case c._I.SaveSchemeAs:0==e._room_entities.length?L.open({key:R,type:"error",content:t("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):Kn({show:!0,source:""})}},ae=[{key:"series",label:t("选风格"),icon:["iconhuanfengge_n","iconhuanfengge_s"],children:(0,r.jsx)(Ft,{})},{key:"layout",label:t("选布局"),icon:["iconhuanbuju_n","iconhuanbuju_s"],children:(0,r.jsx)(wr,{width:300,showSchemeName:!0})},{key:"material",label:t("加素材"),icon:["iconjiasucai_n","iconjiasucai_s"],children:(0,r.jsx)(ni,{})}];(0,S.useEffect)((function(){window.addEventListener("message",(function(n){"uiapi.mk6.basic.ui"===n.data.origin&&"dragModelFromView"===n.data.data.action&&(c.nb.DispatchEvent(c.n0.MaterialSquareDragup,null),mn(!1),window.addEventListener("message",le)),"uiapi.materialUiplugin"===n.data.origin&&"cannotDrag"===n.data.data.action&&x.A.error(t("当前无法使用素材网站素材"))}))}),[]);var le=function(n){if("uiapi.mk6.basic.ui"===n.data.origin&&"modelMove"===n.data.data.action){var e=n.data.params;mn(!0),c.nb.DispatchEvent(c.n0.SelectedSquareMaterial,e),window.removeEventListener("message",le)}};window.addEventListener("message",(function(n){n.data&&"dreamer.api"===n.data.origin&&"checkClose"===n.data.type&&(0==c.nb.instance.layout_container._room_entities.length?window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"):null==c.nb.instance.layout_container._layout_scheme_id?(Z(!1),Kn({show:!0,source:""})):(c.nb.DispatchEvent(c.n0.SaveLayoutScheme,null),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*")))}));var ce=(0,pi.throttle)(no((function(){return ro(this,(function(n){switch(n.label){case 0:return[4,se()];case 1:return n.sent()?[2,!0]:[2,!1]}}))})),2e3,{leading:!0,trailing:!1}),se=function(){var n=no((function(){return ro(this,(function(n){switch(n.label){case 0:return vn.userStore.hasAuth||"dev"===m.Km?[3,2]:[4,vn.userStore.getCheckCurrent()];case 1:if(n.sent(),vn.userStore.overLimit)return ue(),[2,!1];n.label=2;case 2:return[2,!0]}}))}));return function(){return n.apply(this,arguments)}}(),ue=function(){b.A.info({title:t("升级版本 解锁更多功能"),centered:!0,closable:!0,content:(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[t("当前为")," ",(0,r.jsx)("span",{style:{color:"#147FFA"},children:t("免费版")})]}),(0,r.jsx)("p",{children:t("免费版每日最多支持浏览5次3D方案，您可升级版本，享受更多的浏览次数。")}),(0,r.jsx)("a",{onClick:function(){window.open("https://mall.3vjia.com/")},style:{color:"#147FFA"},children:t("了解更多权益")})]}),onOk:function(){},footer:null})},de=function(){var n=no((function(){return ro(this,(function(n){switch(n.label){case 0:return 0==c.nb.instance.layout_container._room_entities.length?(x.A.error(t("当前方案为空，无法生成3D方案！")),[2]):[4,ce()];case 1:return n.sent()?(K.s.trackClickEvent(vn.homeStore.designMode,"生成3D方案"),c.nb.DispatchEvent(c.n0.CheckUnfurnishedBeforePerformFurnish,{}),[2]):[2]}}))}));return function(){return n.apply(this,arguments)}}();(0,S.useEffect)((function(){hn||window.addEventListener("message",le)}),[hn]);return(0,r.jsxs)("div",{className:_.root+" "+(on?_.landscape:""),children:[(0,r.jsx)(s.pF,{title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{style:{color:"#FFFFFF0F"},children:"|"}),dn?" 【"+dn+"】":""]}),handler:oe,create3DLayout:de}),(0,r.jsxs)("div",{id:"Canvascontent",className:_.content,children:[(0,r.jsx)(li.A,{}),(0,r.jsxs)("div",{ref:gn,id:"body_container",className:_.canvas_pannel,children:[(0,r.jsx)("div",{className:_.side_pannel,id:"side_pannel",children:yn!==i.f.MeasurScaleMode&&!Jn&&(0,r.jsx)(w.I5,{items:ae,contentClassName:_.left_content,onChange:function(){Sn&&$n(!1)}})}),Jn&&yn!==i.f.HouseCorrectionMode&&(0,r.jsx)("div",{className:_.left_panel,id:"side_pannel",children:(0,r.jsx)(s.Wx,{})}),(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){Yn(!1)},onMouseLeave:function(){Yn(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t);Xn(r/jn)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t)/kn;r>5?r=5:r<.05&&(r=.05),qn(r),c.nb.DispatchEvent(c.n0.scale,r)}},onTouchEnd:function(){Xn(null)}}),(0,r.jsx)("div",{className:"canvas_btns",children:O?(0,r.jsx)(y.A,{className:"btn",type:"primary",onClick:function(){yn!==i.f.HouseDesignMode?yn!==i.f.ExDrawingMode?"厨房"===(null==wn?void 0:wn.roomname)?c.nb.RunCommand(c._I.AcceptLeaveSubHandler):c.nb.DispatchEvent(c.n0.leaveSingleRoomLayout,{}):c.nb.RunCommand(c._I.LeaveSubHandler):c.nb.RunCommand(c._I.AcceptLeaveSubHandler)},disabled:C.opening||D,children:t("完 成")}):(0,r.jsx)(en,{disabled:C.opening||D,create3DLayout:de})}),Sn&&(0,r.jsx)(w._w,{height:window.innerHeight-50,width:468,draggable:!0,left:60,top:48,title:"素材广场",onClose:function(){$n(!1)},titleIcon:(0,r.jsx)(w.In,{style:{width:"20px",height:"20px"},svg:!0,iconClass:"iconmodleinfo_hover"}),children:(0,r.jsx)("iframe",{id:"material_square_container",onLoad:function(){setTimeout((function(){ne()}),3e3)},className:_.material_iframe,src:"".concat(m.fv,"?sdVersion=*******.116478&dragComponentId=9&expandIframeWidth=400&isVip=true"),width:"100%",height:"100%"})})]}),(0,r.jsx)(s.iX,{}),yn==i.f.AiCadMode&&(0,r.jsx)(I.A,{})]}),(0,r.jsx)(Yi,{}),(0,r.jsx)(s.ti,{}),(0,r.jsx)(Fn,{}),(0,r.jsx)(d.A,{}),(0,r.jsx)(b.A,{wrapClassName:"welcome_page",open:An,centered:!0,width:"60%",zIndex:999999,closable:!0,destroyOnClose:!0,title:"",footer:null,onCancel:function(){Bn(!1)},mask:!0,children:(0,r.jsx)(h.A,{isFixed:!1})}),(0,r.jsx)(u.A,{schemeNameCb:function(n){fn(n)}}),In&&(0,r.jsx)(p.A,{}),Cn&&(0,r.jsx)("div",{className:_.overlay,children:(0,r.jsx)(f.A,{schemeName:dn||"",closeCb:function(){vn.homeStore.setShowSaveLayoutSchemeDialog(!1)},isSaveAs:V})}),En&&H&&(0,r.jsx)(ai,{selectedFigureElement:H||(null===(n=vn.homeStore.selectEntity)||void 0===n?void 0:n._figure_element)}),(0,r.jsx)(Wn.A,{}),(0,r.jsx)(w.cq,{channelCode:"Helptips-004"}),vn.homeStore.showDreamerPopup&&(0,r.jsx)(Bi,{}),yn!==i.f.HouseDesignMode&&(0,r.jsx)(Ke,{}),!Jn&&(0,r.jsx)(ei.A,{}),vn.homeStore.showHouseSchemeAddForm&&(0,r.jsx)(mi.A,{}),!H&&(0,r.jsx)(s.si,{selectedFigureElement:H,right:!0,left:!1}),Dn&&eo(null===(e=vn.homeStore)||void 0===e?void 0:e.selectEntity,l.H)&&(0,r.jsx)(fi,{params:cn}),$&&(0,r.jsxs)("div",{className:_.comfirmFurnishDialog,children:[(0,r.jsx)("div",{id:"unfurnish-tip-container",children:(0,r.jsx)("span",{id:"unfurnish-tip",children:t("存在未匹配图例，生成3D方案后该图例将被清空")})}),(0,r.jsxs)("div",{id:"unfurnish-btn-container",children:[(0,r.jsx)(y.A,{type:"primary",onClick:function(){c.nb.DispatchEvent(c.n0.StartAutoFurnishRemaining,null),Y(!1),Q(!0),tn(!0)},children:t("AI补全")}),(0,r.jsx)(y.A,{onClick:no((function(){return ro(this,(function(n){return Y(!1),Q(!1),c.nb.DispatchEvent(c.n0.CheckUnfurnishedBeforePerformFurnish,{skipCheckUnfurnished:!0}),[2]}))})),children:t("确认")}),(0,r.jsx)(y.A,{onClick:function(){Y(!1),Q(!1)},children:t("取消")})]})]}),nn&&(0,r.jsx)("div",{className:_.progressInfo,children:(0,r.jsx)(hi.A,{title:t("正在AI补全，请稍等..."),color:"#000000"})}),T]})}))}}]);