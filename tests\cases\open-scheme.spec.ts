import { test, expect } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import { resolve } from 'path';
import { existsSync } from 'fs';
import { cwd } from 'process';
import { clickFileItem, ifEnterAILayout, searchAndOpen } from '../utils/basicProcess';
import { compareScreenshot,takeScreenshot } from '../utils/screenshotUtils';

test.use({ ignoreHTTPSErrors: true });
test.describe('打开方案', () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('新建方案', async ({ page }) => {
    // 1. 点击顶部菜单栏——》“文件”——》“新建”
    await clickFileItem(page, designPage, '新建');

    // 判定是否页面正确显示（需要完善验证标准）
    const dialog = page.getByRole('dialog');
    const title = dialog
      .locator('div > div.ant-modal-content > div.ant-modal-body > div > div > div > div.title')
      .getByText('首页');
    await expect(title).toBeVisible();

    await page.waitForTimeout(3000);
    await compareScreenshot(page, 'open-scheme', 'new.png');
  });

  test('户型库打开户型', async ({ page }) => {
    await searchAndOpen(page, designPage);
    await page.waitForTimeout(3000);
    await compareScreenshot(page, 'open-scheme', 'open.png');
  });

  test('导入临摹图识别户型', async ({ page }) => {
    // 1. 点击顶部菜单栏——》“文件”——》“导入临摹图”
    await clickFileItem(page, designPage, '导入临摹图');

    // 2.选择一张户型图识别，点击智能识别，点击确定
    // 临摹图的文件相对路径：tests\test-data\images\4房2厅2卫1厨（实用面积103方）.jpg

    const projectRoot = cwd(); // 获取当前工作目录（通常是项目根目录）
    const testImagePath = resolve(projectRoot, 'tests/test-data/images/test01.jpg');

    if (!existsSync(testImagePath)) {
      throw new Error(`测试图片不存在: ${testImagePath}`);
    }

    // 两种上传方式尝试
    try {
      // 方式1: 使用setInputFiles
      await page.locator('input[type="file"]').setInputFiles(testImagePath);
    } catch (error) {
      console.log('方式1失败，尝试方式2');
      // 方式2: 使用filechooser事件
      const fileChooser = await page.waitForEvent('filechooser', { timeout: 10000 });
      await fileChooser.setFiles(testImagePath);
    }
    console.log(`已上传文件: ${testImagePath}`);

    // 3: 验证识别按钮是否出现
    const recognizeButton = page.getByText('智能识别');
    await recognizeButton.waitFor({ state: 'visible', timeout: 5000 });
    await recognizeButton.click();

    const confirmButton = page.locator(
      '#body_container > div.canvas_btns > button:nth-child(2) > span'
    );
    await confirmButton.waitFor({ state: 'visible', timeout: 5000 });
    confirmButton.click();

    await ifEnterAILayout(page);

    await page.waitForTimeout(3000);
    await compareScreenshot(page, 'open-scheme', 'import.png');
  });

  test('打开我的方案', async ({ page }) => {
    // 1. 点击顶部菜单栏 >> “文件” >> “我的方案”
    await clickFileItem(page, designPage, '我的方案');

    // 等待网络请求完成
    // ...
    await page.waitForTimeout(5000);
    const schemeItems = page.locator(
      '.src-components-MyLayoutSchemeList-style-index-module-itemOvelay-szs8G'
    );
    const schemeItemCount = await schemeItems.count();
    console.info('我的方案列表中共有' + schemeItemCount + '个方案');
    expect(schemeItemCount).toBeGreaterThan(3);

    await page.waitForTimeout(3000);
    await takeScreenshot(page, 'open-scheme', 'new.png');
  });
});
