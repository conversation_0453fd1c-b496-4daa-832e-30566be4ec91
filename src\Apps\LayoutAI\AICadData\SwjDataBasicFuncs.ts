import { Vector3<PERSON><PERSON> } from "three";
import { I_Window } from "../Layout/IRoomInterface";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";

export function get_window_of_win_rect(win_rect: ZRect) {
    let win = win_rect._attached_elements['window'] as I_Window;
    if (!win) {
        let r_center = win_rect.rect_center;
        win = {
            type: "Window",
            realType: "OneWindow",
            posX: r_center.x,
            posY: r_center.y,
            posZ: win_rect.zval,
            rotateZ: win_rect.rotation_z,
            center: r_center,
            rect: win_rect,
            nor: win_rect.nor

        }

        win_rect._attached_elements['window'] = win;
    }
    let label = win_rect.ex_prop.label;
    win.length = win_rect.w;
    win.width = win_rect.h;
    win.center = win_rect.rect_center;
    win.rect = win_rect;
    win.nor = win_rect.nor;

    if (label == "window") {
        win.type = "Window";
        win.realType = "OneWindow";
    } else if (label == "baywindow") {
        win.type = "Window";
        win.realType = "BayWindow";
    }
    else if (label == "door") {
        win.type = "Door";
        win.realType = "SingleDoor";
        win.openDirection = win_rect.u_dv_flag > 0 ? 1 : 0;
    }
    else if (label == "slidedoor") {
        win.type = "Door";
        win.realType = "SlidingDoor";
        win.openDirection = 1;
    }
    else if (label == "doorhole") {
        win.type = "Door";
        win.realType = "DoorHole";
    }
    else if (label == "railing") {
        win.type = "Window";
        win.realType = "Railing";
    }
    else if (label == "passdoor") {
        win.type = "Door";
        win.realType = "PassDoor";
    }
    else if (label == "safetydoor") {
        win.type = "Door";
        win.realType = "SafetyDoor";
    }
    else if (label == "doubledoor") {
        win.type = "Door";
        win.realType = "DoubleDoor";
    }
    return win || null;

}


export function get_room_poly_furnitures(poly: ZPolygon): ZRect[] {
    const furnitures_name = "furnitures";
    if (!poly._attached_elements[furnitures_name]) {
        poly._attached_elements[furnitures_name] = [];
    }

    return poly._attached_elements[furnitures_name];
}

export function set_room_poly_name(poly: ZPolygon, name: string) {
    poly.ex_prop['roomname'] = name;
}

export function get_room_poly_name(poly: ZPolygon) {
    return poly.ex_prop['roomname'] || "未命名";
}

export function set_room_poly_pos(poly: ZPolygon, pos: Vector3Like) {
    poly._attached_elements['roompos'] = pos;
}
export function set_room_poly_uuid(poly: ZPolygon, uuid: string) {
    poly.ex_prop['room_poly_uuid'] = '' + uuid;
}
export function get_room_poly_uuid(poly: ZPolygon) {
    return poly.ex_prop['room_poly_uuid'];
}
export function set_room_poly_id(poly: ZPolygon, id: number) {
    poly.ex_prop['room_poly_id'] = '' + id;
}
export function get_room_poly_id(poly: ZPolygon) {
    return ~~(poly.ex_prop['room_poly_id'] || '-1');
}
export function get_room_poly_pos(poly: ZPolygon): Vector3Like {
    return poly._attached_elements['roompos'] || null;
}
export function get_poly_target_props(poly: ZPolygon) {
    let params: { [key: string]: {} } = {};
}

export function  get_room_poly_of_room_rect(room_rect: ZRect) {
    return room_rect._attached_elements["room_poly"] || null;
}
export function  get_room_rect_of_room_poly(room_poly: ZPolygon, need_update: boolean = false) {
    let rect: ZRect = room_poly._attached_elements['room_rect'] || null;
    if (!rect || need_update) {
        rect = ZRect.fromBox3(room_poly.computeBBox());
        if (rect.w < rect.h) {
            rect.swapWidthAndHeight();
        }
        room_poly._attached_elements['room_rect'] = rect;
        rect._attached_elements['room_poly'] = room_poly;


        // 直接共享参数值
        rect.ex_prop = room_poly.ex_prop;
    }
    return rect;
}
 function is_deleted(wall_rect: ZPolygon) {
    return wall_rect.ex_prop['is_deleted'] === '1';
}

 function set_deleted(wall_rect: ZPolygon, t: boolean) {
    // 如果删除了，就设置is_deleted属性为1
    wall_rect.ex_prop['is_deleted'] = t ? "1" : "0";
    if (!t) {
        delete wall_rect.ex_prop['is_deleted'];
    }
}

export function refineWalls(t_wall_rects: ZRect[], offset_len: number = -1) {

    t_wall_rects.sort((a, b) => a.h - b.h);
    for (let rect0 of t_wall_rects) {
        if (is_deleted(rect0)) continue;

        let ll = -rect0.w / 2;
        let rr = rect0.w / 2;

        let left_pos = rect0.unproject({ x: -rect0.w / 2, y: 0 });
        let right_pos = rect0.unproject({ x: rect0.w / 2, y: 0 });

        for (let rect1 of t_wall_rects) {
            if (is_deleted(rect1)) continue;

            if (rect1 === rect0) continue;

            // 如果两个矩形垂直
            if (Math.abs(rect0.nor.dot(rect1.nor)) < 0.1) {

                let pp = rect0.project(rect1.rect_center);
                if (pp.x < 0) {
                    let left_p1 = rect1.project(left_pos);
                    if (Math.abs(left_p1.x) < rect1.w / 2 + 10 && Math.abs(left_p1.y) < rect1.h) {
                        ll = Math.min(ll, pp.x);
                    }
                }
                else {
                    let right_p1 = rect1.project(right_pos);
                    if (Math.abs(right_p1.x) < rect1.w / 2 + 10 && Math.abs(right_p1.y) < rect1.h) {
                        rr = Math.max(rr, pp.x);
                    }
                }
            }
            else if (Math.abs(rect0.nor.dot(rect1.nor)) > 0.9 && rect0.h <= rect1.h + 0.0001) {
                let pp = rect0.project(rect1.rect_center);
                if (pp.x < 0) {
                    let left_p1 = rect1.project(left_pos);
                    if (Math.abs(left_p1.x) < rect1.w / 2 + 10 && Math.abs(left_p1.y) < rect1.h / 2) {
                        ll = Math.min(ll, pp.x + rect1._w / 2 - rect1.h / 2)
                    }
                }
                else {
                    let right_p1 = rect1.project(right_pos);
                    if (Math.abs(right_p1.x) < rect1.w / 2 + 10 && Math.abs(right_p1.y) < rect1.h / 2) {
                        rr = Math.max(rr, pp.x - rect1._w / 2 + rect1.h / 2);
                    }
                }
            }


        }

        if (rr > rect0.w / 2) {
            if (offset_len > 0) {
                rr = rect0.w / 2 + offset_len;
            }
        }

        if (ll < -rect0.w / 2) {
            if (offset_len > 0) {
                ll = -rect0.w / 2 - offset_len;
            }
        }
        let t_center = rect0.unproject({ x: (ll + rr) / 2, y: 0 });
        rect0._w = rr - ll;
        rect0.rect_center = t_center;
    }


    for (let n_wall of t_wall_rects) {
        if (is_deleted(n_wall)) continue;
        for (let l_wall of t_wall_rects) {
            if (is_deleted(l_wall)) continue;
            if (n_wall == l_wall) continue;
            if (n_wall.containsPoly(l_wall, 0.5)) {
                set_deleted(l_wall, true);
            }
        }
    }
    let ans_rects: ZRect[] = [];
    for (let rect of t_wall_rects) {
        if (is_deleted(rect)) continue;
        ans_rects.push(rect);
    }
    return ans_rects;
}
