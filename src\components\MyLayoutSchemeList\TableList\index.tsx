import React from 'react';
import { Dropdown, Table, Tooltip, Typography } from '@svg/antd';
import type { MenuProps } from '@svg/antd';

import IconFont from '@/components/IconFont/iconFont';
// import { envConfig } from '@/common/config';
import { useStore } from '@/models';
import { getFullAddress } from '../services/address';
import useStyles from "./style"
import { SchemeEvent } from '../Content/schemeContent';

type ListItem = {
  area: number;
  layoutSchemeName: string;
  id: string;
  coverImage: string;
  updateDate: string;
  address: string;
};

const TableList: React.FC<{
  list: ListItem[];
  operation: (item: ListItem, event: SchemeEvent) => void;
  itemsToDisplay?: MenuProps['items'];
}> = ({ list, operation, itemsToDisplay = null}) => {

  const { styles } = useStyles();
  const store = useStore();

  const schemeOnClick = (item: any) => (event: any) => {
    operation(item, event.key);
  };
  const fileClick = (id: string) => {
    // window.open(`${envConfig.layoutHostDomain}/editor/?schemeId=${id}`);
  };

  const formatTime = (time: string) => {
    const date = new Date(time);
    const now = new Date();
    const diff = now.getTime() - date.getTime(); // 计算时间差
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);

    if (seconds < 60) {
      return `${seconds}秒前`;
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 31) {
      return `${days}天前`;
    } else if (months < 12) {
      return `${months}个月前`;
    } else {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  };

  return (
    <div style={{ marginBottom: '20px' }}>
      <Table
        dataSource={list} // 使用 List 作为数据源
        rowKey="id" // 设置唯一键
        pagination={false} // 去除分页
        columns={[
          {
            title: () => <div className={styles.title}>文件名</div>,
            key: 'buildingInfo',
            width: '30%', // 设置宽度为30%
            render: (record) => (
              <div
                style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                onClick={() => fileClick(record.id)}
              >
                <img src={record.coverImage} style={{ width: 32, height: 32, marginRight: 12 }} />
                <span className={styles.fileName}>{record.layoutSchemeName}</span>
              </div>
            ),
          },
          {
            title: () => <div className={styles.title}>小区地址</div>,
            key: 'roomInfo',
            width: '25%', // 设置宽度为25%
            render: (record) => (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span className={styles.text}>
                  {getFullAddress(record, store.homeStore.districtData)}
                </span>
              </div>
            ),
          },
          {
            title: () => <div className={styles.title}>最近编辑时间</div>,
            dataIndex: 'updateDate',
            key: 'updateDate',
            width: '25%', // 设置宽度为25%
            render: (text) => (
              <Tooltip>
                <Typography.Text className={styles.text} title={`最后修改: ${text}`}>
                  {formatTime(text)}
                </Typography.Text>
              </Tooltip>
            ) 
          },
          {
            title: () => <div className={styles.title}>操作</div>,
            key: 'action',
            width: '20%', // 设置宽度为20%
            render: (_, record) => (
              <div>
                <div>
                  <span
                    className={styles.text}
                    style={{ marginRight: '20px', cursor: 'pointer' }}
                    onClick={() => schemeOnClick(record)({ key: 'open' })} // 模拟点击 'open'
                  >
                    查看详情
                  </span>
                  <Dropdown
                    menu={{ items: itemsToDisplay, onClick: schemeOnClick(record) }}
                    placement="bottomLeft"
                    trigger={['click']}
                    overlayStyle={{zIndex: 10000}}
                  >
                    <IconFont type="icon-gengduo_bold" className={''} />
                  </Dropdown>
                </div>
              </div>
            ),
          },
        ]}
      />
    </div>
  );
};

export default TableList;
