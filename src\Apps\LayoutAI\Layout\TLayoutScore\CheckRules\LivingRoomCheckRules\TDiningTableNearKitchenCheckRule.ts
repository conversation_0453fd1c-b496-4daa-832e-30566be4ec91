import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomClassfierCheckRule } from "./TLivingRoomClassfierCheckRule";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { ZRect } from "@layoutai/z_polygon";
import { TLayoutScoreParamName } from "../../../TLayoutScoreConfigurationTool/TLayoutScoreParamName";
import { Vector3 } from "three";
import { I_Window } from "../../../IRoomInterface";

export class TDiningTableNearKitchenCheckRule extends TLivingRoomClassfierCheckRule
{
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    protected setParamConfig(): void {
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]): any
    {
        let kitchenDoors: I_Window[] = room.windows.filter(win => win.type == "Door" && win.room_names.includes("厨房"));
        let diningTableFigures: TFigureElement[] = this.categoryFigures.get(this.diningTableName);
        if(diningTableFigures.length == 0)
        {
            return {score: 0};
        }
        let nearDist: number = null;
        for(let diningTableFigure of diningTableFigures)
        {
            for(let kitchenDoor of kitchenDoors)
            {
                let dist: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(kitchenDoor.rect, diningTableFigure.rect);
                if(nearDist == null || nearDist > dist)
                {
                    nearDist = dist;
                }
            }
        }
        let hallwayDist: number = 900;
        if(nearDist < hallwayDist)
        {
            return {score: 10};
        }
        return {score: 0};
    }
}