import { Vector3 } from "three";
import { I_EntitiesCollection, TLayoutEntityContainer } from "../TLayoutEntityContainter";
import { I_PainterTransform } from "@layoutai/z_polygon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TRoomEntity } from "../TRoomEntity";
import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { TRoom } from "../../TRoom";
import { TRoomLayoutScheme } from "../../TLayoutScheme/TRoomLayoutScheme";
import { TPostLayoutCeiling } from "../../TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutCeiling";
import { TPostDecoratesLayout } from "../../TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutDecorates";
import { compareNames } from "@layoutai/z_polygon";
import { TWholeLayoutScheme } from "../../TLayoutScheme/TWholeLayoutScheme";
import { T_ReplaceFurnituresOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_ReplaceFunituresOperationInfo";
import { TFurnitureEntity } from "../TFurnitureEntity";
import { TBaseEntity } from "../TBaseEntity";
import { TWall } from "../TWall";
import { TSubSpaceAreaEntity } from "../TSubSpaceAreaEntity";
import { TWindowDoorEntity } from "../TWinDoorEntity";
import { TStructureEntity } from "../TStructureEntity";
import { TBaseGroupEntity } from "../TBaseGroupEntity";
import { TCombinationEntity } from "../TCombinationEntity";


/**
 *   一些LayoutContainer相关的常用函数
 */
export class LayoutContainerUtils {
    static previous_canvas: HTMLCanvasElement = null;
    static _debug : boolean = false;
    static computePainterTransformWithWholeBox(width: number, height: number, container: TLayoutEntityContainer, fixed_scale: number = 0.85) {
        if(!container._whole_bbox || container._whole_bbox.isEmpty() 
        || container._whole_bbox.max.x == Infinity)
        {
            container.updateWholeBox();
            
        }
        let size = container._whole_bbox.getSize(new Vector3());
        let center = container._whole_bbox.getCenter(new Vector3());
        let sc_0 = width / size.x;
        let sc_1 = height / size.y;
        let p_center = center;
        let p_sc = Math.min(sc_0, sc_1) * fixed_scale;

        if(p_sc < 0.0001)
        {
            p_sc = 0.0001;
        }
        let transform: I_PainterTransform = {
            _p_center: p_center,
            _p_sc: p_sc,
            _p_zval: 0
        }
        return transform;
    }
    static focusCenterByWholeBox(container:TLayoutEntityContainer,fixed_scale:number=0.5)
    {
        const painter = container.painter;
        if(!painter) return;
        if(painter._canvas.parentElement.clientWidth == 0) return;
        let c_sc = painter.clientScale;
        if(painter._canvas.parentElement)
        {
            c_sc = painter._canvas.parentElement.clientWidth / (painter?.clientWidth || painter.clientWidth);
        }
        if(!c_sc || c_sc !== c_sc || c_sc < 0.25)
        {
            c_sc = 0.25;
        }
        let ts = LayoutContainerUtils.computePainterTransformWithWholeBox(painter.width * c_sc,painter.height * c_sc,container,fixed_scale);
        painter.importTransformData(ts,false);
    }

    static BeginDrawOnCanvas(canvas: HTMLCanvasElement, container: TLayoutEntityContainer, options: { fixed_scale?: number, ts?:I_PainterTransform } = {}) {
        let painter = container.painter;
        let prev_canvas = painter._canvas;
        LayoutContainerUtils.previous_canvas = prev_canvas;
        painter.save_transform();
        painter.bindCanvas(canvas);
        container.updateWholeBox();
        
        let ts = options.ts || LayoutContainerUtils.computePainterTransformWithWholeBox(canvas.width, canvas.height, container, options.fixed_scale || 0.85);
        painter.importTransformData(ts, false);
        painter.clean();
        painter.enter_drawpoly();
        return {
            transformData: ts,
            canvas: prev_canvas
        }
    }
    static EndDrawOnCanvas(canvas: HTMLCanvasElement, container: TLayoutEntityContainer) {
        let painter = container.painter;
        painter.leave_drawpoly();
        if (LayoutContainerUtils.previous_canvas) {
            painter.bindCanvas(LayoutContainerUtils.previous_canvas);
            LayoutContainerUtils.previous_canvas = null;
        }
        painter.restore_transform();
    }

    static CanvasToWorldPos(canvas: HTMLCanvasElement, v0: { x: number, y: number }, transform: I_PainterTransform) {
        let x = v0.x * canvas.width / canvas.clientWidth;
        let y = v0.y * canvas.height / canvas.clientHeight;
        x -= canvas.width / 2;
        y -= canvas.height / 2;

        return { x: x / transform._p_sc + transform._p_center.x, y: (-y / transform._p_sc + transform._p_center.y), z: 0 };
    }
    /**
 *  更新别名，比如卧室，别名会变成卧室1，卧室2
 */
    static updateAliasName(container: TLayoutEntityContainer = null) {
        container = container || (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let t = LayoutAI_App.t;
        const nameCount: { [key: string]: number } = {};
        let room_entities =[...container._room_entities];
        room_entities.sort((a,b)=>b._area-a._area);
        // 更新主卧or次卧名称
        if(LayoutAI_App.instance.Configs.is_auto_predict_roomname)
        {
            let main_bedroom = room_entities.find((room_entity)=>room_entity.name==="主卧");
            if(!main_bedroom)
            {
                let bed_rooms = room_entities.filter((room_entity)=>room_entity.room_type==="卧室");
                bed_rooms.sort((a,b)=>b._area-a._area);
                bed_rooms.forEach((room_entity,index)=>{
                    room_entity.name = index===0?"主卧":"次卧";
                })
            }
        }

        room_entities.forEach((item: TRoomEntity) => {
            const baseName = item.name;
            if (baseName !== '未命名') {
                // 初始化计数
                if (!nameCount[baseName]) {
                    nameCount[baseName] = 0;
                }

                // 生成别名
                if (nameCount[baseName] === 0) {
                    item.aliasName = t(baseName);
                    if(baseName === "次卧")
                    {
                        item.aliasName = item.aliasName +"1";
                    }
                } else {
                    item.aliasName = `${t(baseName)}${nameCount[baseName]+1}`;
                }

                // 增加计数
                nameCount[baseName] += 1;
            }
        });
    }

    

    static updateEntityUids(force: boolean = false, start_uid: number = 15,container:TLayoutEntityContainer=null) {

        container = container ||(LayoutAI_App.instance as TAppManagerBase).layout_container;
        if (!force && !container.needs_making_wall_xml) return;

        let lastUid = start_uid;

        for (let entity of container._room_entities) {
            let uidN = entity.uidN;
            if (!uidN || uidN <= lastUid) {
                entity.uidN = ++lastUid;
                entity.uidS = entity.uidN.toString();
                entity._room.uid = entity.uidS;
            } else {
                lastUid = entity.uidN;
            }
        }

        for (let entity of container._room_entities) {
            let uidN = entity._name_uid;
            if (!uidN || uidN <= lastUid) {
                entity._name_uid = ++lastUid;
            } else {
                lastUid = entity._name_uid;
            }
        }

        for (let entity of container._wall_entities) {
            entity.uidN = ++lastUid;
            entity.uidS = entity.uidN.toString();
        }

        for (let entity of container._structure_entities) {
            entity.uidN = ++lastUid;
            entity.uidS = entity.uidN.toString();
        }

        for (let entity of container._window_entities) {
            entity.uidN = ++lastUid;
            entity.uidS = entity.uidN.toString();
        }

        return lastUid;
    }

    static getMaximalEntityUid(container:TLayoutEntityContainer): number {
         container = container ||(LayoutAI_App.instance as TAppManagerBase).layout_container;

        let maximalEntityUid = 15;

        for (let entity of container._wall_entities) {
            if (entity.uidN > maximalEntityUid) maximalEntityUid = entity.uidN;
        }

        for (let entity of container._structure_entities) {
            if (entity.uidN > maximalEntityUid) maximalEntityUid = entity.uidN;
        }

        for (let entity of container._room_entities) {
            if (entity.uidN > maximalEntityUid) maximalEntityUid = entity.uidN;
        }

        for (let entity of container._room_entities) {
            if (entity._name_uid > maximalEntityUid) maximalEntityUid = entity._name_uid;
        }

        for (let entity of container._window_entities) {
            if (entity.uidN > maximalEntityUid) maximalEntityUid = entity.uidN;
        }
        return maximalEntityUid;
    }

    static rectifyEntityUid(lastUid: number,container:TLayoutEntityContainer=null): number {
        container = container ||(LayoutAI_App.instance as TAppManagerBase).layout_container;

        let currentUid = lastUid;
        for (let entity of container._room_entities) {
            if (!entity._name_uid || entity._name_uid < 0) {
                entity._name_uid = ++currentUid;
            }
        }
        for (let entity of container._structure_entities) {
            if (!entity.uidN || entity.uidN <= 0) {
                entity.uidN = ++currentUid;
                entity.uidS = entity.uidN.toString();
            }
        }
        return currentUid;
    }

    static updateRoomEntityRoomIds(container : TLayoutEntityContainer = null)
    {
        container = container || (LayoutAI_App.instance as TAppManagerBase).layout_container;
        if(!container) return;

        let room_ids : {[key:string]:{uuid:string,room_id:number}} = {};

        let room_entities = [...container._room_entities];
        room_entities.sort((a,b)=>{
            if(a.room_id === b.room_id) {
                return a.uidN - b.uidN;
            }
            return a.room_id - b.room_id;
        });
        
        room_entities.forEach((entity, index)=>{
            if(entity.room_id >= 0)
            {
                if(!room_ids[entity.room_id])
                {
                    room_ids[entity.room_id] = {uuid:entity._uuid,room_id:entity.room_id};
                }
            }
        });
        let c_room_id = 0;
        room_entities.forEach((entity, index)=>{
            if(!room_ids[entity.room_id] || room_ids[entity.room_id].uuid !== entity._uuid)
            {
                while(room_ids[c_room_id]!==undefined) c_room_id++;
                room_ids[c_room_id] = {uuid:entity._uuid,room_id:c_room_id};
                entity._room_id = c_room_id;
            }
            c_room_id = Math.max(entity.room_id,c_room_id);

        });

    }
     /**
      * @param filter.filteredRoomUids  基于uid过滤房间
      * @param filter.filterRoomUuids   基于uuid过滤房间
      * @param filter.filteredRoomNames   基于房间名称过滤房间
      * @returns 
      */
    static getRoomEntitiesWithFilter(container:TLayoutEntityContainer, filter:{ filteredRoomUids?:string[], filterRoomUuids?:string[],filteredRoomNames?:string[]}={})
    {
        let room_entities = container._room_entities;
        if(filter.filterRoomUuids)
        {
            room_entities = room_entities.filter((room)=>compareNames(filter.filterRoomUuids,[room?._room?.uuid||"---",room._uuid||"---"]));
        }
        if(filter.filteredRoomUids)
        {
            room_entities = room_entities.filter((room)=>compareNames(filter.filteredRoomUids,[room?._room?.uid||"---"]));
        }
        if(filter.filteredRoomNames)
        {
            room_entities = room_entities.filter((room)=>compareNames(filter.filteredRoomNames,[room.roomname,room.aliasName||"---",room.name]));
        }
        return room_entities;
    }
    static postProcessInRoom(room: TRoom) {
        if(room._room_entity)
        {
            room._room_entity._updateTabletopEntities(true);
        }
        if (compareNames([room.roomname], ["厨房"])) {
            return;
        }

        TPostLayoutCeiling.instance.postAdjustHeightForCeilingAndFurnitures(room);
        TPostLayoutCeiling.instance.postAddCeilingFigures(room);
        TPostDecoratesLayout.post_add_lighting(room, room.furnitureList, {add_main_lights:true,add_decoration:false});
        TPostDecoratesLayout.post_adjust_mainlights(room, room.furnitureList);
    }
    static updateScene3D(container : TLayoutEntityContainer = null)
    {
        container = container || (LayoutAI_App.instance as TAppManagerBase).layout_container;
        if(LayoutAI_App.instance.scene3D?.isValid())
        {
            // container.updateScene3D();
            container.manager.updateScene3D();
        }
    }

    static postAutoUpdateSubAreas(room_entity:TRoomEntity,container:TLayoutEntityContainer=null,options:{force_auto_sub_area?:boolean}={})
    {
        container = container || (LayoutAI_App.instance as TAppManagerBase).layout_container;
        room_entity.updateSpaceLivingInfo(options);
        container.updateSubAreasInRooms();
        LayoutContainerUtils.postProcessInRoom(room_entity._room);

    }
   static  onSelectLayoutScheme(room: TRoom, data: { value: TRoomLayoutScheme, index: number },container : TLayoutEntityContainer = null) {
        container = container || (LayoutAI_App.instance as TAppManagerBase).layout_container;

        let scheme = data.value;
        if (scheme.room.layoutLock) return;
        if (!room) room = scheme.room;
        if (room !== scheme?.room) return;

        room.furnitureList = [];
        let figure_elements = [...(scheme as TRoomLayoutScheme).figure_list.figure_elements];
        figure_elements.sort((a, b) => {
            let differ_order = a.default_drawing_order - b.default_drawing_order;
            if (differ_order == 0) {
                return (a.min_z || 0) - (b.min_z || 0);
            }
            else {
                return differ_order;
            }
        }
        );
        room.furnitureList = figure_elements.map((val) => val.clone());
        room.furnitureList.forEach((fe) => {
            fe._room = room;
            fe.onRoomFloorThicknessChanged();
        });
        room.selectIndex = data.index || 0;

        LayoutContainerUtils.postProcessInRoom(room);

        let result : {removed_entities:TFurnitureEntity[], added_entities:TFurnitureEntity[]} = {removed_entities:[],added_entities:[]};
        if (room._room_entity) {

                let removed_entities = container.cleanInRoomFurnitures(room);
                let added_entities = container.addFunitureEnitiesInRoom(room);
    
                result.removed_entities = removed_entities;
                result.added_entities = added_entities;

            


            if(room._room_entity.livingSpaceInfo)
            {
                LayoutContainerUtils.postAutoUpdateSubAreas(room._room_entity,container,{force_auto_sub_area:true});
            }
        }

        // this.updateCandidateRects();
        container.updateRoomsFromEntities(false);
        LayoutContainerUtils.updateScene3D(container);

        LayoutAI_App.instance.update();

        return result;

    }


    static onSelectWholeLayoutScheme(data: { value: TWholeLayoutScheme, index: number }, container:TLayoutEntityContainer = null) {
        let scheme = data.value;
        if (!scheme) return;
        container = container || (LayoutAI_App.instance as TAppManagerBase).layout_container;

        // if (this._debug) Logger.instance.log("选择了布局：全屋方案" + (data.index + 1).toString());

        container._furniture_entities.length = 0;
        let new_entities : TFurnitureEntity[] = [];
        for (let room_scheme of scheme._room_scheme_list) {
            if (room_scheme.room.layoutLock) {
                container._furniture_entities.push(
                    ...room_scheme.room.furnitureList.map((ele) => {
                        return ele.furnitureEntity;
                    })
                );
                continue;
            }
            let room = room_scheme.room;
            if (!room) continue;
            room.furnitureList = [];

            let figure_elements = [...room_scheme.figure_list.figure_elements];


            figure_elements.sort((a, b) => {
                let differ_order = a.default_drawing_order - b.default_drawing_order;
                if (differ_order == 0) {
                    return (a.min_z || 0) - (b.min_z || 0);
                }
                else {
                    return differ_order;
                }
            }
            );

            room.furnitureList = figure_elements.map((val) => val.clone());


            LayoutContainerUtils.postProcessInRoom(room);

            if (room._room_entity) {
                let entities = container.addFunitureEnitiesInRoom(room);
                new_entities.push(...entities);
                if(room._room_entity.livingSpaceInfo)
                {
                    LayoutContainerUtils.postAutoUpdateSubAreas(room._room_entity,container,{force_auto_sub_area:true});
                }
            }
        }


        // this.updateCandidateRects();
        LayoutContainerUtils.updateScene3D();
        container.updateRoomsFromEntities(false);

        container.focusCenter();
        LayoutAI_App.instance.update();

        return new_entities;
    }

    static addEntityIntoCollection(entity:TBaseEntity,collection:I_EntitiesCollection)
    {
        if(!collection) return null;
        if(!entity) return null;
        if(entity.type==="Wall")
        {
            if(!collection._wall_entities) collection._wall_entities = [];
            collection._wall_entities.push(entity as TWall);
        }
        if(entity.type==="RoomArea")
        {
            if(!collection._room_entities) collection._room_entities = [];
            if(!collection._room_entities.includes(entity as TRoomEntity))
            {
                collection._room_entities.push(entity as TRoomEntity);
            }
        }
        if(entity.type==="SubArea")
        {
            if(!collection._sub_area_entities) collection._sub_area_entities = [];
            collection._sub_area_entities.push(entity as TSubSpaceAreaEntity);
        }
        if(entity.type==="Window" || entity.type==="Door")
        {
            if(!collection._window_entities) collection._window_entities = [];
            collection._window_entities.push(entity as TWindowDoorEntity);
        }
        if(entity.type==="StructureEntity")
        {
            if(!collection._structure_entities) collection._structure_entities = [];
            collection._structure_entities.push(entity as TStructureEntity);
        }
        if(entity.type==="BaseGroup")
        {
            if(!collection._basegroup_entities) collection._basegroup_entities = [];
            collection._basegroup_entities.push(entity as TBaseGroupEntity);
        }
        if(entity.type==="Furniture")
        {
            if(!collection._furniture_entities) collection._furniture_entities = [];
            collection._furniture_entities.push(entity as TFurnitureEntity);
        }
        if(entity.type==="Group")
        {
            if(!collection._combination_entities) collection._combination_entities = [];
            collection._combination_entities.push(entity as TCombinationEntity);
        }
        return collection;

    }

}