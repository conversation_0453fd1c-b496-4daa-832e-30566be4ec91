import { TRoomNameDict, TRoomNameType, TsAI_app } from '../IRoomInterface';
import { TModelRoomGroupTransferGraph } from '../TLayoutGraph/TLayoutRelations/TModelRoomTransferGraph/TModelRoomGroupTransferGraph';
import { TModelRoomTransferGraph } from '../TLayoutGraph/TLayoutRelations/TModelRoomTransferGraph/TModelRoomTransferGraph';
import { TSimpleRoomPartionGraph } from '../TLayoutGraph/TLayoutRelations/TSpacePartionGraph/TSimpleRoomPartitionGraph';
import { I_TLayoutScheme, TRoomLayoutScheme } from '../TLayoutScheme/TRoomLayoutScheme';
import { I_ExtRoom, TRoom } from '../TRoom';
import { TGraphBasicConfigs } from '../TLayoutGraph/TGraphBasicConfigs';
import { TLayoutJudgeContainter } from '../TLayoutScore/TLayoutJudge';
import { TLivingRoomJudge } from '../TLayoutScore/TLivingRoomJudge';
import { TFurnitureEntity } from '../TLayoutEntities/TFurnitureEntity';
import { I_SwjFurnitureData } from '../../AICadData/SwjLayoutData';
import { I_ModelRoomSourceType } from '../TLayoutGraph/TLayoutRelations/TModelRoomTransferGraph/TBasicRoomTransferRelation';
import { openApiRequest } from '../../../../utils';
import { LayoutAI_App } from '../../../LayoutAI_App';
import { TDreamRoomTransferGraph } from '../TLayoutGraph/TLayoutRelations/TDreamRoomTransferGraph/TDreamRoomTransferGraph';
import { TBedRoomJudge } from '../TLayoutScore/TBedRoomJudge';
import { Logger } from '../../Utils/logger';
import { TKitchenJudge } from '../TLayoutScore/TKitchenJudge';
import { TBathRoomJudge } from '../TLayoutScore/TBathRoomJudge';
import { TEntranceRoomJudge } from '../TLayoutScore/TEntranceRoomjudge';
import { TSubSpaceTransferGraph } from '../TLayoutGraph/TLayoutRelations/TSubSpaceTransferGraph/TSubSpaceTransferGraph';
import { TSubSpaceAreaEntity } from '../TLayoutEntities/TSubSpaceAreaEntity';
import { AutoLayoutAdapter } from './AutoLayoutAdapter';

export var query_solver_url = 'http://localhost:8284/apply_graph';
export const LocalStorageModelRoomsName = 'Layout_LocalModelRooms';

export type SolverMethods =
    | 'LayoutService'
    | 'BasicTransfer'
    | 'GroupTransfer'
    | 'SubSpaceTransfer'
    | 'SpacePartition'
    | 'Turing';
export const SolverMethodsDict: { [key: string]: string } = {
    BasicTransfer: '相似迁移',
    SpacePartition: '分区逻辑',
    LayoutService: '布局服务'
};
/**
 *  请求配置接口
 */
export interface I_SolverQueryConfig {
    series_ids?: string[]; // 指定套系Id
    model_room_ids?: string[]; // 指定样板间Id
    solver_methods?: SolverMethods[]; // 指定求解方法
}
export interface I_RoomTemplateResult {
    codeA?: string;
    codeB?: string;
    codeC?: string;
    nickName?: string;
    createDate?: string;
    createUser?: string;
    updateDate?: string;
    updateUser?: string;
    id?: string;
    isDelete?: number;
    platform?: number;
    templateJson?: string;
    roomImage?: string;
    roomLength?: number;
    roomWidth?: number;
    roomType?: string;
    roomName?: string;
    schemeId?: string;
    roomId?: string;
    source?: string;
    state?: number;
}

export interface I_RoomTemplateQuery {
    pageIndex?: number;
    pageSize?: number;
    // orderBy?:"update_date desc";
    lengthMin?: number;
    lengthMax?: number;
    codeA?: string;
    codeB?: string;
    roomType?: string;
    id?: string;
    schemeId?: string;
    remark?: string;
    source?: 'editor' | 'dreamer' | 'SubSpace';
}
const debug: boolean = false;

/**
 *  布局图谱求解器
 *      --- nodejs 和 web 共用该求解函数
 */
export class TSwjLayoutGraphSolver {
    /**
     *  默认的相似迁移  图谱
     */
    _model_room_transfer_graphs: { [key: string]: TModelRoomTransferGraph };

    /**
     *  组合模板迁移 图谱
     */
    _model_room_group_transfer_graphs: { [key: string]: TModelRoomGroupTransferGraph };

    /**
     *  简单逻辑 图谱
     */
    _space_partion_graphs: { [key: string]: TSimpleRoomPartionGraph };

    /**
     *  对分区进行相似迁移的图谱
     */
    _sub_space_transfer_graphs: { [key: string]: TSubSpaceTransferGraph };

    /**
     *  梦想家迁移 图谱
     */
    _dream_room_transfer_graphs: { [key: string]: TDreamRoomTransferGraph };

    /**
     *  是否向后端请求
     */
    _query_backend: boolean;

    /**
     *   加载过样板间的标记
     */
    _is_model_rooms_loaded: boolean;

    _is_query_server_model_rooms: boolean = true;

    _is_init_graph: boolean = false;

    static instance: TSwjLayoutGraphSolver = null;
    constructor() {
        this._query_backend = false;
        this._is_model_rooms_loaded = false;
        if (!this.load_rules_localstorage()) {
            // this.load_rules_from_server();
        }
    }

    importSpaceLayoutRules(data: any): boolean {
        try {
            for (let key in data) {
                (TGraphBasicConfigs as any)[key] = data[key];
            }
            return true;
        } catch (error) {
            console.log('#####载入本地临时存储规则出错!#####');
            return false;
        }
    }

    exportSpaceLayoutRules() {
        let json_data: any = {};
        json_data['SpaceLayoutRules'] = TGraphBasicConfigs.SpaceLayoutRules;

        return json_data;
    }

    save_rules_localstorage() {
        if (localStorage) {
            localStorage.setItem(
                'LayoutAI_SpaceLayoutRules',
                JSON.stringify(this.exportSpaceLayoutRules())
            );
        }
    }
    load_rules_localstorage(): boolean {
        if (localStorage) {
            let str = localStorage.getItem('LayoutAI_SpaceLayoutRules');
            if (str) {
                try {
                    return this.importSpaceLayoutRules(JSON.parse(str));
                } catch (e) {
                    console.error(e);
                    return false;
                }
            }
        }
        return false;
    }
    clean_rules_localstorage() {
        if (localStorage) {
            if (localStorage.getItem('LayoutAI_SpaceLayoutRules')) {
                localStorage.removeItem('LayoutAI_SpaceLayoutRules');
            }
        }
    }

    load_rules_from_server() {
        // ConfigService.getCurrentVersionLayoutRule((rules:any[]) => {
        //     if(rules && rules.length > 0)
        //     {
        //         ConfigService.downloadLayoutRule(rules[0].id, (ruleJson:any) => {
        //             if (this.importSpaceLayoutRules(ruleJson)) {
        //                 console.log("成功导入远程布局规则.");
        //             } else {
        //                 console.log("无法导入远程布局规则.");
        //             }
        //         }, () => {
        //             console.error("下载远程布局规则失败!");
        //         });
        //     } else {
        //         console.error("下载远程布局规则失败!");
        //     }
        // }, () => {
        //     console.error("下载远程布局规则失败!");
        // });
    }

    /**
     * 添加样板间
     * @param rooms
     */
    addModelRooms(
        rooms: I_ExtRoom[],
        source_type: I_ModelRoomSourceType = I_ModelRoomSourceType.Default
    ) {
        for (let room of rooms) {
            let room_name =
                TRoomNameDict[
                    room?.swj_room?.roomname || room?.swj_room?.name || room?.room.roomname || ''
                ] || null;
            if (!room_name) continue;
            /**
             *  提取 素材级迁移图谱
             */
            let transfer_graph = this._model_room_transfer_graphs[room_name];
            if (transfer_graph) {
                transfer_graph.addModelRoom(room, source_type);
            }
        }

        for (let key in this._model_room_transfer_graphs) {
            this._model_room_transfer_graphs[key].sortRelations();
        }
    }

    addDreamRooms(
        rooms: I_ExtRoom[],
        source_type: I_ModelRoomSourceType = I_ModelRoomSourceType.Default,
        clean_old_data: boolean = false
    ) {
        if (clean_old_data) {
            for (let key in this._dream_room_transfer_graphs) {
                this._dream_room_transfer_graphs[key]._transfer_relations.length = 0;
            }
        }

        for (let room of rooms) {
            let room_name =
                TRoomNameDict[
                    room?.swj_room?.roomname || room?.swj_room?.name || room?.room.roomname || ''
                ] || null;
            if (!room_name) continue;
            /**
             *  提取 素材级迁移图谱
             */
            let transfer_graph = this._dream_room_transfer_graphs[room_name];
            if (transfer_graph) {
                transfer_graph.addModelRoom(room, source_type);
            }
        }
    }

    /**
     *  添加用于组合迁移的样板间, 两个可以不一样
     */
    addGroupTransferModelRooms(
        rooms: I_ExtRoom[],
        src_type: I_ModelRoomSourceType = I_ModelRoomSourceType.PreQueryServer
    ) {
        for (let room of rooms) {
            let room_name =
                TRoomNameDict[room?.swj_room?.name || room?.room?.roomname || ''] || null;
            if (!room_name) continue;
            /**
             *  提取 组合级迁移图谱
             */
            let transfer_group_graph = this._model_room_group_transfer_graphs[room_name];
            if (transfer_group_graph) {
                let relation = transfer_group_graph.addModelRoom(room);
                if (relation) {
                    relation._source_type = src_type;
                }
            }
        }
    }

    addSubSpaceTransferModelRooms(
        rooms: I_ExtRoom[],
        src_type: I_ModelRoomSourceType = I_ModelRoomSourceType.PreQueryServer
    ) {
        for (let room of rooms) {
            let room_name =
                TRoomNameDict[room?.swj_room?.name || room?.room?.roomname || ''] || null;
            if (!room_name) continue;
            let transfer_group_graph =
                this._sub_space_transfer_graphs[room_name] ||
                this._sub_space_transfer_graphs[TRoomNameType.Default];
            if (transfer_group_graph) {
                transfer_group_graph.addModelRoom(room, src_type);
            }
        }
    }

    /**
     * 初始化 布局模板-图谱
     */
    initGraphs() {
        this._model_room_transfer_graphs = {};

        this._model_room_transfer_graphs[TRoomNameType.Bedroom] = new TModelRoomTransferGraph(200);
        this._model_room_transfer_graphs[TRoomNameType.Balcony] = new TModelRoomTransferGraph(200);
        this._model_room_transfer_graphs[TRoomNameType.Washroom] = new TModelRoomTransferGraph(
            1000
        );
        this._model_room_transfer_graphs[TRoomNameType.Kitchen] = new TModelRoomTransferGraph(10);
        this._model_room_transfer_graphs[TRoomNameType.LivingRoom] = new TModelRoomTransferGraph(
            1000
        );
        this._model_room_transfer_graphs[TRoomNameType.Study] = new TModelRoomTransferGraph(200);

        this._model_room_group_transfer_graphs = {};
        // this._model_room_group_transfer_graphs[TRoomNameType.Bedroom] = new TModelRoomGroupTransferGraph(2000,200);
        this._model_room_group_transfer_graphs[TRoomNameType.Balcony] =
            new TModelRoomGroupTransferGraph(2000, 200);
        // this._model_room_group_transfer_graphs[TRoomNameType.Washroom] = new TModelRoomGroupTransferGraph();
        // this._model_room_group_transfer_graphs[TRoomNameType.Study] = new TModelRoomGroupTransferGraph();
        // this._model_room_group_transfer_graphs[TRoomNameType.Kitchen] = new TModelRoomGroupTransferGraph();

        // this._model_room_group_transfer_graphs[TRoomNameType.LivingRoom] = new TModelRoomGroupTransferGraph(2000,1000);
        // this._model_room_group_transfer_graphs[TRoomNameType.Kitchen] = new TModelRoomGroupTransferGraph(650);

        this._dream_room_transfer_graphs = {};
        this._dream_room_transfer_graphs[TRoomNameType.LivingRoom] = new TDreamRoomTransferGraph();
        this._dream_room_transfer_graphs[TRoomNameType.Bedroom] = new TDreamRoomTransferGraph();
        this._dream_room_transfer_graphs[TRoomNameType.Balcony] = new TDreamRoomTransferGraph();
        this._dream_room_transfer_graphs[TRoomNameType.Washroom] = new TDreamRoomTransferGraph();
        this._dream_room_transfer_graphs[TRoomNameType.Kitchen] = new TDreamRoomTransferGraph();
        this._dream_room_transfer_graphs[TRoomNameType.Study] = new TDreamRoomTransferGraph();

        this._space_partion_graphs = {};

        this._space_partion_graphs[TRoomNameType.Kitchen] = new TSimpleRoomPartionGraph('厨房');
        this._space_partion_graphs[TRoomNameType.Bedroom] = new TSimpleRoomPartionGraph('卧室');
        this._space_partion_graphs[TRoomNameType.Study] = new TSimpleRoomPartionGraph();
        this._space_partion_graphs[TRoomNameType.Washroom] = new TSimpleRoomPartionGraph('卫生间');
        this._space_partion_graphs[TRoomNameType.Balcony] = new TSimpleRoomPartionGraph();

        this._space_partion_graphs[TRoomNameType.LivingRoom] = new TSimpleRoomPartionGraph(
            '客餐厅'
        );

        this._space_partion_graphs[TRoomNameType.Living] = new TSimpleRoomPartionGraph('客厅区');
        this._space_partion_graphs[TRoomNameType.Dining] = new TSimpleRoomPartionGraph('餐厅区');

        // 入户花园应当重写，下面这个类是客餐厅玄关柜分区的处理，重新的时候最好按照原有的框架逻辑进行
        this._space_partion_graphs[TRoomNameType.Entrance] = new TSimpleRoomPartionGraph(
            '入户花园'
        );

        this._sub_space_transfer_graphs = {};
        this._sub_space_transfer_graphs[TRoomNameType.Default] = new TSubSpaceTransferGraph();
        this._sub_space_transfer_graphs[TRoomNameType.Living] = new TSubSpaceTransferGraph(
            '客厅区'
        );
        this._sub_space_transfer_graphs[TRoomNameType.Dining] = new TSubSpaceTransferGraph(
            '餐厅区'
        );
        this._sub_space_transfer_graphs[TRoomNameType.Bedroom] = new TSubSpaceTransferGraph(
            '卧室区'
        );
        this._sub_space_transfer_graphs[TRoomNameType.Washroom] = new TSubSpaceTransferGraph(
            '卫浴区'
        );
        this._sub_space_transfer_graphs[TRoomNameType.Study] = new TSubSpaceTransferGraph('书房区');
        // 添加评分器
        TLayoutJudgeContainter.appendJudge(new TLivingRoomJudge());
        TLayoutJudgeContainter.appendJudge(new TBedRoomJudge());
        TLayoutJudgeContainter.appendJudge(new TKitchenJudge());
        TLayoutJudgeContainter.appendJudge(new TBathRoomJudge());
        TLayoutJudgeContainter.appendJudge(new TEntranceRoomJudge());

        this._is_init_graph = true;
    }

    /**
     *  清空样板间
     */
    cleanAllModelRooms() {
        this.initGraphs();
    }

    async applyRoom(room_data: I_ExtRoom, query_config: I_SolverQueryConfig = null) {
        if (this._query_backend) {
            return await this.applyRoom_backend(room_data, query_config);
        } else {
            return this.applyRoom_Local(room_data, query_config);
        }
    }

    async applyRoomWithSolvingMethods(
        room: TRoom,
        xml_str: string = '',
        solver_methods: SolverMethods[] = ['BasicTransfer', 'SpacePartition', 'Turing'],
        logger: Logger = null
    ) {
        // 有一些计算逻辑的问题, 所以用一个room的副本比较好
        let t_room: TRoom = new TRoom(room.exportRoomData());
        // 布局中用_room_entity会比room_shape更准确
        t_room._room_entity = room._room_entity;
        t_room.area = room.area;
        t_room.updateFeatures();

        let result_scheme_list = this.applyRoom_Directly(t_room, solver_methods) || [];
        return result_scheme_list;
    }
    /**
     *
     * 向后端请求
     * @param room_data
     */
    async applyRoom_backend(room_data: I_ExtRoom, query_config: I_SolverQueryConfig = null) {
        let result_scheme_list: TRoomLayoutScheme[] = [];

        let config = null;
        if (query_config) {
            config = {
                series_ids: query_config.series_ids,
                model_room_ids: query_config.model_room_ids
            };
        }

        let res = await fetch(query_solver_url, {
            method: 'post',
            body: JSON.stringify({ room_data: room_data, query_config: config })
        })
            .then(val => {
                return val.json();
            })
            .catch(err => {
                console.log(err);
                return null;
            });

        if (res && res?.scheme_list) {
            let scheme_list: I_TLayoutScheme[] = res.scheme_list;
            for (let data of scheme_list) {
                let scheme = new TRoomLayoutScheme();
                scheme.importScheme(data);
                result_scheme_list.push(scheme);
            }
        }

        console.log(result_scheme_list);
        return result_scheme_list;
    }

    /**
     *  对目标房间使用
     *   --- 返回布局方案
     */
    applyRoom_Local(room_data: I_ExtRoom, query_config: I_SolverQueryConfig = null) {
        let room_name =
            TRoomNameDict[
                room_data?.swj_room?.name ||
                    room_data?.room?.room_type ||
                    room_data?.room?.roomname ||
                    ''
            ] || null;
        if (!room_name) return null;

        let room: TRoom = null;

        if (room_data.swj_room) {
            room = new TRoom({}).fromSwjRoom(room_data.swj_room);
        } else if (room_data.room) {
            if (room_data.room) {
                room = new TRoom(room_data.room);
            }
        }
        if (!room) return null;

        TsAI_app.Quiet = false;

        room.updateFeatures();
        TsAI_app.Quiet = true;

        if (query_config?.solver_methods) {
            return this.applyRoom_Directly(room, query_config.solver_methods);
        } else {
            return this.applyRoom_Directly(room);
        }
    }

    applyRoom_Directly(
        room: TRoom,
        solver_methods: SolverMethods[] = [
            'BasicTransfer',
            'GroupTransfer',
            'SpacePartition',
            'Turing',
            'SubSpaceTransfer'
        ]
    ) {
        if (!room) return null;
        // 1、先转化成算法标准名称
        let std_room_name_in_solver =
            TRoomNameDict[room?.roomname] || TRoomNameDict[room?.room_type] || null;
        if (!std_room_name_in_solver) {
            let room_name = room?.room_type || room?.roomname || '';
            if (room_name.endsWith('区')) {
                room_name = room_name.replace('区', '');
            }
            std_room_name_in_solver = TRoomNameDict[room_name] || '';
        }
        let model_room_graph = this._model_room_transfer_graphs[std_room_name_in_solver];
        let result_scheme_list: TRoomLayoutScheme[] = [];
        if (solver_methods.includes('BasicTransfer')) {
            if (model_room_graph) {
                model_room_graph.applyRoom(room);
                for (let scheme of model_room_graph._result_scheme_list) {
                    // result_scheme_list.push(scheme);
                    TRoomLayoutScheme.appendSchemeToArray(scheme, result_scheme_list);
                }
            }
        }

        if (solver_methods.includes('GroupTransfer')) {
            let group_transfer_graph =
                this._model_room_group_transfer_graphs[std_room_name_in_solver];
            if (group_transfer_graph) {
                group_transfer_graph.applyRoom(room);
                for (let scheme of group_transfer_graph._result_scheme_list) {
                    TRoomLayoutScheme.appendSchemeToArray(scheme, result_scheme_list);
                }
            }
        }

        if (solver_methods.includes('SpacePartition')) {
            let parition_graph = this._space_partion_graphs[std_room_name_in_solver];
            if (parition_graph) {
                parition_graph.applyRoom(room);
                result_scheme_list.push(...parition_graph._result_scheme_list);
                for (let scheme of parition_graph._result_scheme_list) {
                    TRoomLayoutScheme.appendSchemeToArray(scheme, result_scheme_list);
                }
            }
        }

        if (solver_methods.includes('SubSpaceTransfer')) {
            let space_transfer_graph =
                this._sub_space_transfer_graphs[std_room_name_in_solver] ||
                this._sub_space_transfer_graphs[TRoomNameType.Default];
            space_transfer_graph.applyRoom(room);
            // console.log(space_transfer_graph);
            result_scheme_list.push(...space_transfer_graph._result_scheme_list);
        }

        if (solver_methods.includes('LayoutService')) {
            result_scheme_list.push(...AutoLayoutAdapter.applyRoom(room));
        }

        return result_scheme_list;
    }
    removeModelRoom_localstorage(room: I_ExtRoom) {
        let data_list: I_ExtRoom[] = JSON.parse(
            localStorage.getItem(LocalStorageModelRoomsName) || '[]'
        );

        let id = -1;
        for (let i = 0; i < data_list.length; i++) {
            if (data_list[i]?.room?.uuid && room?.room?.uuid) {
                id = i;
                break;
            }
        }
        if (id >= 0) {
            data_list.splice(id, 1);
        }
        localStorage.setItem(LocalStorageModelRoomsName, JSON.stringify(data_list));
    }
    async preQueryModelRoomsFromServer(page_size: number = 300) {
        if (!this._is_query_server_model_rooms) return;
        if (debug) console.time('preQueryModelRoomsFromServer');

        let result_list: { layoutData: string; state: number; [key: string]: any }[] = [];

        let fetch_func_without_code = async (room_type: string) => {
            const res = await openApiRequest({
                method: 'post',
                url: `/api/njvr/RoomLayoutTemplate/page`,
                data: {
                    pageSize: page_size,
                    orderBy: 'update_date desc',
                    lengthMin: 100,
                    roomType: room_type
                },
                timeout: 5000
            }).catch((): any => {
                return null;
            });
            if (!res?.result?.result || res.result.result.length == 0) return;
            result_list.push(...res.result.result);
            return;
        };
        let model_db: { [key: string]: I_ExtRoom[] } = {};

        let add_json_data = (data: any, res: any) => {
            let roomname = res.roomName || res.roomname || '';
            let schemeId = res.schemeId || '';
            let roomId = res.roomId || '';
            if (!roomname) return;
            if (!data.swj_room_data && data.boundary) {
                data = {
                    room_data: null,
                    swj_room_data: data
                };
            }
            let room_ext: I_ExtRoom = {
                scheme_id: schemeId,
                room_id: roomId,
                scheme_room_id: schemeId + '-' + roomId,
                room: data.room_data,
                swj_room: data.swj_room_data,
                createDate: res.createDate,
                server_data: data
            };
            if (!model_db[roomname]) model_db[roomname] = [];
            model_db[roomname].push(room_ext);
        };
        let room_types = ['客餐厅', '厨房', '阳台', '卫生间', '入户花园'];
        for (let roomtype of room_types) {
            let key = TRoomNameDict[roomtype] || null;
            if (!key) continue;
            let room_graph = this._model_room_transfer_graphs[key];
            if (room_graph) {
                room_graph.cleanRelationsWithSourceType(I_ModelRoomSourceType.PreQueryServer);
            }

            let group_graph = this._model_room_group_transfer_graphs[key];
            if (group_graph) {
                group_graph.cleanRelationsWithSourceType(I_ModelRoomSourceType.PreQueryServer);
            }
            await fetch_func_without_code(key);
        }
        let res_ids: { [key: string]: boolean } = {};
        for (let res of result_list) {
            if (res_ids[res.id]) {
                continue;
            }
            res_ids[res.id] = true;
            if (res.templateJson) {
                let data = JSON.parse(res.templateJson);
                add_json_data(data, res);
            }
        }

        for (let key in model_db) {
            model_db[key].sort((a, b) => (b.createDate || '').localeCompare(a.createDate || ''));
        }

        for (let key in model_db) {
            let rooms = model_db[key];

            this.addModelRooms(rooms, I_ModelRoomSourceType.PreQueryServer);
            this.addGroupTransferModelRooms(rooms, I_ModelRoomSourceType.PreQueryServer);
        }
        if (debug) console.timeEnd('preQueryModelRoomsFromServer');

        if (debug) console.log(this._model_room_transfer_graphs);
    }

    async cleanModelRoomsWithSrcType(
        src_type: I_ModelRoomSourceType = I_ModelRoomSourceType.QueryServer
    ) {
        for (let key in this._model_room_transfer_graphs) {
            let graph = this._model_room_transfer_graphs[key];
            graph.cleanRelationsWithSourceType(src_type);
        }

        for (let key in this._model_room_group_transfer_graphs) {
            this._model_room_group_transfer_graphs[key].cleanRelationsWithSourceType(src_type);
        }
    }
    async _queryModelTemplates(options: I_RoomTemplateQuery = {}) {
        const res = await openApiRequest({
            method: 'post',
            url: `/api/njvr/RoomLayoutTemplate/page`,
            data: {
                pageSize: 10,
                orderBy: 'update_date desc',
                ...options
            },
            timeout: 3000
        }).catch((e: any): any => {
            return null;
        });
        let result: I_RoomTemplateResult[] = res?.result?.result;
        if (!result || result.length == 0) return [];
        return result;
    }
    async queryModelRoomsFromServer(
        rooms: TRoom[],
        allow_no_code_query: boolean = true,
        clean_old_data: boolean = true
    ) {
        if (!this._is_query_server_model_rooms) return;

        let clock_t0 = new Date().getTime();
        let promise_array: Promise<void>[] = [];

        let result_list: I_RoomTemplateResult[] = [];
        for (let room of rooms) {
            if (!room.max_R_shape) {
                room.computeShapeList();
            }
            if (!room.max_R_shape) {
                console.log('计算最大矩形错误', room.uid, room.roomname);
                continue;
            }
            if (!room.room_shape._feature_shape) {
                room.updateFeatures();
            }
            let lengthMin = room.max_R_shape._rect.length - 1200;
            let lengthMax = room.max_R_shape._rect.length + 1200;

            let roomType = room.room_type;
            let codeA = room?.room_shape?._feature_shape?.level_shape_codes[0] || '';
            let codeB = room?.feature_shapes[0]?.level_shape_codes[0] || '';

            if (codeB === codeA) {
                codeB = '';
            }
            let fetch_func_with_code_B = async () => {
                let list = await this._queryModelTemplates({
                    lengthMin: lengthMin,
                    lengthMax: lengthMax,
                    codeB: codeB,
                    roomType: roomType
                });
                result_list.push(...list);
                return;
            };
            let fetch_func_with_code_A = async () => {
                let list = await this._queryModelTemplates({
                    lengthMin: lengthMin,
                    lengthMax: lengthMax,
                    codeA: codeA,
                    roomType: roomType
                });
                if (list.length == 0 && allow_no_code_query) {
                    list = await this._queryModelTemplates({
                        lengthMin: lengthMin,
                        lengthMax: lengthMax,
                        roomType: roomType
                    });
                }
                result_list.push(...list);
            };
            promise_array.push(fetch_func_with_code_A());
            if (codeB.length > 0) {
                promise_array.push(fetch_func_with_code_B());
            }
        }

        await Promise.allSettled(promise_array);

        let model_db: { [key: string]: I_ExtRoom[] } = {};

        let add_json_data = (data: any, res: any) => {
            let roomname = res.roomName || res.roomname || '';
            let schemeId = res.schemeId || '';
            let roomId = res.roomId || '';
            if (!roomname) return;
            if (!data.swj_room_data && data.boundary) {
                data = {
                    room_data: null,
                    swj_room_data: data
                };
            }
            let room_ext: I_ExtRoom = {
                scheme_id: schemeId,
                room_id: roomId,
                scheme_room_id: schemeId + '-' + roomId,
                room: data.room_data,
                swj_room: data.swj_room_data,
                createDate: res.createDate,
                server_data: data
            };
            if (!model_db[roomname]) model_db[roomname] = [];
            model_db[roomname].push(room_ext);
        };

        let res_ids: { [key: string]: boolean } = {};
        for (let res of result_list) {
            if (res_ids[res.id]) {
                continue;
            }
            res_ids[res.id] = true;
            if (res.templateJson) {
                let data = JSON.parse(res.templateJson);
                add_json_data(data, res);
            }
        }

        for (let key in model_db) {
            model_db[key].sort((a, b) => (b.createDate || '').localeCompare(a.createDate || ''));
        }

        for (let key in this._model_room_transfer_graphs) {
            let graph = this._model_room_transfer_graphs[key];
            if (clean_old_data) {
                graph.cleanRelationsWithSourceType(I_ModelRoomSourceType.QueryServer);
            }
        }
        for (let key in this._model_room_group_transfer_graphs) {
            let graph = this._model_room_group_transfer_graphs[key];
            if (clean_old_data) {
                graph.cleanRelationsWithSourceType(I_ModelRoomSourceType.QueryServer);
            }
        }

        for (let key in model_db) {
            let rooms = model_db[key];

            for (let room of rooms) {
                let extRoom: I_ExtRoom = room as I_ExtRoom;
                if (extRoom.swj_room?.furniture_list) {
                    for (let aSwjFurnitureData of extRoom.swj_room.furniture_list) {
                        if (aSwjFurnitureData.pos_z >= 8.0 && aSwjFurnitureData.pos_z <= 9.0) {
                            aSwjFurnitureData.pos_z = 0;
                        }
                    }
                }
            }

            this.addModelRooms(rooms, I_ModelRoomSourceType.QueryServer);

            this.addGroupTransferModelRooms(rooms, I_ModelRoomSourceType.QueryServer);
        }
        let clock_t1 = new Date().getTime();
        if (debug && LayoutAI_App.IsDebug)
            console.log('queryModelRoomsFromServer', clock_t1 - clock_t0, 'ms');

        // for(let key in this._model_room_transfer_graphs)
        // {
        //     let graph = this._model_room_transfer_graphs[key];
        //     TsAI_app.log(key,graph.transfer_relations);
        // }
        // if (debug) console.log(this._model_room_transfer_graphs);
    }
    async queryModelSubSpacesFromServer(
        spaceEntity: TSubSpaceAreaEntity = null,
        options: I_RoomTemplateQuery = {}
    ) {
        let query: I_RoomTemplateQuery = {};
        query.pageSize = 20; // 一页多一点
        if (spaceEntity) {
            query.lengthMax = spaceEntity.rect.length + 2000;
            query.lengthMin = spaceEntity.rect.length - 2000;
        }

        query = { ...query, ...options };
        let results: I_RoomTemplateResult[] = [];

        let scope = this;
        let promises = [
            await scope._queryModelTemplates(query).then(result => results.push(...result))
        ];
        if (spaceEntity?._space_area_room) {
            let room = spaceEntity._space_area_room;
            if (!room?.room_shape?._feature_shape) {
                room.updateFeatures();
            }
            let codeA = room?.room_shape?._feature_shape?.level_shape_codes[0] || '';
            let codeB = room?.feature_shapes[0]?.level_shape_codes[0] || '';
            if (codeA && codeA.length > 0) {
                let query_with_code_a = { ...query, codeA: codeA };
                promises.push(
                    await scope
                        ._queryModelTemplates(query_with_code_a)
                        .then(list => results.push(...list))
                );
            }
            if (codeB && codeB.length > 0 && codeA !== codeB) {
                let query_with_code_b = { ...query, codeB: codeB };
                promises.push(
                    await scope
                        ._queryModelTemplates(query_with_code_b)
                        .then(list => results.push(...list))
                );
            }
        }
        await Promise.allSettled(promises);

        let rooms: I_ExtRoom[] = [];
        results.forEach(res => {
            let data = JSON.parse(res.templateJson);
            let roomname = res.roomName || (res as any).roomname || '';
            let schemeId = res.schemeId || '';
            let roomId = res.roomId || '';
            if (!roomname) return;
            if (!data.swj_room_data && data.boundary) {
                data = {
                    room_data: null,
                    swj_room_data: data
                };
            }
            let room_ext: I_ExtRoom = {
                scheme_id: schemeId,
                room_id: roomId,
                scheme_room_id: schemeId + '-' + roomId,
                room: data.room_data,
                swj_room: data.swj_room_data,
                createDate: res.createDate,
                server_data: data
            };

            rooms.push(room_ext);
        });
        this.addSubSpaceTransferModelRooms(rooms, I_ModelRoomSourceType.QueryServer);
    }

    async queryDreamerRoomsFromServer(
        rooms: TRoom[],
        source: string = 'dreamer',
        clean_old: boolean = true,
        is_platform: boolean = true
    ) {
        let clock_t0 = new Date().getTime();

        let promise_array: Promise<void>[] = [];
        let result_list: { layoutData: string; state: number; [key: string]: any }[] = [];

        for (let room of rooms) {
            if (!room.max_R_shape) {
                room.computeShapeList();
            }
            if (!room.max_R_shape) {
                console.log('计算最大矩形错误', room.uid, room.roomname);
                continue;
            }
            if (!room.room_shape._feature_shape) {
                room.updateFeatures();
            }
            let lengthMin = room.max_R_shape._rect.length - 1200;
            let lengthMax = room.max_R_shape._rect.length + 1200;
            let area_min = room.area - 10;
            let area_max = room.area + 10;
            let roomType = room.room_type;
            let codeA = room?.room_shape?._feature_shape?.level_shape_codes[0] || '';

            let fetch_func_with_code_A = async () => {
                const res = await openApiRequest({
                    method: 'post',
                    url: `/api/njvr/RoomLayoutTemplate/page`,
                    data: {
                        pageSize: 5,
                        orderBy: 'update_date desc',
                        lengthMin: lengthMin,
                        lengthMax: lengthMax,
                        platList: is_platform ? [2] : [0],
                        source: source,
                        roomType: roomType,
                        codeA: codeA
                    },
                    timeout: 3000
                }).catch((e: any): any => {
                    return null;
                });
                if (!res?.result?.result || res.result.result.length == 0) return;
                result_list.push(...res.result.result);
                return;
            };
            promise_array.push(fetch_func_with_code_A());

            let codeB = room?.room_shape?._feature_shape?.level_shape_codes[1] || '';

            if (codeB && codeB !== codeA) {
                let fetch_func_with_code_B = async () => {
                    const res = await openApiRequest({
                        method: 'post',
                        url: `/api/njvr/RoomLayoutTemplate/page`,
                        data: {
                            pageSize: 5,
                            orderBy: 'update_date desc',
                            lengthMin: lengthMin,
                            lengthMax: lengthMax,
                            platList: is_platform ? [2] : [0],
                            source: source,
                            roomType: roomType,
                            codeB: codeB
                        },
                        timeout: 3000
                    }).catch((e: any): any => {
                        return null;
                    });
                    if (!res?.result?.result || res.result.result.length == 0) return;
                    result_list.push(...res.result.result);
                    return;
                };
                promise_array.push(fetch_func_with_code_B());
            }

            let fetch_func = async () => {
                const res = await openApiRequest({
                    method: 'post',
                    url: `/api/njvr/RoomLayoutTemplate/page`,
                    data: {
                        pageSize: 50,
                        orderBy: 'update_date desc',
                        areaMin: area_min,
                        areaMax: area_max,
                        platList: is_platform ? [2] : [0],
                        source: source,
                        roomType: roomType
                    },
                    timeout: 3000
                }).catch((e: any): any => {
                    return null;
                });
                if (!res?.result?.result || res.result.result.length == 0) return;
                result_list.push(...res.result.result);
                return;
            };
            promise_array.push(fetch_func());
        }

        await Promise.allSettled(promise_array);

        let model_db: { [key: string]: I_ExtRoom[] } = {};

        let add_json_data = (data: any, res: any) => {
            let roomname = res.roomName || res.roomname || '';
            let schemeId = res.schemeId || '';
            let roomId = res.roomId || '';
            if (!roomname) return;
            if (!data.swj_room_data && data.boundary) {
                data = {
                    room_data: null,
                    swj_room_data: data
                };
            }
            let room_ext: I_ExtRoom = {
                scheme_id: schemeId,
                room_id: roomId,
                scheme_room_id: schemeId + '-' + roomId,
                room: data.room_data,
                swj_room: data.swj_room_data,
                createDate: res.createDate,
                server_data: data
            };
            if (!model_db[roomname]) model_db[roomname] = [];
            model_db[roomname].push(room_ext);
        };

        let res_ids: { [key: string]: boolean } = {};
        for (let res of result_list) {
            if (res_ids[res.id]) {
                continue;
            }
            res_ids[res.id] = true;
            if (res.templateJson) {
                let data = JSON.parse(res.templateJson);
                add_json_data(data, res);
            }
        }

        for (let key in model_db) {
            model_db[key].sort((a, b) => (b.createDate || '').localeCompare(a.createDate || ''));
        }

        this.addDreamRooms([], I_ModelRoomSourceType.Default, clean_old);

        for (let key in model_db) {
            let rooms = model_db[key];

            this.addDreamRooms(rooms, I_ModelRoomSourceType.Default, false);
        }

        let clock_t1 = new Date().getTime();
        if (debug && LayoutAI_App.IsDebug)
            console.log('queryDreamerRoomsFromServer', clock_t1 - clock_t0, 'ms');

        // console.log(this._dream_room_transfer_graphs);
    }

    /**
     *  查询布局相似的样板间Ids
     */
    queryDreamerRoomsWithLayout(rooms: TRoom[]) {
        let scheme_results: { [key: string]: { score?: number } } = {};
        rooms.forEach(room => {
            let room_name = TRoomNameDict[room?.roomname] || TRoomNameDict[room?.room_type] || null;
            if (!room_name) return;
            let graph = this._dream_room_transfer_graphs[room_name];
            if (!graph) return;

            let res = graph.querySimilarDreamRooms(room, 9);

            res.forEach((relation, index) => {
                if (!scheme_results[relation._dream_room_data.scheme_id]) {
                    scheme_results[relation._dream_room_data.scheme_id] = { score: 0 };
                }
                let data = scheme_results[relation._dream_room_data.scheme_id];
                data.score += relation._fit_similar_score; // 命中的数量
            });
        });

        let scheme_id_list = Object.keys(scheme_results);

        scheme_id_list.sort((a, b) => scheme_results[b].score - scheme_results[a].score);

        let ans_results: { scheme_id: string; score: number }[] = scheme_id_list.map(id => {
            return { scheme_id: id, score: scheme_results[id].score };
        });
        return ans_results;
    }

    // 本地存储的样板间
    cleanModelRooms_localstorage() {
        if (!localStorage) return;
        localStorage.removeItem(LocalStorageModelRoomsName);
    }

    saveModelRooms_localstorage(rooms: TRoom[]) {
        if (!localStorage) return;

        let room_data_list: I_ExtRoom[] = [];
        for (let room of rooms) {
            if (room._furniture_list.length == 0) continue;
            let room_data = room.exportSwjRoomData();
            room_data.furniture_list = [];

            for (let figure_ele of room._furniture_list) {
                let rect = figure_ele.rect;

                let entity: TFurnitureEntity =
                    rect._attached_elements['Entity'] || new TFurnitureEntity(figure_ele);

                let t_data = entity.exportData() as I_SwjFurnitureData;
                t_data._figure_element = entity.figure_element.exportJson();
                room_data.furniture_list.push(t_data);
            }
            let t_room_data = room.exportExtRoomData();
            t_room_data.swj_room = room_data;
            room_data_list.push(t_room_data);
        }
        if (room_data_list.length == 0) return;
        let data_list: I_ExtRoom[] = JSON.parse(
            localStorage.getItem(LocalStorageModelRoomsName) || '[]'
        );
        for (let new_data of room_data_list) {
            let has_data: boolean = false;
            for (let old_data of data_list) {
                if (TRoom.compareRoomData(new_data, old_data) > 0) {
                    has_data = true;
                    break;
                }
            }
            if (!has_data) data_list.push(new_data);
        }

        localStorage.setItem(LocalStorageModelRoomsName, JSON.stringify(data_list));
    }
    loadModelRooms_localstorage() {
        if (!localStorage) return;
        let data_list: I_ExtRoom[] = JSON.parse(
            localStorage.getItem(LocalStorageModelRoomsName) || '[]'
        );

        for (let key in this._model_room_transfer_graphs) {
            let graph = this._model_room_transfer_graphs[key];
            graph.cleanRelationsWithSourceType(I_ModelRoomSourceType.LocalStorage);
        }
        this.addModelRooms(data_list, I_ModelRoomSourceType.LocalStorage);
    }
}
