import { <PERSON>uler, Group, Matrix4, Mesh, Object3D, PointLight, Scene, Vector3, Vector3Like } from "three";
import { generateUUID } from "three/src/math/MathUtils.js";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import SunvegaAPI from "@api/clouddesign";

import { g_FigureImagePaths } from "../../../LayoutAI/Drawing/FigureImagePaths";
import { ModelLocPublicCategoryMap } from "../../AICadData/ModelLocPubliccategoryMap";
import { I_SwjCabinetData, I_SwjFurnitureData, I_SwjFurnitureGroup } from "../../AICadData/SwjLayoutData";
import { Model3dApi } from "../../Api/Model3dApi";
import { g_figure_alias_dict } from "../../Drawing/FigureImagePaths";
import { TPainter } from "../../Drawing/TPainter";
import { GLTFExporter } from "../../Scene3D/exporters/GLTFExporter";
import { MeshBuilder } from "../../Scene3D/MeshBuilder";
import { MeshName, SwitchConfig, UserDataKey } from "../../Scene3D/NodeName";
import { FigureTopViewer } from "../../Scene3D/process/FigureTopViewer";
import { Utils3D } from "../../Scene3D/Utils3D";
import { MaterialService } from "../../Services/MaterialMatching/MaterialService";
import { TMaterialMatchingConfigs } from "../../Services/MaterialMatching/TMaterialMatchingConfigs";
import { compareNames, Vec3toMeta } from "@layoutai/z_polygon";
import { I_ZRectData, ZPolygon, ZRectShapeType } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ZRectShape } from "@layoutai/z_polygon";
import { I_DesignMaterialInfo, I_MaterialMatchingItem, isCustomCabinet, isGroupDesignMaterialInfo, isGroupMaterial, isGroupMaterialMatchingItem, isProductFurniture } from "../IMaterialInterface";
import { AI_PolyTargetType, I_SimpleFigureElement } from "../IRoomInterface";
import { TBaseEntity } from "../TLayoutEntities/TBaseEntity";
import { TBaseGroupEntity } from "../TLayoutEntities/TBaseGroupEntity";
import { TFurnitureEntity } from "../TLayoutEntities/TFurnitureEntity";
import { TGraphBasicConfigs } from "../TLayoutGraph/TGraphBasicConfigs";
import { TSerialSizeRangeDB } from "../TLayoutGraph/TGroupTemplate/TSeriesSizeRangeDB";
import { TLayoutRelation } from "../TLayoutGraph/TLayoutRelations/TLayoutRelation";
import { TBaseRoomToolUtil } from "../TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TSize } from "../TModels/TSizeRange";
import { TRoom } from "../TRoom";
import { FigureCategoryManager } from "./FigureCategoryManager";
import { TBoard3D } from "@layoutai/z_polygon";

import { GltfManager, SceneMaterialMode } from "@layoutai/model3d_api";
import { PERMISSIONS } from "@/config/permissions";

export enum FigureDeformType {
    None = 0,
    ParamList = 1, // 备选参数列表, 尺寸链
    Scale = 2 // 变形
}


export type DecorationType = "Lighting" | "Electricity" | "OnTable" | "OnWall" | "InCabinet" | "ViewCamera" | "";
export type FigureShapeType = "矩形" | "L形" | "圆形" | "一字形" | "弧形" | "U形" | "钻石形" | ZRectShapeType;
export type CloseDirectionType = "左收口" | "右收口" | "左右收口" | "左右见光";
export const TargetDefaultModelId = "target_default_model_id";
export interface I_FigureParams {
    length?: number; // 矩形长
    depth?: number; // 矩形深
    height?: number; // 模型高度
    l_ex_width?: number; // L形 挤出部分长
    l_ex_depth?: number;  // L形 挤出部分深
    shape?: ZRectShapeType; // 基本形状
    close_direction?: CloseDirectionType;
    off_land_rule?: string;
    topOffSet?: number;

    zval?: number;

    alignTopZval?: number;

    serialId?: string;  // 当前系列ID
    materialId?: string;  // 当前材质Id
    entityId?: number; //3D实体ID

    //  以下两个数据结构可能还会改
    serial_material_ids?: { [key: string]: string[] }; // serialId -> marerialId[] 可能会返回多个材质id
    materialIds?: string[]; // 备选的材质Id，以后可能有用

    [key: string]: any;
}
export interface I_DeformMethod {
    method_type?: FigureDeformType;

    params_list?: I_FigureParams[];

    width_range?: number[];
    height_range?: number[];

    params_updater?: (method?: I_DeformMethod, figure?: TFigureElement) => void;

}
export interface I_FigureElement extends I_SimpleFigureElement {
    shape?: FigureShapeType;
    params?: I_FigureParams;
    category?: string;
    modelLoc?: string;
    public_category?: string; // 公共分类, 对齐平台
    material_name?: string;
    sub_category?: string;
    // 标签
    tags?: string[];
    priority?: number;
    image_path?: string;
    fill_color?: string;
    model_flag?: string;
    deform_method?: I_DeformMethod;
    mirror?: number;
    min_z?: number;
    max_z?: number;
    z_level?: number;
    _rect_data?: I_ZRectData;
    _matched_rect_data?: I_ZRectData;
    _matched_material?: I_MaterialMatchingItem;
    rect?: ZRect;
    _group_cate?: string;
    _group_uuid?: string;
    _group_main_figure?: boolean;
    /**
     *  是否是饰品图元
     */
    _is_decoration?: boolean;
    /**
     *  饰品类型
     */
    _decoration_type?: DecorationType;
    _is_sub_board?: boolean;
    _combined_rects?: TFurnitureEntity[];
    _ex_prop?: { [key: string]: number | string };

    decorations?: I_FigureElement[];
    wireFrameImageUrl?: string;
    _candidate_materials?: I_MaterialMatchingItem[];
    pictureViewImg?: string;
}

export interface I_FigureSolverParam {
    _init_position?: Vector3Like; // 初始位置
    _init_nor?: Vector3Like; // 初始方向
    _nor_offset_range?: { min: number, max: number, weight?: number }; // 法向移动许可范围 以及 粘滞权重(越大就越不要动)
    _dir_offset_range?: { min: number, max: number, weight?: number }; // 切向移动许可范围 以及 粘滞权重(越大就越不要动)

    _applied_relations?: TLayoutRelation[];
}
export interface I_ElementPoseData {
    nor: Vector3;
    rect_center: Vector3;
    score: number;
    _score_0?: number;
    w?: number;
    h?: number;
    [key: string]: any;
}

export interface I_SerialMaterialInfo {
    modelLoc: string;
    publicCategory: string;
    length: number;
    width: number;
    height: number;
    materialId: string;
    imageUrl: string;
}

/**
 *  待编辑的图元
 */
export enum FigureRectForEditing {
    Default = 0,
    MatchedRect = 1
}

export class TFigureElement {
    uuid: string;
    /**
     *  默认为空(undefined), 用于绑定实体uuid
     */
    material_source = "series"; //素材来源：series、organ、platform（套系、企业库、云素材）
    _entity_uuid: string;
    params: I_FigureParams;
    material_name: string;
    category: string; // 标准图元分类
    public_category: string; // 公共分类, 对齐平台
    sub_category: string;    // 子分类, 一般为空 

    // 标签
    tags?: string[];
    /*
    成品	
        1	模型	
        2	贴图	
        3	门窗	
        4	替换材质	
        15	模型组合	
        37	自由建模	
        38	3D素材配置	

    全屋定制	
        10	衣柜	
        11	橱柜	
        12	系统柜	
        13	卫浴	
        17	整木	
        19	衣柜组合	


        20	橱柜组合	
        21	系统柜组合	
        22	定制通用	
        23	浴室柜	
        24	淋浴房	
        28	铝门窗	

    硬装定制	
        8	吊顶	还在使用，顶墙回读8和18两个模块数据
        14	铺砖	
        26	铺砖	定制硬装-铺砖-瓷砖-添加贴图-混铺设置-多砖混铺
        18	顶墙	以前吊顶是8，已与8合并
        25	水电	
        36	涂料（硅藻泥）	
        41	照明	
        42	装配式	
        101	3D++
    */
    model_flag?: string;

    _shape?: FigureShapeType;
    image_path: string;
    img: HTMLImageElement;
    fill_color: string;
    priority: number;
    deform_method: I_DeformMethod;
    z_level: number;

    min_z?: number;
    max_z?: number;

    _highlight: boolean = false;
    _spotlight: boolean = false;


    /**
     *   只有阵列结果图元才有这个属性;说明它是由一个图元阵列而得。 若为空，则不在阵列当中。
     */
    _array_ref_figure: TFigureElement;


    _solver_params: I_FigureSolverParam;

    _ex_prop: { [key: string]: number | string };

    /**
     *  所在的组合编码
     */
    _group_cate: string;

    /**
     *  所在组合的uuid
     */
    _group_uuid?: string;

    /**
     *   是否是组合的主元
     */
    _group_main_figure?: boolean;

    /**
     *  是否是饰品图元
     */
    _is_decoration?: boolean;

    /**
     *  是否是补板
     */
    _is_sub_board?: boolean;
    /**
     *  饰品类型
     */
    _decoration_type?: DecorationType;

    /**
     *  私有变量, 只有bindRect才能指针级别替换
     */
    protected _rect: ZRect;

    /**
     *  组合图元
     */
    _combined_rects?: TFurnitureEntity[];

    /**
     *  原布局图元矩形
     */
    _figure_rect: ZRect;

    /**
     *  素材图元矩形
     */
    public _matched_rect: ZRect;

    _polygon: ZPolygon; // 有时候 会是一块多边形台面
    _board3d: TBoard3D;

    _matched_material: I_MaterialMatchingItem;

    /**
     *  当前归属房间, 默认是undefined
     */
    _room: TRoom;

    /**
     *  所在分区
     */
    _subarea: string;

    /**
     *  默认匹配的素材列表
     */
    _default_matched_material: I_MaterialMatchingItem;

    _3dpreview_matched_material: I_MaterialMatchingItem;

    _candidate_materials: I_MaterialMatchingItem[];

    _locked: boolean;


    private _design_material_info: I_DesignMaterialInfo;

    static EntityName = "FigureElement";


    // 扩展属性名 --- 一般存在 rect attached_elements 里面
    static LayonElement = "ParentElement";

    static DecorationElements = "DecorationElements";

    static TargetElement = "TargetElement";

    static LeftElement = "LeftElement";

    static RightElement = "RightElement";

    static LeftWallEdge = "LeftWallEdge";

    static BackWallEdge = "BackWallEdge";

    static RightWallEdge = "RightWallEdge";

    static ParentWallRect = "ParentWallRect";

    static AdjustRect = "AdjustRect";

    static CeilingElement = "CeilingElement";

    static IsTableTopFrontEdge = "IsTableTopFrontEdge";

    static IsMatchedRect = "IsMathcedRect";

    static CabinetStyleId = "CabinetStyleId";

    public static SimpleMesh3DVisible: boolean = true;

    /**
     *  简化3D数据
     */
    _simple_mesh3D: Object3D;

    /**
     *  实体3D数据
     */
    _solid_mesh3D: Object3D;


    /**
     * 评分器的异常图元标志
    */
    private _isAbnormalFigure: boolean;


    /**
     *  安装类型
     */
    installType: number;
    /**
     * 
     * 灯具形状
     */
    _rect_shape?: ZRectShapeType;
    /**
     * 顶部偏移
     */
    private _topOffset?: number;

    _wireFrameImage: HTMLImageElement;
    pictureViewImg: HTMLImageElement;
    constructor(data?: I_FigureElement) {

        this.uuid = generateUUID();
        if (data?.uuid && !(data instanceof TFigureElement)) {
            this.uuid = data.uuid;
        }
        this._topOffset = 0;
        data = data || {};
        this.params = {};
        if (data.params) {
            for (let key in data.params) {
                this.params[key] = data.params[key];
            }
        }
        this.material_name = data.material_name || (data?._ex_prop?.material_name as string) || "";
        this.params.length = this.params.length || 1000;
        this.params.depth = this.params.depth || 1000;
        this.params.height = this.params.height || 2600;
        this.image_path = g_FigureImagePaths[data.sub_category]?.png || '';
        this.priority = data.priority || 0;
        this.fill_color = data.fill_color || "#fff";
        this.category = data.category || "Default";
        this.public_category = data.public_category;
        this.sub_category = data.sub_category || "Default";
        this.tags = data.tags ? [...data.tags] : [];
        this.model_flag = data.model_flag || "1";
        this.deform_method = data.deform_method || null;
        this._rect = new ZRect(this.params.length || 100, this.params.depth || 100);
        this._rect._attached_elements[TFigureElement.EntityName] = this;
        this._matched_rect = null;
        this._figure_rect = null;
        this.z_level = data.z_level || 0;
        this.min_z = data.min_z || 0;
        this.max_z = data.max_z || 0;
        if (data._rect_shape) {
            this._rect_shape = data?._rect_shape;
        }
        this._polygon = null;
        this._shape = data.shape;
        if (data.shape === ZRectShapeType.LShape) {

            let l_shape = new ZRectShape(this.rect.w, this.rect.h);
            l_shape._l_extrude_height = data.params.l_ex_depth || 100;
            l_shape._l_extrude_width = data.params.l_ex_width || 100;
            this._polygon = l_shape;
        }

        this.updateFigure();
        // if(this.image_path)
        // {
        //     this.img = new Image();
        //     this.img.src = this.image_path;
        // }

        this._solver_params = null;
        this._ex_prop = {};
        this._group_cate = data._group_cate || "";
        this._group_uuid = data._group_uuid || "";
        this._group_main_figure = data._group_main_figure || false;
        this._decoration_type = data._decoration_type || "";
        this._is_sub_board = data._is_sub_board;
        this._combined_rects = [];
        this._is_decoration = data._is_decoration || false;
        if (data._ex_prop) {
            for (let ekey in data._ex_prop) {
                this._ex_prop[ekey] = '' + data._ex_prop[ekey];
            }

        }

        if (data.rect) {
            this.rect.copy(data.rect);
        }
        else if (data._rect_data) {
            this.rect.importRectData(data._rect_data);
        }

        if (this.rect) {
            this.rect.zval = data?.rect?.zval || this.min_z || 0;
        }
        this._board3d = null;
        this._matched_material = data._matched_material || {
            modelId: null,
            modelLoc: "",
            publicCategoryName: "",
            length: 0,
            width: 0,
            height: 0,
            imageUrl: ""
        } as I_MaterialMatchingItem;

        if (data._matched_rect_data) {
            this._matched_rect = new ZRect(1, 1);
            this._matched_rect.importRectData(data._matched_rect_data);
        }
        if (data.mirror !== undefined) {
            this.mirror = data.mirror;
        }

        this._locked = false;
        this._isAbnormalFigure = false;
        if (data.wireFrameImageUrl) {
            let img = new Image();
            img.src = data.wireFrameImageUrl;
            img.crossOrigin = "Anonymous";
            img.onload = () => {
                this._wireFrameImage = img;
                LayoutAI_App.instance.update();
            }
        } else {
            this._wireFrameImage = null;
        }
        if (data._matched_material?.topViewImage) {
            let img = new Image();
            img.src = data._matched_material.topViewImage;
            img.crossOrigin = "Anonymous";
            img.onload = () => {
                this.pictureViewImg = img;
                LayoutAI_App.instance.update();
            }
        } else {
            this.pictureViewImg = null;
        }
    }

    public makeMaterialItemByMaterialId(materialId: string) {
        let rect = this.rect;
        let pos = rect.rect_center_3d;
        let matchedItem: I_MaterialMatchingItem = {
            modelId: materialId,
            modelLoc: this.modelLoc,
            publicCategoryName: this.modelLoc,
            length: this.length,
            width: this.width,
            height: this.height,
            imageUrl: "",
            targetPosition: Vec3toMeta(pos),
            targetSize: { length: this.length, width: this.width, height: this.height },
            targetRotation: { x: 0, y: 0, z: rect.rotation_z },
            roomUid: '' + (this._room?.room_id || 0),
            originalLength: this.length,
            originalWidth: this.width,
            originalHeight: this.height
        } as I_MaterialMatchingItem;
        return matchedItem;
    }
    public static createSimple(modelLoc: string): TFigureElement {
        let fe = new TFigureElement({
            category: modelLoc,
            public_category: modelLoc,
            sub_category: modelLoc,
            params: {
                length: 0,
                depth: 0,
                height: 100
            },
            _rect_data: {
                w: 0,
                h: 0,
                nor: {} as Vector3Like,
                u_dv_flag: null
            }
        });
        fe._rect.rotation_z = 0;
        return fe;
    }

    public clearMatchedMaterials() {
        this._matched_material = {
            modelId: null,
            modelLoc: "",
            publicCategoryName: "",
            length: 0,
            width: 0,
            height: 0,
            imageUrl: ""
        } as I_MaterialMatchingItem;
        this._candidate_materials = [];
        this.pictureViewImg = null;
        this.renderedTopViewImg = null;
        this.matched_rect = null;
        this.spotlight = false;
        this.updateMesh3DWithDesignMaterialInfo();

        this.markMaterialAsVisible();
        if (this.furnitureEntity && this.furnitureEntity.clearMatchedMaterials) {
            this.furnitureEntity.clearMatchedMaterials();
        }
        if (!TFigureElement.SimpleMesh3DVisible && this._simple_mesh3D) {
            this._simple_mesh3D.removeFromParent();
            this._simple_mesh3D = null;
        } 
        if (this._solid_mesh3D) {
            this._solid_mesh3D.removeFromParent();
            this._solid_mesh3D = null;
        }
    }

    public clearAllMatchedMaterials(): void {
        this.clearMatchedMaterials();
        if (this.disassembledElements) {
            this.disassembledElements.forEach((fe) => fe.clearMatchedMaterials());
        }
        if (this.decorationElements) {
            this.decorationElements.forEach((fe) => fe.clearMatchedMaterials());
        }
    }

    public updateMatchedMaterialByMatchedRect() {
        if (!this._matched_material || !this._matched_rect) return;

        let pos = this._matched_rect.rect_center;

        let zval = this._matched_material?.targetPosition?.z || 0;
        this._matched_material.targetPosition = Vec3toMeta(pos);
        this._matched_material.targetPosition.z = zval;

        if (!this._matched_material.targetSize) {
            this._matched_material.targetSize = { length: this.matched_rect.w, width: this.matched_rect.h, height: this._matched_material.originalHeight || 2400 };
        }
        this._matched_material.targetSize.length = this.matched_rect.length;
        this._matched_material.targetSize.width = this.matched_rect.depth;

        this._matched_material.targetRotation = { x: 0, y: 0, z: this._matched_rect.rotation_z };
    }

    public updateMatchedMaterialByRect() {
        if (!this._matched_material || !this._rect) return;

        let pos = this._rect.rect_center;

        let zval = this._matched_material?.targetPosition?.z || 0;
        this._matched_material.targetPosition = Vec3toMeta(pos);
        this._matched_material.targetPosition.z = zval;

        if (!this._matched_material.targetSize) {
            this._matched_material.targetSize = { length: this._rect.w, width: this._rect.h, height: this._matched_material.originalHeight || 2400 };
        }
        this._matched_material.targetSize.length = this._rect.length;
        this._matched_material.targetSize.width = this._rect.depth;

        this._matched_material.targetRotation = { x: 0, y: 0, z: this._rect.rotation_z };
    }

    static compare_drawing_order(a: TFigureElement, b: TFigureElement) {
        const level_score = (fig: TFigureElement) => {
            if (fig.sub_category.indexOf("毯") >= 0) {
                return -2;
            }
            if (fig.sub_category.indexOf("椅") >= 0) {
                return -1;
            }
            return 1;
        }

        if (a.min_z < 300 && b.min_z < 300) {
            let score_a = level_score(a);
            let score_b = level_score(b);
            if (score_a == score_b) {
                return a.max_z - b.max_z;
            }
            else {
                return score_a - score_b;
            }
        }
        else {
            return a.max_z - b.max_z;
        }
    }
    convertSubCategoryToModelLoc(subCategory: string): string {
        switch (subCategory) {
            case "转角沙发":
                return "沙发";
            case "多人沙发":
                return "沙发";
            case "单人沙发":
                return "休闲椅";
            case "方几":
                return "茶几";
            default:
                return null;
        }
    }

    get disassembledElements(): TFigureElement[] {
        if (this.furnitureEntity != null && this.furnitureEntity instanceof TBaseGroupEntity) {
            return (this.furnitureEntity as TBaseGroupEntity).disassembled_figure_elements;
        } else {
            return null;
        }
    }

    set decorationElements(figures: TFigureElement[]) {
        this.rect._attached_elements[TFigureElement.DecorationElements] = figures;
    }

    get decorationElements(): TFigureElement[] {
        if (this.rect?._attached_elements) {
            return this.rect._attached_elements[TFigureElement.DecorationElements];
        } else {
            return null;
        }
    }

    set cabinetStyleId(s: string) {
        this.rect._attached_elements[TFigureElement.CabinetStyleId] = s;
    }

    get cabinetStyleId() {
        return this?.rect?._attached_elements[TFigureElement.CabinetStyleId] || null;
    }

    /**
     *  如果是饰品, 则看其绑定的图元
     */
    get attchedToTargetElement() {
        if (this._rect?._attached_elements) {
            return this._rect._attached_elements[TFigureElement.TargetElement] || null;
        }
        else {
            return null;
        }
    }
    set attchedToTargetElement(ele: TFigureElement) {
        if (this._rect?._attached_elements) {
            this._rect._attached_elements[TFigureElement.TargetElement] = ele;
        }
    }

    get highlight(): boolean {
        return this._highlight
    }

    get spotlight(): boolean {
        return this._spotlight
    }

    set highlight(value: boolean) {
        this._highlight = value;
        if (value) {
            this._spotlight = false;
        }
    }

    set spotlight(value: boolean) {
        this._spotlight = value;
        if (value) {
            this._highlight = false;
        }
    }


    get modelLoc(): string {
        if (compareNames([this.category], ["地柜-单门地柜", "地柜-转角炉灶地柜", "地柜-转角水槽地柜",
            "地柜-开放地柜边柜", "地柜-转角地柜", "吊柜-单门吊柜", "吊柜-开放吊柜边柜",
            "吊柜-转角吊柜","高柜-功能高柜"], false) == 1) {
            return this.category + `(${this.mirror ? "右" : "左"})`;
        }
        if (compareNames([this.category], ["小板件-地柜收口板", "小板件-吊柜收口板"], false) == 1) {
            return this.category + `(${this.mirror ? "左" : "右"})`;
        }
        if (compareNames([this.category], ["烟机吊柜"])) {
            return "吊柜-烟机吊柜(侧吸)";
        }
        if (compareNames([this.category], ["米箱地柜"])) {
            return "地柜-米箱柜";
        }
        if (compareNames([this.category], ["背景墙"])) {
            if (this.sub_category.indexOf("电视背景墙") >= 0 || this.sub_category.indexOf("沙发背景墙") >= 0) {
                return this.sub_category || this.category;
            }
        }
        return this.category;
    }

    set modelLoc(s: string) {
        this.category = s;
    }

    get modelFlag(): string {
        return this.model_flag;
    }

    get mirror() {
        return this._rect.u_dv_flag < 0 ? 1 : 0;
    }

    set mirror(t: number) {
        if (t > 0.5) {
            this._rect._u_dv_flag = -1;
        }
        else {
            this._rect._u_dv_flag = 1;
        }
        this._rect.updateRect();
    }

    get locked() {
        return this._locked;
    }

    set locked(lock: boolean) {
        this._locked = lock;
    }

    get rect() {
        return this._rect;
    }

    get matched_rect() {
        return this._matched_rect;
    }

    set matched_rect(rect: ZRect) {
        if (rect) {
            rect.ex_prop[TFigureElement.IsMatchedRect] = "1";
        }
        this._matched_rect = rect;
    }

    get length() {
        return this._rect.w;
    }

    set length(ll: number) {
        this._rect._w = ll;
        this.params.length = ll;
        // this._rect.updateRect();
    }

    get width() {
        return this._rect.h;
    }

    set width(h: number) {
        this._rect._h = h;
        this.params.depth = h;
        // this._rect.updateRect();
    }

    get depth() {
        return this.width;
    }

    set depth(ww: number) {
        this.width = ww;
    }

    get height() {
        return this.params.height;
    }

    set height(h: number) {
        this.params.height = h;
    }

    /**
     *  体积: 立方米
     */
    get volume() {
        let height = this.height > 1. ? this.height : 1;
        return this.length / 1000. * this.width / 1000. * height / 1000.;
    }
    updateMaterialTargetSizeByMatchedRect() {
        if (this._matched_material) {
            this._matched_material.targetSize.width = this.matched_rect.depth;
            this._matched_material.targetSize.length = this.matched_rect.length;
            this._matched_material.targetPosition = Vec3toMeta(this.matched_rect.rect_center);
        }
    }

    get default_drawing_order() {

        let level_order = 6;
        let sub_level_order = 0;
        let is_found = false;
        for (let li in TGraphBasicConfigs.OnDrawingLevels) {

            for (let id in TGraphBasicConfigs.OnDrawingLevels[li]) {
                let name = TGraphBasicConfigs.OnDrawingLevels[li][id];

                if (compareNames([this.category, this.sub_category, this.public_category], [name], false)) {
                    level_order = ~~li;
                    sub_level_order = ~~id;
                    is_found = true;
                    break;
                }
            }
            if (is_found) break;

        }


        return level_order + sub_level_order / 100;
    }

    get max_zval() {
        return Math.max(this.min_z, this._rect.zval) + this.height;
    }
    set rect(t_rect: ZRect) {
        this._rect = t_rect;
    }

    bindRect(rect: ZRect) {
        this._rect = rect;
        this._rect._attached_elements[TFigureElement.EntityName] = this;
    }

    static copyFigureRectOnly(figure_element: TFigureElement, fig_data: I_FigureElement) {
        if (!fig_data) return;
        if (fig_data.params) {
            figure_element.rect._w = fig_data.params.length;
            figure_element.rect._h = fig_data.params.depth;
        }
        if (fig_data._rect_data) {
            figure_element.rect._w = fig_data._rect_data.w;
            figure_element.rect._h = fig_data._rect_data.h;
        }
        figure_element.rect.updateRect();
    }
    get furnitureEntity(): TFurnitureEntity {
        let entity = this._rect != null && this._rect._attached_elements != null ? this._rect._attached_elements["Entity"] : null;
        return entity;
    }
    set furnitureEntity(entity: TFurnitureEntity) {
        if (this._rect != null) this._rect._attached_elements["Entity"] = entity;
    }

    toString(): string {
        return "FigureElement modelLoc=" + this.modelLoc +
            ((this.modelLoc == this.public_category || !this.public_category) ? "" : ",publicCategory=" + this.public_category) +
            (this.shape ? ",shape=" + this.shape : "") +
            (this.mirror ? ",mirror" : "") +
            (this.params.close_direction ? ",closeDirection=" + this.params.close_direction : "") +
            (this.params.off_land_rule ? ",offLandRule=" + this.params.off_land_rule : "") +
            ",[l=" + Number(this._rect.w.toFixed(2)) + ",w=" + Number(this._rect.h.toFixed(2)) + ",h=" + (this.params.height ? Number(this.params.height.toFixed(2)) : "None") + "]" +
            ",(x=" + Number(this.rect.rect_center.x.toFixed(2)) + ",y=" + Number(this.rect.rect_center.y.toFixed(2)) + (this.rect.rect_center_3d.z !== 0 ? ",z=" + Number(this.rect.rect_center_3d.z.toFixed(2)) : "") + ")" +
            ",(r_z=" + this.rect.rotation_z.toFixed(2) + ")" + (this._default_matched_material == null || this._default_matched_material.modelId == null ? "" : ",default_modelId=" + this._default_matched_material.modelId);
    }

    initSolverParams() {
        this._solver_params = {};
    }


    checkCategoryByRoom(roomname: string = "", room: TRoom = null) {
        let is_board = this.rect.min_hh < 121 || this.height <= 121;
        if (is_board) return;
        if (compareNames([this.category], ["浴室柜"])) {

            if (this.depth < 300) {
                let cate_name = "收纳柜";
                this.category = cate_name;
                this.public_category = cate_name;
                this.sub_category = cate_name;
            }

        }
        if (compareNames([this.category], ["地柜", "高柜"])) {

            if (compareNames([roomname], ["卧室"])) {
                if (this.category === "高柜") {
                    let cate_name = "衣柜";
                    this.category = cate_name;
                    this.public_category = cate_name;
                    this.sub_category = cate_name;
                }
                if (this.category == "地柜" || this.category == "地柜-地柜") {
                    if (this.rect.min_hh > 1199) {
                        let cate_name = "榻榻米";
                        this.category = cate_name;
                        this.public_category = cate_name;
                        this.sub_category = cate_name;
                    }

                }
                // else{
                //     let cate_name = "衣柜";
                //     this.category =cate_name;
                //     this.public_category = cate_name;
                //     this.sub_category = cate_name;
                // }
            }
            else if (compareNames([roomname], ["书房"])) {
                let cate_name = "书柜";
                this.category = cate_name;
                this.public_category = cate_name;
                this.sub_category = cate_name;
            }
            else if (compareNames([roomname], ["卫生间"])) {
                if (this.depth > 400) {
                    let cate_name = "浴室柜";
                    this.category = cate_name;
                    this.public_category = cate_name;
                    this.sub_category = cate_name;
                }
            }
            else {
            }
        }
    }

    static fromSwjCabinet(cabinet: I_SwjCabinetData, roomname: string = "default", parent_matrix: Matrix4 = null) {
        let figure_ele: I_FigureElement = {
            category: ModelLocPublicCategoryMap.getModelLocByPublicCategory(cabinet.public_category || ''),
            public_category: cabinet.public_category || "",
            sub_category: ModelLocPublicCategoryMap.getModelLocByPublicCategory(cabinet.public_category || ''),
            z_level: 1,
            material_name: cabinet.name,
            params: {
                length: cabinet.length,
                depth: cabinet.width,
                height: cabinet.height
            },
            _ex_prop: {
                material_id: cabinet.material_id || "",
                material_name: cabinet.name || ""
            }
        }


        if (roomname.indexOf("厨房") >= 0) {
            figure_ele.model_flag = "11";
        }
        else if (compareNames([roomname], ["卧室", "书房"])) {
            figure_ele.model_flag = "10";
        }
        else {
            figure_ele.model_flag = "12";
        }
        if (cabinet.deep) {
            figure_ele.params.length = cabinet.width;
            figure_ele.params.depth = cabinet.deep;
        }

        let board3d = new TBoard3D(
            {
                W: figure_ele.params.length,
                D: figure_ele.params.depth,
                H: figure_ele.params.height
            }
        );
        let t_params = {
            PX: cabinet.pos_x,
            PY: cabinet.pos_y,
            PZ: cabinet.pos_z,
            RX: cabinet.rotate_x,
            RY: cabinet.rotate_y,
            RZ: cabinet.rotate_z
        };
        if (cabinet.rotate_x === undefined) {
            t_params.RX = 0;
            t_params.RY = 0;
            t_params.RZ = cabinet.rotate_z / Math.PI * 180;
        }
        let matrix = TBoard3D.BasicParamsToMatrix4(t_params);

        if (parent_matrix) {
            matrix = parent_matrix.clone().multiply(matrix);
        }
        // console.log(t_params);
        board3d.setAndApplyModelMatrix(matrix);



        let figure = new TFigureElement(figure_ele);
        figure.rect.zval = cabinet.pos_z || 0;
        figure.min_z = cabinet.pos_z || 0;
        figure.max_z = figure.min_z + cabinet.height;



        if (cabinet.name.indexOf("组合") >= 0 && compareNames([cabinet.name], ["电视柜", "衣柜", "玄关柜", "书柜"]) == 0) {
            figure.category = "定制柜组合";
            figure.sub_category = "定制柜组合";
        }

        if (figure.category == "Default" || figure.public_category.length == 0) {
            figure.sub_category = ModelLocPublicCategoryMap.getMaterialNameToModelLoc(cabinet.name || "") || figure.sub_category;
            figure.public_category = figure.sub_category;
            figure.category = figure.sub_category;
        }


        figure.updateFigure();
        figure._board3d = board3d;

        let z_f = board3d.z_faces[0];

        figure.rect.initByVertices(z_f.positions);
        figure.rect.reParaFromVertices(1);
        figure.params.length = figure.rect._w;
        figure.params.depth = figure.rect._h;
        figure.updateFigure();

        if (figure.category.length == 0 || figure.category == 'Default') {
            let res = ModelLocPublicCategoryMap.getCategoryOfCabinet(cabinet);
            if (res) {
                figure.category = res.category;
                figure.public_category = res.sub_category;
                figure.sub_category = res.sub_category;
            }

        }
        if (cabinet.material_id) {
            figure._default_matched_material = {
                name: cabinet.name,
                modelId: cabinet.material_id,
                modelLoc: figure.modelLoc,
                publicCategoryName: figure.modelLoc,
                length: cabinet.length,
                width: cabinet.width,
                height: cabinet.height,
                imageUrl: "",
                targetPosition: { x: cabinet.pos_x, y: cabinet.pos_y, z: cabinet.pos_z },
                targetSize: { length: cabinet.length, width: cabinet.width, height: cabinet.height },
                targetRotation: { x: 0, y: 0, z: cabinet.rotate_z },
                roomUid: cabinet.room_ind,
                originalLength: cabinet.length,
                originalWidth: cabinet.width,
                originalHeight: cabinet.height
            } as any as I_MaterialMatchingItem;
        }
        TFigureElement.postProcessCategories(figure);

        return figure;
    }
    static fromSwjFurniture(furniture: I_SwjFurnitureData, room_name: string = "default") {

        let figure_ele: I_FigureElement = furniture._figure_element || {
            category: furniture.modelLoc || ModelLocPublicCategoryMap.getModelLocByPublicCategory(furniture.public_category || '', room_name),
            public_category: furniture.public_category || "",
            sub_category: furniture.modelLoc || ModelLocPublicCategoryMap.getModelLocByPublicCategory(furniture.public_category || '', room_name),
            z_level: 1,
            material_name: furniture.name,
            params: {
                length: furniture.length,
                depth: furniture.width,
                height: furniture.height
            },
            _ex_prop: {
                material_name: furniture.name || "",
                material_id: furniture.material_id || "",
                entity_uid: furniture.uid
            }
        }



        let fe = new TFigureElement(figure_ele);
        fe.rect.zval = furniture.pos_z || 0;
        fe.min_z = furniture.pos_z || 0;
        fe.max_z = fe.min_z + furniture.height;


        TFigureElement.postProcessCategories(fe);


        fe.updateFigure();
        // TsAI_app.log(figure.sub_category,figure.min_z);


        let nor = new Vector3(0, -1, 0).applyEuler(new Euler(0, 0, furniture.rotate_z || 0));

        fe.rect._nor.copy(nor);
        fe.rect.rect_center = new Vector3(furniture.pos_x, furniture.pos_y, 0);




        // figure.modelLoc = ModelLocPublicCategoryMap.convertModelLocOfCabinetFigure(figure.modelLoc);
        fe.updateFigure();



        if (compareNames([fe.sub_category], ["筒灯", "射灯"])) {
            // console.log(fe.sub_category,fe.category,fe.material_name);
            fe._is_decoration = true;
            fe._decoration_type = "Lighting";

        }

        if (compareNames([fe.sub_category, fe.material_name || ""], ["开关", "插孔", "插座", "三开单控"])) {
            // console.log(fe.sub_category,fe.category,fe.material_name);
            fe._is_decoration = true;
            fe._decoration_type = "Electricity"

        }

        fe.loadTopViewImage();
        if (figure_ele.decorations && figure_ele.decorations.length > 0) {
            fe.decorationElements = [];
            figure_ele.decorations.forEach((decoration) => {
                let dfe = TFigureElement.fromSwjFigureElement(decoration);
                fe.decorationElements.push(dfe);
            });
        }

        // if(compareNames([fe.sub_category,fe.public_category,fe.category],["摆件","书籍"]))
        // {
        //     // console.log(fe.sub_category,fe.category,fe.material_name);
        //     fe._is_decoration = true;
        //     fe._decoration_type = "Lighting";

        // }
        // figure._rect._attached_elements['furniture'] = furniture;
        return fe;
    }

    public static fromSwjFigureElement(figure_element: I_FigureElement, room_name: string = "default") {
        let fe = new TFigureElement(figure_element);


        // if (fe.modelLoc == "吊顶" ) {
        //     if (figure_element._rect_data && figure_element._rect_data.points) {
        //         fe.rect = ZRect.fromPointsArray(figure_element._rect_data.points);
        //     }
        //     if (figure_element._matched_rect_data && figure_element._matched_rect_data.points) {
        //         fe.matched_rect = ZRect.fromPointsArray(figure_element._matched_rect_data.points);
        //     }
        // }

        if (compareNames([fe.sub_category], ["筒灯", "射灯"])) {
            fe._is_decoration = true;
            fe._decoration_type = "Lighting";

        }

        if (compareNames([fe.sub_category, fe.material_name || ""], ["开关", "插孔", "插座", "三开单控"])) {
            fe._is_decoration = true;
            fe._decoration_type = "Electricity"
        }

        TFigureElement.postProcessCategories(fe);
        fe.updateFigure();
        fe.loadTopViewImage();

        return fe;
    }

    static fromMaterialMatchingItem(furniture: I_MaterialMatchingItem) {
        let figure_ele: I_FigureElement = {
            category: furniture.modelLoc || ModelLocPublicCategoryMap.getModelLocByPublicCategory(furniture.modelLoc || ''),
            public_category: furniture.modelLoc || "",
            sub_category: furniture.modelLoc || ModelLocPublicCategoryMap.getModelLocByPublicCategory(furniture.modelLoc),
            z_level: 1,
            material_name: furniture.name,
            params: {
                length: furniture.length,
                depth: furniture.width,
                height: furniture.height
            },
            _ex_prop: {
                material_name: furniture.name || "",
                material_id: furniture.modelId || ""
            }
        }

        let fe = new TFigureElement(figure_ele);
        fe.rect.zval = furniture.targetPosition.z || 0;
        fe.rect.ex_prop.label = fe.modelLoc;
        fe.min_z = furniture.targetPosition.z || 0;
        fe.max_z = fe.min_z + furniture.height;
        fe._matched_material = furniture;
        furniture.figureElement = fe;
        fe.matched_rect = fe.rect;
        // fe.matched_rect.length = furniture.length;
        // fe.matched_rect._h = furniture.width;

        TFigureElement.postProcessCategories(fe);


        fe.updateFigure();
        // TsAI_app.log(figure.sub_category,figure.min_z);


        let nor = new Vector3(0, -1, 0).applyEuler(new Euler(0, 0, furniture.targetRotation.z || 0));

        fe.rect._nor.copy(nor);
        fe.rect.rect_center = new Vector3(furniture.targetPosition.x, furniture.targetPosition.y, 0);

        // figure.modelLoc = ModelLocPublicCategoryMap.convertModelLocOfCabinetFigure(figure.modelLoc);
        fe.updateFigure();

        fe.bindRect(fe.rect);

        if (compareNames([fe.sub_category], ["筒灯", "射灯"])) {
            // console.log(fe.sub_category,fe.category,fe.material_name);
            fe._is_decoration = true;
            fe._decoration_type = "Lighting";

        }

        if (compareNames([fe.sub_category, fe.material_name || ""], ["开关", "插孔", "插座", "三开单控"])) {
            // console.log(fe.sub_category,fe.category,fe.material_name);
            fe._is_decoration = true;
            fe._decoration_type = "Electricity"

        }
        return fe;
    }

    /**
     * 集中后处理素材 类别
     * @param figure  
     */
    static postProcessCategories(figure: TFigureElement) {
        if (figure.public_category.indexOf("组合") >= 0) {
            figure.sub_category = figure.public_category.replace("组合", "");

            if (figure.category === "其它") {
                figure.category = figure.sub_category;
            }
        }
        if (figure.category == "Default" || figure.public_category.length == 0) {
            figure.sub_category = ModelLocPublicCategoryMap.getMaterialNameToModelLoc(figure.material_name || "") || figure.sub_category;
            figure.category = figure.sub_category;
        }

        if (figure.sub_category === "淋浴房") {
            if (figure.depth < 300) {
                figure.sub_category = "一字形淋浴房";
            }
            else if (Math.abs(figure.length - figure.depth) < 100) {
                figure.sub_category = "钻石形淋浴房";
            }
            else {
                figure.sub_category = "矩形淋浴房";
            }
        }



        if (compareNames([figure.category, figure.sub_category], ["型淋浴房"])) {
            figure.category = "淋浴房";
            figure.sub_category = figure.sub_category.replace("型淋浴房", "形淋浴房");
            figure.public_category = null;
            figure.shape = figure.sub_category.replace("淋浴房", "") as any;
            if (!figure.shape) {
                figure.shape = "矩形";
            }
        }
        // 后处理纠错
        if (figure.public_category && figure.public_category.indexOf("背景墙") >= 0) {
            if (figure.public_category.indexOf("电视柜背景墙") >= 0) {
                figure.category = "电视背景墙";
            }
            figure.sub_category = figure.public_category;
        }

        if (figure.category.indexOf("吸顶灯") >= 0) {
            figure.category = "主灯";
        }

        if (figure.category.endsWith("灯")) {
            if (figure.length < 120 && figure.depth < 120) {
                figure.category = "筒灯";
                figure.sub_category = "筒灯";
            }
        }


        // 处理图灵的板件
        if (figure.category.indexOf("吊柜") >= 0 || figure.category.indexOf("地柜") >= 0) {
            let sub_category = figure.sub_category;

            sub_category = sub_category.replace("靠墙", "");
            sub_category = sub_category.replace("修口板", "收口板");
            sub_category = sub_category.replace("竖封板", "收口板");
            sub_category = sub_category.replace("转角封板", "收口板");
            sub_category = sub_category.replace("见光板", "收口板");
            figure.sub_category = sub_category;
            if (figure.category.indexOf("小板件") >= 0) {
                figure.category = "小板件-" + figure.sub_category;
            }
            if (figure.category.indexOf("吊柜") >= 0 && figure.category.indexOf("板") < 0) {
                figure.category = "吊柜-" + figure.sub_category;

            }
            if (figure.category.indexOf("地柜") >= 0 && figure.category.indexOf("板") < 0) {
                figure.category = "地柜-" + figure.sub_category;

            }

        }
        if (compareNames([figure.category], ["挂饰", "挂画"])) {
            let prefix = "";
            if (compareNames([figure.category], ["沙发", "电视"])) {
                prefix = "客厅"
            }
            if (compareNames([figure.category], ["餐厅"])) {
                prefix = "餐厅";
            }

            figure.category = prefix + "墙饰";
            figure.sub_category = figure.category;
        }
        if (compareNames([figure.category], ["客厅吊灯", "餐厅吊灯"])) {

            figure.category = figure.category.replace("吊灯", "主灯");
            figure.sub_category = figure.category;
        }

        if (compareNames([figure.category], ["窗帘"])) {
            figure.category = "窗帘";
            figure.sub_category = "窗帘";
        }
        if (figure.category.indexOf("地毯") >= 0) {
            figure.category = "地毯";
        }

        if (figure.category.length == 0 || figure.category == 'Default') {
            figure.category = figure.sub_category || figure.material_name || "Default";
        }

        if (figure.sub_category === "沙发") {
            figure.sub_category = "直排沙发";
        }

        if (figure.sub_category === "餐桌") {
            if (Math.abs(figure.length - figure.width) < 10 && figure.width > 1400) {
                figure.sub_category = "圆形餐桌";
            }
        }

        figure.modelLoc = figure.convertSubCategoryToModelLoc(figure.sub_category) || figure.modelLoc || figure.sub_category;


    }
    static fromCadFurnitureRect(fRect: ZRect) {
        // let categorys = ModelLocPublicCategoryMap.getModelLocByEnglishLabel(fRect.ex_prop.label);
        let FurnitureObj = g_FigureImagePaths[fRect.ex_prop.label];

        let mdoel_loc = ModelLocPublicCategoryMap.getModelLocByPublicCategory(FurnitureObj?.public_category || "");
        let i_figure_element: I_FigureElement = {
            category: mdoel_loc,
            modelLoc: mdoel_loc,
            public_category: FurnitureObj?.public_category,
            sub_category: FurnitureObj?.subCategory,
            z_level: 1,
            params: {
                length: fRect.w,
                depth: fRect.h,
                height: 2600
            },
            _ex_prop: {
                material_name: FurnitureObj?.subCategory,
                ...fRect.ex_prop
            },
            _rect_data: {
                w: fRect.w,
                h: fRect.h,
                back_center: fRect.back_center as Vector3Like,
                nor: fRect.nor,
                u_dv_flag: fRect.u_dv_flag,
            }
        }
        let t_figure_element = new TFigureElement(i_figure_element);
        return t_figure_element;
    }

    static fromCustomCabinetModel(cabinet: SunvegaAPI.BasicBiz.CustomCabinetModel) {
        let cabinetName = (cabinet as any).name;
        let underLineIndex = cabinetName.indexOf("_");
        if (underLineIndex > 0) {
            cabinetName = cabinetName.substring(0, underLineIndex);
        }
        let i_figure_element: I_FigureElement = {
            category: cabinetName,
            public_category: cabinetName,
            sub_category: cabinetName,
            z_level: 1,
            params: {
                length: cabinet.size.length,
                depth: cabinet.size.width,
                height: cabinet.size.height
            },
            _ex_prop: {
                material_name: cabinet.type
            },
            _rect_data: {
                w: cabinet.size.length,
                h: cabinet.size.width,
                rect_center: cabinet.position as Vector3Like,
                nor: { x: Math.sin(cabinet.rotation.z), y: -Math.cos(cabinet.rotation.z), z: 0 },
                u_dv_flag: -1
            }
        }
        let newFigureElement = new TFigureElement(i_figure_element);
        newFigureElement.rect.zval = cabinet.position.z || 0;
        newFigureElement._default_matched_material = {
            modelId: cabinet.materialId,
            modelLoc: cabinetName,
            publicCategoryName: cabinetName,
            length: cabinet.size.length,
            width: cabinet.size.width,
            height: cabinet.size.height,
            imageUrl: "",
            targetPosition: { x: cabinet.position.x, y: cabinet.position.y, z: cabinet.position.z },
            targetSize: { length: cabinet.size.length, width: cabinet.size.width, height: cabinet.size.height },
            targetRotation: { x: cabinet.rotation.x, y: cabinet.rotation.y, z: cabinet.rotation.z },
            roomUid: (cabinet as any).roomId
        } as I_MaterialMatchingItem;
        return newFigureElement;
    }
    static fromSwjFurnitureGroup(furniture: I_SwjFurnitureGroup, roomname: string = null) {
        let figure = TFigureElement.fromSwjFurniture(furniture, roomname);

        return figure;
    }
    setFigureProp(data?: I_FigureElement) {
        if (data.params) {
            this.params = {};
            for (let key in data.params) {
                this.params[key] = data.params[key];
            }
        }
        this.image_path = data.image_path || this.image_path;
        this.priority = data.priority || this.priority;
        this.fill_color = data.fill_color || this.fill_color;
        this.category = data.category || this.category;
        this.deform_method = data.deform_method || this.deform_method;
        this.z_level = data.z_level || this.z_level;
        this.shape = data.shape as ZRectShapeType || this.shape;

    }


    updateDefaultMaterialMatchingItemByDefaultModelId() {
        let size = new TSize(this.rect.w - 0.1, this.rect.h - 0.1, 0);
        let modelFlag = compareNames([this.sub_category], ["地柜", "吊柜", "高柜", "收口板", "见光板"]) ? "11" : "0";
        let res = TSerialSizeRangeDB.QueryDefaultModelIds(this.sub_category, size);
        if (!res?.length) {
            // console.error("default model id is null", this.modelLoc, this.sub_category, size);
            return;
        }

        let modelId = res[0];
        let rotation_z = this.rect.rotation_z;
        this._default_matched_material = {
            modelId: modelId,
            modelLoc: this.category,
            publicCategoryName: this.modelLoc,
            length: this.rect.length,
            width: this.rect.depth,
            height: this.height,
            imageUrl: "",
            targetPosition: Vec3toMeta(this.rect.rect_center_3d),
            targetSize: { length: this.length, width: this.depth, height: this.height },
            targetRotation: { x: 0, y: 0, z: rotation_z },
            roomUid: "",
            originalLength: this.length,
            originalWidth: this.depth,
            originalHeight: this.height,
            modelFlag: modelFlag
        } as any as I_MaterialMatchingItem;
    }

    public getAlternativeFigureElements(): TFigureElement[] {
        if (this.furnitureEntity instanceof TBaseGroupEntity) {
            return (this.furnitureEntity as TBaseGroupEntity).disassembled_figure_elements;
        }
        return [];
    }

    public getMembersFigureElements(): TFigureElement[] {
        if (this.furnitureEntity instanceof TBaseGroupEntity) {
            if (this.furnitureEntity._matched_combination_entitys && this.furnitureEntity._matched_combination_entitys.length > 0) {
                return (this.furnitureEntity as TBaseGroupEntity)._matched_combination_entitys.map((entity) => entity.figure_element);
            }
        }
        return [];
    }

    public haveDefaultMaterial(): boolean {
        if (this._default_matched_material != null && ((this._default_matched_material.modelId && this._default_matched_material.modelId.length > 0))) {
            return true;
        } else {
            return false;
        }
    }

    public haveMatchedMaterial(): boolean {
        if (this._matched_material != null && ((this._matched_material.modelId && this._matched_material.modelId.length > 0)
            || (this._matched_material.deleted_model_id && this._matched_material.deleted_model_id.length > 0))) {
            return true;
        } else {
            return false;
        }
    }

    public haveMatchedMaterial2(): boolean {
        if (this._matched_material != null && ((this._matched_material.modelId && this._matched_material.modelId.length > 0)
            || (this._matched_material.deleted_model_id && this._matched_material.deleted_model_id.length > 0))) {
            return true;
        } else {
            if (this.furnitureEntity instanceof TBaseGroupEntity) {
                let members: TFigureElement[] = (this.furnitureEntity as TBaseGroupEntity).disassembled_figure_elements;
                for (let member of members) {
                    if (member.haveMatchedMaterial()) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public getAttatchedTargetElement(): TFigureElement {
        let targetElement: TFigureElement = null;
        if (this._rect._attached_elements != null) {
            targetElement = this._rect._attached_elements["TargetElement"];
        }
        return targetElement;
    }

    public haveMatchedGroupMaterial(): boolean {
        if (this._matched_material != null && this._matched_material.modelId && this._matched_material.modelId.length > 0) {
            if (this._matched_material.name && this._matched_material.name.indexOf("组合") > -1) {
                return true;
            }
            if (this._matched_material.publicCategoryName && this._matched_material.publicCategoryName.indexOf("组合") > -1) {
                return true;
            }
            if (isGroupMaterialMatchingItem(this._matched_material)) {
                return true;
            }
        }
        return false;
    }

    public haveDeletedMaterial(): boolean {
        if (this._matched_material != null && this._matched_material.deleted_model_id && this._matched_material.deleted_model_id.length > 0) {
            return true;
        } else {
            return false;
        }
    }

    public replaceMatchedMaterial(material: I_MaterialMatchingItem): void {
        let oldMatchedMaterial: I_MaterialMatchingItem = this._matched_material;
        this._matched_material = material;
        this._matched_material.targetPosition = { ...oldMatchedMaterial.targetPosition };
        this._matched_material.targetRotation = { ...oldMatchedMaterial.targetRotation };
        if (["地毯", "窗帘", "背景墙", "沙发背景墙", " 电视背景墙"].indexOf(this.modelLoc) > -1) {
            this._matched_material.targetSize = { ...oldMatchedMaterial.targetSize };
        } else {
            this._matched_material.targetSize = { length: material.length, width: material.width, height: material.height };
        }
        if (oldMatchedMaterial.targetPosition.z + oldMatchedMaterial.targetSize.height > 2400) {
            this._matched_material.targetPosition.z = oldMatchedMaterial.targetPosition.z + oldMatchedMaterial.targetSize.height - this._matched_material.targetSize.height;
        }
        if (material.topViewImage != null) {
            let img = new Image();
            img.src = material.topViewImage;
            img.crossOrigin = "Anonymous";
            img.onload = () => {
                this.pictureViewImg = img;
            };
        }
    }

    public checkIsMatchedSizeSuitable(): boolean {
        return TFigureElement.checkIsMatchedSizeSuitable(this, this._matched_material);
    }

    public isMarkedAsDeleted(): boolean {
        return this._ex_prop['is_deleted'] == "1";
    }

    public isMaterialMarkAsInvisible(): boolean {
        return this._ex_prop['visible'] === "false";
    }

    public markMaterialAsInvisible(): void {
        this._ex_prop['visible'] = "false";
    }

    public markMaterialAsVisible(): void {
        this._ex_prop['visible'] = "true";
    }

    public haveMatchedCustomCabinet(): boolean {
        if (this._matched_material != null && this._matched_material.modelId && this._matched_material.modelId.length > 0) {
            if (isCustomCabinet(this._matched_material)) {
                return true;
            }
        }
        return false;
    }

    public haveMatchedProductFurniture(): boolean {
        if (this._matched_material != null && this._matched_material.modelId && this._matched_material.modelId.length > 0) {
            if (isProductFurniture(this._matched_material)) {
                return true;
            }
        }
        return false;
    }

    public get matchedMaterialId(): string {
        if (this._matched_material != null && this._matched_material.modelId && this._matched_material.modelId.length > 0) {
            return this._matched_material.modelId;
        } else {
            return null;
        }
    }



    // 获取材质，根据优先级，风格套系材质、白膜材质、默认材质
    public getMaterialItem(): I_MaterialMatchingItem | null {

        // 优先使用匹配的材质
        if (this._matched_material?.modelId) {
            return this._matched_material;
        }

        // 其次使用3d预览的材质
        if (this._3dpreview_matched_material?.modelId) {
            return this._3dpreview_matched_material;
        }

        // 最后使用默认的材质
        this.updateDefaultMaterialMatchingItemByDefaultModelId();
        if (this._default_matched_material?.modelId) {
            return this._default_matched_material;
        }

        return null;
    }

    // 获取材质ID，根据优先级，风格套系材质、白膜材质、默认材质
    public getMaterialID(): string {
        let info = this.getMaterialItem();
        if (info) {
            return info.modelId;
        }
        return "";
    }

    public get originalEntityUid(): number {
        if (typeof this._ex_prop["entity_uid"] == "string") {
            return parseInt(this._ex_prop["entity_uid"]);
        } else {
            return this._ex_prop["entity_uid"];
        }
    }

    updateZVal() {
        this.rect.zval = this.min_z || TGraphBasicConfigs.FigureMinZDict[this.sub_category] || TGraphBasicConfigs.FigureMinZDict[this.category] || 0;
    }

    clone() {
        let fig = new TFigureElement(this.exportJson());
        fig.rect.copy(this.rect);
        fig.isAbnormalFigure = this._isAbnormalFigure;
        return fig;
    }
    exportJson() {
        let res: I_FigureElement = {};

        res.category = this.category;
        res.public_category = this.category;
        res.sub_category = this.sub_category;
        res.priority = this.priority;
        res.deform_method = this.deform_method;
        res.z_level = this.z_level;
        res.shape = this.shape;
        res.min_z = this.min_z;
        res.max_z = this.max_z;
        res.model_flag = this.modelFlag;
        res._group_cate = this._group_cate;
        res._group_main_figure = this._group_main_figure;
        res._group_uuid = this._group_uuid;
        // res.wireFrameImageUrl = this._wireFrameImage ? this._wireFrameImage.src : null;
        if (this._is_decoration) {
            res._is_decoration = this._is_decoration;
        }
        res._decoration_type = this._decoration_type || "";
        if (this._is_sub_board) {
            res._is_sub_board = this._is_sub_board;
        }

        if (this.tags && this.tags.length > 0) {
            res.tags = [...this.tags];
        }
        this.params.length = this.rect._w;
        this.params.depth = this.rect._h;

        res._rect_data = this.rect.exportRectData();
        if (this._matched_rect) {
            res._matched_rect_data = this._matched_rect.exportRectData();
        }
        if (this._matched_material) {
            let data: I_MaterialMatchingItem = {} as any;
            for (let key in this._matched_material) {
                if (key === "figureElement" || key === "candidate") {
                    continue;
                }
                let val = (this._matched_material as any)[key];
                (data as any)[key] = val;
            }
            res._matched_material = data;
        }
        res.params = {};
        for (let key in this.params) {
            res.params[key] = this.params[key];
        }
        res._ex_prop = this._ex_prop;
        res.mirror = this.mirror;

        if (this.decorationElements && this.decorationElements.length > 0) {
            res.decorations = [];
            this.decorationElements?.forEach((df) => {
                res.decorations.push(df.exportJson());
            });
        }

        return res;
    }

    async exportGlb() {
        if (this._solid_mesh3D) {
            let parent = this._solid_mesh3D.parent;

            let scene = new Scene();
            scene.add(this._solid_mesh3D);

            let exporter = new GLTFExporter();

            await new Promise((resolve, reject) => {
                exporter.parse(scene, (gltf_blob) => {
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(gltf_blob);
                    link.download = (this._matched_material?.name || this.category) + '.glb';
                    link.click();
                    resolve(true);
                }, {
                    binary: true,
                    trs: false,
                    blobDirectly: true
                })
            }).catch(e => null);

            parent.add(this._solid_mesh3D);
        }
    }
    get shape() {
        return this._shape;
    }

    set shape(s: FigureShapeType) {
        this._shape = s;
    }

    get figure_shape() {
        if (this._shape == "L形") {
            if (this._rect.u_dv_flag > 0) {
                return "左" + this._shape;
            }
            else {
                return "右" + this._shape;
            }
        }
        return this._shape;
    }


    get nor() {
        return this.rect._nor.clone();
    }
    set nor(t: Vector3Like) {
        this.rect._nor.copy(t);
    }

    get dv() {
        return this.rect.dv.clone();
    }

    set dv(tv: Vector3Like) {
        this.rect.u_dv = new Vector3().copy(tv);
    }

    // get pictureViewImg() {
    //     return this.rect._attached_elements['pictureView'];
    // }

    // set pictureViewImg(img: HTMLImageElement) {
    //     if (img) {
    //         this.rect._attached_elements['pictureView'] = img;
    //         this.matched_rect._attached_elements['pictureView'] = img;
    //     }
    //     else {
    //         if (this.rect._attached_elements['pictureView']) {
    //             delete this.rect._attached_elements['pictureView'];
    //         }
    //     }
    // }

    get pictureViewImgUrl() {
        return this.rect._attached_elements['pictureViewUrl'];

    }

    set pictureViewImgUrl(url: string) {
        this.rect._attached_elements['pictureViewUrl'] = url;
    }

    /**
     *  本地渲染的顶视图
     */
    get renderedTopViewImg() {
        return this.rect._attached_elements['renderedTopViewImg'];
    }

    set renderedTopViewImg(img: HTMLImageElement) {
        this.rect._attached_elements["renderedTopViewImg"] = img;
    }

    shouldUseRenderedTopViewImg(): boolean {
        if (this.haveMatchedMaterial() && (this._matched_material.topViewImage == null || this._matched_material.topViewImage == "")) {
            return true;
        } else if (this.modelLoc && this.modelLoc.indexOf("墙面") > -1) {
            return false;
        }
        return false;
    }

    updateFigureImg() {

    }
    updateFigure() {
        this.rect.updateRect();
        this.updateZVal();
    }

    async updateDesignMaterialInfo(force: boolean = false) {
        if (!force) {
            if (this._design_material_info && this._design_material_info?.MaterialId === this.matchedMaterialId) {
                return this._design_material_info;
            }
        }

        let infos = await MaterialService.getDesignMaterialInfoByIds([this.matchedMaterialId]);
        if (infos.length > 0) {
            this._design_material_info = infos[0];
        }
        else {
            this._design_material_info = null;
            console.error("material info is null", this.modelLoc, this.matchedMaterialId);
        }
    }
    public setDesignMaterialInfo(design_material_info: I_DesignMaterialInfo) {
        this._design_material_info = design_material_info;
    }

    private isWhiteModelMaterialItem(materialItem: I_MaterialMatchingItem): boolean {
        return materialItem == this._3dpreview_matched_material;
    }

    async updateMesh3DWithDesignMaterialInfo(mode: SceneMaterialMode = "SolidMaterial") {

        let materialItem: I_MaterialMatchingItem = this.getMaterialItem();

        if (!materialItem) {
            setTimeout(() => {
                LayoutAI_App.instance.scene3D.setLoadingFigureModelUUid(this.uuid, null);

            }, 2000);
            return;
        }
        let design_material_info = this._design_material_info;
        if (!design_material_info) {
            setTimeout(() => {
                LayoutAI_App.instance.scene3D.setLoadingFigureModelUUid(this.uuid, null);

            }, 2000);
            return;
        }
        if (LayoutAI_App.instance.scene3D) {
            let material_id = LayoutAI_App.instance.scene3D.getLoadingFigureModelUUid(this.uuid);
            if (design_material_info.MaterialId === material_id) {
                return;
            }
            // console.log(this.category, this.uuid, LayoutAI_App.instance.scene3D.getLoadingFigureModelUUid(this.uuid));
            LayoutAI_App.instance.scene3D.setLoadingFigureModelUUid(this.uuid, design_material_info.MaterialId);

        }
        let parent_node: Object3D = null;
        if (this._simple_mesh3D && this._simple_mesh3D.parent) {
            parent_node = this._simple_mesh3D.parent;
        }

        if (this._solid_mesh3D && this._solid_mesh3D.parent) {
            parent_node = this._solid_mesh3D.parent;
        }
        if (this._solid_mesh3D) {
            Utils3D.disposeObject(this._solid_mesh3D);
            if (this._solid_mesh3D.parent) {

                this._solid_mesh3D.parent.remove(this._solid_mesh3D);
            }
            this._solid_mesh3D.parent = null;
            this._solid_mesh3D = null;
        }
        let isWhite = this.isWhiteModelMaterialItem(materialItem);
        let param = TFigureElement.makeMesh3DMaterialInfoOptions(this);
        let group_node: Group = null;
        
        if (isGroupDesignMaterialInfo(design_material_info)) {
            // 组合素材
            group_node = await Model3dApi.MakeMesh3DWithGroupDesignMaterialInfo(design_material_info, param);
        }
        else {
            // 单体素材
            group_node = await Model3dApi.MakeMesh3DWithDesignMaterialInfo(design_material_info, param);
        }
        if (group_node && this.cabinetStyleId) {
            await GltfManager.updateCabinet3DModelWithStyleBrush(group_node, this.cabinetStyleId, { category: this.category });
        }

        if (group_node) {
            group_node.userData[UserDataKey.MaterialId] = design_material_info.MaterialId;
            group_node.userData[UserDataKey.MaterialInfo] = design_material_info;
        }

        // 材质替换后的更新函数
        let scope = this;
        this._solid_mesh3D = group_node;
        // 检查灯光，使用 this._solid_mesh3D ，在赋值后检测
        if (SwitchConfig.furnitureLightSwitch) {
            this._checkLight();
        }
        if (parent_node && this._solid_mesh3D) {
            parent_node.add(this._solid_mesh3D);
        } else {
            if (!this._solid_mesh3D) {
                // console.warn("_solid_mesh3D is null", this.uuid, this.category);

            }
            else {
                // console.warn("parent_node is null", this.uuid, this.category);
            }
        }

        if (LayoutAI_App.instance.scene3DManager) {
            LayoutAI_App.instance.scene3DManager.onElementUpdate(this, { isWhite: isWhite });
        }

        if (this._solid_mesh3D) {
            this._solid_mesh3D.visible = mode === "SolidMaterial";
        }
        if (this._simple_mesh3D) {
            this._simple_mesh3D.visible = mode === "WhiteModel" && (TFigureElement.SimpleMesh3DVisible || this.haveMatchedMaterial());
        }
        this.resetMesh3DParent();

        if (LayoutAI_App.instance.scene3D) {
            LayoutAI_App.instance.scene3D.setLoadingFigureModelUUid(this.uuid, null);
        }
    }

    public static makeMesh3DMaterialInfoOptions(ele: TFigureElement) {
        let materialItem: I_MaterialMatchingItem = ele.getMaterialItem();
        let isWhite = ele.isWhiteModelMaterialItem(materialItem);

        return {
            target_rect: ele.getTargetRect(),
            target_size: ele.getTargetSize(materialItem),
            category: ele.category,
            cabinet_board_texture_img: ele.pictureViewImg,
            isWhite: isWhite,
            accurateGlbUrl: materialItem.accurateGlbUrl,
            similarGlbUrl: materialItem.similarGlbUrl,
            alignTopZval: ele.params.alignTopZval,
            uidN: ele.furnitureEntity?.uidN,
        };
    }

    resetMesh3DParent() {
        if (this._solid_mesh3D) {
            if (this.furnitureEntity) {
                this.furnitureEntity.updateMesh3D();
            }
            else {
                if (this.attchedToTargetElement && this.attchedToTargetElement?.furnitureEntity) // 如果绑定了父亲
                {
                    if (this.isMaterialMarkAsInvisible()) // 如果被标记了不可见
                    {
                        if (this._solid_mesh3D) {
                            this._solid_mesh3D.removeFromParent();
                        }
                    }
                    else {
                        if (this.attchedToTargetElement.furnitureEntity._mesh3d) {
                            this.attchedToTargetElement.furnitureEntity._mesh3d.add(this._solid_mesh3D);
                        }
                    }

                }
                else if (!this._solid_mesh3D.parent) {

                    if (this._room && this._room._room_entity) {
                        if (compareNames([this.category], ["墙饰"])) {
                            this._room._room_entity._updateDecoration();
                        }
                    } else {
                        if (this._simple_mesh3D?.parent) {
                            this._simple_mesh3D.parent.add(this._solid_mesh3D);
                        }
                    }

                    // if(!this._solid_mesh3D.parent)
                    // {
                    //     // this.dispose3d();
                    // }

                }
            }
        }
    }

    private _checkLight() {
        if (!this._solid_mesh3D) {
            return;
        }
        let isLight = false;
        let attr = (this._design_material_info as any).MaterialAttribute as string;
        if (attr && attr.indexOf("_Light_") > -1) {
            isLight = true;
        }
        if (!isLight) {
            return;
        }
        let light = this._solid_mesh3D.userData[UserDataKey.Light];
        if (!light) {
            light = new PointLight(0xffffff, 1, 10000, 0.1);
            light.castShadow = true;
            light.position.set(0, 0, 0);
            this._solid_mesh3D.add(light);
            this._solid_mesh3D.userData[UserDataKey.Light] = light;
        }
    }

    updateRenderedTopViewImg() {


        let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
        if (scene3d) {
            let imgTopUrl = FigureTopViewer.instance.renderFigureTopView(this);
            if (imgTopUrl) {
                if (!this.renderedTopViewImg) {
                    this.renderedTopViewImg = new Image();
                }
                this.renderedTopViewImg.src = imgTopUrl;
                this.renderedTopViewImg.onload = () => {
                    LayoutAI_App.instance.update();
                }
            }
            else {
                this.renderedTopViewImg = null;
            }
            // MaterialService.cacheGroupMaterialTopViewImage(this.matchedMaterialId, this.renderedTopViewImg.src);

        }
    }


    /**
     *  后处理离地高和高度
     */
    postProcessStrangeZValAndHeight() {

        if (compareNames([this.category, this.sub_category], TGraphBasicConfigs.OnFloorCategories, false)) {
            if (this.params.height && this.params.height > 1200) {
                this.params.height = TGraphBasicConfigs.FigureDefaultHeightDict[this.sub_category] || 1200; // 
            }
            if (this.rect.zval && this.rect.zval > 100) {
                this.rect.zval = TGraphBasicConfigs.FigureMinZDict[this.sub_category] || 0;
            }
        }

    }
    updateMesh3D() {
        let figure_ele = this;
        if (!this._simple_mesh3D && (TFigureElement.SimpleMesh3DVisible || this.haveMatchedMaterial())) {
            this._simple_mesh3D = new Mesh();
            this._simple_mesh3D.name = MeshName.FigureSample;
            this._simple_mesh3D.userData[UserDataKey.FigureMeshTypeName] = MeshName.SimpleMesh;

            let boxMesh = MeshBuilder.makeBoxMesh("Furniture");

            for (let child of boxMesh.children) {
                child.userData[UserDataKey.FigureMeshTypeName] = MeshName.BoxBoundary;
            }


            boxMesh.userData[UserDataKey.FigureMeshTypeName] = MeshName.WhiteBox;
            this._simple_mesh3D.add(...boxMesh.children);
            this._simple_mesh3D.add(boxMesh);
            this._simple_mesh3D.visible = TFigureElement.SimpleMesh3DVisible || this.haveMatchedMaterial();
        } 
        if (!this.haveMatchedMaterial() && !TFigureElement.SimpleMesh3DVisible) {
            if (this._simple_mesh3D) {
                this._simple_mesh3D.removeFromParent();
                this._simple_mesh3D = null;
            }
            return this._simple_mesh3D ;
        }
        let mesh = this._simple_mesh3D;
        if (mesh) {
            mesh.userData['figure_element'] = figure_ele;
            mesh.userData['category'] = figure_ele.category;
            let pos = figure_ele.rect.rect_center_3d.clone();
            pos.z += figure_ele.height / 2;

            mesh.position.copy(pos as any);
            mesh.rotation.set(0, 0, figure_ele.matched_rect?.rotation_z || figure_ele.rect.rotation_z);
            mesh.scale.set(figure_ele.length, figure_ele.depth, figure_ele.height);
            for (let child of mesh.children) {
                child.userData['figure_element'] = figure_ele;
                child.userData['category'] = figure_ele.category;
            }
        }
        return this._simple_mesh3D;
    }

    updateSimpleMeshPose()
    {
        let figure_ele = this;
        let mesh = this._simple_mesh3D;
        if (mesh) {
            mesh.userData['figure_element'] = figure_ele;
            mesh.userData['category'] = figure_ele.category;
            let pos = figure_ele.rect.rect_center_3d.clone();
            pos.z += figure_ele.height / 2;

            mesh.position.copy(pos as any);
            mesh.rotation.set(0, 0, figure_ele.matched_rect?.rotation_z || figure_ele.rect.rotation_z);
            mesh.scale.set(figure_ele.length, figure_ele.depth, figure_ele.height);
            for (let child of mesh.children) {
                child.userData['figure_element'] = figure_ele;
                child.userData['category'] = figure_ele.category;
            }
        }
    }
    checkIsMatchedMaterials() {
        if (compareNames([this.sub_category], TMaterialMatchingConfigs._ignoreCategories)) return true;
        return !(!this._matched_material?.modelId && (!this._candidate_materials || this._candidate_materials.length == 0));
    }

    /**
     *  绘制预览图
     * @param painter 
     * @param show_text 
     * @param custom_fill_color 
     * @param font_size 
     */
    drawPreviewFigure(painter: TPainter, show_text: boolean = false, custom_fill_color: string = null, font_size: number = 12) {
        let rect = this.matched_rect || this.rect;
        painter._context.lineWidth = 1;
        if (rect) {
            let pictureView = this.pictureViewImg;
            let has_rendered_top_view_img: boolean = this.renderedTopViewImg != null && this.renderedTopViewImg.src != null;
            if (pictureView == null && has_rendered_top_view_img) {
                pictureView = this.renderedTopViewImg;
            }
            if (pictureView) {
                // console.info("    TFigureElement.drawPreviewFigure() " + this.modelLoc + " w=" + rect.w + " h=" + rect.h);
                painter.strokeStyle = "#000";
                painter.fillStyle = this.fill_color;
                painter.fillStyle = custom_fill_color ? custom_fill_color : "#fff";
                painter._context.lineWidth = 1;
                let model_flag = this?._matched_material?.modelFlag || "1";
                let is_cabinet = (model_flag != '1' && model_flag != '15') && ((compareNames([this.category, this.sub_category], TGraphBasicConfigs.MainCabinetsCategories) == 1) && this.sub_category.endsWith("柜"));

                let is_floor_carpet = this.category.includes("地毯");
                let is_background_wall = this.category.includes("背景墙");
                let is_filling = is_cabinet || is_floor_carpet || is_background_wall;

                let is_use_u_dv_flag = true;
                if (has_rendered_top_view_img) {
                    is_filling = true;
                    is_cabinet = false;
                    is_use_u_dv_flag = false;
                }
                is_filling = true;   //因为预览图现在需要拉伸了，不在需要根据图元大小来判断是否填充
                painter.drawPreviewFigureRect(rect, pictureView, is_filling, is_use_u_dv_flag);

                if (is_cabinet) {
                    painter._style_mapping["white"] = "#ffffff33";
                    painter.drawFigureRect(rect, this.sub_category || this.category,
                        [g_figure_alias_dict[this.category], this.sub_category, this.category]);
                    painter._context.globalAlpha = 1.;
                }
                if (is_cabinet) {
                    painter._context.globalAlpha = 1;
                }
                painter.fillStyle = "#000";
            }
            else {
                painter.strokeStyle = "#000";
                if (!this._is_decoration) {
                    painter.drawFigureRect(rect, this.sub_category, [this.category, this.public_category]);
                    // this.drawFigure(painter,false);
                }
                else {
                    painter.strokePolygons([rect]);
                }
            }


            // 如果没有匹配到素材, 就要高亮显示
            if (!this.checkIsMatchedMaterials() &&
                (this._room && (this._room.isFigureElementInCurrentScope(this) || (this._room.waitingForFurshiRemaining && !this._room.isFigureElementInFurnishedScope(this))))) {
                painter.strokeStyle = "#f00";
                painter.strokePolygons([rect]);
            }
        }
    }

    /**
     *  绘制图元
     * @param painter 
     * @param show_text 
     * @param custom_fill_color 
     * @param font_size 
     */
    drawFigure(painter: TPainter, show_text: boolean = false, custom_fill_color: string = null, font_size: number = 12, line_color: string = "#777777") {
        let rect = this.rect;
        if (rect) {
            painter.strokeStyle = "#000";

            painter.fillStyle = this.fill_color;


            painter.fillStyle = custom_fill_color ? custom_fill_color : "#fff";

            painter._context.lineWidth = 1;

            painter._style_mapping["black"] = line_color;

            painter.drawFigureRect(rect, this.sub_category || this.category,
                [g_figure_alias_dict[this.category], this.sub_category, this.category]);
            painter.fillStyle = "#000";


            if (show_text) {
                painter.fillStyle = custom_fill_color ? custom_fill_color : "#000";
                let sub_category = (this.sub_category !== "Default") ? this.sub_category : this.material_name;
                let text = sub_category;
                if (text.length > 5) {
                    let ll = text.length;
                    text = text.substring(0, ll - 3) + "\n" + text.substring(ll - 3);
                }

                let angle = 0;

                if (rect._w < 200 && rect.w < rect.h) {
                    angle = rect.rotation_z + Math.PI / 2;
                    font_size /= 2;

                }

                painter.drawText(text, rect.rect_center, angle, font_size);
            }
            if (this.spotlight) {
                painter.drawEdges(rect.edges, 0, "#f00");
            }
        }
    }

    /**
     *  绘制轮廓
     * @param painter 
     * @param show_text 
     * @param custom_fill_color 
     * @param font_size 
     */
    drawOutline(painter: TPainter, show_text: boolean = false, custom_fill_color: string = null, font_size: number = 12) {
        let rect = this.matched_rect || this.rect;
        painter._context.lineWidth = 1;
        if (rect) {
            let pictureView = this._wireFrameImage;
            if (pictureView) {
                // console.info("    TFigureElement.drawPreviewFigure() " + this.modelLoc + " w=" + rect.w + " h=" + rect.h);
                painter.strokeStyle = "#000";
                painter.fillStyle = this.fill_color;
                painter.fillStyle = custom_fill_color ? custom_fill_color : "#fff";
                painter._context.lineWidth = 2;
                let is_use_u_dv_flag = true;
                painter.drawPreviewFigureRect(rect, pictureView, true, is_use_u_dv_flag);
                painter.fillStyle = "#000";
            }
            else {
                painter.strokeStyle = "#000";
                if (!this._is_decoration) {
                    painter.drawFigureRect(rect, this.sub_category, [this.category, this.public_category]);
                    // this.drawFigure(painter,false);
                }
                else {
                    painter.strokePolygons([rect]);
                }
            }
        }
        // 如果没有匹配到素材, 就要高亮显示
        if (!this.checkIsMatchedMaterials() && this._room && this._room._series_sample_info) {
            painter.strokeStyle = "#f00";
            painter.strokePolygons([rect]);
        }
    }

    public autoResizeMatchedMaterialToModelloc(): void {
        if (!this.haveMatchedProductFurniture() && !isGroupMaterial(this._matched_material)) {
            // console.log("autoResizeMatchedMaterialToModelloc()  " + this.modelLoc + ":  skip");
            return;
        }
        if (this.modelLoc.indexOf("沙发") >= 0) {
            this._matched_material.length = this.length;
            if (this._matched_material.targetSize != null) {
                // const old_length = this._matched_material.targetSize.length
                // const old_width = this._matched_material.targetSize.width
                this._matched_material.targetSize.length = this.length;
                this._matched_material.targetSize.width = this._matched_material.width;
                // console.log("autoResizeMatchedMaterialToModelloc()  " + this.modelLoc + " length: " + old_length + " => " + this._matched_material.targetSize.length );
                // console.log("autoResizeMatchedMaterialToModelloc()  " + this.modelLoc + "  width: " + old_width + " => " + this._matched_material.targetSize.width );
            }
        } else {
            // const oldLength = this._matched_material.length;
            // const oldWidth = this._matched_material.width;
            this._matched_material.length = this.length;
            this._matched_material.width = this.width;
            // console.log("autoResizeMatchedMaterialToModelloc()  " + this.modelLoc + " length: " + oldLength + " => " + this._matched_material.length );
            // console.log("autoResizeMatchedMaterialToModelloc()  " + this.modelLoc + "  width: " +  oldWidth + " => " + this._matched_material.width );

            if (this._matched_material.targetSize != null) {
                // const oldTargetLength = this._matched_material.targetSize.length
                // const oldTargetWidth = this._matched_material.targetSize.width
                this._matched_material.targetSize.length = this.length;
                this._matched_material.targetSize.width = this.width;
                // console.log("autoResizeMatchedMaterialToModelloc()  " + this.modelLoc + " length: " + oldTargetLength + " => " + this._matched_material.targetSize.length );
                // console.log("autoResizeMatchedMaterialToModelloc()  " + this.modelLoc + "  width: " +  oldTargetWidth + " => " + this._matched_material.targetSize.width );
            }
        }
    }

    private static getWardrobeMinMaxLength(base: number): { min: number, max: number } {
        let minMaxLengths = { min: base, max: base };

        let column = Math.round(base / 450);
        if (column < 1) {
            column = 1;
        };
        if (column <= 2) {
            minMaxLengths.min = base - 150 * column;
            if (minMaxLengths.min <= 0) {
                minMaxLengths.min = base;
            }
            minMaxLengths.max = base + 150 * column;
        } else if (column <= 5) {
            minMaxLengths.min = base - 100 * column;
            minMaxLengths.max = base + 100 * column;
        } else {
            minMaxLengths.min = base - 50 * column;
            minMaxLengths.max = base + 100 * column;
        }
        return minMaxLengths;
    }

    public static checkIsMatchedSizeSuitable(ele: TFigureElement, matchedMaterial: I_MaterialMatchingItem): boolean {
        if (ele.modelLoc == "床") {
            if (Math.abs(ele.rect.h - matchedMaterial.width) > 300) {
                return false;
            }
            if (ele.rect.w < 1500) {
                if (Math.abs(ele.rect.w - matchedMaterial.length) > 200) {
                    return false;
                }
            } else if (ele.rect.w >= 1500) {
                if (Math.abs(ele.rect.w - matchedMaterial.length) > 300) {
                    return false;
                }
            }
        } else if (["床头柜", "梳妆台", "书桌"].indexOf(ele.modelLoc) > -1) {
            if (matchedMaterial.length - ele.rect.w > 100) {
                return false;
            }
        } else if (["床尾凳"].indexOf(ele.modelLoc) > -1) {
            if (matchedMaterial.length > ele.rect.length + 300) {
                return false;
            }
        } else if (["挂画"].indexOf(ele.modelLoc) > -1) {
            if (matchedMaterial.height < ele.params.height) {
                return false;
            }
        } else if (ele.modelLoc.endsWith("灯")) {
            if (matchedMaterial.height > ele.params.height) {
                return false;
            }
        } else if (FigureCategoryManager.isCustomCabinet(ele)) {
            let lengthMinMax: { min: number, max: number } = this.getWardrobeMinMaxLength(ele.rect.w);
            if (matchedMaterial.length < lengthMinMax.min || matchedMaterial.length > lengthMinMax.max) {
                return false;
            }
        } else if (ele.modelLoc.indexOf("背景墙") > -1) {
            if (Math.abs(ele.length - matchedMaterial.length) < 5) {
                return false;
            }
        }
        return true;
    }

    public onRoomFloorThicknessChanged() {
        if (this._room && this._room._room_entity && this._room._room_entity.floor_thickness > 0) {
            const oldFloorZ: number = this.rect.floorZ;
            const newFloorZ: number = this._room._room_entity.floor_thickness;

            const newPosZ = this.rect.zval + newFloorZ - oldFloorZ;
            if (newPosZ + this.height <= this._room._room_entity.storey_height) {
                this.rect.zval = newPosZ;
                this.rect.floorZ = newFloorZ;
            }
            this.min_z = this.rect.floorZ;
        } else {
            const oldFloorZ: number = this.rect.floorZ;
            this.rect.zval = this.rect.zval - oldFloorZ;
            this.rect.floorZ = 0;
            this.min_z = 0;
        }
    }

    public set isAbnormalFigure(isAbnormal: boolean) {
        this._isAbnormalFigure = isAbnormal;
    }

    public get isAbnormalFigure(): boolean {
        return this._isAbnormalFigure;
    }

    public loadTopViewImage() {
        if (this._matched_material.topViewImage != null) {
            let img = new Image();
            img.src = this._matched_material.topViewImage;
            img.crossOrigin = "Anonymous";
            img.onload = () => {
                this.pictureViewImg = img;
            };
        }
        if (compareNames([this.sub_category], ["墙面", "地面"]) && this?._matched_material?.imageUrl) {
            let img = new Image();
            img.src = this._matched_material.imageUrl;
            img.crossOrigin = "Anonymous";
            img.onload = () => {
                this.pictureViewImg = img;
            };
        }
    }

    public async loadWireFrameImage() {
        if (this._matched_material.wireFrameImageUrl != null) {
            let img = new Image();
            img.src = this._matched_material.wireFrameImageUrl;
            img.crossOrigin = "Anonymous";
            img.onload = () => {
                this._wireFrameImage = img;
                LayoutAI_App.instance.update();
            };
        }
        if (this._matched_material.wireFrameImageUrl == null && this._ex_prop.material_id) {
            this._matched_material.modelId = String(this._ex_prop.material_id);
            await FigureTopViewer.instance.updateFigureWireFrameImage(this);
        }
    }

    public matchMainRect(heightTolerance: number = 50): ZRect {
        if (this.furnitureEntity instanceof TBaseGroupEntity) {
            let filterFigures: any[] = [];
            this.furnitureEntity._matched_combination_entitys.forEach(entity => {
                if (entity.height > heightTolerance) {
                    filterFigures.push(entity.figure_element);
                }
            });
            if (filterFigures.length > 0) {
                let range: any = TBaseRoomToolUtil.instance.getBoxRangByFigurs(filterFigures);
                let w: number = range.xMax - range.xMin;
                let h: number = range.yMax - range.yMin;
                let rect: ZRect = new ZRect();
                if (Math.abs(rect.nor.clone().dot(this.matched_rect.nor)) > 0.9) {
                    rect.length = w;
                    rect.depth = h;
                }
                else {
                    rect.length = h;
                    rect.depth = w;
                }
                rect.nor = this.matched_rect.nor.clone();
                rect.rect_center = TBaseRoomToolUtil.instance.calCenterByRange(range);
                return rect;
            }
        }
        return this._matched_rect;
    }

    private getTargetSize(materialItem: I_MaterialMatchingItem): { length: number, width: number, height: number } {
        let rectSize = { length: this.rect.w, width: this.rect.h, height: this.height };

        // 通过是否有模型来判断是否是替换
        let isReplace = !!this._solid_mesh3D;
        let entityType = TBaseEntity.get_polygon_type(this.rect);
        let isWindowDoor = entityType == AI_PolyTargetType.Window || entityType == AI_PolyTargetType.Door;

        let isKitchenCabinet = compareNames([this.sub_category], ["地柜", "吊柜","高柜"]);
        let targetSize = materialItem.targetSize || rectSize;

        if (isReplace && (isWindowDoor || isKitchenCabinet)) {
            if(this.category.startsWith("地柜"))
            {
                rectSize.height = targetSize.height;
            }
            return rectSize;
        }

        // 挂画墙饰最大高度800，超过800的按比例缩放
        if (["挂画", "墙饰"].includes(materialItem.modelLoc)) {
            let maxHeight = 800;
            if (targetSize.height > maxHeight) {
                let scale = maxHeight / targetSize.height;
                return {
                    length: rectSize.length * scale,
                    width: rectSize.width * scale,
                    height: maxHeight
                };
            }
        }
        return targetSize;
    }

    public getTargetRect(): ZRect {
        return this.matched_rect || this.rect;
    }

    public cleanDecoration() {
        if(this.decorationElements) {
            this.decorationElements.forEach((ele)=>{
                ele.dispose3d();
            });
            this.decorationElements =null;
        }
        if (this._combined_rects) {
            this._combined_rects.forEach((e) => {
                e.cleanDecoration();
            });
        }
    }

    public dispose3d() {
        if (this._solid_mesh3D) {
            Utils3D.disposeObject(this._solid_mesh3D);
            this._solid_mesh3D.removeFromParent();
            this._solid_mesh3D = null;
        }
        if (this._simple_mesh3D) {
            Utils3D.disposeObject(this._simple_mesh3D);

            this._simple_mesh3D.removeFromParent();
            this._simple_mesh3D = null;
        }
    }

    static getOrMakeFigureElementOfRect(rect: ZRect) {
        let figure_element: TFigureElement = rect._attached_elements[TFigureElement.EntityName];
        let furnitureObj = g_FigureImagePaths[rect.ex_prop.label];
        rect.cornerDepth = furnitureObj?.corner_depth || null;
        rect.cornerWidth = furnitureObj?.corner_width || null;
        if (!figure_element) {
            let category = rect.ex_prop['label'];
            let model_loc = category;
            if (furnitureObj?.public_category && !furnitureObj?.modelLoc) {
            }
            model_loc = furnitureObj?.modelLoc || '';

            figure_element = new TFigureElement({
                category: model_loc,
                modelLoc: furnitureObj?.modelLoc || '',
                public_category: furnitureObj?.public_category,
                sub_category: furnitureObj?.subCategory,
                params: {
                    length: rect._w,
                    depth: rect._h,
                    height: furnitureObj?.height || 2400,
                    off_land_rule: furnitureObj?.off_land_rule || null,
                    l_ex_width: furnitureObj?.corner_width,
                    l_ex_depth: furnitureObj?.corner_depth
                },
                shape: furnitureObj?.shape as FigureShapeType || null,
                min_z: furnitureObj?.zval || 0,
                max_z: 2400,
                image_path: furnitureObj?.img_path || '',
                _candidate_materials: [],
            });

            // 直接绑定rect
            figure_element.bindRect(rect);
            rect.zval = furnitureObj?.zval || 0;
            if (furnitureObj?.rotation_z) {
                rect.rotation_z = furnitureObj.rotation_z;
            }
        }
        return figure_element;
    }

    get topOffset() {
        return this._topOffset || 0;
    }

    set topOffset(value: number) {
        this._topOffset = value;
    }
}

