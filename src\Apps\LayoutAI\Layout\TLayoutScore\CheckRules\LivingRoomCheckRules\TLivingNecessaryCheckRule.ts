import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomSplitByPathClassfierCheckRule } from "./TLivingRoomSplitByPathClassfierCheckRule";

export class TLivingNecessaryCheckRule extends TLivingRoomSplitByPathClassfierCheckRule
{
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]): any
    {
        void(room);
        void(figure_element);
        void(main_figures);
        
        let score: number = 0;
        if(this.livingSpaceRect && this.diningSpaceRect)
        {
            score += 30;
        }
        if(this.otherSpaceRects && this.otherSpaceRects.length > 0)
        {
            score += 10;
        }
        return {score: score};
    }
}