import { LightMode } from "@/pages/SdkFrame/MsgCenter/IMsgType";
import { LightingRuler } from "../AutoLightingRuler";
import { AutoLightingRulerNightBrightConfigs } from "./AutoLightingRulerNightBrightConfigs";
import { AutoLightingRulerDayBrightConfigs } from "./AutoLightingRulerDayBrightConfigs"
import { AutoLightingRulerNightMediumConfigs } from "./AutoLightingRulerNightMediumConfigs";
import { AutoLightingRulerDayMediumConfigs } from "./AutoLightingRulerDayMediumConfigs"
import { AutoLightingRulerNightDarkConfigs } from "./AutoLightingRulerNightDarkConfigs";
import { AutoLightingRulerDayDarkConfigs } from "./AutoLightingRulerDayDarkConfigs"


export enum SeriesMode {
    Dark = "暗色系",
    Medium = "中色系",
    Bright = "亮色系",
    Default = "默认"
}

export const Series2LightMap: {
    [key in LightMode]: Record<SeriesMode, LightingRuler[]>
} = {
    [LightMode.Night]: {
        [SeriesMode.Dark]: AutoLightingRulerNightDarkConfigs,
        [SeriesMode.Medium]: AutoLightingRulerNightMediumConfigs,
        [SeriesMode.Bright]: AutoLightingRulerNightBrightConfigs,
        [SeriesMode.Default]: AutoLightingRulerNightMediumConfigs
    },
    [LightMode.Day]: {
        [SeriesMode.Dark]: AutoLightingRulerDayDarkConfigs,
        [SeriesMode.Medium]: AutoLightingRulerDayMediumConfigs,
        [SeriesMode.Bright]: AutoLightingRulerDayBrightConfigs,
        [SeriesMode.Default]: AutoLightingRulerDayMediumConfigs
    }
};