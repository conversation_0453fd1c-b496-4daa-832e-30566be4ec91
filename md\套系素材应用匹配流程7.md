# LayoutAI套系家具匹配流程

## 1. 总体流程概述

LayoutAI的套系家具匹配是将套系的风格素材应用到用户布局的房间中，实现一键整体风格定制。从产品层面来看，该流程包含以下几个关键阶段：

```mermaid
graph TD
    A[用户选择套系] --> B[套系应用启动]
    B --> C[素材匹配准备]
    C --> D[核心匹配处理]
    D --> E[匹配后处理]
    E --> F[UI更新与展示]
```

该文档将从产品逻辑角度详细介绍整个套系应用流程，帮助产品经理理解系统如何将套系素材与室内布局图元进行高效匹配和调整。

## 2. 套系应用入口：onSeriesSampleSelected方法

当用户选择套系后，系统调用`TSeriesFurnisher.onSeriesSampleSelected`方法，这是整个套系应用流程的起点。

### 2.1 方法参数

- **scope**：套系应用范围，包括：
  - soft：软装（如沙发、床等家具）
  - cabinet：定制柜（如衣柜、橱柜等）
  - hard：硬装（如墙面、地面、天花板等）
  - remaining：是否进行补全（对未匹配到素材的图元进行匹配）

- **series**：选中的套系样本数据

- **targetRooms**：目标房间列表，默认为当前选中的房间

- **options**：可选配置
  - needsDiversifyMatched：是否需要多样化匹配（避免同类素材重复使用）
  - updateTopViewBy3d：是否需要更新3D视图
  - needAutoFinetune：是否需要自动微调

### 2.2 执行流程

1. **初始化环节**：
   - 取消当前选中图元状态
   - 确定目标房间列表

2. **房间循环处理**：
   对每个目标房间进行并行处理：
   ```mermaid
   graph TD
       A[检查房间锁定状态] --> B{是否锁定?}
       B -->|是| C[跳过处理]
       B -->|否| D[创建套系副本]
       D --> E{是常规应用还是补全应用?}
       E -->|常规应用| F[清除当前匹配素材]
       F --> G[记录房间套系映射]
       G --> H[尝试匹配素材]
       E -->|补全应用| I[获取未匹配范围]
       I --> J[设置补全应用范围]
       J --> K[尝试补全匹配]
   ```

3. **后处理环节**：
   - 如启用了多样化匹配，避免相同图元使用重复素材
   - 更新界面显示
   - 进行自动微调（如已配置）

## 3. 素材匹配核心：tryToFurnishRooms与tryToFurnishRoomRemainings

### 3.1 常规套系应用：tryToFurnishRooms

`tryToFurnishRooms`方法负责对房间进行常规套系应用。

**方法流程**：
1. 将每个图元(furniture)与房间建立关联
2. 更新房间已应用套系的范围
3. 检查房间是否有套系对应关系且包含图元
4. 调用`TMaterialMatcher.furnishRoom`进行实际素材匹配

### 3.2 补全套系应用：tryToFurnishRoomRemainings

`tryToFurnishRoomRemainings`方法用于对房间中未匹配素材的图元进行补全处理。

**方法流程**：
1. 更新房间中待补全区域的应用范围
2. 将每个图元与房间建立关联
3. 调用`TMaterialMatcher.furnishRoom`对未匹配的图元进行匹配

## 4. 素材匹配执行：TMaterialMatcher.furnishRoom

该方法是套系素材匹配的核心，负责将套系中的素材匹配到房间中的图元上。

### 4.1 匹配准备阶段

```mermaid
graph TD
    A[检查房间锁定状态] --> B[更新房间套系信息]
    B --> C[清理电器装饰图元]
    C --> D[收集待匹配图元]
    D --> E{是常规匹配还是补全匹配?}
    E -->|常规匹配| F[过滤需匹配的图元]
    E -->|补全匹配| G[获取未匹配图元]
    F --> H[处理硬装图元]
    G --> H
    H --> I[过滤应用范围外图元]
    I --> J[收集台面家具装饰图元]
    J --> K[处理组合类图元]
```

1. **锁定检查**：如房间被锁定，则跳过匹配
2. **套系信息更新**：更新房间的套系信息和应用范围
3. **图元准备**：
   - 清理电器装饰图元
   - 根据应用类型（常规/补全）收集待匹配图元
   - 处理硬装图元（如墙面、地面等）
   - 过滤不在应用范围内的图元
   - 收集装饰图元和组合图元成员

### 4.2 素材匹配服务调用阶段

1. **固定装置生成**：如需匹配硬装，生成灯具、插座等固定装置
2. **素材匹配准备**：
   - 加入组合图元成员和装饰图元到匹配列表
   - 准备匹配参数和图元分类

3. **材质服务调用**：
   - 调用MaterialService.matchMaterials服务进行素材匹配
   - 接收匹配结果，包含每个图元的候选材料列表

4. **匹配结果处理**：
   - 设置图元的匹配素材和候选素材
   - 处理组合图元的匹配结果
   - 统计未匹配图元

### 4.3 匹配后处理阶段

```mermaid
graph TD
    A[调整素材尺寸和位置] --> B[处理背景墙相关图元]
    B --> C[处理吊顶图元]
    C --> D[调整装饰图元位置]
    D --> E[隐藏重复素材]
    E --> F[记录未匹配图元]
```

## 5. 匹配后处理器：MatchingPostProcesser

匹配后处理器负责在素材匹配完成后，对素材和图元进行精细调整，确保视觉效果与功能性。

### 5.1 主要处理功能

1. **adjustMatchedMaterialsAndFigureElements**：
   - 调整所有匹配素材的目标尺寸、位置和旋转
   - 同步更新图元的匹配矩形信息
   - 处理特殊类型图元的位置对齐（如靠墙家具）

2. **adjust_backgroundwall_related**：
   - 限制背景墙素材深度最大为120mm
   - 调整背景墙相关家具图元的位置对齐

3. **隐藏重复素材**：
   - 检测同类型重复素材（如多余的地毯、墙饰等）
   - 通过设置可见性为false处理重复素材

4. **特殊图元处理**：
   - 处理窗帘尺寸和位置
   - 限制素材高度不超过层高
   - 调整定制柜与墙面的对齐

### 5.2 位置尺寸修改

素材位置尺寸修改是后处理中的关键环节，主要包括：

1. **modifyMaterialPositionSize**：
   - 处理特定家具的旋转问题
   - 设置素材的目标布置位置和旋转角度
   - 根据素材类型设置目标尺寸：
     - 组合素材使用原始尺寸
     - 可缩放素材使用图元的尺寸
     - 特殊类型素材进行特定处理

2. **syncMatchedRectAndModifyMaterialPositionZ**：
   - 同步素材信息到图元的matchedRect
   - 处理不同类型家具的墙面对齐问题
   - 调整素材的高度位置

## 6. 整体流程总结

```mermaid
graph TD
    A[用户选择套系] --> B[onSeriesSampleSelected方法触发]
    B --> C[确定应用范围与目标房间]
    C --> D{应用类型判断}
    D -->|常规应用| E[tryToFurnishRooms]
    D -->|补全应用| F[tryToFurnishRoomRemainings]
    E --> G[furnishRoom素材匹配]
    F --> G
    G --> H[素材匹配准备]
    H --> I[素材服务调用]
    I --> J[匹配结果处理]
    J --> K[MatchingPostProcesser后处理]
    K --> L[更新UI与3D场景]
```

套系家具匹配流程是LayoutAI系统的核心功能之一，通过精细的设计与实现，可以将专业设计师精心搭配的套系素材智能应用到用户的房间布局中，极大提升了用户的设计效率和设计质量。该流程能够自动识别房间内的各类图元，从套系中选取合适的素材进行匹配，并通过后处理确保素材的位置、尺寸、旋转等属性符合设计要求，最终呈现专业的设计效果。 