import { Mesh, MeshStandardMaterial, Object3D } from "three";
import { TextureManager } from "@layoutai/model3d_api";


/**
* @description 3D工具类，不用依赖其它本地文件
* <AUTHOR>
* @date 2025-06-25
* @lastEditTime 2025-06-25 14:45:03
* @lastEditors xuld
*/
export class Utils3D {
    static disposeObject(object: Object3D) {
        if ((object as Mesh).isMesh) {
            let mesh = (object as Mesh);
            if (mesh.geometry) {
                mesh.geometry.dispose();
            }
            if (mesh.material) {
                if (mesh.material instanceof Array) {
                    mesh.material.forEach((m) => {
                        (m.dispose && m.dispose());
                    })
                }
                else {
                    let material = mesh.material as MeshStandardMaterial;
                    if(material.map)
                    {
                        let texture = material.map;
                        if(!texture.userData[TextureManager.IsCacheTexture])
                        {
                            texture.dispose();
                        }
                    }
                    mesh.material.dispose();
                }
            }
        }
        if (object.children) {
            object.children.forEach((obj) => Utils3D.disposeObject(obj));
        }
    }
}