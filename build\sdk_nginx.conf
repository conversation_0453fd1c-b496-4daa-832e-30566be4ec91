server {
  listen 80;
  root /usr/share/nginx/html;
  index index.html;

  location / {
        root /usr/share/nginx/html;
        add_header Access-Control-Allow-Origin '*';
        add_header Access-Control-Allow-Methods 'GET,POST,PUT,OPTIONS';
        add_header Access-Control-Allow-Headers '*';
        add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
        
        try_files $uri $uri/ /index.html;
    }

    location ~* \.(ico|gif|bmp|jpg|jpeg|png|swf|js|css|txt|svg|ttf|woff|webp|wasm) {
        add_header Access-Control-Allow-Origin '*';
        add_header Access-Control-Allow-Methods 'GET,POST,PUT,OPTIONS';
        add_header Access-Control-Allow-Headers '*';
        
        expires 7d;
    }

    error_page   500 502 503 504  /50x.html;

  location ~* \.(ico|gif|bmp|jpg|jpeg|png|swf|js|css|txt|svg|ttf|woff|webp|wasm) {
        add_header Access-Control-Allow-Origin '*';
        add_header Access-Control-Allow-Methods 'GET,POST,PUT,OPTIONS';
        add_header Access-Control-Allow-Headers '*';
        
        expires 7d;
  }
}
