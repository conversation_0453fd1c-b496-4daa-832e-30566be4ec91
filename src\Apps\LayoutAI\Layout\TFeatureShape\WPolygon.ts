import { Vector3 } from "three";
import { compareNames, range_substract } from "@layoutai/z_polygon";
import { ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { HallwayWidth, I_Window } from "../IRoomInterface";
import { TRoomShape } from "../TRoomShape";
import { ZDistanceDimension, makeDimensionsOfEdge } from "@layoutai/z_polygon";
export type SplitStrategies = "SplitWall" | "SplitRoom";
// 一些临时扩展属性名称
const ValidWallRects = "ValidWallRects";
const outter_wall_rect_name = "outter_wall_rect_name";
const MaxSingleDoorLength = 1300;

const GridNeighborRects = "GridNeighborRects";
const GridIsAlignWall = "GridIsAlignWall";
const GridAlignWallEdges = "GridAlignWallEdges";
const GridIsInfrontDoor = "GridIsInfrontDoor";
const GridDistanceToDoor = "GridDistanceToDoor";
const GridWallOverlapLength = "GridWallOverlapLength";
const GridPosId = "GridPosId";
const MaxRect = "MaxRect";
export const AttachedWPolygon = "AttachedWPolygon";
export class WPolygon extends ZPolygon {
    constructor() {
        super();
    }
    static sortPolysByInnerMaxRect(ans_polys:ZPolygon[])
    {
        ans_polys.forEach((poly)=>{
            let t_rect = TRoomShape.computeMaxRectBySplitShape(poly);
            poly._attached_elements[MaxRect] = t_rect || new ZRect(1,1);
        });
        ans_polys.sort((a,b)=>b._attached_elements[MaxRect].area - a._attached_elements[MaxRect].area);

    }
    static getOutterWallRects(poly: ZPolygon, thickness: number = 120, force: boolean = false): ZRect[] {

        if (!force) {
            if (poly._attached_elements[outter_wall_rect_name]) {
                return poly._attached_elements[outter_wall_rect_name];
            }
        }

        let wall_rects: ZRect[] = poly.edges.map((edge) => {
            let rect = new ZRect(edge.length, thickness);
            rect.nor = edge.nor;
            rect.back_center = edge.center;
            rect.updateRect();
            rect.reOrderByOrientation();
            return rect;
        });
        poly._attached_elements[outter_wall_rect_name] = wall_rects;
        return wall_rects;
    }
    static cleanOutterWallRects(poly: ZPolygon) {
        if (poly._attached_elements[outter_wall_rect_name]) {
            delete poly._attached_elements[outter_wall_rect_name];
        }
    }
    static getWindowOnEdge(edge: ZEdge): I_Window {
        return edge && edge._attached_elements['window'] || null;
    }

    static setWindowOnEdge(edge: ZEdge, win: I_Window, align_wall_dist: number = 100) {
        edge._attached_elements['window'] = win;
    }
    static setWallRectOnEdge(edge:ZEdge, rect:ZRect)
    {
        edge._attached_elements["WallRect"] = rect;
    }
    static getWallRectOnEdge(edge : ZEdge) : ZRect
    {
        return edge._attached_elements["WallRect"] || null;
    }

    static getDimensionOfEdge(edge:ZEdge,offset_len:number =600):ZDistanceDimension
    {
        let dim :ZDistanceDimension= edge._attached_elements["DistDim"] || makeDimensionsOfEdge(edge);
        dim.offset_len = offset_len;
        dim.pos0.copy(edge.v0.pos);
        dim.pos1.copy(edge.v1.pos);
        edge._attached_elements["DistDim"] = dim;
        return dim;
    }
    
    static getAttachedWPolygon(poly:ZPolygon):ZPolygon
    {
        return poly._attached_elements[AttachedWPolygon] || null;
    }
    static setAttachedWPolygon(poly:ZPolygon,w_poly:ZPolygon)
    {
        poly._attached_elements[AttachedWPolygon] = w_poly;
    }


    /**
     * 对这条边 根据 窗户 进行拆分, 并存到w_poly里面
     * @param edge  
     * @param windows 
     * @param w_poly 
     */
    static splitEdgeByWindows(edge: ZEdge, windows: I_Window[], w_poly: WPolygon) {

        let unvalid_range_pairs: number[][] = [];
        for (let win of windows) {
            let xx = edge.projectEdge2d(win.center).x;
            unvalid_range_pairs.push([xx - win.length / 2, xx + win.length / 2]);
        }
        let valid_range_pairs: number[][] = range_substract(edge.length, unvalid_range_pairs);
        for (let pair of valid_range_pairs) {
            let p0 = edge.unprojectEdge2d({ x: pair[0], y: 0 });
            let p1 = edge.unprojectEdge2d({ x: pair[1], y: 0 });

            let n_edge = new ZEdge({ pos: p0 }, { pos: p1 });
            n_edge._nor.copy(edge.nor);
        }
        let px_list: number[] = [0];

        for (let pair of valid_range_pairs) {
            if (pair[0] > px_list[px_list.length - 1] + 10 && pair[0] <= edge.length - 0.1) {
                px_list.push(pair[0]);
            }
            if (pair[1] > px_list[px_list.length - 1] + 10 && pair[1] <= edge.length - 0.1) {
                px_list.push(pair[1]);
            }
        }

        if (edge.length > px_list[px_list.length - 1]) {
            px_list.push(edge.length);
        }


        for (let ti = 0; ti < px_list.length - 1; ti++) {
            let p0 = edge.unprojectEdge2d({ x: px_list[ti], y: 0 });
            let p1 = edge.unprojectEdge2d({ x: px_list[ti + 1], y: 0 });
            let t_edge = new ZEdge(w_poly.addVertex(p0), w_poly.addVertex(p1));
            t_edge._nor.copy(edge.nor);
            for (let win of windows) {
                let tpp = t_edge.projectEdge2d(win.center);
                if (tpp.x < 0 || tpp.x > t_edge.length) continue;
                WPolygon.setWindowOnEdge(t_edge, win);
            }
            w_poly.edges.push(t_edge);
        }
    }

    static makeWPolygonByWindows(poly:ZPolygon, windows:I_Window[],w_poly:WPolygon,tol:number=240)
    {
        poly.edges.forEach((edge)=>{
            let edge_windows = windows.filter((win)=>{
                if(!win.rect) return false;
                
                let t_edge = win.rect.backEdge.computeLayOnEdge(edge,tol + win.rect.h/2,0.01);
                if(t_edge)
                {
                    return true;
                }
                return false;
            });

            WPolygon.splitEdgeByWindows(edge,edge_windows,w_poly);
        });
        w_poly.is_orderd_array = true;
        w_poly._updateVerticesByEdgesList();
    }
    

    static reOrderOfWPoly(poly: ZPolygon, id: number, order: number = 1) {
        if (id < 0 || id >= poly.edges.length) return;

        let t_edges: ZEdge[] = [];
        for (let i = 0; i < poly.edges.length; i++) {
            let n_edge = poly.edges[(id + i * order + poly.edges.length) % poly.edges.length];

            if (order <= -0.9999) {
                let tmp = n_edge.v0; n_edge.v0 = n_edge.v1; n_edge.v1 = tmp;
            }
            t_edges.push(n_edge);
        }

        poly.edges = t_edges;
        // TsAI_app.log(t_edges);

        poly._updateEdgeNeighbors();
        poly._updateVerticesByEdgesList();
        poly.computeZNor();

    }

    static getEdgeWallDistObj(edge: ZEdge, edge1: ZEdge): {
        i: number, j: number, next_around_dist: number, prev_around_dist: number,
        face_to_dist: number, nearest_path_dist: number, path_ids?: number[]
    } {
        if (edge1._w_edge_id < 0) return null;
        if (edge._attached_elements['wall_dist']) {

            if (!edge._attached_elements['wall_dist'][edge1._w_edge_id]) {
                edge._attached_elements['wall_dist'][edge1._w_edge_id] = {
                    i: edge._w_edge_id, j: edge1._w_edge_id,
                    next_around_dist: 100000, prev_around_dist: 100000, face_to_dist: 100000, nearest_path_dist: 100000, path_ids: []
                };
            }
            return edge._attached_elements['wall_dist'][edge1._w_edge_id];
        }
        else {
            return null;
        }
    }
    static computeWPolyDistance(w_poly: ZPolygon) {

        w_poly.computeZNor();

        let e_id = 0;

        for (let edge of w_poly.edges) {
            edge._w_edge_id = e_id;
            edge._attached_elements['wall_dist'] = {};
            e_id++;
        }


        for (let i = 0; i < w_poly.edges.length; i++) {
            let edge0 = w_poly.edges[i];
            let last_dist = { next_around_dist: 0 };
            for (let j = 0; j < w_poly.edges.length; j++) {
                let edge1 = w_poly.edges[(i + j) % w_poly.edges.length];

                let wall_dist = WPolygon.getEdgeWallDistObj(edge0, edge1);


                if (j == 0) {
                    wall_dist.next_around_dist = 0;
                    wall_dist.prev_around_dist = 0;
                }
                else {
                    last_dist.next_around_dist += edge1.length / 2.;
                    wall_dist.next_around_dist = last_dist.next_around_dist;

                }
                last_dist.next_around_dist += edge1.length / 2.;

            }


        }

        for (let i = 0; i < w_poly.edges.length; i++) {
            for (let j = 0; j < w_poly.edges.length; j++) {
                if (i !== j) {
                    let wall_dist0 = WPolygon.getEdgeWallDistObj(w_poly.edges[i], w_poly.edges[j]);
                    let wall_dist1 = WPolygon.getEdgeWallDistObj(w_poly.edges[j], w_poly.edges[i]);

                    wall_dist0.prev_around_dist = wall_dist1.next_around_dist;
                    wall_dist1.prev_around_dist = wall_dist0.next_around_dist;
                }
            }
        }


        for (let edge of w_poly.edges) {
            let face_to_dist = 100000;
            let layon_len = 0.;
            for (let o_edge of w_poly.edges) {
                if (edge == o_edge) continue;

                if (edge.nor.dot(o_edge.nor) > -0.5) continue; // 需要正对

                let dist = o_edge.center.clone().sub(edge.center).dot(edge.nor);

                if (dist > 0) continue; // dist<0才是内部方向
                let wall_dist0 = WPolygon.getEdgeWallDistObj(edge, o_edge);

                let layon_data: { layon_len?: number, ll?: number, rr?: number } = {};
                if (o_edge.islayOn(edge, 100000, 100 / edge.length, layon_data)) {
                    wall_dist0.face_to_dist = -dist;
                    if (layon_data.layon_len > layon_len) {
                        face_to_dist = wall_dist0.face_to_dist;
                        layon_len = layon_data.layon_len;
                    }


                }

            }
            edge._attached_elements['face_to_dist'] = face_to_dist;

            for (let o_edge of w_poly.edges) {
                let wall_dist0 = WPolygon.getEdgeWallDistObj(edge, o_edge);
                wall_dist0.nearest_path_dist = Math.min(wall_dist0.face_to_dist, wall_dist0.next_around_dist, wall_dist0.prev_around_dist);
                wall_dist0.path_ids = [edge._w_edge_id, o_edge._w_edge_id];

            }
        }

        let iter = 3;
        while (iter--) {

            for (let edge0 of w_poly.edges) {
                for (let edge1 of w_poly.edges) {
                    let dist_0_1 = WPolygon.getEdgeWallDistObj(edge0, edge1);

                    if (edge0 == edge1) continue;
                    for (let edge2 of w_poly.edges) {
                        let dist_0_2 = WPolygon.getEdgeWallDistObj(edge0, edge2);
                        let dist_2_1 = WPolygon.getEdgeWallDistObj(edge2, edge1);

                        let sum = dist_0_2.nearest_path_dist + dist_2_1.nearest_path_dist;

                        if (sum < dist_0_1.nearest_path_dist) {
                            dist_0_1.path_ids = [
                                ...dist_0_2.path_ids,
                                ...dist_2_1.path_ids,
                            ];
                            dist_0_1.nearest_path_dist = sum;
                        }

                    }


                }
            }
        }
        for (let edge0 of w_poly.edges) {
            for (let edge1 of w_poly.edges) {
                let dist_0_1 = WPolygon.getEdgeWallDistObj(edge0, edge1);

                // console.log(dist_0_1.i,dist_0_1.j,dist_0_1.nearest_path_dist - (edge1.length/2),dist_0_1.path_ids);

            }
        }


    }


    static makeNormalPolygon(poly0: ZPolygon) {
        let main_rect = ZRect.fromBox3(poly0.computeBBox(), { x: 0, y: 1, z: 0 });

        let positions = poly0.vertices.map((v) => {
            let pp = main_rect.project(v.pos);
            return { x: pp.x, y: pp.y, z: 0 };
        })
        let poly = new ZPolygon();
        poly.initByVertices(positions);
        poly.computeZNor();

        return poly;

    }
    // 单纯计算两个多边形的相似性
    static comparePolygonSimilarToB(poly0: ZPolygon, poly1: ZPolygon, params: { sample_step_len?: number } = { sample_step_len: 300 }) {
        let t_poly0 = WPolygon.makeNormalPolygon(poly0);
        let t_poly1 = WPolygon.makeNormalPolygon(poly1);

        let sum_length = 0.;
        let sum_dist = 0;
        for (let edge of t_poly1.edges) {
            let num = Math.floor(edge.length / params.sample_step_len + 0.1) + 1;
            for (let i = 0; i < num; i++) {
                let pos = edge.unprojectEdge2d({ x: edge.length / num * i, y: 0 });

                let dist = t_poly0.distanceToPoint(pos);
                sum_dist += Math.abs(dist) * edge.length / num;
            }
            sum_length += edge.length;
        }

        return sum_dist / sum_length;







    }
    static getTargetEdgeAlignOnMainRect(w_poly: ZPolygon, main_rect: ZRect, main_rect_edge_id: number, room_name: string) {
        let length = main_rect.w;
        let depth = main_rect.h;
        let edge: ZEdge = null;
        if (main_rect_edge_id !== undefined && main_rect) {
            let r_edge = main_rect.edges[(main_rect_edge_id + main_rect.edges.length - 1) % main_rect.edges.length];

            let target_edge: ZEdge = null;
            // if(compareNames([room_name],["卫生间"]))
            // {
            //     return r_edge;
            // }
            for (let w_edge of w_poly.edges) {

                let win = WPolygon.getWindowOnEdge(w_edge);
                if (win) {
                    if (compareNames([room_name], ["卧室"]) == 0) {
                        if (win.type == "Door") continue;
                    }
                    else {
                        continue;
                    }
                }


                if (main_rect_edge_id >= 4) {
                    if (r_edge.nor.dot(w_edge.nor) >= 0.6 && !r_edge.islayOn(w_edge, Math.min(length, depth) / 2, 0.2)) {
                        if (!target_edge || w_edge.length > target_edge.length) {
                            target_edge = w_edge;
                        }
                    }
                }
                else {
                    if (r_edge.islayOn(w_edge, Math.min(length, depth) / 2, 0.1)) {
                        if (!target_edge || w_edge.length > target_edge.length) {
                            target_edge = w_edge;
                        }
                    }
                }


            }

            edge = target_edge;


        }
        return edge;
    }



    static splitPolyOnce(wall_poly: ZPolygon, strategy: SplitStrategies = "SplitWall") {
        let ans_polys: ZPolygon[] = [];

        if (wall_poly.edges.length <= 4) {
            ans_polys.push(wall_poly);
            return ans_polys;
        }


        let candidates: { type: number, edge: ZEdge, weight: number }[] = [];
        wall_poly.computeZNor();
        let u_edge_weight: number = 1;
        let s_edge_weight: number = 1;
        if (strategy === "SplitRoom") {
            u_edge_weight = 10;
            s_edge_weight = 1;
        }
        else if (strategy === "SplitWall") {
            u_edge_weight = 10;
            s_edge_weight = 1;
        }
        for (let edge of wall_poly.edges) {
            let is_s_edge: boolean = false;
            let is_u_edge: boolean = false;
            if (Math.abs(edge.next_edge.dv.dot(edge.dv)) < 0.01) // 先保证垂直
            {
                if (edge.next_edge.dv.dot(edge.prev_edge.dv) >= 0.9)   // s型
                {
                    is_s_edge = true;
                }

                if (edge.next_edge.dv.dot(edge.prev_edge.dv) < -0.9 && edge.nor.dot(edge.next_edge.dv) < -0.5) // u型
                {
                    is_u_edge = true;
                }
            }
            if (is_u_edge) {
                let t_nor = edge.dv.clone();
                let t_point: Vector3 = null;
                if (edge.next_edge.length < edge.prev_edge.length) {
                    t_point = edge.next_edge.v1.pos.clone();
                    t_nor.negate();
                }
                else {
                    t_point = edge.prev_edge.v0.pos.clone();
                }

                t_point.add(t_nor.clone().multiplyScalar(0.5));

                let ans_p = wall_poly.getRayIntersection(t_point, t_nor);

                if (ans_p && ans_p.point) {
                    let pos0 = ans_p.point.clone().add(t_nor.clone().multiplyScalar(0.5));
                    let pos1 = t_point.clone().add(t_nor.clone().multiplyScalar(-1));
                    let split_edge = new ZEdge({ pos: pos0 }, { pos: pos1 });
                    let length = Math.min(edge.length, edge.next_edge.length, edge.prev_edge.length);

                    if (length < 100)  // 不能太短
                    {
                        length *= 100;
                    }
                    candidates.push({ type: 1, edge: split_edge, weight: length / u_edge_weight }); // 权重为切割线长度, 越短越好
                }

            }

            if (is_s_edge) {

                let t_nor = edge.next_edge.nor.clone().negate();

                let t_point = (edge.dv.dot(t_nor) > 0) ? edge.v1.pos.clone() : edge.v0.pos.clone();

                t_point.add(t_nor.clone().multiplyScalar(0.5));
                let ans_p = wall_poly.getRayIntersection(t_point, t_nor);

                if (ans_p && ans_p.point) {
                    let pos0 = ans_p.point.clone().add(t_nor.clone().multiplyScalar(0.5));
                    let pos1 = t_point.clone().add(t_nor.clone().multiplyScalar(-1));
                    let split_edge = new ZEdge({ pos: pos0 }, { pos: pos1 });
                    let length = edge.length;

                    candidates.push({ type: 1, edge: split_edge, weight: edge.length / s_edge_weight }); // S线所在的长度
                }
            }


        }


        candidates.sort((a, b) => a.weight - b.weight);  // 从短到长
        for (let candidate of candidates) {
            let split_edge = candidate.edge;
            split_edge.computeNormal(wall_poly.orientation_z_nor);
            let sub_polys = wall_poly.splitByLine(split_edge.center, split_edge.dv, split_edge.length);


            if (!sub_polys || sub_polys.length == 1) continue;
            let t_polys: ZPolygon[] = [];
            if (sub_polys && sub_polys.length == 2) {
                // console.log(wall_poly.edges.length,sub_polys[0].edges.length,sub_polys[1].edges.length);

                let flag = true;
                for (let poly of sub_polys) {
                    if (poly.edges.length == wall_poly.edges.length) {
                        flag = false;
                        break;
                    }
                    if (poly.edges.length < 3) {
                        flag = false;
                        break;
                    }
                    t_polys.push(poly);


                }
                if (!flag) {
                    continue;
                }
                return t_polys;
            }
        }
        return [wall_poly];
    }

    static removeOnlinePoints(poly: ZPolygon) {
        let target_edge: ZEdge = null;
        for (let edge of poly.edges) {
            if (edge.length < 0.0001) {
                target_edge = edge;
                break;
            }
            if (edge.dv.dot(edge.next_edge.dv) >= 0.95) {
                target_edge = edge;
                break;
            }
        }

        if (target_edge) {
            let new_edge = new ZEdge(target_edge.v0, target_edge.next_edge.v1);

            poly.replaceEdges([target_edge, target_edge.next_edge], [new_edge]);
            poly.computeZNor();

        }
        else {
            return false;
        }
        return true;

    }

    static optmizePoly(poly: ZPolygon) {
        let iter = 4;
        while (iter-- >= 0) {
            if (this.removeOnlinePoints(poly)) {
                continue;
            }
        }
    }

    static splitPolyIntoRects(wall_poly: ZPolygon, strategy: SplitStrategies = "SplitWall") {
        let queue: ZPolygon[] = [wall_poly];

        const prop_name = "is_leaf_child";
        for (let qi = 0; qi < queue.length; qi++) {
            let t_poly = queue[qi];
            this.optmizePoly(t_poly);
            if (t_poly.edges.length <= 4) {
                t_poly.ex_prop[prop_name] = "1";
                continue;
            }


            let sub_polys = this.splitPolyOnce(t_poly, strategy);

            if (sub_polys.length >= 2) {
                queue.push(...sub_polys);
            }
            else {
                t_poly.ex_prop[prop_name] = "1";
            }

        }

        let ans_polys: ZPolygon[] = [];

        for (let poly of queue) {
            if (poly.ex_prop[prop_name] === "1") {
                ans_polys.push(poly);
            }
        }

        return ans_polys;

    }
    /**
     * 计算贴在某条边上的内部矩形列表
     * @param edge  
     * @param w_poly 
     */
    static _computeValidRectsOnEdge(edge: ZEdge, w_poly: ZPolygon, filter_func: (rect: ZRect) => boolean = null, ValidWallName: string = ValidWallRects) {
        let x_vals: number[] = [0, edge.length];
        w_poly.edges.forEach((w_edge) => {
            if (edge === w_edge) return;
            let pp = edge.projectEdge2d(w_edge.v0.pos);
            if (pp.y > 0) return;

            if (pp.x < 0 || pp.x > edge.length) return;
            x_vals.push(pp.x);
        });
        x_vals.sort((a, b) => a - b);

        let tx_xvals: number[] = [x_vals[0]];
        for (let i = 1; i < x_vals.length; i++) {
            if (x_vals[i] - tx_xvals[tx_xvals.length - 1] > 1.) {
                tx_xvals.push(x_vals[i]);
            }
        }

        // 计算y_vals, 发出射线, 依次求交
        let y_vals: number[] = [];

        x_vals = [];
        tx_xvals.forEach((x_val) => {
            for (let i = 0; i < 2; i++) {
                let t_x = x_val + (i > 0 ? 1 : -1);
                if (t_x < 0 || t_x > edge.length) continue;
                let p0 = edge.unprojectEdge2d({ x: t_x, y: 0 });
                let nor = edge.nor.clone().negate();
                p0.add(nor.clone().multiplyScalar(1));
                let ans = w_poly.getRayIntersection(p0, nor);
                if (ans && ans.point) {
                    x_vals.push(x_val);
                    y_vals.push(-edge.projectEdge2d(ans.point).y);
                }
                else {
                    x_vals.push(x_val);
                    y_vals.push(0);
                }
            }

        });

        let candidate_rects: ZRect[] = [];
        for (let i = 0; i < x_vals.length; i++) {
            let yy = y_vals[i];
            let ll = x_vals[i];
            let rr = ll;
            for (let j = i + 1; j < x_vals.length; j++) {
                let t_yy = y_vals[j];
                if (yy > t_yy) {
                    break;
                }
                rr = x_vals[j];

            }
            if (rr > ll) {
                let rect = new ZRect(rr - ll, yy);
                rect.nor = edge.nor.clone().negate();
                rect.back_center = edge.unprojectEdge2d({ x: (ll + rr) / 2, y: 0 });
                rect.updateRect();
                candidate_rects.push(rect);
            }
        }

        const is_merged = "is_merged";
        for (let rect0 of candidate_rects) {
            if (rect0.ex_prop[is_merged]) continue;

            for (let rect1 of candidate_rects) {
                if (rect0 === rect1) continue;
                if (rect1.ex_prop[is_merged]) continue;

                if (rect0.containsPoly(rect1, 10)) {
                    rect1.ex_prop[is_merged] = "1";
                }
            }
        }
        candidate_rects = candidate_rects.filter((rect) => !rect.ex_prop[is_merged]);

        if (filter_func) {
            candidate_rects = candidate_rects.filter(rect => filter_func(rect));
        }

        candidate_rects.sort((a, b) => b.area - a.area);
        edge._attached_elements[ValidWallName] = candidate_rects;

        return candidate_rects;
    }

    static _getValidRectsOnEdge(edge: ZEdge, ValidWallName: string = ValidWallRects) {
        return edge._attached_elements[ValidWallName] as ZRect[] || null;
    }

    static _checkConvexWallEdge(edge: ZEdge, dot_tol: number = 0.1) {
        if (!edge.next_edge || !edge.prev_edge) {
            return false;
        }

        if (edge.next_edge.dv.dot(edge.prev_edge.dv) < -1 + dot_tol && Math.abs(edge.next_edge.dv.dot(edge.dv)) < dot_tol) {
            return true;
        }


        return false;
    }


    /**
     * 计算贴墙---且可放置柜子的矩形
     *    --- W_Poly已经裁剪过 门、窗了, 所以柜子不会靠门窗
     *    --- 主要是考虑过道, 特别是阳角处
     *      --- 算法:  假设柜子深600, 过道深800, 那么就需要墙往内延申(800+600)/2=700
     * 
     * @param w_poly  
     * @param cabinet_depth 
     * @param wall_hallway_depth 
     */
    static _computeValidOnWallCabinetRects(w_poly: ZPolygon, wall_hallway_depth: number = 800, cabinet_depth: number = 450, cabinet_length: number = 800) {
        let expand_depth = (cabinet_depth + wall_hallway_depth) / 2;

        let cut_polys = WPolygon._computeCuttedInnerPolygons(w_poly, wall_hallway_depth, cabinet_depth, cabinet_length);
        // console.log(cut_polys);

        // 裁剪剩下---内部的可活动多边形后, 再找跟墙overlap的边, 说明是可以放柜子的
        let inner_poly_edges: ZEdge[] = [];
        cut_polys.forEach((poly) => {
            inner_poly_edges.push(...poly.edges.filter((edge) => edge.length >= 1.));
        });

        let check_is_valid_wall_edge = (edge: ZEdge) => {
            let win = WPolygon.getWindowOnEdge(edge);
            if (win) return false;
            return true;
        }
        let wall_edges = w_poly.edges.filter((edge) => {
            return check_is_valid_wall_edge(edge);
        });

        // console.log(wall_edges,cut_polys);
        let cabinet_rects: ZRect[] = [];

        let has_checked_w_edges: ZEdge[] = [];
        const error_tol = 30;
        inner_poly_edges.forEach((inner_edge) => {
            wall_edges.forEach((wall_edge) => {
                if (!inner_edge.checkSameNormal(wall_edge.nor)) return;
                let layon_edge = inner_edge.computeLayOnEdge(wall_edge, expand_depth + 10, 0.0001);

                // console.log( layon_edge, layon_edge?.length || 0, inner_edge._edge_id, inner_edge.length, wall_edge._edge_id,wall_edge.projectEdge2d(inner_edge.center).y,expand_depth);

                if (layon_edge && layon_edge.length >= 1.) {

                    let v0 = layon_edge.v0;
                    let v1 = layon_edge.v1;
                    let pp0 = wall_edge.projectEdge2d(v0.pos);
                    let pp1 = wall_edge.projectEdge2d(v1.pos);
                    // 1、看看能否向左延拓到边:
                    //  (1) 距离墙角小于等于延拓值; (2) 该角为阴角;  (3)侧边不是门 

                    const check_abs = false;
                    if (pp0.x <= expand_depth + error_tol) {
                        if (wall_edge.prev_edge.checkSameDirection(wall_edge.nor, check_abs)) // 说明应该是阴角
                        {
                            let win = WPolygon.getWindowOnEdge(wall_edge.prev_edge);
                            if (!(win && win.type === "Door" && win.length < 1500)) // 侧边不是推拉门
                            {
                                v0.pos.copy(wall_edge.v0.pos);
                            }
                        }
                    }
                    if (pp1.x >= wall_edge.length - expand_depth - error_tol) {
                        if (wall_edge.checkSameDirection(wall_edge.next_edge.nor, check_abs)) {
                            let win = WPolygon.getWindowOnEdge(wall_edge.next_edge);
                            if (!(win && win.type === "Door" && win.length < 1500)) // 侧边不是推拉门
                            {
                                v1.pos.copy(wall_edge.v1.pos);
                            }
                        }
                    }

                    if (layon_edge.length < cabinet_length) return;

                    let rect = new ZRect(layon_edge.length, cabinet_depth);
                    rect.nor = layon_edge.nor.clone().negate();
                    rect.back_center = layon_edge.center;
                    rect.updateRect();
                    cabinet_rects.push(rect);

                    has_checked_w_edges.push(wall_edge);
                }

            })

        })


        // 检测墙洞类的

        let dot_tol = 0.1;
        w_poly.edges.forEach((edge) => {
            // 检测
            let win = WPolygon.getWindowOnEdge(edge);
            if (win) return;
            if (has_checked_w_edges.includes(edge)) return;
            if (WPolygon._checkConvexWallEdge(edge) && edge.next_edge.nor.dot(edge.dv) > 1. - dot_tol
                && Math.min(edge.next_edge.length, edge.prev_edge.length) < wall_hallway_depth
                && edge.length >= cabinet_length && edge.length < cabinet_length * 2.5) {
                let rect = new ZRect(edge.length, cabinet_depth);
                rect.nor = edge.nor.clone().negate();
                rect.back_center = edge.center;

                let depth = Math.min(edge.next_edge.length, edge.prev_edge.length);
                if (depth < cabinet_depth) {
                    if (depth < cabinet_depth - 120) {
                        return;
                    }
                    rect.depth = depth;
                    rect.back_center = edge.unprojectEdge2d({ x: edge.length / 2, y: 0 });
                }

                rect.updateRect();
                if (w_poly.containsPoint(rect.unproject({ x: 0, y: cabinet_depth / 2 + wall_hallway_depth }))) {
                    cabinet_rects.push(rect);
                }


            }
        })





        return cabinet_rects;

    }



    /**
     * 计算内部可活动的区域多边形
     *   -- 会裁剪接近墙的过道区域, 仅留中间部分
     * @param w_poly  
     * @param wall_hallway_depth 
     * @param cabinet_depth 
     * @param cabinet_length 
     * @returns 
     */
    static _computeCuttedInnerPolygons(w_poly: ZPolygon, wall_hallway_depth: number = 900, cabinet_depth: number = 450, cabinet_length: number = 600) {
        let expand_depth = (cabinet_depth + wall_hallway_depth) / 2;

        let w1_poly = w_poly.clone();
        w1_poly.reOrderByOrientation(true);
        let expanded_poly = w1_poly.clone().expandPolygon(-expand_depth);
        expanded_poly.computeZNor();

        let sub_polys: ZPolygon[] = [];
        for (let i = 0; i < expanded_poly.edges.length; i++) {
            let w_edge = w1_poly.edges[i];
            let i_edge = expanded_poly.edges[i];

            let s_poly = new ZPolygon();
            s_poly.initByVertices([
                w_edge.v0.pos.clone(), w_edge.v1.pos.clone(),
                i_edge.v1.pos.clone(), i_edge.v0.pos.clone()
            ]);
            s_poly.reOrderByOrientation(true);

            sub_polys.push(s_poly);
        }
        // 添加门
        const door_room_names: string[] = ["卧室", "书房", "卫生间"];
        w_poly.edges.forEach((edge) => {
            let win = WPolygon.getWindowOnEdge(edge);
            if (win && win.type === "Door") {
                if (win.realType === "SlidingDoor") return;
                let inner_hh = wall_hallway_depth;

                if (win.room_names && !compareNames(win.room_names, door_room_names) && win.room_names.length > 1) {
                    inner_hh = inner_hh / 2;
                }
                if (edge.length < 1500) // 一般是单开门
                {
                    let rect = new ZRect(edge.length, inner_hh);
                    rect.nor = edge.nor.clone().negate();
                    rect.back_center = edge.center;
                    rect.updateRect();
                    sub_polys.push(rect);
                }
            }
        });

        w_poly.edges.forEach((edge) => {
            if (edge.prev_edge && edge.prev_edge.checkSameDirection(edge.nor.clone().negate(), false) && edge.length > 800 &&
                edge.prev_edge.length > 800) // 检查如果是阳角
            {
                let rect0 = new ZRect(expand_depth, expand_depth);

                rect0.nor = edge.prev_edge.nor.clone().negate();
                rect0.back_center = edge.prev_edge.unprojectEdge2d({ x: edge.prev_edge.length + rect0.w / 2, y: 0 });
                rect0.updateRect();
                sub_polys.push(rect0);

                let rect1 = rect0.clone();
                rect1.nor = edge.nor.clone().negate();
                rect1.back_center = edge.unprojectEdge2d({ x: -rect1.w / 2, y: 0 });
                rect1.updateRect();
                // sub_polys.push(rect1);



            }
        })
        let cut_polys = w1_poly.substract_polygons(sub_polys, 10);
        return cut_polys;
    }


    /**
     * 计算裁剪过道后的区域
     *    --- [减去]过道区域
     *    --- [减去]门前区域
     *    --- [减去]阳角区域
     * @param w_poly  
     * @param wall_hallway_depth 
     * @param cabinet_depth 
     */
    static _computeCuttedValidPolygons(w_poly: ZPolygon, wall_hallway_depth: number = 900, cabinet_depth: number = 450, door_room_names: string[] = ["卧室", "书房", "卫生间", "入户花园"]) {
        let w1_poly = w_poly.clone();
        w1_poly.reOrderByOrientation(true);
        TRoomShape.optimizePoly(w1_poly);
        let hallway_rects: ZRect[] = WPolygon._computeHallwayRects(w1_poly, wall_hallway_depth + cabinet_depth, cabinet_depth);

        let infront_door_rects: ZRect[] = [];
        // 添加门
        const min_inner_tol = 20;

        w_poly.edges.forEach((edge) => {
            let win = WPolygon.getWindowOnEdge(edge);
            if (win && win.type === "Door") {
                if (win.realType === "SlidingDoor") return;
                let inner_hh = Math.max(win.length || wall_hallway_depth ,wall_hallway_depth + min_inner_tol);
                if (win.room_names && !compareNames(win.room_names, door_room_names) && win.room_names.length > 1) {
                    inner_hh = wall_hallway_depth / 2 + min_inner_tol;
                }
                if (edge.length < MaxSingleDoorLength) // 一般是单开门
                {
                    let rect = new ZRect(edge.length, inner_hh);
                    rect.nor = edge.nor.clone().negate();
                    rect.back_center = edge.unprojectEdge2d({ x: edge.length / 2, y: min_inner_tol });
                    rect.updateRect();
                    rect.reOrderByOrientation(true);
                    infront_door_rects.push(rect);
                }
            }
        });

        let angle_rects: ZRect[] = [];


        for (let edge of w1_poly.edges) {
            if (edge.prev_edge && edge.prev_edge.checkSameNormal(edge.dv, false, 0.1)) // 阳角
            {
                if (edge.prev_edge.length > wall_hallway_depth && edge.length > wall_hallway_depth) {
                    let rect = new ZRect(cabinet_depth, cabinet_depth);
                    rect.nor = edge.prev_edge.nor;
                    rect.rect_center = edge.prev_edge.unprojectEdge2d({ x: edge.prev_edge.length + rect.w / 2, y: -rect.w / 2 });

                    let r_center = rect.rect_center;
                    rect._w += min_inner_tol;
                    rect._h += min_inner_tol;

                    rect.rect_center = r_center;

                    angle_rects.push(rect);
                }
            }

            if (WPolygon._checkConvexWallEdge(edge) && edge.length < 250) // 孤立墙
            {
                let rect = new ZRect(edge.length, wall_hallway_depth);
                rect.back_center = edge.center;
                rect.nor = edge.nor.negate();
                rect.updateRect();
                angle_rects.push(rect);
            }
        }

        // 过道矩形
        let split_rects: ZRect[] = [];
        hallway_rects.forEach((hallway_rect) => {
            if (hallway_rect.w > min_inner_tol) {
                let side_edges = [hallway_rect.leftEdge, hallway_rect.rightEdge];


                side_edges = side_edges.filter((s_edge) => {
                    let layon_sum = 0.;
                    w1_poly.edges.forEach((edge) => {
                        let layon_edge = edge.computeLayOnEdge(s_edge, 10, 0.01);
                        if (layon_edge) {
                            layon_sum += layon_edge.length;
                        }
                    });
                    if (layon_sum > s_edge.length * 0.8) return false;

                    return true;
                })

                side_edges.forEach((s_edge) => {
                    let rect = new ZRect(s_edge.length, wall_hallway_depth / 2);
                    rect.nor = s_edge.nor;
                    rect.back_center = s_edge.center;
                    rect.updateRect();
                    split_rects.push(rect);

                    // if(hallway_rect.w > wall_hallway_depth)
                    {
                        let s_rect = rect.clone();
                        s_rect._w = wall_hallway_depth / 2;
                        s_rect._h = wall_hallway_depth * 1.5;
                        s_rect.back_center = s_edge.center;
                        s_rect.updateRect();
                        split_rects.push(s_rect);
                    }
                })

            }

        })



        let sub_polys: ZPolygon[] = [...hallway_rects, ...infront_door_rects, ...angle_rects, ...split_rects];

        let cutted_polys = w_poly.substract_polygons(sub_polys);




        return cutted_polys;



    }


    static _computeHallwayRects(w_poly: ZPolygon, wall_hallway_depth: number = 900, cabinet_depth: number = 450) {
        let hall_dist = wall_hallway_depth + cabinet_depth + 0.5;
        let candidate_rects: ZRect[] = [];
        // 先去掉一些墙洞区
        let dot_tol = 0.1;
        let not_check_edges: ZEdge[] = [];
        w_poly.edges.forEach((edge) => {
            // 检测
            let win = WPolygon.getWindowOnEdge(edge);
            if (win) return;
            if (WPolygon._checkConvexWallEdge(edge) && edge.next_edge.nor.dot(edge.dv) > 1. - dot_tol
                && Math.max(edge.next_edge.length, edge.prev_edge.length) < wall_hallway_depth * 0.67
                && edge.length >= 300 && edge.length < wall_hallway_depth * 2.5) {
                not_check_edges.push(edge.next_edge, edge.prev_edge);



            }
        })


        w_poly.edges.forEach((edge0, i) => {
            w_poly.edges.forEach((edge1, j) => {
                if (i >= j) return;
                if (not_check_edges.includes(edge0) && not_check_edges.includes(edge1)) return;

                if (!edge0.checkSameNormal(edge1.nor.clone().negate(), false, 0.1)) return;
                let layon_edge = edge0.computeLayOnEdge(edge1, hall_dist, 0.01);
                if (layon_edge) {
                    let hh = -layon_edge.projectEdge2d(edge0.center).y;

                    if (hh < 0) return;
                    let rect = new ZRect(layon_edge.length, hh);
                    rect.nor = edge1.nor.clone().negate();
                    rect.back_center = layon_edge.center;

                    rect.updateRect();
                    rect.reOrderByOrientation(true);
                    candidate_rects.push(rect);
                }
            });
        })
        return candidate_rects;
    }

    static _findClosestRectLayonEdge(edge: ZEdge, rects: ZRect[], layon_dist: number = 100, layon_radio: number = 0.1) {
        let target_wardrobe_rect: ZRect = null;

        rects.forEach((wardrobe_rect) => {
            if (wardrobe_rect.backEdge.islayOn(edge, layon_dist, layon_radio)) {
                if (!target_wardrobe_rect || wardrobe_rect.w > target_wardrobe_rect.w) {
                    target_wardrobe_rect = wardrobe_rect;
                }
            }
        });

        return target_wardrobe_rect;
    }

    static _computeInnerGridRects(w_poly: ZPolygon, door_rects: ZRect[] = null, grid_length: number = 800, max_row_grid_num: number = 3) {
        let main_rect = ZRect.computeMainRect(w_poly);

        let inner_max_rect = TRoomShape.computeMaxRectBySplitShape(w_poly);


        let farest_edge: ZEdge = null;
        let min_dist = 0;
        inner_max_rect.edges.forEach((edge) => {
            let v0 = edge.v0.pos;
            let v1 = edge.v1.pos;
            let dist = 0;
            door_rects.forEach((rect) => {
                dist = Math.max(Math.min(rect.distanceToPoint(v0), rect.distanceToPoint(v1)), dist);
            });
            if (!farest_edge || dist > min_dist) {
                farest_edge = edge;
                min_dist = dist;
            }
        })
        if (farest_edge) // 最远的边
        {
            let positions = inner_max_rect.positions;
            let nor = farest_edge.nor.clone();
            let dv = farest_edge.next_edge.nor.clone().negate();
            inner_max_rect = ZRect.fromPoints(positions, nor);
            inner_max_rect.u_dv = dv;
            inner_max_rect.updateRect();

            main_rect = ZRect.fromPoints(main_rect.positions, nor);
            main_rect.u_dv = dv;
            main_rect.updateRect();
        }


        let iw_num = Math.max(Math.floor(main_rect.w / grid_length + 0.25), 1);
        let ih_num = Math.max(Math.floor(main_rect.h / grid_length + 0.25), 1);

        let w_grid_len = main_rect.w / iw_num;
        let h_grid_len = main_rect.h / ih_num;

        let w_num = Math.floor(main_rect.w / w_grid_len + 0.01);
        let h_num = Math.floor(main_rect.h / h_grid_len + 0.01);
        let grid_rects: ZRect[] = [];

        let t_w_grid_len = grid_length;
        let t_h_grid_len = grid_length;

        if (w_num > max_row_grid_num) {
            w_num = max_row_grid_num;
            t_w_grid_len = main_rect.w / w_num;
        }
        if (h_num > max_row_grid_num) {
            h_num = max_row_grid_num;
            t_h_grid_len = main_rect.h / h_num;
        }


        const getDictKey = (i: number, j: number) => {
            return i + "_" + j;
        }
        for (let i = 0; i <= w_num; i++) {
            for (let j = 0; j <= h_num; j++) {
                let rect = new ZRect(t_w_grid_len, t_h_grid_len);
                rect.nor = main_rect.nor;
                rect.rect_center = main_rect.unproject({ x: -main_rect.w / 2 + (i + 0.5) * t_w_grid_len, y: -main_rect.h / 2 + (j + 0.5) * t_h_grid_len });
                rect._attached_elements[GridPosId] = { x: i, y: j };

                let int_polys = rect.intersect_polygons([w_poly]);
                if (int_polys && int_polys[0]) {
                    let t_rect = ZRect.fromPoints(int_polys[0].positions, rect.nor);
                    rect.copy(t_rect);
                }
                else {
                    continue;
                }
                door_rects.forEach((door_rect) => {
                    let t_door_rect = door_rect.clone();
                    let t_rect_center = t_door_rect.rect_center;
                    t_door_rect._w = Math.min(t_door_rect.w, 900);
                    t_door_rect._h += 900 * 2;
                    t_door_rect.rect_center = t_rect_center;


                    let sub_polys = rect.substract_polygons([t_door_rect]);
                    if (sub_polys[0]) {
                        let t_rect = ZRect.fromPoints(sub_polys[0].positions, rect.nor);
                        rect.copy(t_rect);
                    }
                })

                if (rect.min_hh < grid_length * 0.6) {
                    continue;
                }
                grid_rects.push(rect);
                rect._attached_elements[GridNeighborRects] = [];
            }
        }


        grid_rects.forEach((rect) => {
            let overlap_length = 0;
            let res = rect.comparePolyDistance(w_poly, grid_length * 0.6, { save_overlap_edges: true });
            overlap_length += res.overlap_length;
            rect._attached_elements[GridWallOverlapLength] = overlap_length;
            if (overlap_length >= rect.min_hh * 0.8) {
                rect._attached_elements[GridIsAlignWall] = true;
                rect._attached_elements[GridAlignWallEdges] = res.overlap_edges || [];


                let positions = rect.positions;
                res.overlap_edges.forEach((edge) => {
                    if (edge.length < rect.min_hh * 0.5) {
                        return;
                    }
                    positions.push(edge.v0.pos);
                    positions.push(edge.v1.pos);
                });

                let t_rect = ZRect.fromPoints(positions, rect.nor);
                rect.copy(t_rect);
                rect.updateRect();
            }
        });
        if (door_rects) {
            grid_rects.forEach((rect) => {
                let overlap_length = 0;
                let distToDoor = 1e100;

                let is_valid = true;
                let ww = rect.w;
                let hh = rect.h;
                door_rects.forEach(door_rect => {
                    let res = door_rect.comparePolyDistance(rect, door_rect.h / 4, { save_overlap_edges: true });
                    if (res.overlap_edges) {
                        res.overlap_edges.forEach((edge) => {
                            if (edge.checkSameNormal(rect.nor)) {
                                ww -= edge.length;
                            }
                            else {
                                hh -= edge.length;
                            }
                        })
                    }

                    let dist = door_rect.rect_center.distanceTo(rect.rect_center);
                    distToDoor = Math.min(dist, distToDoor);
                });

                if (ww < rect.w - 600 || hh < rect.h - 600) {
                    rect._attached_elements[GridIsInfrontDoor] = true;
                }
                rect._attached_elements[GridDistanceToDoor] = distToDoor;
            })
        }
        // 将不靠门的矩形取出来
        let grid_dicts: { [key: string]: ZRect } = {};

        grid_rects.forEach((rect) => {
            // if(!rect._attached_elements[GridIsAlignWall]) return;
            if (rect._attached_elements[GridIsInfrontDoor]) return;

            let posId = rect._attached_elements[GridPosId];
            let key = getDictKey(posId.x, posId.y);
            grid_dicts[key] = rect;

        })

        let nx = [1, 0, -1, 0];
        let ny = [0, 1, 0, -1];
        let result_rects: ZRect[] = [];
        for (let key in grid_dicts) {
            let s_grid = grid_dicts[key];
            let posId = s_grid._attached_elements[GridPosId];
            let i = posId.x;
            let j = posId.y;
            for (let k = 0; k < 4; k++) {
                let n_grid0 = grid_dicts[getDictKey(i + nx[k], j + ny[k])];
                if (n_grid0) {
                    s_grid._attached_elements[GridNeighborRects].push(n_grid0);
                }
            }
            result_rects.push(s_grid);
        }

        result_rects.sort((a, b) => (b._attached_elements[GridDistanceToDoor] || 0) - (a._attached_elements[GridDistanceToDoor] || 0));

        return result_rects;
    }

    static _getGridRectProps(rect: ZRect) {
        return {
            pos_id: rect._attached_elements[GridPosId] as { x: number, y: number },
            distanceToDoor: rect._attached_elements[GridDistanceToDoor] as number,
            wallOverlapLength: rect._attached_elements[GridWallOverlapLength] as number,
            neighbor_grids: rect._attached_elements[GridNeighborRects] as ZRect[],
            isAlignWall: (rect._attached_elements[GridIsAlignWall] || false) as boolean,
            IsInfrontDoor: (rect._attached_elements[GridIsAlignWall] || false) as boolean,
            align_wall_edges: (rect._attached_elements[GridAlignWallEdges] || []) as ZEdge[]
        }
    }

    /**
     * 
     * @param rect 
     * @param target_length
     * @param options 
     *    side: 1 0 or -1: 如果有设置, 则优先使用
     *    如果没有设置side:
     *       1.1  如果有贴在aligned_poly的边上, 那么以所贴的边为主
     *       1.2  如果没有贴边, 或者贴在两边上，则看target_rect偏左 还是 偏右来确定side
     *    如果有aligned_poly, 则要计算能延长的最长距离, 目标长度不能超过它
     */
    static resetLengthWithSide(rect:ZRect, target_length:number, options:{side?:number, target_rect?:ZRect, aligned_poly?:ZPolygon,algined_tol?:number}={})
    {
        if(options.side === undefined)
        {
            let candidate_sides : number[] = [];

            if(options.aligned_poly)
            {
                let aligend_tol = options.algined_tol || 10;
                options.aligned_poly.edges.forEach((edge)=>{
                    if(edge.islayOn(rect.leftEdge,aligend_tol,0.5))
                    {
                        candidate_sides.push(-1);
                    }
                    if(edge.islayOn(rect.rightEdge,aligend_tol,0.5))
                    {
                        candidate_sides.push(1);
                    }
                });
            }
            if(candidate_sides.length !== 1)
            {
                if(options.target_rect)
                {
                    options.side =  rect.project(options.target_rect.rect_center).x > 0?1:-1;
                }
            }
            else{
                options.side = candidate_sides[0];
            }
        }
        let side = options.side || 0;
        let r_center = rect.unproject({x:(rect.w-target_length)/2 * side,y:0});
        rect._w = target_length;
        rect.rect_center = r_center;
        rect.updateRect();
    }

}