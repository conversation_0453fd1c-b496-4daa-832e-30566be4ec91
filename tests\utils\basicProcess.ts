import { expect, Page } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';

/**
 * 该函数用于点击顶部菜单栏的“文件”选项、再点击对应的item按钮
 * @param page Playwright的原生page对象
 * @param designPage
 * @param item 需要点击的菜单项的名称
 */
export async function clickFileItem(page: Page, designPage: DesignPage, item: string) {
  // 1. 点击顶部菜单栏的“文件”按钮
  await designPage.safeClickTopMenuFileItem();

  // 等待菜单项可见
  const menuItemSelector = `div.swj-dropdown-menu-label-inner > span > span:has-text("${item}")`;
  await page.waitForSelector(menuItemSelector, { state: 'visible', timeout: 5000 });

  // 2. 点击“item”菜单项
  const mySchemeMenuItem = page.locator(menuItemSelector);
  // await mySchemeMenuItem.hover();
  await designPage.safeClick(mySchemeMenuItem);
  console.info(`点击了"${item}"按钮`);

  // 等待网络稳定（替代 networkidle）
  await page.waitForLoadState('domcontentloaded');
}

/**
 * 该函数用于判断是否进入了AI布局
 * @param page Playwright的原生page对象
 */
export async function ifEnterAILayout(page: Page) {
  // 判定“成功进入ai布局打开户型，自动推荐全局布局”
  // 获取“全屋方案”的数量，若不少于1个方案判定为成功
  const firstScheme = page.locator('#side_list_div > div').first();
  await expect(firstScheme).toBeVisible({
    visible: true, // 严格检查物理可见性
    timeout: 30000
  });
}

/**
 * 该函数封装了多个操作：搜索指定id的户型并“开始设计”，进入AI布局
 * @param page Playwright的原生page对象
 * @param designPage
 * @param schemeId （可选）户型id，默认是“1ILLHIIKJFLCDDJGKC”
 */
export async function searchAndOpen(
  page: Page,
  designPage: DesignPage,
  schemeId: string = '1ILLHIIKJFLCDDJGKC'
) {
  // 1. 点击顶部菜单栏——》“文件”——》“搜索户型库”
  await clickFileItem(page, designPage, '搜索户型库');

  // 2.输入户型id（  默认为：1ILLHIIKJFLCDDJGKC  ），点击搜户型
  // 先获取iframe实例
  const frameElement = page.frameLocator('#searchIframe');
  await frameElement.locator('body').waitFor({ state: 'attached' }); // 确保iframe加载

  const houseSearch = frameElement.locator('#App > div > div.content > div.houseSearch');
  await houseSearch.locator('input').fill(schemeId);
  await houseSearch.locator('div.search-btn').click();

  // 3.选择户型，点击开始设计
  // 点击搜索后得到的户型结果卡片
  const content = frameElement.locator('#App > div > div.content');
  const houseCard = content.locator('.house-card').first();
  // 双重等待：元素可见 + 可点击
  await houseCard.waitFor({ state: 'visible' });
  await houseCard.click();

  // 点击“开始设计”
  frameElement.getByRole('dialog').getByText('开始设计', { exact: true }).click();

  await ifEnterAILayout(page);
}

/**
 * 该函数用于在首页，打开任意户型
 * @param page Playwright的原生page对象
 * @param designPage
 */
export async function openAnyScheme(page: Page, designPage: DesignPage) {
  // 1.点击首页中的搜索户型库，在“最新户型图”中打开任意户型
  await clickFileItem(page, designPage, '搜索户型库');

  // 先获取iframe元素
  const frameElement = page.frameLocator('#searchIframe');
  // 点击“最新户型”的第一到第四个的随机一个
  let random_1to4 = Math.floor(Math.random() * 4) + 1;
  const houseCard = frameElement.locator(
    `div.content > div.new-building > div.buildings > div:nth-child(${random_1to4})`
  );
  await houseCard.waitFor({ state: 'visible', timeout: 5000 });
  await page.waitForTimeout(500); // 等待数据加载
  await houseCard.click();
  console.log(`成功点击一级菜单的第 ${random_1to4} 个户型`);

  try {
    // 注意有个二级搜索页面，点击第一到第四个的随机一个
    const random_1to4 = Math.floor(Math.random() * 4) + 1;
    const houseCard01 = frameElement.locator(
      `#scrollWrap > div > div > div > div:nth-child(${random_1to4})`
    );
    await houseCard01.waitFor({ state: 'visible', timeout: 5000 });
    await page.waitForTimeout(500); // 等待数据加载
    await houseCard01.click();
    console.log(`成功点击二级菜单的第 ${random_1to4} 个户型`);
  } catch (error) {
    // 捕获超时或其他错误
    throw new Error(`操作超时或数据获取失败：${error.message}`);
  }

  // 点击“开始设计”
  const dialog = frameElement.getByRole('dialog');
  await dialog.waitFor({ state: 'visible', timeout: 5000 });
  const startDesignButton = dialog.getByText('开始设计', { exact: true });
  await startDesignButton.waitFor({ state: 'visible', timeout: 5000 });
  await startDesignButton.click();

  // 判定是否进入AI布局
  await ifEnterAILayout(page);
}

// /**
//  * 该函数进行了测试用例中常见的三步操作：
//  * 1.点击首页中的户型库，打开任意户型 —— openAnyScheme()
//  * 2.点击下一步进入套系匹配
//  * 3.选择平台第一个套系点击全部应用
//  * @param page
//  * @param designPage
//  */
// export async function stepOneToThree(page: Page, designPage: DesignPage) {
//   // 1、打开任意户型
//   await openAnyScheme(page, designPage);
//   // #body_container > div.canvas_btns > button.ant-btn.css-dev-only-do-not-override-ppub5t.ant-btn-primary.ant-btn-color-primary.ant-btn-variant-solid.btn > span

//   // 2、点击“下一步”，进入套系匹配
//   const nextButton_span = page.locator('#body_container > div.canvas_btns').getByText('下一步');
//   await nextButton_span.waitFor({ state: 'visible', timeout: 10000 });
//   await nextButton_span.click();

//   // 3、点击左侧tab栏中的“平台”
//   const platformButton_span = page
//     .locator('div.ant-radio-group.ant-radio-group-outline > label')
//     .getByText('平台');
//   await platformButton_span.waitFor({ state: 'visible', timeout: 10000 });
//   await platformButton_span.click();

//   // 4、选择第一个套系
//   // 注意：鼠标进入容纳单独一个套系的盒子中（img），才回显示出“全部应用”按钮
//   // 获取img的dom
//   const first_TaoXi_Img = page.locator(
//     'div.ant-spin-nested-loading > div > div > div:nth-child(1) > div > img'
//   );
//   expect(first_TaoXi_Img).toBeVisible({ timeout: 10000 });

//   // 计算img中心坐标
//   const imgBoundingBox = await first_TaoXi_Img.boundingBox();
//   const centerX = imgBoundingBox!.x + imgBoundingBox!.width / 2;
//   const centerY = imgBoundingBox!.y + imgBoundingBox!.height / 2;
//   // 点击
//   await page.mouse.move(centerX, centerY, {
//     steps: 5 // 添加移动步骤使动作更自然
//   });
//   await page.waitForTimeout(300); // 悬停一定时间，等待隐藏的“全部应用”按钮出现
//   // 点击中心点，即“全部应用”按钮
//   await page.mouse.click(centerX, centerY, {
//     delay: 50, // 添加50ms按下到释放的间隔
//     button: 'left' // 明确指定左键点击
//   });
//   // TODO 优化：等待套系加载成功的逻辑
//   await page.waitForTimeout(5000);
// }

/**
 * 该函数用于测试“保存”功能，
 * 生成了一个用于校验是否保存成功的随机id作为名称进行保存、在“我的方案”中校验
 * @param page
 * @param designPage
 */
export async function clickToSave(page: Page, designPage: DesignPage) {
  await designPage.clickTopMenuAppointedItem('保存');
  // 填充一个方案名称，用于后续校验是否保存成功
  const test_example_id = Math.floor(Math.random() * 10000000);
  await page.locator('#basic_schemename').fill(`测试保存功能用例-${test_example_id}`);

  const saveButton = page.locator('#basic').locator('button').getByText('保 存');
  await saveButton.waitFor({ state: 'visible', timeout: 5000 });
  await saveButton.click();

  // 捕获保存成功的轻提示
  try {
    const savedToast = page.getByText('布局方案保存成功');
    await savedToast.waitFor({ state: 'visible', timeout: 6000 });
  } catch (err) {
    throw new Error('没有捕获到布局方案保存成功的轻提示');
    // console.info('没有捕获到成功的轻提示!');
  }

  // 打开方案列表（顶部菜单“文件”-“我的方案”）显示出已经保存的方案
  // await designPage.clickTopMenuAppointedItem('文件', '我的方案');

  // try {
  //   const justSavedExample = page.getByText(`测试保存功能用例-${test_example_id}`);
  //   await justSavedExample.waitFor({ state: 'visible', timeout: 5000 });
  // } catch (err) {
  //   throw new Error('在“我的方案”中没有找到先前保存的示例');
  // }

  // async function tryCloseWithButton(name, locator) {
  //   try {
  //     await locator.waitFor({ state: 'visible', timeout: 5000 });
  //     await locator.click();
  //     console.info(`使用${name}关闭成功`);
  //     return true;
  //   } catch (err) {
  //     console.info(`尝试${name}关闭失败: ${err.message}`);
  //     return false;
  //   }
  // }

  // const closeButtons = [
  //   {
  //     name: '“布局”界面的关闭按钮',
  //     locator: page.getByRole('dialog').locator('[aria-label="Close"]')
  //   },
  //   {
  //     name: '“套系”界面的关闭按钮',
  //     locator: page.locator('div.swj-baseComponent-Containersbox-title').locator('i')
  //   }
  // ];

  // // 并发执行所有关闭尝试
  // const closeAttempts = closeButtons.map(({ name, locator }) => tryCloseWithButton(name, locator));

  // // 使用Promise.race等待第一个成功的尝试
  // try {
  //   // 只要有一个成功就返回
  //   await Promise.race(closeAttempts);
  // } catch {
  //   throw new Error('所有关闭方式都尝试失败');
  // }
}

export async function matchSets(page: Page, designPage: DesignPage){
  // 1.选中户型
    searchAndOpen(page, designPage);

    // 2.应用布局：选择左侧布局列表中的第二个全屋方案
    const secondScheme = page.locator('#side_list_div > div:nth-child(2)');
    await secondScheme.waitFor({ state: 'visible' });
    await secondScheme.click();

    const nextStep = await page.locator("button > span:has-text('下一步')");
    await designPage.safeClick(nextStep);
    console.info("点击'下一步'按钮");

    const platformTab = await page.locator("span:has-text('平台')");
    await designPage.safeClick(platformTab);
    console.info("点击左侧面板的平台标签页");

    // 定位并悬停在第一个系列图片上
    const firstSeriesImage = await page.locator('img[alt*="series-"]').first();
    await firstSeriesImage.hover();
    console.info("选中点击左侧面板第一个套系");

    const applyButton = await page.locator("button:has-text('全部应用')");
    await designPage.safeClick(applyButton);
    console.info("点击'全部应用'按钮");
    await page.waitForTimeout(5000);
}