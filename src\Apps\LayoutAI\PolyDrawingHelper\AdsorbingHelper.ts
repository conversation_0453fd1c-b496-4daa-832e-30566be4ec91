import { Vector3, Vector3<PERSON><PERSON> } from "three";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZPainter } from "@layoutai/z_polygon";
export enum AdsorbingType
{
    None = 0,
    OnPoint = 1,
    OnEdge = 2,
    OnXAxis = 4,
    OnYAxis = 8,
    All = 255
}
export interface I_ExsorbPoint{pos:Vector3,nor?:Vector3, edge?:ZEdge, angle?:number,label?:string, [key:string]:any}
export interface I_CandidateExsorbPoint extends I_ExsorbPoint{
    adsorb_pos:Vector3,
    distance : number,
    weight ?: number,
    adsorbType ?: number
}

/**
 *   吸附助手
 */
export class AdsorbingHelper 
{
    protected _exsorb_vertices :I_ExsorbPoint[];
    protected _candidate_vertices : I_CandidateExsorbPoint[];
    
    protected _adsorb_tol: number = 100;

    protected _adsorbing_updated_distance : number = 50;
    private _candidate_stroke_style: string = "#fe2b2b";

    constructor()
    {
        this.clean();
    }

    public get adsorb_tol(): number {
        return this._adsorb_tol;
    }
    public set adsorb_tol(value: number) {
        this._adsorb_tol = value;
    }

    public get candidate_stroke_style(): string {
        return this._candidate_stroke_style;
    }
    public set candidate_stroke_style(value: string) {
        this._candidate_stroke_style = value;
    }
    clean()
    {
        this._exsorb_vertices = [];
        this._candidate_vertices =[];
    }
    initExsorbPoints(input:{fromRects?:ZRect[],fromPolygons?:ZPolygon[],fromPoints?:Vector3[]})
    {
        this._exsorb_vertices.length = 0;
        if(input.fromPolygons)
        {
            input.fromPolygons.forEach((poly)=>{
                if(poly)
                {
                    poly.edges.forEach((edge)=>{
                        let t_data:I_ExsorbPoint = {
                            pos : edge.v0.pos,
                            edge : edge,
                            nor : edge.nor,
                            angle : Math.round(Math.atan2(edge._nor.x, -edge._nor.y) /Math.PI * 180 / 20) * 20
                        }
                        this._exsorb_vertices.push(t_data);
                    })
                }
            })
        }
        // console.log(this._exsorb_vertices.length);
    }
     updateExsorbCandidates(pos:Vector3Like,adsorb_type:number = AdsorbingType.All,exsorb_vertices:I_ExsorbPoint[]=null,adsorb_tol:number=-1)
    {
        this._candidate_vertices = [];
        const dot_tol = 1e-1;
        exsorb_vertices = exsorb_vertices || this._exsorb_vertices;
        adsorb_tol = adsorb_tol > 0 ? adsorb_tol : this._adsorb_tol;
        if(adsorb_type & AdsorbingType.OnPoint)
        {
            exsorb_vertices.filter((data)=>{
                return data.pos.distanceTo(pos) < this._adsorb_tol;
            }).forEach((data)=>{
                this._candidate_vertices.push({pos:data.pos,adsorb_pos:data.pos,adsorbType:AdsorbingType.OnPoint,distance:data.pos.distanceTo(pos)});
            });
        }
        if(adsorb_type & AdsorbingType.OnEdge)
        {
            exsorb_vertices.forEach((data)=>{
                if(!data.edge) return;
                let tp = data.edge.projectEdge2d(pos);
                if(Math.abs(tp.y) < this._adsorb_tol)
                {
                    this._candidate_vertices.push({pos:data.pos,edge:data.edge, adsorb_pos:data.edge.unprojectEdge2d({x:tp.x,y:0}),distance:Math.abs(tp.y)});
                }                
            })
        }
        if(adsorb_type & AdsorbingType.OnXAxis)
        {
            exsorb_vertices.forEach((data)=>{
                let df = Math.abs(data.pos.x - pos.x);
                if(df < this._adsorb_tol)
                {
                    this._candidate_vertices.push({pos:data.pos, adsorb_pos:data.pos,distance:df,adsorbType:AdsorbingType.OnXAxis});
                }                
            })
        }
        if(adsorb_type & AdsorbingType.OnYAxis)
        {
            exsorb_vertices.forEach((data)=>{
                let df = Math.abs(data.pos.y - pos.y);
                if(df < this._adsorb_tol)
                {
                    this._candidate_vertices.push({pos:data.pos, adsorb_pos:data.pos,distance:df,adsorbType:AdsorbingType.OnYAxis});
                }                
            })
        }
        this._candidate_vertices.sort((a,b)=>a.distance - b.distance);
    }

    _getExsorbPos(pos:Vector3Like,adsorb_type:number = AdsorbingType.All)
    {
        if(!adsorb_type)
        {
            return  new Vector3().copy(pos);
        }
        if(this._candidate_vertices.length > 0)
        {
            return this._candidate_vertices[0].adsorb_pos.clone();
        }
        return new Vector3().copy(pos);

    }

    drawCanvas(painter:ZPainter)
    {
        if(this._candidate_vertices)
        {
            painter.strokeStyle = this.candidate_stroke_style;
            this._candidate_vertices.forEach((p)=>{
                painter.drawPointCircle(p.pos,30,5);
            })
        }
    }
}