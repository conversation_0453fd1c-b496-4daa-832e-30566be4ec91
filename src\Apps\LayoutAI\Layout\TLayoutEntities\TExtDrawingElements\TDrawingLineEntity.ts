import { Vector3, Vector3<PERSON><PERSON> } from "three";
import { TExtDrawingEntity } from "./TExtDrawingEntity";
import { ZPolyline } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { TPainter } from "../../../Drawing/TPainter";
import { I_EntityDrawingState } from "../TBaseEntity";
import { I_SwjExtDrawingData } from "../../../AICadData/SwjLayoutData";
import { Vec3toMeta } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { IRoomEntityRealType, IType2UITypeDict } from "../../IRoomInterface";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";


export interface I_ViewLineOptions {
    moving_speed?: number;
    angle_speed?: number;
    update_view_dir?: boolean;
    angle_radius?: number;
    circulate?: boolean;

}
export class TDrawingLineEntity extends TExtDrawingEntity {
    _points: Vector3[];

    _polyline: ZPolyline;




    /**
     *  视觉矩形
     */
    _view_rect: ZRect;


    /**
     *  视觉曲线
     */
    _view_curve: ZPolyline;

    /**
     *   总长度
     */
    _total_length: number;

    _animation_angle_radius: number;




    constructor() {
        super();
        this._points = [];

        this._polyline = new ZPolyline();

        this._view_rect = new ZRect(100, 100);

        this._total_length = 0;

        this._animation_angle_radius = 50;
        this.realType = "MovingLine";

        this.name = "动线";
    }

    importData(data: I_SwjExtDrawingData): void {
        if (data.points) {
            this._points = [];
            this._points = data.points.map((p) => new Vector3().copy(p));
        }
        this.update();
    }

    exportData(): I_SwjExtDrawingData {
        let data = super.exportData() as I_SwjExtDrawingData;
        data.points = this._points.map((p) => Vec3toMeta(p));
        return data;
    }
    getRealTypeOptions(): { label: string; value: string; }[] {
        let values: IRoomEntityRealType[] = [];

        values = ["MovingLine", "LightStrip"];

        let ans: { label: string, value: string }[] = [];

        for (let val of values) {
            ans.push({ label: LayoutAI_App.t(IType2UITypeDict[val]) || LayoutAI_App.t(val), value: IType2UITypeDict[val] || val });
        }
        return ans;
    }



    getPointdistanceToBoundary(point: Vector3Like) {
        if (this._polyline) {
            return this._polyline.distanceToPoint(point);
        }
        else {
            return 1000000;
        }
    }
    protected getCandidateNames() {
        return [
            { label: LayoutAI_App.t("访客动线"), value: "访客动线" },
            { label: LayoutAI_App.t("居家动线"), value: "居家动线" },
            { label: LayoutAI_App.t("洗漱动线"), value: "洗漱动线" },
            { label: LayoutAI_App.t("家务动线"), value: "家务动线" },


        ];
    }
    get maxHeight() {
        return 0;
    }
    getViewEdgeLength(edge: ZEdge, angle_radius: number = 50) {
        let length = edge.length;

        let angle = 0;
        if (edge.next_edge) {
            let dot_val = edge.dv.dot(edge.next_edge.dv);
            angle = Math.PI / 2 - Math.acos(dot_val);
        }
        else {
            angle = Math.PI;
        }
        let angle_length = angle_radius * angle;

        return length + angle_length;
    }

    getViewEdgePointData(edge: ZEdge, t: number, angle_radius: number = 50) {
        let view_length = this.getViewEdgeLength(edge, angle_radius);
        let length = edge.length;
        let angle_length = view_length - length;

        let pos = edge.v1.pos.clone();
        let dv = edge.dv.clone();
        if (t < length) {
            pos = edge.unprojectEdge2d({ x: t, y: 0 });
        }




        return { pos: pos, dv: dv }
    }

    updateViewRect(t: number, options: I_ViewLineOptions = {}) {

        if (!this._view_curve || this._view_curve.vertices.length < 2) return t;

        let total_length = 0;

        let angle_radius = options.angle_radius || this._animation_angle_radius;


        let target_pos = this._view_curve.edges[0].v0.pos;
        let target_nor = this._view_curve.edges[0].dv.clone();
        if (this._view_curve) {
            total_length = 0;
            for (let edge of this._view_curve.edges) {
                let view_length = this.getViewEdgeLength(edge, angle_radius);
                total_length += view_length;
            }
            total_length = Math.max(total_length, 0.00001);
            if (options.circulate) {
                if (t > total_length) {


                    t -= total_length;

                }
                else if (t < 0) {
                    t += total_length;
                }

            }

            let c_t = 0.;
            for (let edge of this._view_curve.edges) {
                let cview_length = this.getViewEdgeLength(edge, angle_radius);
                let n_t = c_t + cview_length;
                if (c_t - 0.01 <= t) {
                    let s_t = t - c_t;

                    let data = this.getViewEdgePointData(edge, s_t, angle_radius)
                    target_pos = data.pos.clone();
                    target_nor = data.dv.clone();
                    if (n_t > t) {
                        break;
                    }
                }

                c_t = n_t;


            }

        }

        if (options.update_view_dir === true) {
            this._view_rect.nor = target_nor;

        }

        this._view_rect.rect_center = target_pos;
        return t;

    }

    update(): void {
        this._polyline.initByVertices(this._points);
        if (this._points.length > 0) {
            if (this._points.length > 1) {
                let dv = this._points[1].clone().sub(this._points[0]).normalize();
                this._rect.nor.copy(dv);
            }
            this._rect.rect_center = this._points[0];
        }

        if (this._polyline) {
            this._total_length = 0;


            this._polyline.edges.forEach(edge => {
                if (edge.next_edge) {
                    let radius = (Math.min(edge.length, edge.next_edge.length, 200)) / 2;
                    this._polyline.makeArcAt(edge, radius);
                }
            });

            let view_points: Vector3[] = [];

            this._polyline.edges.forEach(edge => view_points.push(...edge.getEdgePoints(50)));

            if (!this._view_curve) {
                this._view_curve = new ZPolyline();
            }
            this._view_curve.initByVertices(view_points);

            this._total_length = 0;

            this._view_curve.edges.forEach((edge) => this._total_length += edge.length);
        }
        super.update();
    }
    addPoint(pos: Vector3Like) {
        this._points.push(new Vector3().copy(pos));
        this.update();
    }

    editBackPoint(pos: Vector3Like, align_axis: boolean = true) {
        if (this._points.length > 0) {
            let t_pos = new Vector3().copy(pos);

            let back_point = this._points[this._points.length - 1];


            if (align_axis) {
                let prev_point = this._points[this._points.length - 2];
                if (prev_point) {
                    let t_offset = t_pos.clone().sub(prev_point);

                    let pp_point = this._points[this._points.length - 3];

                    // if(pp_point)
                    // {
                    //     let dv = prev_point.clone().sub(pp_point).normalize();


                    // }
                    // else{

                    // }

                    if (Math.abs(t_offset.x) > Math.abs(t_offset.y)) {
                        t_offset.y = 0;
                    }
                    else {
                        t_offset.x = 0;
                    }
                    t_pos = prev_point.clone().add(t_offset);
                }


            }
            back_point.copy(t_pos);
        }
        this.update();
    }

    popBack() {
        if (this._points.length > 0) {
            this._points.length = this._points.length - 1;
        }
        this.update();
    }
    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (this._points.length > 0) {
            let t_poly = this._polyline;

            painter._context.lineWidth = 2;
            painter.strokeStyle = this.color_style;
            painter.drawEdges(this._polyline.edges, 1);

            painter._context.beginPath();
            painter._context.setLineDash([]);
            t_poly.edges.forEach(edge => {
                let center = edge.center;

                let pos0 = edge.unprojectEdge2d({ x: edge.length / 2 - 100, y: -70 });
                let pos1 = edge.unprojectEdge2d({ x: edge.length / 2 - 100, y: 70 });

                painter.drawSegment(pos0, center);
                painter.drawSegment(center, pos1);



            });
            painter._context.stroke();

        }

        if (options.is_animation) {
            if (this._view_rect) {
                painter.strokePolygons([this._view_rect]);
            }
        }

    }
}