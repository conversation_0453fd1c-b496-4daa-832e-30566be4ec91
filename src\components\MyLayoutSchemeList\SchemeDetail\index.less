@change: 900px;

.contain {
  display: flex;
  width: 100%;
}

.left {
  border-radius: 9px;
  background-color: #F4F5F5;
  display: flex;
  align-items: center;

  img {
    width: 360px;
    height: auto;
    margin-top: 0;
    // @media screen and (max-width: 1000px) {
    //   width: calc(var(--vw, 1vw) * 20);
    // }
  }
}

.right {
  display: flex;
  flex-direction: column;
  margin-left: 50px;
  flex-grow: 1;

  .name {
    color: #282828;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 24px;
    line-height: 1.33;
    letter-spacing: 0px;
    text-align: left;
    margin-top: 10px;
  }

  .schemeDetailsContainer
  {
    display: flex;
    width: 100%;
    gap: 50px;
    @media (max-width: @change) {
      display: block;
    }

    .schemeDetails {
      display: grid;
      grid-template-columns: auto 1fr;
      gap: 16px;
      margin-top: 32px;
      @media (max-width: @change) {
        margin-right: 0;
      }
    }
  }
}



.label {
  color: #282828;
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 14px;
  line-height: 1.57;
  letter-spacing: 0px;
  text-align: left;
}

.value {
  color: #5b5e60;
  font-family: PingFang SC;
  font-weight: normal;
  font-size: 14px;
  line-height: 1.57;
  letter-spacing: 0px;
  text-align: left;
}

.btnContain {
  display: flex;
  margin-top: 50px;
  margin-bottom: 10px;
  .btnDesign {
    width: 120px;
    height: 40px;
    color: #ffffff;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: 0px;
    margin-right: 16px;
  }
  .btnPPT {
    margin-left: 16px;
    width: 160px;
    height: 40px;
    color: #ffffff;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: 0px;
    border: none;
    border-radius: 8px;
    background: #52c41a;
    text-align: center;
    transition: background 0.3s; /* 背景色过渡效果 */

    &:hover {
      background: #61d825 !important; /* 悬停时背景色变亮 */
      color: white !important; /* 按钮文字颜色 */
    }
  }
}

.tabTitle {
  color: #5B5E60;
  font-family: PingFang SC;
  font-weight: normal;
  font-size: 14px;
  line-height: 1.57;
  letter-spacing: 0px;
  text-align: left;
}

.activeTab {
  color: #282828;
  font-family: PingFang SC;
  font-weight: 540;
  font-size: 14px;
  line-height: 1.57;
  letter-spacing: 0px;
  text-align: left;
}
