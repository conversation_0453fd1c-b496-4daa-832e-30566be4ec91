"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[1460],{56697:function(n,t,e){e.d(t,{A:function(){return g}});var r=e(13274),i=e(41594);function o(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function a(){var n=o(["\n        position: fixed;\n        bottom: -100%;\n        left: 0;\n        right: 0;\n        background: #fff;\n        border-top: 1px solid #ccc;\n        padding: 10px;\n        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);\n        z-index: 99;\n        transition: bottom 0.3s ease; /* 添加过渡效果 */\n\n        @media screen and (orientation: landscape) {\n            left : auto;\n            width : 420px;\n        }\n    "]);return a=function(){return n},n}function c(){var n=o(["\n         bottom: 0 !important;\n    "]);return c=function(){return n},n}function s(){var n=o(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n    "]);return s=function(){return n},n}function u(){var n=o(["\n        display: flex;\n        align-items: center;\n        width: 50%;\n        position: relative;\n        \n    "]);return u=function(){return n},n}function l(){var n=o(["\n        width: 100%;\n        height: 50px;\n        background: #eee;\n        border: none;\n        border-radius: 5px;\n        padding: 5px 30px;\n        margin-left: 5px;\n        /* margin-bottom: 10px; */\n        font-size: 20px;\n    "]);return l=function(){return n},n}function p(){var n=o(["\n        margin-left: 5px;\n        font-size: 20px;\n        position: absolute;\n        right: 10px;\n    "]);return p=function(){return n},n}function d(){var n=o(["\n        display: flex;\n        flex-wrap: wrap;\n        margin-top: 10px;\n        button {\n            flex: 1 0 30%; /* 控制按钮大小 */\n            margin: 5px;\n            padding: 15px;\n            font-size: 20px;\n            cursor: pointer;\n            background: #eee;\n            border: none;\n            border-radius: 5px;\n        }\n    "]);return d=function(){return n},n}function f(){var n=o(["\n        font-size: 30px;\n        margin-right: 10px;\n    "]);return f=function(){return n},n}function h(){var n=o(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        flex: 1 0 30%; /* 控制按钮大小 */\n        .confirm_button{\n            background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n            color: #FFFFFF;\n        }\n    "]);return h=function(){return n},n}var x=(0,e(79874).rU)((function(n){var t=n.css;return{custom_keyboard:t(a()),custom_keyboard_visible:t(c()),top_container:t(s()),top_input_container:t(u()),top_input:t(l()),unit:t(p()),keypad:t(d()),close_icon:t(f()),bottom_row:t(h())}})),m=e(76330);function v(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function b(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,i,o=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(n){c=!0,i=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw i}}return o}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return v(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return v(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var g=function(n){var t=n.onKeyPress,e=n.onDelete,o=n.onConfirm,a=n.onClose,c=n.inputValue,s=n.isVisible,u=x().styles,l=b((0,i.useState)(c),2),p=l[0],d=l[1];return(0,i.useEffect)((function(){d(c)}),[c]),(0,r.jsxs)("div",{className:"".concat(u.custom_keyboard," ").concat(s?u.custom_keyboard_visible:""),children:[(0,r.jsxs)("div",{className:u.top_container,children:[(0,r.jsxs)("div",{className:u.top_input_container,children:[(0,r.jsx)("input",{type:"text",className:u.top_input,value:p,readOnly:!0}),(0,r.jsx)("span",{className:u.unit,children:"mm"})," "]}),(0,r.jsx)(m.A,{type:"icon-a-fangxiangxia",className:u.close_icon,onClick:a})]}),(0,r.jsxs)("div",{className:u.keypad,children:[["1","2","3","4","5","6","7","8","9"].map((function(n){return(0,r.jsx)("button",{onClick:function(){return t(n)},children:n},n)})),(0,r.jsxs)("div",{className:u.bottom_row,children:[(0,r.jsx)("button",{onClick:e,children:(0,r.jsx)(m.A,{type:"icon-a-tianchongFace-1",style:{color:"#585858"}})}),(0,r.jsx)("button",{onClick:function(){return t("0")},children:"0"})," ",(0,r.jsx)("button",{className:"confirm_button",onClick:o,children:"确定"})]})]})]})}},66910:function(n,t,e){var r=e(23825),i=e(79874);function o(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function a(){var n=o(["\n      width:100%;\n      height:calc(var(--vh, 1vh) * 100);\n      .custom-keyboard {\n        position: fixed;\n        bottom: 0;\n        left: 0;\n        right: 0;\n        background: #fff;\n        border-top: 1px solid #ccc;\n        padding: 10px;\n        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);\n      }\n\n      .keypad {\n          display: flex;\n          flex-wrap: wrap;\n      }\n\n      .keypad button {\n          flex: 1 0 30%; /* 控制按钮大小 */\n          margin: 5px;\n          padding: 15px;\n          font-size: 18px;\n          cursor: pointer;\n      }\n      \n    "]);return a=function(){return n},n}function c(){var n=o(["\n      position: fixed;\n      top: 0;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n      input {\n        z-index : 3;\n      }\n      @media screen and (orientation: portrait) {\n        top: 0px;\n        left: 0; \n        right: 0;\n        bottom: 0;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n          @media screen and (max-width : 900px ) {\n            top: 0px;\n            left: 0; \n            right: 0px;\n            bottom: 0;\n        }\n    "]);return c=function(){return n},n}function s(){var n=o(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return s=function(){return n},n}function u(){var n=o(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 0px;\n      background-color: #fff;\n      z-index: 998;\n    "]);return u=function(){return n},n}function l(){var n=o(["\n        position: absolute;\n        left: 0;\n        top: 0;        \n        width : calc(100%);\n        height : calc(100%);\n        overflow: hidden;\n        background-color: #eaeaea;\n        background-image:\n         -webkit-linear-gradient(180deg, #e2e2e2 1px, transparent 1px) ,\n          -webkit-linear-gradient(90deg, #e2e2e2 1px, transparent 1px);\n        background-size: 50px 50px;\n        background-position: calc(50% + 25px) 0%;\n        z-index:1;\n        &.left_panel_layout {\n          height : calc(100%);\n        }\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          touch-action: none;\n          &.canvas_drawing {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 0 0,auto;\n          }\n        } \n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return l=function(){return n},n}function p(){var n=o(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return p=function(){return n},n}function d(){var n=o(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return d=function(){return n},n}function f(){var n=o(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return f=function(){return n},n}function h(){var n=o(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return h=function(){return n},n}function x(){var n=o(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return x=function(){return n},n}function m(){var n=o(["\n      position: fixed;\n      top: 68px;\n      right: 12px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #fff;\n      box-shadow: 0px 6px 20px 0px #00000014;\n      font-size: 30px;\n      // 竖屏样式\n\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n        width: 40px;\n        height: 40px;\n        font-size: 25px;\n      }\n    "]);return m=function(){return n},n}function v(){var n=o(["\n      position: fixed;\n      top: 120px; /* Adjust this value to position it below the focusIcon */\n      right: 12px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #fff;\n      box-shadow: 0px 6px 20px 0px #00000014;\n      font-size: 30px;\n\n      @media screen and (max-width: 450px) {\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      @media screen and (orientation: landscape) {\n        width: 40px;\n        height: 40px;\n        font-size: 25px;\n      }\n    "]);return v=function(){return n},n}function b(){var n=o(["\n      position: absolute;\n      top: 0px;\n      left: 0;\n      width: 100%;\n      height: 45px;\n      z-index: 998;\n      background-color: #fff;\n    "]);return b=function(){return n},n}function g(){var n=o(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 999;\n    "]);return g=function(){return n},n}function y(){var n=o(["\n      padding: 20px;\n      position:absolute;\n      left: 0;\n      top: 0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n      background: #f6f7f9;\n    "]);return y=function(){return n},n}function w(){var n=o(["\n      width: 100%;\n      height: 100%;\n      left: 0;\n      top: 0;\n      background: #f6f7f9;\n    "]);return w=function(){return n},n}t.A=(0,i.rU)((function(n){var t=n.css;return{root:t(a()),content:t(c()),canvas3d:t(s()),side_pannel:t(u()),canvas_pannel:t(l(),(0,r.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:t(p()),backBtn:t(d()),forwardBtn:t(f()),schemeNameSpan:t(h()),overlay:t(x()),focusIcon:t(m()),multiSchemeIcon:t(v()),RoomAreaBtns:t(b()),aiDraw:t(g()),mobile_atlas_container:t(y()),pad_startpage_container:t(w())}}))},90503:function(n,t,e){var r=e(13274),i=e(85783),o=e(14781),a=e(15696),c=e(41594),s=e(69391),u=e(27347),l=e(86417),p=e(27164);function d(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function f(n,t,e,r,i,o,a){try{var c=n[o](a),s=c.value}catch(n){return void e(n)}c.done?t(s):Promise.resolve(s).then(r,i)}function h(n){return function(){var t=this,e=arguments;return new Promise((function(r,i){var o=n.apply(t,e);function a(n){f(o,r,i,a,c,"next",n)}function c(n){f(o,r,i,a,c,"throw",n)}a(void 0)}))}}function x(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,i,o=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(n){c=!0,i=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw i}}return o}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return d(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return d(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(n,t){var e,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(s){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(e=1,r&&(i=2&c[0]?r.return:c[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,c[1])).done)return i;switch(r=0,i&&(c=[2&c[0],i.value]),c[0]){case 0:case 1:i=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,r=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!i||c[1]>i[0]&&c[1]<i[3])){o.label=c[1];break}if(6===c[0]&&o.label<i[1]){o.label=i[1],i=c;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(c);break}i[2]&&o.ops.pop(),o.trys.pop();continue}c=t.call(n,o)}catch(n){c=[6,n],r=0}finally{e=i=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}t.A=(0,a.observer)((function(){var n=(0,i.B)().t,t=(0,o.A)().styles,e=(0,c.useRef)(null),a=x((0,c.useState)([]),2),d=a[0],f=a[1],v=x((0,c.useState)([]),2),b=v[0],g=v[1],y=x((0,c.useState)(""),2),w=y[0],j=(y[1],x((0,c.useState)(!1),2)),_=j[0],k=j[1],N=x((0,c.useState)(!1),2),z=N[0],S=N[1],A=x((0,c.useState)({name:"广州市",code:"440100"}),2),C=A[0],I=A[1],O=function(){var n=h((function(n){var t,e;return m(this,(function(r){switch(r.label){case 0:return t=n.target.value,[4,s.Q.getBuildingList(t,C.code)];case 1:return e=r.sent(),g(e||[]),[2]}}))}));return function(t){return n.apply(this,arguments)}}(),F=function(){var n=h((function(n){var t,e;return m(this,(function(r){switch(r.label){case 0:return[4,s.Q.search("",n.code)];case 1:return t=r.sent(),e=(null==t?void 0:t.records)||[],f(e||[]),[2]}}))}));return function(t){return n.apply(this,arguments)}}(),B=function(){var n=h((function(){var n,t,r;return m(this,(function(i){switch(i.label){case 0:return e.current?(n=e.current.value,[4,s.Q.search(n,C.code)]):[3,2];case 1:t=i.sent(),r=(null==t?void 0:t.records)||[],f(r||[]),i.label=2;case 2:return[2]}}))}));return function(){return n.apply(this,arguments)}}(),P=function(){var n=h((function(){var n;return m(this,(function(t){switch(t.label){case 0:return[4,s.Q.ipParse()];case 1:return(n=t.sent())&&n.city&&(I(n.city),F(n.city)),[2]}}))}));return function(){return n.apply(this,arguments)}}();return(0,c.useEffect)((function(){u.nb.on("setIsVisible",(function(n){k(n)})),P()}),[]),_?(0,r.jsxs)("div",{className:t.root,children:[(0,r.jsx)(p.A,{iconClass:"iconclose1",className:"iconicon",onClick:function(){k(!1)}}),(0,r.jsx)("div",{className:t.row,children:(0,r.jsx)("div",{className:t.hx_logo})}),(0,r.jsx)("div",{className:t.row,children:(0,r.jsxs)("div",{className:"houseSearch",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"style_trigger__1c72413f",children:[(0,r.jsx)(l.Z,{is_visible:z,onSelected:function(n){I(n),F(n),S(!1)}}),(0,r.jsx)("span",{className:"style_city_label__1c72413f",onClick:function(){return S(!z)},children:C.name}),(0,r.jsx)("span",{role:"img","aria-label":"caret-down",className:"anticon anticon-caret-down",children:(0,r.jsx)("svg",{viewBox:"0 0 1024 1024",focusable:"false","data-icon":"caret-down",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"})})})]})}),(0,r.jsx)("div",{className:"line"}),b&&b.length>0&&(0,r.jsx)("div",{style:{position:"absolute"},children:(0,r.jsxs)("div",{className:"candidate_buiding_names",children:[(0,r.jsx)("div",{className:"select-operate",children:(0,r.jsx)("div",{children:"你是不是想找"})}),b.map((function(t,i){return(0,r.jsxs)("div",{className:"select_item",onClick:h((function(){var n,r;return m(this,(function(i){switch(i.label){case 0:return e&&e.current?(e.current.value=t.buildingName,[4,s.Q.search(t.buildingName,C.code)]):[3,2];case 1:n=i.sent(),r=(null==n?void 0:n.records)||[],f(r||[]),i.label=2;case 2:return g([]),[2]}}))})),children:[(0,r.jsx)("div",{className:"select-item_left",children:(0,r.jsx)("b",{children:t.buildingName})}),(0,r.jsx)("div",{className:"select-item_right",children:(0,r.jsxs)("span",{children:[t.roomCount,n("个结果")]})})]},"candidate"+i)}))]})}),(0,r.jsx)("input",{placeholder:n("输入楼盘、小区名称查找户型"),className:"ant-input search-input",type:"text",defaultValue:w,ref:e,onChange:O}),(0,r.jsx)("div",{className:"search-btn",onClick:B,children:n("搜户型")})]})}),(0,r.jsx)("div",{className:t.row+" buildingContent",children:d.map((function(n,t){return(0,r.jsxs)("div",{className:"house-card",onClick:function(){u.nb.instance&&(u.nb.DispatchEvent(u.n0.PostBuildingId,{id:n.id,name:n.buildingName}),k(!1))},children:[(0,r.jsx)("img",{src:n.imagePath,alt:n.buildingName}),(0,r.jsxs)("div",{className:"house-card__info",children:[(0,r.jsx)("div",{className:"name",children:n.buildingName}),(0,r.jsxs)("div",{className:"detail-wrap",children:[(0,r.jsxs)("div",{className:"houseType",children:[n.roomTypeName," | ",n.area+"m²","  "]}),(0,r.jsxs)("div",{className:"location",children:[n.provinceName," / ",n.cityName]})]})]})]},"build_result_"+t)}))})]}):(0,r.jsx)(r.Fragment,{})}))}}]);