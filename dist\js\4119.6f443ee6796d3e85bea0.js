"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4119],{16353:function(n,e,t){t.r(e);var a=t(13274),i=t(85783),o=t(79316),r=t(15696),s=t(41594),c=t(27347),u=t(98612),l=t(9003),d=t(88934),h=t(34577),f=t(78644),b=t(93365),m=t(23825),v=t(65810),p=t(99030),y=t(32184);function _(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,a=new Array(e);t<e;t++)a[t]=n[t];return a}function g(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var a,i,o=[],r=!0,s=!1;try{for(t=t.call(n);!(r=(a=t.next()).done)&&(o.push(a.value),!e||o.length!==e);r=!0);}catch(n){s=!0,i=n}finally{try{r||null==t.return||t.return()}finally{if(s)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return _(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return _(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}e.default=(0,r.observer)((function(){var n=(0,i.B)().t,e=(0,o.A)().styles,t=g((0,s.useState)(""),2),r=(t[0],t[1]),_=g((0,s.useState)(-2),2),S=_[0],w=_[1],A=(0,l.P)();(0,s.useRef)(null);c.nb.UseApp(u.e.AppName),c.nb.instance&&(c.nb.t=n);var j=function(){c.nb.instance&&(c.nb.instance.bindCanvas(document.getElementById("cad_canvas")),c.nb.instance.update())};return(0,s.useEffect)((function(){c.nb.instance&&(c.nb.instance._is_website_debug=m.iG),window.addEventListener("resize",j),j(),c.nb.instance&&(c.nb.instance.initialized||(c.nb.instance.init(),c.nb.RunCommand(u.f.AiCadMode),c.nb.instance.layout_graph_solver._is_query_server_model_rooms=!1,c.nb.instance.layout_container.drawing_figure_mode=y.qB.Figure2D,c.nb.instance.prepare().then((function(){})),c.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),c.nb.instance.update()),c.nb.on_M(v.z.showLight3DViewer,"LightHuawei",(function(n){n?(w(2),c.nb.emit(p.r.UpdateScene3D,!1)):w(-1)})),c.nb.on(d.U.LayoutSchemeOpened,(function(n){r(n.name),c.nb.emit(b.$T,b.Kw.Default)}))}),[]),(0,a.jsxs)("div",{className:e.root,children:[(0,a.jsx)(b.Ay,{state:b.Kw.HuaweiDemo}),(0,a.jsx)(h.A,{}),(0,a.jsxs)("div",{id:"Canvascontent",className:e.content,children:[(0,a.jsx)("div",{className:"3d_container "+e.canvas3d,style:{zIndex:S},children:(0,a.jsx)(f.A,{defaultViewMode:4})}),(0,a.jsx)("div",{id:"body_container",className:e.canvas_pannel,children:(0,a.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){A.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){A.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,a=Math.sqrt(e*e+t*t);A.homeStore.setInitialDistance(a/A.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,a=Math.sqrt(e*e+t*t)/A.homeStore.initialDistance;a>5?a=5:a<.05&&(a=.05),A.homeStore.setScale(a),c.nb.DispatchEvent(c.n0.scale,a)}},onTouchEnd:function(){A.homeStore.setInitialDistance(null)}})})]})]})}))}}]);