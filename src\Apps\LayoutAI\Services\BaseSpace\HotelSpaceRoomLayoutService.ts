import { Vector3 } from "three";
import { I_SwjWindow } from "../../AICadData/SwjLayoutData";
import { IRoomEntityType, IRoomSpaceAreaType, RoomSpaceAreaType } from "../../Layout/IRoomInterface";
import { WPolygon } from "../../Layout/TFeatureShape/WPolygon";
import { TBaseEntity } from "../../Layout/TLayoutEntities/TBaseEntity";
import { TLayoutEntityContainer } from "../../Layout/TLayoutEntities/TLayoutEntityContainter";
import { TBaseSpaceEntity } from "../../Layout/TLayoutEntities/TSpaceEntities/TBaseSpaceEntity";
import { TSubSpaceAreaEntity } from "../../Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { TWindowDoorEntity } from "../../Layout/TLayoutEntities/TWinDoorEntity";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { I_SpaceTypeConfig } from "./BaseSpaceService";
import { TFigureElement } from "../../Layout/TFigureElements/TFigureElement";
import { TFurnitureEntity } from "../../Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "../../Layout/TLayoutEntities/TRoomEntity";
import { TSwjLayoutGraphSolver } from "../../Layout/TAppSolvers/TSwjLayoutGraphSolver";
import { roomSubAreaService } from "../Basic/RoomSubAreaService";
import { compareNames } from "@layoutai/z_polygon";

interface I_SubAreaResult{
    area_name : string,
    area_rect : ZRect
}
interface I_AreaLayoutElementRule 
{
    category : string;
    code ?: string;
    length : number;
    depth : number;
    height ?: number;
    back_wall_offset ?: number;
    padding_left ?: number;
    start_val ?: string;
    length_val ?: string;
    rotate_degree?: number;
    u_dv_flag ?: number;
    align_target_codes?: string[];
} 
interface I_AreaLayoutRule
{
    main_rect_edge_id : number;
    u_dv_flag : number;
    element_rules : I_AreaLayoutElementRule[];
}
let AreaLayoutRules : {name:string, tags?:string[],area_type ?: IRoomSpaceAreaType, min_length?:number, rules:I_AreaLayoutRule[]}[] = [
    {
        name: "双床房",
        tags : ["双床"],
        area_type : RoomSpaceAreaType.SleepingArea,
        min_length : 4200,
        rules : [
            {
                main_rect_edge_id : 0, // 此为backEdge,
                u_dv_flag : 1,
                element_rules : [
                    {
                        category : "无靠背沙发",
                        code : "无靠背沙发",
                        length : 1350,
                        depth : 800,
                        height : 820,
                        back_wall_offset : 120
                    },
 
                    {
                        category : "床",
                        code : "床",
                        length : 1200,
                        depth : 2000,
                        height : 1500,
                        padding_left : 100,
                        back_wall_offset : 120,
                    },
                    {
                        category : "床",
                        code : "床1",
                        length : 1200,
                        depth : 2000,
                        height : 1500,
                        padding_left : 350,
                        back_wall_offset : 120,
                    },
                    {
                        category : "床头柜",
                        length : 500,
                        depth : 500,
                        padding_left : 0,
                        back_wall_offset : 60,
                    },
                    {
                        category : "圆形茶几",
                        length : 759,
                        depth : 570,
                        height : 450,
                        back_wall_offset : 1150,
                        align_target_codes : ["无靠背沙发"]
                    },
                    {
                        category : "休闲椅",
                        length : 590,
                        depth : 590,
                        height : 800,
                        rotate_degree : 180,
                        back_wall_offset : 2690,
                        align_target_codes : ["无靠背沙发"]
                    },
                    {
                        category : "背景墙",
                        length : 2000,
                        depth : 120,
                        height : 2400,
                        back_wall_offset : 0,
                        align_target_codes : ["床","床1","无靠背沙发"],
                        length_val : "g_l"
                    },{
                        category : "地毯",
                        length : 4000,
                        depth : 1500,
                        height : 2400,
                        back_wall_offset : 800,
                        align_target_codes : ["床","床1"],
                        length_val : "Math.min(g_l + 400,t_l)"
                    }
                    
 

                ]
            },{
                main_rect_edge_id : 2,
                u_dv_flag : -1,
                element_rules : [
                    {
                        category : "电视柜",
                        code : "电柜柜",
                        length :3200,
                        depth : 450,
                        padding_left : 0,
                        back_wall_offset : 0,
                        align_target_codes :  ["床","床1"],
                        length_val : 'Math.max(g_l,t_l)'                    
                    },
                ]
            }
        ],
    },
    {
        name: "双床房2",
        tags : ["双床"],
        area_type : RoomSpaceAreaType.SleepingArea,
        min_length : 3600,
        rules : [
            {
                main_rect_edge_id : 0, // 此为backEdge,
                u_dv_flag : 1,
                element_rules : [
                    {
                        category : "床头柜",
                        length : 500,
                        depth : 500,
                        padding_left : 0,
                        back_wall_offset : 60,
                    },
                    {
                        category : "床",
                        code : "床",
                        length : 1200,
                        depth : 2000,
                        height : 1500,
                        padding_left : 100,
                        back_wall_offset : 120,
                    },
                    {
                        category : "床",
                        code : "床1",
                        length : 1200,
                        depth : 2000,
                        height : 1500,
                        padding_left : 350,
                        back_wall_offset : 120,
                    },
                    {
                        category : "床头柜",
                        length : 500,
                        depth : 500,
                        padding_left : 100,
                        back_wall_offset : 60,
                    },
                    {
                        category : "背景墙",
                        length : 2000,
                        depth : 120,
                        height : 2400,
                        back_wall_offset : 0,
                        align_target_codes : ["床","床1"],
                        length_val : "g_l"
                    },
                    {
                        category : "地毯",
                        length : 4000,
                        depth : 1500,
                        height : 2400,
                        back_wall_offset : 800,
                        align_target_codes : ["床","床1"],
                        length_val : "Math.min(g_l + 400,t_l)"
                    }
 

                ]
            },{
                main_rect_edge_id : 2,
                u_dv_flag : -1,
                element_rules : [
                    {
                        category : "电视柜",
                        code : "电柜柜",
                        length : 3200,
                        depth : 450,
                        padding_left : 0,
                        back_wall_offset : 0,
                        align_target_codes :  ["床","床1"],
                        length_val : 'Math.max(g_l,t_l)'                    
                    },
                ]
            }
        ],
    },
    {
        name: "大床房",
        tags : ["大床"],
        area_type : RoomSpaceAreaType.SleepingArea,
        min_length : 3950,
        rules : [
            {
                main_rect_edge_id : 0, // 此为backEdge,
                u_dv_flag : 1,
                element_rules : [
                    {
                        category : "无靠背沙发",
                        code : "无靠背沙发",
                        length : 1350,
                        depth : 800,
                        height : 820,
                        back_wall_offset : 120
                    },
                    {
                        category : "床",
                        code : "床",
                        length : 2000,
                        depth : 2000,
                        height : 1500,
                        padding_left : 50,
                        back_wall_offset : 120,
                    },
                    {
                        category : "床头柜",
                        code : "床头柜2",
                        length : 500,
                        depth : 500,
                        padding_left : 50,
                        back_wall_offset : 60,
                    },
                    {
                        category : "背景墙",
                        length : 2000,
                        depth : 120,
                        height : 2400,
                        back_wall_offset : 0,
                        align_target_codes : ["床","无靠背沙发"],
                        length_val : "g_l"
                    },
                    {
                        category : "地毯",
                        length : 4000,
                        depth : 1500,
                        height : 2400,
                        back_wall_offset : 800,
                        align_target_codes : ["床","床1"],
                        length_val : "Math.min(g_l + 400,t_l)"
                    }
                    
 

                ]
            },
            ,{
                main_rect_edge_id : 2,
                u_dv_flag : -1,
                element_rules : [
                    {
                        category : "电视柜",
                        code : "电柜柜",
                        length : 3200,
                        depth : 450,
                        padding_left : 0,
                        back_wall_offset : 0,
                        align_target_codes :  ["床","床1"],
                        length_val : 'Math.max(g_l,t_l)'
                    },
                ]
            }
        ],
    },
    {
        name: "大床房",
        tags : ["大床"],
        area_type : RoomSpaceAreaType.SleepingArea,
        min_length : 3200,
        rules : [
            {
                main_rect_edge_id : 0, // 此为backEdge,
                u_dv_flag : 1,
                element_rules : [
                    {
                        category : "床头柜",
                        code : "床头柜1",
                        length : 500,
                        depth : 500,
                        padding_left : 0,
                        back_wall_offset : 60,
                    },
                    {
                        category : "床",
                        code : "床",
                        length : 2000,
                        depth : 2000,
                        height : 1500,
                        padding_left : 100,
                        back_wall_offset : 120,
                    },
                    {
                        category : "床头柜",
                        code : "床头柜2",
                        length : 500,
                        depth : 500,
                        padding_left : 100,
                        back_wall_offset : 60,
                    },
                    {
                        category : "背景墙",
                        length : 2000,
                        depth : 120,
                        height : 2400,
                        back_wall_offset : 0,
                        align_target_codes : ["床","床头柜1","床头柜2"],
                        length_val : "g_l"
                    },
                    {
                        category : "地毯",
                        length : 4000,
                        depth : 1500,
                        height : 2400,
                        back_wall_offset : 800,
                        align_target_codes : ["床","床1"],
                        length_val : "Math.min(g_l + 400,t_l)"
                    }
 

                ]
            },{
                main_rect_edge_id : 2,
                u_dv_flag : -1,
                element_rules : [
                    {
                        category : "电视柜",
                        code : "电柜柜",
                        length : 3200,
                        depth : 450,
                        padding_left : 0,
                        back_wall_offset : 0,
                        align_target_codes :  ["床","床1"],
                        length_val : 'Math.max(g_l,t_l)'
                    },
                ]
            }
        ],
    },
    {
        name: "大床房",
        tags : ["大床"],
        area_type : RoomSpaceAreaType.SleepingArea,
        min_length : 2200,
        rules : [
            {
                main_rect_edge_id : 0, // 此为backEdge,
                u_dv_flag : 1,
                element_rules : [

                    {
                        category : "床",
                        code : "床",
                        length : 2000,
                        depth : 2000,
                        height : 1500,
                        padding_left : 100,
                        back_wall_offset : 120,
                    },
                    {
                        category : "背景墙",
                        length : 2000,
                        depth : 120,
                        height : 2400,
                        back_wall_offset : 0,
                        align_target_codes : ["床","床头柜1","床头柜2"],
                        length_val : "g_l"
                    },
                    {
                        category : "地毯",
                        length : 4000,
                        depth : 1500,
                        height : 200,
                        back_wall_offset : 800,
                        align_target_codes : ["床","床1"],
                        length_val : "Math.min(g_l + 400,t_l)"
                    }
 

                ]
            },{
                main_rect_edge_id : 2,
                u_dv_flag : -1,
                element_rules : [
                    {
                        category : "电视柜",
                        code : "电柜柜",
                        length : 3200,
                        depth : 450,
                        padding_left : 0,
                        back_wall_offset : 0,
                        align_target_codes :  ["床","床1"],
                        length_val : 'Math.min(g_l,t_l)'
                    },
                ]
            }
        ],
    },
    {
        name: "卫浴区",
        tags : [],
        area_type : RoomSpaceAreaType.WashingArea,
        min_length : 2000,
        rules:[
            {
                main_rect_edge_id : 0, // 此为backEdge,
                u_dv_flag : 1,
                element_rules : [
                    {
                        category : "内墙",
                        code : "内墙",
                        length : 120,
                        depth : 1100,
                        height : 2800,
                        padding_left : 0,
                        back_wall_offset : 0,
                    },
                    {
                        category : "一字形淋浴房",
                        code : "一字形淋浴房",
                        length : 1000,
                        depth : 120,
                        height : 2600,
                        padding_left : 0,
                        back_wall_offset : 1120,
                        rotate_degree : 180,
                        u_dv_flag :-1
                    },
                    {
                        category : "内墙",
                        code : "内墙1",
                        length : 60,
                        depth : 1100,
                        height : 2800,
                        padding_left : 0,
                        back_wall_offset : 0,
                    },
                    {
                        category : "一字形淋浴房",
                        code : "一字形淋浴房1",
                        length : 1300,
                        depth : 120,
                        height : 2600,
                        padding_left : 0,
                        back_wall_offset : 1120,
                        rotate_degree : 180,
                    },
                ]   
            },{
                main_rect_edge_id : -1,
                u_dv_flag : 1,
                element_rules : [
                    {
                        category : "马桶",
                        length : 400,
                        depth : 600,
                        height : 400,
                        back_wall_offset : 120,
                        align_target_codes : ["内墙"],
                    }
                ],
                
            },{
                main_rect_edge_id : 1,
                u_dv_flag : 1,
                element_rules : [
                    {
                        category : "花洒",
                        length : 400,
                        depth : 600,
                        height : 600,
                        back_wall_offset : 0,
                        align_target_codes : ["内墙"],
                    }
                ]
            },{
                main_rect_edge_id : 2,
                u_dv_flag : -1,
                element_rules : [
                    {
                        category : "浴室柜",
                        length : 1700,
                        depth : 600,
                        height : 2400,
                        back_wall_offset : 0,
                    }
                ]
            }
        ] 
    }
];

/**
 *   酒店房间规划
 */
export class HotelSpaceRoomLayoutService
{
    private static _instance :HotelSpaceRoomLayoutService;
    protected _container : TLayoutEntityContainer = null;

    private _subSpaceTypeConfigs: I_SpaceTypeConfig[];

    constructor()
    {
        this._subSpaceTypeConfigs =[    
            { name: '卫浴区', defaultWidth: 2600, defaultDepth: 2800 },
            { name: '睡眠区',  defaultWidth: 4500, defaultDepth:120},
            { name: '入户衣柜区',  defaultWidth: 1500, defaultDepth:600},

        ]
    }

    static get instance()
    {
        if(!HotelSpaceRoomLayoutService._instance)
        {
            HotelSpaceRoomLayoutService._instance = new HotelSpaceRoomLayoutService();
        }
        return HotelSpaceRoomLayoutService._instance;
    }

    protected _updateDoorAndWindowsInRoomSpace(space_entity:TBaseSpaceEntity)
    {
        if(!space_entity.isLeafSpace()) return;
        if(!space_entity.inner_space_poly || space_entity.inner_space_poly.edges.length == 0) return;
        let w_poly = space_entity.w_polygon;
        let wall_thickness = 120;
        let door_length = 1080;

        let door_side_offset = 200;

        const addDoor = (edge:ZEdge, pos:Vector3)=>{
            let door_entity :TWindowDoorEntity= null; 
            let old_doors = space_entity.attached_entities.filter((entity)=>entity.type === "Door");
            if(old_doors.length > 0)
            {
                door_entity = old_doors[0] as TWindowDoorEntity;
            }
            else{
                let I_SwjDoor = TBaseEntity.makeSimpleEntityData("Door", "Door");
                door_entity = new TWindowDoorEntity(I_SwjDoor);
            }

            space_entity.attached_entities.push(door_entity);

            let wins : I_SwjWindow[] = [];
            w_poly.edges.forEach((edge)=>{
                let win = WPolygon.getWindowOnEdge(edge);
                if(win) wins.push(win);
            });
            let alignLeft = true;
            if(wins.length > 0) // 如果超过1个
            {
                
            }
            let tpp = edge.projectEdge2d(pos);
            let initPos = edge.unprojectEdge2d({x:tpp.x,y:-wall_thickness/2});

            door_entity.length = door_length;
            door_entity.thickness = wall_thickness;

            door_entity.rect.nor = edge.nor.clone();

            door_entity.rect.u_dv = edge.dv;
            door_entity.rect.rect_center = initPos;    
        }
        let washing_area = space_entity.attached_entities.find((area)=>area.type==="SubArea" && (area as TSubSpaceAreaEntity).space_area_type === RoomSpaceAreaType.WashingArea);
        if(washing_area)
        {
            let washing_area_rect = washing_area.rect;
            let t_pos = washing_area_rect.frontEdge.unprojectEdge2d({x:0,y:(door_side_offset+door_length)/2});
            let front_edges = w_poly.edges.filter((edge)=>edge.checkSameNormal(space_entity.rect.nor,false,0.2) && !WPolygon.getWindowOnEdge(edge) && edge.length > door_side_offset+door_length);
            if(front_edges[0])
            {
                addDoor(front_edges[0],t_pos);
            }
        }
        else{
            let front_edges = w_poly.edges.filter((edge)=>edge.checkSameNormal(space_entity.rect.nor,false,0.2) && !WPolygon.getWindowOnEdge(edge) && edge.length > door_side_offset+door_length);
            if(front_edges[0])
            {
                let initPos = front_edges[0].unprojectEdge2d({x:door_side_offset+door_length/2,y:-wall_thickness/2});
                addDoor(front_edges[0],initPos);
          
            }
        }


    }

    protected _getAttachedEntityByType(space_entity:TBaseSpaceEntity,type:IRoomEntityType)
    {
        let entities = space_entity.attached_entities.filter((entity)=>entity.type === type);
        return entities[0] || null;

    }
    protected _cleanAttachedEntitiesByType(space_entity:TBaseSpaceEntity, types:IRoomEntityType[])
    {
        let entities = space_entity.attached_entities.filter((entity)=>types.includes(entity.type));
        entities.forEach(entity=>{
            let id = space_entity.attached_entities.indexOf(entity);
            space_entity.attached_entities.splice(id,1);
        });

    }
    protected _updateSubSpacesInRoomSpace(space_entity:TBaseSpaceEntity)
    {
        let w_poly = space_entity.w_polygon;
        let rect = space_entity.rect;

        let inner_poly = space_entity.inner_space_poly;
        let inner_w_poly = WPolygon.getAttachedWPolygon(inner_poly) || w_poly;
        let wins : I_SwjWindow[] = [];
        inner_w_poly.edges.forEach((edge)=>{
            let win = WPolygon.getWindowOnEdge(edge);
            if(win) wins.push(win);
        });

        let roomEntity : TRoomEntity = this._getAttachedEntityByType(space_entity,"RoomArea") as TRoomEntity || new TRoomEntity(inner_poly,space_entity.name);
        this._cleanAttachedEntitiesByType(space_entity,["RoomArea"]);
        roomEntity._room_poly = inner_poly;
        roomEntity.makeTRoom([]);

        const minDoubleBedsAreaLength = 1200 + 1200 + 600;  
        const minSingleBedAreaLength = 1800+ 600;
        const minWashingRoomLength = 2400;
        const wall_thickness = 120;

        const t_main_rect = ZRect.fromPoints(inner_poly.positions,rect.nor,rect.u_dv_flag);
        const tryLayoutFromEdge = (poly_edge:ZEdge)=>{
            // let inner_layon_edges = inner_w_poly.edges.filter((edge)=>edge.checkSameNormal(rect_edge.nor,false,0.1));
            
            let t_edge = poly_edge;
            if(poly_edge.dv.dot(rect.nor) < 0)
            {
                t_edge = new ZEdge({pos:poly_edge.v1.pos},{pos:poly_edge.v0.pos});
                t_edge._nor = poly_edge.nor.clone();
            }

            if(t_edge.length < Math.min(minDoubleBedsAreaLength,minSingleBedAreaLength)+minWashingRoomLength) return null;

            let washingRect = new ZRect(minWashingRoomLength,minWashingRoomLength+200);

            washingRect.nor = t_edge.nor.clone().negate();

            washingRect.back_center = t_edge.unprojectEdge2d({x:t_edge.length-minWashingRoomLength/2,y:0});

            washingRect.u_dv = t_edge.dv;
            washingRect.updateRect();
            let bedsAreaRect = new ZRect(t_edge.length - washingRect.w,t_main_rect.min_hh);

            bedsAreaRect.nor = washingRect.nor.clone();

            bedsAreaRect.back_center = t_edge.unprojectEdge2d({x:bedsAreaRect.w/2,y:0});
            bedsAreaRect.u_dv = t_edge.dv;

            bedsAreaRect.updateRect();


            return [{
                area_name: RoomSpaceAreaType.WashingArea,
                area_rect : washingRect
            },{
                area_name: RoomSpaceAreaType.SleepingArea,
                area_rect:bedsAreaRect
            }];
        }

        let r_edges = [rect.leftEdge,rect.rightEdge,rect.frontEdge];

        let target_ans:I_SubAreaResult[] = null;
        r_edges.forEach((r_edge)=>{
            if(target_ans) return;
            let poly_edges = inner_poly.edges.filter((edge)=>edge.islayOn(r_edge,wall_thickness * 4,0.1));
            poly_edges.forEach((edge)=>{
                if(target_ans) return;
                let res = tryLayoutFromEdge(edge);
                if(res)
                {
                    target_ans = res;
                }
            })
        });

        if(target_ans)
        {
            this._cleanAttachedEntitiesByType(space_entity,["SubArea"]);
            target_ans.forEach((data)=>{
                let area = new TSubSpaceAreaEntity();
                area.space_area_type = data.area_name as any;
                area.rect.copy(data.area_rect);
                area.update();
                area.room_entity = roomEntity;
                area.updateSpaceAreaTRoom();
                space_entity.attached_entities.push(area);
            })
        }


    }
    protected _updateFurnituresInRoomSpace(space_entity:TBaseSpaceEntity)
    {
        this._cleanAttachedEntitiesByType(space_entity,["Furniture"]);
        let sub_area_entities = space_entity.attached_entities.filter((sub_entity)=>sub_entity.type==="SubArea") as TSubSpaceAreaEntity[];

        sub_area_entities.forEach((sub_area)=>{
            let rules = AreaLayoutRules.filter((rule)=>rule.area_type === sub_area.space_area_type && (sub_area.rect.w)>(rule.min_length||10000));
            const figScore = (a:{name:string,tags?:string[]})=>{
                if(compareNames([space_entity.name],[a.name,...a.tags]))
                {
                    return 1;
                }
                return 0;

            }
            rules.sort((a,b)=>{
                return figScore(b) - figScore(a);
            })
            if(rules[0])
            {
                let curr_rule = rules[0];
                const code_element_dict : {[key:string]:TFigureElement} = {};
                const figure_elements : TFigureElement[] = [];
                curr_rule.rules.forEach((layout_rule)=>{
                    let t_edge = sub_area.rect.edges[((layout_rule.main_rect_edge_id||0)+3) %4];
                    if(layout_rule.u_dv_flag && layout_rule.u_dv_flag < 0)
                    {
                        let nor = t_edge.nor.clone();
                        t_edge = new ZEdge({pos:t_edge.v1.pos},{pos:t_edge.v0.pos});
                        t_edge._nor = nor;
                    }

    
                    let start_val = 0;
                    layout_rule.element_rules.forEach((ele_rule)=>{

                        let ele = TFigureElement.createSimple(ele_rule.category);
                        ele.length = ele_rule.length;
                        ele.depth = ele_rule.depth;
                        ele.height = ele_rule.height || ele.height;

                        ele.nor = t_edge.nor.clone().negate();
                        if(ele_rule.rotate_degree)
                        {
                            ele.nor = t_edge.nor.clone().negate().applyAxisAngle(new Vector3(0,0,1), ele_rule.rotate_degree /180*Math.PI);
                        }
                        if(ele_rule.u_dv_flag)
                        {
                            ele.rect._u_dv_flag = ele_rule.u_dv_flag;
                        }

                        if(ele_rule.align_target_codes)
                        {
                            let target_elements : TFigureElement[] = [];
                            ele_rule.align_target_codes.forEach((code)=>{
                                if(code_element_dict[code])
                                {
                                    target_elements.push(code_element_dict[code]);
                                }
                            });

                            if(target_elements.length > 0)
                            {
                                let group_rect = ZRect.fromPoints(target_elements.map((ele)=>ele.rect.positions).flatMap(p=>p),ele.nor);
                                let t_center_val = t_edge.projectEdge2d(group_rect.rect_center).x;  
                                let t_offset_val = ele_rule.back_wall_offset || 0;
                                let pos = t_edge.unprojectEdge2d({x:t_center_val,y:-t_offset_val});
                                ele.rect.back_center = pos;
                                ele.rect.updateRect();
                                if(ele_rule.length_val)
                                {
                                    let inputData: { [key: string]: number } = {
                                        g_l: group_rect.w,
                                        t_l: ele_rule.length,
                                    }
                                    let code = '';
                                    for (let key in inputData) {
                                        code += `let ${key} = arg['${key}'] ?? 0; `;
                                    }
                                    code += `return  (${ele_rule.length_val});`;
                    
                                    let val = new Function('arg', code)(inputData);
                                    ele.length= val;
                                    ele.rect.updateRect();
                                }
                                figure_elements.push(ele);
                                if(ele_rule.code)
                                {
                                    code_element_dict[ele_rule.code] = ele;
                                }
                            }



                        }
                        else{
                            let t_center_val = start_val + (ele_rule.padding_left || 0) + ele.length /2;
                            let t_offset_val = ele_rule.back_wall_offset || 0;
                            let pos = t_edge.unprojectEdge2d({x:t_center_val,y:-t_offset_val});
                            ele.rect.back_center = pos;
                            ele.rect.updateRect();
                            start_val += (ele_rule.padding_left||0) + ele.length;
                            figure_elements.push(ele);
                        }
                        if(ele_rule.code)
                        {
                            code_element_dict[ele_rule.code] = ele;
                        }

                    })

                });

                figure_elements.sort((a,b)=>a.default_drawing_order - b.default_drawing_order);
                figure_elements.forEach((ele)=>{
                    let furnitureEntity = new TFurnitureEntity(ele);
                    space_entity.attached_entities.push(furnitureEntity);
                })
            }



            
        })
    }
    computeInnerSpaceLayout(space_entity:TBaseSpaceEntity, container:TLayoutEntityContainer=null)
    {
        this._container = this._container || container;
        this._updateSubSpacesInRoomSpace(space_entity);
        this._updateFurnituresInRoomSpace(space_entity);
        this._updateDoorAndWindowsInRoomSpace(space_entity);

    }
}
