from translate import Translator
import xml
import xml.etree.ElementTree as ET
import os
import shutil
import json
import combine_xml
def make_svg_list():
    dirname = os.path.dirname(__file__)
    translator = Translator(from_lang='zh',to_lang='en')
    figure_names = []
    figure_svg_path_dict = {}
    for root_path,dirs,files in os.walk(dirname):
        if root_path.find("橱柜")<0:
            continue
        for file in files:
            if not file.endswith("svg"):
                continue
            figure_name = file.replace(".svg","").strip()
            
            figure_names.append(figure_name)
            figure_svg_path_dict[figure_name] = os.path.join(root_path,file)

    def sort_by_name(name:str):
        if name.find("水")>=0:
            return 0
        if name.find("炉灶")>=0:
            return 1
        if name.find("冰箱")>=0:
            return 2
        if name.find("板")>=0:
            return 5
        if name.find("地柜")>=0:
            return 3
        if name.find("吊柜")>=0:
            return 4
        return 6
    figure_names.sort(key=sort_by_name)
    print(figure_names)
    figure_name_translate_dict = {}
    with open(os.path.join(dirname,"figure_name_dict.json"),encoding='utf-8') as fp:
        figure_name_translate_dict = json.load(fp=fp)
    t_figure_names = []
    for name in figure_names:
        if name in figure_name_translate_dict:
            t_figure_names.append(figure_name_translate_dict[name])
        else:
            print("not in dict",name)
            t_figure_names.append(translator.translate(name))
    print(t_figure_names)
    static_path = dirname[0:dirname.find("layout_ai")] + "/layout_ai/static"


    figure_list = []
    figure_img_list = {}
    t_has_name_list = []
    for name,en_name in zip(figure_names,t_figure_names):
        name = name.strip()
        t_en_name = en_name.strip().lower()
        t_en_name = t_en_name.replace(" ","_")
        if len(t_en_name)==0:
            continue

        if t_en_name in t_has_name_list:
            t_en_name+=str(len(t_has_name_list))
        t_has_name_list.append(t_en_name)
        figure_name_translate_dict[name] = t_en_name
        figure_path = figure_svg_path_dict[name]
        png_figure_path = str(figure_path).replace(".svg",".png")
        png_suffix =".svg"
        target_figure_path =os.path.join(dirname, static_path+"/figures_imgs/"+t_en_name+png_suffix)
        shutil.copyfile(src=figure_path,dst=target_figure_path)
        # print(png_figure_path,os.path.exists(png_figure_path))
        # if os.path.exists(png_figure_path):
        #     print(png_figure_path)
        #     png_suffix = ".png"
        #     figure_path = png_figure_path
        if name=="吊柜" or name=="地柜" or name=="收口板":
            continue    
        figure_list.append({
            "title": name,
            "image": t_en_name+png_suffix,
            "png":  t_en_name+png_suffix,
            "label": name,
            "dragId": name,
            "id": name,
            "roomType": ['厨房'],
        })
    

        
        length = 600
        depth = 560

        if name.find("板")>=0:
            length = 30
        if name.find("吊柜")>=0:
            depth = 375
        figure_img_list[name] = {
            "img_path": "./static/figures_imgs/"+t_en_name+png_suffix,
            "alias": name,
            'length': length,
            'depth': depth,
            "modelLoc": name,
            "public_category": name,
            "subCategory": name
        }
    # print(figure_list)

    fp = open(os.path.join(dirname,"figure_name_dict.json"),"w",encoding="utf-8")

    json.dump(figure_name_translate_dict,fp,ensure_ascii=False,indent=2)
    fp.close()
    fp = open(os.path.join(dirname,"figure_list.json"),"w",encoding="utf-8")

    json.dump(figure_list,fp,ensure_ascii=False,indent=2)
    fp.close()
        
    fp = open(os.path.join(dirname,"figure_img_list.json"),"w",encoding="utf-8")

    json.dump(figure_img_list,fp,ensure_ascii=False,indent=2)
    fp.close()
make_svg_list()
combine_xml.combine_xml()
