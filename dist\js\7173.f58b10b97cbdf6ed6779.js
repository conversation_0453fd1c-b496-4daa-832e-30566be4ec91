"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[7173],{7173:function(n,e,t){t.r(e),t.d(e,{default:function(){return R}});var r=t(13274),a=t(41594),o=t(15696),i=t(85783),c=t(9003),s=t(27347),u=t(98612),l=t(78644),d=t(88934),f=t(23825);function h(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function p(){var n=h(["\n      width:100%;\n      height:100vh;\n    "]);return p=function(){return n},n}function v(){var n=h(["\n      position: absolute;\n      top: 0px;\n      left: 0; \n      width: 400px;\n      height: 400px;\n      overflow: hidden;\n    "]);return v=function(){return n},n}function b(){var n=h(["\n      position: absolute;\n      top: 0px;\n      left: 0; \n      width: 100px;\n      height: 60px;\n      overflow: hidden;\n    "]);return b=function(){return n},n}function _(){var n=h(["\n      position:absolute;\n      top:0;\n      left:0;\n      width: 600px;\n      height: 600px;\n      z-index:-1;\n    "]);return _=function(){return n},n}function m(){var n=h(["\n        position: absolute;\n        left:-100px;\n        top: -100px;\n        background-color: #EAEAEB;\n        width : calc(100% + 200px);\n        height : calc(100% + 200px);\n        overflow: hidden;\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          &.canvas_drawing {\n            cursor : url(./static/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(./static/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(./static/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(./static/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(./static/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(./static/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(./static/icons/split.png) 0 0,auto;\n          }\n        }\n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return m=function(){return n},n}function g(){var n=h(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return g=function(){return n},n}function y(){var n=h(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return y=function(){return n},n}function w(){var n=h(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return w=function(){return n},n}function x(){var n=h(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return x=function(){return n},n}var S=(0,t(79874).rU)((function(n){var e=n.css;return{root:e(p()),content:e(v()),topBtns:e(b()),canvas3d:e(_()),canvas_pannel:e(m(),(0,f.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:e(g()),backBtn:e(y()),forwardBtn:e(w()),schemeNameSpan:e(x())}})),k=t(62460),j=t(20074),I=t(53589),O=t(67869),z=t(64186),q=t(32184),A=t(1870),D=t(65640);function E(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function C(n,e,t,r,a,o,i){try{var c=n[o](i),s=c.value}catch(n){return void t(n)}c.done?e(s):Promise.resolve(s).then(r,a)}function M(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function N(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){M(n,e,t[e])}))}return n}function B(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,a,o=[],i=!0,c=!1;try{for(t=t.call(n);!(i=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);i=!0);}catch(n){c=!0,a=n}finally{try{i||null==t.return||t.return()}finally{if(c)throw a}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return E(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return E(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(n,e){var t,r,a,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=c(0),i.throw=c(1),i.return=c(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(s){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(o=0)),o;)try{if(t=1,r&&(a=2&c[0]?r.return:c[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,c[1])).done)return a;switch(r=0,a&&(c=[2&c[0],a.value]),c[0]){case 0:case 1:a=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,r=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(a=o.trys,(a=a.length>0&&a[a.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!a||c[1]>a[0]&&c[1]<a[3])){o.label=c[1];break}if(6===c[0]&&o.label<a[1]){o.label=a[1],a=c;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(c);break}a[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],r=0}finally{t=a=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,s])}}}var R=(0,o.observer)((function(){var n=(0,i.B)().t,e=B((0,a.useState)(""),2),t=(e[0],e[1]),o=B((0,a.useState)(-2),2),f=o[0],h=(o[1],B((0,a.useState)(!1),2)),p=h[0],v=h[1],b=(0,c.P)(),_=S().styles;s.nb.UseApp(u.e.AppName),s.nb.instance&&(s.nb.t=n);var m=function(){s.nb.instance&&(s.nb.instance.bindCanvas(document.getElementById("cad_canvas")),s.nb.instance.update())},g=function(){var n=s.nb.instance,e=null;n._attached_elements||(n._attached_elements={}),n._attached_elements.ws||(n._attached_elements.ws=new WebSocket("ws://localhost:37661/",["layoutai.3vjia.com"])),(e=n._attached_elements.ws).onopen=function(n){x(),v(!0)},e.onmessage=function(){var n,t=(n=function(n){var t,r,a,o,i,c,s,u,l,d,f,h,p,v,b;return P(this,(function(_){switch(_.label){case 0:return t=n.data,D.log("收到请求",t),(r=JSON.parse(t)).queryId?(o=(null===(a=r.queryData)||void 0===a?void 0:a.method)||"",r.queryResult={},"queryModelById"!==o?[3,6]:(c=null===(i=r.queryData)||void 0===i?void 0:i.modelId)?[4,k.VF.getDesignMaterialInfoByIds([c])]:[3,5]):[3,10];case 1:return(s=_.sent()[0]||null)?[3,2]:(r.queryResult={error:"未获得DesignInfo"},[3,5]);case 2:return[4,new Promise((function(n,e){j.R.MakeMesh3DWithDesignMaterialInfo(s,{target_size:{length:2e3,width:600,height:2400},category:"衣柜",onLoadEnd:function(e){n(e)}}).then((function(e){e||n(null)})).catch((function(n){e(n)}))}))];case 3:return(u=_.sent())?(l=new I.r,(d=new O.Z58).add(u),[4,new Promise((function(n,e){l.parse(d,(function(e){var t=new FileReader;t.readAsDataURL(e),t.onloadend=function(e){n(t.result)}}),{binary:!0,trs:!1,blobDirectly:!0})})).catch((function(n){return null}))]):[3,5];case 4:f=_.sent(),r.queryResult={gltf:f},_.label=5;case 5:return e.send(JSON.stringify(r)),[3,10];case 6:if("queryCabinetList"!==o)return[3,10];h=r.queryData,p=N({trace_id:(0,A.w_)("ws"),category:["sliding_door_wardrobe","swing_door_wardrobe"],organ_id_list:["C00000022"],label_dict:{long_clothes_num:2,short_clothes_num:3,out_drawer_num:1,stack_area_ratio:.4,desk:0},material_id_list:"",depth:600,height:2400,width:2250},h),_.label=7;case 7:return _.trys.push([7,9,,10]),[4,(0,z.Ap)({method:"post",url:"/api/turing/cabinet_label/api/query/v1",data:N({},p),timeout:6e4})];case 8:return 0==(v=_.sent()).success?[2,[]]:(b=(null==v?void 0:v.data)||[],r.queryResult={materialInfos:b},e.send(JSON.stringify(r)),[3,10]);case 9:return _.sent(),e.send(JSON.stringify(r)),[3,10];case 10:return[2]}}))},function(){var e=this,t=arguments;return new Promise((function(r,a){var o=n.apply(e,t);function i(n){C(o,r,a,i,c,"next",n)}function c(n){C(o,r,a,i,c,"throw",n)}i(void 0)}))});return function(n){return t.apply(this,arguments)}}(),e.onclose=function(t){D.log("WebSocket closed",t),n._attached_elements.ws=null,e=null,v(!1),w()}},y=function(){var n=s.nb.instance;n._attached_elements||(n._attached_elements={}),D.log("trying..."),n._attached_elements.ws?x():g()},w=function(){var n=s.nb.instance;n._attached_elements||(n._attached_elements={}),n._attached_elements.start_checking_ws||(n._attached_elements.start_checking_ws=window.setInterval(y,2e3),g())},x=function(){var n=s.nb.instance;n._attached_elements||(n._attached_elements={});var e=n._attached_elements.start_checking_ws;e&&(e>0&&window.clearInterval(e),delete n._attached_elements.start_checking_ws)};return(0,a.useEffect)((function(){window.addEventListener("resize",m),m(),w(),s.nb.instance&&(s.nb.instance.initialized||(s.nb.instance.init(),s.nb.RunCommand(u.f.AiCadMode),s.nb.instance.layout_graph_solver._is_query_server_model_rooms=!1,s.nb.instance.layout_container.drawing_figure_mode=q.qB.Texture,s.nb.instance.prepare().then((function(){})),s.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),s.nb.instance.update()),s.nb.on(d.U.LayoutSchemeOpened,(function(n){t(n.name)}))}),[]),(0,r.jsxs)("div",{className:_.root,children:[(0,r.jsxs)("div",{id:"Canvascontent",className:_.content,children:[(0,r.jsx)("div",{className:"3d_container "+_.canvas3d,style:{zIndex:f},children:(0,r.jsx)(l.A,{defaultViewMode:6})}),(0,r.jsx)("div",{id:"body_container",className:_.canvas_pannel,children:(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){b.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){b.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t);b.homeStore.setInitialDistance(r/b.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t)/b.homeStore.initialDistance;r>5?r=5:r<.05&&(r=.05),b.homeStore.setScale(r),s.nb.DispatchEvent(s.n0.scale,r)}},onTouchEnd:function(){b.homeStore.setInitialDistance(null)}})})]}),(0,r.jsx)("div",{className:_.topBtns,children:(0,r.jsx)("button",{onClick:g,children:p?"已连结websocket":"连接WebSockect"})})]})}))}}]);