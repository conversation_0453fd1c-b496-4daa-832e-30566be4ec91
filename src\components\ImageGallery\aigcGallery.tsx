import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import useStyles from "./style"
import { AIGCService } from "@/Apps/LayoutAI/Services/AIGC/AIGCService";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
interface I_ImageResultData {
    diffuseImage: string;
    imageResult: string;
    stylizationImage: string;
}
let updatedCount = 0;
const AigcGallery: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const [checkState, setCheckState] = useState<{ [key: string]: string }>({});
    const timerRef = useRef(null);
    const [imageList, setImageList] = useState<I_ImageResultData[]>([]);

    const [gallery_type, setGalleryType] = useState<number>(0);
    const MaxUpdated = 5;

    const updateGallery = async () => {
        if (updatedCount >= MaxUpdated) {

            return;
        }
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;


        let list = await AIGCService.instance.queryImageList(container._scheme_id);

        // console.log(list);
        setImageList(list.result || []);
        updatedCount += 1;
        console.log(updatedCount);

        if (updatedCount < MaxUpdated) {
            setTimeout(() => updateGallery(), 4000);
        }

    }
    const start_updating = () => {

        updatedCount = 0;
        updateGallery();


    }

    const download_imgurl = (url: string, filename: string) => {
        let a_link = document.createElement("a") as HTMLAnchorElement;

        a_link.download = filename;
        a_link.href = url;
        a_link.target = "_blank";
        document.body.appendChild(a_link);
        a_link.click()
        document.body.removeChild(a_link);
    }

    useEffect(() => {

        LayoutAI_App.on(EventName.AigcImageUpdated, (t: number) => {
            if (t) {
                start_updating();
            }
        });
        LayoutAI_App.on(EventName.ShowAigcGallery, (t: number) => {

            console.log(t, gallery_type);
            if (t < 0) {

                setGalleryType(1);

            }
            else {
                setGalleryType(t);

            }
        })
    }, []);

    useEffect(() => {
        if (gallery_type > 0) {
            start_updating();
        }

    }, [gallery_type])
    const SideGallery = () => {
        return <div id="AIGC_Gallery" className={styles.side_gallery}>

            <div className={styles.hide_gallery} onClick={() => setGalleryType(0)}>
                {t("收起相册")}
            </div>
            <div className={"sub_gallery"}>
                {imageList.map((data, index) => <div className="gallery_div" key={"aigc_key_" + index}>
                    <img src={data.imageResult || data.diffuseImage}></img>
                    <div className="i_btns">
                        <div className={"iconfont icondownload"} onClick={() => {
                            download_imgurl(data.imageResult || data.diffuseImage, "AIGC");
                        }}></div>
                    </div>

                </div>)}
            </div>




        </div>
    }


    return <>
        {gallery_type == 1 && SideGallery()}
    </>
}

export default AigcGallery;