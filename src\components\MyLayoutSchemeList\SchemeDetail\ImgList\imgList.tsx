import React, { useEffect, useState } from 'react';
import { Pagination, Image } from '@svg/antd';
import type { PaginationProps } from '@svg/antd';
import useStyles from './style';

import NoData from '../../NoData/noData';
import { filesListByPage, getAiDrawImgList } from '../../services/scheme';

const ImgList: React.FC<{ tabType: string; id: any }> = ({ tabType, id }) => {
    const {styles} = useStyles()
    const [list, setList] = useState<any[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalItems, setTotalItems] = useState(0);

    let interval: NodeJS.Timeout; //ai绘图定时器

    const getImgList = async () => {
        const params: any = {
        pageIndex: currentPage,
        pageSize: 20,
        fileType: tabType,
        layoutSchemeId: id.id,
        };

        const res = await filesListByPage(params);
        console.log(res);
        if (res.success) {
        setList(res.result.result);
        setTotalItems(res.result.recordCount);
        }
    };

    const aiDrawImgList = async () => {
        let res = await getAiDrawImgList({
        layoutSchemeId: id.id,
        pageIndex: currentPage,
        pageSize: 20,
        orderBy: "create_date desc"
        });
        if (res.success) {
        setList(res.result.result);
        setTotalItems(res.result.recordCount);

        // 检查每一项的 state
        // const allCompleted = res.result.result.every((item: any) => item.state === 1);
        // if (allCompleted) {
        //   console.log('关闭定时器');
        //   clearInterval(interval); // 关闭定时器
        // }
        }
    };

    const getData = () => {
        if (tabType === 'aidraw') {
        aiDrawImgList();

        // interval = setInterval(() => {
        //   aiDrawImgList(); // 每5秒执行一次
        // }, 5000);

        // return () => clearInterval(interval); // 清理计时器
        } else {
        getImgList();
        }
    };

    useEffect(() => {
        getData();
    }, [currentPage]);

    useEffect(() => {
        setCurrentPage(1);
        console.log('tabType', tabType);
        getData();
    }, [tabType]);

    const CardPagination: React.FC = () => {
        const onChange: PaginationProps['onChange'] = (page) => {
        console.log(page);
        setCurrentPage(page);
        };

        return (
        <Pagination current={currentPage} onChange={onChange} total={totalItems} pageSize={20} showSizeChanger={false} />
        );
    };

    return (
        <div>
        {list.length === 0 && (
            <div className={styles.noDataContainer}>
            <NoData />
            </div>
        )}
        {list.length > 0 && (
            <>
            <div className={styles.cardContainer}>
                {list.map((item) => (
                <div className={styles.imageContainer} key={item.id}>
                    <Image className='image_item' src={tabType === 'aidraw' ? item.imageResult : item.fileUrl} />
                </div> 
                ))}
            </div>
            <div className={styles.PageContainer}>
                <CardPagination />
            </div>
            </>
        )}
        </div>
    );
};

export default ImgList;
