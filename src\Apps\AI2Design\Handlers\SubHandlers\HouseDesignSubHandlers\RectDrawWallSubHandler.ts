import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { EventName } from "@/Apps/EventSystem";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZDistanceDimension } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Vector3, Vector3Like } from "three";
import { HouseDesignSubHandler } from "./HouseDesignBaseHandler";
import { T_AddWallOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_AddWallOperationInfo";

// 墙体位置类型常量
export const WallLocationType = {
    Center: "中心线",
    Inline: "内线",
    Outline: "外线"
}

// 矩形画墙体逻辑：
// 1. 点击左键，开始绘制墙体记录一个起点
// 2. 再次点击左键，点击的点为终点，一个矩形房间
// 3. 点击右键，结束结束当前绘制矩形
// 4. 双击右键，退出绘制墙体模式
// 5.长按左键、中键，移动画布
export class RectDrawWallSubHandler extends HouseDesignSubHandler {
    // 常量
    private readonly MIN_WALL_SIZE = 60;
    private readonly MIN_TIME_TO_MOVE_CANVAS = 300;
    private readonly ADSORB_DISTANCE = 50;
    private readonly CENTER_ADSORB_DISTANCE = 60;
    
    // 墙体绘制点
    _wall_end_points: Vector3[] = [];
    drawing_wall_rects: ZRect[] = [];
    _candidate_point: Vector3 = null;
    _candidate_point_type: string = "";
    
    // 对齐和吸附相关
    align_pos0: Vector3 = null;
    align_pos1: Vector3 = null;
    drawEdage: ZEdge = null;
    drawPoint: Vector3 = null;
    centerpoint: Vector3 = null;
    
    // 鼠标状态
    private isMouseDown: boolean = false;
    private mouseDownTime: number = 0;
    private mouseButton: number = -1;
    private closeNum: number = 0;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this._candidate_point = new Vector3();
        this._candidate_point_type = WallLocationType.Center;
    }

    get wall_rects(): ZRect[] {
        return this.container.getCandidateRects(["Wall"]);
    }
    
    enter(state?: number): void {
        super.enter(state);

        LayoutAI_App.emit(EventName.ShowWallTopMenu, true);
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit_M(EventName.SelectingTarget, null);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, true);
        
        this.cleanSelection();
        this.manager.layout_container.cleanDimension();
        this.manager.setCanvasCursorState(LayoutAI_CursorState.Drawing);
        
        if (!this._candidate_point_type) {
            this._candidate_point_type = WallLocationType.Center;
        }
        
        LayoutAI_App.DispatchEvent(LayoutAI_Events.setTopWallMenuProps, { 
            width: this._thickness.toString(), 
            lineValue: this._candidate_point_type 
        });

        this.update();
    }
    
    leave(state?: number): void {
        super.leave(state);
        this.updateCandidateRects();
        
        LayoutAI_App.emit(EventName.ShowWallTopMenu, false);
        LayoutAI_App.emit_M(EventName.ShowFigureMenuList, false);
        LayoutAI_App.emit(EventName.ShowSubHandlerBtn, false);

        this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        this.reset();
    }

    // 重置状态
    private reset(): void {
        this.drawing_wall_rects = [];
        this._wall_end_points = [];
        this.closeNum = 0;
        this.align_pos0 = null;
        this.align_pos1 = null;
        this.drawEdage = null;
        this.drawPoint = null;
        this.centerpoint = null;
    }

    // 吸附功能
    adsorbent(pos: Vector3Like): void {
        const t_pos: Vector3 = new Vector3().copy(pos);
        this.drawEdage = null;
        this.drawPoint = null;
        this.centerpoint = null;
        
        let dist = this.ADSORB_DISTANCE;
        
        for (const rect of this.wall_rects) {
            for (const edge of rect.edges) {
                // 边缘吸附
                const pp = edge.projectEdge2d(t_pos);
                if (Math.abs(pp.y) < Math.abs(dist)) {
                    dist = pp.y;
                    this.drawEdage = edge;
                    this.drawPoint = edge.unprojectEdge2d({ x: pp.x, y: 0 });
                }
                
                // 中点吸附
                if (Math.abs(t_pos.distanceTo(edge.center)) < this.CENTER_ADSORB_DISTANCE) {
                    this.drawPoint = edge.center.clone();
                    this.centerpoint = edge.center.clone();
                }
            }
        }
    }

    // 对齐功能
    alignment(pos: Vector3Like): void {
        if (this.drawing_wall_rects.length === 0) return;
        
        this.align_pos0 = null;
        this.align_pos1 = null;
        
        const dv = this.drawing_wall_rects[this.drawing_wall_rects.length - 1].dv;
        let dist = this.ADSORB_DISTANCE;
        
        for (const rect of this.wall_rects) {
            // 墙的宽度大于某个值才去对齐
            if (rect._w < 100) continue;
            
            for (const edge of rect.edges) {
                if (Math.abs(dv.dot(edge.nor)) < 0.1) continue;
                
                const pp = edge.projectEdge2d(this._candidate_point);
                if (Math.abs(pp.y) < Math.abs(dist)) {
                    dist = pp.y;
                    const align_pos = edge.unprojectEdge2d({ x: pp.x, y: 0 });
                    this._candidate_point.copy(align_pos);
                    this.align_pos0 = align_pos.clone();
                    this.align_pos1 = edge.start_pos.clone();
                }
            }
        }
    }

    // 更新候选点
    udpateCandidatePoint(pos: Vector3Like): void {
        const t_pos: Vector3 = new Vector3().copy(pos);
        this._candidate_point.copy(this.drawPoint || t_pos);
    }
    
    // 鼠标事件处理
    onmousedown(ev: I_MouseEvent): void {
        this.isMouseDown = true;
        this.mouseButton = ev.button;

        if (ev.button === 0 || ev.button === 1) {
            this.closeNum = 0;
            this.mouseDownTime = Date.now();
        }
        this.drawPoint = null;
    }

    onmousemove(ev: I_MouseEvent): void {
        const pos = { x: ev.posX, y: ev.posY, z: 0 };

        // 按下鼠标拖动时
        if (this.isMouseDown && (this.mouseButton === 0 || this.mouseButton === 1)) {
            this._cad_mode_handler._is_moving_element = false;
            this.update();
            return;
        }

        this._cad_mode_handler._is_moving_element = true;
        this.udpateCandidatePoint(pos);

        // 添加墙的时候的对齐或吸附功能
        if (this._candidate_point) {
            if (this._wall_end_points.length > 0) {
                this.alignment(pos);
            } else {
                this.adsorbent(pos);
            }
        }

        this.updateDrawingRects();
        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        if (!this.isMouseDown) return;

        // 检查是否是长按移动画布的情况
        const isLongPress = Date.now() - this.mouseDownTime > this.MIN_TIME_TO_MOVE_CANVAS;

        this.isMouseDown = false;
        this.mouseDownTime = 0;
        this.mouseButton = -1;
        this.align_pos0 = null;
        this.align_pos1 = null;
        
        this._cad_mode_handler._is_moving_element = false;
        
        // 右键处理
        if (ev.button === 2) {
            this.closeNum++;
            this._wall_end_points = [];
            this.drawing_wall_rects = [];
            
            if (this.closeNum >= 2) {
                this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);
            }

            this.update();
            return;
        }

        // 左键处理 - 只在非长按的情况下记录点位
        if (ev.button === 0 && this._candidate_point && !isLongPress) {
            // 第一次点击，记录起点
            if (this._wall_end_points.length === 0) {
                this._wall_end_points.push(this._candidate_point.clone());
                this.updateDrawingRects();
                this.update();
                return;
            }
            // 第二次点击，创建矩形房间
            else if (this._wall_end_points.length === 1) {
                // 更新绘制预览
                this.updateDrawingRects();
                
                // 验证墙体合法性
                if (!this.validateWalls()) {
                    return;
                }
                
                // 创建墙体实体
                this.createWallEntities();
                
                // 清空并重置
                this.drawing_wall_rects = [];
                this._wall_end_points = [];
                
                this.update();
            }
        }
    }

    // 验证墙体合法性
    private validateWalls(): boolean {
        // 检查是否有足够数量的墙体
        if (this.drawing_wall_rects.length < 4) {
            console.warn("墙体矩形创建不完整，需要四面墙");
            return false;
        }
        
        // 检查每个墙体是否足够长
        if (this.drawing_wall_rects.some(rect => rect._w < this.MIN_WALL_SIZE)) {
            console.warn("墙体太小，无法创建");
            return false;
        }
        
        return true;
    }
    
    // 创建墙体实体
    private createWallEntities(): void {
        for (const rect of this.drawing_wall_rects) {
            const info = new T_AddWallOperationInfo(this.manager);
            const entity = TWall.getOrMakeEntityOfCadRect(rect);
            info._entity = entity as TWall;
            info.redo(this.manager);
            this.manager.appendOperationInfo(info);
        }
    }

    // 更新绘制的矩形墙体
    updateDrawingRects(): void {
        this.drawing_wall_rects = [];

        if (!this._candidate_point || this._wall_end_points.length === 0) return;

        const startPoint = this._wall_end_points[0].clone();
        const endPoint = this._candidate_point.clone();
        
        // 确保startPoint和endPoint不是同一点
        if (startPoint.distanceTo(endPoint) < 10) return;
        
        // 获取墙体厚度
        const thickness = this._thickness || 240;
        const halfThickness = thickness / 2;  
        
        // 根据定位线类型调整中心位置
        this.adjustPointsByLocationType(startPoint, endPoint, halfThickness);
        
        // 计算矩形的四个角点
        const minX = Math.min(startPoint.x, endPoint.x);
        const maxX = Math.max(startPoint.x, endPoint.x);
        const minY = Math.min(startPoint.y, endPoint.y);
        const maxY = Math.max(startPoint.y, endPoint.y);
        
        // 创建四面墙体
        this.createFourWalls(minX, maxX, minY, maxY, halfThickness);
    }
    
    // 根据定位线类型调整点位置
    private adjustPointsByLocationType(startPoint: Vector3, endPoint: Vector3, halfThickness: number): void {
        // 中心线模式不需要调整
        if (this._candidate_point_type === WallLocationType.Center) {
            return;
        }
        
        // 计算起点到终点的方向向量
        const direction = new Vector3().subVectors(endPoint, startPoint);
        
        // 正交化 - 分解为X轴和Y轴方向
        const xDirection = Math.sign(direction.x); // 1 或 -1 表示X轴方向
        const yDirection = Math.sign(direction.y); // 1 或 -1 表示Y轴方向
        
        // 调整起点和终点
        if (this._candidate_point_type === WallLocationType.Outline) {
            // 外线模式：向矩形外部偏移
            startPoint.x += xDirection * halfThickness;
            startPoint.y += yDirection * halfThickness;
            endPoint.x -= xDirection * halfThickness;
            endPoint.y -= yDirection * halfThickness;
        } else if (this._candidate_point_type === WallLocationType.Inline) {
            // 内线模式：向矩形内部偏移
            startPoint.x -= xDirection * halfThickness;
            startPoint.y -= yDirection * halfThickness;
            endPoint.x += xDirection * halfThickness;
            endPoint.y += yDirection * halfThickness;
        }
    }
    
    // 创建四面墙
    private createFourWalls(minX: number, maxX: number, minY: number, maxY: number, halfThickness: number): void {
        // 底部墙体 (左到右)
        this.createWallRect(
            new Vector3(minX - halfThickness, minY, 0), 
            new Vector3(maxX - halfThickness, minY, 0) 
        );
        
        // 右侧墙体 (下到上)
        this.createWallRect(
            new Vector3(maxX, minY - halfThickness, 0),
            new Vector3(maxX, maxY - halfThickness, 0)
        );
        
        // 顶部墙体 (右到左)
        this.createWallRect(
            new Vector3(maxX + halfThickness, maxY, 0),
            new Vector3(minX + halfThickness, maxY, 0)  
        );
        
        // 左侧墙体 (上到下)
        this.createWallRect(
            new Vector3(minX, maxY + halfThickness, 0),
            new Vector3(minX, minY + halfThickness, 0) 
        );
    }

    // 创建单个墙体矩形
    createWallRect(start_pos0: Vector3, start_pos1: Vector3): void {
        // 计算方向向量和法向量
        const direction = start_pos1.clone().sub(start_pos0).normalize();
        const nor = direction.clone().cross({ x: 0, y: 0, z: 1 });
        
        // 计算墙体属性
        const thickness = this._thickness || 240;
        const center = start_pos0.clone().add(start_pos1).multiplyScalar(0.5);
        const ww = start_pos0.distanceTo(start_pos1);
        
        // 创建墙体矩形
        const rect = new ZRect(ww, thickness);
        rect.nor = nor;
        rect.rect_center = center;
        
        // 设置墙体属性
        TBaseEntity.set_polygon_type(rect, 'Wall');
        this.drawing_wall_rects.push(rect);
    }

    // 绘制画布
    drawCanvas(): void {
        if (this.drawing_wall_rects.length > 0) {
            this.drawWallRects();
        }
        
        this.drawGuideLines();
    }
    
    // 绘制墙体矩形
    private drawWallRects(): void {
        // 绘制所有墙体
        this.painter.fillStyle = "#3D9EFF";
        this.painter.strokeStyle = "#2b2b2b";
        this.painter.fillPolygons(this.drawing_wall_rects);
        this.painter.strokePolygons(this.drawing_wall_rects);

        // 显示墙体连接点
        if (this.drawing_wall_rects.length === 4) {
            this.drawCornerPoints();
        }

        // 绘制尺寸线
        if (this._wall_end_points.length > 0 && this._candidate_point) {
            this.drawDimensionLines();
        }
    }
    
    // 绘制角点
    private drawCornerPoints(): void {
        this.painter.strokeStyle = "#f23dd1";
        
        if (!this._wall_end_points[0] || !this._candidate_point) return;
        
        // 获取矩形的四个角点
        const startPoint = this._wall_end_points[0];
        const endPoint = this._candidate_point;
        
        // 计算矩形的四个角点
        const minX = Math.min(startPoint.x, endPoint.x);
        const maxX = Math.max(startPoint.x, endPoint.x);
        const minY = Math.min(startPoint.y, endPoint.y);
        const maxY = Math.max(startPoint.y, endPoint.y);
        
        // 在四个角点绘制小圆点标记
        const radius = 5 / this.painter._p_sc;
        const corners = [
            new Vector3(minX, minY, 0), // 左下
            new Vector3(maxX, minY, 0), // 右下
            new Vector3(maxX, maxY, 0), // 右上
            new Vector3(minX, maxY, 0)  // 左上
        ];
        
        corners.forEach(corner => {
            this.painter.drawPointCircle(corner, radius, 3);
        });
    }
    
    // 绘制尺寸线
    private drawDimensionLines(): void {
        const startPoint = this._wall_end_points[0];
        const endPoint = this._candidate_point;
        
        // 计算矩形的宽和高
        const width = Math.abs(endPoint.x - startPoint.x);
        const height = Math.abs(endPoint.y - startPoint.y);
        
        // 只在矩形足够大时绘制尺寸标注
        if (width > 150 || height > 150) {
            this.painter.strokeStyle = "#282828";
            this.painter.fillStyle = "#282828";
            
            // 计算矩形的四个角点
            const minX = Math.min(startPoint.x, endPoint.x);
            const maxX = Math.max(startPoint.x, endPoint.x);
            const minY = Math.min(startPoint.y, endPoint.y);
            const maxY = Math.max(startPoint.y, endPoint.y);
            
            // 绘制宽度尺寸线（下方）
            if (width > 150) {
                const dim1 = new ZDistanceDimension(
                    new Vector3(minX, maxY, 0).addScaledVector(new Vector3(0, -1, 0), 400),
                    new Vector3(maxX, maxY, 0).addScaledVector(new Vector3(0, -1, 0), 400)
                );
                dim1.nor.set(0, 1, 0);
                dim1.offset_len = 0;
                dim1._font_size = 2 / this.painter._p_sc;
                dim1.text_offset_len = 7 / this.painter._p_sc;
                this.painter.drawDimension(dim1);
            }
            
            // 绘制高度尺寸线（右侧）
            if (height > 150) {
                const dim2 = new ZDistanceDimension(
                    new Vector3(minX, minY, 0).addScaledVector(new Vector3(1, 0, 0), 400),
                    new Vector3(minX, maxY, 0).addScaledVector(new Vector3(1, 0, 0), 400)
                );
                dim2.nor.set(1, 0, 0);
                dim2.offset_len = 0;
                dim2._font_size = 2 / this.painter._p_sc;
                dim2.text_offset_len = 7 / this.painter._p_sc;
                this.painter.drawDimension(dim2);
            }
        }
    }
    
    // 绘制辅助线
    private drawGuideLines(): void {
        // 绘制对齐线
        if (this.align_pos0 && this.align_pos1) {
            this.painter._context.lineWidth = 1;
            this.painter.drawLineSegment(this.align_pos0, this.align_pos1, '#f23dd1');
        }

        // 绘制边缘吸附线
        if (this.drawEdage && this.drawPoint) {
            this.painter._context.lineWidth = 1;
            this.painter.drawLineSegment(this.drawEdage.center, this.drawPoint, '#f23dd1');
            const radius = 6 / this.painter._p_sc;
            this.painter.strokeStyle = "#f23dd1";
            this.painter.drawPointCircle(this.drawPoint, radius, 4);
        }

        // 绘制候选点和端点
        if (this._candidate_point && this._wall_end_points[this._wall_end_points.length - 1]) {
            this.painter._context.lineWidth = 1;
            const radius = 3 / this.painter._p_sc;
            this.painter.strokeStyle = "#f23dd1";
            this.painter.drawPointCircle(this._candidate_point, radius, 3);
            this.painter.drawPointCircle(this._wall_end_points[this._wall_end_points.length - 1], radius, 3);
        }

        // 绘制中点标签
        if (this.centerpoint) {
            this.painter.drawRectText(
                new Vector3(this.centerpoint.x + (20 / this._p_sc), this.centerpoint.y - (20 / this._p_sc), 0),
                30, 20, 1, "中点"
            );
        }
    }
}
