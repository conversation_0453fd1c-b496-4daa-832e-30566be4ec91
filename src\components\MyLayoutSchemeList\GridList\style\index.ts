import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
cardContainer: css`
    display: flex;
    flex-wrap: wrap;
    height: auto;
    margin: 0 -20px;
`,
card: css`
    margin: 20px;
    border: 1px solid #e0e0e0;
    width: calc((100% / 3) - 40px) !important;
    border-radius: 12px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
    position: relative;
    .iconHidden {
    visibility: visible;
    }
    &:hover {
    border: 1px solid #e0e0e0;
    box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.04);
    }
`,
buildingName: css`
    font-size: 14px;
    font-weight: bold;
`,
focusInput: css`
    height: 22px;
    border-radius: 0;
    padding: 0;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
    &:focus {
    background: #f4f5f5;
    border: none;
    box-shadow: 0 0 0 0px rgba(255, 255, 255, 0) !important;
    outline: none;
    }
`,
footerContainer: css`
    display: flex;
    justify-content: space-between;
    /* .time {
    font-size: 12px;
    color: #666;
    margin: 5px 0;
    white-space: nowrap;
    } */
`,
time: css`
    font-size: 12px;
    color: #666;
    margin: 5px 0;
    white-space: nowrap;
`,
roomAreaContainer: css`
    display: flex;
    align-items: center;
    margin: 5px 0;
`,
roomType: css`
    font-size: 12px;
    color: #666;
`,
buttonContainer: css`
    display: flex;
    align-items: center;
    margin-left: 5px;
    /* .ellipsis {
        border-radius: 5.5px;
        svg {
            width: 22px;
            height: 22px;
        }
        &:hover {
            background: #F4F5F5;
        }
    } */
`,
ellipsis: css`
    border-radius: 5.5px;
    svg {
        width: 22px;
        height: 22px;
    }
    &:hover {
        background: #F4F5F5;
    }
`,
dropdownMenu: css`
    color: #282828;
    font-family: 'PingFang SC';
    font-weight: normal;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    text-align: left;
    width: 203px;
    border-radius: 8px;
    padding-left: 22px;
`,
iconHidden: css`
    visibility: hidden;
`,
separator: css`
    width: 2px;
    height: 15px;
    background-color: #ccc;
    margin: 0 6px;
`,
imageContainer: css`
    display: flex !important;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    aspect-ratio: 4 / 3;
    background: #38295707;
    img {
        border-radius: 12px 12px 0px 0px;
        width: auto;
        height: 100%;
    };
`,
overlay: css`
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(200, 200, 200, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
`,
// imageContainerHover: css`
//     &:hover .overlay {
//         opacity: 1;
//     }
// `,
createButton: css`
    width: 148px;
    height: 32px;
    color: #ffffff;
    font-family: PingFang SC;
    font-weight: normal;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
`,
editButton: css`
    display: block;
    width: 148px;
    height: 32px;
    margin: 10px 0;
    color: #282828;
    font-family: PingFang SC;
    font-weight: normal;
    font-size: 14px;
    line-height: 1.57;
    letter-spacing: 0px;
    border: none;
    cursor: pointer;
    text-align: center;
    transition: background 0.3s;
    border-radius: 6px;
    background-color: #ffffff;
    box-shadow: 0px 6px 20px 0px #00000014;
    &:hover {
    margin: 10px 0;
    color: #282828 !important;
    }
`,
deleteModal: css`
    .deleteMessage {
    margin: 40px 0;
    p:last-of-type {
        margin: 2px 0;
        color: #7d7d7d;
    }
    p {
        margin: 2px 0;
    }
    }
`,
}));