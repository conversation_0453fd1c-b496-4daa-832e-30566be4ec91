import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
    cardContainer: css`
        display: flex;
        flex-wrap: wrap;
        height: auto;
    `,
    imageContainer: css`
        display: flex !important;
        justify-content: center;
        align-items: center;
        aspect-ratio: 4 / 3;
        width: 288px;
        margin: 20px;
        border: none;
        border-radius: 12px;
        background: #F4F5F5;
        overflow: hidden;
        position: relative;

        .image_item {
        border-radius: 12px;
        max-width: 100%;
        max-height: 100%;
        }

        @media screen and (min-width: 1057px) {
        width: calc((100% / 3) - 40px) !important;
        }
        @media screen and (min-width: 1335px) {
        width: calc((100% / 4) - 40px) !important;
        }
        @media screen and (min-width: 1633px) {
        width: calc((100% / 5) - 40px) !important;
        }
        @media screen and (min-width: 1921px) {
        width: calc((100% / 6) - 40px) !important;
        }
    `,
    loading_container: css`
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        aspect-ratio: 4 / 3;
        width: 288px;
        margin: 20px;
        border-radius: 12px;
        overflow: hidden;
        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);
        position: relative;

        &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 8px;
        padding: 1px;
        background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        pointer-events: none;
        }

        span {
        color: #959598;
        font-family: PingFang SC;
        font-weight: normal;
        font-size: 12px;
        line-height: 1.67;
        letter-spacing: 0px;
        text-align: left;
        margin-top: 4px;
        }

        @media screen and (min-width: 1057px) {
        width: calc((100% / 3) - 40px) !important;
        }
        @media screen and (min-width: 1335px) {
        width: calc((100% / 4) - 40px) !important;
        }
        @media screen and (min-width: 1633px) {
        width: calc((100% / 5) - 40px) !important;
        }
        @media screen and (min-width: 1921px) {
        width: calc((100% / 6) - 40px) !important;
        }
    `,
    PageContainer: css`
        display: flex;
        justify-content: center;
    `,
    noDataContainer: css`
        margin-top: 10px;
    `,
}));