import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { ZPolygon } from "@layoutai/z_polygon";
import { TAppManagerBase } from "../../AppManagerBase";
export class TCadRoomDecoratesLayer extends TDrawingLayer
{
    


    _target_poly : ZPolygon;

    constructor(manager:TAppManagerBase)
    {
        super(CadDrawingLayerType.CadRoomStrucure,manager);
        this._target_poly = null;
        this._dirty = true;
    
    }


     _updateLayerContent(): void {

    }
    
    onDraw(): void {
       
        if(this.layout_container._drawing_layer_mode === "SingleRoom") return; // 单空间模式 不绘制

        // let entities = this.layout_container.getCandidateEntities(["Furniture"],{target_realtypes:["Decoration"]});


        // for(let entity of entities)
        // {
        //     entity.drawEntity(this.painter,{is_draw_figure:true});
        // }

        for(let room of this.layout_container._rooms)
        {
            room._furniture_list && room._furniture_list.forEach((furniture)=>{
                if(furniture._is_decoration)
                {

                    furniture.drawFigure(this.painter,false,furniture.fill_color)
                }
            })
        }

        this.painter.strokeStyle= "#a42";
        this.painter.fillStyle = "#fff";
    }
}