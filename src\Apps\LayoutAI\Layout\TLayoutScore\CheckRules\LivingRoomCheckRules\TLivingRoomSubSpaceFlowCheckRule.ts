import { <PERSON><PERSON><PERSON>, ZPolygon, ZRect } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions } from "../TCheckRule";
import { Vector3 } from "three";
import { TLivingRoomSplitByPathClassfierCheckRule } from "./TLivingRoomSplitByPathClassfierCheckRule";
import { I_Window } from "../../../IRoomInterface";
import { TLayoutFlowToolUtil } from "../../../TLayoutFlowTool/TLayoutFlowToolUtil";

export class TLivingRoomSubSpaceFlowCheckRule extends TLivingRoomSplitByPathClassfierCheckRule
{
    public static hallwayDist: number = 900;
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    protected setParamConfig(): void {
    }
    
    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        void(figure_element);
        void(main_figures);
        // 1. 计算主动线
        let mainPaths: ZEdge[] = calMainPaths(room, [this.minLivingSpaceRect, this.minDiningSpaceRect]);
        if(mainPaths.length == null)
        {
            return {score: 0};
        }
    }
}

function calMainPaths(room: TRoom, blockRects: ZRect[]): ZEdge[]
{
    // 1. 主动线，入户门->卧室门（入户门不存在的情况下，但是存在多个入户门，则随机选择两个入户门作为主动线)，并且此主动线计算需要剔除掉多边形内部定义好的禁行区域
    let entranceDoors: I_Window[] = room.windows.filter(win => 
        win.type == "Door" && ((win.room_names.includes("入户") && win.room_names.includes("客餐厅")) 
        || (win.room_names.length == 1 && win.room_names.includes("客餐厅"))));
    let bedRoomDoors: I_Window[] = room.windows.filter(win => win.type == "Window" && win.room_names.includes("卧室"));
    if(entranceDoors.length == 0)
    {
        return null;
    }
    let validPolys: ZPolygon[] = room.room_shape._poly.clone().substract_polygons(blockRects);
    let mainPaths: ZEdge[] = calMainPathsByValidPolysAndDoors(room.room_shape._poly, validPolys, entranceDoors, bedRoomDoors);
    return mainPaths;   
  }

function calMainPathsByValidPolysAndDoors(roomPoly: ZPolygon, validPolys: ZPolygon[], entranceDoors: I_Window[], bedRoomDoors: I_Window[]): ZEdge[]
{
    if(entranceDoors.length == 1 && bedRoomDoors.length == 0)
    {
        return null;
    }
    //  1. 确定主动线信息
    let mainPathInfo: any = getMainPathInfo(roomPoly, validPolys, entranceDoors, bedRoomDoors);

    // 2.计算主动线信息，这个主动线只能在有效区域内进行移动
    let mainPathEdges: ZEdge[] = calMainPathEdges(roomPoly, validPolys, mainPathInfo);
    


    return null;
}

function getMainPathInfo(roomPoly: ZPolygon, validPolys: ZPolygon[], entranceDoors: I_Window[], bedRoomDoors: I_Window[]): any
{
    let startEntranceDoor: I_Window = null;
    let endDoor: I_Window = null;
    let farDist: number = null;
    if(bedRoomDoors.length > 0)
    {
        for(let entranceDoor of entranceDoors)
        {
            for(let bedRoomDoor of bedRoomDoors)
            {
                let dist = entranceDoor.center.distanceTo(bedRoomDoor.center);
                let isOverlayWithValid: boolean =  isOverlayValidPolyWithExpandDoor(validPolys, bedRoomDoor);
                if((farDist == null || dist > farDist) && !isOverlayWithValid)
                {
                    farDist = dist;
                    startEntranceDoor == entranceDoor;
                    endDoor = bedRoomDoor;
                }
            }
        }
    }
    else
    {
        for(let entranceDoor of entranceDoors)
        {
            for(let otherEntranceDoor of entranceDoors)
            {
                if(entranceDoor == otherEntranceDoor)
                {
                    continue;
                }
                let dist = entranceDoor.center.distanceTo(otherEntranceDoor.center);
                let isOverlayWithValid: boolean =  isOverlayValidPolyWithExpandDoor(validPolys, otherEntranceDoor);
                if((farDist == null || dist > farDist) && !isOverlayWithValid)
                {
                    farDist = dist;
                    startEntranceDoor == entranceDoor;
                    endDoor = otherEntranceDoor;
                }
            }
        }
    }
    let startPoint: Vector3 = null;
    let endPoint: Vector3 = null;
    let startLayonRoomEdge: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(startEntranceDoor.rect.frontEdge, roomPoly, startEntranceDoor.rect.depth * 2, 0.01);
    if(startLayonRoomEdge)
    {
        let startDoorCenter: Vector3 = startEntranceDoor.rect.frontEdge.center;
        let  startEntranceProjectInfo: any = startLayonRoomEdge.projectEdge2d({x: startDoorCenter.x, y: startDoorCenter.y});
        let onRoomEdgePoint: Vector3 = startLayonRoomEdge.unprojectEdge2d({x: startEntranceProjectInfo.x, y: 0});
        let layonRoomEdgeNeagte: Vector3 = startLayonRoomEdge.nor.clone().negate();
        startPoint = onRoomEdgePoint.clone().add(layonRoomEdgeNeagte.clone().multiplyScalar(TLivingRoomSubSpaceFlowCheckRule.hallwayDist));
    }
    
    let endLayonRoomEdge: ZEdge = TBaseRoomToolUtil.instance.edgeOnPolygon(endDoor.rect.frontEdge, roomPoly, endDoor.rect.depth * 2, 0.01);
    if(endLayonRoomEdge)
    {
        let endDoorCenter: Vector3 = endDoor.rect.frontEdge.center;
        let  endEntranceProjectInfo: any = endLayonRoomEdge.projectEdge2d({x: endDoorCenter.x, y: endDoorCenter.y});
        let onRoomEdgePoint: Vector3 = endLayonRoomEdge.unprojectEdge2d({x: endEntranceProjectInfo.x, y: 0});
        let layonRoomEdgeNeagte: Vector3 = endLayonRoomEdge.nor.clone().negate();
        endPoint = onRoomEdgePoint.clone().add(layonRoomEdgeNeagte.clone().multiplyScalar(10));
    }
    let pathVec: Vector3 = endPoint.clone().sub(startPoint);
    let mainPathInfo: any = {
        pathVec: pathVec,
        pathStartPoint: startPoint,
        pathEndPoint: endPoint,
        entranceDoor: startEntranceDoor
    }
    return mainPathInfo;
}

function isOverlayValidPolyWithExpandDoor(validPolys: ZPolygon[], door: I_Window): boolean
{

    let expandDoorRect: ZRect = door.rect.clone();
    let oldExpandDoorCenter: Vector3 = expandDoorRect.rect_center;
    expandDoorRect.depth += TLivingRoomSubSpaceFlowCheckRule.hallwayDist * 2;
    expandDoorRect.rect_center = oldExpandDoorCenter.clone();
    expandDoorRect.updateRect();
    let intersectPolys: ZPolygon[] = expandDoorRect.clone().intersect_polygons(validPolys);
    if(intersectPolys.length > 0)
    {
        return true;
    }
    return false;
}

function calMainPathEdges(roomPoly: ZPolygon, validPolys: ZPolygon[], mainPathInfo: any): ZEdge[]
{
    let mainPathEdges: ZEdge[] = [];
    let initPathInfo: any = TLayoutFlowToolUtil.instance.parsePathVec(mainPathInfo, roomPoly);
    
    return mainPathEdges;
}