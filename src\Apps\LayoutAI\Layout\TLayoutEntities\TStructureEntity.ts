import { I_SwjEntityBase } from "../../AICadData/SwjLayoutData";
import { ZRect } from "@layoutai/z_polygon";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TPainter } from "../../Drawing/TPainter";
import { AI_PolyTargetType, IRoomEntityRealType, IType2UITypeDict, KeyEntity } from "../IRoomInterface";
import { LayoutAI_App } from "../../../LayoutAI_App";
import { checkIsMobile } from "@/config";


/**
 *  结构件: 烟道、包管、柱子、横梁 等
 */
export class TStructureEntity extends TBaseEntity implements I_SwjEntityBase
{

    room_id ?: number;

    _thickness : number;


    constructor(data:I_SwjEntityBase,rect:ZRect=null)
    {
        super(rect);
        this.importData(data);
        this.type = "StructureEntity";
        this.title = LayoutAI_App.t("结构件信息");
        
        this._priority_for_selection = 3;
    }
    static getOrMakeEntityOfCadRect(rect:ZRect):TStructureEntity
    {
        let entity: TStructureEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            entity = new TStructureEntity({ type: "StructureEntity", realType: rect.ex_prop.label as IRoomEntityRealType || "Pillar" });
            entity._rect = rect;
            if (entity.realType === "Platform") {
                entity.height = 500;

            }
            else if (entity.realType === "Beam") {
                entity.height = 200;
            }
            else {
                entity.height = 2800;
            }
            entity.update();
            rect._attached_elements[KeyEntity] = entity;

        }
        return entity;
    }
    static RegisterGenerators()
    {
        TBaseEntity.RegisterPolyEntityGenerator(AI_PolyTargetType.StructureEntity,this.getOrMakeEntityOfCadRect)
    } 
    get thickness()
    {
        return this._thickness
    }

    set thickness(hh : number)
    {
        this._thickness = hh;
    }

    getRealTypeOptions(): { label: string; value: string; }[] {
        let values:IRoomEntityRealType[] = [];


        values = ["Flue","Pillar","Beam","Platform","Envelope_Pipe"];

        let ans : {label:string,value:string}[] = [];

        for(let val of values)
        {
            ans.push({label:LayoutAI_App.t(IType2UITypeDict[val]) || LayoutAI_App.t(val) , value:IType2UITypeDict[val]||val});
        }
        return ans;
    }
    initProperties(): void {

        super.initProperties();
        let isMobile = checkIsMobile();
        if(isMobile)
        {
            this._ui_properties["width"] = {
                name : LayoutAI_App.t("深度"),
                widget :"LabelItem",
                type: "string",
                min: 1,
                max: 20000,
                disabled : isMobile,
                defaultValue : ''+Math.round(this.width),
                props : {
                    type :"input",
                    suffix :"mm"
                }
            }

            this._ui_properties["length"] = {
                name: LayoutAI_App.t("宽度"),
                widget: "LabelItem",
                type: "string",
                min: 1,
                max : 20000,
                disabled : isMobile,
                defaultValue : ''+Math.round(this.length),
                props : {
                    type :"input",
                    suffix :"mm"
                }
            }

            this._ui_properties["height"] = {
                name : LayoutAI_App.t("高度"),
                widget :"LabelItem",
                type : "string",
                min : 1,
                max : 6000,
                disabled : isMobile,
                defaultValue : ''+Math.round(this.height),
                props : {
                    type :"input",
                    suffix :"mm"
                }
            }   
        }
        
        this._ui_props_keys = ["ui_type","ui_realType","length","width","height"];
        this._bindPropertiesOnChange();

    }


    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {

        let is_draw_figure = options.is_draw_figure || false;
        options.is_draw_figure = false;
        super.drawEntity(painter,options);
        
        if(is_draw_figure)
        {
            painter.strokeStyle = "#000";
            painter.fillStyle = "#fff"
            painter.fillPolygons([this.rect],0.5);
            if(this.realType === "Flue")
            {
                painter.drawDxfBlockInRect("SVJ-烟道",this.rect);
            }
            else if(this.realType === "Pillar")
            {
                painter.fillStyle = "#777";
                painter.strokeStyle = "#000";

                painter.fillPolygon(this.rect,0.5);
                painter.strokePolygons([this.rect]);
            }
            else if(this.realType === "Envelope_Pipe")
            {
                painter.fillStyle = "#777";
                painter.strokePolygons([this.rect]);
            }
            else if(this.realType === "Beam")
            {
                painter.strokeStyle = "#e30";
                painter.strokePolygons([this.rect]);

            }
            else if(this.realType === "Platform")
            {
                painter.strokeStyle = "#000";
                painter.strokePolygons([this.rect]);

            }


        }
    }
}