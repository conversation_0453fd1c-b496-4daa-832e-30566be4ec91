import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import { I_SwjXmlScheme, SchemeSourceType } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { TRoomLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme";
import { TWholeLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TWholeLayoutScheme";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { base64ToUtf8, deflate_to_base64_str, uploadImageToOss } from "@/Apps/LayoutAI/Utils/xml_utils";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { _from, _magiccubeToken, APP_ID, designer_id, designer_open_id, imsApiUrl, openApiHost, visitor_open_id } from "@/config";
import { getCookie, setCookie } from "@/utils";
import { CookieParams } from "@/utils/cookie";
import createProRequest from "@/utils/request/request";
import { t } from "i18next";
import { makeAutoObservable } from "mobx";
import {GenDateUUid } from "@layoutai/z_polygon";
import { BuildingService } from "../../LayoutAI/Services/Basic/BuildingService";
import { LayoutSchemeData } from "@/Apps/LayoutAI/Services/Basic/LayoutSchemeService";
import { SchemeXmlParseService } from "../../LayoutAI/Services/Basic/SchemeXmlParseService";
import { insertClueTrack } from ".";

// 定义状态接口
interface TrialState {
    layoutImage: string | null;
    colorImage: string | null;
    floorPlanUrl: string | null;
    schemeInfo: {
        scheme_name: string | null;
        scheme_id: string | null;
        rooms: Array<{
            area: number;
        }>;
    };
    timestamp: number;
}

// 体验版数据服务
export class TrialDataService {
    private static instance: TrialDataService;
    public readonly TRIAL_STATE_DATA = 'TRIAL_STATE_DATA';
    public readonly TRIAL_LAYOUT_SCHEME_DATA = 'TRIAL_LAYOUT_SCHEME_DATA';

    // 状态数据
    private _floorPlanUrl: string | null = null;
    private _whole_layout_scheme_list: TWholeLayoutScheme[] = [];
    private _layoutImageUrl: string | null = null;
    private _colorImageUrl: string | null = null;
    private _isLoggedIn: boolean = false;
    private USER_ID: string = "464151407629516859";

    private constructor() {
        makeAutoObservable(this);
    }

    public static getInstance(): TrialDataService {
        if (!TrialDataService.instance) {
            TrialDataService.instance = new TrialDataService();
        }
        return TrialDataService.instance;
    }

    public get layout_container(): TLayoutEntityContainer {
        return (LayoutAI_App.instance as TAppManagerBase).layout_container;
    }

    public set whole_layout_scheme_list(value: TWholeLayoutScheme[]) {
        this._whole_layout_scheme_list = value;
    }

    public get whole_layout_scheme_list(): TWholeLayoutScheme[] {
        return this._whole_layout_scheme_list;
    }

    // 获取登录状态
    public get isLoggedIn(): boolean {
        return this._isLoggedIn;
    }

    // 设置登录状态
    public setLoggedIn(value: boolean): void {
        this._isLoggedIn = value;
    }

    // 获取户型图URL
    public getFloorPlanUrl(): string | null {
        return this._floorPlanUrl;
    }

    // 设置方案信息
    public setSchemeInfo(layout_scheme_name: string, layout_scheme_id?: string) {
        if (layout_scheme_id) {
            this.layout_container._layout_scheme_id = layout_scheme_id;
        }
        this.layout_container._scheme_name = layout_scheme_name;
        this.layout_container._layout_scheme_name = layout_scheme_name;
    }

    // 获取房屋信息
    public getSchemeInfo() {
        const schemeName = this.layout_container._layout_scheme_name;
        // 分割方案名称，如果有空格则分为标题和户型信息
        let title = "";
        let houseType = "";
        if (schemeName) {
            const parts = schemeName.split(" ");
            title = parts[0];
            houseType = parts.length > 1 ? parts[1] : "";
        }
        let svjSchemeArea: number = 0;
        this.layout_container._rooms.forEach((room: TRoom) => {
            svjSchemeArea += room.area;
        });
        return {
            scheme_name: title || "未命名",
            house_type: houseType || "",
            scheme_area: svjSchemeArea ? svjSchemeArea.toFixed(2) + "㎡" : "",
        };
    }
    // 检查cookie并判断登录状态
    private async _initLoginStatus(): Promise<void> {
        // 小程序的逻辑
        if (_from == 'wx') {
            if (_magiccubeToken) {
                this.setLoggedIn(true);
                // 要重新设置cookie中的Magiccube-Token, 否则数据会串
                setCookie('Magiccube-Token', _magiccubeToken);
            } else {
                this.setLoggedIn(false);
                let tempCookie = await this.getTempCookie();
                this._setGuestCookie(tempCookie);
                if (!tempCookie) {
                    console.error("Fail to get temp cookie");
                    return
                }
            }
            const currentUrl = window.location.href;
            const url = new URL(currentUrl);
            url.searchParams.delete('from');
            window.history.replaceState({}, document.title, url.toString());
            return;
        }
        // h5的逻辑
        const existingAuthCode = getCookie("authCode");
        let tempCookie = await this.getTempCookie();
        if (!tempCookie) {
            console.error("Fail to get temp cookie");
            return
        }
        if (existingAuthCode) {
            // 获取游客cookie进行比对
            let guestAuthCode = null;
            // 从临时cookie中找到authCode
            for (const cookieString of tempCookie) {
                const cookieData = this._parseCookie(cookieString);
                if (cookieData?.name === "authCode") {
                    guestAuthCode = cookieData.value;
                    break;
                }
            }
            this.setLoggedIn(existingAuthCode != guestAuthCode);
        }
        else {
            // 没有authCode，表示未登录
            this._setGuestCookie(tempCookie);
            this.setLoggedIn(false);
        }
    }

    // 设置游客cookie
    private _setGuestCookie(tempCookie: string[]): void {
        if (tempCookie) {
            // 解析并设置所有cookie
            for (const cookieString of tempCookie) {
                const cookieData = this._parseCookie(cookieString);
                if (cookieData?.name && cookieData?.value) {
                    setCookie(cookieData.name, cookieData.value, cookieData.params);
                }
            }
        }
    }

    // 保存当前状态到localStorage
    public saveCurrentState(): void {
        try {
            if (!this.layout_container) {
                console.error('Layout container is not initialized');
                return;
            }

            const state: TrialState = {
                layoutImage: this._layoutImageUrl,
                colorImage: this._colorImageUrl,
                floorPlanUrl: this._floorPlanUrl,
                schemeInfo: {
                    scheme_name: this.layout_container._layout_scheme_name,
                    scheme_id: this.layout_container._layout_scheme_id,
                    rooms: this.layout_container._rooms.map(room => ({
                        area: room.area
                    }))
                },
                timestamp: Date.now()
            };

            localStorage.setItem(this.TRIAL_STATE_DATA, JSON.stringify(state));
            localStorage.setItem(this.TRIAL_LAYOUT_SCHEME_DATA, JSON.stringify(this.layout_container.toXmlSchemeData()));
            console.log('State saved successfully:', state);
        } catch (error) {
            console.error('Failed to save state:', error);
        }
    }

    // 从localStorage恢复状态
    public async restoreState(): Promise<boolean> {
        try {
            const stateStr = localStorage.getItem(this.TRIAL_STATE_DATA);
            const schemeDataStr = localStorage.getItem(this.TRIAL_LAYOUT_SCHEME_DATA);
            if (!stateStr || !schemeDataStr) {
                console.log('No saved state found');
                return false;
            }

            const state = JSON.parse(stateStr) as TrialState;
            const schemeData = JSON.parse(schemeDataStr) as I_SwjXmlScheme;

            // 恢复所有状态
            this._layoutImageUrl = state.layoutImage;
            this._colorImageUrl = state.colorImage;
            this._floorPlanUrl = state.floorPlanUrl;

            // 恢复方案数据
            if (state.schemeInfo && this.layout_container) {
                this.layout_container._layout_scheme_name = state.schemeInfo.scheme_name;
                this.layout_container._layout_scheme_id = state.schemeInfo.scheme_id;
            }
            this.layout_container.fromXmlSchemeData(schemeData, true, SchemeSourceType.MyScheme);
            console.log("==============");
            console.log("schemeData:", schemeData);
            await this._initLoginStatus();
            localStorage.setItem("TRIAL_LAYOUT_SCHEME_XML_JSON", JSON.stringify(this.layout_container.toXmlSchemeData()));
            console.log('State restored successfully');
            return true;
        } catch (error) {
            console.error('Failed to restore state:', error);
            this.clearStoredState();
            return false;
        }
    }

    // 清除存储的状态
    public clearStoredState(): void {
        try {
            localStorage.removeItem(this.TRIAL_STATE_DATA);
            console.log('State cleared successfully');
        } catch (error) {
            console.error('Failed to clear state:', error);
        }
    }

    // 重写reset方法
    public reset(): void {
        this._floorPlanUrl = null;
        this._whole_layout_scheme_list = [];
        this._layoutImageUrl = null;
        this._colorImageUrl = null;
    }

    // 重写generateLayoutAndColorImage方法
    public async generateLayoutAndColorImage(type: 'layoutImage' | 'colorScreen' = 'layoutImage') {
        if (type === 'layoutImage') {
            this._layoutImageUrl = await uploadImageToOss(
                this.layout_container.saveLayoutSchemeImage(1200, 1000, 0.9),
                "snapShot" + Math.floor(Math.random() * 10000) + ".png"
            );
        } else {
            this._colorImageUrl = await uploadImageToOss(
                this.layout_container.saveLayoutSchemeImage(1200, 1000, 0.9),
                "snapShot" + Math.floor(Math.random() * 10000) + ".png"
            );
        }
    }

    // 重写init方法
    public async init(): Promise<void> {
        await this._initLoginStatus();
        console.log("isLoggedIn:", this.isLoggedIn);

    }

    // 解析cookie字符串
    private _parseCookie(cookieString: string): { name: string, value: string, params?: CookieParams } | null {
        try {
            if (!cookieString) {
                console.warn('解析cookie失败: cookie字符串为空');
                return null;
            }

            const parts = cookieString.split(';').map(part => part.trim());
            const firstPart = parts[0].split('=');

            if (firstPart.length !== 2) {
                console.warn('解析cookie失败: cookie格式不正确');
                return null;
            }

            const name = firstPart[0];
            const value = firstPart[1];

            const params: CookieParams = {};

            // 解析其他参数
            parts.slice(1).forEach(part => {
                const [key, val] = part.split('=').map(s => s.trim());
                switch (key) {
                    case 'Max-Age':
                        params.seconds = parseInt(val);
                        break;
                    case 'Path':
                        params.path = val;
                        break;
                    case 'Domain':
                        params.domain = val;
                        break;
                }
            });

            return {
                name,
                value,
                params
            };
        } catch (error) {
            console.error('解析cookie时发生错误:', error);
            return null;
        }
    }

    // 方式一：打开我的方案
    public async loadSchemeByData(data: LayoutSchemeData) {

        if (!data) {
            console.error("Fail to open layout scheme: LayoutSchemeData object is null");
            return;
        }
        let layoutSchemeUrl: string = data.contentUrl;
        if (layoutSchemeUrl) {
            await fetch(layoutSchemeUrl).then((response) => {
                response.text().then(async (base64str) => {
                    const layoutSchemeJsonStr = base64ToUtf8(base64str);
                    const schemeJson = JSON.parse(layoutSchemeJsonStr.replace(/'/g, '"'));
                    this.layout_container.fromXmlSchemeData(schemeJson, true, SchemeSourceType.MyScheme);
                    this.setSchemeInfo(data.layoutSchemeName, data.id);
                    this.requestWholeLayoutSchemeRecommend();
                    this.layout_container.updateWholeBox();
                });
            }).catch((e) => {
                console.error(e);
            });
        }
    }

    // 方式二：打开户型库方案
    public async loadSchemeByBuildingId(id: string) {
        let authCode = getCookie("authCode");
        let data = await BuildingService.getBuildingRoomById(id);
        if (!data) return;
        let res = await SchemeXmlParseService.getSchemeXmlBySchemeId(data.schemeId, authCode);
        if (res) {
            let scheme_name = (data?.buildingName ? data?.buildingName + " " : "") + (data?.roomTypeName ? data?.roomTypeName + " " : "");
            scheme_name = scheme_name || t("未命名");
            this.layout_container.fromXmlSchemeData(res, true, SchemeSourceType.LayoutLibrary);
            this.setSchemeInfo(scheme_name);
            this.requestWholeLayoutSchemeRecommend();
        } else {
            LayoutAI_App.emit_M(EventName.WholeLayoutSchemeList, { schemeList: [], index: 0 })
            console.warn("Fail to load layout scheme");
        }
    }


    // 更新全屋布局方案列表
    private _updateWholeLayoutSchemeList() {
        let _whole_layout_scheme_list = [];
        let scheme_ids_list: string[] = [];
        let room_schemes_list: TRoomLayoutScheme[][] = [];

        for (let room of this.layout_container._rooms) {
            if (!room._layout_scheme_list) continue;

            let temp_scheme_list = [...room._layout_scheme_list];
            if (room._furniture_list.length > 0) {
                let diy_scheme = room.toLayoutScheme();
                temp_scheme_list = [diy_scheme, ...room._layout_scheme_list];
            }
            room_schemes_list.push(temp_scheme_list);
        }

        {
            let whole_layout_scheme0 = new TWholeLayoutScheme(this.layout_container);
            let scheme_ids = "";
            for (let room_schemes of room_schemes_list) {
                let scheme = room_schemes[0];
                scheme_ids += '0_';
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme0._room_scheme_list.push(scheme);
                }
            }
            scheme_ids_list.push(scheme_ids);
            _whole_layout_scheme_list.push(whole_layout_scheme0);
        }

        {
            let whole_layout_scheme1 = new TWholeLayoutScheme(this.layout_container);
            let scheme_ids = "";
            for (let room_schemes of room_schemes_list) {
                let scheme: TRoomLayoutScheme = null;
                for (let j = 1; j >= 0; j--) {
                    scheme = room_schemes[j];
                    if (scheme) {
                        scheme_ids += j + '_';
                        break;
                    }
                }
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme1._room_scheme_list.push(scheme);
                }
            }
            scheme_ids_list.push(scheme_ids);
            _whole_layout_scheme_list.push(whole_layout_scheme1);
        }

        let target_num = 5;
        // 依次类推模式
        for (let id = 2; id < 5; id++) {
            let scheme_ids = "";
            let whole_layout_scheme = new TWholeLayoutScheme(this.layout_container);
            for (let room_schemes of room_schemes_list) {
                let t_id = Math.min(id, room_schemes.length - 1);
                let scheme = room_schemes[t_id];
                scheme_ids += t_id + '_';
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme._room_scheme_list.push(scheme);
                }
            }
            if (!scheme_ids_list.includes(scheme_ids)) {
                scheme_ids_list.push(scheme_ids);
                _whole_layout_scheme_list.push(whole_layout_scheme);
                if (_whole_layout_scheme_list.length >= target_num) break;
            }
        }


        this._whole_layout_scheme_list = _whole_layout_scheme_list.filter((scheme) => scheme._room_scheme_list.length > 0);
        this.layout_container._whole_layout_scheme_list = this._whole_layout_scheme_list;
        LayoutAI_App.emit_M(EventName.WholeLayoutSchemeList, { schemeList: this._whole_layout_scheme_list || [], index: 0 })
    }

    // 请求全屋布局方案推荐并更新
    public async requestWholeLayoutSchemeRecommend() {
        await this.layout_container.applyRoomEntitiesWithSolvingMethods(null, ["BasicTransfer", "GroupTransfer", "SpacePartition"], {append_furniture_entites:true,needs_make_group_templates:true},null);

        this._updateWholeLayoutSchemeList();
    }


    // 获取布局图
    public get layoutImage(): string | null {
        return this._layoutImageUrl;
    }

    // 获取彩平图
    public get colorImage(): string | null {
        return this._colorImageUrl;
    }

    // 方式三：上传户型图 智能识别户型
    public async uploadFloorPlan(file: File): Promise<boolean> {
        const isValid = await this.validateFloorPlan(file);
        if (isValid) {
            const base64 = await this._fileToBase64(file);
            if (base64) {
                this._postHouseRecognitionRequest(base64);
                this._floorPlanUrl = base64;

                return true;
            }
        }
        return false;
    }

    // 小程序的图片上传,识别智能户型
    public async uploadImage(base64: string) {
        this._postHouseRecognitionRequest(base64);
        this._floorPlanUrl = base64;
    }

    // 户型识别
    private _postHouseRecognitionRequest(base64Image: string) {
        // 识别户型图开始
        LayoutAI_App.emit(LayoutAI_Events.HouseRecognitionStatus, "start");

        this.requestHouseRecognition(base64Image)
            .then(async (response: any) => {
                if (!response || !response.success) {
                    this._handleException();
                } else {
                    if (response.Status == 200 && response.Content) {
                        const houseXmlContent = response.Content;
                        const houseXmlBase64 = deflate_to_base64_str(houseXmlContent);
                        let xmlSchemeJson: I_SwjXmlScheme = await SchemeXmlParseService.parseSchemeXml2Json(houseXmlBase64, GenDateUUid());
                        if (!xmlSchemeJson) {
                            console.error("Fail to parse xml scheme");
                            this._handleException();
                            return;
                        }
                        this.layout_container.fromXmlSchemeData(xmlSchemeJson);
                        let scheme_img = this.layout_container.saveLayoutSchemeImage(1200, 1000, 0.85, true, null);
                        this._floorPlanUrl = scheme_img;
                        LayoutAI_App.emit(LayoutAI_Events.HouseRecognitionStatus, "success");
                    } else {
                        this._handleException();
                    }
                }
            })
            .catch((e: any) => {
                this._handleException();
            });
    }

    // 户型识别请求函数 - 抽取为可复用函数
    public async requestHouseRecognition(base64Image: string): Promise<any> {
        const base64WithoutHeader = base64Image.split(',')[1];

        let data = {
            "b64_img": base64WithoutHeader,
        }

        const proRequest = createProRequest();
        proRequest.defaults.withCredentials = true;
        proRequest.defaults.baseURL = openApiHost;
        proRequest.defaults.headers = {
            'Content-Type': 'application/json',
            'Magiccube-Token': _magiccubeToken
        };

        try {
            return await proRequest({
                method: 'post',
                url: `https://open.3vjia.com/api/turing/api/rebuildHouse/v3/predict_xml`,
                data: data,
                timeout: 10000,
            });
        } catch (error) {
            console.error("House recognition request failed:", error);
            throw error;
        }
    }

    // 获取临时cookie
    public async getTempCookie(): Promise<string[] | null> {
        const proRequest = createProRequest();
        proRequest.defaults.baseURL = imsApiUrl;
        proRequest.defaults.headers = {
            'Content-Type': 'application/json',
            "sysCode": APP_ID
        };
        return proRequest({
            method: 'post',
            url: `/api/nj3d/getCookie`,
            data: {
                userId: this.USER_ID
            },
            timeout: 10000,
        }).then(async (res: any) => {
            if (res && res.success) {
                return res.result;
            } else {
                return null;
            }
        }).catch((e: any): any => {
            console.error(e);
            return null;
        });

    }

    // 保存方案
    public async insert() {
        if (!designer_open_id || !designer_id) return;
        const params = {
            behaviorType: '使用了AI布局生成',
            designerId: designer_id,
            designerOpenId: designer_open_id,
            extTitle: this.layout_container._layout_scheme_name,
            viewType: '布局方案',
            viewPath: `pages/b/aiLayout/layoutDetail/index?id=${this.layout_container._scheme_id}&from=designer&designerOpenId=${designer_open_id}&designerId=${designer_id}`,
            visitorOpenId: visitor_open_id,
        }
        await insertClueTrack(params);
    }

    // 处理异常
    private _handleException() {
        console.error("Fail to recognize house");
        LayoutAI_App.emit(LayoutAI_Events.HouseRecognitionStatus, "fail");
    }

    // 验证户型图 图片格式和大小
    private async validateFloorPlan(file: File): Promise<boolean> {
        const validImageTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!validImageTypes.includes(file.type)) {
            return false;
        }

        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            return false;
        }

        return true;
    }

    // 将文件转换为base64
    private _fileToBase64(file: File): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                const base64 = reader.result as string;
                resolve(base64);
            };
            reader.onerror = (error) => reject(error);
        });
    }
}

export const trialDataService = TrialDataService.getInstance();
