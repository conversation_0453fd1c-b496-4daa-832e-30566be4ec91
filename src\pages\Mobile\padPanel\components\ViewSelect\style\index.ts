import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    shijiaoBarContainer: css`
      z-index:999;
      margin-right: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(0, 0, 0, 0.40);
      backdrop-filter: blur(50px);
      border-radius: 20px;
      cursor: pointer;
      
      .pageRenderList
      {
        position: absolute;
        left: 50%;
        height: 91px;
        bottom: 150px;
        transform: translate(-50%);
        max-width: 500px;
        overflow-x: scroll;
        scroll-behavior: smooth;
        display: flex;
        gap: 8px;
        ::-webkit-scrollbar
        {
          display: none;
        }

        .pageRenderItem{
          border: 2px solid #fff;
          width: 122px;
          height: 91px;
          border-radius: 8px;
          overflow: hidden;
          flex-shrink: 0;
        }
      }
      .selectInfo {
        z-index: 1;
        position: absolute;
        left: 50%;
        bottom: 50px;
        transform: translate(-50%);
        max-width: 500px;
        overflow-x: scroll;
        transition: height 0.3;
        display: flex;
        scroll-behavior: smooth;
        ::-webkit-scrollbar
        {
          display: none;
        }
        .shijiaoItem
        {
          width: 100%;
          font-size: 16px;
          text-align: center;
          position: relative;
          margin-right: 8px;
          transition: all .3s;
          border-radius: 8px;
          width: 127px;
          height: 95px;
          img{
            width: 122px;
            height: 100%;
            border-radius: 8px;
          }
          .title
          {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translate(-50%);
            color: #fff;
            font-size: 14px;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 1.43%, rgba(0, 0, 0, 0.60) 101.43%);
            width: 123px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
          }
          .tag
          {
            position: absolute;
            top: 5px;
            right: 5px;
            color: #fff;
            font-size: 12px;
            border-radius: 5px;
            padding: 2px 4px;
            background: linear-gradient(270deg, rgba(102, 117, 255, 1) 0%, rgba(113, 221, 255, 1) 100%);
          }
        }
        .shijiaoItem:hover
        {
          background-color: #ffffff1a;
          transition: all 0.3;
        }
      }
    `,
    leftArrow: css`
      border-right: 1px solid rgba(255,255,255,.1);
      height: 42px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 45px;
    `,
    rightArrow: css`
      border-left: 1px solid rgba(255,255,255,.1);
      height: 42px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 45px;
    `,
    shijiaoBar: css`
      width: 80px;
      height: 42px;
      font-size: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      @media screen and (max-width: 450px) { // 手机宽度
        width: 100px;
      }
    `,
  }
});