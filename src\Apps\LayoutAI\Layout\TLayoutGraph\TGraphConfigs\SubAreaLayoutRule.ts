import { ZRect } from "@layoutai/z_polygon";
import { IRoomSpaceAreaType } from "../../IRoomInterface";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { ZEdge } from "@layoutai/z_polygon";
import { Vector3 } from "three";

export interface I_SubAreaResult{
    area_name : string,
    area_rect : ZRect
}
export interface I_AreaLayoutElementRule 
{
    category : string;
    code ?: string;
    length : number;
    depth : number;
    height ?: number;
    back_wall_offset ?: number;
    padding_left ?: number;
    start_val ?: string;
    length_val ?: string;
    rotate_degree?: number;
    u_dv_flag ?: number;
    align_target_codes?: string[];
} 
export interface I_AreaLayoutEdgeRule
{
    main_rect_edge_id : number;
    u_dv_flag : number;
    element_rules : I_AreaLayoutElementRule[];
}
export interface I_AreaLayoutRule{
    name:string, tags?:string[],area_type ?: IRoomSpaceAreaType, min_length?:number, rules:I_AreaLayoutEdgeRule[]
}

export function ApplyAreaLayoutRule(curr_rule:I_AreaLayoutRule,area_rect:ZRect)
{
    const code_element_dict : {[key:string]:TFigureElement} = {};
    const figure_elements : TFigureElement[] = [];
    curr_rule.rules.forEach((layout_rule)=>{
        let t_edge =area_rect.edges[((layout_rule.main_rect_edge_id||0)+3) %4];
        if(layout_rule.u_dv_flag && layout_rule.u_dv_flag < 0)
        {
            let nor = t_edge.nor.clone();
            t_edge = new ZEdge({pos:t_edge.v1.pos},{pos:t_edge.v0.pos});
            t_edge._nor = nor;
        }
        let start_val = 0;
        layout_rule.element_rules.forEach((ele_rule)=>{

            let ele = TFigureElement.createSimple(ele_rule.category);
            ele.length = ele_rule.length;
            ele.depth = ele_rule.depth;
            ele.height = ele_rule.height || ele.height;

            ele.nor = t_edge.nor.clone().negate();
            if(ele_rule.rotate_degree)
            {
                ele.nor = t_edge.nor.clone().negate().applyAxisAngle(new Vector3(0,0,1), ele_rule.rotate_degree /180*Math.PI);
            }
            if(ele_rule.u_dv_flag)
            {
                ele.rect._u_dv_flag = ele_rule.u_dv_flag;
            }

            if(ele_rule.align_target_codes)
            {
                let target_elements : TFigureElement[] = [];
                ele_rule.align_target_codes.forEach((code)=>{
                    if(code_element_dict[code])
                    {
                        target_elements.push(code_element_dict[code]);
                    }
                });

                if(target_elements.length > 0)
                {
                    let group_rect = ZRect.fromPoints(target_elements.map((ele)=>ele.rect.positions).flatMap(p=>p),ele.nor);
                    let t_center_val = t_edge.projectEdge2d(group_rect.rect_center).x;  
                    let t_offset_val = ele_rule.back_wall_offset || 0;
                    let pos = t_edge.unprojectEdge2d({x:t_center_val,y:-t_offset_val});
                    ele.rect.back_center = pos;
                    ele.rect.updateRect();
                    if(ele_rule.length_val)
                    {
                        let inputData: { [key: string]: number } = {
                            g_l: group_rect.w,
                            t_l: ele_rule.length,
                        }
                        let code = '';
                        for (let key in inputData) {
                            code += `let ${key} = arg['${key}'] ?? 0; `;
                        }
                        code += `return  (${ele_rule.length_val});`;
        
                        let val = new Function('arg', code)(inputData);
                        ele.length= val;
                        ele.rect.updateRect();
                    }
                    figure_elements.push(ele);
                    if(ele_rule.code)
                    {
                        code_element_dict[ele_rule.code] = ele;
                    }
                }



            }
            else{
                let t_center_val = start_val + (ele_rule.padding_left || 0) + ele.length /2;
                let t_offset_val = ele_rule.back_wall_offset || 0;
                let pos = t_edge.unprojectEdge2d({x:t_center_val,y:-t_offset_val});
                ele.rect.back_center = pos;
                ele.rect.updateRect();
                start_val += (ele_rule.padding_left||0) + ele.length;
                figure_elements.push(ele);
            }
            if(ele_rule.code)
            {
                code_element_dict[ele_rule.code] = ele;
            }

        })

    });

}