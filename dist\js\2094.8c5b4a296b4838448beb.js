"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[2094],{74786:function(e,n,t){t.r(n),t.d(n,{default:function(){return a}});var r=t(13274);t(41594);function i(){var e,n,t=(e=["\n      display:flex;\n      width:100%;\n      height:100vh;\n      background:#fff;\n      // align-items:center;\n      justify-content:center;\n\n\n    "],n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}})));return i=function(){return t},t}var u=(0,t(79874).rU)((function(e){return{root:(0,e.css)(i())}})),c=t(15696),s=t(85783),f=t(65810),a=(0,c.observer)((function(){(0,s.B)().t,u().styles;return(0,r.jsx)(f.A,{})}))}}]);