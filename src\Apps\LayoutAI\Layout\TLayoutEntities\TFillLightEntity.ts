import { CircleGeometry, ConeGeometry, DirectionalLight, DoubleSide, Light, Mesh, MeshBasicMaterial, PlaneGeometry, PointLight, RectAreaLight, SphereGeometry, SpotLight } from "three";

import { TBaseEntity } from "./TBaseEntity";


/**
* @description 补光灯实体
* <AUTHOR>
* @date 2025-05-15
* @lastEditTime 2025-05-15 16:08:30
* @lastEditors xuld
*/
export class TFillLightEntity extends TBaseEntity {
    // 灯光对象
    private _light: Light;
    // 灯光外显对象
    private _lightMesh: Mesh;
    // 是否显示
    private _isShowLight: boolean = true;
    // 最大亮度
    private _maxLightIntensity: number = 100;
    // 透明度是否影响显示的颜色
    private _isOpacityColor: boolean = false;

    constructor(light?: Light) {
        super();
        this._light = light;
        this._lightMesh = TFillLightEntity.createLightMesh(this._light);
        this._lightMesh.visible = this._isShowLight;
    }

    public get lightMesh() {
        return this._lightMesh;
    }

    public get light() {
        return this._light;
    }

    public get isShowLight() {
        return this._isShowLight;
    }

    public set isShowLight(isShow: boolean) {
        this._isShowLight = isShow;
        this._lightMesh.visible = isShow;
    }

    public get maxLightIntensity() {
        return this._maxLightIntensity;
    }

    public set maxLightIntensity(maxLightIntensity: number) {
        this._maxLightIntensity = maxLightIntensity;
    }

    public update3D(): Light {
        if (this._lightMesh.material instanceof MeshBasicMaterial) {
            this._lightMesh.material.transparent = true;
            if (this._isOpacityColor) {
                let opacity = Math.max(Math.min(this.light.intensity / this._maxLightIntensity, 1), 0);
                this._lightMesh.material.opacity = opacity;
            } else {
                this._lightMesh.material.opacity = 1;
            }
            this._lightMesh.material.color.set(this.light.color);
            // this._lightMesh.material.color.set(1, 0, 0);
            this._lightMesh.material.needsUpdate = true;
        }
        return this._light;
    }

    /**
     * @description 创建灯光的外显
     * @param light 灯光
     * @param lightMeshColor 灯光外显颜色
     * @return Mesh | undefined
     */
    public static createLightMesh(light: Light): Mesh | undefined {
        let lightShowMesh: Mesh | undefined;
        let op = {
            color: 0xffff00,
            opacity: 1,
            transparent: true,
            side: DoubleSide,
        };
        if (light instanceof DirectionalLight) {
            // 创建一个小圆盘作为平行光的外显
            lightShowMesh = new Mesh(new CircleGeometry(200, 32), new MeshBasicMaterial(op));
        } else if (light instanceof PointLight) {
            // 创建一个小球体作为点光的外显
            lightShowMesh = new Mesh(new SphereGeometry(200, 32, 16), new MeshBasicMaterial(op));
        } else if (light instanceof RectAreaLight) {
            // 创建一个矩形平面作为矩形光的外显
            lightShowMesh = new Mesh(new PlaneGeometry(light.width, light.height), new MeshBasicMaterial(op));
        } else if (light instanceof SpotLight) {
            // 创建一个圆锥体作为聚光灯的外显
            lightShowMesh = new Mesh(new ConeGeometry(200, 200, 32), new MeshBasicMaterial(op));
        } else {
            console.error("unsupported light type", light);
        }
        if (!lightShowMesh) {
            return;
        }

        lightShowMesh.name = light.name;
        lightShowMesh.position.set(light.position.x, light.position.y, light.position.z);
        lightShowMesh.rotation.set(light.rotation.x, light.rotation.y, light.rotation.z);
        return lightShowMesh;
    }

    public setLightColor(color: number) {
        this._light.color.set(color);
        if (this._lightMesh.material instanceof MeshBasicMaterial) {
            this._lightMesh.material.color.set(this._light.color);
        }
    }

    public setLightBrightness(brightness: number) {
        this._light.intensity = brightness;
        if (this._lightMesh.material instanceof MeshBasicMaterial) {
            if (this._isOpacityColor) {
                this._lightMesh.material.opacity = Math.max(Math.min(brightness / this._maxLightIntensity, 1), 0);
            } else {
                this._lightMesh.material.opacity = 1;
            }
        }
    }
}