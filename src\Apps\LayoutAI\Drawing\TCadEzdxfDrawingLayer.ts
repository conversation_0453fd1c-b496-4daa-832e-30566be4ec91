import { Vector3, Box3 } from "three";
import { I_EzdxfJsonData } from "../AICadData/EzdxfEntity";
import { Vec3FromArray, Vec3toMeta, computeViewCenterOfPoints } from "@layoutai/z_polygon";
import { I_EzdxfEntity } from "@layoutai/z_polygon";
import { CadDrawingLayerType, TDrawingLayer } from "./TDrawingLayer";
import { TAppManagerBase } from "../../AppManagerBase";


export class TEzdxfDataDrawingLayer extends TDrawingLayer {

    constructor(manager:TAppManagerBase) {
        super(CadDrawingLayerType.CadEzdxfDrawing, manager);
        this._manager = manager;

        this._dirty = false;
    }

    get ezdxf_data()
    {
        return this._manager.layout_container.ezdxf_cad_data;
    }

    set ezdxf_data(data:I_EzdxfJsonData)
    {
        this._manager.layout_container.ezdxf_cad_data = data;
    }
    
    loadData(data: I_EzdxfJsonData) {
        this.ezdxf_data = data;
        this._dirty = true;
    }
    
    initEzdxfData()
    {
        let bbox = new Box3();
        let t_points: Vector3[] = [];
        let update_entity = (ele: I_EzdxfEntity,t_points:Vector3[], is_add_point: boolean = true) => {
            let t_ele = ele;

            if (t_ele.attribs) {
                for (let key in t_ele.attribs) {
                    let val = (t_ele.attribs as any)[key];

                    if (val instanceof Array) {
                        if (val.length == 3) {
                            (t_ele.attribs as any)[key + "_v3"] = Vec3FromArray(val);
                            if (key == "start" && is_add_point) {
                                t_points.push((t_ele.attribs as any)[key + "_v3"]);
                            }
                        }
                    }
                }
            }
            if (t_ele.lwpoints) {
                t_ele.lwpoints_v3 = [];
                for (let val of t_ele.lwpoints) {
                    t_ele.lwpoints_v3.push(Vec3FromArray(val));
                    if (is_add_point) {
                        t_points.push(...t_ele.lwpoints_v3);

                    }

                }
            }

            if (ele.type == "MTEXT") {
                if (is_add_point) {
                    t_points.push(ele.attribs.insert_v3);
                }

                let text = ele.text;

                if(text)
                {
                    text =  text.replaceAll("\\P","\n");

                    let font_index = text.indexOf("{\\f");
                    if(font_index >= 0)
                    {
                        text = text.substring(font_index);
                        let t_index = text.indexOf(";");

                        if(t_index>=0)
                        {
                            text = text.substring(t_index+1);
                            text = text.replace("}","");
                        }
        
                    }

                    ele.text = text;

                }
            }
        }
        for (let ele of this.ezdxf_data.modelspace.entities) {
            let t_ele = ele;
            update_entity(ele,t_points);
        };

        for (let key in this.ezdxf_data.blocks) {
            let block = this.ezdxf_data.blocks[key];
            let points : Vector3[] = [];
            for (let entity of block.entities) {
                update_entity(entity, points, true);
            }
        }
        if (this.painter) {
            for (let key in this.ezdxf_data.blocks) {
                this.painter._ezdxf_blockrecords[key] = this.ezdxf_data.blocks[key];
            }
            if(!this.ezdxf_data.layers)
            {
                this.ezdxf_data.layers = {};
            }
            for(let key in this.ezdxf_data.layers)
            {
                this.painter._ezdxf_layers[key] = this.ezdxf_data.layers[key];
            }
        }

        let points = this.painter.computeDxfLayoutBoxAndPoints(this.ezdxf_data.modelspace,new Vector3(0,0,0),{x:1,y:1,z:1},0,0);
        let view_data = computeViewCenterOfPoints(points);

        this.painter.p_center = view_data.center;

        this.ezdxf_data.view_center = Vec3toMeta(view_data.center);
        this.ezdxf_data.view_radius = view_data.radius;
        this.ezdxf_data.complete = true;

    
    }

    _clean()
    {
    }

    _updateLayerContent(): void {
        if(!this._dirty) return;

        this.initEzdxfData();

        this._dirty = false;
    }    
    onDraw(): void {
        if (!this.painter) return;

        if (this._dirty) {
            this._updateLayerContent();
        }

        if (this.ezdxf_data) {
            this.painter.fillStyle = "#000";
            this.painter.strokeStyle = "#000";
            this.painter._context.globalAlpha = 0.5;
            this.painter.drawDxfLayout(this.ezdxf_data.modelspace, new Vector3(0, 0, 0), { x: 1, y: 1, z: 1 }, 0, 0);
            this.painter._context.globalAlpha = 1.;
            return;
        }
    }
}