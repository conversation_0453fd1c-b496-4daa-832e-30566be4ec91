import { Matrix4, Vector3, Vector3<PERSON>ike } from 'three';
import { IRoomEntityRealType, IRoomEntityType, IRoomSpaceAreaType, I_Room } from '../Layout/IRoomInterface';
import { I_FigureElement } from '../Layout/TFigureElements/TFigureElement';
import { ZRect } from "@layoutai/z_polygon";

export interface I_SwjLineEdge {
    /** 线起点 */
    start: Vector3Like;
    /** 线终点 */
    end: Vector3Like;
}
export interface I_SwjEntityBase {
    length?: number;
    /**
     *  宽度、深度、厚度 经常性混用
     */
    width?: number;
    deep?: number;
    thickness?: number;
    thick?: number;
    height?: number;
    room_id?: number;
    mirror?: number;
    openDirection?: number;
    rotate_x?: number;
    rotate_y?: number;
    rotate_z?: number;
    direct_x?: number;
    direct_y?: number;
    pos_x?: number;
    pos_y?: number;
    pos_z?: number;
    uid?: number | string;
    uuid?: string;
    name?: string;

    /**
     *  元素类型：这个type是2D编辑器定义的大类
     */
    type?: IRoomEntityType;
    /**
     *  真实类型: 对齐子类
     */
    realType?: IRoomEntityRealType;

    modelLoc?: string;
    public_category?: string;
    /**
     *  material_id: 数字->字符串
     */
    material_id?: string;
    material_replace_id?: string;

    _xml_entity_type?: string;


    /**
     *  通常不输出保存
     */
    _rect?: ZRect;
    target?: string[];
    dir?: string;

}
export const SwjPropskeyDict: { [key: string]: string } = { "posX": "pos_x", "posY": "pos_y", "posZ": "pos_z", "rotateZ": "rotate_z" };

export interface I_SwjRoomFloorData extends I_SwjEntityBase {
    uid: number;
    roomUid: number;
    anchorPos: Vector3Like;
}

export interface I_SwjCabinetData extends I_SwjEntityBase {
    room_ind?: number;
    length: number;
    width: number;
    deep: number;
    visible?: boolean;
    _variables: { [key: string]: { value: number | string, expr: string } };
    showCondition?: string;
    standardCategory?: string;
    _children?: I_SwjCabinetData[];

    _is_parts_cabinet?: boolean;

    _parent_matrix?: Matrix4;

    materialIds?: Array<string>;
    material?: { [key: string]: { value: number | string } };
}
export interface I_SwjFurnitureData extends I_SwjEntityBase {
    room_ind?: number;
    group_template_code?: string;
    sub_list?: I_SwjFurnitureData[];
    _figure_element?: I_FigureElement;
}
export interface I_SwjStructureData extends I_SwjEntityBase {
    uid?: number;
    pos_x?: number;
    pos_y?: number;
    length?: number;
    width?: number;
    height?: number;
    direct_x?: number;
    direct_y?: number;
}
export interface I_SwjPillarData extends I_SwjStructureData {

}
export interface I_SwjPlatformData extends I_SwjStructureData {

}
export interface I_SwjFlueData extends I_SwjStructureData {

}
export interface I_SwjPipeData extends I_SwjStructureData {
}
export interface I_SwjFurnitureGroup {
    pos_x?: number;
    pos_y?: number;
    pos_z?: number;

    rotate_z?: number;
    name?: string;
    length?: number;
    height?: number;
    width?: number;
    uid?: string;
    room_ind?: number;
    public_category?: string;

    furniture_list: I_SwjFurnitureData[];
    sub_group_list?: I_SwjFurnitureGroup[];

    group_template_code?: string;
    group_template_uuid?: string;

    material_id?: string;
    material_replace_id?: string;

}
export interface I_SwjBaseGroup {
    pos_x?: number;
    pos_y?: number;
    pos_z?: number;

    rotate_z?: number;
    name?: string;
    length?: number;
    height?: number;
    width?: number;
    uid?: string;
    room_ind?: number;
    public_category?: string;
    sub_list: I_SwjFurnitureData[];

    material_id?: string;
    material_replace_id?: string;
}

export interface I_SwjWall extends I_SwjEntityBase {
    start_x?: number;
    start_y?: number;
    end_x?: number;
    end_y?: number;
    boundary?: I_SwjLineEdge[];
}



export interface I_SwjAreaName extends I_SwjEntityBase {

}
export interface I_SwjWindow extends I_SwjEntityBase {
    uid?: string | number;
    length?: number;
    width?: number;
    height?: number;
    mirror?: number;
    type?: IRoomEntityType;
    realType?: IRoomEntityRealType
    pos_x?: number;
    pos_y?: number;
    pos_z?: number;
    rotate_z?: number;
    openDirection?: number;
    room_names?: string[];
    /**
     *  主要是用于自动拆改、排房时自动清空过滤
     */
    isInside ?: boolean;
}
export interface I_SwjInnerWall {
    uid?: string | number;
    start_x?: number;
    start_y?: number;
    end_x?: number;
    end_y?: number;
    thick?: number;
    wall_uid?: string;
    window_list?: I_SwjWindow[];
    door_list?: I_SwjWindow[];
    boundary: I_SwjLineEdge[];
    type: string;
}
export interface I_SwjRoom {
    name?: string;
    roomname?: string;
    room_type?: string;
    room_id?: number;
    uid?: string | number;
    uuid?: string;
    ind?: number;
    room_size?: string;
    area?: number;
    center_x?: number;
    center_y?: number;
    floor?: I_SwjRoomFloorData;
    cabinet_list?: I_SwjCabinetData[];
    furniture_group_list?: I_SwjFurnitureGroup[];
    furniture_list?: I_SwjFurnitureData[];

    scheme_id?: string;
    beam_list?: I_SwjEntityBase[];
    pillar_list?: I_SwjEntityBase[];
    flue_list?: I_SwjEntityBase[];
    pipe_list?: I_SwjEntityBase[];
    platform_list?: I_SwjEntityBase[];
    inner_wall_list?: I_SwjInnerWall[];
    wall_list?: I_SwjWall[];
    window_list?: I_SwjWindow[];
    door_list?: I_SwjWindow[];
    boundary?: I_SwjLineEdge[];
    scope_series_map?: { [key: string]: any };
    ceiling_element_list?: I_FigureElement[];
    wall_element?: I_FigureElement;
    floor_element?: I_FigureElement;
}
export interface I_SwjExtDrawingData extends I_SwjEntityBase {
    points?: Vector3Like[];
    line_style?: string;
}

export interface I_SwjViewCameraData extends I_SwjExtDrawingData {
    is_focus_mode: boolean;
    near: number;
    far: number;
    view_center: Vector3Like;
    pos_z ?:number;
    room_info?: I_Room;
    description ?: string;
    target?: string[];
    dir?: string;
    ukey?: string;
}
export type ViewCameraImgPreviewType = "Plane2D" | "View3D";
export interface I_SwjSubSpaceData extends I_SwjEntityBase {
    space_area_type?: IRoomSpaceAreaType;
    color_style?: string;
    room_uidN?: number;
}

export interface I_SplitSpaceItem
{
    /**
     *  分割的范围:    
     */
    interval : number[];
    
    /**
     *   范围计算的对象, 默认是BackEdge
     */
    splitRefer ?: "BackEdge" | "Rect",

    /**
     *  法向改变, 默认是0
     */
    normalChanged ?: "90"|"-90"|"180"|"0";

    /**
     *  1 or -1
     */
    u_dv_flag ?: number;

    /**
     *  关联的窗户, 一般只有一个
     */
    attached_windows ?: I_SwjWindow[];

    /**
     *   关联的柱子等
     */
    attached_structures ?: I_SwjStructureData[];

    /**
     *  类别
     */
    category ?: string;

    /**
     *  得分计算
     */
    score ?: number;


    childrenSplitItems ?: I_SplitSpaceItem[];

    isInSide?:boolean;

    prev_item ?: I_SplitSpaceItem;
    next_item ?: I_SplitSpaceItem;
}
export interface I_SwjBaseSpaceData extends I_SwjEntityBase
{
    space_type ?: string;
    color_style ?: string;
    childrenSpaces ?:I_SwjBaseSpaceData[];
    splitParams ?: I_SplitSpaceItem[];
}
export interface I_SwjbuildInfo {
    pos_num?: string;
    scale_num?: string;
    version?: string;
}

export interface I_SwjXmlScheme {
    /**
     *  扩展LayoutAI_Version 版本信息
     */
    LayoutAI_Version?: string;
    name?: string;
    /**
     *  布局方案Id
     */
    layout_scheme_id?: string;
    room_id?: number;
    scheme_id?: string;
    hxId?: string;
    scheme_name?: string;
    organization_id?: string;
    xml_str?: string;
    area?: number;
    storey_height?: number;
    uid?: string | number;
    ind?: number;
    room_size?: string;
    center_x?: number;
    center_y?: number;
    beam_list?: any[];
    cabinet_list?: I_SwjCabinetData[];
    furniture_group_list?: I_SwjFurnitureGroup[];
    base_group_list?: I_SwjBaseGroup[];
    furniture_list?: I_SwjFurnitureData[];
    pillar_list?: I_SwjPillarData[];
    platform_list?: I_SwjPlatformData[];
    flue_list?: I_SwjFlueData[];
    pipe_list?: I_SwjPipeData[];
    room_list?: I_SwjRoom[];
    wall_list?: I_SwjWall[];
    window_list?: I_SwjWindow[];
    door_list?: I_SwjWindow[];
    area_names?: I_SwjAreaName[];
    building_info?: I_SwjbuildInfo;
    ext_drawing_list?: I_SwjExtDrawingData[];
    /**
 *  子分区列表
 */
    sub_area_list?: I_SwjSubSpaceData[];
    base_space_list ?: I_SwjBaseSpaceData[];
    wireFrameImageJsonUrl?: string;
}

export interface I_XmlParseResult {
    duration?: number;
    exception?: string;
    hostIp?: string;
    level?: string;
    logTime?: string; // date string
    message?: string; // SUCCESS 
    reqBody?: string; // null

    result: I_SwjXmlScheme;
    sourceIp?: string;
    sysCode?: number;
    statusCode?: number;
    sysName?: string;
    traceId?: string;

}

export interface I_SwjRemodelingRequestData {
    traceId: string;
    userId: string;
    outerWalls: I_SwjWall[]; // 外墙
    innerWalls: I_SwjWall[]; // 内墙
    outWindows: I_SwjWindow[]; // 外窗
    ruhuDoors: I_SwjWindow[]; // 入户门
    roomInfos: { name: string, pos: Vector3 }[]; // 房间信息
    pillarList: I_SwjPillarData[]; // 柱子
    flueList: I_SwjFlueData[]; // 烟道
    pipeList: I_SwjPipeData[]; // 管道
    platformList: I_SwjPlatformData[]; // 地台
}
// 打开方案的来源
export enum SchemeSourceType {
    CloudDesign = "CloudDesign", // 3D云设计来的方案
    LayoutLibrary = "LayoutLibrary", // 户型库
    MyScheme = "MyScheme", // 我的方案
    LayoutCorrection = "LayoutCorrection", // 户型纠正
    LayoutRenovation = "LayoutRenovation" // 户型拆改
}



export interface StyleBrush {
    materials: string[];
    defaultTab: string[];
    applyCondition: string;
    isCanEnable: boolean;
    items: StyleComposeItem[];
    id: string;
    name: string;
}

export interface StyleComposeItem { 
    id: string;
    name: string;
    materialImgPath: string;
    materialId: string;
    materialName: string;
    categoryCode: string;
    isRotate: boolean;
    picHeight: number;
    picWidth: number;
    applyCondition: string;
    useType: number;
}
