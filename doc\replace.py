import os

dirname = os.path.dirname(__file__)

for root,dirs,files in os.walk(os.path.join(dirname,"..\\src\\")):
    for file in files:
        if file.endswith("ts") or file.endswith("tsx"):
            filename = os.path.join(root,file)
            fp = open(file=filename,mode="r",encoding='utf-8')
            lines = fp.readlines()
            fp.close()

            for i in range(0,len(lines)):
                line = lines[i]
                if line.find("import")>=0:
                    if line.find("three-math")>=0:
                        line = line.replace("three-math","three")
                        lines[i] = line

            fp = open(file=filename,mode="w",encoding="utf-8")
            fp.writelines(lines)
            fp.close()