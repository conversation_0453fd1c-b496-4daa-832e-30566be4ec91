import { Image } from '@svg/antd'

interface ImageWithDownloadProps {
    src: string;
    pano: {
        isPanoMode: boolean;
        effect?: () => any
    };
    alt?: string;
    width?: number | string;
    [key: string]: any; // 允许其他Antd Image组件的属性
}

const LayoutAIImage: React.FC<ImageWithDownloadProps> = ({ 
    src, 
    pano = {
        isPanoMode: false,
        effect: null
    },
    alt, 
    width,
    item,
    onClick,
    ...props 
}) => {

    return <>
        <Image
            src={src}
            alt={alt || '图片'}
            preview={pano.isPanoMode ?  pano.effect() : false} // 禁用默认预览
            onClick={onClick}
            width={width}
            {...props}
        />

        {/* {!pano.isPanoMode && <CustomImagePreview
            url={src}
            visible={previewVisible}
            onClose={() => setPreviewVisible(false)}
            imageAlt={alt}
            item={item}
        />} */}
    </>
}

export default LayoutAIImage