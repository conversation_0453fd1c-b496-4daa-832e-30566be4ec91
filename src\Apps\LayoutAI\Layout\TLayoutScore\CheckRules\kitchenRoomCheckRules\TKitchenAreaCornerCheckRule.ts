import { IKitchenLayout, KitchenAreaType, KitchenLayoutType } from "@layoutai/layout_service";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoomLayoutScheme } from "../../../TLayoutScheme/TRoomLayoutScheme";
import { TRoom } from "../../../TRoom";
import { TCheckRule } from "../TCheckRule";

/**
 * @description 转角禁区评分
 * 转角禁区：
 * 水盆区
 * 烹饪区
 * <AUTHOR>
 * @date 2025-08-30
 * @lastEditTime 2025-08-30 17:32:58
 * @lastEditors xuld
 */
export class TKitchenAreaCornerCheckRule extends TCheckRule {
    private _forbidCornerTypes = [KitchenAreaType.CookingArea, KitchenAreaType.SinkArea];

    checkValue(
        room: TRoom,
        figure_elements?: TFigureElement[],
        layout_scheme: TRoomLayoutScheme = null
    ) {
        if (!layout_scheme) {
            return { value: 0 };
        }

        let extra = layout_scheme.extra as IKitchenLayout;
        if (!extra) {
            return { value: 0 };
        }

        let isTargetInCorner = false;
        if (extra.layoutType === KitchenLayoutType.L_TYPE) {
            const edge0 = extra.edgeLayouts[0];
            const edge0EndArea = edge0.areaTypeRects[edge0.areaTypeRects.length - 1];
            if (edge0.isExtendEnd && this._forbidCornerTypes.includes(edge0EndArea.areaType)) {
                isTargetInCorner = true;
            }

            const edge1 = extra.edgeLayouts[1];
            const edge1StartArea = edge1.areaTypeRects[0];
            if (edge1.isExtendStart && this._forbidCornerTypes.includes(edge1StartArea.areaType)) {
                isTargetInCorner = true;
            }
        } else if (extra.layoutType === KitchenLayoutType.U_TYPE) {
            // 第1边的最后一块
            const edge0 = extra.edgeLayouts[0];
            const edge0EndArea = edge0.areaTypeRects[edge0.areaTypeRects.length - 1];
            if (edge0.isExtendEnd && this._forbidCornerTypes.includes(edge0EndArea.areaType)) {
                isTargetInCorner = true;
            }

            // 第2边的第一块
            const edge1 = extra.edgeLayouts[1];
            const edge1StartArea = edge1.areaTypeRects[0];
            if (edge1.isExtendStart && this._forbidCornerTypes.includes(edge1StartArea.areaType)) {
                isTargetInCorner = true;
            }

            // 第2边的最后一块
            const edge1EndArea = edge1.areaTypeRects[edge1.areaTypeRects.length - 1];
            if (edge1.isExtendEnd && this._forbidCornerTypes.includes(edge1EndArea.areaType)) {
                isTargetInCorner = true;
            }

            // 第3边的第一块
            const edge2 = extra.edgeLayouts[2];
            const edge2StartArea = edge2.areaTypeRects[0];
            if (edge2.isExtendStart && this._forbidCornerTypes.includes(edge2StartArea.areaType)) {
                isTargetInCorner = true;
            }
        }
        return { value: isTargetInCorner ? 0 : 1 };
    }
}
