import { AI2Base<PERSON>odeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { EventName } from "@/Apps/EventSystem";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import createProRequest from "@/utils/request/request";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { openApiHost } from "@/config";

// 拆改结果展示子模式
export class ResultShowSubHandler extends CadBaseSubHandler {
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = "ResultShowSubHandler";
    }

    enter(state?: number): void {
        super.enter(state);
        this.manager.layer_CadRoomNameLayer.visible = true;
        this.manager.layer_CadFurnitureLayer.visible = true;
        this.manager.layer_AILayoutLayer.visible = true;
        this.manager.layer_AIMatchingLayer.visible = true;
        LayoutAI_App.emit_M(EventName.SubHandlerChanged, { is_default: false, name: this.name });
        this._postReModelingRequest();
    }
    leave(): void {
        LayoutAI_App.emit_M(EventName.SubHandlerChanged, { is_default: false });
        super.leave();
        this.manager.layer_CadRoomNameLayer.visible = false;
    }

    private _postReModelingRequest() {
        if (!this.manager.layout_container.remodeiling_data) {
            console.error("remodeiling_data is null");
            return;
        }
        let state = "fail";
        let data = this.manager.layout_container.remodeiling_data;
        const remodelingApiRequest = createProRequest();
        remodelingApiRequest.defaults.withCredentials = true;
        remodelingApiRequest.defaults.baseURL = openApiHost;
        remodelingApiRequest.defaults.headers = {
            'Content-Type': 'application/json',
        };
        remodelingApiRequest({
            method: 'post',
            url: `api/turing/api/floorplanDiffuse/v1/GetHouseLayout`,
            data: data,
            timeout: 10000,
        }).then(async (response: any) => {
            if (!response || !response.success) {
                console.error("Fail to get remodeling scheme.");
            } else {
                if (response.success && response.code == 200) {
                    let content = JSON.parse(response.Content);
                    this.manager.layout_container.fromXmlSchemeData(content.output.house_infoo);
                    this.manager.onLayerVisibilityChanged();
                    this.manager.layout_container.focusCenter();
                    LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "AI推荐计算中..." });
                    await this.ai_layout_for_whole_house(true);
                    state = "success";
                } else {
                    console.error("response.Content is null");
                }
                this.update();
            }
            LayoutAI_App.emit(EventName.RemodelingSchemeState, state);
        }).catch((e: any) => {
            LayoutAI_App.emit(EventName.RemodelingSchemeState, state);
            console.error(e);
        });
    }

    onmousedown(ev: I_MouseEvent): void {
    }

    onmousemove(ev: I_MouseEvent): void {
    }

    onmouseup(ev: I_MouseEvent): void {
    }

    onwheel(ev: WheelEvent): void {
    }

    drawCanvas(): void {
    }
} 