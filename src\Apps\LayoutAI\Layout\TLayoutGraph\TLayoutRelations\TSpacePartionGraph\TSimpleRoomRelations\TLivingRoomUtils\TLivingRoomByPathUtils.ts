import { WPolygon } from "@/Apps/LayoutAI/Layout/TFeatureShape/WPolygon";
import { TBaseRoomToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { <PERSON><PERSON><PERSON>, ZPolygon, ZRect, compareNames } from "@layoutai/z_polygon";
import { I_TinyLivingRoomByPathParams, TTinyLivingRoomByPathRelation } from "../TTinyLivingRoomByPathRelation";


export class TLivingRoomByPathUtils {


    static multiDiningAreaLayouts(params:I_TinyLivingRoomByPathParams) : I_TinyLivingRoomByPathParams[]
    {
        let results : I_TinyLivingRoomByPathParams[] = [];

        if(params.sofa_group_rect)
        {
            if(params.sofa_background_wall_rect) // 说明背靠墙, 直接返回即可
            {
                return null;
            }
            let sofa_group_rect = params.sofa_group_rect;
            let livingAreaRect = params.livingAreaRect;
            if(!livingAreaRect.checkSameNormal(sofa_group_rect.nor,false))
            {
                livingAreaRect.copy(ZRect.fromPoints(livingAreaRect.positions,sofa_group_rect.nor,sofa_group_rect.u_dv_flag));
            }
            let sofa_group_expand_rect = sofa_group_rect.clone();
            let tt_gap =  TTinyLivingRoomByPathRelation.diningCabinetGapDistance
            sofa_group_expand_rect._h = livingAreaRect._h +tt_gap;
            sofa_group_expand_rect._w += tt_gap * 2;
            sofa_group_expand_rect.back_center = sofa_group_rect.backEdge.unprojectEdge2d({x:sofa_group_rect.w/2,y:tt_gap});
            sofa_group_expand_rect.updateRect();

            let back_area_rect = livingAreaRect.clip_side_rect(sofa_group_expand_rect,null);
            if(back_area_rect)
            {
        
                if(back_area_rect.min_hh >= TTinyLivingRoomByPathRelation.diningTableTargetMinLength -0.1)
                {
                     // 原先的餐厅区域如果ok, 就继续保留, 如果不ok, 就直接跳过
                     if(params.diningAreaRect && params.diningAreaRect.min_hh > TTinyLivingRoomByPathRelation.diningTableTargetMinLength)
                     {
                        results.push({
                            diningAreaRect : params.diningAreaRect
                        })
                     }
                     let livingArea = livingAreaRect.clip_side_rect(back_area_rect,null);
                     livingArea = ZRect.fromPoints(livingArea.positions,livingAreaRect.nor,livingAreaRect.u_dv_flag);

                     // 不能因为裁剪之后, 客厅区域就变得过于狭长
                     if(livingArea.max_hh < livingArea.min_hh * 2.)
                     {
                        results.push({
                            diningAreaRect :back_area_rect,
                            livingAreaRect : livingArea
                        })
                     }

                }

            }

        }
        if(results.length > 0) return results;
        return null;
    }

    static layoutEntranceCanbinet(params:I_TinyLivingRoomByPathParams) : I_TinyLivingRoomByPathParams[]
    {
        let allCabinetRects = params.allCabinetRects;
        if(!allCabinetRects) return null;


        if(!params.entrance_expand_rects)
        {
            return null;
        }

        const entranceScore = (entrance_rect:ZRect)=>{
            let backEdgeLayonLength = 0;
            let intersectLength = 0;
            let layonLength = 0;
            params.entrance_expand_rects.forEach((rect)=>{
                let backLayonLen = entrance_rect.backEdge.checkLayOnLength(rect.backEdge,rect.h/2,0.01);

                backLayonLen += entrance_rect.leftEdge.checkLayOnLength(rect.backEdge,rect.h/2,0.01) * 5.;
                backLayonLen += entrance_rect.rightEdge.checkLayOnLength(rect.backEdge,rect.h/2,0.01) * 5.;
                backEdgeLayonLength+=backLayonLen;

                let int_rect = rect.intersect_rect(entrance_rect);
                if(int_rect)
                {
                    intersectLength+=int_rect.max_hh;
                }
                rect.edges.forEach((edge)=>{
                    let sideLayonLength = edge.checkLayOnLength(entrance_rect.leftEdge,rect.h/2,0.01);
                    sideLayonLength += edge.checkLayOnLength(entrance_rect.rightEdge,rect.h/2,0.01);
                    layonLength += sideLayonLength;
                });
            })
            return backEdgeLayonLength + intersectLength * 0.1 + layonLength * 0.1;
        }
        const EntranceScore = "EntranceScore";
        let entrance_candidate_rects = allCabinetRects.filter((rect)=>{
            let score = entranceScore(rect);
            rect._attached_elements[EntranceScore] = score;
            if(score > 0.1)
            {
                return true;
            }
            else{
                return false;
            }
        });
        entrance_candidate_rects.sort((a,b)=>b._attached_elements[EntranceScore] - a._attached_elements[EntranceScore]);
        // console.log(params.entrance_candidate_polys);
        let other_area_rects :ZRect[] = [params.livingPolyInfo?.subMainRect, params.diningPolyInfo?.subMainRect].filter((rect)=>rect);
        let ans_rect :ZRect = null;
        entrance_candidate_rects.find((candidate_rect)=>{
            let target_rect = candidate_rect.clone();
            let isSetNull: boolean = false;
            other_area_rects.forEach(o_rect=>{
                if(target_rect)
                {
                    let subPolys: ZPolygon[] = target_rect.clone().substract(o_rect);
                    
                    if(subPolys.length > 0 )
                    {
                        let innerRect: ZRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(subPolys[0]);
                        if(innerRect)
                        {
                            if(Math.abs(innerRect.nor.dot(target_rect.nor)) < 0.9)
                            {
                                innerRect.swapWidthAndHeight();
                            }
                            if(innerRect.h >= TTinyLivingRoomByPathRelation.entranceCabinetMinDepth - 0.1)
                            {
                                target_rect = target_rect.clip_side_rect(o_rect,target_rect.nor);
                            }
                            else{
                                target_rect = null;
                                isSetNull = true;
                            }
                        }
                    }
                    else{
                        target_rect = null;
                        isSetNull = true;
                    }
                }
            });
            if(!isSetNull && target_rect && target_rect.w >= TTinyLivingRoomByPathRelation.entranceCabinetMinLen -0.1 &&
                 target_rect.h >= TTinyLivingRoomByPathRelation.entranceCabinetMinDepth - 0.1)
            {
                ans_rect =target_rect;
                return true;
            }
            else{
                return false;
            }
        });

        allCabinetRects.forEach((rect)=>{
            if(rect._attached_elements[EntranceScore])
            {
                delete rect._attached_elements[EntranceScore];
            }
        });
        if(ans_rect)
        {
            let len = Math.min(ans_rect.w,TTinyLivingRoomByPathRelation.entranceCabinetMaxLen);
            let depth = TTinyLivingRoomByPathRelation.cabinetDepth;
            let leftRect = ans_rect.clone();
            let rightRect = ans_rect.clone();
            leftRect.length = len;
            leftRect.depth = depth;
            rightRect.length = len;
            rightRect.depth = depth;
            leftRect.back_center = ans_rect.backEdge.unprojectEdge2d({x:leftRect.w/2,y:0});
            rightRect.back_center = ans_rect.backEdge.unprojectEdge2d({x:ans_rect.backEdge.length-rightRect.w/2,y:0});
            leftRect.updateRect();
            rightRect.updateRect();

            let targetRects = [leftRect,rightRect];
            targetRects.sort((a,b)=>{
                let scoreA = entranceScore(a);
                let scoreB = entranceScore(b);
                return scoreB - scoreA;
            });


            ans_rect = targetRects[0];

            let result  :I_TinyLivingRoomByPathParams = {
                entrance_cabinet_rect : ans_rect
            }
            return [result]

        }
        // let entranceCabinetInfo: I_TinyLivingRoomByPathParams[] = layoutSubSpaceAreaByEntranceCabinetArea(params);
        // if(entranceCabinetInfo)
        // {
        //     return {
        //         results: entranceCabinetInfo,
        //     };
        // }
        return null;
    }

    /**
     * 添加地毯|背景墙等图元
     * @param params  
     */
    static postLayoutElements(params:I_TinyLivingRoomByPathParams) : I_TinyLivingRoomByPathParams[]
    {
        let result : I_TinyLivingRoomByPathParams = {};

        // 处理背景墙
        if(params.sofa_background_wall_rect)
        {
            let sofa_background_wall_rect = params.sofa_background_wall_rect.clone();
            // 使用w_poly来处理
            let w_poly = params._room.room_shape._feature_shape._w_poly;
            let backWallEdges =  w_poly.edges.filter((edge)=>{
                let win = WPolygon.getWindowOnEdge(edge);
                if(win) return false;
                if(edge.checkSameNormal(sofa_background_wall_rect.nor.clone().negate(),false) 
                    && edge.islayOn(sofa_background_wall_rect.backEdge, TTinyLivingRoomByPathRelation.backgroundWallOffsetDistance,0.5))
                {

                    return true;
                }
                return false;
            });
            
            backWallEdges.sort((a,b)=>b.length-a.length);
            if(backWallEdges[0])
            {
                let backWallEdge = backWallEdges[0];
                let c_x = backWallEdge.projectEdge2d(sofa_background_wall_rect.rect_center).x;
                let min_l_x = 0;
                let max_r_x = backWallEdge.length;
                if(backWallEdge.prev_edge && Math.abs(backWallEdge.prev_edge.dv.dot(backWallEdge.dv))<0.1)
                {
                    let win = WPolygon.getWindowOnEdge(backWallEdge.prev_edge);
                    if(win && win.type ==="Window")
                    {
                        min_l_x = 100;
                    }
                }
                if(backWallEdge.next_edge && Math.abs(backWallEdge.next_edge.dv.dot(backWallEdge.dv))<0.1)
                {
                    let win = WPolygon.getWindowOnEdge(backWallEdge.next_edge);
                    if(win && win.type ==="Window")
                    {
                        max_r_x = max_r_x - 100;
                    }
                }

                // 背景墙不超过2倍
                let l_x = Math.max(min_l_x, c_x - sofa_background_wall_rect.w/2);
                let r_x = Math.min(max_r_x,c_x + sofa_background_wall_rect.w/2);

                if(l_x - sofa_background_wall_rect.w/2 < min_l_x)
                {
                    l_x = min_l_x;
                }
                if(r_x + sofa_background_wall_rect.w/2 >max_r_x)
                {
                    r_x = max_r_x;
                }

                let new_rect = new ZRect(r_x-l_x,TTinyLivingRoomByPathRelation.backgroundWallDepth);
                new_rect.back_center = backWallEdge.unprojectEdge2d({x:(l_x+r_x)/2,y:0});
                new_rect.nor = sofa_background_wall_rect.nor;
                new_rect._u_dv_flag = sofa_background_wall_rect.u_dv_flag;
                new_rect.updateRect();
                sofa_background_wall_rect.copy(new_rect);
                result.sofa_background_wall_rect = sofa_background_wall_rect;
            }

        }
        if(params.sofa_group_rect)
        {
            params.sofa_carpet_rect = params.sofa_group_rect.clone()
        }
        return [result]
    }
    /**
     * 微调客厅和餐厅区
     * @param params  
     */
    static simpleFinetuneLivingAndDiningRects(params: I_TinyLivingRoomByPathParams): I_TinyLivingRoomByPathParams[] {
        let dining_table_rect = params.dinning_table_rect;
        if (!dining_table_rect) return null;

        let result: I_TinyLivingRoomByPathParams = {};
        // let finetuned_living = TLivingRoomByPathUtils.simpleFinetune_BetterLivingArea({...params,...result});
        // if(finetuned_living)
        // {
        //     result = {...result,...finetuned_living}
        // }
        let finetuned_dining = TLivingRoomByPathUtils.simpleFinetune_BetterDiningArea({ ...params, ...result });
        if (finetuned_dining) {
            result = { ...result, ...finetuned_dining }
        }
        return [result];
    }


    /**
     * 微调产生更优的客厅区
     * @param params  
     */
    // 沙发区调优不知道在跳什么
    static simpleFinetune_BetterLivingArea(params: I_TinyLivingRoomByPathParams) {
        let result : I_TinyLivingRoomByPathParams = {};
        return result;

        let sofa_group_rect = params.sofa_group_rect;

        if(!sofa_group_rect) return null;
        let room_poly = params?._room?.room_shape?._poly;
        if(!room_poly) return null;
        let w_poly = params._room?.room_shape?._feature_shape?._w_poly;
       
        // 重定义沙发组合区: 左右侧朝向---尽量左侧向阳台(先检测靠墙即可),右侧向内
        let dirScore = (edge:ZEdge)=>{
            let layon_length = 0;
            w_poly.edges.forEach((w_edge)=>{
                let win = WPolygon.getWindowOnEdge(w_edge);
                let weight = 1;
                if(win && compareNames(win.room_names,["阳台"]))
                {
                    weight = 2;
                }
                let len = w_edge.checkLayOnLength(edge,TTinyLivingRoomByPathRelation.hallwayDist/2,0.1);
                layon_length += len * weight;
            });
            return layon_length;
        }
        let leftWScore = dirScore(sofa_group_rect.leftEdge);
        let rightWScore = dirScore(sofa_group_rect.rightEdge);

        if(rightWScore > leftWScore) // 需要左右侧调换
        {
            sofa_group_rect._u_dv_flag = -sofa_group_rect._u_dv_flag;
            sofa_group_rect.updateRect();
            result.sofa_group_rect = sofa_group_rect.clone();
            sofa_group_rect = result.sofa_group_rect;
        }



        if(params._room?.room_shape?._feature_shape?._w_poly)
        {
            // let w_edges = params._room?.room_shape?._feature_shape?._w_poly.edges.filter(edge=>{
            //     if(edge.checkSameNormal(sofa_group_rect.backEdge.nor,false))
            //     {
            //         if(edge.islayOn(sofa_group_rect.backEdge,TTinyLivingRoomByPathRelation.hallwayDist,0.2))
            //         {
            //             return true;
            //         }
            //     }
            // });

            // w_edges.sort((a,b)=>b.checkLayOnLength(sofa_group_rect.backEdge,TTinyLivingRoomByPathRelation.hallwayDist,0.01)-a.checkLayOnLength(sofa_group_rect.backEdge,TTinyLivingRoomByPathRelation.hallwayDist,0.01));

        }
        const expandStepLen = TTinyLivingRoomByPathRelation.hallwayDist / 2;

        // 尝试扩宽区域
        if(sofa_group_rect.depth >= TTinyLivingRoomByPathRelation.sofaGroupExpectedMinDepth-0.1)
        {
            let expand_rect = ZRect.fromPoints([...sofa_group_rect.positions, sofa_group_rect.rightEdge.unprojectEdge2d({x:0,y:expandStepLen}),sofa_group_rect.leftEdge.unprojectEdge2d({x:0,y:expandStepLen})],sofa_group_rect.nor,sofa_group_rect.u_dv_flag);

            let int_polys = room_poly.intersect_polygons([expand_rect]);

            if(int_polys && int_polys.length == 1)
            {
                let poly = int_polys[0];

                let unvalidRects : ZRect[] = [params.diningAreaRect];

                w_poly.edges.forEach((edge)=>{
                    let win = WPolygon.getWindowOnEdge(edge);
                    if(!win) return;
                    if(win.type !== "Door") return;
                    if(compareNames(win.room_names,["阳台"])) return;
                    if(win.length > 1300) return; //  先用魔法数字
                    let depth = (!edge.checkSameNormal(sofa_group_rect.nor.clone().negate(),false))? TTinyLivingRoomByPathRelation.hallwayDist/2: TTinyLivingRoomByPathRelation.hallwayDist;
                    let rect = new ZRect(edge.length, depth);
                    rect.nor = edge.nor.clone().negate();
                    rect.back_center = edge.center;
                    rect.updateRect();
                    unvalidRects.push(rect);
                });

                let b_wall_edge = w_poly.edges.find((edge)=>{
                    if(sofa_group_rect.backEdge.islayOn(edge,TTinyLivingRoomByPathRelation.hallwayDist,0.3))
                    {
                        return true;   
                    }
                    return false;
                });

                if(b_wall_edge?.prev_edge && b_wall_edge.prev_edge.nor.dot(b_wall_edge.dv) > 0.5)
                {
                    let rect = new ZRect(TTinyLivingRoomByPathRelation.hallwayDist,TTinyLivingRoomByPathRelation.hallwayDist);
                    rect.nor = b_wall_edge.nor.clone().negate();
                    rect.rect_center = b_wall_edge.unprojectEdge2d({x:-rect.w/2,y:-rect.h/2});

                    unvalidRects.push(rect);
                }
                if(b_wall_edge?.next_edge && b_wall_edge.nor.dot(b_wall_edge.next_edge.dv) > 0.5)
                {
                    let rect = new ZRect(TTinyLivingRoomByPathRelation.hallwayDist,TTinyLivingRoomByPathRelation.hallwayDist);
                    rect.rect_center = b_wall_edge.next_edge.unprojectEdge2d({x:-rect.w/2,y:-rect.w/2});
                    unvalidRects.push(rect);
                }
                unvalidRects = unvalidRects.filter((rect)=>rect);


                let res_polys =  poly.substract_polygons(unvalidRects);
                if(res_polys && res_polys.length == 1)
                {
                    int_polys[0] = res_polys[0];
                }
                else{
                    int_polys[0] = null;
                }
                if(int_polys[0])
                {
                    let max_rect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(int_polys[0]);

                    if(max_rect)
                    {
                        max_rect = ZRect.fromPoints(max_rect.positions,sofa_group_rect.nor,sofa_group_rect.u_dv_flag);

                        max_rect.length = Math.round(max_rect.length);
                        max_rect.depth = Math.round(max_rect.depth);
                        max_rect.updateRect();

                        if(max_rect.length > sofa_group_rect.length && max_rect.depth >= TTinyLivingRoomByPathRelation.sofaGroupExpectedMinDepth-0.1)
                        {
                            // console.log(max_rect.length,max_rect.depth,sofa_group_rect.length);
                            result.sofa_group_rect = max_rect.clone();

                            if(params.livingAreaRect)
                            {
                                result.livingAreaRect = ZRect.fromPoints([...params.livingAreaRect.positions,...max_rect.positions],params.livingAreaRect.nor,params.livingAreaRect.u_dv_flag)
                            }
                        }
                    }
                }
            }
        }

        return result;
    }

    /**
     * 微调产生更优的餐厅区
     * @param params  
     */
    static simpleFinetune_BetterDiningArea(params: I_TinyLivingRoomByPathParams) {
        let dining_table_rect = params.dinning_table_rect;
        let sofa_group_rect = params.sofa_group_rect;
        let diningAreaRect = params.diningAreaRect;

        if(dining_table_rect.length < dining_table_rect.depth)
        {
            dining_table_rect.swapWidthAndHeight();
        }
        if(dining_table_rect.min_hh > TTinyLivingRoomByPathRelation.diningGroupExpectedLen+TTinyLivingRoomByPathRelation.diningCabinetGapDistance)
        {

            let subDiningAreaRect = diningAreaRect.clone();
            subDiningAreaRect._w -= TTinyLivingRoomByPathRelation.diningCabinetGapDistance*2;
            subDiningAreaRect._h -= TTinyLivingRoomByPathRelation.diningCabinetGapDistance*2;
            subDiningAreaRect.rect_center = diningAreaRect.rect_center;

            let inner_rect = dining_table_rect.intersect_rect(subDiningAreaRect);
            if(inner_rect)
            {   
                dining_table_rect.copy(ZRect.fromPoints(inner_rect.positions,dining_table_rect.nor,dining_table_rect.u_dv_flag));
            }
        }

        let result : I_TinyLivingRoomByPathParams = {};

        result.dinning_table_rect =  dining_table_rect;
        let wall_edges: ZEdge[] = [];

        if (params._room && params._room?.room_shape?._poly) {
            wall_edges.push(...params._room.room_shape._poly.edges);
        }
        if (params.dinning_cabinet_rect) {
            wall_edges.push(params.dinning_cabinet_rect.frontEdge);
        }

        const checkLayonLength = (edge: ZEdge) => {
            let layonLength = 0;
            wall_edges.forEach((w_edge) => layonLength += w_edge.checkLayOnLength(edge, TTinyLivingRoomByPathRelation.diningTableExpandDepth + TTinyLivingRoomByPathRelation.diningCabinetGapDistance, 0.01));
            return layonLength;
        }
        let tt_tol = 50;
        let frontLayonLength = checkLayonLength(dining_table_rect.frontEdge);
        if (frontLayonLength > tt_tol) {
            let backLayoutLength = checkLayonLength(dining_table_rect.backEdge);
            if (backLayoutLength <= tt_tol) {
                dining_table_rect.copy(ZRect.fromPoints(dining_table_rect.positions, dining_table_rect.nor.clone().negate(), dining_table_rect.u_dv_flag));
                frontLayonLength = backLayoutLength;
            }
        }


        let isExpandAble = frontLayonLength <= tt_tol;

        if (dining_table_rect.depth < TTinyLivingRoomByPathRelation.diningGroupMinDepth && isExpandAble) {
            dining_table_rect._h = TTinyLivingRoomByPathRelation.diningGroupMinDepth;
            dining_table_rect.updateRect();

            let expand_dining_rect = dining_table_rect.clone();
            expand_dining_rect._h += TTinyLivingRoomByPathRelation.diningCabinetGapDistance;
            expand_dining_rect.updateRect();
            let livingAreaRect = params.livingAreaRect;
            let target_polys = livingAreaRect.substract_polygons([expand_dining_rect]);
            if (target_polys[0] && sofa_group_rect) {
                let mainRect = TBaseRoomToolUtil.instance.getMaxInnerRectByPolygon(target_polys[0]);
                livingAreaRect = ZRect.fromPoints(mainRect.positions, sofa_group_rect.nor, sofa_group_rect.u_dv_flag);

                let sofa_inside_rect = sofa_group_rect.intersect_rect(livingAreaRect);
                if (sofa_inside_rect) {
                    sofa_group_rect.copy(ZRect.fromPoints(sofa_inside_rect.positions, sofa_group_rect.nor, sofa_group_rect.u_dv_flag));
                }
            }
            diningAreaRect = ZRect.fromPoints([...diningAreaRect.positions, ...expand_dining_rect.positions], diningAreaRect.nor, diningAreaRect.u_dv_flag);
            
            result = {
                dinning_table_rect: dining_table_rect,
                sofa_group_rect: sofa_group_rect,
                livingAreaRect: livingAreaRect,
                diningAreaRect: diningAreaRect
            }
        }

        if(result.dinning_table_rect)
        {
            dining_table_rect = result.dinning_table_rect;
            let gapLen = TTinyLivingRoomByPathRelation.diningCabinetGapDistance;
            

            if(dining_table_rect.length > TTinyLivingRoomByPathRelation.diningGroupExpectedLen)
            {
                let r_center = dining_table_rect.rect_center;
                dining_table_rect.length = Math.max(dining_table_rect.length - gapLen*2, TTinyLivingRoomByPathRelation.diningGroupMinDepth);                
                dining_table_rect.length = Math.min(dining_table_rect.length, TTinyLivingRoomByPathRelation.diningGroupMaxLen);
                dining_table_rect.rect_center = r_center;
            }

            result.dinning_table_rect = dining_table_rect;
        }


        if(dining_table_rect.length /  dining_table_rect.depth < 0.5)
        {
            dining_table_rect.swapWidthAndHeight();
            result.dinning_table_rect = dining_table_rect;
        }


        return result;
    }

    static calcEntranceExpandRects(params: I_TinyLivingRoomByPathParams) {
        let room = params._room;
        let w_poly = room?.room_shape?._feature_shape?._w_poly;
        if (!w_poly) return [];

        let entrance_door_edges = w_poly.edges.filter((edge) => {
            let win = WPolygon.getWindowOnEdge(edge);
            if (!win || win.type !== "Door") return false;
            return (compareNames(win.room_names || [], ["入户花园"]) || (compareNames(win.room_names || [], ["客餐厅"]) && (win?.room_names?.length == 1)));
        });

        let entrance_area_rects = entrance_door_edges.map((edge) => {
            let area_rect = new ZRect(edge.length + TTinyLivingRoomByPathRelation.hallwayDist * 4, TTinyLivingRoomByPathRelation.hallwayDist);
            area_rect.nor = edge.nor.clone().negate();
            area_rect.back_center = edge.center;
            area_rect.updateRect();

            return area_rect;
        })
        return entrance_area_rects;
    }
}