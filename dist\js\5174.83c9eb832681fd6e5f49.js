"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[5174],{52898:function(e,n,t){var o=t(13274),r=t(33313),a=t(41594),i=t(27347),s=t(88934),c=t(41980),u=t(55111),l=t(62837);function h(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function d(e,n,t,o,r,a,i){try{var s=e[a](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(o,r)}function f(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var o,r,a=[],i=!0,s=!1;try{for(t=t.call(e);!(i=(o=t.next()).done)&&(a.push(o.value),!n||a.length!==n);i=!0);}catch(e){s=!0,r=e}finally{try{i||null==t.return||t.return()}finally{if(s)throw r}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return h(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return h(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,n){var t,o,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(t=1,o&&(r=2&s[0]?o.return:s[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,s[1])).done)return r;switch(o=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(e){s=[6,e],o=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}n.A=function(){(0,r.A)().styles;var e=f((0,a.useState)(!1),2),n=e[0],t=e[1],h=f((0,a.useState)([]),2),p=h[0],b=h[1],y=i.nb.t,S=((0,a.useRef)(null),i.nb.instance.layout_container),v=function(){var e,n=(e=function(){var e,n;return m(this,(function(t){return(e=S._selected_room)&&(n=u.lj.ComputeScoreInRoom(e),b(n)),[2]}))},function(){var n=this,t=arguments;return new Promise((function(o,r){var a=e.apply(n,t);function i(e){d(a,o,r,i,s,"next",e)}function s(e){d(a,o,r,i,s,"throw",e)}i(void 0)}))});return function(){return n.apply(this,arguments)}}(),g=function(e){t(e)};return(0,a.useEffect)((function(){return i.nb.on(s.U.ShowLayoutScoreDialog,(function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];g(e),e&&v()})),i.nb.on_M(s.U.UpdateLayoutScore,"LayoutScoreDialog",(function(){v()})),function(){}}),[]),(0,o.jsx)(o.Fragment,{children:n&&(0,o.jsx)(c._w,{title:y("布局评分器"),right:250,width:265,height:600,resizable:!0,draggable:!0,onClose:function(){g(!1)},bodyStyle:{background:"#ffffff",border:"0",boxShadow:"0"},children:(0,o.jsx)(l.A,{layoutScoreList:p,style:0})})})}},85174:function(e,n,t){t.r(n);var o=t(13274),r=t(98612),a=t(84872),i=t(88934),s=t(27347),c=t(17287),u=t(19356),l=t(26966),h=t(51010),d=t(25629),f=t(75887),m=t(25617),p=t(23825),b=t(58567),y=t(9003),S=t(31281),v=t(33100),g=t(36134),w=t(77320),x=t(41980),_=t(15696),A=t(41594),I=t(85783),j=t(49063),M=t(49816),D=t(90974),C=t(52898),L=t(20995),k=t(3727),U=t(66742),E=t(84545),R=t(61928),O=t(65640);function T(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function N(e,n,t,o,r,a,i){try{var s=e[a](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(o,r)}function H(e){return function(){var n=this,t=arguments;return new Promise((function(o,r){var a=e.apply(n,t);function i(e){N(a,o,r,i,s,"next",e)}function s(e){N(a,o,r,i,s,"throw",e)}i(void 0)}))}}function F(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var o,r,a=[],i=!0,s=!1;try{for(t=t.call(e);!(i=(o=t.next()).done)&&(a.push(o.value),!n||a.length!==n);i=!0);}catch(e){s=!0,r=e}finally{try{i||null==t.return||t.return()}finally{if(s)throw r}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return T(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return T(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(e,n){var t,o,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(t=1,o&&(r=2&s[0]?o.return:s[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,s[1])).done)return r;switch(o=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,o=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(e){s=[6,e],o=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}n.default=(0,_.observer)((function(){var e=(0,I.B)().t,n=(0,k.A)().styles,t=F((0,A.useState)({opening:!1,title:""}),2),_=t[0],T=(t[1],F((0,A.useState)(!1),2)),N=T[0],z=T[1],B=F((0,A.useState)(!1),2),W=B[0],Z=B[1],X=F(v.A.useMessage(),2),G=X[0],Y=X[1],q="SaveSchemeProgress",K=F((0,A.useState)(!1),2),$=(K[0],K[1]),V=F((0,A.useState)(!1),2),J=V[0],Q=V[1],ee=F((0,A.useState)(window.innerWidth<window.innerHeight),2),ne=ee[0],te=ee[1],oe=F((0,A.useState)(null),2),re=oe[0],ae=oe[1],ie=(0,j.Zp)();s.nb.UseApp(r.e.AppName),s.nb.instance&&(s.nb.instance._is_landscape=ne,s.nb.t=e);var se=(0,A.useRef)(null),ce=(0,y.P)();s.nb.instance&&(s.nb.instance._is_website_debug=p.iG);var ue=function(){s.nb.instance&&(s.nb.instance.bindCanvas(document.getElementById("cad_canvas")),s.nb.instance.update()),s.nb.instance&&(s.nb.instance._is_landscape=ne),te(window.innerWidth<window.innerHeight)},le=function(){var e=H((function(e){return P(this,(function(e){return z(!1),setTimeout((function(){var e="/design?importType=importHouse&&from=2dedit";p.iG&&(e+="&debug=true"),ie(e)}),100),[2]}))}));return function(n){return e.apply(this,arguments)}}(),he=function(){var e=H((function(){var e,n,t;return P(this,(function(o){switch(o.label){case 0:return p.uN?(e={isDelete:0,pageIndex:1,pageSize:9,keyword:p.uN},[4,a.D.getLayoutSchemeList(e)]):[2];case 1:return n=o.sent(),t=n.layoutSchemeDataList,n.total,t&&(s.nb.DispatchEvent(s.n0.OpenMyLayoutSchemeData,t[0]),s.nb.emit(i.U.OpenHouseSearching,!1)),[2]}}))}));return function(){return e.apply(this,arguments)}}();(0,A.useEffect)((function(){if(ae(s.nb.instance.layout_container._layout_scheme_name),ce.homeStore.setAppOpenAsCadPlugin(!0),window.addEventListener("resize",ue),ue(),s.nb.instance){var n;if(p.PP&&(s.nb.instance.Configs.app_specific_domain="Huawei"),s.nb.instance.initialized||(s.nb.instance.init(),s.nb.RunCommand(r.f.AiCadMode),s.nb.instance.prepare().then((function(){he(),de()})),s.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(n=window)||void 0===n?void 0:n.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var o="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(o)),s.nb.instance._debug_mode=o)}}s.nb.instance.update()}s.nb.on(i.U.ShowWallTopMenu,(function(e){ce.homeStore.setIsShowWallTopMenu(e)})),S.A.BasicBiz.Room.bindOnOpenSchemeFinish(le),s.nb.on(i.U.setIssueReportVisible,$),s.nb.on(i.U.ShowSubHandlerBtn,Z),s.nb.on(i.U.SwitchIntoDesign,le),s.nb.on_M(i.U.RoomList,"room_list1",(function(e){ce.homeStore.setRoomInfos(e)})),s.nb.on_M(i.U.SelectingRoom,"cad_home",(function(e){setTimeout((function(){ce.homeStore.setSelectData({rooms:null==e?void 0:e.current_rooms,clickOnRoom:!0})}),20),ce.homeStore.setCurrenScheme(null==e?void 0:e.event_param)})),s.nb.on(i.U.PerformFurnishResult,(function(n){"error"===n.progress?v.A.error(e(n.message)):"info"===n.progress&&v.A.info({key:q,type:"info",content:e(n.message),duration:1,style:{marginTop:"4vh",zIndex:9999}})})),s.nb.on(i.U.OpenMySchemeList,(function(){ce.homeStore.setShowMySchemeList(!0)})),s.nb.on(i.U.selectRoom,(function(e){ce.homeStore.setSelectedRoom(e)})),s.nb.on(i.U.LayoutSchemeOpening,(function(e){ae(e.name)})),s.nb.on(i.U.LayoutSchemeOpened,(function(e){ae(e.name)})),s.nb.on(i.U.LayoutSchemeOpenFail,(function(e){ae(e.name)})),s.nb.on(i.U.SaveProgress,(function(n){"success"===n.progress?(G.open({key:q,type:"success",content:e("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}),"autoExit"===ce.homeStore.isAutoExit&&(R.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=p.O9),ce.homeStore.setIsAutoExit("")):"fail"===n.progress?G.open({key:q,type:"error",content:e("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"ongoing"===n.progress&&G.open({key:q,type:"loading",content:e("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})})),s.nb.on(i.U.MessageTip,(function(e){v.A.config({top:50,duration:3,maxCount:1}),v.A.info(e)})),ce.userStore.getCheckCurrent()}),[ce.homeStore.isAutoExit]);var de=function(){var n=H((function(){var n,t,o,a;return P(this,(function(i){switch(i.label){case 0:return"HouseId"!==p.Zx?[3,1]:(H((function(){var e,n,t;return P(this,(function(o){switch(o.label){case 0:return o.trys.push([0,2,,3]),[4,(0,b.ZN)("HouseId")];case 1:return e=o.sent(),O.log(e),n=e.data,s.nb.DispatchEvent(s.n0.PostBuildingId,{id:n,name:""}),[3,3];case 2:return t=o.sent(),O.error("Error loading file:",t),[3,3];case 3:return[2]}}))}))(),[3,9]);case 1:return"DwgBase64"!==p.Zx?[3,2]:(H((function(){return P(this,(function(e){try{s.nb.RunCommand(s._I.OpenDwgFilefromWork)}catch(e){O.error("Error loading file:",e)}return[2]}))}))(),[3,9]);case 2:return"CopyingBase64"!==p.Zx?[3,3]:(H((function(){var n,t,o;return P(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,(0,b.ZN)("CopyingBase64")];case 1:return n=r.sent(),t=n.data,ce.homeStore.setImgBase64(t),(0,h.e)(e,ce),[3,3];case 2:return o=r.sent(),O.error("Error loading file:",o),[3,3];case 3:return[2]}}))}))(),[3,9]);case 3:return"hxcreate"!==p.Zx?[3,6]:p.fW?[4,(0,L.ON)({id:p.fW})]:[3,5];case 4:(n=i.sent()).result.contentUrl=n.result.dataUrl,s.nb.DispatchEvent(s.n0.OpenMyLayoutSchemeData,n.result),s.nb.DispatchEvent(s.n0.autoSave,null),i.label=5;case 5:return[3,9];case 6:return"hxedit"!==p.Zx?[3,9]:p.vu?[4,(0,L.ON)({id:p.vu})]:[3,8];case 7:(t=i.sent()).success&&t.result&&t.result.dataUrl&&(t.result.contentUrl=t.result.dataUrl,s.nb.DispatchEvent(s.n0.OpenMyLayoutSchemeData,t.result)),i.label=8;case 8:s.nb.instance&&"SingleRoom"==(null===(a=s.nb.instance)||void 0===a||null===(o=a.layout_container)||void 0===o?void 0:o._drawing_layer_mode)&&s.nb.DispatchEvent(s.n0.leaveSingleRoomLayout,{}),s.nb.instance._current_handler_mode=r.f.HouseDesignMode,s.nb.RunCommand(r.f.HouseDesignMode),ce.homeStore.setDesignMode(r.f.HouseDesignMode),i.label=9;case 9:return[2]}}))}));return function(){return n.apply(this,arguments)}}(),fe=[{key:"",label:"",icon:[""],children:(0,o.jsx)(c.Wx,{})}];return(0,o.jsxs)("div",{className:n.root+" "+(ne?n.landscape:""),children:[(0,o.jsx)(c.pF,{create3DLayout:null,title:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{style:{color:"#FFFFFF0F"},children:"|"}),re?" 【"+re+"】":""]}),handler:function(n){var t=s.nb.instance.layout_container;switch(n){case s._I.SaveMyLayoutSchemeAs:0==t._room_entities.length?G.open({key:q,type:"error",content:e("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):(Q(!0),ce.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"}));break;case s._I.SaveMyLayoutScheme:0==t._room_entities.length?G.open({key:q,type:"error",content:e("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):null==t._layout_scheme_id?(Q(!1),ce.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"})):s.nb.DispatchEvent(s.n0.SaveLayoutScheme,null);break;case s._I.SaveSchemeAs:0==t._room_entities.length?G.open({key:q,type:"error",content:e("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):ce.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:""})}}}),(0,o.jsxs)("div",{id:"Canvascontent",className:n.content,children:[(0,o.jsxs)("div",{ref:se,id:"body_container",className:n.canvas_pannel,children:[(0,o.jsx)("div",{className:n.side_pannel,id:"side_pannel",children:ce.homeStore.designMode!==r.f.MeasurScaleMode&&ce.homeStore.designMode!==r.f.HouseCorrectionMode&&(0,o.jsx)(x.I5,{items:fe,contentClassName:n.left_content})}),(0,o.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){ce.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){ce.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,o=Math.sqrt(n*n+t*t);ce.homeStore.setInitialDistance(o/ce.homeStore.scale)}},onTouchMove:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,o=Math.sqrt(n*n+t*t)/ce.homeStore.initialDistance;o>5?o=5:o<.05&&(o=.05),ce.homeStore.setScale(o),s.nb.DispatchEvent(s.n0.scale,o)}},onTouchEnd:function(){ce.homeStore.setInitialDistance(null)}}),(0,o.jsx)("div",{className:"canvas_btns",children:W?(0,o.jsx)(g.A,{className:"btn",type:"primary",onClick:function(){var e,n;ce.homeStore.designMode!==r.f.HouseDesignMode?ce.homeStore.designMode!==r.f.ExDrawingMode?"厨房"===(null===(n=ce.homeStore)||void 0===n||null===(e=n.selectedRoom)||void 0===e?void 0:e.roomname)?s.nb.RunCommand(s._I.AcceptLeaveSubHandler):s.nb.DispatchEvent(s.n0.leaveSingleRoomLayout,{}):s.nb.RunCommand(s._I.LeaveSubHandler):s.nb.RunCommand(s._I.AcceptLeaveSubHandler)},disabled:_.opening||N,children:e("完 成")}):(0,o.jsx)(D.A,{disabled:_.opening||N})})]}),ce.homeStore.designMode!==r.f.MeasurScaleMode&&(0,o.jsx)(c.Nt,{}),(0,o.jsx)(c.iX,{}),ce.homeStore.designMode==r.f.AiCadMode&&(0,o.jsx)(M.A,{})]}),(0,o.jsx)(c.RU,{}),(0,o.jsx)(c.ti,{}),(0,o.jsx)(C.A,{}),(0,o.jsx)(h.A,{}),(0,o.jsx)(w.A,{wrapClassName:"welcome_page",open:ce.homeStore.showWelcomePage,centered:!0,width:"60%",zIndex:999999,closable:!0,destroyOnClose:!0,title:"",footer:null,onCancel:function(){ce.homeStore.setShowWelcomePage(!1)},mask:!0,children:(0,o.jsx)(m.A,{isFixed:!1})}),(0,o.jsx)(u.A,{schemeNameCb:function(e){ae(e)}}),ce.homeStore.isShowWallTopMenu&&(0,o.jsx)(f.A,{}),ce.homeStore.showSaveLayoutSchemeDialog.show&&(0,o.jsx)("div",{className:n.overlay,children:(0,o.jsx)(d.A,{schemeName:re,closeCb:function(){ce.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:""})},isSaveAs:J})}),ce.homeStore.showReplace&&(0,o.jsx)(l.A,{}),ce.homeStore.showHouseSchemeAddForm&&(0,o.jsx)(E.A,{}),(0,o.jsx)(U.A,{}),!s.nb.IsDebug&&(0,o.jsx)(x.cq,{channelCode:"Helptips-004"}),Y]})}))}}]);