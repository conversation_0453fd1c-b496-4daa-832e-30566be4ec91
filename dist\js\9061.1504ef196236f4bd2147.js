"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[9061],{29061:function(n,t,e){e.r(t),e.d(t,{default:function(){return R}});var i=e(13274),o=e(85783),a=e(15696),r=e(41594),s=e(27347),c=e(98612),d=e(9003),u=e(88934),p=e(23825),l=e(17365);function f(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function h(){var n=f(["\n      width:100%;\n      height:calc(var(--vh, 1vh) * 100);\n      .custom-keyboard {\n        position: fixed;\n        bottom: 0;\n        left: 0;\n        right: 0;\n        background: #fff;\n        border-top: 1px solid #ccc;\n        padding: 10px;\n        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);\n      }\n\n      .keypad {\n          display: flex;\n          flex-wrap: wrap;\n      }\n\n      .keypad button {\n          flex: 1 0 30%; /* 控制按钮大小 */\n          margin: 5px;\n          padding: 15px;\n          font-size: 18px;\n          cursor: pointer;\n      }\n      \n    "]);return h=function(){return n},n}function x(){var n=f(["\n      position: fixed;\n      top: 0;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n      input {\n        z-index : 3;\n      }\n      @media screen and (orientation: portrait) {\n        top: 0px;\n        left: 0; \n        right: 0;\n        bottom: 0;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n          @media screen and (max-width : 900px ) {\n            top: 0px;\n            left: 0; \n            right: 0px;\n            bottom: 0;\n        }\n    "]);return x=function(){return n},n}function g(){var n=f(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return g=function(){return n},n}function m(){var n=f(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 0px;\n      background-color: #fff;\n      z-index: 998;\n    "]);return m=function(){return n},n}function v(){var n=f(["\n        position: absolute;\n        left: 0;\n        top: 0;        \n        width : calc(100%);\n        height : calc(100%);\n        overflow: hidden;\n        background-color: #eaeaea;\n        background-image:\n         -webkit-linear-gradient(180deg, #e2e2e2 1px, transparent 1px) ,\n          -webkit-linear-gradient(90deg, #e2e2e2 1px, transparent 1px);\n        background-size: 50px 50px;\n        background-position: calc(50% + 25px) 0%;\n        z-index:1;\n        &.left_panel_layout {\n          height : calc(100%);\n        }\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          touch-action: none;\n          &.canvas_drawing {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(./static/icons/split.png) 0 0,auto;\n          }\n        }\n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return v=function(){return n},n}function b(){var n=f(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return b=function(){return n},n}function w(){var n=f(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return w=function(){return n},n}function y(){var n=f(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return y=function(){return n},n}function _(){var n=f(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return _=function(){return n},n}function z(){var n=f(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return z=function(){return n},n}function j(){var n=f(["\n      position: fixed;\n      top: 68px;\n      right: 12px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #fff;\n      box-shadow: 0px 6px 20px 0px #00000014;\n      font-size: 30px;\n      // 竖屏样式\n\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n        width: 40px;\n        height: 40px;\n        font-size: 25px;\n      }\n    "]);return j=function(){return n},n}function k(){var n=f(["\n      position: fixed;\n      top: 120px; /* Adjust this value to position it below the focusIcon */\n      right: 12px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #fff;\n      box-shadow: 0px 6px 20px 0px #00000014;\n      font-size: 30px;\n\n      @media screen and (max-width: 450px) {\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      @media screen and (orientation: landscape) {\n        width: 40px;\n        height: 40px;\n        font-size: 25px;\n      }\n    "]);return k=function(){return n},n}function S(){var n=f(["\n      position: absolute;\n      top: 0px;\n      left: 0;\n      width: 100%;\n      height: 45px;\n      z-index: 999;\n      background-color: #fff;\n    "]);return S=function(){return n},n}function A(){var n=f(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 999;\n    "]);return A=function(){return n},n}function I(){var n=f(["\n        padding: 20px;\n        position:absolute;\n        left: 0;\n        top: 0;\n        width:100%;\n        height:100%;\n        z-index:-1;\n        background: #f6f7f9;\n    "]);return I=function(){return n},n}function E(){var n=f(["\n        position:fixed;\n        background : #fff;\n        z-index: 10;\n        .closeBtn {\n            display:none;\n            position:absolute;\n            right : 6px;\n            top : 6px;\n            font-size:20px;\n            width:60px;\n            height : 24px;\n            text-align:right;\n        }\n        &.panel_hide {\n            box-shadow: 0px 0px 0px 0px #00000000;\n        }\n        @media screen and (orientation: landscape) {\n            position:fixed;\n            left: 12px !important;\n            top: 52px !important;\n            bottom: 12px !important;\n            right: auto !important;\n            height: auto;\n            padding-left: 0 !important;\n            max-height: calc(var(--vh, 1vh) * 100);\n            max-width:224px;\n            width: 224px;\n            border-radius: 8px;\n            box-shadow:  0px 0px 16px 10px #0000000A;\n            &.panel_hide {\n            display: none;\n            }\n        }\n        @media screen and (orientation: portrait) {\n            position:fixed;\n            left:0;\n            bottom:0px;\n            right:0;\n            width : auto;\n            height:340px;\n            max-width : auto;\n            max-height:340px;\n            overflow: hidden;\n            background-color: #fff;\n            border-radius: 8px 8px 0px 0px;\n            box-shadow:  0px 0px 16px 10px #0000000A;\n            @media screen and (-webkit-min-device-pixel-ratio:2) and (max-height:700px) {\n            transform : scale(0.7);\n            transform-origin : bottom;\n            left : -15%;\n            right : -15%;\n            }\n            &.panel_hide {\n            max-width : 0px;\n            }\n            .closeBtn {\n            display : block;\n            }\n        }\n\n\n        .fade-enter {\n            opacity: 0;\n        }\n\n        .fade-enter-active {\n            opacity: 1;\n            transition: opacity 300ms ease-in-out;\n        }\n\n        .fade-exit {\n            opacity: 1;\n        }\n\n        .fade-exit-active {\n            opacity: 0;\n            transition: opacity 300ms ease-in-out;\n        }\n    "]);return E=function(){return n},n}function C(){var n=f(["\n        height:100%;\n        width:100%;\n    "]);return C=function(){return n},n}var N=(0,e(79874).rU)((function(n){var t=n.css;return{root:t(h()),content:t(x()),canvas3d:t(g()),side_pannel:t(m()),canvas_pannel:t(v(),(0,p.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:t(b()),backBtn:t(w()),forwardBtn:t(y()),schemeNameSpan:t(_()),overlay:t(z()),focusIcon:t(j()),multiSchemeIcon:t(k()),RoomAreaBtns:t(S()),aiDraw:t(A()),mobile_atlas_container:t(I()),leftPanelRoot:t(E()),listContainer:t(C())}})),O=e(70060),M=e(6934),P=e(78154),B=e(22681);function D(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,i=new Array(t);e<t;e++)i[e]=n[e];return i}function L(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var i,o,a=[],r=!0,s=!1;try{for(e=e.call(n);!(r=(i=e.next()).done)&&(a.push(i.value),!t||a.length!==t);r=!0);}catch(n){s=!0,o=n}finally{try{r||null==e.return||e.return()}finally{if(s)throw o}}return a}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return D(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return D(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var R=(0,a.observer)((function(){var n=(0,o.B)().t,t=(0,d.P)(),e=N().styles;s.nb.UseApp(c.e.AppName),s.nb.instance&&(s.nb.t=n);var a=L((0,r.useState)(null),2),f=a[0],h=a[1],x="PadMobile",g=function(){s.nb.instance&&(s.nb.instance.bindCanvas(document.getElementById("cad_canvas")),s.nb.instance.update()),m()},m=function(){s.nb.instance&&(s.nb.instance._is_landscape=window.innerWidth>window.innerHeight);t.homeStore.IsLandscape;t.homeStore.setIsLandscape(window.innerWidth>window.innerHeight),document.documentElement.style.setProperty("--vh","".concat(.01*window.innerHeight,"px"))};return(0,r.useEffect)((function(){l.f.updateAliasName(),t.homeStore.setRoomEntites(s.nb.instance.layout_container._room_entities)}),[s.nb.instance.layout_container._room_entities]),(0,r.useEffect)((function(){if(s.nb.instance&&(s.nb.instance._is_website_debug=p.iG),window.addEventListener("resize",g),g(),s.nb.instance){var n;if(s.nb.instance.initialized||(!(0,p.fZ)()||"HouseId"!==p.Zx&&"CopyingBase64"!==p.Zx||s.nb.emit(u.U.Initializing,{initializing:!0}),s.nb.instance.init(),s.nb.RunCommand(c.f.AiCadMode),s.nb.instance.prepare().then((function(){s.nb.emit(u.U.Initializing,{initializing:!1});var n=s.nb.instance.scene3D;n&&n.stopRender()})),s.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(n=window)||void 0===n?void 0:n.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var e="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(e)),s.nb.instance._debug_mode=e)}}s.nb.instance.update()}}),[t.homeStore.isAutoExit]),(0,r.useEffect)((function(){s.nb.on_M(P.$.showPopup,x,(function(n){h(n)})),s.nb.on_M(u.U.SelectingTarget,x,(function(n,t,e){n||h(null)})),window.parent.postMessage({origin:"layoutai.api",type:"get_group",data:{}},"*"),window.addEventListener("message",(function(n){var e,i,o,a,r,s,c,d,u,p,l,f;"ims2"===n.data.origin&&"addCombination"===n.data.type&&t.addGroupStore.setAddGroupData({data:(null==n||null===(i=n.data)||void 0===i||null===(e=i.data)||void 0===e?void 0:e.menuObj)&&JSON.parse(null==n||null===(a=n.data)||void 0===a||null===(o=a.data)||void 0===o?void 0:o.menuObj),selected_key:null==n||null===(s=n.data)||void 0===s||null===(r=s.data)||void 0===r?void 0:r.selectedKeys,sizeObj:(null==n||null===(d=n.data)||void 0===d||null===(c=d.data)||void 0===c?void 0:c.sizeObj)&&JSON.parse(null==n||null===(p=n.data)||void 0===p||null===(u=p.data)||void 0===u?void 0:u.sizeObj),type:null==n||null===(f=n.data)||void 0===f||null===(l=f.data)||void 0===l?void 0:l.type})}))}),[]),(0,i.jsxs)("div",{className:e.root,children:[(0,i.jsx)(M.A,{}),(0,i.jsx)("div",{id:"pad_left_panel",className:e.leftPanelRoot,children:(0,i.jsxs)("div",{className:e.listContainer,children:[(0,i.jsxs)("div",{className:e.listContainer,style:{display:"sizeEditor"!==f?"block":"none"},children:[(0,i.jsx)(O.A,{})," "]}),(0,i.jsxs)("div",{className:e.listContainer,style:{display:"sizeEditor"===f?"block":"none"},children:[" ",(0,i.jsx)(B.A,{})," "]})]})}),(0,i.jsx)("div",{id:"Canvascontent",className:e.content,children:(0,i.jsx)("div",{id:"body_container",className:e.canvas_pannel+" left_panel_layout",children:(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){t.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){t.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,i=n.touches[0].clientY-n.touches[1].clientY,o=Math.sqrt(e*e+i*i);t.homeStore.setInitialDistance(o/t.homeStore.scale)}},onTouchMove:function(n){if(n.stopPropagation(),2!=n.touches[n.touches.length-1].identifier&&2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,i=n.touches[0].clientY-n.touches[1].clientY,o=Math.sqrt(e*e+i*i)/t.homeStore.initialDistance;o>5?o=5:o<.001&&(o=.001),t.homeStore.setScale(o),s.nb.DispatchEvent(s.n0.scale,o)}},onTouchEnd:function(n){n.touches.length>0&&s.nb.DispatchEvent(s.n0.updateLast_pos,n),t.homeStore.setInitialDistance(null)}})})})]})}))}}]);