import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { BoxGeometry, Mesh, MeshBasicMaterial, Object3D, Vector3, Vector3Like } from "three";
import { generateUUID } from "three/src/math/MathUtils.js";
import { LayoutAI_App } from "../../../LayoutAI_App";
import { I_SwjEntityBase, SwjPropskeyDict } from "../../AICadData/SwjLayoutData";
import { TPainter } from "../../Drawing/TPainter";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { DrawingFigureMode, IRoomEntityRealType, IRoomEntityType, IType2UITypeDict, IUIType2TypeDict, I_Entity3D, KeyEntity, KeyPolyTargetType } from "../IRoomInterface";
import { IPropertyUI } from "./IPropertyUI";
import { MeshBuilder } from "../../Scene3D/MeshBuilder";
import { SceneMaterialMode } from "@layoutai/model3d_api";

export interface I_EntityDrawingState {
    is_moving?: boolean, is_selected?: boolean, is_hovered?: boolean,
    is_draw_figure?: boolean, is_draw_texture?: boolean,
    is_show_room_id?: boolean, is_animation?: boolean, is_draw_roomname?: boolean,
    globalAlpha?: number, draw_decoration?: boolean,
    is_mobile?: boolean,
    is_draw_outline?: boolean,
}
export class TBaseEntity {
    _uuid: string;
    uidN: number;
    uidS: string; // 字符串uid --- 兼容4.0

    protected _name: string;

    type: IRoomEntityType;
    title: string;
    realType: IRoomEntityRealType;


    _height: number; // 因为是2D  高度很多时候没有值

    _rect: ZRect;

    /**
     *  默认可能为空
     */
    _polygon: ZPolygon;

    _ui_properties: { [key: string]: IPropertyUI };
    _ui_props_keys: string[];

    _texture_name: string;
    _texture_image: HTMLImageElement;


    _need_update: boolean = false;


    _priority_for_selection: number = 1;
    _default_priority_for_selection: number = 1;

    onPanelUpdate: (key: string, value: number | string) => void;
    onHandlerUpdate: () => void;
    forcePanelUpdate: (force: boolean) => void; 

    /**
     *  放缩时的固定点
     *    --- 默认是undefined
     *    --- 用完也可以delete掉它
     */
    _scale_fixed_point: Vector3;

    _src_data: I_SwjEntityBase;


    /**
     *  3D 网格数据
     */
    _mesh3d: Object3D;
    protected _is_selected: boolean;
    protected _is_hovered: boolean;
    protected _aliasName: string;
    static readonly EntityUidDefaultNum = 15;
    static EntityUidNCounter = TBaseEntity.EntityUidDefaultNum;


    /**
     *  实体生成器: key是实体Type
     */
    static PolyEntityGenerator : {[key:string]:(poly:ZRect)=>TBaseEntity} = {};

    static readonly EntityType :IRoomEntityType = "Base";
    constructor(rect: ZRect = null, uidN: number = 0) {
        this._uuid = generateUUID();
        this.uidN = uidN || ++TBaseEntity.EntityUidNCounter;
        this._rect = rect || new ZRect(1, 1);
        this._height = 1;
        this._ui_properties = null;
        this._ui_props_keys = [];
        this._is_selected = false;
        this._is_hovered = false;
        this.onPanelUpdate = null;
        this.onHandlerUpdate = null;
        this.title = LayoutAI_App.t("结构件信息")
    }


    bindEntity() {

        TBaseEntity.bindEntityOfPolygon(this._rect, this);

        this.setRectProperties();
    }
    static get_label(poly: ZPolygon) {
        return poly.ex_prop['label'] || null;
    }
    static set_polygon_type(poly: ZPolygon, type: IRoomEntityType) {
        poly.ex_prop[KeyPolyTargetType] = type;
    }


    static get_polygon_type(poly: ZPolygon): IRoomEntityType {
        if (!poly) return null;
        return (poly.ex_prop[KeyPolyTargetType] || null) as IRoomEntityType;
    }

    static bindEntityOfPolygon(rect: ZPolygon, entity: TBaseEntity) {
        rect._attached_elements[KeyEntity] = entity;
    }
    static is_deleted(wall_rect: ZPolygon) {
        return wall_rect.ex_prop['is_deleted'] === '1';
    }

    static set_deleted(wall_rect: ZPolygon, t: boolean) {
        // 如果删除了，就设置is_deleted属性为1
        wall_rect.ex_prop['is_deleted'] = t ? "1" : "0";
        if (!t) {
            delete wall_rect.ex_prop['is_deleted'];
        }
    }

    /**
     * 为了避免循环引用, 会在TLayoutEntityContainer里扩展
     * @param rect  
     * @returns 
     */
    static _FuncGetOrMakeEntityOfCadRect = (rect:ZRect):TBaseEntity=>{
        let type = TBaseEntity.get_polygon_type(rect);
        if(TBaseEntity.PolyEntityGenerator[type])
        {
            return TBaseEntity.PolyEntityGenerator[type](rect);
        }
        return null;
    }
    static getOrMakeEntityOfCadRect(rect:ZRect):TBaseEntity
    {
        if(!rect) return null;
        let base_entity: TBaseEntity = rect._attached_elements[KeyEntity] || null;

        return base_entity;
    }
    static getEntityOfRect(rect:ZRect):TBaseEntity
    {
        if(!rect) return null;
        let base_entity: TBaseEntity = rect._attached_elements[KeyEntity] || null;

        return base_entity;
    }

    /**
     *  注册实体生成器
     */
    static RegisterPolyEntityGenerator(entityType:string, generator:(poly:ZRect)=>TBaseEntity)
    {
        TBaseEntity.PolyEntityGenerator[entityType] = generator;
    }
    
    static RegisterGenerators()
    {

    }
    /**
     *  将一个实体从场景中移除
     */
    dispose()
    {

    }

    updateMesh3D(mode: SceneMaterialMode = "WhiteModel") {
        if (!this._mesh3d) {
            this._mesh3d = MeshBuilder.makeBoxMesh(this.type);
        }
        let pos = this.position;
        pos.z += this.height / 2;

        this._mesh3d.position.copy(pos);
        this._mesh3d.rotation.set(0, 0, this.rotate_z);
        this._mesh3d.scale.set(this.length, this.depth, this.height);

        return this._mesh3d;
    }

    set is_selected(t: boolean) {
        this._is_selected = t;
    }

    get is_selected() {
        return this._is_selected;
    }

    set is_hovered(t: boolean) {
        this._is_hovered = t;
    }
    get is_hovered() {
        return this._is_hovered;
    }
    setRectProperties() {
        if (!this._rect) {
            return;
        }
        TBaseEntity.set_polygon_type(this._rect, this.type);
        this._rect.ex_prop.label = this.realType;
    }
    get name() {
        return this._name;
    }
    set name(str: string) {
        this._name = str;
    }
    get aliasName() {
        return this._aliasName;
    }
    set aliasName(str: string) {
        this._aliasName = str;
    }
    get rect() {
        return this._rect;
    }

    get polygon() {
        return this._polygon || this.rect;
    }
    get start_v3() {
        return this._rect.unproject({ x: -this._rect.w / 2, y: 0 });
    }

    get end_v3() {
        return this._rect.unproject({ x: this._rect.w / 2, y: 0 });
    }

    get start_x() {
        return this.start_v3.x;
    }

    get start_y() {
        return this.start_v3.y;
    }

    get end_x() {
        return this.end_v3.x;
    }
    get end_y() {
        return this.end_v3.y;
    }

    get thickness() {
        return this._rect.h;
    }

    set thickness(hh: number) {
        this._rect._h = hh;
        this._rect.updateRect();
    }
    get width() {
        return this._rect.h;
    }

    set width(hh: number) {
        let rect_center = this._rect.rect_center;
        this._rect._h = hh;
        this._rect.rect_center = rect_center;
        this._rect.updateRect();
    }

    get height() {
        return this._height;
    }

    set height(h: number) {
        this._height = h;
    }

    get maxHeight(): number {
        return 3000;
    }

    get depth() {
        return Math.round(this._rect.h);
    }

    set depth(hh: number) {
        this._rect._h = hh;
    }
    get length() {
        return Math.round(this._rect.w);
    }

    set length(ll: number) {
        this._rect._w = ll;
    }

    get rotate_z() {
        return this._rect.rotation_z;
    }


    set rotate_z(angle: number) {
        this._rect.rotation_z = angle;
    }


    get rotation_z_degree() {
        return Math.round(this._rect.rotation_z / Math.PI * 180);
    }

    set rotation_z_degree(degree: number) {
        this.rotate_z = (Math.round(degree) / 180 * Math.PI);
    }

    get mirror() {
        return this._rect.u_dv_flag > 0 ? 0 : 1;
    }
    set mirror(flag: number) {
        this._rect._u_dv_flag = flag > 0.5 ? -1 : 1;
    }

    set position(p: Vector3Like) {
        this._rect.rect_center_3d = p;
    }
    get position(): Vector3 {
        return this._rect.rect_center_3d;
    }

    get pos_x() {
        return Math.round(this.position.x);
    }

    set pos_x(x: number) {
        let r_center = this._rect.rect_center;
        r_center.x = x;
        this._rect.rect_center = r_center;
    }

    get pos_y() {
        return Math.round(this.position.y);
    }
    set pos_y(y: number) {
        let r_center = this._rect.rect_center;
        r_center.y = y;
        this._rect.rect_center = r_center;
    }
    get pos_z() {
        return Math.round(this.position.z);
    }
    set pos_z(z: number) {
        let r_center_3d = this._rect.rect_center_3d;
        r_center_3d.z = z;
        this._rect.rect_center_3d = r_center_3d;
    }
    get ui_type() {
        return LayoutAI_App.t(IType2UITypeDict[this.type]);
    }

    set ui_type(str: string) {
        this.type = (IUIType2TypeDict[str] as IRoomEntityType) || this.type;
    }

    get ui_realType() {
        return IType2UITypeDict[this.realType || this.type];
    }

    set ui_realType(str: string) {
        this.realType = IUIType2TypeDict[str] as IRoomEntityRealType || this.realType;
    }

    get storey_height() {
        return (LayoutAI_App.instance as TAppManagerBase)?.layout_container?._storey_height || 2800;
    }

    get matched_rect() :ZRect{
        if (!this.rect) return null;
        if (TBaseEntity.get_polygon_type(this.rect) === 'Furniture') {
            let entity = TBaseEntity.getEntityOfRect(this.rect) as any;
            return entity?.figure_element?.matched_rect || null;
        } else if (TBaseEntity.get_polygon_type(this.rect) === 'BaseGroup') {
            let entity = TBaseEntity.getEntityOfRect(this.rect) as any;
            return entity?.figure_element?.matched_rect || null;
        }

        return null;
    }

    /**
     *  获得自定义扩展信息
     */
    get attached_elements()
    {
        return this.rect._attached_elements;
    }
    exportSimpleData(): I_Entity3D {
        return {
            id: this.uidN,
            length: this.length,
            width: this.width,
            height: this.height,
            mirror: this.mirror,
            type: this.type,
            realType: this.realType,
            posX: this.pos_x,
            posY: this.pos_y,
            posZ: this.pos_z,
            rotateZ: this.rotate_z,
            rect: this.rect,
            nor: this.rect.nor,
            center: this.rect.rect_center

        }

    }

    static makeSimpleEntityData(type: IRoomEntityType, realType: IRoomEntityRealType) {
        let entity = new TBaseEntity();
        entity.type = type;
        entity.realType = realType;
        entity.initProperties();
        return entity.exportData();
    }

    /**
     *  变形结束后 执行的更新
     */
    doneTransform() {

    }

    updateOnMovement(rect: ZRect, positionOffset: Vector3) {

    }

    updatePosition()
    {
        if((LayoutAI_App.instance as TAppManagerBase).layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D)
        {
            if(this.matched_rect)
            {
                this.matched_rect.rect_center = this.rect.rect_center.clone();
                this.matched_rect.updateRect();
            }
        }
        else
        {
            if(this.matched_rect)
            {
                this.rect.rect_center = this.matched_rect.rect_center.clone();
                this.rect.updateRect();
            }
        }
    }
    
    update() {
        this._rect.updateRect();
        for (let key of this._ui_props_keys) {
            if (this._ui_properties[key] && (this as any)[key]) {
                this._ui_properties[key].defaultValue = (this as any)[key];
            }
        }
    }

    importData(data: I_SwjEntityBase) {
        for (let key in SwjPropskeyDict) {
            if ((data as any)[key] !== undefined) {
                (data as any)[SwjPropskeyDict[key]] = (data as any)[key];
            }
        }
        this._src_data = data;
        this.uidN = (~~('' + data.uid)) || 0;
        if (!this.uidN) {
            this.uidS = '' + (data.uid || '');
        }
        this.name = data.name || "";
        this.thickness = data.thickness || 100;
        this.width = data.width || data.deep || 100;
        this.length = data.length || 100;
        this._height = data.height || 2800;
        if (data.rotate_z !== undefined) {
            this.rotate_z = data.rotate_z;

        }
        else {
            if (data.direct_x !== undefined) {
                let dir_x = data.direct_x;
                let dir_y = data.direct_y || 0;
                let t_dv = new Vector3(dir_x, dir_y, 0);
                t_dv.normalize();

                this.rect.nor = t_dv;
            }
        }
        this.mirror = data.mirror || 0;
        this.type = data.type;
        if (data.openDirection !== undefined) {
            // console.log(data);

            if (data.openDirection == 0) {
                this.mirror = 1;
            }
            else if (data.openDirection == 1) {
            }
            else if (data.openDirection == 4) {
                this.rect.nor = this.rect.nor.negate();

            }
            else if (data.openDirection == 6) {
                this.rect.nor = this.rect.nor.negate();
                this.mirror = 1;
            }
        }
        this.realType = data.realType || this.realType || "Wall";



        this.position = { x: data.pos_x || 0, y: data.pos_y || 0, z: data.pos_z || 0 };

        this.update();

    }

    exportData(): I_SwjEntityBase {
        let pos = this.position;
        return {
            pos_x: pos.x,
            pos_y: pos.y,
            pos_z: pos.z,
            rotate_z: this.rotate_z,
            mirror: this.mirror,
            length: this.length,
            width: this.width,
            height: this._height,
            thickness: this.thickness,
            name: this._name,
            uid: this.uidN,
            type: this.type,
            realType: this.realType
        }
    }

    updateDataByRect() {
        for (let key of this._ui_props_keys) {
            if (this._ui_properties[key] && (this as any)[key]) {
                this._ui_properties[key].defaultValue = (this as any)[key];

            }

        }
    }

    getPointdistanceToBoundary(point: Vector3Like) {
        // 线框图拿的是matched_rect
        if(this.matched_rect && (LayoutAI_App.instance as TAppManagerBase).layout_container.drawing_figure_mode !== DrawingFigureMode.Figure2D)
        {
            return this.matched_rect.distanceToPoint(point);
        }
        return this.rect.distanceToPoint(point);
    }
    /**
 * 
 * @param point  
 * @param tol 
 */
    getDistanceForMousePosSelection(point: Vector3Like, tol: number = 300) {
        let dist = this.getPointdistanceToBoundary(point);
        if (dist < 0) {
            dist = Math.max(-tol, dist);
        }
        else {
        }
        return dist;
    }

    recordRectData() {
        this._rect._attached_elements['record_rect'] = this.rect.clone();
    }

    getRecordRect(): ZRect {
        return this._rect._attached_elements["record_rect"] || this.rect.clone();
    }

    // 检查有没有匹配到素材
    checkHaveMatchRect() {
        if (TBaseEntity.get_polygon_type(this.rect) === 'BaseGroup') {
            let entity = TBaseEntity.getEntityOfRect(this.rect) as any;
            if (entity && entity.combination_entitys.length > 0) {
                if (entity.combination_entitys[0]?.figure_element?._matched_rect) {
                    return true;
                }
            }
        }
        if (TBaseEntity.get_polygon_type(this.rect) === 'Furniture' || TBaseEntity.get_polygon_type(this.rect) === 'Cabinet') {
            let entity = TBaseEntity.getEntityOfRect(this.rect) as any;
            if (entity?.figure_element?._matched_rect) {
                return true;
            }
        }
        return false;
    }

    protected _bindPropertiesOnChange() {
        let scope = this;
        for (let key in this._ui_properties) {
            let property = this._ui_properties[key];
            if (property.editable === false) continue;
            property.onChange = (value) => {

                (scope as any)[property._key || key] = (property.type == "number") ? (~~value) : value;
                scope.update();
                if (scope.onPanelUpdate) {
                    scope.onPanelUpdate(key, value);
                }
            }
        }
    }

    initProperties() {
        if (!this._ui_properties) {
            this._ui_properties = {};
        }
        this._ui_properties['uidN'] = {
            name: "uid",
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.uidN,
            editable: false,
            props: {
                type: "input",
            }
        }
        this._ui_properties["ui_type"] = {
            name: LayoutAI_App.t("图层"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.ui_type,
            editable: false,
            disabled: true,
            props: {
                type: "input",
            }
        }
        this._ui_properties["ui_realType"] = {
            name: LayoutAI_App.t("模块"),
            widget: "LabelItem",
            type: "string",
            defaultValue: '' + this.ui_realType,
            props: {
                type: "select",
                options: this.getRealTypeOptions(),
                optionWidth: 100
            }
        }
        this._ui_properties["length"] = {
            name: LayoutAI_App.t("宽度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 20000,
            editable: true,
            defaultValue: '' + Math.round(this.length),
            props: {
                type: "number",
                // suffix :"mm"
            }
        }
        this._ui_properties["width"] = {
            name: LayoutAI_App.t("深度"),
            widget: "SlideItem",
            type: "number",
            min: 10,
            max: 20000,
            editable: false,
            defaultValue: '' + Math.round(this.width),
            props: {
                type: "number",
                // suffix :"mm"
            }
        }
        this._ui_properties["height"] = {
            name: LayoutAI_App.t("高度"),
            widget: "SlideItem",
            type: "number",
            min: 0,
            max: this.maxHeight,
            editable: true,
            defaultValue: '' + Math.round(this.height),
            props: {
                type: "number",
                suffix: "mm"
            }
        }
        this._ui_properties["pos_x"] = {
            name: "位置X",
            widget: "LabelItem",
            type: "number",
            min: -500000,
            max: 500000,
            editable: true,
            defaultValue: '' + Math.round(this.pos_x),
            props: {
                type: "number",
                suffix: "mm"
            }
        }
        this._ui_properties["pos_y"] = {
            name: "位置Y",
            widget: "LabelItem",
            type: "number",
            min: -500000,
            max: 500000,
            editable: true,
            defaultValue: '' + Math.round(this.pos_y),
            props: {
                type: "number",
                suffix: "mm"
            }
        }
        this._ui_properties["pos_z"] = {
            name: LayoutAI_App.t("离地高"),
            widget: "LabelItem",
            type: "number",
            min: 0,
            max: 2800,
            editable: true,
            defaultValue: '' + Math.round(this.pos_z),
            props: {
                type: "number",
                // suffix :"mm"
            }
        }



        // this._ui_properties["rotation_z_degree"] = {
        //     name : "旋转角",
        //     widget :"LabelItem",
        //     type : "number",
        //     min : -500000,
        //     max : 500000,
        //     editable : true,
        //     defaultValue : ''+Math.round(this.rotation_z_degree),
        //     props : {
        //         type :"number",
        //         // suffix :"mm"
        //     }
        // }
        this._ui_props_keys = ["ui_type", "ui_realType", "length", "width"];
        this._bindPropertiesOnChange();
    }


    getRealTypeOptions(): { label: string; value: string; }[] {
        let values: IRoomEntityRealType[] = [];

        if (this.type === "Window") {
            values = ["OneWindow", "BayWindow", "SingleDoor", "SlidingDoor"];

        }
        else if (this.type === "Door") {
            values = ["SingleDoor", "SlidingDoor", "DoubleDoor", "OneWindow", "BayWindow"];
        }
        else if (this.type == "StructureEntity") {
            values = ["Flue", "Pillar", "Beam", "Platform", "Envelope_Pipe"];
        }
        else if (this.type == "Furniture") {
            values = ["Lighting", "SoftFurniture", "Cabinet"];
        }
        let ans: { label: string, value: string }[] = [];

        for (let val of values) {
            ans.push({ label: LayoutAI_App.t(IType2UITypeDict[val]) || LayoutAI_App.t(val), value: IType2UITypeDict[val] || val });
        }
        return ans;
    }

    getTitle() {
        return this.title;
    }

    getUiProperties() {
        if (!this._ui_properties) {
            this.initProperties();
        } else {
            if (this.maxHeight && this._ui_properties["height"]) {
                this._ui_properties["height"].max = this.maxHeight;
                if (this._ui_properties["pos_z"] != null) {
                    this._ui_properties["pos_z"].defaultValue = this.rect.zval;
                }
            }

        }
        // 先更新一下 一些列表信息
        if (this._ui_properties['ui_realType']) {
            this._ui_properties["ui_realType"].defaultValue = '' + this.ui_realType;
            this._ui_properties["ui_realType"].props.options = this.getRealTypeOptions();
        }
        let data: { [key: string]: IPropertyUI } = {};

        for (let key of this._ui_props_keys) {
            if (this._ui_properties[key]) {

                this._ui_properties[key].defaultValue = (this as any)[key];


                data[key] = this._ui_properties[key];
            }
        }

        // console.log(data);
        return data;

    }

    drawEntity(painter: TPainter, options: I_EntityDrawingState = {}) {
        let rect = this.matched_rect || this._rect;
        if (options.is_selected) {
            painter.fillStyle = "#66b8ff";
            painter.fillPolygon(rect, 0.5);
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#66b8ff";
            painter.fillPolygon(rect, 0.5);
        }
        if (options.is_draw_figure) {
            painter.strokeStyle = "#000";
            painter.strokePolygons([rect]);
        }

    }
}