import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
headerSearch: css`
    display: flex;
    align-items: center;
    height: 46px;
`,
selectTime: css`
    color: black;
    margin-right: 28px;
    font-size: 12px;
    &:hover {
    color: #000;
    }
`,
selectType: css`
    color: black;
    margin-right: 8px;
    font-size: 12px;
    &:hover {
    color: #000;
    }
`,
showSelect: css`
    display: flex;
    background-color: #F5F5F5;
    padding: 2px;
    border-radius: 6px;
    width: 84px;
    height: 32px;
    svg {
    width: 16px;
    height: 16px;
    }
`,
}));