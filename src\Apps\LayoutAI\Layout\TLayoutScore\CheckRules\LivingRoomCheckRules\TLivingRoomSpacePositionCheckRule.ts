import { Vector3 } from "three";
import { I_Window } from "../../../IRoomInterface";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomSplitByPathClassfierCheckRule } from "./TLivingRoomSplitByPathClassfierCheckRule";

export class TLivingRoomSpacePositionCheckRule extends TLivingRoomSplitByPathClassfierCheckRule
{
    private minLinearScore: number = 0;
    private maxLinearScore: number = 20;
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]) {
        void(figure_element);
        void(main_figures);

        let score: number = 0;

         let entranceDoors: I_Window[] = room.windows.filter(win => win.type == "Door" && (win.room_names.includes("入户花园") || (win.room_names.length == 1 && win.room_names.includes("客餐厅"))));
         let kitchenDoors: I_Window[] = room.windows.filter(win => win.type == "Door" && win.room_names.includes("厨房"));
         
         // 1. 两个区域距离 距离厨房中心进行得分计算
         if(this.livingSpaceRect && this.diningSpaceRect)
        {
            let livingRectCenter: Vector3 = this.livingSpaceRect.rect_center;
            let dininngRectCenter: Vector3 = this.diningSpaceRect.rect_center;
            // 判断离厨房门中心最近距离
            if(kitchenDoors && kitchenDoors.length != 0)
            {
                let nearLivingToKitchenDist: number = calNearDistToDoors(livingRectCenter, kitchenDoors);
                let nearDiningToKitchenDist: number = calNearDistToDoors(dininngRectCenter, kitchenDoors);
                if(nearDiningToKitchenDist < nearLivingToKitchenDist)
                {
                    score += 10;
                }
            }

            if(entranceDoors && entranceDoors.length != 0)
            {
                let nearLivingToEntranceDist: number = calNearDistToDoors(livingRectCenter, entranceDoors);
                let nearDiningToEntranceDist: number = calNearDistToDoors(dininngRectCenter, entranceDoors);
                if(nearDiningToEntranceDist < nearLivingToEntranceDist)
                {
                    score += 5;
                }
            }  
        }
        return {score: score};
    }
}

function calNearDistToDoors(center: Vector3, doors: I_Window[]): number
{
    let nearDist: number = null;
    for(let door of doors)
    {
        let doorCenter: Vector3 = door.rect.rect_center;
        let dist: number = center.distanceTo(doorCenter);
        if(nearDist == null || dist < nearDist)
        {
            nearDist = dist;
        }
    }
    return nearDist;
}