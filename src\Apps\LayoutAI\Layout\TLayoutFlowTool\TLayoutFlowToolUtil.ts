import { ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { Vector3 } from "three";

const xDir: Vector3 = new Vector3(1, 0, 0);
const yDir: Vector3 = new Vector3(0, 1, 0);

export class TLayoutFlowToolUtil
{
    private static _instance: TLayoutFlowToolUtil;

    constructor() { }

    public static get instance(): TLayoutFlowToolUtil {
        if (!this._instance) {
            this._instance = new TLayoutFlowToolUtil();
        }
        return this._instance;
    }

    public parsePathVec(pathInfo: any, roomPoly: ZPolygon, pathMinLen: number = 200): any
    {
        let endNearWallEdge: ZEdge = null;
        let minDist: number = null;
        for(let wallEdge of roomPoly.edges)
        {
            let projectInfo: any = wallEdge.projectEdge2d({x: pathInfo.pathEndPoint.x, y: pathInfo.pathEndPoint.y});
            if(projectInfo.x < 0 || projectInfo.x > wallEdge.length)
            {
                continue;
            }
            let dist: number = Math.abs(projectInfo.y);
            if(minDist === null || dist < minDist)
            {
                minDist = dist;
                endNearWallEdge = wallEdge;
            }
        }

        let resolveX: Vector3 = xDir.clone().multiplyScalar(pathInfo.pathVec.clone().dot(xDir));
        let resolveY: Vector3 = yDir.clone().multiplyScalar(pathInfo.pathVec.clone().dot(yDir));
        let firstPath1: Vector3[] = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveX)];
        if(firstPath1[0].distanceTo(firstPath1[1]) < pathMinLen)
        {
            firstPath1 = null;
        }
        let firstPath2: Vector3[] = null;
        if(firstPath1)
        {
            firstPath2 = [firstPath1[1], pathInfo.pathEndPoint];
        }
        else
        {
            firstPath2 = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveY)];
        }
        let firstPathDir: Vector3 = firstPath2[1].clone().sub(firstPath2[0]).normalize();
        if(Math.abs(firstPathDir.clone().dot(endNearWallEdge.nor)) > 0.9)
        {
            let firstIntersectInfo: any = roomPoly.getRayIntersection(firstPath2[1], firstPathDir);
            if(firstIntersectInfo.point)
            {
                firstPath2[1] = firstIntersectInfo.point;
            }
        }
        if(firstPath2[0].distanceTo(firstPath2[1]) < pathMinLen)
        {
            firstPath2 = null;
        }
        let secondPath1: Vector3[] = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveY)];
        if(secondPath1[0].distanceTo(secondPath1[1]) < pathMinLen)
        {
            secondPath1 = null;
        }
        let secondPath2: Vector3[] = null;
        if(secondPath1)
        {
            secondPath2 = [secondPath1[1], pathInfo.pathEndPoint];
        }
        else
        {
            secondPath2 = [pathInfo.pathStartPoint, pathInfo.pathStartPoint.clone().add(resolveX)];
        }
        let secondPathDir: Vector3 = secondPath2[1].clone().sub(secondPath2[0]).normalize();
        if(Math.abs(secondPathDir.clone().dot(endNearWallEdge.nor)) > 0.9)
        {
            let secondIntersectInfo: any = roomPoly.getRayIntersection(secondPath2[1], secondPathDir);
            if(secondIntersectInfo.point)
            {
                secondPath2[1] = secondIntersectInfo.point;
            }
        }
        if(secondPath2[0].distanceTo(secondPath2[1]) < pathMinLen)
        {
            secondPath2 = null;
        }
        let path1: Vector3[][] = [];
        if(firstPath1)
        {
            path1.push(firstPath1);
        }
        if(firstPath2)
        {
            path1.push(firstPath2);
        }
        let path2: Vector3[][] = [];
        if(secondPath1)
        {
            path2.push(secondPath1);
        }
        if(secondPath2)
        {
            path2.push(secondPath2);
        }
        let path: any = {
            path1: path1,
            path2: path2,
        };
        return path;
    }
}