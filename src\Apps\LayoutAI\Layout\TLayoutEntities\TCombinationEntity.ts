import { LayoutAI_App } from "../../../LayoutAI_App";
import { TPainter } from "../../Drawing/TPainter";
import { ZRect } from "@layoutai/z_polygon";
import { IRoomEntityType, KeyEntity } from "../IRoomInterface";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { TBaseGroupEntity } from "./TBaseGroupEntity";
import { TFurnitureEntity } from "./TFurnitureEntity";



/**
 *  绑定了一个TFigureElement
 */
export class TCombinationEntity extends TBaseGroupEntity
{
    _figure_element :TFigureElement;
    _combination_entitys : TFurnitureEntity[];          // 组合的矩形
    static readonly EntityType:IRoomEntityType  = "Group";
    constructor(figure_element:TFigureElement)
    {
        super(figure_element);
        this.type = "Group";
        this.title = LayoutAI_App.t("组合信息");
        this._figure_element = figure_element || new TFigureElement;
        this._rect = this._figure_element.rect;
        this._combination_entitys = [];
        this._default_priority_for_selection = this._priority_for_selection = 12;
        if(this._figure_element.min_z > 2500)
        {
            this.realType = "Lighting";
        }
        else{
            this.realType = "SoftFurniture";
        }
    }

    clone()
    {
        let ele = new TFigureElement(this._figure_element.exportJson());
        let combinationEntity = new TCombinationEntity(ele);
        combinationEntity._combination_entitys = this._combination_entitys.map(entity => entity.clone());
        return combinationEntity;
    }
    static getOrMakeEntityOfCadRect(rect: ZRect): TCombinationEntity {
        let entity: TCombinationEntity = rect._attached_elements[KeyEntity] || null;
        if (!entity) {
            let figure_element = TFigureElement.getOrMakeFigureElementOfRect(rect);
            entity = new TCombinationEntity(figure_element);
            entity._rect = rect;
            entity.update();
            rect._attached_elements[KeyEntity] = entity;
        }
        return entity;
    }
    static RegisterGenerators()
    {
        TBaseEntity.RegisterPolyEntityGenerator(TCombinationEntity.EntityType,TCombinationEntity.getOrMakeEntityOfCadRect)
    }

    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {

        if(options.is_selected)
        {
            painter.strokeStyle = "#147FFA";

            painter.strokePolygons([this._rect]);

            painter._context.setLineDash([4,4]);
            painter.strokeStyle = "#D2E3FB";

            painter.strokePolygons([this._rect]);

            painter._context.setLineDash([]);


        }
        if(options.is_hovered)
        {
            painter.strokeStyle = "#147FFA";
            painter.strokePolygons([this._rect]);
            
            painter._context.setLineDash([4,4]);
            painter.strokeStyle = "#D2E3FB";

            painter.strokePolygons([this._rect]);

            painter._context.setLineDash([]);

        }

        
    }
}