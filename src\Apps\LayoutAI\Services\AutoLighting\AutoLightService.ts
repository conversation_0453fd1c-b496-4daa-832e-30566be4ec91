import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { IRoomSpaceAreaType, RoomSpaceAreaType } from "../../Layout/IRoomInterface";
import { TRoomType } from "../../Layout/RoomType";
import { TFillLightEntity } from "../../Layout/TLayoutEntities/TFillLightEntity";
import { TLayoutEntityContainer } from "../../Layout/TLayoutEntities/TLayoutEntityContainter";
import { TRoomEntity } from "../../Layout/TLayoutEntities/TRoomEntity";
import { CategoryName } from "../../Scene3D/NodeName";
import { LightingRuler } from "./AutoLightingRuler";
import { AutoLightingRulerNightMediumConfigs } from "./configs/AutoLightingRulerNightMediumConfigs";
import { AutoLightingRulerDayMediumConfigs } from "./configs/AutoLightingRulerDayMediumConfigs";
import { AutoSunLightUtils } from "./AutoSunLightUtils";
import { EventName } from "@/Apps/EventSystem";
import { AutoLightingRulerFactory } from "./handlers/AutoLightingRulerFactory";
import { SceneLightMode } from "../../Scene3D/SceneMode";


/**
* @description 自动打光服务
* <AUTHOR>
* @date 2025-05-19
* @lastEditTime 2025-05-19 09:35:33
* @lastEditors xuld
*/
export class AutoLightingService {
    private static _instance: AutoLightingService;

    constructor() {
        // 便于调试
        (globalThis as any).AutoLightingService = this;
        
        AutoLightingRulerFactory.init();
    }

    public static get instance(): AutoLightingService {
        if (!this._instance) {
            this._instance = new AutoLightingService();
        }
        return this._instance;
    }

    public rulers: LightingRuler[] =
        LayoutAI_App.instance.scene3D.getLightMode() === SceneLightMode.Day
            ? AutoLightingRulerDayMediumConfigs
            : AutoLightingRulerNightMediumConfigs;
            
    public setRulers(rulers: LightingRuler[]) {
        this.rulers = rulers;
    }

    public async updateLighting(isClean: boolean = false): Promise<TFillLightEntity[]> {
        LayoutAI_App.emit(EventName.ApplySeriesSample, {
            seriesOpening: true,
            title: "正在应用灯光规则中...",
            timeout: 20000
        });
        if (isClean) {
            this.cleanLighting();
        }
        let entities = await this.applyLighting();
        (LayoutAI_App.instance as TAppManagerBase).scene3DManager.updateFillLights(true);
        LayoutAI_App.emit(EventName.ApplySeriesSample, {
            seriesOpening: false,
            title: ""
        });
        return entities;
    }

    public cleanLighting() {
        console.log('cleanLighting');
        let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container as TLayoutEntityContainer;
        layout_container.cleanFillLights();
    }

    public async applyLighting(): Promise<TFillLightEntity[]> {
        let entities: TFillLightEntity[] = [];
        console.log('applyLighting', this.rulers);
        for (let ruler of this.rulers) {
            let handler = AutoLightingRulerFactory.getHandler(ruler.typeId);
            if (handler) {
                let result = await handler.handle(ruler);
                entities.push(...result);
            } else {
                console.error(`未实现类型为${ruler.typeId}的灯光规则`);
            }
        }
        return entities;
    }

    public autoRoomSunLighting(roomEntity: TRoomEntity, spaceAreaType: IRoomSpaceAreaType = "UnknownArea"): boolean {
        let isSuccess = false;
        let roomType = roomEntity._room.room_type;
        switch (roomType) {
            case TRoomType.livingRoom:
                switch (spaceAreaType) {
                    case RoomSpaceAreaType.LivingArea:
                        // 客厅
                        isSuccess = AutoSunLightUtils.updateCategorySunLight(roomEntity, CategoryName.Sofa);
                        break;
                    case RoomSpaceAreaType.DiningArea:
                        // 餐厅
                        isSuccess = AutoSunLightUtils.updateCategorySunLight(roomEntity, CategoryName.Table);
                        break;
                    default:
                        console.error(`${roomEntity.room_type}未实现空间区域类型为${spaceAreaType}的自动打光`);
                        break;
                }
                break;
            case TRoomType.bedroom:
                // 卧室
                isSuccess = AutoSunLightUtils.updateCategorySunLight(roomEntity, CategoryName.Bed);
                break;
            case TRoomType.bathroom:
                // 卫生间
                isSuccess = AutoSunLightUtils.updateCategorySunLight(roomEntity, CategoryName.BathCabinet);
                break;
            case TRoomType.study:// 书房
            case TRoomType.wardrobe:// 衣帽间
            case TRoomType.kitchen:// 厨房
            case TRoomType.balcony:// 阳台
            case TRoomType.entranceGarden:// 入户花园
                break;
            default:
                console.log(`未实现房间类型为${roomType}的自动打光`);
                break;
        }
        // 如果自动打光失败，或者无特殊打光规则，则使用房间中心打光
        if (!isSuccess) {
            isSuccess = AutoSunLightUtils.updateRoomCenterSunLight(roomEntity);
        }
        return isSuccess;
    }
}


(globalThis as any).AutoLightingService = AutoLightingService;