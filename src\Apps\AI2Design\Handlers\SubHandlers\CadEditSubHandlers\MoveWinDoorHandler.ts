
import { LayoutAI_App, LayoutAI_Commands } from "@/Apps/LayoutAI_App";
import { CadBaseSubHandler } from "./CadBaseSubHandler";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { EventName } from "@/Apps/EventSystem";
import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { checkIsMobile } from "@/config";
import { AI2DesignBasicModes } from "@/Apps/AI2Design/AI2DesignManager";

export class MoveWinDoorHandler extends CadBaseSubHandler {
    _previous_rect: ZRect;

    _last_pos: Vector3;

    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.name = LayoutAI_Commands.MoveWinDoorHandler;
        this._previous_rect = null;

        this._last_pos = new Vector3(0, 0, 0);
    }

    enter()
    {
        console.log("MoveWinDoorHandler enter");
    }
    leave(state?: number): void {
        console.log("MoveWinDoorHandler leave");
    }
    onmousedown(ev: I_MouseEvent): void {
        if (!this.selected_target.selected_transform_element || !this.selected_target.selected_transform_element._target_rect) {
            this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
            return;
        }

        if (this.selected_target && this.selected_target.selected_rect) {
            this._cad_mode_handler._is_moving_element = true;
        }
        else {
            this._cad_mode_handler._is_moving_element = false;
        }
        this._previous_rect = this.selected_target.selected_transform_element._target_rect.clone();

        this.selected_target.selected_rect._attached_elements['last_center'] = this.selected_target.selected_rect.rect_center.clone();
        this.selected_target.selected_transform_element.recordOriginRect(this._previous_rect);
        this._last_pos = new Vector3(ev.posX, ev.posY, 0);
    }
    onmousemove(ev: I_MouseEvent): void {
        if (ev.buttons != 1) return;

        let transform_element = this.selected_target.selected_transform_element;
        if (!transform_element) return;

        let current_pos = new Vector3(ev.posX, ev.posY, 0);
        let movement = null;
        // 如果添加的是门窗，则按当前位置移动
        if(this._is_window_or_door(this.ai_cad_data._adding_figure_entity))
        {
            movement = current_pos.clone();
        } else 
        {
            movement = current_pos.clone().sub(this._last_pos);
        }
        
        transform_element.applyTransformByMovement(movement,this.exsorb_rects);

        this.updateTransformElements();

        this.update();
    }

    onmouseup(ev: I_MouseEvent): void {
        // 如果鼠标松开的时候在左侧的工具栏上，不做任何操作
        if(checkIsMobile() && this.manager._current_handler_mode === AI2DesignBasicModes.HouseDesignMode)
        {
            let elementId = "#pad_house_left_panel";
            let scrollContainer = document.querySelector(elementId);
            if(scrollContainer)
            {
                const containerRect = scrollContainer.getBoundingClientRect();
                // 横屏下 鼠标松开的时候不超过侧边栏的右侧 则离开状态
                if((LayoutAI_App.instance._is_landscape))
                {
                    if(ev._ev.x <= containerRect.right && ev._ev.y > containerRect.top && ev._ev.y < containerRect.bottom)
                    {
                        this._leaveCurrentHandler();
                        return;
                    }
                } else 
                {
                    // 竖屏下 鼠标松开的时候不超过侧边栏顶部 则离开状态
                    if(ev._ev.y > containerRect.top)
                    {
                        this._leaveCurrentHandler();
                        return;
                    }
                }
            }
        } else 
        {
            if(ev._ev.x < 340)
            {
                this._leaveCurrentHandler();
                return;
            }
        }
        // 要判断在不在墙内，不在墙内还不能添加
        let isWallAdsorpt = this.selected_target.selected_transform_element?.isWallAdsorpt;
        this.updateAttributes("init");

        let endPoint = this.selected_target.selected_rect?._attached_elements['last_center'];
        if(!isWallAdsorpt && endPoint && this.selected_target.selected_rect && !this.ai_cad_data._adding_figure_entity)
        {
            let start: any = null;
            let duration = 100;  // 动画持续时间，单位为毫秒
            let startPoint = this.selected_target.selected_rect.rect_center;
            let animate = (timestamp: any) => {
                if (!start) start = timestamp;
                let progress = (timestamp - start) / duration;
                if (progress > 1) progress = 1;
                // 计算当前的 rect_center
                let currentPoint = startPoint.clone().lerp(endPoint, progress);
                if(this.selected_target.selected_rect)
                {
                    this.selected_target.selected_rect.rect_center = currentPoint;
                }
                // 重新绘制画布
                this.update();
                if (progress < 1) {
                    // 如果动画还没有结束，请求下一帧
                    requestAnimationFrame(animate);
                }
            };
            
            // 开始动画
            requestAnimationFrame(animate);
            this._leaveCurrentHandler();
            return;
        }

        if(!isWallAdsorpt)
        {
            this._leaveCurrentHandler();
            return;
        }
        
        if(this.ai_cad_data._adding_figure_entity && isWallAdsorpt)
        {
            if(this._is_window_or_door(this.ai_cad_data._adding_figure_entity))
            {
                // 添加门窗
                this.addNewEntitiy();
                this.ai_cad_data._adding_figure_entity = null;
                this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
                return;
            }
        }
        if (this.selected_target.selected_transform_element && isWallAdsorpt) {
            let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
            if (info) {
                this.manager.appendOperationInfo(info);
            }
            this.selected_target.selected_transform_element.recordOriginRect(null);
            let rightTop =  this.computeRightVerical();
            if(!rightTop) return;
            let _pp = this.painter.worldToCanvas(rightTop);
            let entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
            this.EventSystem.emit_M(EventName.SelectingTarget, entity, ev._ev, _pp);
        }
        
        this._cad_mode_handler.runCommand(LayoutAI_Commands.LeaveSubHandler);
        this.updateCandidateRects();

        this.container.updateEntityRelations();
        this.container.updateRoomsFromEntities(false);

        this._cad_mode_handler._is_moving_element = false;

        //  如果改了门、窗

        this.container.needs_making_wall_xml = true;

        this.update();
        
    }

    private _leaveCurrentHandler()
    {
        this.cleanSelection();
        this.ai_cad_data._adding_figure_entity = null;
        this.manager._runCommand(LayoutAI_Commands.LeaveSubHandler);

    }


    // 判断是否是门窗实体
    private _is_window_or_door(entity: TBaseEntity): boolean
    {
        if(!entity) return false;
        return entity.type === "Window" || entity.type === "Door";
    }
    
}