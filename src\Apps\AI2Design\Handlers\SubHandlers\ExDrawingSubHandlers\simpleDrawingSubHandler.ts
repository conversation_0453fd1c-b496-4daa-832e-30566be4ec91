import { AI2B<PERSON><PERSON>odeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { I_MouseEvent } from "@layoutai/z_polygon";


export class SimpleDrawingSubHandler extends CadBaseSubHandler
{
    constructor(cad_mode_handler: AI2BaseModeHandler) 
    {
        super(cad_mode_handler);
    }

    onmousedown(ev: I_MouseEvent): void {
        
    }

    onmousemove(ev: I_MouseEvent): void {
        
    }

    onmouseup(ev: I_MouseEvent): void {
        
    }
    onwheel(ev: WheelEvent): void {
        
    }

    drawCanvas(): void {
        
    }
} 