import { test, expect, Page } from '@playwright/test';
import { DesignPage } from '../models/DesignPage';
import { setupTest } from '../utils/testSetup';
import { matchSets } from '../utils/basicProcess';
import { compareScreenshot, takeScreenshot } from '../utils/screenshotUtils';

/**
 * 1.点击首页中的户型库，打开任意户型
 * 2.点击下一步进入套系匹配
 * 3.选择平台第一个套系点击全部应用
 * @param page
 * @param designPage
 */
test.describe('套系界面相关测试', () => {
  let designPage: DesignPage;

  // 使用封装的beforeEach钩子
  test.beforeEach(async ({ page }) => {
    designPage = await setupTest(page);
  });

  test('匹配套系', async ({ page }) => {
    await matchSets(page, designPage);
    await page.waitForTimeout(3000);
    // await compareScreenshot(page, 'style', 'match.png');
    await takeScreenshot(page, 'style', 'match.png');
  });

  test('替换素材', async ({ page }) => {
    await matchSets(page, designPage);

    let roomsInfo = await designPage.getRoomInfo();
    if (roomsInfo.length > 0) {
      for (let i = 0; i < roomsInfo.length; i++) {
        const room = roomsInfo[i];
        if (room.roomName === '客餐厅') {
          designPage.clickRoom(room);
          console.info('点击客餐厅');
          await page.waitForTimeout(1000);

          await page.getByText(/沙发组合/).click();
          await page.getByRole('tab', { name: '云素材' }).click();
          await page.waitForTimeout(5000);
          await page.locator('div').filter({ hasText: /^现代组合沙发落地灯1677\*2374\*2048$/ }).locator('img').click();

          // // TODO 优化：因为图形的中心坐标是几何中心，可能不在图形中内。
          // // 有概率导致点击错误的房间！需要更换API或重新计算图形的内接子图形的中心
          // const targetX = KeCanTing.posInScreen.x;
          // const targetY = KeCanTing.posInScreen.y;

          // // 使用mouse API点击坐标
          // await page.mouse.click(targetX, targetY);

          // // 点击之后等待右侧“空间信息”栏更新名称为“客餐厅”
          // const spaceInfo_KeCanTing = page
          //   .locator('div.swj-businessComponent-propsPanelContainer')
          //   .getByText('客餐厅');
          // await spaceInfo_KeCanTing.waitFor({ state: 'visible', timeout: 5000 });

          // const RuanZhuang = page.getByRole('tablist').getByText('软装');
          // await RuanZhuang.waitFor({ state: 'visible', timeout: 10000 });
          // await RuanZhuang.click();

          // // #rc-tabs-16-panel-软装 > div > div:nth-child(6) ————这里的id选择器不是固定的
          // // const RuanZhuang_list = page.locator('#rc-tabs-25-panel-软装 > div');
          // // 换用另一种方式定位：
          // const RuanZhuang_list = page.getByRole('tabpanel');

          // const sofas = RuanZhuang_list.getByTitle(/直排沙发/); // 使用正则实现模糊匹配
          // await sofas.waitFor({ state: 'visible', timeout: 5000 });
          // await sofas.click();

          // // 5.选择替换列表中第二个素材
          // const secondMaterial = page
          //   .locator('div.swj-baseComponent-Containersbox-body')
          //   .locator('div.ant-spin-container > div:nth-child(2) > div > div:nth-child(2)');

          // // ('div.ant-spin-nested-loading > div.ant-spin-container > div > div > div:nth-child(2)')
          // await secondMaterial.waitFor({ state: 'visible', timeout: 5000 });
          // // await secondMaterial.click();

          // // 判定成功的方案一：替换成功后，会增添一个svg-j6vqjp类
          // // await expect(secondMaterial).toHaveClass(/svg-j6vqjp/); // 正则匹配

          // // 判定成功的方案二：通过类名的增加判定是否替换成功
          // // 1. 获取操作前的类名列表
          // const initialClassList = (await secondMaterial.getAttribute('class'))?.split(/\s+/) || [];
          // // 2. 执行替换操作（例如点击）
          // await secondMaterial.click();
          // // 3. 检查操作后的类名是否比之前多
          // await expect(async () => {
          //   const currentClassList = (await secondMaterial.getAttribute('class'))?.split(/\s+/) || [];
          //   // expect(currentClassList.length).toBeGreaterThan(initialClassList.length);
          //   return currentClassList.length > initialClassList.length;
          // }).toPass({ timeout: 5000 }); // 轮询检查，超时 5 秒

          break;
        }
      }
    }

    await page.waitForTimeout(3000);
    await takeScreenshot(page, 'style', 'instead.png');

    // // 4.选中客餐厅空间，点击右侧面板选择沙发
    // const roomsInfo = await designPage.getRoomInfo();
    // const KeCanTing = roomsInfo.find(room => room.roomName === '客餐厅');
    // if (!KeCanTing) {
    //   throw new Error('未找到客餐厅');
    // }

    // // TODO 优化：因为图形的中心坐标是几何中心，可能不在图形中内。
    // // 有概率导致点击错误的房间！需要更换API或重新计算图形的内接子图形的中心
    // const targetX = KeCanTing.posInScreen.x;
    // const targetY = KeCanTing.posInScreen.y;

    // // 使用mouse API点击坐标
    // await page.mouse.click(targetX, targetY);

    // // 点击之后等待右侧“空间信息”栏更新名称为“客餐厅”
    // const spaceInfo_KeCanTing = page
    //   .locator('div.swj-businessComponent-propsPanelContainer')
    //   .getByText('客餐厅');
    // await spaceInfo_KeCanTing.waitFor({ state: 'visible', timeout: 5000 });

    // const RuanZhuang = page.getByRole('tablist').getByText('软装');
    // await RuanZhuang.waitFor({ state: 'visible', timeout: 10000 });
    // await RuanZhuang.click();

    // // #rc-tabs-16-panel-软装 > div > div:nth-child(6) ————这里的id选择器不是固定的
    // // const RuanZhuang_list = page.locator('#rc-tabs-25-panel-软装 > div');
    // // 换用另一种方式定位：
    // const RuanZhuang_list = page.getByRole('tabpanel');

    // const sofas = RuanZhuang_list.getByTitle(/直排沙发/); // 使用正则实现模糊匹配
    // await sofas.waitFor({ state: 'visible', timeout: 5000 });
    // await sofas.click();

    // // 5.选择替换列表中第二个素材
    // const secondMaterial = page
    //   .locator('div.swj-baseComponent-Containersbox-body')
    //   .locator('div.ant-spin-container > div:nth-child(2) > div > div:nth-child(2)');

    // // ('div.ant-spin-nested-loading > div.ant-spin-container > div > div > div:nth-child(2)')
    // await secondMaterial.waitFor({ state: 'visible', timeout: 5000 });
    // // await secondMaterial.click();

    // // 判定成功的方案一：替换成功后，会增添一个svg-j6vqjp类
    // // await expect(secondMaterial).toHaveClass(/svg-j6vqjp/); // 正则匹配

    // // 判定成功的方案二：通过类名的增加判定是否替换成功
    // // 1. 获取操作前的类名列表
    // const initialClassList = (await secondMaterial.getAttribute('class'))?.split(/\s+/) || [];
    // // 2. 执行替换操作（例如点击）
    // await secondMaterial.click();
    // // 3. 检查操作后的类名是否比之前多
    // await expect(async () => {
    //   const currentClassList = (await secondMaterial.getAttribute('class'))?.split(/\s+/) || [];
    //   // expect(currentClassList.length).toBeGreaterThan(initialClassList.length);
    //   return currentClassList.length > initialClassList.length;
    // }).toPass({ timeout: 5000 }); // 轮询检查，超时 5 秒
  });

  test('更改套系', async ({ page }) => {
    await matchSets(page, designPage);

    // 注意：鼠标进入容纳单独一个套系的盒子中（img），才回显示出“全部应用”按钮
    // 获取img的dom
    const first_TaoXi_Img = page.locator(
      'div.ant-spin-nested-loading > div > div > div:nth-child(2) > div > img'
    );
    expect(first_TaoXi_Img).toBeVisible({ timeout: 10000 });

    // 计算img中心坐标
    const imgBoundingBox = await first_TaoXi_Img.boundingBox();
    const centerX = imgBoundingBox!.x + imgBoundingBox!.width / 2;
    const centerY = imgBoundingBox!.y + imgBoundingBox!.height / 2;
    // 点击
    await page.mouse.move(centerX, centerY, {
      steps: 5 // 添加移动步骤使动作更自然
    });
    await page.waitForTimeout(300); // 悬停一定时间，等待隐藏的“全部应用”按钮出现
    // 点击中心点，即“全部应用”按钮
    await page.mouse.click(centerX, centerY, {
      delay: 50, // 添加50ms按下和释放间隔
      button: 'left' // 明确指定左键点击
    });

    await page.waitForTimeout(3000);
    await compareScreenshot(page, 'style', 'change.png');
  });

  test('清空套系', async ({ page }) => {
    await matchSets(page, designPage);

    // 4.点击顶部菜单清空按钮，弹框选择确定清空
    // !!! 注意：“套系”界面的“清空”按钮的id和“布局”界面的不一样，所以我更改了DesignPage的clickTopMenuResetItem方法
    await designPage.clickTopMenuResetItem();
    const comfirmResetButton = page.locator('div.ant-modal-confirm-btns').getByText('确定清空'); // ！！！注意这里有点坑，布局界面的按钮是“确认清空”，而套系中的是“确定清空”
    await comfirmResetButton.waitFor({ state: 'visible', timeout: 5000 });
    await comfirmResetButton.click();

    await page.waitForTimeout(3000);
    await compareScreenshot(page, 'style', 'clear.png');

    // TODO 判定是否清空套系（涉及canvas内部元素，需要使用预留接口）
    // 判定方法也可以“手动”判定：在可视化界面看
  });
});
